const DOMGlobals = []
const NodeGlobals = ['module', 'require']

module.exports = {
  env: {
    browser: true,
    es6: true
  },
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: '@typescript-eslint/parser',
    sourceType: 'module'
  },
  extends: ['plugin:vue/recommended'],
  plugins: ['@typescript-eslint', 'vue'],
  rules: {
    'no-restricted-globals': ['error', ...DOMGlobals, ...NodeGlobals],
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": "error",
    'indent': ['error', 4],
    // vue template script indent
    'vue/html-indent': ['error', 4],
    'vue/script-indent': [
      'error',
      4,
      {
        baseIndent: 1,
        switchCase: 1,
      },
    ],
    // Require template literals instead of string concatenation
    'vue/prefer-template': 'warn',
  
    'vue/space-infix-ops': 'error',
    'vue/object-curly-newline': [
      'error',
      {
        ObjectExpression: { minProperties: 4, multiline: true, consistent: true },
        ObjectPattern: { minProperties: 4, multiline: true, consistent: true },
        ImportDeclaration: { minProperties: 4, multiline: true, consistent: true },
        ExportDeclaration: { minProperties: 4, multiline: true, consistent: true },
      },
    ],
    // enforce "same line" or "multiple line" on object properties.
    // https://eslint.org/docs/rules/object-property-newline
    'object-property-newline': [
      'error',
      {
        allowAllPropertiesOnSameLine: true,
      },
    ],
    'vue/object-property-newline': [
      'error',
      {
        allowAllPropertiesOnSameLine: true,
      },
    ],
    // 兼容现有代码对props的改动
    'vue/no-mutating-props': 'warn',
    // 标签禁用自闭合
    'vue/html-self-closing': ['error', {
      'html': {
        'void': 'always',
        'normal': 'never',
        'component': 'never',
      },
      'svg': 'always',
      'math': 'always',
    }],
    'vue/component-name-in-template-casing': ['error', 'kebab-case', {
      'registeredComponentsOnly': false,
      'ignores': [],
    }],
    "object-curly-spacing": ["warn", "always"],
    "comma-dangle": ["warn", "always-multiline"]
  },
  'overrides': [
    {
      'files': [
        '*.vue',
          '*.ts',
      ],
      'rules': {
        'indent': 'off',
      },
    },
  ],
}
