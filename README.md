# AbcPrint
## 使用

## data-type
### 基础元素类型
block 
mix-block
table

### 子元素类型
text
group
item

### 高级类型
header
footer

## 开始前
1. 统一在一级children使用data-type标识元素类型

## 使用方式

```html
<script src="http://cis-static-dev.oss-cn-shanghai.aliyuncs.com/abc-print/index.js"></script>
<script src="http://cis-static-dev.oss-cn-shanghai.aliyuncs.com/abc-print/templates/index.js"></script>
```

## TODO
- ~~打印日志优化~~
- ~~构建时间优化~~
- tree shaking未生效
- 示例数据移到最后方便开发
- 生长曲线图/abc-form-engine抽离 减少abc-print工程
- 服务机等打印迁移调整
- 图片打印优化(异步加载的图片)

## 新开发的同学看这里！！！
https://abcyun.yuque.com/abc-home/ywhr14/zhm9mfytpavqz9nt
