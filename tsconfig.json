{
    "compilerOptions": {
      "baseUrl": ".",
      "outDir": "./lib", // 输出目录
      "sourceMap": true, // 是否生成sourceMap
      "target": "es2015", // 编译目标
      "module": "esnext", // 模块类型
      "allowJs": false, // 是否编辑js文件
      "strict": true, // 严格模式
      "noUnusedLocals": true, // 未使用变量报错
      "experimentalDecorators": true, // 启动装饰器
      "esModuleInterop": true,
      "removeComments": false, // 删除注释
      "declaration": true, // 生成定义文件
      "declarationMap": false, // 生成定义sourceMap
      "declarationDir": "./lib/types", // 定义文件输出目录
      "resolveJsonModule": true,
      "lib": ["esnext", "dom"],
      "moduleResolution": "node",
      "typeRoots": [
        "src/types/index.d.ts"
      ]
    },
    "include": [
      "src/**/*",
      "share/"
    ],
    "exclude": [
      "./logs",
      "src/split-dom.ts"
    ]
}
