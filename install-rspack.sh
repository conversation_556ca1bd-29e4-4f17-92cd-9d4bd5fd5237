#!/bin/bash

echo "🚀 正在安装 Rspack 相关依赖..."

# 检查 npm 是否可用
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未找到，请先安装 Node.js 和 npm"
    exit 1
fi

echo "📦 安装 Rspack 核心依赖..."
npm install --save-dev @rspack/cli @rspack/core

echo "📦 安装 Vue 相关 loader..."
npm install --save-dev vue-loader

echo "📦 安装 CSS 相关 loader..."
npm install --save-dev css-loader postcss-loader sass-loader

echo "📦 安装 PostCSS 插件..."
npm install --save-dev autoprefixer

echo ""
echo "✅ Rspack 依赖安装完成！"
echo ""
echo "🧪 运行测试脚本验证安装："
echo "node test-rspack.js"
echo ""
echo "🚀 接下来您可以："
echo "1. 运行 'npm run dev:templates:rspack' 进行开发模式构建"
echo "2. 运行 'npm run build:templates:rspack' 进行生产模式构建"
echo "3. 运行 'npm run build:all:rspack' 进行完整构建"
echo "4. 运行 'npm run dev:rspack' 启动完整开发环境"
