type Orientations = (Orientation.portrait | Orientation.landscape | Orientation.unknow)[] | null | undefined;

export enum Orientation {
    unknow,
    portrait,
    landscape,
}

interface IHeightLevel {
    name: 'string';
    key: 'string';
    width: 'string';
    height: 'string';
}

export declare class IPageSize {
    name: string;
    key: string;
    width: string;
    height: string;
    orientations: Orientations;
    heightLevels: IHeightLevel[] | null;
}

export class PageSize implements IPageSize {
    width: string;
    height: string;
    name: string;
    key: string;
    orientations: Orientations;
    heightLevels: IHeightLevel[] | null;

    constructor(pageSize: Record<string, any>) {
        const width = pageSize.width === undefined ? '' : pageSize.width;
        const height = pageSize.height === undefined ? '' : pageSize.height;
        const key = pageSize.key === undefined ? '' : pageSize.key;
        const name = pageSize.name === undefined ? '' : pageSize.name;

        this.name = name
        this.key = key
        this.width = width
        this.height = height
        this.orientations = pageSize.orientations
        this.heightLevels = pageSize.heightLevels
    }
}

function extendPageSize(parent: PageSize, extend: Object) {
    return Object.assign({}, parent, extend)
}

export const A4 = new PageSize({
    name: 'A4',
    key: 'A4',
    width: '210mm',
    height: '297mm',
    orientations: [
        Orientation.portrait,
        Orientation.landscape,
    ],
    heightLevels: null,
})

export const A5 = new PageSize({
    name: 'A5',
    key: 'A5',
    width: '148mm',
    height: '210mm',
    orientations: [
        Orientation.portrait,
        Orientation.landscape,
    ],
    heightLevels: null,
})

export const A6 = new PageSize({
    name: 'A6',
    key: 'A6',
    width: '105mm',
    height: '148mm',
    orientations: [
        Orientation.portrait,
        Orientation.landscape,
    ],
    heightLevels: null,
})

export const B5 = new PageSize({
    name: 'B5',
    key: 'B5',
    width: '176mm',
    height: '250mm',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
})

export const B6 = new PageSize({
    name: 'B6',
    key: 'B6',
    width: '125mm',
    height: '176mm',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
})

export const MM80 = new PageSize({
    name: '热敏小票（80mm）',
    key: 'MM80',
    width: '70mm',
    height: 'auto',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
})

export const MM58 = new PageSize({
    name: '热敏小票（58mm）',
    key: 'MM58',
    width: '48mm',
    height: 'auto',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
});

export const MM100 = new PageSize({
    name: '热敏小票（100mm）',
    key: 'MM100',
    width: '100mm',
    height: 'auto',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
});

export const MM82 = new PageSize({
    name: 'MM82',
    key: 'MM82',
    width: '82mm',
    height: 'auto',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
})

export const MM76_130 = new PageSize({
    name: 'MM76_130',
    key: 'MM76_130',
    width: '76mm',
    height: '130mm',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
})

export const MM90 = new PageSize({
    name: 'MM90',
    key: 'MM90',
    width: '90mm',
    height: '90mm',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
})

export const MM156 = new PageSize({
    name: 'MM156',
    key: 'MM156',
    width: '156mm',
    height: 'auto',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
})

export const MM114 = new PageSize({
    name: 'MM114',
    key: 'MM121',
    width: '121mm',
    height: 'auto',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
})

export const NeedleMultiPaper = new PageSize({
    name: '针式多联241mm宽 ',
    key: 'NeedleMultiPaper',
    width: '241mm',
    height: '280mm',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: [
        {
            name: '一等分',
            key: '一等分',
            width: '210mm',
            height: '280mm',
        },
        {
            name: '二等分',
            key: '二等分',
            width: '210mm',
            height: '140mm',
        },
        {
            name: '三等分',
            key: '三等分',
            width: '210mm',
            height: '93mm',
        },
    ],
})

export const MedicalNeedleMultiPaper = new PageSize({
    name: '针式多联241mm宽',
    key: 'MedicalNeedleMultiPaper',
    width: '241mm',
    height: '280mm',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: [
        {
            name: '一等分',
            key: '一等分',
            width: '210mm',
            height: '280mm',
        },
        {
            name: '二等分',
            key: '二等分',
            width: '210mm',
            height: '140mm',
        },
    ],
})

export const MM211_127 = new PageSize({
    name: '211×127',
    key: 'MM211_127',
    width: '211mm',
    height: '127mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM211_140 = new PageSize({
    name: '211×140',
    key: 'MM211_140',
    width: '211mm',
    height: '140mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM114_127 = new PageSize({
    name: '114×127',
    key: 'MM114_127',
    width: '114mm',
    height: '127mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM85_100 = new PageSize({
    name: '85×100',
    key: 'MM85_100',
    width: '85mm',
    height: '100mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM240_92 = new PageSize({
    name: '240×92',
    key: 'MM240_92',
    width: '240mm',
    height: '92mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM240_90 = new PageSize({
    name: '240×90',
    key: 'MM240_90',
    width: '240mm',
    height: '90mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM244_92 = new PageSize({
    name: '244×92',
    key: 'MM244_92',
    width: '244mm',
    height: '92.5mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM140_203 = new PageSize({
    name: '140×203',
    key: 'MM140_203',
    width: '140mm',
    height: '203mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM186_101 = new PageSize({
    name: '186×102',
    key: 'MM186_102',
    width: '186mm',
    height: '102mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM190_101 = new PageSize({
    name: '190×101.6',
    key: 'MM190_101.6',
    width: '190mm',
    height: '101.6mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM191_151 = new PageSize({
    name: '191×151',
    key: 'MM191_151',
    width: '191mm',
    height: '151mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM120_95 = new PageSize({
    name: '120×95',
    key: 'MM120_95',
    width: '120mm',
    height: '95mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM120_94 = new PageSize({
    name: '120×94',
    key: 'MM120_94',
    width: '120mm',
    height: '95mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM120_114 = new PageSize({
    name: '120×114',
    key: 'MM120_114',
    width: '120mm',
    height: '114mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM121_94 = new PageSize({
    name: '121×94',
    key: 'MM121_94',
    width: '121mm',
    height: '94mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM211_119 = new PageSize({
    name: '211×119',
    key: 'MM211_119',
    width: '211mm',
    height: '119mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM120_127 = new PageSize({
    name: '120×127',
    key: 'MM120_127',
    width: '120mm',
    height: '127mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM120_76 = new PageSize({
    name: '120×76',
    key: 'MM120_76',
    width: '120mm',
    height: '76mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM120_93 = new PageSize({
    name: '120×93',
    key: 'MM120_93',
    width: '120mm',
    height: '93mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM126_70 = new PageSize({
    name: '126×70',
    key: 'MM126_70',
    width: '126mm',
    height: '70mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM95_190 = new PageSize({
    name: '95x190',
    key: 'MM95_190',
    width: '95mm',
    height: '190mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM210_127 = new PageSize({
    name: '210×127',
    key: 'MM210_127',
    width: '210mm',
    height: '127mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM241_94 = new PageSize({
    name: '241×94',
    key: 'MM241_94',
    width: '241mm',
    height: '94mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM200_114 = new PageSize({
    name: '200×114',
    key: 'MM200_114',
    width: '200mm',
    height: '114mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM241_93 = new PageSize({
    name: '241×93',
    key: 'MM241_93',
    width: '241mm',
    height: '93mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM240_93 = new PageSize({
    name: '240×93',
    key: 'MM240_93',
    width: '240mm',
    height: '93mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM82_101 = new PageSize({
    name: '82×101',
    key: 'MM82_101',
    width: '82mm',
    height: '101mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM281_127 = new PageSize({
    name: '281×127',
    key: 'MM281_127',
    width: '281mm',
    height: '127mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM122_202 = new PageSize({
    name: '122×202',
    key: 'MM122_202',
    width: '122mm',
    height: '202mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM251_93 = new PageSize({
    name: '251×93',
    key: 'MM251_93',
    width: '251mm',
    height: '93mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM240_101 = new PageSize({
    name: '240×101',
    key: 'MM240_101',
    width: '240mm',
    height: '101mm',
    orientations: [
        Orientation.portrait,
    ],
})


export const MM120_202 = new PageSize({
    name: '120×202',
    key: 'MM120_202',
    width: '120mm',
    height: '202mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM210_139 = new PageSize({
    name: '小票纸(21x13.97cm)',
    key: 'MM210_139',
    width: '210mm',
    height: '139.7mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM220_114 = new PageSize({
    name: '220x114',
    key: 'MM220_114',
    width: '220mm',
    height: '114mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM160_101 = new PageSize({
    name: '160x101',
    key: 'MM160_101',
    width: '160mm',
    height: '101mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM195_140 = new PageSize({
    name: '195×140',
    key: 'MM195_140',
    width: '195mm',
    height: '140mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM93_229 = new PageSize({
    name: '93×229',
    key: 'MM93_229',
    width: '93mm',
    height: '229mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM159_101 = new PageSize({
    name: '159×101',
    key: 'MM159_101',
    width: '159mm',
    height: '101mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM190_127 = new PageSize({
    name: '190×127',
    key: 'MM190_127',
    width: '190mm',
    height: '127mm',
    orientations: [
        Orientation.portrait,
    ],
})


export const MM242_151 = new PageSize({
    name: '242×151',
    key: 'MM242_151',
    width: '242mm',
    height: '151mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM192_102 = new PageSize({
    name: '192×102',
    key: 'MM192_102',
    width: '192mm',
    height: '102mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM191_127 = new PageSize({
    name: '小票纸(19.1x12.7cm)',
    key: 'MM191_127',
    width: '191mm',
    height: '127mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM126_165 = new PageSize({
    name: '小票纸(12.6x16.5cm)',
    key: 'MM126_165',
    width: '126mm',
    height: '165mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM240_114 = new PageSize({
    name: '小票纸(24x11.4cm)',
    key: 'MM240_114',
    width: '240mm',
    height: '114mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM240_140 = new PageSize({
    name: '小票纸(24x14cm)',
    key: 'MM240_140',
    width: '240mm',
    height: '140mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM120_212 = new PageSize({
    name: '小票纸(120x21.2cm)',
    key: 'MM120_212',
    width: '212mm',
    height: '120mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM129_200 = new PageSize({
    name: '小票纸(12.9x20cm)',
    key: 'MM129_200',
    width: '129mm',
    height: '200mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM148_70 = new PageSize({
    name: '卡纸(14.8x7cm)',
    key: 'MM148_70',
    width: '148mm',
    height: '70mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM110_50 = new PageSize({
    name: '卡纸(11x5cm)',
    key: 'MM110_50',
    width: '110mm',
    height: '50mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const B6_LANSCAPE = extendPageSize(B6, { name: 'B6 ', key: 'B6_LANSCAPE', orientations: [Orientation.landscape] })
export const MM82_101_SICHUANG_TAX = extendPageSize(MM82_101, { name: '四川医疗票据(税务8.2×10cm)', height: '102mm' })
export const MM114_LIAONING = extendPageSize(MM114, { name: '辽宁医疗票据(11.4×12.7cm)' })
export const MM211_127_ANHUI = extendPageSize(MM211_127, { name: '安徽医疗票据(21×12.7cm)' })
export const MM211_127_CHONGQING = extendPageSize(MM211_127, { name: '重庆医疗票据(21×12.7cm)' })
export const MM211_127_GUANGXI = extendPageSize(MM211_127, { name: '广西医疗票据(21×12.7cm)' })
export const MM211_127_LIAONING = extendPageSize(MM211_127, { name: '辽宁医疗票据(21×12.7cm)' })
export const MM211_127_NATIONAL = extendPageSize(MM211_127, { name: '全国统一票据(21×12.7cm)' })
export const MM211_127_ZHEJIANG = extendPageSize(MM211_127, { name: '浙江医疗票据(新版21×12.7cm)' })
export const MM85_100_SICHUANG = extendPageSize(MM85_100, { name: '四川医疗票据(财政8.5×10cm)', height: '102mm' })
export const MM85_100_CHONGQING = extendPageSize(MM85_100, { name: '重庆医疗票据(纵向8.5×10cm)' })
export const MM240_92_CHONGQING = extendPageSize(MM240_92, { name: '重庆医疗票据(横向24×9.2cm)', width: '241.5mm', height: '93.5mm' })
export const MM240_90_QINGDAO = extendPageSize(MM244_92, { name: '青岛医疗票据(24×9.3cm)' })
export const MM186_101_GUANGDONG = extendPageSize(MM186_101, { name: '广东医疗票据(18.6×10cm)' })
export const MM190_101_HUBEI = extendPageSize(MM190_101, { name: '湖北医疗票据(19×10cm)' })
export const MM120_95_HUNAN = extendPageSize(MM120_95, { name: '湖南医疗票据(12×9.5cm)' })
export const MM120_94_HUNAN = extendPageSize(MM120_94, { name: '湖南医疗票据(12×9.4cm)' })
export const MM211_119_SHANGHAI = extendPageSize(MM211_127, { name: '上海医疗票据(21×12cm)' })
export const MM211_140_SHANGHAI = extendPageSize(MM211_140, { name: '上海医疗票据(21×14cm)' })
export const MM120_127_SHANXI = extendPageSize(MM120_127, { name: '陕西医疗票据(财政12×12.5cm)' })
export const MM120_76_SHANXI_TAX = extendPageSize(MM120_76, { name: '陕西医疗票据(税务12×7.6cm)' })
export const MM120_93_SHENYANG = extendPageSize(MM120_93, { name: '沈阳医疗票据(12×9.3cm)' })
export const MM126_70_YUNNAN = extendPageSize(MM126_70, { name: '云南医疗票据(12.6×7cm)' })
export const MM210_127_JIANGSU = extendPageSize(MM210_127, { name: '江苏医疗票据(21×12.7cm)' })
export const MM210_127_YUNNAN = extendPageSize(MM210_127, { name: '云南医疗票据(新版21×12.7cm)' })
export const MM210_127_ZHEJIANG = extendPageSize(MM210_127, { name: '浙江医疗票据(21×12.7cm)' })
export const MM210_127_SHANDONG = extendPageSize(MM210_127, { name: '山东医疗票据(21×12.7cm)' })
export const MM241_94_WUHU = extendPageSize(MM241_94, { name: '安徽省芜湖市医疗票据(24.1×9.4cm)' })
export const MM241_93_HEFEI = extendPageSize(MM241_93, { name: '合肥医疗票据(24×9.5cm)' })
export const MM241_93_ANHUI_XUANCHENG = extendPageSize(MM241_93, { name: '宣城医疗票据(24×9.3cm)' })
export const MM281_127_HENAN = extendPageSize(MM281_127, { name: '河南医疗票据(28×12.7cm)' })
export const MM240_101_HUHEHAOTE = extendPageSize(MM240_101, { name: '呼和浩特医疗票据(24×10.1cm)' })
export const MM122_202_JIANGXI = extendPageSize(MM122_202, { name: '江西医疗票据(12.2×20.2cm)' })
export const MM120_202_zhejiang = extendPageSize(MM120_202, { name: '浙江医疗票据(12×20.2cm)' })
export const MM210_139_zhejiang = extendPageSize(MM210_139, { name: '浙江医疗票据(21×13.9cm)' })
export const MM210_139_ningbo = extendPageSize(MM210_139, { name: '宁波医疗票据(21×13.9cm)' })
export const MM210_139_heilongjiang = extendPageSize(MM191_127, { name: '黑龙江医疗票据(19.1×12.7cm)' })
export const MM126_165_qingdao = extendPageSize(MM126_165, { name: '青岛住院收费票据(12。6×16。5cm)' })
export const MM120_212_HEBEI = extendPageSize(MM120_212, { name: '河北住院收费票据(12×21.2cm)' })
export const MM210_127_HOSPITAL_YUNNAN = extendPageSize(MM210_127, { name: '云南住院医疗票据(21×12.7cm)' })
export const MM240_114_kunshan = extendPageSize(MM240_114, { name: '昆山医疗票据(24×11.4cm)' })
export const MM240_140_jilin = extendPageSize(MM240_140, { name: '吉林医疗票据(24×14cm)' })
export const MM242_151_JILIN_TAX = extendPageSize(MM242_151, { name: '吉林税务医疗票据(24.2×15.1cm)' })
export const MM240_140_wujiang = extendPageSize(MM240_140, { name: '240×140' })
export const MM251_93_HENAN_TAX = extendPageSize(MM251_93, { name: '河南税务医疗票据(25.1×9.3cm)' })
export const MM195_140_TIANJIN = extendPageSize(MM195_140, { name: '天津市医疗票据(19.5×14.0cm)' })
export const MM159_101_GANSU = extendPageSize(MM159_101, { name: '甘肃省医疗票据(税务15.9×10.1cm)' })
export const MM190_127_GANSU = extendPageSize(MM190_127, { name: '甘肃省医疗票据(财政19×12.7cm)' })
export const MM93_229_SHANXI_JIN = extendPageSize(MM93_229, { name: '山西省医疗票据(9.3×22.9cm)' })
export const MM210_127_GUANGDONG_NEW = extendPageSize(MM210_127, { name: '广东省医疗票据（新版）(21×12.7cm)' })
export const MM210_127_XIAMEN_XIN = extendPageSize(MM210_127, { name: '厦门市医疗收费票据（新版）(21×12.7cm)' })
export const MM240_93_TANGSHAN = extendPageSize(MM240_93, { name: '河北省医疗收费票据（唐山）(24×9.3cm)' })
export const MM192_102_WUHAN = extendPageSize(MM192_102, { name: '武汉市医疗收费票据(19.2×10.2cm)' })
export const MM129_200_TAIYUAN = extendPageSize(MM129_200, { name: '太原市医疗收费票据(12.9×20cm)' })


export const TAG40_30 = new PageSize({
    name: '40mm×30mm',
    key: 'TAG40_30',
    width: '40mm',
    height: '30mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const TAG40_60 = new PageSize({
    name: '40mm×60mm',
    key: 'TAG40_60',
    width: '40mm',
    height: '60mm',
    orientations: [
        Orientation.landscape,
    ],
})

export const TAG50_30 = new PageSize({
    name: '50mm×30mm',
    key: 'TAG50_30',
    width: '50mm',
    height: '30mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const TAG60_60 = new PageSize({
    name: '60mm×60mm',
    key: 'TAG60_60',
    width: '60mm',
    height: '60mm',
    orientations: [
        Orientation.portrait,
    ],
})


export const TAG50_40 = new PageSize({
    name: '50mm×40mm',
    key: 'TAG50_40',
    width: '50mm',
    height: '40mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const TAG60_30 = new PageSize({
    name: '60mm×30mm',
    key: 'TAG60_30',
    width: '60mm',
    height: '30mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const TAG60_40 = new PageSize({
    name: '60mm×40mm',
    key: 'TAG60_40',
    width: '60mm',
    height: '40mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const TAG70_40 = new PageSize({
    name: '70mm×40mm',
    key: 'TAG70_40',
    width: '70mm',
    height: '40mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const TAG70_50 = new PageSize({
    name: '70mm×50mm',
    key: 'TAG70_50',
    width: '70mm',
    height: '50mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const TAG75_50 = new PageSize({
    name: '75mm×50mm',
    key: 'TAG75_50',
    width: '75mm',
    height: '50mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const TAG50_70 = new PageSize({
    name: '50mm×70mm',
    key: 'TAG50_70',
    width: '50mm',
    height: '70mm',
    orientations: [
        Orientation.landscape,
    ],
})

export const TAG80_40 = new PageSize({
    name: '80mm×40mm',
    key: 'TAG80_40',
    width: '80mm',
    height: '40mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const TAG80_50 = new PageSize({
    name: '80mm×50mm',
    key: 'TAG80_50',
    width: '80mm',
    height: '50mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const TAG80_60 = new PageSize({
    name: '80mm×60mm',
    key: 'TAG80_60',
    width: '80mm',
    height: '60mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM70_120 = new PageSize({
    name: '小票纸(7x12cm)',
    key: 'MM70_120',
    width: '70mm',
    height: '120mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM80_100 = new PageSize({
    name: '小票纸(8x10cm)',
    key: 'MM80_100',
    width: '80mm',
    height: '100mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM100_120 = new PageSize({
    name: '小票纸(10x12cm)',
    key: 'MM100_120',
    width: '100mm',
    height: '120mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM100_140 = new PageSize({
    name: '小票纸(10x14cm)',
    key: 'MM100_140',
    width: '100mm',
    height: '140mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM241_279 = new PageSize({
    name: '小票纸(24.1x27.9cm)',
    key: 'MM241_279',
    width: '241mm',
    height: '279mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM140_190 = new PageSize({
    name: '小票纸(14x19cm)',
    key: 'MM140_140',
    width: '140mm',
    height: '190mm',
    orientations: [
        Orientation.portrait,
    ],
})


export const MM200_1397 = new PageSize({
    name: '小票纸(20x13.97cm)',
    key: 'M200_1397',
    width: '200mm',
    height: '139.7mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM175_94 = new PageSize({
    name: '小票纸(17.5x9.4cm)',
    key: 'M175_94',
    width: '175mm',
    height: '94mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM120_93_CASHIER = new PageSize({
    name: '小票纸(12x9.3cm)',
    key: 'M120_93_CASHIER',
    width: '120mm',
    height: '93mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM175_94_CASHIER = new PageSize({
    name: '小票纸(17.5x9.4cm)',
    key: 'MM175_94_CASHIER',
    width: '175mm',
    height: '94mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM120_200 = new PageSize({
    name: '小票纸(12x20cm)',
    key: 'MM120_200',
    width: '120mm',
    height: '200mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM121_70 = new PageSize({
    name: '小票纸(121x70cm)',
    key: 'MM121_70',
    width: '121mm',
    height: '70mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM140_230 = new PageSize({
    name: '小票纸(14x23cm)',
    key: 'MM140_230',
    width: '140mm',
    height: '230mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM120_240 = new PageSize({
    name: '小票纸(12x24cm)',
    key: 'MM120_240',
    width: '120mm',
    height: '240mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM90_120_CASHIER = new PageSize({
    name: '小票纸(9x12cm)',
    key: 'MM90_120_CASHIER',
    width: '90mm',
    height: '120mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM148_118_CASHIER = new PageSize({
    name: '小票纸(14.8x11.8cm)',
    key: 'MM148_118_CASHIER',
    width: '148mm',
    height: '118mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM75_75 = new PageSize({
    name: '小票纸(7.5x7.5cm)',
    key: 'MM75_75',
    width: '75mm',
    height: '75mm',
    orientations: [
        Orientation.portrait,
    ],
})
export const MM200_127 = new PageSize({
    name: '处方纸(20x12.7cm)',
    key: 'MM200_127',
    width: '200mm',
    height: '127mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM150_203 = new PageSize({
    name: '处方纸(15x20.3cm)',
    key: 'MM150_203',
    width: '150mm',
    height: '203mm',
    orientations: [
        Orientation.portrait,
    ],
})

export const MM130_193 = new PageSize({
    name: '处方纸(13x19.3cm)',
    key: 'MM130_193',
    width: '130mm',
    height: '193mm',
    orientations: [
        Orientation.portrait,
    ],
})


export const MMA4_AUTO = new PageSize({
    name: 'A4(自动)',
    key: 'MMA4_AUTO',
    width: '210mm',
    height: 'auto',
    orientations: [
        Orientation.portrait,
    ],
    heightLevels: null,
});

export default {
    A4,
    A5,
    A6,
    B5,
    B6,
    B6_LANSCAPE,
    MM76_130,
    MM80,
    MM90,
    MM100,
    MM58,
    MM82,
    MM156,
    MM114,
    MM80_100,
    MM100_120,
    MM100_140,
    MM140_190,
    MM200_1397,
    MM114_LIAONING,
    NeedleMultiPaper,
    MedicalNeedleMultiPaper,
    MM211_127,
    MM211_127_ANHUI,
    MM211_127_CHONGQING,
    MM211_127_GUANGXI,
    MM211_127_LIAONING,
    MM211_127_NATIONAL,
    MM211_127_ZHEJIANG,
    MM85_100,
    MM85_100_SICHUANG,
    MM85_100_CHONGQING,
    MM240_92,
    MM240_92_CHONGQING,
    MM240_90,
    MM240_90_QINGDAO,
    MM186_101,
    MM186_101_GUANGDONG,
    MM190_101,
    MM190_101_HUBEI,
    MM191_151,
    MM120_95,
    MM120_95_HUNAN,
    MM120_94_HUNAN,
    MM211_119,
    MM211_119_SHANGHAI,
    MM211_140_SHANGHAI,
    MM120_127,
    MM120_127_SHANXI,
    MM120_93,
    MM120_93_SHENYANG,
    MM126_70,
    MM126_70_YUNNAN,
    MM120_76,
    MM120_76_SHANXI_TAX,
    MM210_127,
    MM210_127_JIANGSU,
    MM210_127_YUNNAN,
    MM210_127_ZHEJIANG,
    MM210_127_SHANDONG,
    MM240_93_TANGSHAN,
    MM192_102_WUHAN,
    MM241_94_WUHU,
    MM241_93,
    MM241_93_HEFEI,
    MM241_93_ANHUI_XUANCHENG,
    MM82_101,
    MM82_101_SICHUANG_TAX,
    MM281_127_HENAN,
    MM240_101_HUHEHAOTE,
    MM122_202_JIANGXI,
    MM241_279,
    MM120_93_CASHIER,
    MM148_118_CASHIER,
    MM175_94,
    MM120_202_zhejiang,
    MM126_165_qingdao,
    MM120_212_HEBEI,
    MM210_127_HOSPITAL_YUNNAN,
    MM129_200_TAIYUAN,
    TAG40_30,
    TAG40_60,
    TAG50_30,
    TAG50_40,
    TAG60_30,
    TAG60_40,
    TAG70_40,
    TAG70_50,
    TAG75_50,
    TAG50_70,
    TAG80_40,
    TAG80_50,
    TAG60_60,
    TAG80_60,
    MM175_94_CASHIER,
    MM120_200,
    MM90_120_CASHIER,
    MM70_120,
    MM140_230,
    MM120_114,
    MM121_94,
    MM121_70,
    MM120_240,
    MM200_114,
    MM210_139,
    MM210_139_zhejiang,
    MM210_139_ningbo,
    MM114_127,
    MM140_203,
    MM95_190,
    MM210_139_heilongjiang,
    MM220_114,
    MM160_101,
    MM240_114_kunshan,
    MM240_140_jilin,
    MM240_93,
    MM251_93_HENAN_TAX,
    MM240_140_wujiang,
    MM195_140_TIANJIN,
    MM93_229_SHANXI_JIN,
    MM210_127_GUANGDONG_NEW,
    MM159_101_GANSU,
    MM190_127_GANSU,
    MM242_151,
    MM242_151_JILIN_TAX,
    MM75_75,
    MM200_127,
    MM150_203,
    MM210_127_XIAMEN_XIN,
    MM130_193,
    MMA4_AUTO,
    MM148_70,
    MM110_50,
};
