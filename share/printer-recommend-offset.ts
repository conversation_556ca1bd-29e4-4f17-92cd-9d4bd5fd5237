import Logger from "../src/logger";

const recommonendRules =  [
    {
        name: '得力DB-615KII',
        matcher: /^Deli DB-615KII.*?/,
        pageSizeMatcher: [
            {
                name: 'A4',
                matcher: /A4.*?/,
                offset: {
                    left: 1,
                    top: 5,
                }
            },
            {
                name: '*',
                matcher: /.*?/,
                offset: {
                    left: -1,
                    top: -4,
                }
            }
        ]
    },
    {
        name: '得力通用',
        matcher: /^Deli/,
        pageSizeMatcher: [
            {
                name: 'A4',
                matcher: /A4.*?/,
                offset: {
                    left: 1,
                    top: 5,
                }
            },
            {
                name: '*',
                matcher: /.*?/,
                offset: {
                    left: -1,
                    top: -4,
                }
            }
        ]
    },
    {
        name: '爱普生通用',
        matcher: /^EPSON/,
        pageSizeMatcher: [
            {
                name: 'A4',
                matcher: /A4.*?/,
                offset: {
                    left: 2,
                    top: 2,
                }
            },
            {
                name: '*',
                matcher: /.*?/,
                offset: {
                    left: 2,
                    top: 2,
                }
            }
        ]
    },
    {
        name: '其他打印机',
        matcher: /.*?/,
        pageSizeMatcher: [
            {
                name: '*',
                matcher: /.*?/,
                offset: {
                    left: 0,
                    top: 0,
                }
            }
        ]
    }
]

export default function recommendOffsetMatcher(deviceName: string, pageSize: string) {
    for(let i = 0; i < recommonendRules.length; i ++) {
        if(recommonendRules[i].matcher.test(deviceName)) {
            Logger.log(`[${deviceName} ${pageSize}] 匹配到 ===>`)
            Logger.log(JSON.stringify(recommonendRules[i], null, 2))
            const { pageSizeMatcher } = recommonendRules[i];
            for (let j = 0; j < pageSizeMatcher.length; j ++) {
                if(pageSizeMatcher[j].matcher.test(pageSize)) {
                    Logger.log(`根据页面匹配 ====>`)
                    Logger.log(JSON.stringify(pageSizeMatcher[j], null, 2))
                    return pageSizeMatcher[j].offset
                }
            }
            Logger.log(`默认匹配第一个 ====>`)
            Logger.log(JSON.stringify(pageSizeMatcher[0], null, 2))
            return  pageSizeMatcher[0].offset;
        }
    }
}
