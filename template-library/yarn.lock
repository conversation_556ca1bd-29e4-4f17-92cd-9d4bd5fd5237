# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/helper-validator-identifier@^7.14.5":
  version "7.14.5"
  resolved "http://npm.abczs.cn/@babel%2fhelper-validator-identifier/-/helper-validator-identifier-7.14.5.tgz#d0f0e277c512e0c938277faa85a3968c9a44c0e8"
  integrity sha1-0PDid8US4Mk4J3+qhaOWjJpEwOg=

"@babel/parser@^7.12.0", "@babel/parser@^7.13.9":
  version "7.14.7"
  resolved "http://npm.abczs.cn/@babel%2fparser/-/parser-7.14.7.tgz#6099720c8839ca865a2637e6c85852ead0bdb595"
  integrity sha1-YJlyDIg5yoZaJjfmyFhS6tC9tZU=

"@babel/types@^7.12.0", "@babel/types@^7.13.0":
  version "7.14.5"
  resolved "http://npm.abczs.cn/@babel%2ftypes/-/types-7.14.5.tgz#3bb997ba829a2104cedb20689c4a5b8121d383ff"
  integrity sha1-O7mXuoKaIQTO2yBonEpbgSHTg/8=
  dependencies:
    "@babel/helper-validator-identifier" "^7.14.5"
    to-fast-properties "^2.0.0"

"@types/estree@^0.0.48":
  version "0.0.48"
  resolved "http://npm.abczs.cn/@types%2festree/-/estree-0.0.48.tgz#18dc8091b285df90db2f25aa7d906cfc394b7f74"
  integrity sha1-GNyAkbKF35DbLyWqfZBs/DlLf3Q=

"@vitejs/plugin-vue@^1.2.5":
  version "1.2.5"
  resolved "http://npm.abczs.cn/@vitejs%2fplugin-vue/-/plugin-vue-1.2.5.tgz#ef7dc4a92e53fe866b54bcc1266788513262ac09"
  integrity sha1-733EqS5T/oZrVLzBJmeIUTJirAk=

"@vue/compiler-core@3.1.4":
  version "3.1.4"
  resolved "http://npm.abczs.cn/@vue%2fcompiler-core/-/compiler-core-3.1.4.tgz#a3a74cf52e8f01af386d364ac8a099cbeb260424"
  integrity sha1-o6dM9S6PAa84bTZKyKCZy+smBCQ=
  dependencies:
    "@babel/parser" "^7.12.0"
    "@babel/types" "^7.12.0"
    "@vue/shared" "3.1.4"
    estree-walker "^2.0.1"
    source-map "^0.6.1"

"@vue/compiler-dom@3.1.4":
  version "3.1.4"
  resolved "http://npm.abczs.cn/@vue%2fcompiler-dom/-/compiler-dom-3.1.4.tgz#bf3795e1449f32c965d38c4ea6d808ca05fdfc97"
  integrity sha1-vzeV4USfMsll04xOptgIygX9/Jc=
  dependencies:
    "@vue/compiler-core" "3.1.4"
    "@vue/shared" "3.1.4"

"@vue/compiler-sfc@^3.0.5":
  version "3.1.4"
  resolved "http://npm.abczs.cn/@vue%2fcompiler-sfc/-/compiler-sfc-3.1.4.tgz#93e87db950e0711339c18baa7bb7d28d3522d7bc"
  integrity sha1-k+h9uVDgcRM5wYuqe7fSjTUi17w=
  dependencies:
    "@babel/parser" "^7.13.9"
    "@babel/types" "^7.13.0"
    "@types/estree" "^0.0.48"
    "@vue/compiler-core" "3.1.4"
    "@vue/compiler-dom" "3.1.4"
    "@vue/compiler-ssr" "3.1.4"
    "@vue/shared" "3.1.4"
    consolidate "^0.16.0"
    estree-walker "^2.0.1"
    hash-sum "^2.0.0"
    lru-cache "^5.1.1"
    magic-string "^0.25.7"
    merge-source-map "^1.1.0"
    postcss "^8.1.10"
    postcss-modules "^4.0.0"
    postcss-selector-parser "^6.0.4"
    source-map "^0.6.1"

"@vue/compiler-ssr@3.1.4":
  version "3.1.4"
  resolved "http://npm.abczs.cn/@vue%2fcompiler-ssr/-/compiler-ssr-3.1.4.tgz#7f6eaac5b1851fc15c82c083e8179eb1216b303c"
  integrity sha1-f26qxbGFH8FcgsCD6BeesSFrMDw=
  dependencies:
    "@vue/compiler-dom" "3.1.4"
    "@vue/shared" "3.1.4"

"@vue/reactivity@3.1.4":
  version "3.1.4"
  resolved "http://npm.abczs.cn/@vue%2freactivity/-/reactivity-3.1.4.tgz#d926ed46fb0d48582ccf8665b062d37b5d35ba99"
  integrity sha1-2SbtRvsNSFgsz4ZlsGLTe101upk=
  dependencies:
    "@vue/shared" "3.1.4"

"@vue/runtime-core@3.1.4":
  version "3.1.4"
  resolved "http://npm.abczs.cn/@vue%2fruntime-core/-/runtime-core-3.1.4.tgz#3e30ae6ecbfff06df5adc9414491143191a375ba"
  integrity sha1-PjCubsv/8G31rclBRJEUMZGjdbo=
  dependencies:
    "@vue/reactivity" "3.1.4"
    "@vue/shared" "3.1.4"

"@vue/runtime-dom@3.1.4":
  version "3.1.4"
  resolved "http://npm.abczs.cn/@vue%2fruntime-dom/-/runtime-dom-3.1.4.tgz#acfeee200d5c45fc2cbdf7058cda1498f9b45849"
  integrity sha1-rP7uIA1cRfwsvfcFjNoUmPm0WEk=
  dependencies:
    "@vue/runtime-core" "3.1.4"
    "@vue/shared" "3.1.4"
    csstype "^2.6.8"

"@vue/shared@3.1.4":
  version "3.1.4"
  resolved "http://npm.abczs.cn/@vue%2fshared/-/shared-3.1.4.tgz#c14c461ec42ea2c1556e86f60b0354341d91adc3"
  integrity sha1-wUxGHsQuosFVbob2CwNUNB2RrcM=

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://npm.abczs.cn/big.js/-/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

bluebird@^3.7.2:
  version "3.7.2"
  resolved "http://npm.abczs.cn/bluebird/-/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==

colorette@^1.2.2:
  version "1.2.2"
  resolved "http://npm.abczs.cn/colorette/-/colorette-1.2.2.tgz#cbcc79d5e99caea2dbf10eb3a26fd8b3e6acfa94"
  integrity sha1-y8x51emcrqLb8Q6zom/Ys+as+pQ=

consolidate@^0.16.0:
  version "0.16.0"
  resolved "http://npm.abczs.cn/consolidate/-/consolidate-0.16.0.tgz#a11864768930f2f19431660a65906668f5fbdc16"
  integrity sha1-oRhkdokw8vGUMWYKZZBmaPX73BY=
  dependencies:
    bluebird "^3.7.2"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://npm.abczs.cn/cssesc/-/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

csstype@^2.6.8:
  version "2.6.17"
  resolved "http://npm.abczs.cn/csstype/-/csstype-2.6.17.tgz#4cf30eb87e1d1a005d8b6510f95292413f6a1c0e"
  integrity sha1-TPMOuH4dGgBdi2UQ+VKSQT9qHA4=

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "http://npm.abczs.cn/emojis-list/-/emojis-list-3.0.0.tgz#5570662046ad29e2e916e71aae260abdff4f6a78"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

esbuild@^0.12.8:
  version "0.12.15"
  resolved "http://npm.abczs.cn/esbuild/-/esbuild-0.12.15.tgz#9d99cf39aeb2188265c5983e983e236829f08af0"
  integrity sha1-nZnPOa6yGIJlxZg+mD4jaCnwivA=

estree-walker@^2.0.1:
  version "2.0.2"
  resolved "http://npm.abczs.cn/estree-walker/-/estree-walker-2.0.2.tgz#52f010178c2a4c117a7757cfe942adb7d2da4cac"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

fsevents@~2.3.2:
  version "2.3.2"
  resolved "http://npm.abczs.cn/fsevents/-/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://npm.abczs.cn/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==

generic-names@^2.0.1:
  version "2.0.1"
  resolved "http://npm.abczs.cn/generic-names/-/generic-names-2.0.1.tgz#f8a378ead2ccaa7a34f0317b05554832ae41b872"
  integrity sha1-+KN46tLMqno08DF7BVVIMq5BuHI=
  dependencies:
    loader-utils "^1.1.0"

has@^1.0.3:
  version "1.0.3"
  resolved "http://npm.abczs.cn/has/-/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
  dependencies:
    function-bind "^1.1.1"

hash-sum@^2.0.0:
  version "2.0.0"
  resolved "http://npm.abczs.cn/hash-sum/-/hash-sum-2.0.0.tgz#81d01bb5de8ea4a214ad5d6ead1b523460b0b45a"
  integrity sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==

icss-replace-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://npm.abczs.cn/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz#06ea6f83679a7749e386cfe1fe812ae5db223ded"
  integrity sha1-Bupvg2ead0njhs/h/oEq5dsiPe0=

icss-utils@^5.0.0:
  version "5.1.0"
  resolved "http://npm.abczs.cn/icss-utils/-/icss-utils-5.1.0.tgz#c6be6858abd013d768e98366ae47e25d5887b1ae"
  integrity sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=

is-core-module@^2.2.0:
  version "2.5.0"
  resolved "http://npm.abczs.cn/is-core-module/-/is-core-module-2.5.0.tgz#f754843617c70bfd29b7bd87327400cda5c18491"
  integrity sha1-91SENhfHC/0pt72HMnQAzaXBhJE=
  dependencies:
    has "^1.0.3"

json5@^1.0.1:
  version "1.0.1"
  resolved "http://npm.abczs.cn/json5/-/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe"
  integrity sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==
  dependencies:
    minimist "^1.2.0"

loader-utils@^1.1.0:
  version "1.4.0"
  resolved "http://npm.abczs.cn/loader-utils/-/loader-utils-1.4.0.tgz#c579b5e34cb34b1a74edc6c1fb36bfa371d5a613"
  integrity sha512-qH0WSMBtn/oHuwjy/NucEgbx5dbxxnxup9s4PVXJUDHZBQY+s0NWA9rJf53RBnQZxfch7euUui7hpoAPvALZdA==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "http://npm.abczs.cn/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz#b28aa6288a2b9fc651035c7711f65ab6190331a6"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://npm.abczs.cn/lru-cache/-/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

magic-string@^0.25.7:
  version "0.25.7"
  resolved "http://npm.abczs.cn/magic-string/-/magic-string-0.25.7.tgz#3f497d6fd34c669c6798dcb821f2ef31f5445051"
  integrity sha1-P0l9b9NMZpxnmNy4IfLvMfVEUFE=
  dependencies:
    sourcemap-codec "^1.4.4"

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "http://npm.abczs.cn/merge-source-map/-/merge-source-map-1.1.0.tgz#2fdde7e6020939f70906a68f2d7ae685e4c8c646"
  integrity sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==
  dependencies:
    source-map "^0.6.1"

minimist@^1.2.0:
  version "1.2.5"
  resolved "http://npm.abczs.cn/minimist/-/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

monaco-editor@^0.26.1:
  version "0.26.1"
  resolved "http://npm.abczs.cn/monaco-editor/-/monaco-editor-0.26.1.tgz#62bb5f658bc95379f8abb64b147632bd1c019d73"
  integrity sha1-YrtfZYvJU3n4q7ZLFHYyvRwBnXM=

nanoid@^3.1.23:
  version "3.1.23"
  resolved "http://npm.abczs.cn/nanoid/-/nanoid-3.1.23.tgz#f744086ce7c2bc47ee0a8472574d5c78e4183a81"
  integrity sha1-90QIbOfCvEfuCoRyV01ceOQYOoE=

path-parse@^1.0.6:
  version "1.0.7"
  resolved "http://npm.abczs.cn/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

postcss-modules-extract-imports@^3.0.0:
  version "3.0.0"
  resolved "http://npm.abczs.cn/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.0.0.tgz#cda1f047c0ae80c97dbe28c3e76a43b88025741d"
  integrity sha1-zaHwR8CugMl9vijD52pDuIAldB0=

postcss-modules-local-by-default@^4.0.0:
  version "4.0.0"
  resolved "http://npm.abczs.cn/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.0.tgz#ebbb54fae1598eecfdf691a02b3ff3b390a5a51c"
  integrity sha1-67tU+uFZjuz99pGgKz/zs5ClpRw=
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.0.0:
  version "3.0.0"
  resolved "http://npm.abczs.cn/postcss-modules-scope/-/postcss-modules-scope-3.0.0.tgz#9ef3151456d3bbfa120ca44898dfca6f2fa01f06"
  integrity sha1-nvMVFFbTu/oSDKRImN/Kby+gHwY=
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "http://npm.abczs.cn/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz#d7c5e7e68c3bb3c9b27cbf48ca0bb3ffb4602c9c"
  integrity sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=
  dependencies:
    icss-utils "^5.0.0"

postcss-modules@^4.0.0:
  version "4.1.3"
  resolved "http://npm.abczs.cn/postcss-modules/-/postcss-modules-4.1.3.tgz#c4c4c41d98d97d24c70e88dacfc97af5a4b3e21d"
  integrity sha1-xMTEHZjZfSTHDojaz8l69aSz4h0=
  dependencies:
    generic-names "^2.0.1"
    icss-replace-symbols "^1.1.0"
    lodash.camelcase "^4.3.0"
    postcss-modules-extract-imports "^3.0.0"
    postcss-modules-local-by-default "^4.0.0"
    postcss-modules-scope "^3.0.0"
    postcss-modules-values "^4.0.0"
    string-hash "^1.1.1"

postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4:
  version "6.0.6"
  resolved "http://npm.abczs.cn/postcss-selector-parser/-/postcss-selector-parser-6.0.6.tgz#2c5bba8174ac2f6981ab631a42ab0ee54af332ea"
  integrity sha1-LFu6gXSsL2mBq2MaQqsO5UrzMuo=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.1.0:
  version "4.1.0"
  resolved "http://npm.abczs.cn/postcss-value-parser/-/postcss-value-parser-4.1.0.tgz#443f6a20ced6481a2bda4fa8532a6e55d789a2cb"
  integrity sha1-RD9qIM7WSBor2k+oUypuVdeJoss=

postcss@^8.1.10, postcss@^8.3.5:
  version "8.3.5"
  resolved "http://npm.abczs.cn/postcss/-/postcss-8.3.5.tgz#982216b113412bc20a86289e91eb994952a5b709"
  integrity sha1-mCIWsRNBK8IKhiiekeuZSVKltwk=
  dependencies:
    colorette "^1.2.2"
    nanoid "^3.1.23"
    source-map-js "^0.6.2"

resolve@^1.20.0:
  version "1.20.0"
  resolved "http://npm.abczs.cn/resolve/-/resolve-1.20.0.tgz#629a013fb3f70755d6f0b7935cc1c2c5378b1975"
  integrity sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=
  dependencies:
    is-core-module "^2.2.0"
    path-parse "^1.0.6"

rollup@^2.38.5:
  version "2.53.2"
  resolved "http://npm.abczs.cn/rollup/-/rollup-2.53.2.tgz#3279f9bfba1fe446585560802e418c5fbcaefa51"
  integrity sha1-Mnn5v7of5EZYVWCALkGMX7yu+lE=
  optionalDependencies:
    fsevents "~2.3.2"

source-map-js@^0.6.2:
  version "0.6.2"
  resolved "http://npm.abczs.cn/source-map-js/-/source-map-js-0.6.2.tgz#0bb5de631b41cfbda6cfba8bd05a80efdfd2385e"
  integrity sha1-C7XeYxtBz72mz7qL0FqA79/SOF4=

source-map@^0.6.1:
  version "0.6.1"
  resolved "http://npm.abczs.cn/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

sourcemap-codec@^1.4.4:
  version "1.4.8"
  resolved "http://npm.abczs.cn/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz#ea804bd94857402e6992d05a38ef1ae35a9ab4c4"
  integrity sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=

string-hash@^1.1.1:
  version "1.1.3"
  resolved "http://npm.abczs.cn/string-hash/-/string-hash-1.1.3.tgz#e8aafc0ac1855b4666929ed7dd1275df5d6c811b"
  integrity sha1-6Kr8CsGFW0Zmkp7X3RJ1311sgRs=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://npm.abczs.cn/to-fast-properties/-/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "http://npm.abczs.cn/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

vite@^2.4.2:
  version "2.4.2"
  resolved "http://npm.abczs.cn/vite/-/vite-2.4.2.tgz#07d00615775c808530bc9f65641062b349b67929"
  integrity sha1-B9AGFXdcgIUwvJ9lZBBis0m2eSk=
  dependencies:
    esbuild "^0.12.8"
    postcss "^8.3.5"
    resolve "^1.20.0"
    rollup "^2.38.5"
  optionalDependencies:
    fsevents "~2.3.2"

vue@^3.0.5:
  version "3.1.4"
  resolved "http://npm.abczs.cn/vue/-/vue-3.1.4.tgz#120d6818c51eaa35d0879e5bc1cff60135bc69fd"
  integrity sha1-Eg1oGMUeqjXQh55bwc/2ATW8af0=
  dependencies:
    "@vue/compiler-dom" "3.1.4"
    "@vue/runtime-dom" "3.1.4"
    "@vue/shared" "3.1.4"

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://npm.abczs.cn/yallist/-/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==
