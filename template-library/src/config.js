export default {
    "data": {
        "print": {
            "inventoryGoodsIn": {
                // 抬头信息
                "header": {
                    "goodsInStore": 1, // 入库门店
                    "goodsInWarehouse": 1, // 入库库房
                    "goodsInDate": 1, // 入库日期
                    "goodsInOrder": 1, // 入库单号
                    "supplier": 1, // 供应商
                    "supplierOrder": 1, // 随货单号
                },
                "content": {
                    // 药品信息
                    "goodsInfo": {
                        "goodsCode": 1, // 药品编码
                        "goodsName": 1, // 药品名称
                        "goodsSpec": 1, // 规格
                        "goodsManufacturer": 1, // 厂家
                        "goodsMedicineNmpn": 1, // 批准文号
                        "goodsDosageFormType": 1, // 剂型
                        "goodsType": 1, // 类型
                        "goodsSecondClass": 1, // 二级分类
                        "goodsPrice": 1, // 售价
                        "goodsTaxRate": 1, // 税率
                    },
                    // 入库信息
                    "goodsInInfo": {
                        "goodsBatch": 1, // 生产批号
                        "goodsProductionDate": 1, // 生产日期
                        "goodsExpiryDate": 1, // 效期
                        "goodsNumber": 1, // 数量
                        "goodsPurchasePrice": 1, // 进价
                        "goodsTaxAmount": 1, // 含税金额
                        "goodsNoTaxAmount": 1, // 无税金额
                        "goodsGrossProfitRate": 1, // 预计毛利率
                    },
                },
                "footer": {
                    // 统计信息
                    "statisticInfo": {
                        "totalAmountChinese": 1, // 合计金额
                        "totalAmount": 1, // 合计金额（大写）
                        "noTaxTotal": 1, // 无税合计
                        "totalVariety": 1, // 合计品种
                    },
                    // 签字行
                    "signInfo": {
                        "consignee": 1, // 收货人
                        "acceptor": 1, // 验收人
                        "quality": 1, // 质量情况
                        "custodian": 1, // 保管人
                        "maker": 1, // 制单人
                    },
                },
            },
            "medicineTag": {
                "chineseRemark": "服用期间勿饮酒",
                "mobile": 1,
                "mobileType": 1,
                "printDate": 1,
                "title": "高新大源店ddd",
                "westernDays": 1,
            },
            "test": {
                "test3": {
                    "leaf1": 1,
                },
            },
            "bill": {
                "anhui": {
                    "chargeItems": 1,
                    "composeType": 2,
                    "direction": "horizontal",
                    "institutionName": "高新大源高新大源高新大源高新大源高新大源",
                    "medicalOrganizationType": 0,
                    "splitType": 1,
                    "type": 0,
                },
                "chengdu": {
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "高新大源店高新大源店高新大源店高新大源店",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 1,
                    "splitType": 0,
                    "type": 1,
                },
                "chongqing": {
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "123123",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "chongqingNational": {
                    "chargeItems": 1,
                    "composeType": 1,
                    "direction": "horizontal",
                    "institutionName": "都",
                    "medicalOrganizationType": 1,
                    "splitType": 1,
                    "type": 0,
                },
                "format": "tianjin",
                "gansu": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "guangdong": {
                    "chargeInstitution": 1,
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "flowNumber": 1,
                    "institutionName": "高新大源店高新大源店高新大源店高新大源店",
                    "medicalOrganizationType": 0,
                    "medicalRecordNumber": 1,
                    "socialSecurityNumber": 1,
                    "splitType": 0,
                    "type": 0,
                },
                "guangxi": {
                    "chargeItems": 1,
                    "composeType": 1,
                    "direction": "horizontal",
                    "institutionName": "你好你好",
                    "medicalOrganizationType": 1,
                    "splitType": 0,
                    "type": 0,
                },
                "hebei": {
                    "chargeItems": 1,
                    "composeType": 1,
                    "direction": "horizontal",
                    "institutionName": "阿哒哒",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "hefei": {
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "12313123",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "heilongjiang": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "收费单位",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "henan": {
                    "type": 0,
                },
                "huangshan": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "hubei": {
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "131312321313",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 1,
                    "splitType": 0,
                    "type": 0,
                },
                "huhehaote": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "",
                    "medicalOrganizationType": 0,
                    "splitType": 1,
                    "type": 0,
                },
                "hunan": {
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "jiangsu": {
                    "chargeInstitution": 1,
                    "chargeItems": 1,
                    "composeType": 2,
                    "direction": "horizontal",
                    "institutionName": "调整示例数据123123",
                    "medicalOrganizationType": 1,
                    "splitType": 0,
                    "type": 0,
                },
                "jiangxi": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "哈哈哈嗝",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "jilin": {
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "1341234",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "kunshan": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "liaoning": {
                    "chargeItems": 1,
                    "composeType": 2,
                    "direction": "horizontal",
                    "institutionName": "",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "liaoningNational": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "112313",
                    "medicalOrganizationType": 0,
                    "splitType": 1,
                    "type": 0,
                },
                "national": {
                    "chargeInstitution": 1,
                    "chargeItems": 0,
                    "composeType": 1,
                    "direction": "horizontal",
                    "institutionName": "高新大源店",
                    "medicalOrganizationType": 1,
                    "splitType": 0,
                    "type": 0,
                },
                "ningbo": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "",
                    "isShowOwnPaySign": 1,
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "ningxia": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "fasdad",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "qingdao": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "shanghai": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "高新高新高新高新高新高新高新高新高新高新",
                    "medicalOrganizationType": 0,
                    "splitType": 1,
                    "type": 0,
                },
                "shanxi": {
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "陕西陕西陕西陕西",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "shenyang": {
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "高新大源",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "tianjin": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "aaaa",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "xiamen": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "",
                    "medicalOrganizationType": 0,
                    "outpatientNo": 0,
                    "splitType": 1,
                    "type": 0,
                },
                "yunnan": {
                    "chargeInstitution": 1,
                    "chargeItems": 1,
                    "composeType": 2,
                    "direction": "horizontal",
                    "institutionName": "12213123213",
                    "medicalOrganizationType": 1,
                    "splitType": 0,
                    "type": 0,
                },
                "yunnanOld": {
                    "chargeInstitution": 1,
                    "chargeItems": 0,
                    "composeType": 0,
                    "direction": "horizontal",
                    "institutionName": "高新大源店高新大高新大源店高新",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "zhejiang": {
                    "chargeItems": 1,
                    "composeType": 0,
                    "direction": "vertical",
                    "institutionName": "13123123",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
                "zhejiangNational": {
                    "chargeItems": 1,
                    "composeType": 1,
                    "direction": "horizontal",
                    "institutionName": "313123123",
                    "isShowOwnPaySign": 1,
                    "medicalOrganizationType": 1,
                    "splitType": 0,
                    "type": 0,
                },
                "zhejiangVertical": {
                    "chargeItems": 1,
                    "composeType": 1,
                    "direction": "vertical",
                    "institutionName": "asdddsffweqweqwe",
                    "medicalOrganizationType": 0,
                    "splitType": 0,
                    "type": 0,
                },
            },
            "registrationTag": {
                "enabled": 0,
            },
            "prescription": {
                "address": 1,
                "amount": 1,
                "examination": {
                    "totalPrice": 0,
                    "unitPrice": 1,
                },
                "feeType": 1,
                "infusionExecute": {
                    "includeExternal": 1,
                    "includeTreatment": 1,
                    "spec": 1,
                    "totalCount": 0,
                    "tradeName": 0,
                },
                "latin": 0,
                "logo": 1,
                "medical": {
                    "medicalItem": 0,
                },
                "medicalLatin": 0,
                "mobile": 1,
                "perKindCount": 5,
                "personalCode": 1,
                "pr": {
                    "amount": 1,
                    "amountType": 1,
                    "doctorAdvice": 0,
                    "standardKindCount": 1,
                },
                "socialCode": 0,
                "treatmentExecute": {
                    "totalPrice": 0,
                    "unitPrice": 0,
                },
            },
            "medicalDocuments": {
                "examination": {
                    "content": {
                        "productUnitPrice": 1,
                        "inspectPurpose": 1,
                        "medicalRecord": 1,
                        "examineNo": 2,
                        "composeChildren": 1,
                    },
                    "footer": {
                        "amount": 1,
                        "printDate": 1,
                        "remark": "请按时按量执行，过期作废。药物执行用量请遵医嘱。",
                        "doctorSignature": 1,
                    },
                    "header": {
                        "feeType": 1,
                        "logo": 1,
                        "mobile": 1,
                        "personType": 1,
                        "qrcode": 1,
                        "socialCode": 1,
                        "title": "12345",
                        "barcode": 1,
                        'idCard': 1,
                        'computerCode': 1,
                        'fileNumber': 1,
                        'allergicHistory': 1,
                        'chiefComplaint': 1,
                    },
                },
                "illnessCert": {
                    "header": {
                        "feeType": 1,
                        "logo": 1,
                        "mobile": 1,
                        "qrcode": 1,
                        "title": "12345",
                    },
                },
                "infusion": {
                    "content": {
                        "includeExternal": 0,
                        "includeTreatment": 0,
                        "medicalLatin": 0,
                        "medicineSpec": 1,
                        "medicineTotalCount": 1,
                        "medicineTradeName": 0,
                        "tableHeader": [
                            "执行时间",
                            "执行签名",
                            "患者签名",
                            "执行时间",
                            "执行签名",
                            "患者签名",
                        ],
                        "rightHeader": [
                            "执行时间",
                            "执行签名",
                            "患者签名",
                        ],
                    },
                    "footer": {
                        "amount": 1,
                        "assinger": 1,
                        "check": 1,
                        "dispense": 1,
                        "printDate": 1,
                        "remark": "请按时按量执行，过期作废。药物执行用量请遵医嘱。",
                    },
                    "header": {
                        "feeType": 1,
                        "logo": 0,
                        "mobile": 1,
                        "personType": 0,
                        "qrcode": 1,
                        "signPosition": 1,
                        "socialCode": 0,
                        "title": "12345",
                    },
                },
                "inspection": {
                    "header": {
                        "feeType": 1,
                        "logo": 1,
                        "qrcode": 1,
                        "title": "12345",
                    },
                },
                "medical": {
                    'style': {
                        'themeColor': '#000000',
                    },
                    "content": {
                        "compose": 2,
                        "doctorAdvice": 1,
                        "medicalItem": 1,
                        "medicalLatin": 1,
                        "medicineTradeName": 1,
                        "westernMedicineDays": 1,
                        "westernMedicineSpec": 0,
                        'eyeInspectReport': 1, // 眼科检查报告
                    },
                    "footer": {
                        "amount": 1,
                        "printDate": 1,
                        "remark": "这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注这是备注",
                        'address': 1, // 地址 可选值：0 1
                        'telephone': 1, // 电话 可选值：0 1
                    },
                    "header": {
                        "address": 1,
                        "birthday": 1,
                        "computerCode": 1,
                        "feeType": 1,
                        "fileNumber": 0,
                        "idCard": 1,
                        "logo": 1,
                        "married": 1,
                        "mobile": 2,
                        "nation": 1,
                        "personType": 1,
                        "qrcode": 1,
                        "revisit": 1,
                        "socialCode": 0,
                        "title": "12345",
                        "weight": 1,
                        'emrTitle': 0, // 文书标题
                    },
                },
                "prescription": {
                    "content": {
                        "chineseLayout": 0,
                        "chineseLayoutForHechi": 0,
                        "chineseMedicineCount": 1,
                        "chineseMedicineCountTotal": 0,
                        "chineseMedicineTotalCount": 0,
                        "doctorAdvice": 0,
                        "expressInfo": 0,
                        "medicalLatin": 0,
                        "medicineTradeName": 1,
                        "position": 1,
                        "processInfo": 1,
                        "standardKindCount": 0,
                        "westernMedicineDays": 0,
                        "westernMedicineSpec": 1,
                        "westernPosition": 1,
                    },
                    "footer": {
                        "amount": 1,
                        "assinger": 1,
                        "assingerSignature": 1,
                        "billSign": 1,
                        "check": 1,
                        "checkSignature": 1,
                        "dispense": 1,
                        "dispenseSignature": 1,
                        "doctorSignature": 0,
                        "printDate": 1,
                        "remark": "除主诊医师特别注明外，处方仅当日有效。按卫生部规定，药房药品一经发出。\n",
                    },
                    "header": {
                        "address": 0,
                        "barcode": 1,
                        "birthday": 0,
                        "computerCode": 0,
                        "feeType": 1,
                        "fileNumber": 0,
                        "idCard": 0,
                        "logo": 0,
                        "married": 0,
                        "mobile": 1,
                        "nation": 0,
                        "personType": 0,
                        "prescriptionType": 1,
                        "qrcode": 1,
                        "revisit": 1,
                        "socialCode": 0,
                        "templateType": 0,
                        "title": "12345",
                        "weight": 0,
                    },
                },
                "treatment": {
                    "footer": {
                        "amount": 1,
                        "chargedByName": 1,
                        "doctor": 1,
                        "printDate": 1,
                        "remark": "请按时按量执行，过期作废。治疗执行量请遵医嘱。",
                        "seller": 1,
                    },
                    "header": {
                        "barcode": 0,
                        "computerCode": 1,
                        "enableSignNumber": 0,
                        "feeType": 1,
                        "fileNumber": 0,
                        "idCard": 1,
                        "logo": 0,
                        "mobile": 1,
                        "personType": 1,
                        "qrcode": 1,
                        "signPosition": 2,
                        "socialCode": 1,
                        "subtitle": "",
                        "templateType": 0,
                        "title": "冬暖夏凉（南京栖霞）",
                        "diagnosis": 0,
                    },
                    "content": {
                        "compose": 0,
                        "diagnosisTreatmentPrintUsage": 1,
                        "includeExternalAcupuncture": 1,
                        "onceCount": 1,
                        "printExternalUsageType": 1,
                        "productDays": 0,
                        "productExecutedPrice": 1,
                        "productMaterialsPrice": 1,
                        "productOtherPrice": 1,
                        "productTotalPrice": 1,
                        "productUnitPrice": 1,
                        "remarkOnlyOneRow": 0,
                        "tableHeader": [
                            "执行时间",
                            "执行签名",
                            "患者签名",
                            "执行时间",
                            "执行签名",
                            "患者签名",
                        ],
                        "treatmentPhysicalSeparate": 1,
                        "signatureRowNum": 1,
                    },
                },
                "examineReport": {
                    content: {
                        bloodHistogram: 1, // 血常规直方图
                        'itemCode': 1, //项目代码
                        'comment': 0,//备注
                    },
                    'footer': {
                        'applyDate': 1, // 申请时间
                        'checkDate': 1, // 审核时间
                        'printDate': 1, // 打印时间
                        'remark': '备注备注备注', // 页尾备注
                        'examineDate': 1, //检验时间
                        'testerSignature': 1, // 检验者签名
                        'checkerSignature': 1, // 审核者签名
                        'mutualRecognitionDesc': 1,
                    },
                    "header": {
                        "barcode": 1,
                        "examineName": 1,
                        "logo": 1,
                        "title": "本实验结果仅本实验结果仅本实验结果仅本实验结果仅",
                        "isCustomLogo": 0,
                        'patientSn': 0,//档案号
                    },
                },
                "defaultInspectReport": {
                    "style": {
                        "fontSize": 0,
                    },
                    "content": {
                        'name': 1,
                        'gender': 1,
                        'age': 1,
                        'patientOrderNumber': 1,
                        'orderNo': 1,
                        'departmentName': 1,
                        'doctorName': 1,
                        'created': 1,
                        'diagnosisText': 1,
                        'orderName': 1,
                        'orderTitle': '',
                        'orderSubTitle': '',
                    },
                    "footer": {
                        'checkerName': 1,//操作医师
                        'recordDoctorName': 1,//记录医师
                        'testTime': 1, // 检查时间
                        'checkTime': 1, // 审核时间
                        'reportTime': 1, // 报告时间
                        'remark': '本检查结果仅反应受检查者当时的情况，仅供临床医生诊断时参考。', // 页尾备注
                    },
                    "header": {
                        "barcode": 0,
                        "examineName": 1,
                        "logo": 1,
                        "title": "12345",
                        "isCustomLogo": 0,
                        "deviceUuid": 1,
                    },
                },
                "cdusReport": {
                    "style": {
                        "fontSize": 0,
                    },
                    'header': { // 处方前记
                        'title': '', // 抬头名称 字符串必填
                        'subtitle': '', // 副抬头名称 可选值
                        'titleColor': 0,//抬头颜色
                        'assistantTitle': '彩色多普勒超声报告', // 副抬头名称 可使用项目名称
                        'assistantTitleUseProductName': 0,//项目副抬头名称 可使用项目名称
                        'logo': 0, // Logo
                        'inspectionDevice': 1, // 检查设备'
                        'barcode': 0,// 就诊条码
                    },
                    'content': {
                        'patientName': 1, // 姓名
                        'patientSex': 1,//性别
                        'patientAge': 1,//年龄
                        'patientOrderNo': 1,// 门诊/住院/体检号
                        'inspectionOrderNo': 1,//检查单号
                        'applyDepartment': 1,//申请科室
                        'applyDoctor': 1,//申请医生
                        'applyDate': 1,//申请日期
                        'clinicalDiagnosis': 1,//临床诊断
                        'inspectionSite': 1,//检查部位
                        'inspectionHeader': '超声所见',//检查标题
                        'conclusionHeader': '诊断意见',//结论标题
                    },
                    'footer': {
                        'operateDoctor': 1,//操作医师
                        'recordDoctor': 1,//记录医师
                        'inspectionTime': 1, // 检查时间
                        'auditTime': 1, // 审核时间
                        'reportTime': 1, // 报告时间
                        'remark': '本检查结果仅反应受检查者当时的情况，仅供临床医生诊断时参考。', // 页尾备注
                    },
                },
                "ctReport": {
                    "style": {
                        "fontSize": 0,
                    },
                    'header': { // 处方前记
                        'title': '', // 抬头名称 字符串必填
                        'subtitle': '', // 副抬头名称 可选值
                        'titleColor': 0,//抬头颜色
                        'assistantTitle': '彩色多普勒超声报告', // 副抬头名称 可使用项目名称
                        'assistantTitleUseProductName': 0,//项目副抬头名称 可使用项目名称
                        'examineName': 0, // 项目名称
                        'logo': 1, // Logo
                        'inspectionDevice': 1, // 检查设备'
                        'barcode': 1,// 就诊条码
                    },
                    'content': {
                        'patientName': 1, // 姓名
                        'patientSex': 1,//性别
                        'patientAge': 1,//年龄
                        'patientOrderNo': 1,// 门诊/住院/体检号
                        'inspectionOrderNo': 1,//检查单号
                        'applyDepartment': 1,//申请科室
                        'applyDoctor': 1,//申请医生
                        'applyDate': 1,//申请日期
                        'clinicalDiagnosis': 1,//临床诊断
                        'inspectionSite': 1,//检查部位
                        'inspectionHeader': '影像所见',//检查标题
                        'conclusionHeader': '诊断意见',//结论标题
                    },
                    'footer': {
                        'reportDoctor': 1,//报告医师
                        'auditDoctor': 1,//审核医师
                        'inspectionTime': 1, // 检查时间
                        'auditTime': 1, // 审核时间
                        'reportTime': 1, // 报告时间
                        'remark': '本检查结果仅反应受检查者当时的情况，仅供临床医生诊断时参考。', // 页尾备注
                    },
                },
                "drReport": {
                    "style": {
                        "fontSize": 0,
                    },
                    'header': { // 处方前记
                        'title': '', // 抬头名称 字符串必填
                        'subtitle': '', // 副抬头名称 可选值
                        'titleColor': 0,//抬头颜色
                        'assistantTitle': '数字化X射线报告', // 副抬头名称 可使用项目名称
                        'assistantTitleUseProductName': 0,//项目副抬头名称 可使用项目名称
                        'logo': 0, // Logo
                        'inspectionDevice': 1, // 检查设备'
                        'barcode': 0,// 就诊条码
                    },
                    'content': {
                        'patientName': 1, // 姓名
                        'patientSex': 1,//性别
                        'patientAge': 1,//年龄
                        'patientOrderNo': 1,// 门诊/住院/体检号
                        'inspectionOrderNo': 1,//检查单号
                        'applyDepartment': 1,//申请科室
                        'applyDoctor': 1,//申请医生
                        'applyDate': 1,//申请日期
                        'clinicalDiagnosis': 1,//临床诊断
                        'inspectionSite': 1,//检查部位
                        'inspectionHeader': '影像所见',//检查标题
                        'conclusionHeader': '诊断意见',//结论标题
                    },
                    'footer': {
                        'reportDoctor': 1,//报告医师
                        'auditDoctor': 1,//审核医师
                        'inspectionTime': 1, // 检查时间
                        'auditTime': 1, // 审核时间
                        'reportTime': 1, // 报告时间
                        'remark': '本检查结果仅反应受检查者当时的情况，仅供临床医生诊断时参考。', // 页尾备注
                    },
                },
                "mrReport": {
                    "style": {
                        "fontSize": 0,
                    },
                    'header': { // 处方前记
                        'title': '', // 抬头名称 字符串必填
                        'subtitle': '', // 副抬头名称 可选值
                        'titleColor': 0,//抬头颜色
                        'assistantTitle': '核磁共振成像报告', // 副抬头名称 可使用项目名称
                        'assistantTitleUseProductName': 0,//项目副抬头名称 可使用项目名称
                        'logo': 0, // Logo
                        'inspectionDevice': 1, // 检查设备'
                        'barcode': 1,// 就诊条码
                    },
                    'content': {
                        'patientName': 1, // 姓名
                        'patientSex': 1,//性别
                        'patientAge': 1,//年龄
                        'patientOrderNo': 1,// 门诊/住院/体检号
                        'inspectionOrderNo': 1,//检查单号
                        'applyDepartment': 1,//申请科室
                        'applyDoctor': 1,//申请医生
                        'applyDate': 1,//申请日期
                        'clinicalDiagnosis': 1,//临床诊断
                        'inspectionSite': 1,//检查部位
                        'inspectionHeader': '影像所见',//检查标题
                        'conclusionHeader': '诊断意见',//结论标题
                    },
                    'footer': {
                        'reportDoctor': 1,//报告医师
                        'auditDoctor': 1,//审核医师
                        'inspectionTime': 1, // 检查时间
                        'auditTime': 1, // 审核时间
                        'reportTime': 1, // 报告时间
                        'remark': '本检查结果仅反应受检查者当时的情况，仅供临床医生诊断时参考。', // 页尾备注
                    },
                },
                "mgReport": {
                    "style": {
                        "fontSize": 0,
                    },
                    "content": {
                        "applyDate": 1,
                        "applyDepartment": 1,
                        "applyDoctor": 1,
                        "clinicalDiagnosis": 1,
                        "conclusionHeader": "诊断意见",
                        "inspectionHeader": "影像所见",
                        "inspectionOrderNo": 1,
                        "inspectionSite": 1,
                        "patientAge": 1,
                        "patientName": 1,
                        "patientOrderNo": 1,
                        "patientSex": 1,
                    },
                    "footer": {
                        "auditDoctor": 1,
                        "auditDoctorSignature": 1,
                        "auditTime": 1,
                        "consultationDoctor": 1,
                        "consultationDoctorSignature": 1,
                        "inspectionTime": 1,
                        "remark": "本检查结果仅反应受检查者当时的情况，仅供临床医生诊断时参考。",
                        "reportDoctor": 1,
                        "reportDoctorSignature": 1,
                        "reportTime": 1,
                    },
                    "header": {
                        "assistantTitle": "核磁共振成像报告",
                        "assistantTitleUseProductName": 0,
                        "barcode": 1,
                        "inspectionDevice": 1,
                        "logo": 1,
                        "subtitle": "",
                        "title": "ABC医院",
                        "titleColor": 2,
                    },
                },
                "eyeInspectReport": {
                    "content": {
                        "diagnosisAdvice": 1,
                        "testTime": 1,
                        "tester": 1,
                    },
                    "footer": {
                        "address": 1,
                        "createdTime": 1,
                        "optometrist": 1,
                        "printTime": 1,
                        "remark": "本实验结果仅对所检测样本负责，如有疑问请咨询本实验室。",
                        "reporter": 1,
                        "seller": 1,
                        "telephone": 1,
                    },
                    "header": {
                        "barcode": 1,
                        "examineName": 1,
                        "inspectNo": 1,
                        "logo": 1,
                        "patientBirthDate": 1,
                        "relationOutpatient": 1,
                        "subtitle": "",
                        "title": "眼科子店1",
                    },
                    "style": {
                        "themeColor": "#58A0FF",
                    },
                },
                "endoscopeReport": {
                    "style": {
                        "fontSize": 0,
                    },
                    "content": {
                        "applyDate": 1,
                        "applyDepartment": 1,
                        "applyDoctor": 1,
                        "clinicalDiagnosis": 1,
                        "conclusionHeader": "诊断意见",
                        "inspectionHeader": "检查所见",
                        "inspectionOrderNo": 1,
                        "inspectionSite": 1,
                        "patientAge": 1,
                        "patientName": 1,
                        "patientOrderNo": 1,
                        "patientSex": 1,
                    },
                    "footer": {
                        "auditDoctor": 1,
                        "auditDoctorSignature": 2,
                        "auditTime": 1,
                        "consultationDoctor": 1,
                        "consultationDoctorSignature": 0,
                        "inspectionTime": 1,
                        "remark": "本检查结果仅反应受检查者当时的情况，仅供临床医生诊断时参考。",
                        "reportDoctor": 1,
                        "reportDoctorSignature": 2,
                        "reportTime": 1,
                    },
                    "header": {
                        "assistantTitle": "电子内镜检查报告",
                        "assistantTitleUseProductName": 0,
                        "barcode": 1,
                        "deviceModelDesc": 1,
                        "inspectionDevice": 1,
                        "logo": 1,
                        "subtitle": "",
                        "title": "ABC医院",
                        "titleColor": 0,
                    },
                },
            },
            "patientTag": {
                "birthday": 1,
                "doctor": 1,
                "mobile": 1,
                "mobileType": 1,
                "title": "高新大源店ddd",
            },
            "cashier": {
                "cashierInfo": {
                    "discountFee": 1,
                    "netIncomeFee": 1,
                    "receivableFee": 1,
                    "totalFee": 1,
                },
                "chinese": {
                    "batchNumber": 1,
                    "manufacturer": 1,
                    "position": 1,
                    "specialRequirement": 1,
                    "totalCount": 1,
                    "unitCount": 1,
                    "validityDate": 1,
                },
                "clinicInfo": {
                    "address": 1,
                    "chargeDate": 1,
                    "chargeOperator": 1,
                    "mobile": 1,
                    "printDate": 1,
                    "qrCode": 1,
                    "replacementReminder": 1,
                },
                "composeDetail": 0,
                "feeInfo": {
                    "autoChangeLine": 0,
                    "chargeItem": 1,
                    "chineseMedicine": 2,
                    "compose": 2,
                    "composeChildren": 1,
                    "examination": 2,
                    "materialGoods": 2,
                    "other": 2,
                    "registration": 1,
                    "totalAmount": 0,
                    "treatment": 2,
                    "westernMedicine": 2,
                },
                "healthCardInfo": {
                    "balanceInfo": 1,
                    "cardInfo": 1,
                    "medicalFeeGrade": 1,
                    "ownExpenseRatio": 1,
                    "settlementInfo": 1,
                },
                "materialGoods": {
                    "position": 0,
                },
                "patientInfo": {
                    "age": 1,
                    "doctor": 1,
                    "mobile": 1,
                    "mobileType": 1,
                    "patientOrderNo": 1,
                    "sellerName": 1,
                    "sex": 1,
                },
                "printCount": 1,
                "remark": "",
                "title": "高新大源店",
                "westernMedicine": {
                    "batchNumber": 1,
                    "manufacturer": 1,
                    "position": 1,
                    "spec": 1,
                    "validityDate": 1,
                },
            },
            "dispensing": {
                "barcode": 1,
                "chineseMedicine": {
                    "position": 1,
                    "spec": 0,
                    "specialRequirement": 1,
                    "totalUnitCount": 0,
                },
                "format": 1,
                "materialGoods": {
                    "position": 1,
                },
                "other": {
                    "delivery": 1,
                    "doctorAdvice": 0,
                    "medicineRemark": 0,
                    "process": 1,
                },
                "patientInfo": {
                    "age": 1,
                    "departmentName": 1,
                    "diagnose": 1,
                    "doctor": 1,
                    "mobile": 1,
                    "mobileType": 1,
                    "patientOrderNo": 1,
                    "sex": 1,
                },
                "remark": "药品的调剂和服用请遵医嘱；按卫生部规定，药品一经发出，不得退换",
                "ticketFooter": {
                    "address": 1,
                    "amount": 1,
                    "assigner": 1,
                    "chargedByName": 1,
                    "check": 1,
                    "dispense": 0,
                    "mobile": 1,
                    "printDate": 1,
                    "printTime": 1,
                    "qrCode": 1,
                    "replacementReminder": 0,
                    "reviewDispense": 1,
                },
                "title": "高新大源店",
                "westernMedicine": {
                    "position": 1,
                    "spec": 1,
                    "usage": 1,
                    "manufacturer": 1,
                },
            },
            "medicalList": {
                "dalian": {
                    "composeChildren": 0,
                },
                "format": "normal",
                "huaian": {
                    "composeChildren": 1,
                    "institutionName": "",
                    "splitType": 0,
                },
                "jiangsu": {
                    "composeChildren": 2,
                    "institutionName": "",
                    "splitType": 0,
                },
                "normal": {
                    "canAffordRange": 0,
                    "composeChildren": 0,
                    "idCardNum": 1,
                    "institutionName": "fffffff",
                    "noAffordMoney": 1,
                    "patientAddress": 0,
                    "selfPaymentFee": 0,
                    "title": "",
                },
                "wujiang": {
                    "composeChildren": 2,
                    "institutionName": "asdadad",
                    "splitType": 0,
                },
            },
            "registration": {
                "feeInfo": {
                    "amount": 1,
                    "netIncomeFee": 1,
                },
                "outpatientInfo": {
                    "consultingRemark": 1,
                    "consultingRoom": 1,
                    "formItemProducts": 1,
                    "outpatientDate": 1,
                    "outpatientTime": 1,
                    "signInTime": 1,
                },
                "patientInfo": {
                    "age": 1,
                    "birthday": 1,
                    "mobile": 1,
                    "mobileType": 1,
                    "sex": 1,
                },
                "registrationInfo": {
                    "department": 1,
                    "doctor": 1,
                    "orderNo": 1,
                },
                "remark": "",
                "ticketFooter": {
                    "address": 1,
                    "expiredReminder": 1,
                    "mobile": 1,
                    "operator": 1,
                    "printDate": 1,
                    "reserveDate": 1,
                },
                "title": "高新大源店",
            },
            "commonSettings": {
                "header": 1,
            },
            "hospital": {
                "format": "qingdao",
                "qingdao": {
                    "institutionName": "dasdad",
                },
            },
            "medicalInsuranceSettlement": {
                "format": "normal",
                "normal": {
                    "remark": "",
                    "title": "",
                },
            },
            "peReport": {
                "cover": {
                    "background": "https://cd-cis-static-assets.oss-cn-chengdu.aliyuncs.com/clinic-usage/37733ccc8c6a44258113153e1f87c1ef/medical-record/ph-cover-1_fPpSGNrsWcS3.png",
                    "institutionAddress": "四川省成都市桂溪街道四川省成都市桂溪街道四川省成都市四川省成都市桂溪街",
                    "institutionContact": "***********",
                    "institutionLogoUrl": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/physical-examination-print-logo/WechatIMG5210_RWWeJdILZjeI.jpg",
                    "institutionName": "ABC智慧医院",
                    "mainTitle": "健康体检报告",
                    "personInfo": {
                        "basicInfo": 1,
                        "peDate": 1,
                        "peOrderNo": 1,
                        "peOrganName": 1,
                        "reportDate": 1,
                        "mobile": 1,//联系手机 可选值：0 1 默认值 1
                        "company": 1,//所属单位 可选值：0 1 默认值 1
                        "address": 1,//联系地址 可选值：0 1 默认值 1
                        "xAxis": 23,
                        "yAxis": 40,
                    },
                    "subTitle": "HEALTH EXAMINATION REPORT",
                    qrCode: 1,
                },
                "introduction": {
                    "content": "请您认真阅读本体检报告，如果您对本报告的检查结果有不明之处或有异议，<br>     请及时与我们联系。感谢您对我们工作的支持和信任。<br>          本报告是我们健康管理中心对您体检结果的分析汇总及建议指导。<br>           通过本报告，可以帮助您更好地了解自身的健康状况，及时发现存在的健康危险因素，进行痪病预防及健康管理。 <br>         在此我们要提醒您，由于个体间存在生物差异，医生所做的健康诊断及医学建议仅依据您的陈述和本次检查的结果得出，通常是确切的，但任何一次医学检查的手段和方法都不存在100%的可靠和准确，我们建议您对异常的结果进行随访复查，便于医生有更多详实的医学证据去建立医学判断。 最后，我们欢迎并建议您每年至少来我中心进行一次系统检查，我们将为您提供历次体检结果对比，让您能够直观地了解自身近期的健康变化。祝您身体健康！<br>     请及时与我们联系。感谢您对我们工作的支持和信任。<br>     请及时与我们联系。感谢您对我们工作的支持和信任。<br>     请及时与我们联系。感谢您对我们工作的支持和信任。",
                    "abnormalImage": 1,
                },
                content:{
                    mainComments:1,
                },
            },
            "publicHealthReport": {
                "cover": {
                    "background": "https://cd-cis-static-assets.oss-cn-chengdu.aliyuncs.com/clinic-usage/37733ccc8c6a44258113153e1f87c1ef/medical-record/ph-cover-1_fPpSGNrsWcS3.png",
                    "institutionAddress": "四川省成都市桂溪街道四川省成都市桂溪街道四川省成都市桂溪街道四川省成都市桂溪街道四川省成都市桂溪街道四川省成都市桂溪街道四川省成都市桂溪街道",
                    "institutionContact": "***********",
                    "institutionLogoUrl": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/physical-examination-print-logo/WechatIMG5210_RWWeJdILZjeI.jpg",
                    "institutionName": "ABC智慧医院",
                    "mainTitle": "健康体检报告",
                    "personInfo": {
                        "basicInfo": 1,
                        "peDate": 1,
                        "peOrderNo": 1,
                        "peOrganName": 1,
                        "reportDate": 1,
                        "mobile": 1,//联系手机 可选值：0 1 默认值 1
                        "company": 1,//所属单位 可选值：0 1 默认值 1
                        "address": 1,//联系地址 可选值：0 1 默认值 1
                        "xAxis": 23,
                        "yAxis": 20,
                    },
                    "subTitle": "HEALTH EXAMINATION REPORT",
                    qrCode: 1,
                },
                "introduction": {
                    "content": "请您认真阅读本体检报告，如果您对本报告的检查结果有不明之处或有异议，<br>     请及时与我们联系。感谢您对我们工作的支持和信任。<br>          本报告是我们健康管理中心对您体检结果的分析汇总及建议指导。<br>           通过本报告，可以帮助您更好地了解自身的健康状况，及时发现存在的健康危险因素，进行痪病预防及健康管理。 <br>         在此我们要提醒您，由于个体间存在生物差异，医生所做的健康诊断及医学建议仅依据您的陈述和本次检查的结果得出，通常是确切的，但任何一次医学检查的手段和方法都不存在100%的可靠和准确，我们建议您对异常的结果进行随访复查，便于医生有更多详实的医学证据去建立医学判断。 最后，我们欢迎并建议您每年至少来我中心进行一次系统检查，我们将为您提供历次体检结果对比，让您能够直观地了解自身近期的健康变化。祝您身体健康！<br>     请及时与我们联系。感谢您对我们工作的支持和信任。<br>     请及时与我们联系。感谢您对我们工作的支持和信任。<br>     请及时与我们联系。感谢您对我们工作的支持和信任。",
                    "abnormalImage": 1,
                },
                content:{
                    mainComments:1,
                },
            },
            "peGroupReport": {
                'cover': {
                    'background': 'https://cd-cis-static-common.oss-cn-chengdu.aliyuncs.com/img/ph-cover-2.png',
                    'institutionAddress': '四川省成都市高新区',
                    'institutionContact': '***********',
                    'institutionLogoUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/physical-examination-print-logo/cat_DKFQCBbhfs7W.jpeg',
                    'institutionName': 'ABC数字医疗云',
                    'orderName': 1,
                    'organInfo': {
                        'address': 1,
                        'contactPhone': 1,
                        'name': 1,
                        'reportDate': 1,
                    },
                    'qrCode': 1,
                    'subTitle': '健康体检报告',
                },
                'mainBody': {
                    'conclusion': '1122222',
                    'content': {
                        'abnormalDetail': 1,
                        'abnormalStat': 1,
                        'abstract': 1,
                        'ageStage': 1,
                        'examinedStat': 1,
                    },
                },
                'introduction': {
                    'content': '1211112222111111',
                },
            },
        },
        "scope": "clinic",
        "scopeId": "fff730ccc5ee45d783d82a85b8a0e52d",
    },
}
