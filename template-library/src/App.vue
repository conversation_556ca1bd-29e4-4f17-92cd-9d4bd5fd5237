<template>
    <div>
        <div id="header">
            <h1>ABC打印模板</h1>
            <label>
                <input
                    v-model="config.isPreview"
                    type="checkbox"
                />
                预览模式
            </label>

            <label>
                <input
                    v-model="config.isLocal"
                    type="checkbox"
                />
                本地开发
            </label>

            <label>
                错误日志
                <input
                    v-model="errorLogId"
                    type="text"
                    style="width: 640px;"
                />
                <button @click="save">
                    保存
                </button>
                <button @click="clear">
                    清除
                </button>
            </label>

            <a
                id="lodop"
                href="#"
                @click.prevent="lodopPreview"
            >
                lodop
            </a>

            <div id="options-wrapper">
                <div id="options-label">
                    {{ (currentTemplate && currentTemplate.name) || "模板" }} ↘
                </div>
                <ul id="options">
                    <li
                        v-for="(template, key) in templates"
                        :key="template.name"
                    >
                        <input
                            :id="key"
                            v-model="config.currentTemplateName"
                            type="radio"
                            :value="key"
                        />
                        <label :for="key">{{ key }}</label>
                    </li>
                </ul>
            </div>
        </div>
        <div
            id="output"
            class="preview"
        >
            <iframe
                ref="iframe"
                src=""
                frameborder="0"
                height="100%"
                width="100%"
            ></iframe>
            <div
                v-if="renderConfig && renderConfig.length"
                class="preview-config-panel"
            >
                <!-- 分组模式 -->
                <template v-if="isGroup">
                    <div
                        v-for="configItem in renderConfig"
                        :key="configItem.key"
                    >
                        <p style="display: flex;just-content: space-between">
                            <span>{{ configItem.groupName }}</span>
                            <span>{{ configItem.createdDate }}</span>
                        </p>
                        <label
                            v-for="item in configItem.examItems"
                            :key="item.id"
                        >
                            {{ item.label }}
                            <input
                                v-model="item.value"
                                type="checkbox"
                            />
                        </label>
                    </div>
                </template>

                <template v-else-if="isRadioGroup">
                    <div 
                        v-for="(option) in renderConfig[0].options" 
                        :key="option.value"
                        style="display: flex;gap: 8px;cursor: pointer"
                    >
                        <input
                            type="radio"
                            name="option"
                            :value="option.value"
                            :checked="renderConfig[0].value === option.value"
                            @change="handleRadioChange"
                        />
                        <label :for="option.value">
                            {{ option.label }}
                        </label>
                    </div>
                </template>

                <template v-else>
                    <label
                        v-for="configItem in renderConfig"
                        :key="configItem.key"
                    >
                        {{ configItem.label }}
                        <input
                            v-model="configItem.value"
                            type="checkbox"
                        />
                    </label>
                </template>
            </div>
        </div>
    </div>
</template>

<script>
    import config from "./config.js";
    import LocalLog from "./log.json";
    // hot-reload
    import '../../lib/index';
    import.meta.glob('/lib/templates/*.js')
    const CACHE_CONFIG_KEY = "CACHE_CONFIG_KEY"

    const defaultConfig = {
        currentTemplateName: '',
        isPreview: true,
        errorLogId: '',
        isLocal: true
    }

    const getConfig = () => {
        try {
            const cacheString = localStorage.getItem(CACHE_CONFIG_KEY)
            if(cacheString) {
                return JSON.parse(cacheString)
            }
            return defaultConfig
        } catch (e) {
            return defaultConfig
        }
    }

    const setConfig = (config) => {
        localStorage.setItem(CACHE_CONFIG_KEY, JSON.stringify(config))
    }

    export default {
        data() {
            const config = getConfig();
            const handleResponse = async res => {
                this.currentErrorLog = JSON.parse(res.detail)
                const AbcTemplates = window.AbcPackages.AbcTemplates
                for (const key in AbcTemplates) {
                    if(AbcTemplates[key].businessKey === this.currentErrorLog.options.template.businessKey) {
                        this.printInstance = new AbcPackages.AbcPrint({
                            ...this.currentErrorLog.options,
                            template: this.config.isLocal ? AbcTemplates[key] : this.currentErrorLog.options.template
                        });
                        await AbcPackages.AbcPrint.setGlobalConfig({
                            globalConfig: this.currentErrorLog._globalConfig
                        });
                        await this.printInstance.init();
                        this.renderConfig = this.printInstance.getStaticRenderConfig();
                        await this.update()
                    }
                }
            }

            if(!LocalLog) {
                Promise.resolve().then(() => {
                    handleResponse(LocalLog);
                })
            } else {
                if(config.errorLogId) {
                    fetch(`//172.19.119.250:8998/api/v2/print-log/${config.errorLogId}`, {
                        method: 'GET',
                    }).then(res => res.json()).then(handleResponse)
                } else {
                    setTimeout(() => {
                        this.renderPrint(this.templates[config.currentTemplateName]);
                    })
                }
            }
            return {
                templates: AbcPackages.AbcTemplates,
                config,
                renderConfig: [],
                errorLogId: config.errorLogId,
                currentErrorLog: null,
            };
        },
        computed: {
            currentTemplate() {
                return this.templates[this.config.currentTemplateName];
            },
            isGroup() {
                return this.currentTemplate.businessKey === 'examination-apply-sheet';
            },
            isRadioGroup() {
                return this.currentTemplate.businessKey === 'examination-report';
            },
        },
        watch: {
            'config.currentTemplateName': {
                handler(newValue) {
                    if (newValue) {
                        setConfig(this.config);
                        this.renderPrint(this.templates[newValue]);
                    }
                },
                immediate: false,
            },
            'config.isPreview': {
                handler() {
                    setConfig(this.config);
                    this.renderPrint();
                },
            },
            'config.isLocal': {
                handler() {
                    setConfig(this.config);
                    location.reload();
                },
            },
            renderConfig: {
                async handler(newValue) {
                    await this.printInstance.setRenderConfig(newValue);
                    await this.update();
                },
                deep: true,
            },
        },
        mounted() {
            AbcPackages.AbcPrint.setGlobalConfig({
                globalConfig: config.data.print
            });
        },
        methods: {
            async renderPrint(template) {
                if(!template) {
                    template = this.templates[this.config.currentTemplateName]
                }
                const Orientation = AbcPackages.AbcPrint.Orientation;
                const AbcPageSizeMap = AbcPackages.AbcPrint.AbcPageSizeMap;
                // 模板推荐的方向和纸张大小
                const recommendedPageSize = template.pages.find(page => page.isRecommend)?.paper?.name || AbcPageSizeMap.A4.name;
                const defaultOrientation = template.pages.find(page => page.isRecommend)?.defaultOrientation || Orientation.portrait;
                this.printInstance = new AbcPackages.AbcPrint({
                    template: template,
                    page: {
                        orientation: defaultOrientation,
                        size: recommendedPageSize,
                        customStyles: {
                            'box-shadow': 'rgb(0 0 0 / 15%) 0px 0px 4px 2px',
                            'margin-bottom': '10px'
                        },
                        pageSizeReduce: this.config.isPreview ? {
                            top: 4,
                            bottom: 4,
                            right: 4,
                            left: 4,
                        } : {
                            top: 8,
                            right: 8,
                            bottom: 8,
                            left: 8,
                        }
                    },
                });
                await this.printInstance.init();
                this.renderConfig = this.printInstance.getStaticRenderConfig();
            },
            async update() {
                const html = this.config.isPreview ? await this.printInstance.splitPreview() : await this.printInstance.split();
                this._html = html
                const frame = this.$refs.iframe;
                frame.srcdoc = `<style>.abc-page { margin: 0 auto 8px; } ::-webkit-scrollbar { width: 0; height: 0; color: transparent; }</style>` +  html;
            },
            lodopPreview() {
                if(!window.LODOP) {
                    return alert('安装LODOP后可预览')
                }
                console.log('打印预览的html', this._html)
                const LODOP = window.LODOP
                LODOP.PRINT_INIT('');
                LODOP.ADD_PRINT_HTM(10, 0, '100%', '100%', this._html);
                LODOP.SET_PRINT_PAGESIZE(
                    1,
                    '',
                    '',
                    'A5'
                );
                // LODOP.SET_PRINT_MODE('CATCH_PRINT_STATUS', true);
                return LODOP.PREVIEW();
                // window.LODOP.PRINT();
            },
            save() {
                this.config.errorLogId = this.errorLogId
                setConfig(this.config);
                location.reload();
            },
            clear() {
                this.errorLogId = ''
                this.save()
            },
            handleRadioChange(e) {
                this.renderConfig[0].value = +e.target.value;
            }
        },
    };
</script>

<style lang="scss">
:root {
    --bg: #dadbe0;
    --header-bg: #1d1f21;
    --text-color: #ffffff;
    --border: #333333;
    --primary: rgb(101, 163, 221);
}

body {
    background: var(--bg);
}

#header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1;
    box-sizing: border-box;
    height: 60px;
    padding: 0.3em 1.6em;
    color: var(--text-color);
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--border);

    button {
      margin-left: 8px;
    }

    h1 {
        display: inline-block;
        margin-right: 15px;
        font-size: 18px;
    }

    a {
        font-weight: 600;
        color: var(--primary);
    }

    #options-wrapper {
        position: absolute;
        top: 20px;
        right: 10px;
    }

    #options-wrapper:hover #options {
        display: block;
    }

    #lodop {
        position: absolute;
        top: 50%;
        right: 100px;
        cursor: pointer;
        transform: translateY(-50%);
    }

    #options-label {
        padding-right: 10px;
        font-weight: bold;
        text-align: right;
        cursor: pointer;
    }

    #options {
        display: none;
        padding: 15px 30px;
        margin-top: 15px;
        list-style-type: none;
        background-color: var(--header-bg);
        border: 1px solid var(--border);
    }

    #options li {
        margin: 8px 0;
    }

    #header input {
        margin-right: 6px;
    }

    #header label {
        color: #999999;
    }
}

.preview {
    margin: 12px 0;
    position: absolute;
    top: 60px;
    bottom: 0;
    box-sizing: border-box;
}

#source {
    left: 0;
    width: 45%;
    display: none;
}

#output {
    left: 0%;
    width: 100%;

    .preview-config-panel {
        position: fixed;
        top: 50%;
        right: 8px;
        padding: 12px 4px;
        font-size: 12px;
        background: #cccccc;
        border-radius: 4px;
        transform: translateY(-50%);

        label {
            display: flex;
            align-items: center;
        }
    }
}
</style>
