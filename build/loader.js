const fs = require('fs')
const path = require('path')
const crypto = require('crypto')


function md5Encode(data) {
  return crypto.createHash('md5').update(data).digest('hex')
}

const abcPrintIndexFile = path.resolve('./lib/index.js')
const content = fs.readFileSync(abcPrintIndexFile)
const contentHash = md5Encode(content).slice(0, 8)
const abcPrintIndexFileWithHash = path.resolve(`./lib/index.${contentHash}.js`)
fs.renameSync(abcPrintIndexFile, abcPrintIndexFileWithHash)
const fullPrintIndexPath = process.env.__BASE_URL__+ `index.${contentHash}.js`
const templateFileDir = fs.readdirSync(path.resolve('lib/templates'))
const templateIndexFile = templateFileDir.find(fileName => /index.[A-Za-z0-9]+.js/.test(fileName))
const fullTemplateIndexPath = process.env.__BASE_URL__ + 'templates/' + templateIndexFile


const loaderContent = `(function(){
							function loadAssets( filePath, fileType ) {
								var node;
								if (fileType === "js") {
									node = document.createElement('script');
									node.setAttribute("type", "text/javascript");
									node.setAttribute("src", filePath);
									node.setAttribute("crossorigin", "anonymous");
								}
								if (typeof node != "undefined")
									document.getElementsByTagName( "head" )[ 0 ].appendChild( node );
							}
							console.info('AbcPrint【构建时间：${new Date().toLocaleString()}】');
							console.info("AbcPrint【core：${fullPrintIndexPath}");
							console.info("AbcPrint【templates：${fullTemplateIndexPath}");
							loadAssets( '${fullPrintIndexPath}', 'js' );
							loadAssets( '${fullTemplateIndexPath}', 'js' );
						})();`

fs.writeFileSync(path.resolve('./lib/loader.js'), loaderContent)

