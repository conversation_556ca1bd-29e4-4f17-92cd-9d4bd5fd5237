import Tool from "abc-fed-build-tool";
import {internalIpV4Sync} from "./internal-ip.js";
const useOSS = process.env.BUILD_ENV === 'gray' || process.env.BUILD_ENV === 'test';

const bucketInfo = Tool.OSS.getOSSInfo(process.env.BUILD_ENV || 'dev', '', useOSS)

const __PROJECT_PATH = 'abc-print/'

export const __DEV_INTERNAL_IP__ = internalIpV4Sync()
export const __DEV__ = process.env.NODE_ENV === 'development'
export const __BUILD_DEV__ = process.env.buildEnv === 'dev'
export const __BUILD_TEST__ = process.env.buildEnv === 'test'
export const __BASE_URL__ = __DEV__ ?  `http://${__DEV_INTERNAL_IP__}:9999/` : (process.env.__BASE_URL__  || (bucketInfo.url + __PROJECT_PATH));
export const __TEMPLATE_BASE_URL__ = __BASE_URL__ + 'templates/'
export const __ABCPRINT_URL__ = __DEV__ ? __BASE_URL__ + 'index.js' : __BASE_URL__ + 'loader.js'
export const __VUE_URL__ = __BASE_URL__ + 'vue.min.js'
export const __BUILD_ENV__ = process.env.BUILD_ENV || 'dev'
export const __BUILD_TAG__ = process.env.BUILD_TAG || 'latest'
export const __IS_PERFORMANCE_BUILD__ = __DEV__ || __BUILD_DEV__ || __BUILD_TEST__