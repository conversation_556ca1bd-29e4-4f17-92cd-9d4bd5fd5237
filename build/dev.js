import {spawn}  from 'child_process'
import path from 'path'
import {__DEV__, __DEV_INTERNAL_IP__} from "./constants.js";
import fs from 'fs';
import open from 'open';

function createSpawn(...arg) {
    const newSpawn = spawn(...arg, {stdio: "inherit"}, {
        cwd: path.resolve(__dirname, '../')
    })

    newSpawn.on('data', (data) => {
        console.error(data.toString())
    })

    newSpawn.on('exit', (code) => {
        console.log(`Child exited with code ${code}`)
    })

    return newSpawn
}

createSpawn('yarn', ['dev:template-library'])
createSpawn('yarn', ['dev:server'])
createSpawn('yarn', ['dev:core'])
createSpawn('yarn', ['dev:templates'])

// 替换模板库的ip地址
if(__DEV__) {
    const IP_REGEXP = /((2(5[0-5]|[0-4]\d))|1\d{2}|[1-9]?\d)(\.((2(5[0-5]|[0-4]\d))|1\d{2}|[1-9]?\d)){3}/g
    const templateLibraryIndexFilePath = path.resolve(__dirname, '../template-library/index.html')
    const templateLibraryIndexHTML = fs.readFileSync(templateLibraryIndexFilePath, { encoding: 'utf8' }).replace(IP_REGEXP,__DEV_INTERNAL_IP__)
    fs.writeFileSync(templateLibraryIndexFilePath, templateLibraryIndexHTML)

    open(`http://${__DEV_INTERNAL_IP__}:3999`)
}
