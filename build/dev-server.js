const http = require('http')
const fs = require('fs')
const url = require('url')
const path = require('path')
const chalk = require('chalk')

const server = http.createServer(function (request, response) {
  const pathObj = url.parse(request.url, true);
  const staticPath = pathObj.pathname.startsWith('/src') ? path.resolve(__dirname, '../') : path.resolve(__dirname, '../', 'lib')
  const filePath = path.join(staticPath, pathObj.pathname)
  const index = filePath.lastIndexOf(".");
  const ext = filePath.substr(index + 1);
  let MIME = {
    css: 'text/css',
    html: 'text/html',
    json: 'application/json',
    js: 'application/javascript',
    jpg: 'image/jpeg',
    png: 'image/png'
  }[ext];
  if (!MIME) {
    MIME = 'text/*'
  }
  fs.readFile(filePath, 'binary', function (err, fileContent) {
    if (err) {
      response.writeHead(404, 'not found')
      response.end('<h1>404 Not Found</h1>')
    } else {
      response.setHeader('Content-Type', MIME)
      response.write(fileContent, 'binary')
      response.end()
    }
  })
})

server.listen(9999, function () {
  console.log(`dev-server: ${chalk.green('已启动')}\n端口: ${chalk.green('9999')}`)
})
