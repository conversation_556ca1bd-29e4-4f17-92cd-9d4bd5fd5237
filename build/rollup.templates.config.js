import './copy-static'
import path from 'path'
import alias from '@rollup/plugin-alias'
import commonjs from '@rollup/plugin-commonjs'
import resolve from '@rollup/plugin-node-resolve'
import esbuild from 'rollup-plugin-esbuild'

import postcss from 'rollup-plugin-postcss'
import autoprefixer from 'autoprefixer'
import vue from 'rollup-plugin-vue'
import babel from 'rollup-plugin-babel'
import fs from 'fs'
import {__BASE_URL__, __IS_PERFORMANCE_BUILD__, __DEV__, __TEMPLATE_BASE_URL__} from './constants'
import {PrintBusinessKeyEnum, PrintGroupMap} from "../templates/constant/print-constant.js";
import {uglify} from "rollup-plugin-uglify";
import crypto from 'crypto';

const rollupConfig = []
let taskId = 0;

function bundledPlugin(fn) {
    return {
        name: 'bundled',
        closeBundle() {
            taskId ++
            if(taskId >= rollupConfig.length) {
                setImmediate(() => {
                    console.log('开始构建templates索引文件')
                    fn && fn()
                })
            }
        }
    }
}

function md5Encode(data) {
    return crypto.createHash('md5').update(data).digest('hex')
}

const getTemplatePages = (file) => {
    const templateCode = fs.readFileSync(file, {
        encoding: 'utf8'
    })
    if (templateCode.match(/pages\s*:\s*(\[[^[]*\])/g)) {
        return RegExp.$1
    }
    return null;
}

const getTemplateBusinessKey = (file) => {
    const templateCode = fs.readFileSync(file, {
        encoding: 'utf8'
    })
    if (templateCode.match(/businessKey\s*:\s*(\S+).?,/g)) {
        return RegExp.$1
    }
    return null;
}

const decodeBusinessKey = (str) => {
    try {
        return new Function(`const PrintBusinessKeyEnum = ${JSON.stringify(PrintBusinessKeyEnum)};return ${str.replace(/^\s+/, '')}`)()
    } catch (e) {
        console.error(e)
        return {}
    }
}

const decodePageList = (str) => {
    try {
        const PageSizeMap = require('../share/page-size.js')
        const Orientation = require('../share/page-size.js').Orientation
        return new Function(`const PageSizeMap = ${JSON.stringify(PageSizeMap)};const Orientation = ${JSON.stringify(Orientation)};return ${str.replace(/^\s+/, '')}`)()
    } catch (e) {
        console.error(e)
        return {}
    }
}

const mergePrintPage = (curPages, checkPages) => {
    let pagesMap = new Map();
    let resPages = [];
    curPages.forEach(item => {
        const paper = item.paper;
        if (!pagesMap.has(paper.key)) {
            pagesMap.set(paper.key, item);
            resPages.push(item);
        }
    })
    checkPages.forEach(item => {
        const paper = item.paper;
        if (!pagesMap.has(paper.key)) {
            pagesMap.set(paper.key, item);
            resPages.push(item);
        }
    })
    return resPages;
}

const findBusinessGroup = (businessKey) => {
    if (!businessKey) {
        return '';
    }
    let groupKey = '';
    for (let key in PrintGroupMap) {
        const groupItems = PrintGroupMap[key].groupItems;
        const group = groupItems.find(item => {
            return item === businessKey;
        })
        if (group) {
            groupKey = key
        }
    }
    return groupKey || '';
}

function safeUnlinkSync(path) {
    try {
        fs.unlinkSync(path)
    } catch (e) {
        console.error(e)
    }
}

function buildUMDIndexFile(templateFiles) {
    const templateSourceUrlMap = {}
    const hackCssPath = path.resolve(`./lib/templates/ie-hack.css`)
    const hackCssContent = readFile(hackCssPath, 'utf8')
    if(!__DEV__) {
        fs.unlinkSync(hackCssPath)
    }
    const templateIndexUMDModuleCode = `
    (function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, (global.AbcPackages = global.AbcPackages || {}, global.AbcPackages.AbcTemplates = factory()));
    }(this, (function () { 'use strict';
        return templateIndex;
    })));
    `

    for (const templateOptions of templateFiles) {
        const {templateFile, isDemo} = templateOptions;
        const templatePath = isDemo ? './templates/demo': './templates'
        const templateFilePath = resolvePath(templatePath, templateFile);
        const sourceFileName = templateFile.match(/^([^.]+)\.([a-zA-Z-])+/)[1]
        const originTemplateFile = resolvePath('/lib/templates/', sourceFileName + '.js')

        // 获取纸张列表
        const pagesString = getTemplatePages(templateFilePath);
        const pageList = decodePageList(pagesString);

        // 获取 business 和 businessName
        const businessString = isDemo ? formatDemoKey(templateFile)  : getTemplateBusinessKey(templateFilePath);
        const businessKey = isDemo ? formatDemoKey(templateFile) : decodeBusinessKey(businessString).toString();
        const businessKeyName = camelize(businessKey);
        const templateFileHash = md5Encode(fs.readFileSync(originTemplateFile, {encoding: 'utf8'})).slice(0, 8)

        // 获取 group
        const group = findBusinessGroup(businessKey);

        // 获取templateName
        // const templatePageConfigStr = getTemplateConfig(resolvePath('./templates', templateFile))
        // const templatePageConfig = decodeConfig(templatePageConfigStr)
        // const templateName = camelize(templatePageConfig.name)

        // 获取打印的示例数据
        const templateExampleData = getTemplateExampleData(templateFilePath, templateFile)

        const cssPath = resolvePath(`./lib/templates/${sourceFileName}.css`)
        const cssContent = readFile(cssPath, 'utf8')
        if(!__DEV__) {
            safeUnlinkSync(cssPath)
        }

        // 上传文件名称
        const templateFileNameWithHash = `${sourceFileName}.${templateFileHash}.js`;

        if (!pageList || !pageList.length) {
            throw Error('未提供templatePageConfig参数')
        }

        // 没有 对应的模板，需要新增一个 当前 businessKey 的对象，初始化对象的数据
        // 初始化： pages: [] 支持的纸张列表
        //         group: '' 打印的分组
        //         businessKey: '' 打印的业务key
        //         printConfig: {} 打印机配置
        //         templates: [] 多个打印模板
        if (!templateSourceUrlMap.hasOwnProperty(businessKeyName)) {
            templateSourceUrlMap[businessKeyName] = {
                group: group,
                businessKey: businessKey,
                pages: [],
                templates: [],
                printConfig: {},
            };
        }
        templateSourceUrlMap[businessKeyName].templates.push({
            url: __TEMPLATE_BASE_URL__ + (__DEV__ ? `${sourceFileName}.js` : templateFileNameWithHash),
            css: cssContent,
            hackCss: hackCssContent,
            pages: pageList,
            exampleData: __DEV__ ? decodeExampleData(templateExampleData, templateFile) : undefined,
            file: templateFile,
            windowRegisteredName: capitalize(camelize(sourceFileName))
        })
        templateSourceUrlMap[businessKeyName].pages = mergePrintPage(templateSourceUrlMap[businessKeyName].pages, pageList)
        if(!__DEV__) {
            fs.copyFileSync(originTemplateFile, resolvePath('/lib/templates/', templateFileNameWithHash))
            fs.unlinkSync(originTemplateFile)
        }
    }
    const templateIndexContent = templateIndexUMDModuleCode.replace('templateIndex', stringify(templateSourceUrlMap))
    const templateIndexHash = md5Encode(templateIndexContent).slice(0, 8)
    fs.writeFileSync(resolvePath(__DEV__ ? `/lib/templates/index.js` : `/lib/templates/index.${templateIndexHash}.js`), templateIndexContent)
}

const readFile = (...arg) => {
    try {
        return fs.readFileSync(...arg)
    } catch (e) {
        return ''
    }
}

const resolvePath = (...releativePath) => {
    return path.join(__dirname, '../', ...releativePath)
}

const external = [
    'vue'
]

const camelizeRE = /-(\w)/g
const camelize = (str) => {
    return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : '')
}

const capitalize = (str) => {
    return str.charAt(0).toUpperCase() + str.slice(1)
}

const stringify = (obj) => {
    return JSON.stringify(obj, null, 2)
}

const getTemplateExampleData = (file, fileName) => {

    const templateCode = fs.readFileSync(file, {
        encoding: 'utf8'
    })
    if (templateCode.match(/<!--exampleData([\s\S^(-->)]*?)-->/g)) {
        return RegExp.$1
    } else {
        const fileNameNoExt = fileName.slice(0, fileName.indexOf('.'))
        const exampleDataFilePath = resolvePath(`./templates/example-data/${fileNameNoExt}.json`);
        try {
            return  fs.readFileSync(exampleDataFilePath, {
                encoding: 'utf-8'
            });
        } catch (e) {
            return  null;
        }
    }
}

const formatDemoKey = (templateFile) => {
    return `demo/${templateFile}`
}

const decodeExampleData = (str, templateFile) => {
    try {
        if (!str) {
            return null
        }
        return new Function(`return ${str.replace(/^\s+/, '')}`)()
    } catch (e) {
        console.error('exampleData解析失败: ', templateFile)
    }
}

let fsTemplatesFiles =  fs.readdirSync(resolvePath('./templates'));

if(process.env.SCOPE) {
    fsTemplatesFiles = fs.readFileSync(resolvePath('./templates/.scope'), {encoding: 'utf8'}).split('\n').filter(Boolean);
}

const templateFiles = fsTemplatesFiles
    .filter(fileOrDir => (
        !fileOrDir.startsWith('.') &&
        ![
            'common',
            'data-handler',
            'components',
            'style',
            'constant',
            'demo',
            'mixins',
            'example-data',
        ].includes(fileOrDir))
    )
    .map(templateFile => {
        return {templateFile, isDemo: false}
    });

// 处理css 打包
rollupConfig.push({
    input: `templates/style/ie-hack.scss`,
    output: [
        {
            file: `lib/templates/ie-hack.css`,
            format: 'umd',
            name: 'IeHack'
        },
    ],
    plugins: [
        postcss({
            inject: false,
            extract: true,
            plugins: [autoprefixer()]
        }),
        bundledPlugin(() => buildUMDIndexFile(templateFiles)),
    ],
})

if (__DEV__) {
    const demoTemplatesFiles = fs.readdirSync(resolvePath('./templates/demo'))
        .map(templateFile => {
            return {templateFile, isDemo: true}
        })
    templateFiles.push(...demoTemplatesFiles)
}

for (const templateOptions of templateFiles) {
    const {templateFile, isDemo} = templateOptions
    const sourceFileName = templateFile.match(/^([^.]+)\.([a-zA-Z-])+/)[1]
    const plugins = [
        alias({
            customResolver: resolve({
                extensions: ['.vue', '.js']
            }),
            entries: [
                {
                    find: 'utils',
                    replacement: resolvePath('./utils'),
                }
            ]
        }),
        commonjs(),
        vue({
            css: false,
            compileTemplate: true,
        }),
        __IS_PERFORMANCE_BUILD__ ? esbuild({}) : babel({
            runtimeHelpers: true,
            sourceMap: true,
            extensions: ['.js', '.jsx', '.es6', '.es', '.mjs', '.vue'],
        }),
        __IS_PERFORMANCE_BUILD__ ? null : uglify(),
        resolve({
            extensions: ['vue', 'js'],
        }),
        postcss({
            inject: false,
            extract: true,
            minimize: true,
            plugins: [
                autoprefixer(),
                root => {
                    root.replaceValues(/url.*/, { fast: 'url' }, string => {
                        const repalceUrl = string.replace('/static/', __BASE_URL__)
                        console.log('css资源已被替换 => ' + repalceUrl)
                        return repalceUrl
                    })
                    root.walk(node => {
                        let selectorArr = (node.selector || '').split(',')
                        return node.selector = ((selectorArr || []).map((itm) => {
                            if (itm.match(/^(\s*)(html|body)(\s*)$/) || itm.includes('abc-page_compatible') || itm.includes('abc-page_preview') || itm.includes('abc-page_print')) {
                                return itm
                            }
                            return '.abc-page' + ' ' + itm
                        }).join(','))
                    })
                }
            ],
        }),
        bundledPlugin(() => buildUMDIndexFile(templateFiles)),
    ]

    rollupConfig.push({
        input: isDemo ? resolvePath(`./templates/demo/${templateFile}`) : resolvePath(`./templates/${templateFile}`),
        output: [
            {
                file: `lib/templates/${sourceFileName}.js`,
                format: 'umd',
                name: `AbcPackages.AbcPrint.$mount.${capitalize(camelize(sourceFileName))}`,
                sourcemap: false,
            },
        ],
        plugins,
        external
    })
}

// 处理css 打包
rollupConfig.push({
    input: `templates/style/ie-hack.scss`,
    output: [
        {
            file: `lib/templates/ie-hack.css`,
            format: 'umd',
            name: 'IeHack'
        },
    ],
    plugins: [
        postcss({
            inject: false,
            extract: true,
            plugins: [autoprefixer()]
        }),
        bundledPlugin(() => buildUMDIndexFile(templateFiles))
    ],
})


export default rollupConfig
