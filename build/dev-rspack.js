import {spawn} from 'child_process'
import {__DEV_INTERNAL_IP__, __DEV__} from './constants'
import open from 'open'
import path from 'path'
import fs from 'fs'

function createSpawn(command, args, options = {}) {
    const child = spawn(command, args, {
        stdio: 'inherit',
        shell: true,
        ...options
    })
    
    child.on('error', (err) => {
        console.error(`启动 ${command} 失败:`, err)
    })
    
    return child
}

console.log('🚀 启动开发环境 (使用 Rspack)...')
console.log(`📡 开发服务器地址: http://${__DEV_INTERNAL_IP__}:3999`)

// 启动各个服务
createSpawn('yarn', ['dev:template-library'])
createSpawn('yarn', ['dev:server'])
createSpawn('yarn', ['dev:core'])
createSpawn('yarn', ['dev:templates:rspack'])

// 替换模板库的ip地址
if(__DEV__) {
    const IP_REGEXP = /((2(5[0-5]|[0-4]\d))|1\d{2}|[1-9]?\d)(\.((2(5[0-5]|[0-4]\d))|1\d{2}|[1-9]?\d)){3}/g
    const templateLibraryIndexFilePath = path.resolve(__dirname, '../template-library/index.html')
    const templateLibraryIndexHTML = fs.readFileSync(templateLibraryIndexFilePath, { encoding: 'utf8' }).replace(IP_REGEXP,__DEV_INTERNAL_IP__)
    fs.writeFileSync(templateLibraryIndexFilePath, templateLibraryIndexHTML)

    open(`http://${__DEV_INTERNAL_IP__}:3999`)
}
