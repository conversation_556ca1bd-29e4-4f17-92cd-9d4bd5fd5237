const path = require('path')
const fs = require('fs')
const chalk = require('chalk')

function copy(from, to) {
  const fromPath = path.resolve(from)
  const toPath = path.resolve(to)
  fs.access(toPath, function (err) {
    if (err) {
      fs.mkdirSync(toPath)
    }
  })
  fs.readdir(fromPath, function (err, paths) {
    if (err) {
      console.log(err)
      return
    }
    paths.forEach(function (item) {
      const newFromPath = fromPath + '/' + item
      const newToPath = path.resolve(toPath + '/' + item)

      fs.stat(newFromPath, function (err, stat) {
        if (err) return
        if (stat.isFile()) {
          copyFile(newFromPath, newToPath)
          console.log(newToPath)
        }
        if (stat.isDirectory()) {
          copy(newFromPath, newToPath)
        }
      })
    })
  })
}

function copyFile(from, to) {
  try {
    fs.copyFileSync(from, to)
    console.log(`copy ${chalk.yellow(from)} => ${chalk.yellow(to)}: ${chalk.green('success')}`)
  } catch (e) {
    console.log(`copy ${chalk.yellow(from)} => ${chalk.yellow(to)}: ${chalk.red('error')}`)
  }
}

copy(path.resolve('./static'), path.resolve('./lib'))
