import path from 'path'
import { defineConfig } from '@rspack/core'
import { VueLoaderPlugin } from 'vue-loader'
import { generateEntries, templateFiles, buildUMDIndexFile, resolvePath, capitalize, camelize } from './rspack.templates.config.mjs'
import { __BASE_URL__, __IS_PERFORMANCE_BUILD__, __DEV__, __TEMPLATE_BASE_URL__ } from './constants.js'

const entries = generateEntries()

// 自定义插件：构建完成后生成索引文件
class BuildIndexPlugin {
    apply(compiler) {
        compiler.hooks.done.tap('BuildIndexPlugin', (stats) => {
            console.log('开始构建templates索引文件')
            buildUMDIndexFile(templateFiles)
        })
    }
}

// 自定义插件：处理 CSS 中的 URL 替换和选择器前缀
class PostCSSPlugin {
    apply(compiler) {
        compiler.hooks.compilation.tap('PostCSSPlugin', (compilation) => {
            compilation.hooks.processAssets.tap(
                {
                    name: 'PostCSSPlugin',
                    stage: compilation.PROCESS_ASSETS_STAGE_OPTIMIZE,
                },
                (assets) => {
                    Object.keys(assets).forEach(filename => {
                        if (filename.endsWith('.css')) {
                            const asset = assets[filename]
                            let content = asset.source()
                            
                            // 替换 URL
                            content = content.replace(/url\([^)]*\/static\/[^)]*\)/g, (match) => {
                                const replacedUrl = match.replace('/static/', __BASE_URL__)
                                console.log('css资源已被替换 => ' + replacedUrl)
                                return replacedUrl
                            })
                            
                            // 添加选择器前缀
                            content = content.replace(/([^{}]+)\s*{/g, (match, selector) => {
                                const selectorArr = selector.split(',')
                                const newSelector = selectorArr.map((itm) => {
                                    const trimmed = itm.trim()
                                    if (trimmed.match(/^(html|body)$/) || 
                                        trimmed.includes('abc-page_compatible') || 
                                        trimmed.includes('abc-page_preview') || 
                                        trimmed.includes('abc-page_print')) {
                                        return itm
                                    }
                                    return '.abc-page ' + trimmed
                                }).join(',')
                                return newSelector + ' {'
                            })
                            
                            // 更新资源
                            compilation.updateAsset(filename, {
                                source: () => content,
                                size: () => content.length,
                            })
                        }
                    })
                },
            )
        })
    }
}

export default defineConfig({
    mode: __DEV__ ? 'development' : 'production',
    
    entry: {
        ...entries,
        // 添加 ie-hack.scss 的入口
        'ie-hack': resolvePath('./templates/style/ie-hack.scss'),
    },
    
    output: {
        path: resolvePath('./lib/templates'),
        filename: '[name].js',
        library: {
            type: 'umd',
            name: 'AbcPackages.AbcPrint.$mount.[name]',
        },
        clean: false, // 不清理输出目录，因为可能有其他文件
        globalObject: 'this',
    },
    
    resolve: {
        extensions: ['.vue', '.js', '.ts', '.json'],
        alias: {
            'utils': resolvePath('./utils'),
            'vue$': 'vue/dist/vue.esm.js',
        },
    },
    
    externals: {
        vue: {
            commonjs: 'vue',
            commonjs2: 'vue',
            amd: 'vue',
            root: 'Vue',
        },
    },
    
    module: {
        rules: [
            {
                test: /\.vue$/,
                use: [
                    {
                        loader: 'vue-loader',
                        options: {
                            compilerOptions: {
                                preserveWhitespace: false,
                            },
                        },
                    },
                ],
            },
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: [
                    {
                        loader: 'builtin:swc-loader',
                        options: {
                            jsc: {
                                parser: {
                                    syntax: 'ecmascript',
                                    jsx: false,
                                },
                                target: 'es2015',
                            },
                        },
                    },
                ],
            },
            {
                test: /\.scss$/,
                type: 'css/auto',
                use: [
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: {
                                plugins: [
                                    require('autoprefixer')(),
                                ],
                            },
                        },
                    },
                    'sass-loader',
                ],
            },
            {
                test: /\.css$/,
                type: 'css/auto',
                use: [
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: {
                                plugins: [
                                    require('autoprefixer')(),
                                ],
                            },
                        },
                    },
                ],
            },
        ],
    },
    
    plugins: [
        new VueLoaderPlugin(),
        new PostCSSPlugin(),
        new BuildIndexPlugin(),
    ],
    
    optimization: {
        minimize: !__DEV__,
        splitChunks: false, // 禁用代码分割，保持每个模板独立打包
        sideEffects: false,
    },
    
    devtool: __DEV__ ? 'source-map' : false,
    
    stats: {
        colors: true,
        modules: false,
        children: false,
        chunks: false,
        chunkModules: false,
    },
})
