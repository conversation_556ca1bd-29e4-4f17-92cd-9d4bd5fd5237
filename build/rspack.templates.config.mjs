import './copy-static.js'
import path from 'path'
import fs from 'fs'
import crypto from 'crypto'
import { __BASE_URL__, __IS_PERFORMANCE_BUILD__, __DEV__, __TEMPLATE_BASE_URL__ } from './constants.js'
import { PrintBusinessKeyEnum, PrintGroupMap } from "../templates/constant/print-constant.js"

const resolvePath = (...releativePath) => {
    return path.join(__dirname, '../', ...releativePath)
}

function md5Encode(data) {
    return crypto.createHash('md5').update(data).digest('hex')
}

// 获取所有模板文件
let fsTemplatesFiles = fs.readdirSync(resolvePath('./templates'))

if (process.env.SCOPE) {
    fsTemplatesFiles = fs.readFileSync(resolvePath('./templates/.scope'), { encoding: 'utf8' }).split('\n').filter(Boolean)
}

const templateFiles = fsTemplatesFiles
    .filter(fileOrDir => (
        !fileOrDir.startsWith('.') &&
        ![
            'common',
            'data-handler',
            'components',
            'style',
            'constant',
            'demo',
            'mixins',
            'example-data',
        ].includes(fileOrDir)),
    )
    .map(templateFile => {
        return { templateFile, isDemo: false }
    })

// 添加 demo 文件
if (__DEV__) {
    const demoTemplatesFiles = fs.readdirSync(resolvePath('./templates/demo'))
        .map(templateFile => {
            return { templateFile, isDemo: true }
        })
    templateFiles.push(...demoTemplatesFiles)
}

// 生成入口文件映射
const generateEntries = () => {
    const entries = {}
    
    for (const templateOptions of templateFiles) {
        const { templateFile, isDemo } = templateOptions
        const sourceFileName = templateFile.match(/^([^.]+)\.([a-zA-Z-])+/)[1]
        const entryPath = isDemo 
            ? resolvePath(`./templates/demo/${templateFile}`)
            : resolvePath(`./templates/${templateFile}`)
        
        entries[sourceFileName] = entryPath
    }
    
    return entries
}

// 工具函数
const camelizeRE = /-(\w)/g
const camelize = (str) => {
    return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : '')
}

const capitalize = (str) => {
    return str.charAt(0).toUpperCase() + str.slice(1)
}

const stringify = (obj) => {
    return JSON.stringify(obj, null, 2)
}

const getTemplatePages = (file) => {
    const templateCode = fs.readFileSync(file, {
        encoding: 'utf8',
    })
    if (templateCode.match(/pages\s*:\s*(\[[^[]*\])/g)) {
        return RegExp.$1
    }
    return null
}

const getTemplateBusinessKey = (file) => {
    const templateCode = fs.readFileSync(file, {
        encoding: 'utf8',
    })
    if (templateCode.match(/businessKey\s*:\s*(\S+).?,/g)) {
        return RegExp.$1
    }
    return null
}

const decodeBusinessKey = (str) => {
    try {
        return new Function(`const PrintBusinessKeyEnum = ${JSON.stringify(PrintBusinessKeyEnum)};return ${str.replace(/^\s+/, '')}`)()
    } catch (e) {
        console.error(e)
        return {}
    }
}

const decodePageList = (str) => {
    try {
        const PageSizeMap = require('../share/page-size.js')
        const Orientation = require('../share/page-size.js').Orientation
        return new Function(`const PageSizeMap = ${JSON.stringify(PageSizeMap)};const Orientation = ${JSON.stringify(Orientation)};return ${str.replace(/^\s+/, '')}`)()
    } catch (e) {
        console.error(e)
        return {}
    }
}

const mergePrintPage = (curPages, checkPages) => {
    let pagesMap = new Map()
    let resPages = []
    curPages.forEach(item => {
        const paper = item.paper
        if (!pagesMap.has(paper.key)) {
            pagesMap.set(paper.key, item)
            resPages.push(item)
        }
    })
    checkPages.forEach(item => {
        const paper = item.paper
        if (!pagesMap.has(paper.key)) {
            pagesMap.set(paper.key, item)
            resPages.push(item)
        }
    })
    return resPages
}

const findBusinessGroup = (businessKey) => {
    if (!businessKey) {
        return ''
    }
    let groupKey = ''
    for (let key in PrintGroupMap) {
        const groupItems = PrintGroupMap[key].groupItems
        const group = groupItems.find(item => {
            return item === businessKey
        })
        if (group) {
            groupKey = key
        }
    }
    return groupKey || ''
}

const getTemplateExampleData = (file, fileName) => {
    const templateCode = fs.readFileSync(file, {
        encoding: 'utf8',
    })
    if (templateCode.match(/<!--exampleData([\s\S^(-->)]*?)-->/g)) {
        return RegExp.$1
    } else {
        const fileNameNoExt = fileName.slice(0, fileName.indexOf('.'))
        const exampleDataFilePath = resolvePath(`./templates/example-data/${fileNameNoExt}.json`)
        try {
            return fs.readFileSync(exampleDataFilePath, {
                encoding: 'utf-8',
            })
        } catch (e) {
            return null
        }
    }
}

const formatDemoKey = (templateFile) => {
    return `demo/${templateFile}`
}

const decodeExampleData = (str, templateFile) => {
    try {
        if (!str) {
            return null
        }
        return new Function(`return ${str.replace(/^\s+/, '')}`)()
    } catch (e) {
        console.error('exampleData解析失败: ', templateFile)
    }
}

const readFile = (...arg) => {
    try {
        return fs.readFileSync(...arg)
    } catch (e) {
        return ''
    }
}

function safeUnlinkSync(path) {
    try {
        fs.unlinkSync(path)
    } catch (e) {
        console.error(e)
    }
}

// 构建索引文件的函数
function buildUMDIndexFile(templateFiles) {
    const templateSourceUrlMap = {}
    const hackCssPath = path.resolve(`./lib/templates/ie-hack.css`)
    const hackCssContent = readFile(hackCssPath, 'utf8')
    if (!__DEV__) {
        fs.unlinkSync(hackCssPath)
    }
    
    const templateIndexUMDModuleCode = `
    (function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, (global.AbcPackages = global.AbcPackages || {}, global.AbcPackages.AbcTemplates = factory()));
    }(this, (function () { 'use strict';
        return templateIndex;
    })));
    `

    for (const templateOptions of templateFiles) {
        const { templateFile, isDemo } = templateOptions
        const templatePath = isDemo ? './templates/demo' : './templates'
        const templateFilePath = resolvePath(templatePath, templateFile)
        const sourceFileName = templateFile.match(/^([^.]+)\.([a-zA-Z-])+/)[1]
        const originTemplateFile = resolvePath('/lib/templates/', sourceFileName + '.js')

        // 获取纸张列表
        const pagesString = getTemplatePages(templateFilePath)
        const pageList = decodePageList(pagesString)

        // 获取 business 和 businessName
        const businessString = isDemo ? formatDemoKey(templateFile) : getTemplateBusinessKey(templateFilePath)
        const businessKey = isDemo ? formatDemoKey(templateFile) : decodeBusinessKey(businessString).toString()
        const businessKeyName = camelize(businessKey)
        const templateFileHash = md5Encode(fs.readFileSync(originTemplateFile, { encoding: 'utf8' })).slice(0, 8)

        // 获取 group
        const group = findBusinessGroup(businessKey)

        // 获取打印的示例数据
        const templateExampleData = getTemplateExampleData(templateFilePath, templateFile)

        const cssPath = resolvePath(`./lib/templates/${sourceFileName}.css`)
        const cssContent = readFile(cssPath, 'utf8')
        if (!__DEV__) {
            safeUnlinkSync(cssPath)
        }

        // 上传文件名称
        const templateFileNameWithHash = `${sourceFileName}.${templateFileHash}.js`

        if (!pageList || !pageList.length) {
            throw Error('未提供templatePageConfig参数')
        }

        if (!templateSourceUrlMap.hasOwnProperty(businessKeyName)) {
            templateSourceUrlMap[businessKeyName] = {
                group: group,
                businessKey: businessKey,
                pages: [],
                templates: [],
                printConfig: {},
            }
        }
        templateSourceUrlMap[businessKeyName].templates.push({
            url: __TEMPLATE_BASE_URL__ + (__DEV__ ? `${sourceFileName}.js` : templateFileNameWithHash),
            css: cssContent,
            hackCss: hackCssContent,
            pages: pageList,
            exampleData: __DEV__ ? decodeExampleData(templateExampleData, templateFile) : undefined,
            file: templateFile,
            windowRegisteredName: capitalize(camelize(sourceFileName)),
        })
        templateSourceUrlMap[businessKeyName].pages = mergePrintPage(templateSourceUrlMap[businessKeyName].pages, pageList)
        if (!__DEV__) {
            fs.copyFileSync(originTemplateFile, resolvePath('/lib/templates/', templateFileNameWithHash))
            fs.unlinkSync(originTemplateFile)
        }
    }
    const templateIndexContent = templateIndexUMDModuleCode.replace('templateIndex', stringify(templateSourceUrlMap))
    const templateIndexHash = md5Encode(templateIndexContent).slice(0, 8)
    fs.writeFileSync(resolvePath(__DEV__ ? `/lib/templates/index.js` : `/lib/templates/index.${templateIndexHash}.js`), templateIndexContent)
}

export { generateEntries, templateFiles, buildUMDIndexFile, resolvePath, capitalize, camelize }
