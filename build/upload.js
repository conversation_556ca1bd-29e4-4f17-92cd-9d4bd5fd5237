const OSS = require('ali-oss')
const archiver = require('archiver')
const COS = require('cos-nodejs-sdk-v5')
const Tool = require('abc-fed-build-tool')
const useOSS = process.env.BUILD_ENV === 'gray' || process.env.BUILD_ENV === 'test';
const aliOSSBucketInfo = Tool.OSS.getOSSInfo(process.env.BUILD_ENV || 'dev', '', useOSS)
const tencentOSSBucketInfo = Tool.OSS.getCOSInfo(process.env.BUILD_ENV || 'dev', '', useOSS)
const chalk = require('chalk')
const fs = require('fs')
const cp = require("child_process");
const path = require('path');
const log = console.log
const __DEV__ = process.env.NODE_ENV === 'development'
const region = __DEV__ ? 'oss-cn-shanghai' : aliOSSBucketInfo.region
const buildOfflineBundle = require('./offline-bundle.js')
const BUILD_TAG = process.env.BUILD_TAG || 'latest';



console.log('阿里OSS信息: \n', aliOSSBucketInfo)
console.log('腾讯OSS信息: \n', tencentOSSBucketInfo)

const aliClient = new OSS({
    accessKeyId: aliOSSBucketInfo.secretId,
    accessKeySecret: aliOSSBucketInfo.secretKey,
    bucket: aliOSSBucketInfo.bucket,
    region,
});

const tencentClient = new COS({
    SecretId: tencentOSSBucketInfo.secretId,
    SecretKey: tencentOSSBucketInfo.secretKey,
});

const aliPutObject = (...params) => {
    return aliClient.put(...params)
}

const tencentPutObejct = (params) => {
    return new Promise((resolve, reject) => {
        tencentClient.putObject(
            params,
            function(err, body) {
                if (err) return reject(err);
                resolve(body)
            });
    })
}

const upload = async ({
    isAliOSS = false,
    isTencentOSS = false,
}) => {
    async function uploadTarget(src, dest) {
        try {
            console.error(src)
            if(!['js', 'png', 'jpg'].some(it => src.endsWith(it))) {
                return;
            }
            log(chalk.gray(`开始上传任务: ${src} ----> ${dest}`))
            if(isAliOSS) {
                // 阿里云oss
                const aliResult = await aliPutObject(dest, src)
                log(chalk.green('阿里云上传成功', aliResult.url))
            }

            if(isTencentOSS) {
                // 腾讯云oss
                const tencentResult = await tencentPutObejct({
                    Bucket: tencentOSSBucketInfo.bucket,
                    Region: tencentOSSBucketInfo.region,
                    Key: dest,
                    Body: fs.readFileSync(src),
                });
                log(chalk.green('腾讯云上传成功', tencentResult.Location))
            }
            console.log(src, dest)
        } catch (e) {
            log(chalk.red('上传失败', src))
            log(e)
        }
    }

    function generateUploadTarget(src, dest) {
        return new Promise((resolve) => {
            fs.readdir(src, async function (err, files) {
                if (err) {
                    log(err)
                    return
                }
                files = files.filter(it => !it.includes('loader'))
                for(let i = 0; i < files.length; i++) {
                    const file = files[i]
                    const _src = src + '/' + file
                    const _dest = dest + '/' + file
                    const stats = fs.statSync(_src)
                    if(stats.isDirectory()) {
                        (_src.includes('templates') || _src.includes('assets')) && await generateUploadTarget(_src, _dest)
                    }
                    stats.isFile() && await uploadTarget(_src, _dest)
                }

                resolve();
            })
        });
    }

    await generateUploadTarget('./temp', 'abc-print')
    // const dest = Tool.handleLoaderName({
    //     name: 'abc-print/loader',
    //     buildEnvNo: process.env.BUILD_ENV_NO,
    // })
    // await uploadTarget('./temp/loader.js', dest)

    if (isAliOSS) {
        await Tool.createBackupLoader({
            buildEnvNo: process.env.BUILD_ENV_NO,
            buildTag: BUILD_TAG,
            buildEnv: process.env.BUILD_ENV || 'dev',
            loaderPath: path.join(process.cwd(), './temp/loader.js'),
        })
    }
}

const exec = (cmd) => {
    return new Promise((resolve, reject) => {
        cp.exec(cmd, (err, stdout) => {
            if (err) {
                reject(err)
            }
            resolve(stdout)
        });
    })
}

// 清空temp目录
const clean = () => {
    return new Promise((resolve, reject) => {
        fs.rmdir('./temp', { recursive: true }, (err) => {
            if (err) {
                reject(err)
            }
            resolve()
        });
    })
}

const replace = (REPLACEMENT_VALUE = '@@', TEMP_DIR_PATH = path.resolve('temp')) => {
    const PLACEHOLDER = '__BASE_URL__HOLDER__';

    /**
     * 递归遍历目录及其子目录下的所有文件
     * @param {string} dirPath 目录路径
     * @return {Array<string>} 文件路径数组
     */
    function readDirRecursive(dirPath) {
        const files = [];
        fs.readdirSync(dirPath).forEach(file => {
            const filePath = path.join(dirPath, file);
            const fileStat = fs.statSync(filePath);
            if (fileStat.isDirectory()) {
                files.push(...readDirRecursive(filePath));
            } else if (fileStat.isFile()) {
                files.push(filePath);
            }
        });
        return files;
    }


    const files = readDirRecursive(TEMP_DIR_PATH);

    // 遍历所有js文件并替换指定字符串
    files.forEach(filePath => {
        // 只替换js文件
        if (!filePath.endsWith('.js')) {
            return;
        }
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const updatedContent = fileContent.replace(new RegExp(PLACEHOLDER, 'g'), REPLACEMENT_VALUE);
        fs.writeFileSync(filePath, updatedContent, 'utf8');
    });
}

// 文件处理
async function prepareTempFiles(replaceValue) {
    // 清空temp目录
    await clean();
    // 复制lib到temp目录
    await exec('cp -r ./lib ./temp');
    // 替换
    replace(replaceValue);
}

async function uploadSheBao() {
    await prepareTempFiles('./abc-print/');
    const buildTime = new Date().toLocaleString()
    const buildTag = process.env.BUILD_TAG || `local_${new Date().getTime()}`
    const zipFileName = `abc-print_alone_${buildTag}.zip`

    const zipFilePath = path.resolve(`./temp-zip/${zipFileName}`);
    const sourcePath = path.resolve("./temp");
    const archive = archiver("zip", {
        zlib: { level: 9 },
    });
    const output = fs.createWriteStream(zipFilePath);
    archive.pipe(output);
    archive.directory(sourcePath, false)
    archive.finalize().then(async () => {
        async function fileMd5(file) {
            return new Promise((resolve, reject) => {
                const crypto = require('crypto')
                const md5Generator = crypto.createHash('md5')
                const fd = fs.createReadStream(file)
                md5Generator.setEncoding("hex")
                fd.pipe(md5Generator);

                fd.on('end', () => {
                    md5Generator.end()
                    resolve(md5Generator.read())
                });
                fd.on("error", (error) => {
                    reject(error)
                })
            })
        }
        const md5 = await fileMd5(zipFilePath);
        const pluginIfo = {
            tagName: buildTag,
            buildTime: buildTime,
            md5:md5,
            fileName: zipFileName,
        };

        const latestFilepath = path.resolve('./temp/latest.json');
        fs.writeFileSync(latestFilepath, JSON.stringify(pluginIfo));

        // 上传
        const aliZipResult = await aliPutObject(`abc-print/${zipFileName}`, zipFilePath)
        log(chalk.green('阿里云上传成功', aliZipResult.url))
        const aliLatestResult = await aliPutObject(`abc-print/latest.json`, latestFilepath)
        log(chalk.green('阿里云上传成功', aliLatestResult.url))

        const tencentZipResult = await tencentPutObejct({
            Bucket: tencentOSSBucketInfo.bucket,
            Region: tencentOSSBucketInfo.region,
            Key: `abc-print/${zipFileName}`,
            Body: fs.readFileSync(zipFilePath),
        });
        log(chalk.green('腾讯云上传成功', tencentZipResult.Location))
        const tencentLatestResult = await tencentPutObejct({
            Bucket: tencentOSSBucketInfo.bucket,
            Region: tencentOSSBucketInfo.region,
            Key: `abc-print/latest.json`,
            Body: fs.readFileSync(latestFilepath),
        });
        log(chalk.green('腾讯云上传成功', tencentLatestResult.Location))
    });
}

(async function main() {
    try {
        // 上传到阿里云
        await prepareTempFiles(aliOSSBucketInfo.url + 'abc-print/');
        await buildOfflineBundle();
        await upload({ isAliOSS: true, isTencentOSS: false });
        // 上传到腾讯云
        await prepareTempFiles(tencentOSSBucketInfo.url + 'abc-print/');
        await upload({ isAliOSS: false, isTencentOSS: true });
        // 上传社保包
        await uploadSheBao();
    } catch (e) {
        process.exit(1)
    }
})()
