import path from 'path'
import resolve from '@rollup/plugin-node-resolve'
import commonjs from '@rollup/plugin-commonjs'
import ts from '@rollup/plugin-typescript'
import eslint from '@rollup/plugin-eslint'
import packageJSON from '../package.json'
import replace from '@rollup/plugin-replace'
import {
    __ABCPRINT_URL__,
    __BASE_URL__,
    __DEV__,
    __VUE_URL__,
    __BUILD_ENV__,
    __BUILD_TAG__,
    __IS_PERFORMANCE_BUILD__
} from './constants'
import {uglify} from "rollup-plugin-uglify";
import fs from "fs";
import crypto from "crypto";
import json from '@rollup/plugin-json';

let taskId = 0;
let rollupConfig = [];
function bundledPlugin(fn) {
    return {
        name: 'bundled',
        closeBundle() {
            taskId ++
            if(taskId >= rollupConfig.length) {
                setImmediate(() => {
                    console.log('开始构建templates索引文件')
                    fn && fn()
                })
            }
        }
    }
}

function md5Encode(data) {
    return crypto.createHash('md5').update(data).digest('hex')
}


const getPath = _path => path.resolve(__dirname, '../', _path)

const extensions = [
    '.js',
    '.ts',
    '.tsx'
]

const tsPlugin = ts()

const esPlugin = eslint({
    throwOnError: true,
    include: ['src/**/*.ts'],
    exclude: ['node_modules/**', 'lib/**']
})

const commonConf = {
    input: getPath('./src/index.ts'),
    plugins: [
        replace({
            preventAssignment: true,
            values: {
                __ABCPRINT_URL__: JSON.stringify(__ABCPRINT_URL__),
                __VUE_URL__: JSON.stringify(__VUE_URL__),
                __DEV__: JSON.stringify(__DEV__),
                'process.env.NODE_ENV': JSON.stringify( 'production' ),
                __BUILD_ENV__: JSON.stringify(__BUILD_ENV__),
                __BUILD_TAG__: JSON.stringify(__BUILD_TAG__)
            }
        }),
        resolve(extensions),
        commonjs(),
        __IS_PERFORMANCE_BUILD__ ? null : uglify(),
        __IS_PERFORMANCE_BUILD__ ? null : esPlugin,
        tsPlugin,
        bundledPlugin(() => {
            const abcSpliterFile = path.resolve('./lib/spliter.js')
            const abcPrintFile = path.resolve('./lib/index.js')
            const spliterContent = fs.readFileSync(abcSpliterFile)
            const spliterContentHash = md5Encode(spliterContent).slice(0, 8)
            const abcSpliterFileWithHash = path.resolve(`./lib/spliter.${spliterContentHash}.js`)
            fs.copyFileSync(abcSpliterFile, abcSpliterFileWithHash)
            const fullSpliterIndexPath = (process.env.__BASE_URL__ || __BASE_URL__) + `spliter.${spliterContentHash}.js`
            const printContent = fs.readFileSync(path.resolve('./lib/index.js'), {encoding: 'utf8'})
            const newPrintContent = printContent.replace('<!--AbcSpliterInject-->', `<script>console.warn('AbcSpliter【构建时间：${new Date().toLocaleString()}】')</script><script src="${fullSpliterIndexPath}"></script>`)
            fs.writeFileSync(abcPrintFile, newPrintContent)
        }),
        json()
    ]
}

const outputMap = [
    {
        file: packageJSON.main,
        format: 'umd',
        name: 'AbcPackages.AbcPrint',
        sourcemap: __DEV__
    },
    {
        file: packageJSON.module,
        format: 'es',
        sourcemap: __DEV__
    }
]

const buildConf = options => Object.assign({}, commonConf, options)

const AbcPrintConfig = outputMap.map(output => buildConf({output: {name: packageJSON.name, ...output}}))
const AbcSpliterConfig = Object.assign({}, commonConf, {
    input: getPath('./src/spliter.ts'),
    output: {
        name: 'AbcPackages.AbcSpliter',
        file: 'lib/spliter.js',
        format: 'umd',
        sourcemap: __DEV__
    }
})

rollupConfig = [...AbcPrintConfig, AbcSpliterConfig];

export default rollupConfig;
