#!/usr/bin/env node

/**
 * 测试 Rspack 配置的脚本
 * 用于验证配置文件是否正确，以及构建是否能正常工作
 */

import { spawn } from 'child_process'
import fs from 'fs'
import path from 'path'

const __dirname = path.dirname(new URL(import.meta.url).pathname)

console.log('🧪 开始测试 Rspack 配置...\n')

// 检查必要的文件是否存在
const requiredFiles = [
    'build/rspack.config.js',
    'build/rspack.templates.config.js',
    'build/constants.js',
]

console.log('📋 检查配置文件...')
for (const file of requiredFiles) {
    if (fs.existsSync(path.join(__dirname, file))) {
        console.log(`✅ ${file} 存在`)
    } else {
        console.log(`❌ ${file} 不存在`)
        process.exit(1)
    }
}

// 检查是否安装了必要的依赖
console.log('\n📦 检查依赖...')
const requiredDeps = [
    '@rspack/core',
    '@rspack/cli',
    'vue-loader',
    'postcss-loader',
    'sass-loader',
    'autoprefixer',
]

const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'))
const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies }

const missingDeps = []
for (const dep of requiredDeps) {
    if (allDeps[dep]) {
        console.log(`✅ ${dep} 已安装`)
    } else {
        console.log(`❌ ${dep} 未安装`)
        missingDeps.push(dep)
    }
}

if (missingDeps.length > 0) {
    console.log('\n⚠️  发现缺失依赖，请运行以下命令安装：')
    console.log(`npm install --save-dev ${missingDeps.join(' ')}`)
    console.log('\n或者运行安装脚本：')
    console.log('./install-rspack.sh')
    process.exit(1)
}

// 测试配置文件是否能正确加载
console.log('\n🔧 测试配置文件加载...')
try {
    // 动态导入配置文件
    const configModule = await import('./build/rspack.config.mjs')
    console.log('✅ Rspack 配置文件加载成功')
    
    // 检查配置的基本结构
    const config = configModule.default
    if (config.entry && config.output && config.module) {
        console.log('✅ 配置文件结构正确')
    } else {
        console.log('❌ 配置文件结构不完整')
        process.exit(1)
    }
} catch (error) {
    console.log('❌ 配置文件加载失败:', error.message)
    process.exit(1)
}

// 检查模板文件数量
console.log('\n📁 检查模板文件...')
try {
    const templatesDir = path.join(__dirname, 'templates')
    const files = fs.readdirSync(templatesDir)
    const vueFiles = files.filter(file => file.endsWith('.vue'))
    console.log(`✅ 发现 ${vueFiles.length} 个 Vue 模板文件`)
    
    if (vueFiles.length === 0) {
        console.log('⚠️  没有找到 Vue 模板文件')
    }
} catch (error) {
    console.log('❌ 无法读取模板目录:', error.message)
    process.exit(1)
}

console.log('\n🎉 所有检查通过！')
console.log('\n📝 接下来您可以：')
console.log('1. 运行 "npm run dev:templates:rspack" 进行开发模式构建')
console.log('2. 运行 "npm run build:templates:rspack" 进行生产模式构建')
console.log('3. 运行 "npm run dev:rspack" 启动完整的开发环境')

// 询问是否要进行测试构建
console.log('\n❓ 是否要进行测试构建？(y/N)')
process.stdin.setEncoding('utf8')
process.stdin.on('readable', () => {
    const chunk = process.stdin.read()
    if (chunk !== null) {
        const input = chunk.trim().toLowerCase()
        if (input === 'y' || input === 'yes') {
            console.log('\n🚀 开始测试构建...')
            testBuild()
        } else {
            console.log('\n👋 测试完成，退出。')
            process.exit(0)
        }
    }
})

function testBuild() {
    const buildProcess = spawn('npm', ['run', 'build:templates:rspack'], {
        stdio: 'inherit',
        shell: true,
        env: { ...process.env, NODE_ENV: 'production' },
    })
    
    buildProcess.on('close', (code) => {
        if (code === 0) {
            console.log('\n✅ 测试构建成功！')
            
            // 检查输出文件
            const outputDir = path.join(__dirname, 'lib/templates')
            if (fs.existsSync(outputDir)) {
                const outputFiles = fs.readdirSync(outputDir)
                const jsFiles = outputFiles.filter(file => file.endsWith('.js'))
                console.log(`📦 生成了 ${jsFiles.length} 个 JavaScript 文件`)
                
                if (jsFiles.length > 0) {
                    console.log('🎉 Rspack 构建配置工作正常！')
                } else {
                    console.log('⚠️  没有生成 JavaScript 文件，请检查配置')
                }
            } else {
                console.log('⚠️  输出目录不存在，请检查配置')
            }
        } else {
            console.log('\n❌ 测试构建失败，请检查错误信息')
        }
        process.exit(code)
    })
    
    buildProcess.on('error', (error) => {
        console.log('\n❌ 构建过程出错:', error.message)
        process.exit(1)
    })
}
