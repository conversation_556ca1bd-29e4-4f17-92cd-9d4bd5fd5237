#!/usr/bin/env node

/**
 * 性能对比脚本
 * 比较 Rollup 和 Rspack 的构建性能
 */

import { spawn } from 'child_process'
import fs from 'fs'
import path from 'path'

const __dirname = path.dirname(new URL(import.meta.url).pathname)

console.log('⚡ Rollup vs Rspack 性能对比测试\n')

// 清理输出目录
function cleanOutput() {
    const libDir = path.join(__dirname, 'lib/templates')
    if (fs.existsSync(libDir)) {
        fs.rmSync(libDir, { recursive: true, force: true })
    }
    fs.mkdirSync(libDir, { recursive: true })
}

// 运行构建并计时
function runBuild(command, args, name) {
    return new Promise((resolve, reject) => {
        console.log(`🚀 开始 ${name} 构建...`)
        cleanOutput()
        
        const startTime = Date.now()
        const buildProcess = spawn(command, args, {
            stdio: 'pipe',
            shell: true,
            env: { ...process.env, NODE_ENV: 'production' },
        })
        
        let output = ''
        let errorOutput = ''
        
        buildProcess.stdout.on('data', (data) => {
            output += data.toString()
        })
        
        buildProcess.stderr.on('data', (data) => {
            errorOutput += data.toString()
        })
        
        buildProcess.on('close', (code) => {
            const endTime = Date.now()
            const duration = (endTime - startTime) / 1000
            
            if (code === 0) {
                // 统计输出文件
                const outputDir = path.join(__dirname, 'lib/templates')
                let fileCount = 0
                let totalSize = 0
                
                if (fs.existsSync(outputDir)) {
                    const files = fs.readdirSync(outputDir)
                    const jsFiles = files.filter(file => file.endsWith('.js'))
                    fileCount = jsFiles.length
                    
                    jsFiles.forEach(file => {
                        const filePath = path.join(outputDir, file)
                        const stats = fs.statSync(filePath)
                        totalSize += stats.size
                    })
                }
                
                resolve({
                    name,
                    duration,
                    fileCount,
                    totalSize: Math.round(totalSize / 1024), // KB
                    success: true,
                })
            } else {
                reject({
                    name,
                    duration,
                    error: errorOutput || output,
                    success: false,
                })
            }
        })
        
        buildProcess.on('error', (error) => {
            reject({
                name,
                duration: 0,
                error: error.message,
                success: false,
            })
        })
    })
}

// 格式化结果
function formatResults(results) {
    console.log('\n📊 构建结果对比：')
    console.log('┌─────────────┬──────────────┬────────────┬─────────────┬────────────┐')
    console.log('│ 构建工具    │ 构建时间(s)  │ 文件数量   │ 总大小(KB)  │ 状态       │')
    console.log('├─────────────┼──────────────┼────────────┼─────────────┼────────────┤')
    
    results.forEach(result => {
        const name = result.name.padEnd(11)
        const duration = result.success ? result.duration.toFixed(2).padStart(12) : 'FAILED'.padStart(12)
        const fileCount = result.success ? result.fileCount.toString().padStart(10) : '-'.padStart(10)
        const totalSize = result.success ? result.totalSize.toString().padStart(11) : '-'.padStart(11)
        const status = result.success ? '✅ 成功'.padEnd(10) : '❌ 失败'.padEnd(10)
        
        console.log(`│ ${name} │ ${duration} │ ${fileCount} │ ${totalSize} │ ${status} │`)
    })
    
    console.log('└─────────────┴──────────────┴────────────┴─────────────┴────────────┘')
    
    // 计算性能提升
    const rollupResult = results.find(r => r.name === 'Rollup')
    const rspackResult = results.find(r => r.name === 'Rspack')
    
    if (rollupResult && rspackResult && rollupResult.success && rspackResult.success) {
        const speedup = rollupResult.duration / rspackResult.duration
        console.log(`\n🚀 性能提升: Rspack 比 Rollup 快 ${speedup.toFixed(2)}x`)
        
        const timeSaved = rollupResult.duration - rspackResult.duration
        console.log(`⏱️  节省时间: ${timeSaved.toFixed(2)} 秒`)
        
        if (rollupResult.fileCount === rspackResult.fileCount) {
            console.log('✅ 输出文件数量一致')
        } else {
            console.log(`⚠️  输出文件数量不一致: Rollup ${rollupResult.fileCount}, Rspack ${rspackResult.fileCount}`)
        }
    }
}

// 主函数
async function main() {
    const results = []
    
    try {
        // 测试 Rollup
        console.log('1️⃣ 测试 Rollup 构建性能...')
        const rollupResult = await runBuild('npm', ['run', 'build:templates'], 'Rollup')
        results.push(rollupResult)
        console.log(`✅ Rollup 构建完成: ${rollupResult.duration.toFixed(2)}s`)
        
        // 等待一秒
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 测试 Rspack
        console.log('\n2️⃣ 测试 Rspack 构建性能...')
        const rspackResult = await runBuild('npm', ['run', 'build:templates:rspack'], 'Rspack')
        results.push(rspackResult)
        console.log(`✅ Rspack 构建完成: ${rspackResult.duration.toFixed(2)}s`)
        
    } catch (error) {
        console.log(`❌ 构建失败: ${error.name}`)
        console.log(`错误信息: ${error.error}`)
        results.push(error)
    }
    
    // 显示结果
    formatResults(results)
    
    console.log('\n💡 提示:')
    console.log('- 首次运行可能较慢，因为需要安装依赖和缓存')
    console.log('- 多次运行取平均值会更准确')
    console.log('- 确保两种构建工具都已正确安装和配置')
}

// 检查依赖
console.log('🔍 检查构建脚本是否存在...')
const scripts = ['build:templates', 'build:templates:rspack']
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'))

for (const script of scripts) {
    if (packageJson.scripts[script]) {
        console.log(`✅ ${script} 脚本存在`)
    } else {
        console.log(`❌ ${script} 脚本不存在`)
        console.log('请确保已正确配置 package.json 中的构建脚本')
        process.exit(1)
    }
}

console.log('')
main().catch(console.error)
