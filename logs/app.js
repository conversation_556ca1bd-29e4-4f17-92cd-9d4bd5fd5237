const mongoose = require('mongoose')
const Koa = require('koa')
const Router = require('koa-router')
const fetch = require("node-fetch")
const dayjs = require("dayjs")
const cors = require('@koa/cors')
const bodyParser = require('koa-bodyparser')
const router = new Router()
const schedule = require('node-schedule');

main().catch(err => console.error(err))

const Log = mongoose.model('Log', {
  namespace: String,
  timestamp: Number,
  detail: String,
  id: String,
  message: String,
})

async function main() {
  const db = process.env.NODE_ENV === 'prod' ? 'prod' : 'dev'
  console.log(`[db]: ${db}`)
  await mongoose.connect(`mongodb://localhost:27017/${db}`)
  console.log('数据库连接成功')
  await createApp();
  // 每小时统计一次
  schedule.scheduleJob('0 * * * *', async () => {
    try {
      // 统计前一小时的日志以及汇总message
      const start = dayjs().subtract(1, 'hour').startOf('hour').valueOf();
      const end = dayjs().subtract(1, 'hour').endOf('hour').valueOf();
      const logs = await Log.find({
        timestamp: {
          $gte: start,
          $lte: end,
        }
      });
      if (logs.length === 0) {
        return;
      }
      const messageMap = {};
      logs.forEach(log => {
        if (!messageMap[log.message]) {
          messageMap[log.message] = 0;
        }
        messageMap[log.message] += 1;
      });
      const messageList = Object.keys(messageMap).map(message => {
        return {
          message,
          count: messageMap[message],
        };
      });
      await sendWechatMessageSummary(messageList);
    } catch (error) {
      console.error(error);
    }
  });
}

async function createApp() {
  const app = new Koa()

  router.get('/api/v2/print-log/:id', async (ctx) => {
    const {id} = ctx.params
    ctx.body = await Log.findById(id)
  })

  router.post('/api/v2/print-log', async (ctx) => {
    try {
      const {namespace, detail, message} = ctx.request.body
      // 查询前一条日志
      const lastLog = await Log.find({}).sort({timestamp: -1}).limit(1);
      const log = new Log({
        namespace,
        timestamp: Date.now(),
        detail,
        message,
      })
      await log.save()
      // 如果前一条日志的message和当前日志的message相同，则不发送微信消息
      if(lastLog && lastLog.length > 0 && lastLog[0].message === message) {
        return;
      }
      await sendWechatMessage(log._id, message, ctx.request.headers.origin, ctx.request.headers['user-agent'])
    } catch (e) {
      console.error(e);
    } finally {
      ctx.body = {
        code: 0,
        message: 'success',
      };
    }
  })

  app.use(cors())
  app.use(bodyParser())
  app.use(router.routes())
  app.listen(8998, () => {
    console.log(`[${8998}] 启动成功`)
  })
}

// 发送普通消息
async function sendWechatMessage(id, message, origin, userAgent) {
  await fetch('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4d851aa9-c93e-496b-8cc4-d4ee7cc9cb5b', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
      "msgtype": "markdown",
      "markdown": {
        "content": `## <font color="red">正式环境</font>
<font color="warning">${dayjs().format('YYYY-MM-DD HH:mm:ss')}</font>
> 错误信息: <font color="comment">${message}</font>
> 地址: <font color="comment">${origin}</font>
> 浏览器: <font color="comment">${userAgent}</font>
> 日志: <font color="green">${id}</font>`,
      },
    })
  })
}

// 发送汇总消息
async function sendWechatMessageSummary(messages) {
  await fetch('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4d851aa9-c93e-496b-8cc4-d4ee7cc9cb5b', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
      "msgtype": "markdown",
      "markdown": {
        "content": `## <font color="blue">${dayjs().format('YYYY-MM-DD HH:mm:ss')}汇总</font>
${messages.map(msg => `> ${msg.message} <font color="red">(${msg.count})</font>`).join('\n')}`,
      },
    })
  })
}
