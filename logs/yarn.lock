# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@koa/cors@^3.3.0":
  version "3.3.0"
  resolved "http://npm.abczs.cn/@koa%2fcors/-/cors-3.3.0.tgz#b4c1c7ee303b7c968c8727f2a638a74675b50bb2"
  integrity sha512-lzlkqLlL5Ond8jb6JLnVVDmD2OPym0r5kvZlMgAWiS9xle+Q5ulw1T358oW+RVguxUkANquZQz82i/STIRmsqQ==
  dependencies:
    vary "^1.1.2"

"@types/node@*":
  version "17.0.23"
  resolved "http://npm.abczs.cn/@types%2fnode/-/node-17.0.23.tgz#3b41a6e643589ac6442bdbd7a4a3ded62f33f7da"
  integrity sha512-UxDxWn7dl97rKVeVS61vErvw086aCYhDLyvRQZ5Rk65rZKepaFdm53GeqXaKBuOhED4e9uWq34IC3TdSdJJ2Gw==

"@types/webidl-conversions@*":
  version "6.1.1"
  resolved "http://npm.abczs.cn/@types%2fwebidl-conversions/-/webidl-conversions-6.1.1.tgz#e33bc8ea812a01f63f90481c666334844b12a09e"
  integrity sha1-4zvI6oEqAfY/kEgcZmM0hEsSoJ4=

"@types/whatwg-url@^8.2.1":
  version "8.2.1"
  resolved "http://npm.abczs.cn/@types%2fwhatwg-url/-/whatwg-url-8.2.1.tgz#f1aac222dab7c59e011663a0cb0a3117b2ef05d4"
  integrity sha1-8arCItq3xZ4BFmOgywoxF7LvBdQ=
  dependencies:
    "@types/node" "*"
    "@types/webidl-conversions" "*"

accepts@^1.3.5:
  version "1.3.8"
  resolved "http://npm.abczs.cn/accepts/-/accepts-1.3.8.tgz#0bf0be125b67014adcb0b0921e62db7bffe16b2e"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

base64-js@^1.3.1:
  version "1.5.1"
  resolved "http://npm.abczs.cn/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

bson@^4.2.2, bson@^4.6.1:
  version "4.6.2"
  resolved "http://npm.abczs.cn/bson/-/bson-4.6.2.tgz#3241c79d23d225b86ab6d2bc268b803d8a5fd444"
  integrity sha512-VeJKHShcu1b/ugl0QiujlVuBepab714X9nNyBdA1kfekuDGecxgpTA2Z6nYbagrWFeiIyzSWIOzju3lhj+RNyQ==
  dependencies:
    buffer "^5.6.0"

buffer@^5.6.0:
  version "5.7.1"
  resolved "http://npm.abczs.cn/buffer/-/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

bytes@3.1.2:
  version "3.1.2"
  resolved "http://npm.abczs.cn/bytes/-/bytes-3.1.2.tgz#8b0beeb98605adf1b128fa4386403c009e0221a5"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

cache-content-type@^1.0.0:
  version "1.0.1"
  resolved "http://npm.abczs.cn/cache-content-type/-/cache-content-type-1.0.1.tgz#035cde2b08ee2129f4a8315ea8f00a00dba1453c"
  integrity sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA==
  dependencies:
    mime-types "^2.1.18"
    ylru "^1.2.0"

call-bind@^1.0.0:
  version "1.0.2"
  resolved "http://npm.abczs.cn/call-bind/-/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

co-body@^6.0.0:
  version "6.1.0"
  resolved "http://npm.abczs.cn/co-body/-/co-body-6.1.0.tgz#d87a8efc3564f9bfe3aced8ef5cd04c7a8766547"
  integrity sha1-2HqO/DVk+b/jrO2O9c0Ex6h2ZUc=
  dependencies:
    inflation "^2.0.0"
    qs "^6.5.2"
    raw-body "^2.3.3"
    type-is "^1.6.16"

co@^4.6.0:
  version "4.6.0"
  resolved "http://npm.abczs.cn/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

content-disposition@~0.5.2:
  version "0.5.4"
  resolved "http://npm.abczs.cn/content-disposition/-/content-disposition-0.5.4.tgz#8b82b4efac82512a02bb0b1dcec9d2c5e8eb5bfe"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.4:
  version "1.0.4"
  resolved "http://npm.abczs.cn/content-type/-/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==

cookies@~0.8.0:
  version "0.8.0"
  resolved "http://npm.abczs.cn/cookies/-/cookies-0.8.0.tgz#1293ce4b391740a8406e3c9870e828c4b54f3f90"
  integrity sha512-8aPsApQfebXnuI+537McwYsDtjVxGm8gTIzQI3FDW6t5t/DAhERxtnbEPN/8RX+uZthoz4eCOgloXaE5cYyNow==
  dependencies:
    depd "~2.0.0"
    keygrip "~1.1.0"

copy-to@^2.0.1:
  version "2.0.1"
  resolved "http://npm.abczs.cn/copy-to/-/copy-to-2.0.1.tgz#2680fbb8068a48d08656b6098092bdafc906f4a5"
  integrity sha1-JoD7uAaKSNCGVrYJgJK9r8kG9KU=

cross-env@^7.0.3:
  version "7.0.3"
  resolved "http://npm.abczs.cn/cross-env/-/cross-env-7.0.3.tgz#865264b29677dc015ba8418918965dd232fc54cf"
  integrity sha1-hlJkspZ33AFbqEGJGJZd0jL8VM8=
  dependencies:
    cross-spawn "^7.0.1"

cross-spawn@^7.0.1:
  version "7.0.3"
  resolved "http://npm.abczs.cn/cross-spawn/-/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

dayjs@^1.11.3:
  version "1.11.3"
  resolved "http://npm.abczs.cn/dayjs/-/dayjs-1.11.3.tgz#4754eb694a624057b9ad2224b67b15d552589258"
  integrity sha512-xxwlswWOlGhzgQ4TKzASQkUhqERI3egRNqgV4ScR8wlANA/A9tZ7miXa44vTTKEq5l7vWoL5G57bG3zA+Kow0A==

debug@4.x, debug@^4.1.1, debug@^4.3.2:
  version "4.3.4"
  resolved "http://npm.abczs.cn/debug/-/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

deep-equal@~1.0.1:
  version "1.0.1"
  resolved "http://npm.abczs.cn/deep-equal/-/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"
  integrity sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://npm.abczs.cn/delegates/-/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

denque@^2.0.1:
  version "2.0.1"
  resolved "http://npm.abczs.cn/denque/-/denque-2.0.1.tgz#bcef4c1b80dc32efe97515744f21a4229ab8934a"
  integrity sha1-vO9MG4DcMu/pdRV0TyGkIpq4k0o=

depd@2.0.0, depd@^2.0.0, depd@~2.0.0:
  version "2.0.0"
  resolved "http://npm.abczs.cn/depd/-/depd-2.0.0.tgz#b696163cc757560d09cf22cc8fad1571b79e76df"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

depd@~1.1.2:
  version "1.1.2"
  resolved "http://npm.abczs.cn/depd/-/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

destroy@^1.0.4:
  version "1.2.0"
  resolved "http://npm.abczs.cn/destroy/-/destroy-1.2.0.tgz#4803735509ad8be552934c67df614f94e66fa015"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://npm.abczs.cn/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

encodeurl@^1.0.2:
  version "1.0.2"
  resolved "http://npm.abczs.cn/encodeurl/-/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

escape-html@^1.0.3:
  version "1.0.3"
  resolved "http://npm.abczs.cn/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

fresh@~0.5.2:
  version "0.5.2"
  resolved "http://npm.abczs.cn/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://npm.abczs.cn/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==

get-intrinsic@^1.0.2:
  version "1.1.1"
  resolved "http://npm.abczs.cn/get-intrinsic/-/get-intrinsic-1.1.1.tgz#15f59f376f855c446963948f0d24cd3637b4abc6"
  integrity sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

has-symbols@^1.0.1, has-symbols@^1.0.2:
  version "1.0.3"
  resolved "http://npm.abczs.cn/has-symbols/-/has-symbols-1.0.3.tgz#bb7b2c4349251dce87b125f7bdf874aa7c8b39f8"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "http://npm.abczs.cn/has-tostringtag/-/has-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has@^1.0.3:
  version "1.0.3"
  resolved "http://npm.abczs.cn/has/-/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
  dependencies:
    function-bind "^1.1.1"

http-assert@^1.3.0:
  version "1.5.0"
  resolved "http://npm.abczs.cn/http-assert/-/http-assert-1.5.0.tgz#c389ccd87ac16ed2dfa6246fd73b926aa00e6b8f"
  integrity sha1-w4nM2HrBbtLfpiRv1zuSaqAOa48=
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.8.0"

http-errors@2.0.0:
  version "2.0.0"
  resolved "http://npm.abczs.cn/http-errors/-/http-errors-2.0.0.tgz#b7774a1486ef73cf7667ac9ae0858c012c57b9d3"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@^1.6.3, http-errors@^1.7.3, http-errors@~1.8.0:
  version "1.8.1"
  resolved "http://npm.abczs.cn/http-errors/-/http-errors-1.8.1.tgz#7c3f28577cbc8a207388455dbd62295ed07bd68c"
  integrity sha1-fD8oV3y8iiBziEVdvWIpXtB71ow=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.1"

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "http://npm.abczs.cn/iconv-lite/-/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.13:
  version "1.2.1"
  resolved "http://npm.abczs.cn/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

inflation@^2.0.0:
  version "2.0.0"
  resolved "http://npm.abczs.cn/inflation/-/inflation-2.0.0.tgz#8b417e47c28f925a45133d914ca1fd389107f30f"
  integrity sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8=

inherits@2.0.4:
  version "2.0.4"
  resolved "http://npm.abczs.cn/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

ip@^1.1.5:
  version "1.1.5"
  resolved "http://npm.abczs.cn/ip/-/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

is-generator-function@^1.0.7:
  version "1.0.10"
  resolved "http://npm.abczs.cn/is-generator-function/-/is-generator-function-1.0.10.tgz#f1558baf1ac17e0deea7c0415c438351ff2b3c72"
  integrity sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=
  dependencies:
    has-tostringtag "^1.0.0"

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://npm.abczs.cn/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

kareem@2.3.5:
  version "2.3.5"
  resolved "http://npm.abczs.cn/kareem/-/kareem-2.3.5.tgz#111fe9dbab754c8ed88b7a2360e2680cec1420ca"
  integrity sha512-qxCyQtp3ioawkiRNQr/v8xw9KIviMSSNmy+63Wubj7KmMn3g7noRXIZB4vPCAP+ETi2SR8eH6CvmlKZuGpoHOg==

keygrip@~1.1.0:
  version "1.1.0"
  resolved "http://npm.abczs.cn/keygrip/-/keygrip-1.1.0.tgz#871b1681d5e159c62a445b0c74b615e0917e7226"
  integrity sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ==
  dependencies:
    tsscmp "1.0.6"

koa-bodyparser@^4.3.0:
  version "4.3.0"
  resolved "http://npm.abczs.cn/koa-bodyparser/-/koa-bodyparser-4.3.0.tgz#274c778555ff48fa221ee7f36a9fbdbace22759a"
  integrity sha1-J0x3hVX/SPoiHufzap+9us4idZo=
  dependencies:
    co-body "^6.0.0"
    copy-to "^2.0.1"

koa-compose@^4.1.0:
  version "4.1.0"
  resolved "http://npm.abczs.cn/koa-compose/-/koa-compose-4.1.0.tgz#507306b9371901db41121c812e923d0d67d3e877"
  integrity sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw==

koa-convert@^2.0.0:
  version "2.0.0"
  resolved "http://npm.abczs.cn/koa-convert/-/koa-convert-2.0.0.tgz#86a0c44d81d40551bae22fee6709904573eea4f5"
  integrity sha1-hqDETYHUBVG64i/uZwmQRXPupPU=
  dependencies:
    co "^4.6.0"
    koa-compose "^4.1.0"

koa-router@^10.1.1:
  version "10.1.1"
  resolved "http://npm.abczs.cn/koa-router/-/koa-router-10.1.1.tgz#20809f82648518b84726cd445037813cd99f17ff"
  integrity sha1-IICfgmSFGLhHJs1EUDeBPNmfF/8=
  dependencies:
    debug "^4.1.1"
    http-errors "^1.7.3"
    koa-compose "^4.1.0"
    methods "^1.1.2"
    path-to-regexp "^6.1.0"

koa@^2.13.4:
  version "2.13.4"
  resolved "http://npm.abczs.cn/koa/-/koa-2.13.4.tgz#ee5b0cb39e0b8069c38d115139c774833d32462e"
  integrity sha1-7lsMs54LgGnDjRFROcd0gz0yRi4=
  dependencies:
    accepts "^1.3.5"
    cache-content-type "^1.0.0"
    content-disposition "~0.5.2"
    content-type "^1.0.4"
    cookies "~0.8.0"
    debug "^4.3.2"
    delegates "^1.0.0"
    depd "^2.0.0"
    destroy "^1.0.4"
    encodeurl "^1.0.2"
    escape-html "^1.0.3"
    fresh "~0.5.2"
    http-assert "^1.3.0"
    http-errors "^1.6.3"
    is-generator-function "^1.0.7"
    koa-compose "^4.1.0"
    koa-convert "^2.0.0"
    on-finished "^2.3.0"
    only "~0.0.2"
    parseurl "^1.3.2"
    statuses "^1.5.0"
    type-is "^1.6.16"
    vary "^1.1.2"

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://npm.abczs.cn/media-typer/-/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "http://npm.abczs.cn/memory-pager/-/memory-pager-1.5.0.tgz#d8751655d22d384682741c972f2c3d6dfa3e66b5"
  integrity sha1-2HUWVdItOEaCdByXLyw9bfo+ZrU=

methods@^1.1.2:
  version "1.1.2"
  resolved "http://npm.abczs.cn/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://npm.abczs.cn/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.18, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "http://npm.abczs.cn/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mongodb-connection-string-url@^2.4.1:
  version "2.5.2"
  resolved "http://npm.abczs.cn/mongodb-connection-string-url/-/mongodb-connection-string-url-2.5.2.tgz#f075c8d529e8d3916386018b8a396aed4f16e5ed"
  integrity sha512-tWDyIG8cQlI5k3skB6ywaEA5F9f5OntrKKsT/Lteub2zgwSUlhqEN2inGgBTm8bpYJf8QYBdA/5naz65XDpczA==
  dependencies:
    "@types/whatwg-url" "^8.2.1"
    whatwg-url "^11.0.0"

mongodb@4.3.1:
  version "4.3.1"
  resolved "http://npm.abczs.cn/mongodb/-/mongodb-4.3.1.tgz#e346f76e421ec6f47ddea5c8f5140e6181aaeb94"
  integrity sha512-sNa8APSIk+r4x31ZwctKjuPSaeKuvUeNb/fu/3B6dRM02HpEgig7hTHM8A/PJQTlxuC/KFWlDlQjhsk/S43tBg==
  dependencies:
    bson "^4.6.1"
    denque "^2.0.1"
    mongodb-connection-string-url "^2.4.1"
    socks "^2.6.1"
  optionalDependencies:
    saslprep "^1.0.3"

mongoose@^6.2.9:
  version "6.2.9"
  resolved "http://npm.abczs.cn/mongoose/-/mongoose-6.2.9.tgz#6318dceeebdd23d30a5a414a98bdfeab13fc83ef"
  integrity sha512-6ApgF3rKYah5pUEO/1H+QrT0GT05OR7FprtVM45yzcrT/IKKlXizPyttrMiK1mLPt+55pGU7PMsBWY7yx/xZ4g==
  dependencies:
    bson "^4.2.2"
    kareem "2.3.5"
    mongodb "4.3.1"
    mpath "0.8.4"
    mquery "4.0.2"
    ms "2.1.3"
    sift "16.0.0"

mpath@0.8.4:
  version "0.8.4"
  resolved "http://npm.abczs.cn/mpath/-/mpath-0.8.4.tgz#6b566d9581621d9e931dd3b142ed3618e7599313"
  integrity sha1-a1ZtlYFiHZ6THdOxQu02GOdZkxM=

mquery@4.0.2:
  version "4.0.2"
  resolved "http://npm.abczs.cn/mquery/-/mquery-4.0.2.tgz#a13add5ecd7c2e5a67e0f814b3c7acdfb6772804"
  integrity sha512-oAVF0Nil1mT3rxty6Zln4YiD6x6QsUWYz927jZzjMxOK2aqmhEz5JQ7xmrKK7xRFA2dwV+YaOpKU/S+vfNqKxA==
  dependencies:
    debug "4.x"

ms@2.1.2:
  version "2.1.2"
  resolved "http://npm.abczs.cn/ms/-/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.1.3:
  version "2.1.3"
  resolved "http://npm.abczs.cn/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

negotiator@0.6.3:
  version "0.6.3"
  resolved "http://npm.abczs.cn/negotiator/-/negotiator-0.6.3.tgz#58e323a72fedc0d6f9cd4d31fe49f51479590ccd"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

object-inspect@^1.9.0:
  version "1.12.2"
  resolved "http://npm.abczs.cn/object-inspect/-/object-inspect-1.12.2.tgz#c0641f26394532f28ab8d796ab954e43c009a8ea"
  integrity sha512-z+cPxW0QGUp0mcqcsgQyLVRDoXFQbXOwBaqyF7VIgI4TWNQsDHrBpUQslRmIfAoYWdYzs6UlKJtB2XJpTaNSpQ==

on-finished@^2.3.0:
  version "2.4.1"
  resolved "http://npm.abczs.cn/on-finished/-/on-finished-2.4.1.tgz#58c8c44116e54845ad57f14ab10b03533184ac3f"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

only@~0.0.2:
  version "0.0.2"
  resolved "http://npm.abczs.cn/only/-/only-0.0.2.tgz#2afde84d03e50b9a8edc444e30610a70295edfb4"
  integrity sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q=

parseurl@^1.3.2:
  version "1.3.3"
  resolved "http://npm.abczs.cn/parseurl/-/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

path-key@^3.1.0:
  version "3.1.1"
  resolved "http://npm.abczs.cn/path-key/-/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-to-regexp@^6.1.0:
  version "6.2.0"
  resolved "http://npm.abczs.cn/path-to-regexp/-/path-to-regexp-6.2.0.tgz#f7b3803336104c346889adece614669230645f38"
  integrity sha1-97OAMzYQTDRoia3s5hRmkjBkXzg=

punycode@^2.1.1:
  version "2.1.1"
  resolved "http://npm.abczs.cn/punycode/-/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==

qs@^6.5.2:
  version "6.10.3"
  resolved "http://npm.abczs.cn/qs/-/qs-6.10.3.tgz#d6cde1b2ffca87b5aa57889816c5f81535e22e8e"
  integrity sha512-wr7M2E0OFRfIfJZjKGieI8lBKb7fRCH4Fv5KNPEs7gJ8jadvotdsS08PzOKR7opXhZ/Xkjtt3WF9g38drmyRqQ==
  dependencies:
    side-channel "^1.0.4"

raw-body@^2.3.3:
  version "2.5.1"
  resolved "http://npm.abczs.cn/raw-body/-/raw-body-2.5.1.tgz#fe1b1628b181b700215e5fd42389f98b71392857"
  integrity sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

safe-buffer@5.2.1:
  version "5.2.1"
  resolved "http://npm.abczs.cn/safe-buffer/-/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "http://npm.abczs.cn/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

saslprep@^1.0.3:
  version "1.0.3"
  resolved "http://npm.abczs.cn/saslprep/-/saslprep-1.0.3.tgz#4c02f946b56cf54297e347ba1093e7acac4cf226"
  integrity sha1-TAL5RrVs9UKX40e6EJPnrKxM8iY=
  dependencies:
    sparse-bitfield "^3.0.3"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "http://npm.abczs.cn/setprototypeof/-/setprototypeof-1.2.0.tgz#66c9a24a73f9fc28cbe66b09fed3d33dcaf1b424"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://npm.abczs.cn/shebang-command/-/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://npm.abczs.cn/shebang-regex/-/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

side-channel@^1.0.4:
  version "1.0.4"
  resolved "http://npm.abczs.cn/side-channel/-/side-channel-1.0.4.tgz#efce5c8fdc104ee751b25c58d4290011fa5ea2cf"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

sift@16.0.0:
  version "16.0.0"
  resolved "http://npm.abczs.cn/sift/-/sift-16.0.0.tgz#447991577db61f1a8fab727a8a98a6db57a23eb8"
  integrity sha512-ILTjdP2Mv9V1kIxWMXeMTIRbOBrqKc4JAXmFMnFq3fKeyQ2Qwa3Dw1ubcye3vR+Y6ofA0b9gNDr/y2t6eUeIzQ==

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "http://npm.abczs.cn/smart-buffer/-/smart-buffer-4.2.0.tgz#6e1d71fa4f18c05f7d0ff216dd16a481d0e8d9ae"
  integrity sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=

socks@^2.6.1:
  version "2.6.2"
  resolved "http://npm.abczs.cn/socks/-/socks-2.6.2.tgz#ec042d7960073d40d94268ff3bb727dc685f111a"
  integrity sha512-zDZhHhZRY9PxRruRMR7kMhnf3I8hDs4S3f9RecfnGxvcBHQcKcIH/oUcEWffsfl1XxdYlA7nnlGbbTvPz9D8gA==
  dependencies:
    ip "^1.1.5"
    smart-buffer "^4.2.0"

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "http://npm.abczs.cn/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz#ff4ae6e68656056ba4b3e792ab3334d38273ca11"
  integrity sha1-/0rm5oZWBWuks+eSqzM004JzyhE=
  dependencies:
    memory-pager "^1.0.2"

statuses@2.0.1:
  version "2.0.1"
  resolved "http://npm.abczs.cn/statuses/-/statuses-2.0.1.tgz#55cb000ccf1d48728bd23c685a063998cf1a1b63"
  integrity sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=

"statuses@>= 1.5.0 < 2", statuses@^1.5.0:
  version "1.5.0"
  resolved "http://npm.abczs.cn/statuses/-/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

toidentifier@1.0.1:
  version "1.0.1"
  resolved "http://npm.abczs.cn/toidentifier/-/toidentifier-1.0.1.tgz#3be34321a88a820ed1bd80dfaa33e479fbb8dd35"
  integrity sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=

tr46@^3.0.0:
  version "3.0.0"
  resolved "http://npm.abczs.cn/tr46/-/tr46-3.0.0.tgz#555c4e297a950617e8eeddef633c87d4d9d6cbf9"
  integrity sha1-VVxOKXqVBhfo7t3vYzyH1NnWy/k=
  dependencies:
    punycode "^2.1.1"

tsscmp@1.0.6:
  version "1.0.6"
  resolved "http://npm.abczs.cn/tsscmp/-/tsscmp-1.0.6.tgz#85b99583ac3589ec4bfef825b5000aa911d605eb"
  integrity sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA==

type-is@^1.6.16:
  version "1.6.18"
  resolved "http://npm.abczs.cn/type-is/-/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

unpipe@1.0.0:
  version "1.0.0"
  resolved "http://npm.abczs.cn/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

vary@^1.1.2:
  version "1.1.2"
  resolved "http://npm.abczs.cn/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "http://npm.abczs.cn/webidl-conversions/-/webidl-conversions-7.0.0.tgz#256b4e1882be7debbf01d05f0aa2039778ea080a"
  integrity sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=

whatwg-url@^11.0.0:
  version "11.0.0"
  resolved "http://npm.abczs.cn/whatwg-url/-/whatwg-url-11.0.0.tgz#0a849eebb5faf2119b901bb76fd795c2848d4018"
  integrity sha1-CoSe67X68hGbkBu3b9eVwoSNQBg=
  dependencies:
    tr46 "^3.0.0"
    webidl-conversions "^7.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://npm.abczs.cn/which/-/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

ylru@^1.2.0:
  version "1.3.2"
  resolved "http://npm.abczs.cn/ylru/-/ylru-1.3.2.tgz#0de48017473275a4cbdfc83a1eaf67c01af8a785"
  integrity sha512-RXRJzMiK6U2ye0BlGGZnmpwJDPgakn6aNQ0A7gHRbD4I0uvK4TW6UqkK1V0pp9jskjJBAXd3dRrbzWkqJ+6cxA==
