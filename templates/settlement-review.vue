<template>
    <settlement
        :print-data="printData"
        settlement-type="review"
    ></settlement>
</template>

<script>
    import CommonHandler from "./data-handler/common-handler";
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import Settlement from "./components/settlement/index.vue";

    export default {
        name: 'SettlementReview',
        components: {Settlement},
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.SETTLEMENT_REVIEW,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分', // 默认选择的等分纸
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
        },
    }
</script>
