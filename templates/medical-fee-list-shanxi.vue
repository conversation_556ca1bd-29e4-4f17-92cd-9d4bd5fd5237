<template>
    <div class="medical-fee-list-shanxi">
        <!-- 标题 -->
        <div
            data-type="header"
            class="shanxi-header"
        >
            <span class="shanxi-title">山西省城镇医疗保险门诊明细清单</span>
        </div>

        <!-- 基本信息 -->
        <div class="shanxi-base-info">
            <div
                class="shanxi-base-info-text shanxi-no-wrap"
                style="width: 50%;"
                overflow
            >
                参保单位：{{ extraInfo.empName }}
            </div>
            <div
                class="shanxi-base-info-text shanxi-no-wrap"
                style="width: 20%;"
                overflow
            >
                人员类别：{{ shebaoPayment.cardOwnerType }}
            </div>
            <div
                class="shanxi-base-info-text shanxi-no-wrap"
                style="width: 30%; text-align: right;"
                overflow
            >
                结算号：{{ extraInfo.setlId }}
            </div>
        </div>

        <!-- 明细清单 -->
        <table>
            <tbody>
                <!-- 患者就诊信息 -->
                <tr>
                    <td
                        :colspan="1"
                        class="shanxi-fee-type-td"
                    >
                        姓名
                    </td>
                    <td
                        :colspan="1"
                        class="shanxi-fee-type-td"
                    >
                        {{ patient.name }}
                    </td>
                    <td
                        :colspan="1"
                        class="shanxi-fee-type-td"
                    >
                        社保号码
                    </td>
                    <td
                        :colspan="2"
                        class="shanxi-fee-type-td"
                        style="width: 25%;"
                    >
                        {{ extraInfo.psnNo }}
                    </td>
                    <td
                        :colspan="1"
                        class="shanxi-fee-type-td"
                    >
                        医疗类型
                    </td>
                    <td
                        :colspan="2"
                        class="shanxi-fee-type-td"
                        style="width: 25%;"
                    >
                        {{ shebaoPayment.medType }}
                    </td>
                </tr>

                <!-- 费用类型 -->
                <tr
                    v-for="(list, listIndex) in feeTypeViewList"
                    :key="listIndex"
                >
                    <template v-for="(item, itemIndex) in list">
                        <td
                            :key="`fee-type-view-list-${listIndex}-${itemIndex}-1`"
                            :colspan="1"
                            class="shanxi-fee-type-td"
                        >
                            {{ item.label }}
                        </td>
                        <td
                            :key="`fee-type-view-list-${listIndex}-${itemIndex}-2`"
                            :colspan="1"
                            class="shanxi-fee-type-td"
                        >
                            {{ item.value | formatMoney }}
                        </td>
                    </template>
                </tr>
            
                <!-- 费用合计 -->
                <tr>
                    <td
                        :colspan="2"
                        class="shanxi-fee-info"
                    >
                        费用合计(小写)
                    </td>
                    <td
                        :colspan="2"
                        class="shanxi-fee-info"
                    >
                        {{ totalPrice | formatMoney }}
                    </td>
                    <td
                        :colspan="1"
                        class="shanxi-fee-info"
                        style="width: 12.5%;"
                    >
                        大写
                    </td>
                    <td
                        :colspan="3"
                        class="shanxi-fee-info"
                        style="width: 37.5%;"
                    >
                        {{ totalPrice | digitUppercase }}
                    </td>
                </tr>
            
                <tr
                    v-for="(list, listIndex) in shebaoSettleInfo"
                    :key="listIndex"
                >
                    <template v-for="(item, itemIndex) in list">
                        <td
                            :key="`shebao-card-info-${listIndex}-${itemIndex}-1`"
                            :colspan="2"
                            class="shanxi-fee-info"
                        >
                            {{ item.label }}
                        </td>
                        <td
                            :key="`shebao-card-info-${listIndex}-${itemIndex}-2`"
                            :colspan="2"
                            class="shanxi-fee-info"
                        >
                            {{ item.value | formatMoney }}
                        </td>
                    </template>
                </tr>
            </tbody>
        </table>

        <!-- 机构信息 -->
        <div
            class="shanxi-base-info"
            style="padding-top: 6pt;"
        >
            <div
                class="shanxi-base-info-text shanxi-no-wrap"
                style="width: 50%;"
                overflow
            >
                定点医疗机构：{{ institutionName }}
            </div>
            <div
                class="shanxi-base-info-text shanxi-no-wrap"
                style="width: 20%;"
                overflow
            >
                经办人：{{ printData.chargedByName }}
            </div>
            <div
                class="shanxi-base-info-text shanxi-no-wrap"
                style="width: 30%; text-align: right;"
                overflow
            >
                经办日期：{{ printData.chargedTime | parseTime('y-m-d h:i:s') }}
            </div>
        </div>
        
        <!-- 备注 -->
        <div class="shanxi-remark">
            备注：本明细清单只作为“山西省医药单位医药费统一发票（医保专用）的附件”，需加盖定点医疗机构费用专用章，不单独作为报销凭证。
        </div>
        
        <div class="shanxi-fee-list-title">
            费用清单
        </div>
        
        <div class="shanxi-department-info">
            <div
                class="shanxi-department-text shanxi-no-wrap"
                overflow
            >
                科室：{{ printData.departmentName }}
            </div>
            <div
                class="shanxi-department-text shanxi-no-wrap"
                style="text-align: right;"
                overflow
            >
                结算号：{{ extraInfo.setlId }}
            </div>
        </div>

        <table-charge-item-shanxi
            :print-data="printData"
            :charge-form-items="renderChargeFormItems"
            :is-fee-compose="isFeeCompose"
            :product-info-rule-config="productInfoRuleConfig"
            :charged-by-name="printData.chargedByName"
            :charged-time="printData.chargedTime"
        ></table-charge-item-shanxi>
        
        <div class="shanxi-divide-dashed-line"></div>
        
        <div class="shanxi-charge-list-title">
            科室留存
        </div>

        <table-charge-item-shanxi
            :print-data="printData"
            :charge-form-items="renderChargeFormItems"
            :is-fee-compose="isFeeCompose"
            :product-info-rule-config="productInfoRuleConfig"
            :charged-by-name="printData.chargedByName"
            :charged-time="printData.chargedTime"
        ></table-charge-item-shanxi>
    </div>
</template>

<script>
    import BillDataMixins from "./mixins/bill-data.js";
    import NationalBillData from "./mixins/national-bill-data.js";
    import CommonHandler from "./data-handler/common-handler.js";
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import {clone, digitUppercase} from "./common/utils.js";
    import TableChargeItemShanxi from "./components/medical-fee-list/table-charge-item-shanxi.vue";

    export default {
        name: 'MedicalFeeListShanxi',
        components: {TableChargeItemShanxi},
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_FEE_LIST_SHANXI,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        filters: {
            digitUppercase,
        },
        mixins: [BillDataMixins, NationalBillData],
        computed: {
            medicalListConfig() {
                return this.renderData.config.medicalListConfig || {}
            },
            curMedicalConfig() {
                return this.medicalListConfig.shanxi || {};
            },
            isFeeCompose() {
                return !!this.printData.IS_FEE_COMPOSE;
            },
            // 项目信息规则配置
            productInfoRuleConfig() {
                return this.curMedicalConfig.productInfoRule || 0;
            },
            // 医疗清单 0 只展示套餐  2 只展示子项  1 展示套餐加子项 转换成同 医疗票据 0 只展示套餐1 只展示子项  2 展示套餐名加子项
            composeChildrenConfig() {
                switch (this.curMedicalConfig.composeChildren || 0) {
                    case 1:
                        return 2;
                    case 2:
                        return 1;
                    default:
                        return 0;
                }
            },
            otherViewFee() {
                return this.examinationFee + this.nursingFee + this.physiotherapyFee + this.bedFee + this.pharmacyServiceFee + this.otherFee + this.outerFlagFee;
            },
            patient() {
                return this.printData.patient || {};
            },
            institutionName() {
                return this.curMedicalConfig.institutionName || '';
            },
            feeTypeViewList() {
                return [
                    [
                        { label: '挂号费', value: this.registrationFee },
                        { label: '诊查费', value: 0 },
                        { label: '检查费', value: this.examinationInspectionFee },
                        { label: '化验费', value: this.examinationExaminationFee },
                    ],
                    [
                        { label: '治疗费', value: this.treatmentFee },
                        { label: '手术', value: this.operationFee },
                        { label: '卫生材料费', value: this.materialFee },
                        { label: '西药费', value: this.westernMedicineFee },
                    ],
                    [
                        { label: '中药饮片', value: this.chineseMedicineDrinksPieceFee },
                        { label: '中成药费', value: this.chineseComposeMedicineFee },
                        { label: '一般诊疗费', value: this.generalDiagnosisAndTreatmentFee },
                        { label: '其他费', value: this.otherViewFee },
                    ],
                ];
            },
            // 是否为住院发票
            isHospital() {
                return this.printData.feeListType === 'hospital';
            },
            totalPrice() {
                return this.isHospital ? (this.printData.totalPrice ?? 0) : (this.printData.netIncomeFee ?? 0);
            },
            shebaoSettleInfo() {
                // 基本基金支付
                const hifpPay = this.extraInfo.hifpPay ?? 0;
                // 个人账户支付
                const accountPaymentFee = (this.isHospital ? this.shebaoPayment.acctPay : this.shebaoPayment.accountPaymentFee) ?? 0;
                // 大病基金支付
                const hifmiPay = this.extraInfo.hifmiPay ?? 0;
                // 个人自付现金
                const cashPay = this.extraInfo.psnCashPay ?? 0;
                // 公务员补助基金支付
                const cvlservPay = this.extraInfo.cvlservPay ?? 0;
                // 其中个人自费
                const personalPaymentFee = (this.printData.personalPaymentFee ?? 0) - cashPay;
                // 补充基金支付
                const hifobPay = this.extraInfo.hifobPay ?? 0;
                // 个人账户余额
                const cardBalance = this.shebaoPayment.cardBalance ?? 0;

                return [
                    [
                        { label: '基本基金支付', value: hifpPay },
                        { label: '个人账户支付', value: accountPaymentFee },
                    ],
                    [
                        { label: '大病基金支付', value: hifmiPay },
                        { label: '个人自付现金', value: isNaN(cashPay) ? 0 : cashPay },
                    ],
                    [
                        { label: '公务员补助基金支付', value: cvlservPay },
                        { label: '其中个人自费', value: isNaN(personalPaymentFee) ? 0 : personalPaymentFee },
                    ],
                    [
                        { label: '补充基金支付', value: hifobPay },
                        { label: '个人账户余额', value: cardBalance },
                    ],
                ];
            },
            renderChargeFormItems() {
                return Array.isArray(this.chargeFormItems) ? this.chargeFormItems.slice(0, 12) : [];
            },
        },
        watch: {
        },
        created() {
            this.initFee();
            console.log('%crenderData\n', 'background: green; padding: 0 5px', clone(this.renderData));
        },
        methods: {
        },
    }
</script>

<style lang="scss">
.abc-page_preview {
    color: #000000;
    background: #ffffff;
}

.medical-fee-list-shanxi {
    padding: 20pt;
    font-family: SimSun, serif;
    font-size: 10pt;

    .shanxi-no-wrap {
        overflow: hidden;
        white-space: nowrap;
    }

    .shanxi-header {
        width: 100%;
        padding: 0 0 20pt 0;
        text-align: center;
    }

    .shanxi-title {
        font-size: 16pt;
        line-height: 21pt;
    }

    .shanxi-base-info {
        width: 100%;
        padding-bottom: 6pt;
        font-size: 0;
    }

    .shanxi-base-info-text {
        display: inline-block;
        width: 33%;
        font-size: 10pt;
        line-height: 12pt;
    }

    table {
        width: 100%;
        font-size: 10pt;
        line-height: 12pt;
        border-collapse: collapse;
    }

    table,
    th,
    td {
        border: 1px solid #000000;
    }

    th,
    td {
        padding: 6pt;
    }

    th {
        font-weight: normal;
        text-align: left;
    }

    .shanxi-fee-type-td {
        width: 12.5%;
    }

    .shanxi-fee-info {
        width: 25%;
    }

    .shanxi-remark {
        line-height: 12pt;
    }

    .shanxi-fee-list-title {
        width: 100%;
        padding: 12pt 0;
        font-size: 14pt;
        line-height: 16pt;
        text-align: center;
    }

    .shanxi-department-info {
        width: 100%;
        padding-bottom: 6pt;
        font-size: 0;
    }

    .shanxi-department-text {
        display: inline-block;
        width: 50%;
        font-size: 10pt;
        line-height: 12pt;
    }

    .shanxi-divide-dashed-line {
        display: inline-block;
        width: 100%;
        height: 0;
        padding-top: 12pt;
        border-bottom: 1px dashed #000000;
    }

    .shanxi-charge-list-title {
        display: inline-block;
        width: 100%;
        padding: 12pt 0;
        font-size: 14pt;
        line-height: 18pt;
        text-align: center;
    }
}
</style>
