<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    socialName: '诊费',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 100.11,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    socialName: 'HPV基因全套',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 320,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    socialName: '甲胎蛋白（AFP）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 40,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '针灸理疗套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
        },

        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    socialName: '四环素软膏（三益）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '支',
                    discountedPrice: 36.0,
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    socialName: '法莫替丁片（迪诺洛克）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '片',
                    discountedPrice: 6.0,
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    socialName: '胸腺肽肠溶片（奇莫欣）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '盒',
                    discountedPrice: 20.0,
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            curYearBalance: 8, // 当年账户余额
            allYearBalance: 10, // 历年账户余额
            curYearAccountPaymentFee: 7, // 本年账户支付
            allYearAccountPaymentFee: 9, // 历年账户支付
            fundPayment: 0, // 规定病种
            cashPayment: 0, // 规定病种
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 1, // 自负金额
            allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 3, // 自理金额
            allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 5, // 自费金额
            allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            sbzzPaymentFee: 0, // 商保赔付
        },
    },
}
-->

<template>
    <div>
        <div class="dalian-fee-list-wrapper">
            <table class="shebao-table-wrapper">
                <thead>
                    <tr>
                        <th
                            colspan="7"
                            class="organ-name"
                        >
                            {{ organ.name }}医疗保险结账收据
                        </th>
                    </tr>
                    <tr>
                        <th colspan="7">
                            <span
                                class="medical-type"
                                style="width: 38%;"
                            >就诊分类:{{ extraInfo.clinicType }}</span>
                            <span
                                class="medical-type right-text"
                                style="width: 60%;"
                            >单据号:{{ extraInfo.documentNo }}</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td
                            colspan="1"
                            class="border-right-line center-text title-td"
                        >
                            <span class="head-label">基本信息</span>
                        </td>
                        <td
                            colspan="6"
                            class="content-td"
                        >
                            <div style="font-size: 0;">
                                <span class="card-number">保号:{{ shebaoPayment.cardId }}</span>
                                <span class="second-col">起付标准:{{ extraInfo.startToPayStandard }}</span>
                                <span>类别:{{ shebaoPayment.cardOwnerType }}</span>
                            </div>
                            <div style="font-size: 0;">
                                <span class="card-number">姓名:{{ patient.name }}</span>
                                <span>补助类型:{{ extraInfo.grantType }}</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td
                            colspan="1"
                            class="border-right-line center-text title-td"
                        >
                            费用
                        </td>
                        <td
                            colspan="6"
                            class="content-td"
                        >
                            <div>
                                <span
                                    v-for="(item, index) in medicalBills"
                                    :key="index"
                                    class="charge-item"
                                >{{ item.name }}:{{ item.totalFee | formatMoney }}</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td
                            colspan="1"
                            rowspan="4"
                            class="border-right-line center-text title-td"
                        >
                            <span class="head-label">账户信息</span>
                        </td>
                        <td
                            colspan="6"
                            class="content-td"
                            style="font-size: 0;"
                        >
                            <span class="first-price">个人账户结前:{{ shebaoPayment.beforeCardBalance | formatMoney }}</span>
                            <span class=" card-number">支付:{{ shebaoPayment.accountPaymentFee | formatMoney }}</span>
                            <span>余额:{{ shebaoPayment.cardBalance | formatMoney }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td
                            colspan="6"
                            class="content-td"
                            style="font-size: 0;"
                        >
                            <span class="first-price">统筹累计结前:{{ extraInfo.asWholeAccumulatedSettlementBefore | formatMoney }}</span>
                            <span>结后:{{ extraInfo.asWholeAccumulatedSettlementAfter | formatMoney }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td
                            colspan="6"
                            class="content-td"
                            style="font-size: 0;"
                        >
                            <span class="first-price">门诊统筹结前:{{ extraInfo.outpatientServiceAsWholeSettlementBefore | formatMoney }}</span>
                            <span class="card-number">支付:{{ extraInfo.outpatientServiceAsWholeSettlement | formatMoney }}</span>
                            <span>结后:{{ extraInfo.outpatientServiceAsWholeSettlement | formatMoney }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td
                            colspan="6"
                            class="content-td"
                            style="font-size: 0;"
                        >
                            <div>
                                <span class="big-price">总费用:{{ finalFee | formatMoney }}</span>
                                <span>其中保险支付:{{ extraInfo.insurancePayments | formatMoney }}</span>
                            </div>
                            <div>
                                <span class="big-price">大写:{{ finalFee|digitUppercase }}</span>
                                <span>现金支付:{{ shebaoPayment.personalPaymentFee | formatMoney }}</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td
                            colspan="7"
                            style="font-size: 0;"
                        >
                            <span
                                class="foot-item"
                                style="width: 24%;"
                            >操作员:{{ printData.chargedByName }}</span>
                            <span
                                class="foot-item"
                                style="width: 40%;"
                            >诊断:{{ formatDiagnosis2Str(printData.diagnosis) }}</span>
                            <span class="foot-item">医生:{{ printData.doctorName }}</span>
                        </td>
                    </tr>
                </tfoot>
            </table>

            <table class="product-items-wrapper">
                <thead>
                    <tr>
                        <th colspan="4">
                            单据号:{{ extraInfo.documentNo }}
                        </th>
                        <th
                            colspan="3"
                            class="right-text"
                        >
                            姓名:{{ patient.name }}
                        </th>
                    </tr>
                    <tr>
                        <td
                            colspan="3"
                            class="title-td"
                        >
                            药品名称
                        </td>
                        <td
                            colspan="1"
                            class="right-text title-td"
                        >
                            数量
                        </td>
                        <td
                            colspan="1"
                            class="right-text title-td"
                        >
                            单位
                        </td>
                        <td
                            colspan="1"
                            class="right-text title-td"
                        >
                            单价
                        </td>
                        <td
                            colspan="1"
                            class="right-text title-td"
                        >
                            金额
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <template v-for="item in chargeFormItems">
                        <tr :key="item.id">
                            <td
                                colspan="3"
                                :class="{ 'is-child': item.composeType === 2 && composeChildrenConfig === 2 }"
                            >
                                <template v-if="medicalFeeGrade2PrintStr(item.medicalFeeGrade)">
                                    [{{ medicalFeeGrade2PrintStr(item.medicalFeeGrade) }}]
                                </template>
                                <span>{{ item.name }}</span>
                            </td>
                            <td
                                colspan="1"
                                class="right-text"
                            >
                                <span>{{ item.count }}</span>
                            </td>
                            <td
                                colspan="1"
                                class="right-text"
                            >
                                <span>{{ item.unit }}</span>
                            </td>
                            <td
                                colspan="1"
                                class="right-text"
                            >
                                <span>{{ item.discountedUnitPrice | formatMoney }}</span>
                            </td>
                            <td
                                colspan="1"
                                class="right-text"
                            >
                                <span>{{ item.discountedPrice | formatMoney }}</span>
                            </td>
                        </tr>
                        <template v-if="item.goodsStockInfos">
                            <tr
                                v-for="(stockItem, sIndex) in item.goodsStockInfos"
                                :key="sIndex"
                            >
                                <td colspan="7">
                                    <span>批号:{{ stockItem.batchNo }}</span>
                                    <span>效期:{{ stockItem.expiryDate }}</span>
                                    <span>厂家:{{ stockItem.manufacturer }}</span>
                                    <span>规格:{{ item.displaySpec }}</span>
                                </td>
                            </tr>
                        </template>
                    </template>
                </tbody>
                <tfoot>
                    <tr>
                        <td
                            colspan="7"
                            class="right-text amount-tr"
                        >
                            合计:{{ finalFee | formatMoney }}
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';

    import { formatDiagnosis2Str, medicalFeeGrade2PrintStr } from "./common/utils.js";
    import NationalBillData from "./mixins/national-bill-data.js";
    export default {
        name: "MedicalBillLiaoNing",
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_LIAONING,
        pages: [
            {
                paper: PageSizeMap.MM114_LIAONING,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            liaoning() {
                return this.config.liaoning || {};
            },
            composeChildrenConfig() {
                return this.liaoning.composeType;
            },
        },
        methods: {
            formatDiagnosis2Str,
            medicalFeeGrade2PrintStr
        },
    }
</script>
<style lang="scss">
.abc-page_preview {
  background: #fff;
  color: #2a82e4;
}
.dalian-fee-list-wrapper {
  padding: 2mm;
  font-size: 10pt;

  .shebao-table-wrapper {
    width: 116mm;
    table-layout: fixed;
    border-collapse: collapse;

    td {
      border: 1pt solid #000000;
    }

    .organ-name {
      font-size: 11pt;
      text-align: center;
    }
  }

  .medical-type,
  .card-number,
  .second-col,
  .first-price,
  .foot-item,
  .big-price,
  .charge-item,
  .head-label,
  span {
    display: inline-block;
    *display: inline;
    _display: inline;
  }

  .head-label {
    width: 24pt;
  }

  .card-number {
    width: 35%;
    min-width: 35%;
  }

  .second-col {
    width: 30%;
    min-width: 30%;
  }

  .first-price {
    width: 42%;
    min-width: 42%;
  }

  .foot-item {
    overflow: hidden;
    font-size: 10pt;
    word-break: keep-all;
    white-space: nowrap;
  }

  .center-text {
    text-align: center;
  }

  .title-td {
    padding: 0 2mm;
  }

  .content-td {
    padding: 0 2mm;

    span {
      padding-top: 1mm;
      padding-bottom: 1mm;
      font-size: 10pt;
      vertical-align: text-top;
    }
  }

  .border-right-line {
    border-right: 1pt solid #000000;
  }

  .margin-item {
    margin-left: 2mm;
  }

  .charge-item {
    width: 33%;
    vertical-align: top;
  }

  .product-items-wrapper {
    width: 116mm;
    margin-top: 4mm;
    word-break: break-all;
    table-layout: fixed;
    border-collapse: collapse;

    .title-td {
      border-top: 1pt solid #000000;
      border-bottom: 1pt solid #000000;
    }

    td,
    th {
      padding: 0 2mm;
    }

    td {
      padding-top: 1mm;
      padding-bottom: 1mm;
      overflow: hidden;
      word-break: keep-all;
      white-space: nowrap;

      &.is-child {
        padding-left: 14pt;
      }
    }

    tbody tr td {
      font-size: 0;

      span {
        font-size: 10pt;
      }
    }
  }

  .right-text {
    text-align: right;
  }

  .amount-tr {
    border-top: 1pt solid #000000;
  }

  .big-price {
    width: 54%;
    font-size: 11pt;
  }
}
</style>
