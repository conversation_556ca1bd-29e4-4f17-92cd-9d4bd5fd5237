<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    departmentName: "内科",
    doctorName: "刘喜",
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈3',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 0,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    socialName: '诊费',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 100.11,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    socialName: 'HPV基因全套',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 320,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    socialName: '甲胎蛋白（AFP）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 40,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '针灸理疗套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
        },

        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    socialName: '四环素软膏（三益）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '支',
                    discountedPrice: 36.0,
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    socialName: '法莫替丁片（迪诺洛克）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '片',
                    discountedPrice: 6.0,
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    socialName: '胸腺肽肠溶片（奇莫欣）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '盒',
                    discountedPrice: 20.0,
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        medType: '门慢',
        extraInfo: {
            curYearBalance: 8, // 当年账户余额
            allYearBalance: 10, // 历年账户余额
            curYearAccountPaymentFee: 7, // 本年账户支付
            allYearAccountPaymentFee: 9, // 历年账户支付
            fundPayment: 0, // 规定病种
            cashPayment: 0, // 规定病种
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 1, // 自负金额
            allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 3, // 自理金额
            allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 5, // 自费金额
            allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            sbzzPaymentFee: 0, // 商保赔付
            acctMulaidPay: 12, // 共济账户支付
            gongjiAuthorName: '张三', // 授权人
            gongjiRelation: '父母', // 授权人关系
            gongjiBalc: 100, // 授权人余额
        },
    },
}
-->
<template>
    <div>
        <template v-for="(bills, pageIndex) in renderPage">
            <div class="chengdu-tax-medical-bill-content">
                <refund-icon
                    v-if="printData.IS_REFUND"
                    top="0.3cm"
                    left="1cm"
                ></refund-icon>

                <div
                    v-if="printData.departmentName"
                    class="department-name"
                >
                    科室：{{ printData.departmentName }}
                </div>

                <div
                    v-if="printData.doctorName"
                    class="doctor-name"
                >
                    医生：{{ printData.doctorName }}
                </div>

                <block-box
                    top="11"
                    left="10"
                    :font="8"
                >
                    医疗类别：{{ shebaoPayment.medType }}
                </block-box>
                <div class="amount">
                    账户余额：{{ shebaoPayment.cardBalance | formatMoney }}
                </div>
                <block-box
                    top="15"
                    left="47"
                    :font="8"
                >
                    统筹支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}
                </block-box>
                <block-box
                    top="19"
                    left="47"
                    :font="8"
                >
                    共济支付：{{ extraInfo.acctMulaidPay | formatMoney }}
                </block-box>
                <block-box
                    top="23"
                    left="47"
                    :font="8"
                >
                    授权人：{{ extraInfo.gongjiAuthorName }}
                </block-box>
                <block-box
                    top="27"
                    left="47"
                    :font="8"
                >
                    授权人关系：{{ extraInfo.gongjiRelation }}
                </block-box>
                <block-box
                    top="31"
                    left="47"
                    :font="8"
                >
                    授权人余额：{{ extraInfo.gongjiBalc | formatMoney }}
                </block-box>
                <div class="name">
                    {{ patient.name }} &nbsp;&nbsp; {{ patient.sex }}&nbsp;&nbsp; {{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                </div>

                <div
                    v-if="bills.length < 4"
                    class="items-wrapper"
                >
                    <div
                        v-for="(item, index) in bills"
                        :key="index"
                    >
                        <span class="item-name">{{ item.name }}</span>
                        <span class="item-amount">{{ item.totalFee | formatMoney }}</span>
                    </div>
                </div>
                <div
                    v-else-if="bills.length <= 6"
                    class="more-items-wrapper-limit"
                >
                    <div
                        v-for="(item, index) in bills"
                        :key="index"
                        class="item"
                    >
                        <span class="item-name">{{ item.name }}</span>
                        <span class="item-amount">{{ item.totalFee | formatMoney }}</span>
                    </div>
                </div>
                <div
                    v-else
                    class="more-items-wrapper"
                >
                    <div
                        v-for="(item, index) in bills"
                        :key="index"
                        class="item"
                    >
                        <span class="item-name">{{ item.name }}</span>
                        <span class="item-amount">{{ item.totalFee | formatMoney }}</span>
                    </div>
                </div>
                <template v-if="!pageIndex">
                    <div
                        class="personal-fee-label"
                    >
                        现金支付
                    </div>
                    <div class="personal-fee">
                        {{ printData.personalPaymentFee | formatMoney }}
                    </div>
                    <div
                        class="account-fee-label"
                    >
                        个人账户支付
                    </div>
                    <div class="account-fee">
                        {{ shebaoPayment.accountPaymentFee | formatMoney }}
                    </div>
                </template>
                <div class="total">
                    <template v-if="oneChargeTypePerPage && bills.length">
                        {{ bills[0].totalFee | formatMoney }}
                    </template>
                    <template v-else>
                        {{ finalFee | formatMoney }}
                    </template>
                </div>
                <div class="total-case">
                    <template v-if="oneChargeTypePerPage && bills.length">
                        {{ digitUppercase(bills[0].totalFee) }}
                    </template>
                    <template v-else>
                        {{ digitUppercase(finalFee) }}
                    </template>
                </div>
                <div class="charger">
                    {{ printData.chargedByName }}
                </div>

                <div class="year">
                    {{ year }}
                </div>

                <div class="month">
                    {{ month }}
                </div>
                <div class="day">
                    {{ day }}
                </div>
            </div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import BlockBox from "./components/medical-bill/national-medical-bill/block-box.vue";

    export default {
        name: "MedicalBillChengdu",
        components: {
            RefundIcon,
            BlockBox
        },
        mixins: [BillDataMixins],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_CHENGDU,
        props: {
            printLogger: {
                type: Object,
                default: () => {}
            },
            loggerKeyId: String,
            loggerScene: String,
        },
        pages: [
            {
                paper: PageSizeMap.MM82_101_SICHUANG_TAX,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            chengdu() {
                return this.config.chengdu || {};
            },
            oneChargeTypePerPage() {
                return this.chengdu.oneChargeTypePerPage;
            },
            isPageLimitAmount() {
                return this.chengdu.isPageLimitAmount;
            },
            pageLimitAmount() {
                return this.chengdu.pageLimitAmount || 9999;
            },
            renderPage() {
                let renderBills = [];
                if(this.oneChargeTypePerPage) {
                    const tempMedicalBills = this.medicalBills.filter( item => {
                        return item.totalFee;
                    })
                    tempMedicalBills.forEach( item => {
                        // 设置了按分类打印，并且分类费用大于分页限制金额，且限制金额不为0
                        const totalFee = Math.abs(item.totalFee);
                        if(this.isPageLimitAmount && this.pageLimitAmount && totalFee > this.pageLimitAmount){
                            const pages = Math.ceil(totalFee / this.pageLimitAmount);
                            for( let i = 0; i < pages; i++) {
                                const resTotalFee = i === pages - 1 ? totalFee - this.pageLimitAmount * i : this.pageLimitAmount;
                                renderBills.push([{
                                    ...item,
                                    totalFee: item.totalFee < 0 ? `-${resTotalFee}` : resTotalFee,
                                }])
                            }
                        } else {
                            renderBills.push([item])
                        }
                    })
                } else {
                    renderBills = [this.medicalBills]
                }
                if (!renderBills.length) {
                    renderBills.push([]);
                }
                return renderBills;
            }
        },
        watch: {
            renderPage: {
                handler(val) {
                    if(val && this.printLogger && this.loggerKeyId && this.loggerScene) {
                        this.printLogger._send(this.loggerScene, {
                            keyId: this.loggerKeyId,
                            info: '实际渲染的 renderPage',
                            medicalBills: this.medicalBills,
                            renderPage: val,
                        })
                    }
                },
                deep: true
            }
        }
    }
</script>
<style lang="scss">
* {
  padding: 0;
  margin: 0;
}

.chengdu-tax-medical-bill-content {
  @import "./components/refund-icon/refund-icon";

  position: relative;
  top: 0;
  left: 0;
  width: 82mm;
  height: 101mm;
  font-size: 9pt;
  line-height: 10pt;

  .department-name,
  .doctor-name,
  .amount,
  .card-no,
  .name,
  .total,
  .items-wrapper,
  .more-items-wrapper,
  .more-items-wrapper-limit,
  .total-case,
  .charger,
  .personal-fee,
  .personal-fee-label,
  .account-fee,
  .account-fee-label,
  .organ,
  .year,
  .month,
  .day {
    position: absolute;
  }

  .amount {
    top: 11mm;
    left: 47mm;
  }

  .card-no {
    top: 38.5mm;
    left: 22mm;
  }

  .name {
    top: 41.5mm;
    left: 22mm;
  }

  .items-wrapper {
    top: 53mm;
    left: 14.5mm;

    .item-name,
    .item-amount {
      display: inline-block;
      width: 24mm;
    }

    .item-name {
      margin-left: 4mm;
    }

    .item-amount {
      padding-left: 2mm;
    }
  }

  .more-items-wrapper {
    top: 46mm;
    left: 16mm;
    width: 62mm;

    .item {
      display: inline-block;
      width: 28mm;
    }
  }

  .more-items-wrapper-limit {
    top: 53mm;
    left: 16mm;
    width: 62mm;

    .item {
      display: inline-block;
      width: 28mm;
    }
  }

  .personal-fee {
    top: 65mm;
    left: 45.5mm;
  }
  .personal-fee-label {
    top: 65mm;
    left: 18mm;
  }

  .account-fee {
    top: 71mm;
    left: 45.5mm;
  }
  .account-fee-label {
    top: 71mm;
    left: 18mm;
  }

  .total {
    top: 77.2mm;
    left: 45.5mm;
  }

  .total-case {
    top: 83.5mm;
    left: 28mm;
  }

  .charger {
    top: 89mm;
    left: 24mm;
  }

  .year,
  .month,
  .day {
    top: 89mm;
  }

  .year {
    left: 44mm;
  }

  .month {
    left: 54mm;
  }

  .day {
    left: 61mm;
  }

  .department-name {
    top: 15mm;
    left: 10mm;
  }

  .doctor-name {
    top: 19mm;
    left: 10mm;
  }
}

.abc-page_preview {
  background: url("/static/assets/print/chengdu-tax-new.png");
  background-size: 82mm 101mm;
  color: #2a82e4;
  .chengdu-tax-medical-bill-content {
    top: -1mm;
  }
}
</style>
