<template>
    <div></div>
</template>

<script>
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import CommonHandler from "./data-handler/common-handler";
    import PageSizeMap, {Orientation} from "../share/page-size.js";

    export default {
        name: "StatReportTypeTemplate",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.STAT_REPORT_TABLE_TEMPLATE,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
        ],
    }
</script>

<style lang="scss">
.report-table-card-header {
    &-title {
        display: flex;
        justify-content: center;
        font-size: 20px;
        font-weight: 600;
        line-height: 54px;
    }

    &-subTitle {
        margin: 12px 0 14px 0;
        line-height: 20px;
        font-size: 12px;
    }
}

table {
    width: 100% !important;
    font-size: 8px;
    border: 1px solid #aaa;
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;

    thead {
        background: #f5f7fb;

        th {
            text-align: center;
            width: 235px;
            min-width: 235px;
            height: 28px;
            white-space: normal;
            word-break: normal;
            max-height: 30px;
            padding: 2px;
            font-weight: 400;
            color: #000000;
            border: 1px solid #aaa;
            border-left: none;
            border-top: none;
            box-sizing: border-box;
            background: #fff;
        }
    }

    tbody {
        .tr-summaries {
          font-weight: bold;
        }
        tr {
            border-bottom: 1px solid #aaa;
            border-left: none;
            border-right: none;

            td {
                box-sizing: border-box;

                &:not(:last-child) {
                    border-right: 1px solid #aaa;
                }

                div.cell {
                    padding: 2px;
                }

                background: #fff;
            }
        }
    }
}
</style>
