<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费s',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305903',
            chargeFormItems: [
                {
                    id: 'ffebc4a7da95425489aeeb456b0c43ec',
                    name: '推拿',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 50.0,
                },
                {
                    id: 'eb2a534087c34b18934c84f5af292fd6',
                    name: '肩周炎针灸治疗',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 90.0,
                },
            ],
            sourceFormType: 3,
        },
        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f0200',
                    name: '复方丹参片（罗浮山）',
                    unit: '瓶',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
        {
            id: '9410ffd3ece8439e9e12c8f3df396bc8',
            chargeFormItems: [
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                    name: '盐知母',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                    specialRequirement: '包煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9072',
                    name: '山药YG',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9073',
                    name: '牡丹皮YG',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9076',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
            ],
            sourceFormType: 6,
            specification: '中药饮片',
            doseCount: 1,
            dailyDosage: '1日1剂',
            usage: '煎服',
            freq: '1日3次',
            usageLevel: '每次150ml',
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            curYearBalance: 8, // 当年账户余额
            allYearBalance: 10, // 历年账户余额
            curYearAccountPaymentFee: 7, // 本年账户支付
            allYearAccountPaymentFee: 9, // 历年账户支付
            fundPayment: 0, // 规定病种
            cashPayment: 0, // 规定病种
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 1, // 自负金额
            allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 3, // 自理金额
            allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 5, // 自费金额
            allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            sbzzPaymentFee: 0, // 商保赔付
        },
    },
};
-->

<template>
    <div>
        <template v-for="(page, pageIndex) in currentRenderPage">
            <div class="hebei-medical-bill-content">
                <div
                    v-if="blueInvoiceData"
                    style="position: absolute; top: 0.2cm; left: 1.8cm;"
                >
                    销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }}
                    号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                </div>
                <refund-icon
                    v-if="isRefundBill"
                    top="1cm"
                    left="1.8cm"
                ></refund-icon>
                <block-box
                    :top="10"
                    :left="23"
                >
                    机构编码：{{ printData.hospitalCode }}
                </block-box>
                <block-box
                    :top="10"
                    :left="85"
                >
                    医疗类别：{{ shebaoPayment.medType }}
                </block-box>
                <block-box
                    :top="10"
                    :left="135"
                >
                    人员编码：{{ extraInfo.psnNo }}
                </block-box>
                <block-box
                    :top="14"
                    :left="77"
                >
                    {{ extraInfo.insutype ? '医保' : '自费' }}
                </block-box>
                <div class="stub-form">
                    <span class="card-id">
                        {{ shebaoPayment.cardId }}
                    </span>
                    <span class="name">
                        {{ patient.name }}
                    </span>

                    <span class="sex">
                        {{ patient.sex }}
                    </span>
                    <div
                        class="organ"
                    >
                        {{ hebei.institutionName }}
                    </div>
                    <div class="charger">
                        {{ printData.chargedByName }}
                    </div>
                    <span
                        class="card-type"
                        style="width: 95mm"
                    >
                        {{ shebaoPayment.cardOwnerType }}
                    </span>
                    <span class="date">
                        <span class="year">{{ year }}</span>
                        <span class="month">{{ month }}</span>
                        <span class="day">{{ day }}</span>
                    </span>


                    <span class="total-fee">{{ digitUppercase(finalFee) }}  </span>
                    <span class="total-fee-small">{{ finalFee|formatMoney }}</span>
                </div>
                <div
                    v-if="!extra.isPreview && currentRenderPage.length > 1"
                    class="page-number"
                >
                    <span data-page-no="PageNo">##</span>/<span data-page-count="PageCount">##</span>
                </div>
                <div class="invoice">
                    <div class="form-items-wrapper">
                        <template>
                            <div
                                v-for="(item, index) in page.formItems"
                                :key="index + pageIndex"
                                class="form-item-tr form-item-two-col"
                            >
                                <span
                                    overflow
                                    class="item-name"
                                >
                                    <template v-if="isShowSocialCode(item)">
                                        [{{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}]
                                    </template>
                                    {{ item.name }}
                                    <template
                                        v-if="item.displaySpec && !(item.productType === GoodsTypeEnum.MEDICINE && item.productSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine)"
                                    >/{{ item.displaySpec }}</template>
                                </span>
                                <span class="item-count">{{ item.count }}{{ item.unit }}</span>
                                <span class="item-price">{{ item.discountedPrice | formatMoney }}</span>
                            </div>
                        </template>
                        <div
                            v-if="hasOverPageTip"
                            class="only-one-page form-item-tr"
                        >
                            *** 因纸张限制，部分项目未打印 ***
                        </div>
                    </div>
                    <block-box
                        :top="17"
                        :left="117"
                    >
                        {{ printData.patientOrderNo }}
                    </block-box>
                    <block-box
                        :top="17"
                        :left="81"
                    >
                        {{ printData.departmentName }}
                    </block-box>
                    <block-box
                        :top="17"
                        :left="17"
                    >
                        <span v-if="currentConfig && currentConfig.medicalOrganizationType">
                            {{ organ.category }}
                        </span>
                    </block-box>
                    <div
                        v-if="!pageIndex"
                        style="position: absolute; top: 95mm; width: 169mm; height: 21mm;"
                        class="shebao-wrapper"
                    >
                        <block-box
                            :top="1"
                            :left="24"
                            :font="7"
                        >
                            {{ shebaoPayment.fundPaymentFee | formatMoney }}
                        </block-box>

                        <block-box
                            :top="1"
                            :left="66"
                            :font="7"
                        >
                            {{ shebaoPayment.accountPaymentFee | formatMoney }}
                        </block-box>


                        <block-box
                            :top="1"
                            :left="102"
                            :font="7"
                        >
                            {{ shebaoPayment.selfPaymentFee | formatMoney }}
                        </block-box>

                        <block-box
                            :top="1"
                            :left="144"
                            :font="7"
                        >
                            {{ extraInfo.fulamtOwnpayAmt | formatMoney }}
                        </block-box>

                        <block-box
                            :top="6"
                            :left="24"
                            :font="7"
                        >
                            {{ shebaoPayment.cardBalance | formatMoney }}
                        </block-box>

                        <block-box
                            :top="6"
                            :left="66"
                            :font="7"
                        >
                            {{ extraInfo.cvlservPay | formatMoney }}
                        </block-box>

                        <block-box
                            :top="6"
                            :left="108"
                            :font="7"
                        >
                            {{ extraInfo.optFundPayCum | formatMoney }}
                        </block-box>

                        <block-box
                            :top="6"
                            :left="128"
                            :font="7"
                        >
                            本次起付标准：{{ extraInfo.currentDedcStd | formatMoney }}
                        </block-box>

                        <block-box
                            :top="10"
                            :left="2.5"
                            :font="7"
                        >
                            个人现金支付：{{ extraInfo.psnCashPay | formatMoney }}
                        </block-box>
                        <block-box
                            :top="10"
                            :left="44"
                            :font="7"
                        >
                            医疗救助支付：{{ extraInfo.mafPay | formatMoney }}
                        </block-box>
                        <block-box
                            :top="10"
                            :left="86"
                            :font="7"
                        >
                            符合政策范围金额：{{ extraInfo.inscpScpAmt | formatMoney }}
                        </block-box>
                        <block-box
                            :top="10"
                            :left="128"
                            :font="7"
                        >
                            起付标准累计：{{ extraInfo.dedcCum | formatMoney }}
                        </block-box>

                        <block-box
                            :top="14"
                            :left="2.5"
                            :font="7"
                        >
                            个人负担总金额：{{ extraInfo.psnPartAmt | formatMoney }}
                        </block-box>
                        <block-box
                            :top="14"
                            :left="44"
                            :font="7"
                        >
                            个人门诊共济支付金额：{{ extraInfo.acctMulaidPay | formatMoney }}
                        </block-box>
                        <block-box
                            :top="14"
                            :left="86"
                            :font="7"
                        >
                            先行自付：{{ extraInfo.preselfpayAmt | formatMoney }}
                        </block-box>
                        <block-box
                            :top="14"
                            :left="128"
                            :font="7"
                        >
                            慢特病病种：{{ extraInfo.chronicDiseaseName }}
                        </block-box>
                    </div>
                </div>
            </div>
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import {GoodsSubTypeEnum, GoodsTypeEnum, PRINT_BILL_AMOUNT_TYPE} from "./common/constants.js";
    import NationalBillData from "./mixins/national-bill-data";
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';

    export default {
        name: "MedicalBillHebei",
        components: {
            RefundIcon,
            BlockBox
        },
        mixins: [BillDataMixins, NationalBillData],
        props: {
            extra: {
                type: Object,
                default() {
                    return {}
                }
            }
        },
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_HEBEI,
        pages: [
            {
                paper: PageSizeMap.MM210_127_ZHEJIANG,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        data() {
            return {
                GoodsTypeEnum,
                GoodsSubTypeEnum,
                registrationFee: '', // 挂号费
                westernMedicineFee: '', // 西药费
                chineseMedicineFee: '',
                chineseMedicineDrinksPieceFee: '', // 中药饮片费用
                chineseComposeMedicineFee: '', // 中成药费用 + 中药颗粒
                treatmentFee: '', // 治疗理疗费
                examinationInspectionFee: '', // 检查费
                examinationExaminationFee: '', // 化验费
                materialFee: '', // 材料费
                otherFee: '', // 一般诊疗费( 其他费用 )
            };
        },
        computed: {
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                return this.isOnlyOnePage ? this.renderPage.slice(0, 1) : this.renderPage
            },
            splitType() {
                return this.hebei.splitType;
            },
            isOnlyOnePage() {
                return this.splitType === 1 && (this.renderPage.length > 1 || this.extra.isPreview);
            },
            hebei() {
                return this.config.hebei || {};
            },
            // chargeFormItems() {
            //     let formItems = [];
            //     this.chargeForms.forEach((form) => {
            //         formItems = formItems.concat(form.chargeFormItems);
            //     });
            //     return formItems;
            // },
            renderPage() {
                return this.spliceFormItems(this.chargeFormItems, 24);
            },
        },
        created() {
            this.initFee();
        },
        methods: {
            initFee() {
                this.medicalBills.forEach((item) => {
                    switch (item.printType) {
                        case PRINT_BILL_AMOUNT_TYPE.westernMedicineFeeType:
                            this.westernMedicineFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.chineseMedicineDrinksPieceFeeType:
                            this.chineseMedicineDrinksPieceFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.chineseComposeMedicineFeeType:
                            this.chineseComposeMedicineFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.examinationInspectionFeeType:
                            this.examinationInspectionFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.examinationExaminationFeeType:
                            this.examinationExaminationFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.treatmentFeeType:
                            this.treatmentFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.registrationFeeType:
                            this.registrationFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.materialFeeType:
                            this.materialFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.otherFeeType:
                            this.otherFee = item.totalFee;
                            break;
                        default:
                            break;
                    }
                });
            },
        },
    }
</script>
<style lang="scss">
* {
    padding: 0;
    margin: 0;
}

.hebei-medical-bill-content {
    @import "./components/refund-icon/refund-icon.scss";
    font-size: 9pt;

    position: absolute;
    top: 1mm;
    width: 210mm;
    height: 127mm;

    .stub-form {
        position: relative;
        width: 95mm;
        height: 127mm;
        //border: 1pt solid #00aca0;
        .card-id,
        .name,
        .date,
        .sex,
        .fee-item,
        .total-fee,
        .total-fee-small,
        .card-type,
        .charger,
        .organ {
            position: absolute;
        }

        .card-type {
            top: 19mm;
            left: 100mm;
        }

        .card-id {
            top: 19mm;
            left: 150mm;
        }

        .date {
            top: 113mm;
        }

        .sex {
            top: 19mm;
            left: 68mm;
        }

        .name {
            top: 19mm;
            left: 32mm;
        }

        .date {
            left: 160mm;

            .year, .month, .day {
                position: absolute;
            }

            .year {
                left: 0;
            }

            .month {
                left: 13mm;
            }

            .day {
                left: 20mm;
            }
        }

        .fee-item {
            position: absolute;
        }

        .col-1 {
            left: 35mm;
        }

        .col-2 {
            left: 68mm;
        }

        .row-1 {
            top: 47mm;
        }

        .row-2 {
            top: 53mm;
        }

        .row-3 {
            top: 60mm;
        }

        .row-4 {
            top: 66mm;
        }

        .row-5 {
            top: 71mm;
        }

        .row-6 {
            top: 79mm;
        }

        .row-7 {
            top: 86mm;
        }

        .row-8 {
            top: 92.5mm;
        }

        .total-fee {
            top: 86.5mm;
            left: 43mm;
        }

        .total-fee-small {
            top: 86.5mm;
            left: 113mm;
        }

        .organ {
            top: 113.5mm;
            left: 46mm;
            white-space: nowrap;
        }

        .charger {
            top: 113.5mm;
            left: 120mm;
            width: 95mm;
        }
    }

    .invoice {
        position: absolute;
        top: -3.5mm;
        left: 20mm;
        width: 210mm;
        height: 80mm;
        //border: 1pt solid #00aca0;

        .form-items-wrapper {
            position: absolute;
            top: 35mm;
            left: 5mm;
            width: 170mm;
            height: 65mm;
            font-size: 0;

            .form-item-tr {
                width: 100%;
                font-size: 0;

                &.form-item-two-col {
                    display: inline-block;
                    width: 49.9%;

                    .item-name {
                        width: 64%;
                        min-width: 64%;
                        max-width: 64%;
                    }

                    .item-count {
                        width: 14%;
                        min-width: 14%;
                        max-width: 14%;
                    }

                    .item-price {
                        width: 19%;
                        min-width: 19%;
                        max-width: 19%;
                    }
                }

                &.only-one-page {
                    font-size: 9pt;
                    text-align: center;
                }
            }

            .item-name,
            .item-count,
            .item-price {
                display: inline-block;
                padding-right: 0.1mm;
                font-size: 9pt;
                line-height: 12pt;
                vertical-align: text-top;
            }

            .item-name {
                width: 57%;
                min-width: 57%;
                max-width: 57%;
                margin-right: 2pt;
                overflow: hidden;
                word-break: keep-all;
                white-space: nowrap;
            }

            .item-count {
                width: 11%;
                min-width: 11%;
                max-width: 11%;
                overflow: hidden;
                word-break: keep-all;
                white-space: nowrap;
            }

            .item-price {
                width: 15%;
                min-width: 15%;
                max-width: 15%;
            }
        }


        .med-type {
            position: absolute;
            top: 0;
            right: 8mm;
        }

        .year-amount {
            > span {
                display: inline-block;
                width: 42mm;
            }
        }
    }

    .page-number {
        position: absolute;
        top: 109mm;
        left: 105mm;
    }
}

.abc-page_preview {
    background: url("/static/assets/print/hebei.jpg");
    background-size: 210mm 127mm;
    color: #2a82e4;
}
</style>
