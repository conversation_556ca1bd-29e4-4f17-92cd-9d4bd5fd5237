<template>
    <div class="points-bill-wrapper">
        <div class="points-bill-content">
            <div class="points-bill-title">
                {{ clinicBasicConfig.name }}
            </div>

            <div class="points-bill-subtitle">
                积分抵扣凭证
            </div>

            <!-- 患者信息 -->
            <div class="points-bill-info">
                <div class="points-bill-info-item">
                    <span>姓 名：</span>
                    <span>{{ patient.name || '匿名患者' }}</span>
                    <span class="points-bill-info-gender">{{ patient.sex }}</span>
                    <span class="points-bill-info-age">{{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}</span>
                </div>

                <div class="points-bill-info-item">
                    <span>手 机：</span>
                    <span>{{ patient.mobile }}</span>
                </div>
            </div>

            <div class="points-bill-dashed"></div>

            <!-- 积分信息 -->
            <div class="points-bill-info">
                <div class="points-bill-points-row">
                    <span>原始积分</span>
                    <span>{{ printData.pointsBefore || 0 }}</span>
                </div>
                <div class="points-bill-points-row">
                    <span>兑换积分</span>
                    <span>{{ Math.abs(printData.points) || 0 }}</span>
                </div>
                <div class="points-bill-points-row">
                    <span>兑后积分</span>
                    <span>{{ printData.pointsAfter || 0 }}</span>
                </div>
            </div>

            <!-- 兑换信息 -->
            <div class="points-bill-exchange-info">
                {{ remark }}
            </div>

            <div class="points-bill-dashed"></div>

            <!-- 操作信息 -->
            <div class="points-bill-info">
                <div class="points-bill-info-item">
                    <span>操作人：</span>
                    <span>{{ printData.operatorName || '' }}</span>
                </div>
                <div class="points-bill-info-item">
                    <span>操作时间：</span>
                    <span>{{ printData.operatorTime | formatDate('YYYY-MM-DD HH:mm') }}</span>
                </div>
            </div>

            <!-- 签名 -->
            <div class="points-bill-signature">
                <span>客户签名：</span>
            </div>

            <div class="end-point">
                <div></div>
            </div>
        </div>
    </div>
</template>

<script>
    import PrintCommonDataHandler from "./data-handler/common-handler.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { formatAge } from './common/utils';
    import { formatDate } from '@tool/date';

    export default {
        name: "PointsBill",
        DataHandler: PrintCommonDataHandler,
        businessKey: PrintBusinessKeyEnum.POINTS_BILL,
        pages: [
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],

        filters: {
            formatDate,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            clinicBasicConfig() {
                return this.printData.organ || {};
            },
            patient() {
                return this.printData.patient || {};
            },
            remark() {
                return this.printData.params?.remark || '';
            },
        },
        methods: {
            formatAge,
        }
    };
</script>
<style lang="scss">
.points-bill-wrapper {
    width: 100%;
    padding: 0;
    background-color: #fff;
    font-size: 12px;
    color: #000;
    line-height: 1.5;

    .points-bill-content {
        width: 100%;
        padding: 18px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .points-bill-title {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
    }

    .points-bill-subtitle {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
    }

    .points-bill-dashed {
        height: 1px;
        border-top: 1px dashed #000;
    }

    .points-bill-info {
        display: flex;
        flex-direction: column;
        gap: 1px;
    }

    .points-bill-info-item {
        display: flex;
        align-items: center;
    }

    .points-bill-info-gender,
    .points-bill-info-age {
        margin-left: 15px;
    }

    .points-bill-points-row {
        display: flex;
        justify-content: space-between;
    }

    .points-bill-signature {
        margin: 10px 0 12px;
    }

    .end-point {
        display: flex;
        justify-content: center;
        > div {
            height: 1px;
            width: 1px;
            border-top: 1px solid #000;
        }
    }
}
</style>