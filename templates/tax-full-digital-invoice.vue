<!--exampleData
{
  address: "杭州西湖区x2",
  bankAccount: "1212143412312124124123412",
  bankAgent: "工商银行",
  invoiceChecker: "张三",
  invoiceFee: 185.8,
  invoiceOpener: "李四",
  invoiceRemark: "2025-01-15用电费用",
  patientId: "ffffffff0000000019b298100f340000",
  patientName: "电力发票",
  payee: "李四",
  sendRemarkType: 0,
  status: 2,
  taxName: "杭州供电公司",
  taxNum: "***************",
  telephone: "***********",
  invoiceNumber: '1111',
  invoiceDate: "2025-03-02T07:22:33Z",
  type: 1,
  buyerName: "购买方名称",
  buyerTaxNum: "购买方税号",
  sellerName: "销售方名称",
  sellerTaxNum: "销售方税号",
  "displayGoods": [
                        {
                            "displayProjectName": "*医疗服务*挂号费",
                            "displaySpecType": null,
                            "displayUnit": "次",
                            "displayNum": 1,
                            "displayUnitPrice": 1.2,
                            "displayExcludedTaxAmount": 1.2,
                            "displayTaxRateName": "免税",
                            "displayTaxAmount": "***"
                        },
                        {
                            "displayProjectName": "*医疗服务*熬药费（青白江）",
                            "displaySpecType": null,
                            "displayUnit": "次",
                            "displayNum": 32,
                            "displayUnitPrice": 1,
                            "displayExcludedTaxAmount": 32,
                            "displayTaxRateName": "免税",
                            "displayTaxAmount": "***"
                        },
                        {
                            "displayProjectName": "*医疗服务*肌电图",
                            "displaySpecType": null,
                            "displayUnit": "每条肌肉",
                            "displayNum": 24,
                            "displayUnitPrice": 1.2E+2,
                            "displayExcludedTaxAmount": 2.88E+3,
                            "displayTaxRateName": "免税",
                            "displayTaxAmount": "***"
                        },
                        {
                            "displayProjectName": "*医疗服务*肌电图监测",
                            "displaySpecType": null,
                            "displayUnit": "小时",
                            "displayNum": 24,
                            "displayUnitPrice": 26,
                            "displayExcludedTaxAmount": 624,
                            "displayTaxRateName": "免税",
                            "displayTaxAmount": "***"
                        }
                    ],
}
-->

<template>
    <div>
        <div class="print-shudian-e-invoice-preview">
            <div class="invoice-wrapper">
                <div class="header">
                    <block-box
                        v-if="printData.invoiceNumber"
                        :top="11"
                        :left="157"
                        :font="7"
                    >
                        {{ printData.invoiceNumber }}
                    </block-box>
                    <block-box
                        v-if="printData.invoiceDate"
                        :top="17"
                        :left="157"
                        :font="7"
                    >
                        {{ formatDate(printData.invoiceDate, 'YYYY[年]MM[月]DD[日]') }}
                    </block-box>
                </div>
                <div class="table-wrapper">
                    <div class="table-header">
                        <!-- 购买方信息 -->
                        <block-box
                            :top="3"
                            :left="16"
                            :font="9"
                        >
                            {{ buyerName }}
                        </block-box>
                        <!-- 购买方税号 -->
                        <block-box
                            :top="12"
                            :left="47"
                            :font="9"
                        >
                            {{ buyerNo }}
                        </block-box>
                       

                        <!-- 销售方信息 -->
                        <block-box
                            :top="3"
                            :left="110"
                            :font="9"
                        >
                            {{ printData.sellerName }}
                        </block-box>
                        <block-box
                            :top="12"
                            :left="144"
                            :font="9"
                        >
                            {{ printData.sellerTaxNum }}
                        </block-box>
                    </div>
                    <div class="table-body">
                        <div
                            v-for="(item, index) in displayGoods"
                            :key="index"
                            class="table-tr-item"
                        >
                            <div
                                class="tr-product-name"
                            >
                                {{ item.displayProjectName }}
                            </div>
                            <div
                                class="product-spec"
                            >
                                {{ item.displaySpecType }}
                            </div>
                            <div
                                class="product-unit"
                            >
                                {{ item.displayUnit }}
                            </div>
                            <div
                                class="product-num"
                            >
                                {{ item.displayNum }}
                            </div>
                            <div
                                class="product-unit-price"
                            >
                                {{ item.displayUnitPrice }}
                            </div>
                            <div
                                class="product-tax-amount"
                            >
                                {{ item.displayExcludedTaxAmount | formatMoney }}
                            </div>
                            <div
                                class="product-tax-name"
                            >
                                {{ item.displayTaxRateName }}
                            </div>
                            <div
                                class="product-amount"
                            >
                                {{ item.displayTaxAmount }}
                            </div>
                        </div>
                    </div>
                    <div class="table-footer">
                        <div class="price-total">
                            <block-box
                                :top="2"
                                :left="52"
                                :font="9"
                            >
                                {{ digitUppercase(printData.invoiceFee) }}
                            </block-box>
                            <block-box
                                :top="2"
                                :left="145"
                                :font="9"
                            >
                                ¥ {{ printData.invoiceFee }}
                            </block-box>
                        </div>
                        <block-box
                            :top="8"
                            :left="7"
                            :font="9"
                        >
                            {{ printData.invoiceRemark }}
                        </block-box>
                    </div>
                </div>
            </div>
            <div class="footer">
                <block-box
                    :top="4"
                    :left="26"
                    :font="9"
                >
                    {{ printData.invoiceOpener }}
                </block-box>
            </div>
        </div>
    </div>
    </div>
</template>

<script>
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { digitUppercase, formatMoney } from './common/utils.js';
    import BlockBox from "./components/medical-bill/national-medical-bill/block-box.vue";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import CommonHandler from './data-handler/common-handler.js';
    import {formatDate} from '@tool/date';

    export default {
        name: "TaxFullDigitalInvoice",
        DataHandler: CommonHandler,
        components: {
            BlockBox
        },
        businessKey: PrintBusinessKeyEnum.TAX_FULL_DIGITAL_INVOICE,
        filters: {
            formatMoney
        }, 
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        pages: [
            {
                paper: PageSizeMap.MMA4_AUTO,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            printData() {
                return this.renderData.printData || null;
            },
            displayGoods() {
                return this.printData.displayGoods || [];
            },
            digitalInvoice() {
                return this.printData.digitalInvoice || null;
            },
            buyerName() {
                if(this.digitalInvoice) {
                    return this.digitalInvoice.buyerName || null;
                }
                return this.printData.buyerName;
            },
            buyerNo() {
                if(this.digitalInvoice) {
                    // 0 个人开票
                    return this.digitalInvoice.buyerType === 0 ?  this.digitalInvoice.idCard : this.digitalInvoice.buyerTaxNum;
                }
                return this.printData.buyerTaxNum;
            }
        },
        methods: {
            digitUppercase,
            formatDate
        }
    }
</script>

<style lang="scss">
.print-shudian-e-invoice-preview {
  position: relative;
  width: 100%;
  height: 100%;

  .invoice-wrapper {
    width: 711px;
    margin: 0 auto;
  }
  .table-wrapper {
    border: 1.5px solid #8D0000;
  }
  .header {
    height: 111px;
    position: relative;
    background-image: url("/static/assets/print/shudian-invoice/header.png");
    background-repeat: no-repeat;
    background-size: cover;
  }
  .table-wrapper {
    position: relative;
    border: 1.5px solid #8D0000;
  }
  .table-body {
    min-height: 200px;
    height: auto;
    position: relative;
  }
  .table-tr-item {
    display: flex;
    & + .table-tr-item {
      margin-top: 2mm;
    }
    > div {
      font-size: 10pt;
      box-sizing: border-box;
    }
    .tr-product-name {
      flex: 1;
      max-width: 37mm;
      padding-left: 2mm;
    }
    .product-spec,
    .product-unit{
      width: 20mm;
      min-width: 20mm;
      max-width: 20mm;
      text-align: center;
    }

    .product-num {
      width: 26mm;
      min-width: 26mm;
      max-width: 26mm;
      text-align: center;
    }
    .product-unit-price,
    .product-tax-amount {
      width: 17mm;
      min-width: 17mm;
      max-width: 17mm;
      text-align: right;
    }
    .product-unit-price {
      padding-right: 6mm;
    }
        
    .product-tax-name {
      width: 21mm;
      min-width: 21mm;
      max-width: 21mm;
      text-align: right;
    }
    
    .product-amount {
      width: 24mm;
      max-width: 24mm;
      min-width: 24mm;
      text-align: right;
      padding-right: 2mm;
    }
    
  }
  .footer {
    height: 47px;
    position: relative;
    background-image: url("/static/assets/print/shudian-invoice/buyer.png");
    background-repeat: no-repeat;
    background-size: cover;
  }
  .table-header {
    height: 97px;
    position: relative;
    background-image: url("/static/assets/print/shudian-invoice/table-header.png");
    background-repeat: no-repeat;
    background-size: contain;
  }
  .table-footer {
    height: 101px;
    position: relative;
    background-image: url("/static/assets/print/shudian-invoice/table-footer.png");
    background-repeat: no-repeat;
    background-size: cover;
  }
}

</style>
