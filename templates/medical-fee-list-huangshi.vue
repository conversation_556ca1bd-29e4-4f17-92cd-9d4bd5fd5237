<template>
    <div class="medical-fee-list-huangshi-wrapper">
        <template v-for="(page, pageIndex) in currentRenderPage">
            <div
                :key="`huangshi-content-${pageIndex}`"
                class="huangshi-content"
            >
                <div class="huangshi-hospital-code">
                    定点编码：{{ extraInfo.hospitalCode }}
                </div>
                
                <div class="huangshi-set-id">
                    单据号：{{ extraInfo.setlId }}
                </div>

                <!-- 医疗类别 -->
                <div class="huangshi-med-type">
                    {{ shebaoPayment.medType }}
                </div>
                
                <!-- 单位名称 -->
                <div class="huangshi-institution-name">
                    {{ huangshi.institutionName }}
                </div>
                
                <!-- 患者姓名 -->
                <div class="huangshi-patient-name">
                    {{ patient.name }}
                </div>
                
                <!-- 时间 -->
                <div class="huangshi-time">
                    {{ printData.chargedTime | parseTime }}
                </div>
                
                <!-- 医保编码 / 医保号 -->
                <div class="huangshi-card-id">
                    {{ shebaoPayment.cardId }}
                </div>
                
                <div class="huangshi-charge-item-wrapper">
                    <table class="huangshi-table">
                        <thead>
                            <tr class="huangshi-table-thead-tr">
                                <th class="huangshi-table-td huangshi-table-name">
                                    药名
                                </th>
                                <th class="huangshi-table-td huangshi-table-code">
                                    医保编码
                                </th>
                                <th class="huangshi-table-td huangshi-table-td-text-right huangshi-table-unit-price">
                                    单价
                                </th>
                                <th class="huangshi-table-td huangshi-table-td-text-right huangshi-table-unit-count">
                                    数量
                                </th>
                                <th class="huangshi-table-td huangshi-table-td-text-right huangshi-table-total-price">
                                    金额
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(item, itemIndex) in page.formItems"
                                :key="`huangshi-medicine-item-${itemIndex}`"
                                class="huangshi-table-tbody-tr"
                            >
                                <!-- 药名 -->
                                <td
                                    class="huangshi-table-td huangshi-table-name"
                                    overflow
                                >
                                    {{ item.name }}
                                </td>
                                <!-- 医保编码 -->
                                <td
                                    class="huangshi-table-td huangshi-table-code"
                                    overflow
                                >
                                    {{ item.socialCode }}
                                </td>
                                <!-- 单价 -->
                                <td
                                    class="huangshi-table-td huangshi-table-td-text-right huangshi-table-unit-price"
                                    overflow
                                >
                                    {{ item.discountedUnitPrice | formatMoney }}
                                </td>
                                <!-- 数量 -->
                                <td
                                    class="huangshi-table-td huangshi-table-td-text-right huangshi-table-unit-count"
                                    overflow
                                >
                                    {{ item.count }}{{ item.unit }}
                                </td>
                                <!-- 金额 -->
                                <td
                                    class="huangshi-table-td huangshi-table-td-text-right huangshi-table-total-price"
                                    overflow
                                >
                                    {{ item.discountedPrice | formatMoney }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 分页 -->
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                :key="`huangshi-new-page-${pageIndex}`"
                data-type="new-page"
            ></div>
        </template>
        
        <div class="huangshi-shebao-info-wrapper">
            <div
                v-for="(item, itemIndex) in shebaoInfoList"
                :key="itemIndex"
                class="huangshi-shabao-item"
            >
                {{ item.label }}：{{ item.value | formatMoney }}
            </div>
        </div>
    </div>
</template>

<script>
    import BillDataMixins from "./mixins/bill-data";
    import NationalBillData from "./mixins/national-bill-data";
    import CommonHandler from "./data-handler/common-handler";
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import clone from "./common/clone";
    import {formatMoney, parseTime} from "./common/utils";

    export default {
        name: 'MedicalFeeListHuangshi',
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_FEE_LIST_HUANGSHI,
        filters: {
            parseTime,
            formatMoney,
        },
        pages: [
            {
                paper: PageSizeMap.MM121_94,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        mixins: [BillDataMixins, NationalBillData],
        props: {
            extra: {
                type: Object,
                default() {
                    return {};
                }
            }
        },
        computed: {
            config() {
                return this.renderData.config.medicalListConfig || {};
            },
            huangshi() {
                return this.config.huangshi || {};
            },
            splitType() {
                return this.huangshi.splitType;
            },
            renderPage() {
                return this.spliceFormItems(this.chargeFormItems, 6);
            },
            isOnlyOnePage() {
                return this.splitType === 1 || this.extra.isPreview;
            },
            currentRenderPage() {
                return this.isOnlyOnePage ? this.renderPage.slice(0, 1) : this.renderPage;
            },
            // 医疗清单 0 只展示套餐  1 展示套餐加子项  2 只展示子项
            // 需要转换
            // 医疗票据 0 只展示套餐  1 只展示子项     2 展示套餐加子项
            composeChildrenConfig() {
                const composeChildren = this.huangshi.composeChildren ?? 0;
                switch (composeChildren) {
                    case 1:
                        return 2;
                    case 2:
                        return 1;
                    default:
                        return 0;
                }
            },
            shebaoInfoList() {
                return [
                    {
                        label: '费用合计',
                        value: this.finalFee,
                    },
                    {
                        label: '结前余额',
                        value: this.shebaoPayment.beforeCardBalance,
                    },
                    {
                        label: '结后余额',
                        value: this.shebaoPayment.cardBalance,
                    },
                    {
                        label: '个人账户',
                        value: this.shebaoPayment.accountPaymentFee,
                    },
                    {
                        label: '现金支付',
                        value: this.printData.personalPaymentFee,
                    },
                    {
                        label: '居民大病',
                        value: this.extraInfo.hifmiPay,
                    },
                    {
                        label: '统筹支付',
                        value: this.shebaoPayment.fundPaymentFee,
                    },
                    {
                        label: '公补支付',
                        value: this.extraInfo.cvlservPay,
                    },
                    {
                        label: '医疗救助',
                        value: this.extraInfo.mafPay,
                    },
                ];
            },
        },
        watch: {
            currentRenderPage: {
                handler(v) {
                    console.log('%c currentRenderPage\n', 'background: green; padding: 0 5px', clone(v));
                },
                immediate: true,
                deep: true,
            },
        },
    }
</script>

<style lang="scss">
.abc-page_preview {
    color: #2a82e4;
    background: url("/static/assets/print/huangshi.png");
    background-size: 121mm 94mm;
}

.medical-fee-list-huangshi-wrapper {
    font-size: 12px;
    line-height: 12px;

    .huangshi-content {
        height: 100%;
    }

    .huangshi-hospital-code {
        position: absolute;
        top: 4mm;
        left: 12mm;
    }

    .huangshi-set-id {
        position: absolute;
        top: 4mm;
        left: 66mm;
    }

    .huangshi-med-type {
        position: absolute;
        top: 16mm;
        left: 12mm;
    }

    .huangshi-institution-name {
        position: absolute;
        top: 22mm;
        left: 29mm;
        max-width: 37mm;
        overflow: hidden;
        white-space: nowrap;
    }

    .huangshi-patient-name {
        position: absolute;
        top: 27mm;
        left: 29mm;
    }

    .huangshi-time {
        position: absolute;
        top: 22mm;
        left: 81mm;
    }

    .huangshi-card-id {
        position: absolute;
        top: 27mm;
        left: 81mm;
    }

    .huangshi-charge-item-wrapper {
        position: absolute;
        top: 31mm;
        left: 12mm;
        width: 96mm;
        height: 41mm;
    }

    .huangshi-table {
        width: 100%;
        line-height: 21px;
        table-layout: fixed;
        border-collapse: collapse;

        th {
            font-weight: 400;
            text-align: left;
        }
    }

    .huangshi-table-thead-tr {
        border-top: 1px solid #000000;
        border-bottom: 1px solid #000000;
    }

    .huangshi-table-tbody-tr {
        border-bottom: 1px solid #000000;
    }

    .huangshi-table-td-text-right {
        text-align: right !important;
    }

    .huangshi-table-td {
        overflow: hidden;
        white-space: nowrap;
    }

    .huangshi-table-code {
        width: 80px;
    }

    .huangshi-table-unit-price {
        width: 50px;
    }

    .huangshi-table-unit-count {
        width: 50px;
    }

    .huangshi-table-total-price {
        width: 50px;
    }

    .huangshi-shebao-info-wrapper {
        position: absolute;
        top: 290px;
        left: 12mm;
        width: 96mm;
        height: 42px;
        line-height: 14px;
    }

    .huangshi-shabao-item {
        display: inline-block;
        width: 33%;
    }
}
</style>
