
<template>
    <gansu-medical-bill
        class="gansu-medical-bill-wrapper"
        :detail-top="38.5"
        :detail-height="62"
        :split-count="26"
        is-need-reset-compose-social-fee
        v-bind="$options.propsData"
    >
        <block-box
            :top="24"
            :left="39"
            :font="8"
        >
            {{ printData.serialNo }}
        </block-box>
        <block-box
            v-if="currentConfig && currentConfig.medicalOrganizationType"
            :top="24"
            :left="85"
            :font="8"
        >
            {{ organ.category }}
        </block-box>

        <block-box
            :top="30"
            :left="32"
            :font="8"
        >
            {{ patient.name }}
        </block-box>
        <block-box
            :top="30"
            :left="69"
            :font="8"
        >
            {{ patient.sex }}
        </block-box>
        <block-box
            :top="30"
            :left="95"
            :font="8"
        >
            {{ shebaoPayment.cardOwnerType }}
        </block-box>
        <block-box
            v-if="printData.healthCardNo"
            :top="30"
            :left="137"
            :font="8"
        >
            {{ printData.healthCardNo }}
        </block-box>

        <block-box
            :top="102"
            :left="44"
            :font="8"
        >
            {{ digitUppercase(finalFee) }}
        </block-box>

        <block-box
            :top="102"
            :left="118"
            :font="8"
        >
            {{ finalFee | formatMoney }}
        </block-box>
        <block-box
            :top="109.5"
            :left="44"
            :font="8"
        >
            {{ shebaoPayment.fundPaymentFee | formatMoney }}
        </block-box>

        <block-box
            :top="109.5"
            :left="79"
            :font="8"
        >
            {{ shebaoPayment.accountPaymentFee | formatMoney }}
        </block-box>

        <block-box
            :top="109.5"
            :left="114"
            :font="8"
        >
            {{ shebaoPayment.otherPaymentFee | formatMoney }}
        </block-box>

        <block-box
            :top="109.5"
            :left="148.5"
            :font="8"
        >
            {{ printData.personalPaymentFee | formatMoney }}
        </block-box>


        <block-box
            :top="115.5"
            :left="45"
            :font="8"
        >
            {{ currentConfig.institutionName }}
        </block-box>

        <block-box
            :top="115.5"
            :font="8"
            :left="93"
        >
            {{ printData.chargedByName }}
        </block-box>
        <block-box
            :top="115.5"
            :left="114"
            :font="8"
        >
            {{ year }}
        </block-box>
        <block-box
            :top="115.5"
            :left="126.5"
            :font="8"
        >
            {{ month }}
        </block-box>
        <block-box
            :top="115.5"
            :left="136"
            :font="8"
        >
            {{ day }}
        </block-box>
    </gansu-medical-bill>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, {MM190_127_GANSU, Orientation} from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import NationalBillData from "./mixins/national-bill-data";
    import GansuMedicalBill from './components/medical-bill/national-medical-bill/gansu.vue';
    export default {
        name: "MedicalBillGansu",
        components: {
            BlockBox,
            GansuMedicalBill,
        },
        mixins: [ BillDataMixins, NationalBillData ],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_GANSU,
        pages: [
            {
                paper: PageSizeMap.MM190_127_GANSU,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            gansu() {
                return this.config.gansu || {}
            },
            year() {
                if(this.printData.chargedTime) {
                    return new Date(this.printData.chargedTime).getFullYear();
                }
                return '';
            },
            month() {
                if(this.printData.chargedTime) {
                    return new Date(this.printData.chargedTime).getMonth() + 1;
                }
                return '';
            },
            day() {
                if(this.printData.chargedTime) {
                    return new Date(this.printData.chargedTime).getDate();
                }
                return '';
            },
            // 是否医保支付
            isShebaoPayment() {
                return Object.keys(this.shebaoPayment).length
            },
        },
        created() {
            this.initFee();
        },
    }
</script>
<style lang="scss">
* {
  padding: 0;
  margin: 0;
}

.gansu-medical-bill-wrapper {
  .institution-name {
    width: 36mm;
    word-wrap: break-word;
    word-break: break-all;
  }
  font-size: 8pt;
}

.abc-page_preview {
  background: url("/static/assets/print/gansu.png");
  background-size: 190mm 127mm;
  color: #2a82e4;
}
</style>
