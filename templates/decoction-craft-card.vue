<!--exampleData
{
"logo": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/basic/%E8%AF%8A%E6%89%80Logo%E6%B2%99%E5%9D%AA%E5%9D%9D_ah8qQyhlUdmr.jpg",
    "patient": {
        "id": "ffffffff00000000148859100c7e0000",
        "chainId": null,
        "name": "名字是个很长的名字",
        "namePy": null,
        "namePyFirst": null,
        "birthday": "2000-01-01",
        "mobile": "18030976151",
        "countryCode": null,
        "sex": "男",
        "idCard": null,
        "isMember": 1,
        "age": {
            "year": 24,
            "month": 8,
            "day": 17
        },
        "address": null,
        "sn": null,
        "remark": null,
        "profession": null,
        "company": null,
        "patientSource": null,
        "tags": null,
        "marital": null,
        "weight": null,
        "ethnicity": null,
        "wxOpenId": null,
        "wxHeadImgUrl": null,
        "wxNickName": null,
        "wxBindStatus": null,
        "isAttention": null,
        "appFlag": null,
        "pastHistory": null,
        "allergicHistory": null,
        "visitReason": null,
        "parentName": null,
        "consultantId": null,
        "dutyTherapistId": null,
        "primaryTherapistId": null,
        "shebaoCardInfo": null,
        "childCareInfo": null,
        "chronicArchivesInfo": null
    },
    "organ": {
        "id": "fff730ccc5ee45d783d82a85b8a0e52d",
        "name": "高新大原店",
        "shortName": "高新大原店",
        "addressProvinceName": null,
        "addressCityName": null,
        "addressDistrictName": null,
        "addressDetail": "湖北省黄石市大冶市大冶大道117号",
        "contactPhone": "0851-8511132",
        "logo": null,
        "category": null,
        "addressProvinceId": null,
        "addressCityId": null,
        "addressDistrictId": null,
        "hisType": 0,
        "medicalDocumentsTitle": null
    },
    "dispensingForms": [
        {
            "dispensingFormItems": [
                {
                    "name": "川楝子(炒制)",
                    "unit": "g",
                    "count": 1,
                    "doseCount": 3,
                    "specialRequirement": null,
                    "formatSpec": "",
                    "position": "",
                    "manufacturer": "四川中庸药业有限公司",
                    "productType": 1,
                    "productSubType": 2,
                    "sourceItemType": 0,
                    "usageInfo": {
                        "doseCount": 0,
                        "ivgtt": 0,
                        "processBagUnitCountDecimal": 0,
                        "checked": true
                    },
                    "receivedPrice": 0,
                    "dispensingFormItemBatches": [
                        {
                            "totalPrice": 11,
                            "sourceTotalPrice": 11,
                            "unitPrice": 11,
                            "unitCount": 1,
                            "batchInfo": {
                                "batchId": 100018424,
                                "pharmacyType": 0,
                                "pharmacyNo": 22,
                                "inDate": "2023-05-31T02:37:09Z",
                                "supplierId": "c232c1b431ad463697fe330edf63b8cf",
                                "supplierName": "药品规格修改",
                                "packageCostPrice": 11,
                                "pieceCount": 23,
                                "packageCount": 0,
                                "stockPieceCount": 0,
                                "stockPackageCount": 0,
                                "lockingPieceCount": 23,
                                "lockingPackageCount": 0,
                                "status": 0
                            }
                        },
                        {
                            "totalPrice": 0,
                            "sourceTotalPrice": 0,
                            "unitPrice": 0,
                            "unitCount": 2,
                            "batchInfo": {
                                "batchId": 100018426,
                                "pharmacyType": 0,
                                "pharmacyNo": 22,
                                "inDate": "2023-05-31T02:38:49Z",
                                "supplierId": "c232c1b431ad463697fe330edf63b8cf",
                                "supplierName": "药品规格修改",
                                "packageCostPrice": 0,
                                "pieceCount": 4,
                                "packageCount": 0,
                                "stockPieceCount": 1,
                                "stockPackageCount": 0,
                                "lockingPieceCount": 3,
                                "lockingPackageCount": 0,
                                "status": 0
                            }
                        }
                    ]
                }
            ],
            "sourceFormType": 6,
            "specification": "中药饮片",
            "doseCount": 3,
            "dailyDosage": "1日2剂",
            "usage": "煎服",
            "freq": "1日3次",
            "usageLevel": "每次150ml",
            "processUsageInfo": "机器煎药（普通），1剂1.5袋",
            "processUsage": "机器煎药（普通）",
            "dispensingFormId": "ffffffff0000000034d59edcbcbd4004",
            "pharmacyNo": 22,
            "pharmacyType": 0,
            "processBagUnitCount": 0,
            "processBagUnitCountDecimal": 1.5,
            "totalProcessCount": 2,
            "processRemark": null,
            "processedStatus": 1,
            "auditName": null,
            "auditId": null,
            "compoundName": null,
            "compoundId": null,
            "isCombineForm": 0,
            "receivedPrice": 0,
            "cMSpec": "中药饮片"
        }
    ],
    "chargedByName": "自助支付",
    "chargedTime": "2024-09-18T03:15:49Z",
    "sellerName": "彭磊",
    "dispensedByName": "",
    "dispensedTime": null,
    "auditName": null,
    "auditId": null,
    "compoundName": null,
    "compoundId": null,
    "isDecoction": true,
    "contactMobile": null,
    "doctorName": "彭磊",
    "patientOrderNo": "00028081",
    "netIncomeFee": 6,
    "deliveryInfo": {
        "id": "3807123730020499464",
        "chargeSheetId": "ffffffff0000000034d59e8a87d50000",
        "addressProvinceId": "510000",
        "addressProvinceName": "四川",
        "addressCityId": "510100",
        "addressCityName": "成都市",
        "addressDistrictId": "510105",
        "addressDistrictName": "青羊区",
        "addressDetail": "长虹大道未来城",
        "deliveryName": "李炜",
        "deliveryMobile": "18060976151",
        "deliveryOrderNo": null,
        "deliveryCompany": {
            "id": "3802967364528357376",
            "name": "ces"
        },
        "deliveryPayType": 0,
        "deliveryFee": 0,
        "deliveryTraceData": null
    },
    "departmentName": "小儿外科诊室",
    "diagnose": "慢性咽炎",
    "doctorAdvice": null,
    "barcode": "00028081-01",
    "pharmacyNo": 22,
    "pharmacyType": 0,
    "pharmacyName": "中药库1a",
    "openPharmacyFlag": 20,
    "processedStatus": 1,
    "deliveredStatus": 1,
    "isPatientSelfPay": 1,
    "receivedPrice": 0,
    "chargeTransactionTime": null
}
-->
<template>
    <div>
        <template v-for="(form, index) in dispensingForms">
            <div
                :key="form.id"
                class="print-decoction-craft-card"
            >
                <h2
                    class="print-decoction-craft-card__title"
                    :style="{justifyContent: printData.logo ? 'flex-start' : 'center' }"
                >
                    <img
                        v-if="printData.logo"
                        :src="printData.logo"
                        alt="logo"
                    />
                    济华中医馆煎药工艺卡
                </h2>
                <div class="print-decoction-craft-card__content">
                    <div class="print-decoction-craft-card__create-date">
                        <span>
                            <span class="print-decoction-craft-card__label">填表时间：</span>
                            <span class="print-decoction-craft-card__unit">年</span>
                            <span class="print-decoction-craft-card__unit">月</span>
                            <span class="print-decoction-craft-card__unit">日</span>
                        </span>
                    </div>
                    <print-row
                        v-for="(row, rowIndex) in formRows"
                        :key="rowIndex"
                        :gutter="16"
                    >
                        <print-col
                            v-for="(col, colIndex) in row"
                            :key="colIndex"
                            :span="col.span"
                        >
                            <span class="print-decoction-craft-card__label">{{ `${col.label}：` || '' }}</span>
                            <span
                                v-if="col.slot === 'doseCount'"
                                class="print-decoction-craft-card__slot"
                            >
                                <span class="print-decoction-craft-card__value">{{ form.doseCount || '' }}</span>
                                <span class="print-decoction-craft-card__unit">剂</span>
                                <span class="print-decoction-craft-card__value">{{ totalProcessCount(index) || '' }}</span>
                                <span class="print-decoction-craft-card__unit">袋</span>
                                <span class="print-decoction-craft-card__value"></span>
                                <span class="print-decoction-craft-card__unit">ml</span>
                            </span>
                            <span
                                v-else-if="col.slot === 'medicineCollectionTime'"
                                class="print-decoction-craft-card__slot"
                            >
                                <span class="print-decoction-craft-card__value">{{ formatDate.month || '' }}</span>
                                <span class="print-decoction-craft-card__unit">月</span>
                                <span class="print-decoction-craft-card__value">{{ formatDate.day || '' }}</span>
                                <span class="print-decoction-craft-card__unit">日</span>
                                <span class="print-decoction-craft-card__value">{{ formatDate.hour || '' }}</span>
                                <span class="print-decoction-craft-card__unit">时</span>
                            </span>
                            <span
                                v-else-if="col.slot === 'suffix'"
                                class="print-decoction-craft-card__slot"
                            >
                                <span class="print-decoction-craft-card__value">{{ col.value || '' }}</span>
                                <span class="print-decoction-craft-card__unit">{{ col.suffixText || '' }}</span>
                            </span>
                            <span
                                v-else-if="col.slot === 'processUsage'"
                                class="print-decoction-craft-card__value white-space-nowrap"
                            >
                                {{ form.processUsage || '' }}</span>
                            <span
                                v-else-if="col.slot === 'processRemark'"
                                class="print-decoction-craft-card__value"
                            >
                                {{ form.processRemark || '' }}
                            </span>
                            <span
                                v-else-if="col.slot === 'time'"
                                class="print-decoction-craft-card__slot-tims"
                            >
                                <span class="print-decoction-craft-card__slot">
                                    <span class="print-decoction-craft-card__time-label">开始:</span>
                                    <span class="print-decoction-craft-card__value">{{ col.value || '' }}</span>
                                    <span class="print-decoction-craft-card__time-label">时</span>
                                    <span class="print-decoction-craft-card__value">{{ col.value || '' }}</span>
                                    <span class="print-decoction-craft-card__time-label">分</span>
                                </span>
                                <span class="print-decoction-craft-card__slot">
                                    <span class="print-decoction-craft-card__time-label">结束:</span>
                                    <span class="print-decoction-craft-card__value">{{ col.value || '' }}</span>
                                    <span class="print-decoction-craft-card__time-label">时</span>
                                    <span class="print-decoction-craft-card__value">{{ col.value || '' }}</span>
                                    <span class="print-decoction-craft-card__time-label">分</span>
                                </span>
                                <span class="print-decoction-craft-card__slot">
                                    <span class="print-decoction-craft-card__time-label">共</span>
                                    <span class="print-decoction-craft-card__value">{{ col.value || '' }}</span>
                                    <span class="print-decoction-craft-card__time-label">分钟</span>
                                </span>
                            </span>
                            <span
                                v-else-if="col.slot === 'name'"
                                class="print-decoction-craft-card__value name-white-space-nowrap"
                            >{{ col.value || '' }}</span>
                            <span
                                v-else
                                class="print-decoction-craft-card__value"
                            >{{ col.value || '' }}</span>
                        </print-col>
                    </print-row>
                </div>
            </div>
            <div
                v-if="index !== dispensingForms.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>
<script>
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size";
    import PrintCommonHandler from './data-handler/print-handler.js';
    import PrintRow from "./components/layout/print-row.vue";
    import PrintCol from "./components/layout/print-col.vue";
    export default {
        name: 'DecoctionCraftCard',
        components: { PrintCol, PrintRow },
        DataHandler: PrintCommonHandler,
        businessKey: PrintBusinessKeyEnum.DECOCTION_CRAFT_CARD,
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        pages: [
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        computed: {
            printData() {
                console.log(this.renderData, 'this.renderData');

                return this.renderData.printData || {};
            },
            dispensingForms() {
                return this.printData.dispensingForms || [];
            },
            formatDate() {
                if(!this.printData.date){
                    return {};
                }
                const date = new Date(this.printData.date);
                return {
                    month: date.getMonth() + 1,
                    day: date.getDate(),
                    hour: date.getHours(),
                };
            },
            formRows() {
                return [
                    [
                        {
                            label: '姓名',
                            value: this.printData.patient?.name,
                            span: 8,
                            slot: 'name'
                        },
                        {
                            label: '卡号',
                            value: this.printData.patientOrderNo,
                            span: 8,
                        },
                        {
                            label: '填表人',
                            value: '',
                            span: 8,
                        },
                    ],
                    [
                        {
                            label: '联系方式',
                            value: this.printData.patient?.mobile,
                            span: 12,
                        },
                        {
                            label: '剂数',
                            slot: 'doseCount',
                            span: 12,
                        },
                    ],
                    [
                        {
                            label: '取药时间',
                            value: this.printData.date,
                            slot: 'medicineCollectionTime',
                            span: 12,
                        },
                        {
                            label: '煎药方式',
                            slot: 'processUsage',
                            span: 12,
                        },
                    ],
                    [
                        {
                            label: '备注',
                            slot: 'processRemark',
                            span: 24,
                        },
                    ],
                    [
                        {
                            label: '首次加水量',
                            slot: 'suffix',
                            suffixText: '毫升',
                            span: 12,
                        },
                        {
                            label: '二次加水量',
                            slot: 'suffix',
                            suffixText: '毫升',
                            span: 12,
                        },
                    ],
                    [
                        {
                            label: '浸泡时间',
                            slot: 'time',
                            span: 24,
                        },
                    ],
                    [
                        {
                            label: '先煎药品',
                            slot: 'time',
                            span: 24,
                        },
                    ],
                    [
                        {
                            label: '一煎时间',
                            slot: 'time',
                            span: 24,
                        },
                    ],
                    [
                        {
                            label: '后下药品',
                            slot: 'time',
                            span: 24,
                        },
                    ],
                    [
                        {
                            label: '二煎时间',
                            slot: 'time',
                            span: 24,
                        },
                    ],
                    [
                        {
                            label: '煎药温度',
                            slot: 'suffix',
                            suffixText: 'ºC',
                            span: 12,
                        },
                        {
                            label: '质量情况',
                            span: 12,
                        },
                    ],
                    [
                        {
                            label: '分装剂量',
                            span: 12,
                        },
                        {
                            label: '分装袋数',
                            slot: 'suffix',
                            suffixText: '袋',
                            span: 12,
                        },
                    ],
                    [
                        {
                            label: '特殊用法药品',
                            span: 12,
                        },
                        {
                            label: '备注',
                            span: 12,
                        },
                    ],
                    [
                        {
                            label: '泡药人',
                            span: 12,
                        },
                        {
                            label: '煎药人',
                            span: 12,
                        },
                    ],
                    [
                        {
                            label: '发药员',
                            span: 12,
                        },
                        {
                            label: '顾客签字',
                            span: 12,
                        },
                    ],
                ]
            }
        },
        methods: {
            totalProcessCount(index) {
                const dispensingFormItem = this.dispensingForms[index] || {};
                const processBagUnitCount = dispensingFormItem.processBagUnitCountDecimal ||dispensingFormItem.processBagUnitCount || 0;
                const doseCount = dispensingFormItem.doseCount || 0;
                return dispensingFormItem.totalProcessCount || processBagUnitCount ? Math.ceil(doseCount  / processBagUnitCount) : null;
            },
        }
    };
</script>
<style lang="scss">
@import './components/layout/print-layout.scss';

.print-decoction-craft-card {
    height: calc(100% - 80px);
    padding: calc(40px - 4mm) calc(50px - 4mm);
    font-family: Songti SC;
    font-size: 16px;

    .white-space-nowrap {
        white-space: nowrap;
    }

    .print-decoction-craft-card__content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: calc(100% - 62px);
    }

    &__title {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        font-size: 28px;
        font-weight: 700;
        letter-spacing: 0.04em;

        img {
            width: 90px;
            height: 42px;
            margin-right: 10px;
        }
    }

    .print-decoction-craft-card__create-date {
        text-align: right;

        & > span {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .print-decoction-craft-card__label + .print-decoction-craft-card__unit,
        .print-decoction-craft-card__unit + .print-decoction-craft-card__unit {
            margin-left: 16px;
        }
    }

    &__label {
        font-weight: 700;
        color: #000000;
        white-space: nowrap;
    }

    &__unit,
    &__time-label {
        font-size: 13px;
        line-height: 16px;
        color: #565656;
    }

    &__slot {
        display: flex;
        flex: 1;
        align-items: flex-end;

        &-tims {
            display: flex;
            flex: 1;
            justify-content: space-between;
            margin-left: 20px;

            .print-decoction-craft-card__slot + .print-decoction-craft-card__slot {
                margin-left: 28px;
            }
        }
    }

    .print-col {
        display: flex;
        align-items: flex-end;

        .print-decoction-craft-card__value {
            flex: 1;
            min-height: 16px;
            padding-left: 16px;
            border-bottom: 1px solid #565656;

            &.name-white-space-nowrap {
                min-width: 5em;
                max-width: 5em;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>
