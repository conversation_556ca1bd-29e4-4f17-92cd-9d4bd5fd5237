<!--exampleData

{
    "organName":"高新大源店",
    "patientName":"X",
    "sex":"男",
    "age":
        {
            "year":40,
            "month":0,
            "day":0
        },
    "doctorName":"刘喜"
}

-->

<template>
    <div>
        <div
            class="patient-tag-header"
            data-type="header"
        >
            <img
                v-if="config.barcode && barcodeSrc"
                class="barcode-image"
                :src="barcodeSrc"
                alt=""
            />
            <div
                v-else
                class="patient-tag-patient-organ"
            >
                {{ clinicName }}
            </div>
        </div>
        <div
            class="patient-tag-patient-info"
        >
            <div class="patient-tag-patient-info-name">
                {{ patientName }}
            </div>
            <div class="patient-tag-patient-info-other">
                {{ patientSex }}
            </div>
            <div class="patient-tag-patient-info-other">
                {{ formatAge(printData.age, { monthYear: 12, dayYear: 1 }) }}
            </div>
        </div>

        <div
            v-if="config.mobile"
            class="patient-tag-detail"
        >
            手机：{{ printData.mobile | filterMobileV2(config.mobileType) }}
        </div>
        <div
            v-if="config.birthday"
            class="patient-tag-detail"
        >
            生日：{{ printData.birthday }}
        </div>
        <div
            v-if="config.patientOrderId"
            class="patient-tag-detail"
        >
            诊号：{{ patientOrderNo }}
        </div>
        <div
            v-if="config.doctor"
            class="patient-tag-detail"
        >
            医生：{{ doctorName }}
        </div>
        <div
            v-if="config.printDate"
            class="patient-tag-detail"
        >
            {{ new Date() | parseTime('y-m-d h:i:s') }}
        </div>

        <div
            data-type="footer"
        ></div>
    </div>
</template>

<script>
    import CommonHandler from "./data-handler/common-handler.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";

    import { formatAge, parseTime, textToBase64BarCode } from "./common/utils.js";

    import PageSizeMap, { Orientation } from "../share/page-size.js";


    import { filterMobileV2 } from "./common/medical-transformat.js";

    export default {
        name: "PatientTag",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.PATIENT_TAG,
        pages: [
            {
                paper: PageSizeMap.TAG40_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG40_60,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_70,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_40,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG70_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG70_50,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG80_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG80_50,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        filters: {
            filterMobileV2,
            parseTime,
        },
        props: {
            renderData: {
                type: Object,
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            config() {
                return this.renderData.config.patientTag || { };
            },
            patientName() {
                return this.printData.patientName;
            },
            patientSex() {
                return this.printData.sex;
            },
            doctorName() {
                return this.printData.doctorName;
            },
            clinicName() {
                return this.config.title || this.printData.organName;
            },
            patientOrderNo() {
                return this.printData.patientOrder?.no ? `${this.printData.patientOrder.no}`.padStart(8, '0') : '';
            },
            barcodeSrc() {
                return textToBase64BarCode(this.patientOrderNo);
            },
        },
        methods: {
            formatAge,
        },
    }
</script>

<style lang="scss">
* {
    font-family: "Microsoft YaHei", "微软雅黑";
}

.patient-tag-header {
    box-sizing: content-box;
    height: 30pt;
    padding-top: 8pt;
    padding-left: 8pt;
    margin-bottom: 8pt;

    .barcode-image {
        display: block;
        width: 90pt;
        height: 30pt;
    }

    .patient-tag-patient-organ {
        font-size: 14pt;
        line-height: 14pt;
        word-break: keep-all;
        white-space: nowrap;
    }
}

.patient-tag-patient-info {
    padding-left: 8pt;
    margin-bottom: 8pt;

    .patient-tag-patient-info-name {
        display: inline-block;
        font-size: 18pt;
        font-weight: bold;
        line-height: 18pt;
        word-break: keep-all;
        white-space: nowrap;
        vertical-align: bottom;
    }

    .patient-tag-patient-info-other {
        display: inline-block;
        margin-left: 7pt;
        font-size: 14pt;
        line-height: 14pt;
        word-break: keep-all;
        white-space: nowrap;
        vertical-align: bottom;
    }
}

.patient-tag-detail {
    padding-left: 8pt;
    margin-bottom: 8pt;
    font-size: 14pt;
    line-height: 14pt;
    word-break: keep-all;
    white-space: nowrap;

    &:last-child {
        margin-bottom: 0;
    }
}

.patient-tag-patient-doctor {
    height: 24pt;
    padding-left: 8pt;
    font-size: 14pt;
    line-height: 24pt;
    word-break: keep-all;
    white-space: nowrap;

    &.patient-tag-patient-doctor-divide-line {
        border-top: 1pt solid #000000;
    }
}

[data-size=page_50mm×40mm],
[data-size=page_60mm×40mm],
[data-size=page_40mm×60mm],
[data-size=page_70mm×40mm],
[data-size=page_80mm×40mm] {
    .patient-tag-header {
        height: 20pt;
        padding-top: 6pt;
        padding-left: 6pt;
        margin-bottom: 7pt;

        .barcode-image {
            width: 90pt;
            height: 20pt;
        }

        .patient-tag-patient-organ {
            font-size: 12pt;
            line-height: 12pt;
        }
    }

    .patient-tag-patient-info {
        padding-left: 6pt;
        margin-bottom: 7pt;

        .patient-tag-patient-info-name {
            font-size: 16pt;
            line-height: 16pt;
        }

        .patient-tag-patient-info-other {
            margin-left: 2pt;
            font-size: 12pt;
            line-height: 12pt;
        }
    }

    .patient-tag-detail {
        padding-left: 6pt;
        margin-bottom: 7pt;
        font-size: 12pt;
        line-height: 12pt;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .patient-tag-patient-doctor {
        height: 20pt;
        padding-left: 6pt;
        font-size: 12pt;
        line-height: 20pt;
    }
}

[data-size=page_40mm×30mm],
[data-size=page_50mm×30mm],
[data-size=page_60mm×30mm] {
    .patient-tag-header {
        height: 15pt;
        padding-top: 4pt;
        padding-left: 4pt;
        margin-bottom: 4pt;

        .barcode-image {
            width: 90pt;
            height: 15pt;
        }

        .patient-tag-patient-organ {
            font-size: 10pt;
            line-height: 10pt;
        }
    }

    .patient-tag-patient-info {
        padding-left: 4pt;
        margin-bottom: 4pt;

        .patient-tag-patient-info-name {
            font-size: 14pt;
            line-height: 14pt;
        }

        .patient-tag-patient-info-other {
            margin-left: 3pt;
            font-size: 10pt;
            line-height: 10pt;
        }
    }

    .patient-tag-detail {
        padding-left: 4pt;
        margin-bottom: 4pt;
        font-size: 10pt;
        line-height: 10pt;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .patient-tag-patient-doctor {
        height: 16pt;
        padding-left: 4pt;
        font-size: 10pt;
        line-height: 16pt;
    }
}
</style>
