<template>
    <div class="order-shipment-wrapper">
        <div data-type="header">
            <div class="order-title">
                发货单
            </div>
        </div>
		
        <div data-type="mix-box">
            <table
                data-type="group"
                class="print-stock-table-wrapper shipment-order-table-wrapper"
            >
                <thead>
                    <tr class="table-title">
                        <td class="goods-name">
                            商品简称/名称
                        </td>
                        <td class="goods-spec">
                            规格简称/名称
                        </td>
                        <td class="unit-count">
                            数量
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(order, index) in detailOrders"
                        :key="order.id"
                        class="table-tr"
                        data-type="item"
                    >
                        <td class="goods-name">
                            {{ getGoodsInfo(order).goodsName || '' }}
                        </td>
					
                        <td class="goods-spec">
                            {{ getGoodsInfo(order).goodsSpec || '' }}
                        </td>
                        <td class="unit-count">
                            {{ getGoodsInfo(order).goodsCount || '' }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
		
        <div data-type="mix-box">
            <div data-type="group">
                <print-row
                    data-type="item"
                    class="order-info"
                >
                    <print-col :span="24">
                        订单编号：{{ displayOrderNo }}
                    </print-col>
                </print-row>
                <print-row
                    data-type="item"
                    class="order-info"
                >
                    <print-col :span="24">
                        支付时间：{{ orderInfo.payTime | parseTime('y-m-d h:i') }}
                    </print-col>
                </print-row>
                <print-row
                    v-if="printData.receiverNameEnable"
                    data-type="item"
                    class="order-info"
                >
                    <print-col :span="24">
                        收件人：{{ receiverInfo.receiverNameMask }}
                    </print-col>
                </print-row>
		        
                <print-row
                    v-if="printData.receiverPhoneEnable"
                    data-type="item"
                    class="order-info"
                >
                    <print-col :span="24">
                        收件人电话：{{ receiverInfo.receiverPhoneMask }}
                    </print-col>
                </print-row>
		        
                <print-row
                    v-if="printData.receiverAddressEnable"
                    data-type="item"
                    class="order-info"
                >
                    <print-col :span="24">
                        收件地址：{{ receiverInfo.receiverAddressMask }}
                    </print-col>
                </print-row>
              
                <print-row
                    v-if="printData.mallName"
                    data-type="item"
                    class="order-info"
                >
                    <print-col :span="24">
                        店铺名称：{{ printData.mallName }}
                    </print-col>
                </print-row>
              
                <print-row
                    v-if="printData.sellerPhone"
                    data-type="item"
                    class="order-info"
                >
                    <print-col :span="24">
                        卖家电话：{{ printData.sellerPhone }}
                    </print-col>
                </print-row>
              
                <print-row
                    v-if="sellerAddressStr"
                    data-type="item"
                    class="order-info"
                >
                    <print-col :span="24">
                        发货地址：{{ sellerAddressStr }}
                    </print-col>
                </print-row>
            </div>
        </div>
    </div>
</template>
<script>
    import PrintRow from "./components/layout/print-row.vue";
    import PrintCol from "./components/layout/print-col.vue";
    import CommonHandler from "./data-handler/common-handler";
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import { parseTime } from './common/utils.js';
	
    export default {
        name: 'OrderCloudShipment76',
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.ORDER_CLOUD_SHIPMENT_76,
        components: {PrintCol, PrintRow},
        pages: [
            {
                paper: PageSizeMap.MM76_130,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        filters: {
            parseTime
        },
        props: {
            options: {
                type: Object,
                default() {
                    return {}
                }
            },
            renderData: {
                type: Object,
                default: () => ({
                    printData: {},
                }),
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            orderInfo() {
                return this.printData.orderInfo || {}
            },
            detailOrders() {
                return this.orderInfo.detailOrders || []
            },
            displayOrderNo() {
                return this.detailOrders.map(it => it.orderNo).join('，')
            },
            receiverInfo() {
                return this.orderInfo.receiverInfo || {}
            },
            shipper() {
                return this.printData.shipper || {}
            },
            isA4() {
                return this.options.page?.size === 'A4';
            },
            sellerAddressStr() {
                if(!this.printData.sellerAddress) return ''
                const {
                    city,
                    detail,
                    district,
                    province,
                } = this.printData.sellerAddress.address;
                const _arr = [];
                if (province) {
                    _arr.push(`${province}`);
                }
                if (city) {
                    _arr.push(`${city}`);
                }
                if (district) {
                    _arr.push(district);
                }
                if (detail) {
                    _arr.push(detail);
                }
                return _arr.join('');
            },
          
        },
        methods: {
            getGoodsInfo(order) {
                const goods = order.goodsList[0]
                return goods || {}
            },
        }
    }
</script>

<style lang="scss">
	@import "./style/inventory-common.scss";
	.order-shipment-wrapper {
    padding-bottom: 20pt;

		.order-title {
			padding-bottom: 0;
		}
		.shipment-order-table-wrapper {
			margin-top: 10pt;
			.align-center {
				text-align: center;
			}
			.table-title {
				background: #F2F4F7;
			}
			td,
			th {
				padding: 4pt;
				font-size: 8pt;
			}
			
			.unit-count {
				width: 20%;
				padding: 6pt 2pt;
				text-align: center;
			}
			.goods-name,
			.goods-spec {
				width: 40%;
			}
		}
		.order-info .print-col{
			font-size: 8pt;
			line-height: 10pt;
		}
	}

</style>