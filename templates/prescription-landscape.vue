<!--exampleData
{
  "id": "ffffffff000000001d6f3f8806944000",
  "patient": {
    "id": "ffffffff000000001d6f9c200605e000",
    "name": "任盈盈",
    "namePy": "renyingying",
    "namePyFirst": "RYY",
    "birthday": "1999-12-15",
    "mobile": "13900000000",
    "sex": "女",
    "idCard": null,
    "isMember": 1,
    "age": {
      "year": 22,
      "month": 0,
      "day": 6
    },
    "address": null,
    "sn": "000881",
    "remark": null,
    "profession": null,
    "company": null,
    "patientSource": null,
    "tags": [],
    "marital": null,
    "weight": null,
    "wxOpenId": null,
    "wxHeadImgUrl": null,
    "wxNickName": null,
    "wxBindStatus": 0,
    "isAttention": 0,
    "shebaoCardInfo": null,
    "childCareInfo": null,
    "chronicArchivesInfo": null
  },
  "patientOrderNo": 6577,
  "organ": {
    "id": "9daeca0eab074c639892433ffef36f94",
    "name": "成都高新秉正堂中医门诊部连锁有限公司",
    "shortName": "成都高新秉正堂中医门诊部连锁有限公司",
    "addressDetail": "惠民佳苑北路东南侧西部122号别墅路110室从火车北站下车后，往南走10公里，左转第二个红绿灯路口右转，前方100米就到了",
    "contactPhone": "",
    "logo": "https://cis-images-test.oss-cn-shanghai.aliyuncs.com/headimg_test/jVlBR6MsFsWZ9Nm3ShdLbE8CazFmpcpD_1603681040002.png",
    "category": "妇幼保健院"
  },
  "departmentName": "",
  "diagnosis": "急性上呼吸道感染",
  "doctorAdvice": "1.饮食规律宜清淡，忌烟酒，忌辛辣荤腥<br>2.多喝水，保持身体充足水分<br>3.少吃油炸、腌制、生冷、麻辣等刺激性食物",
  "syndrome": "",
  "medicalRecord": {
    "chiefComplaint": "咳嗽，夜咳，咽痛，咽干",
    "pastHistory": "既往体健",
    "familyHistory": "否认家族遗传病史",
    "presentHistory": "①头痛头晕；②失眠多梦；③噩梦<br>已经持续5年，久病不治，多方寻医未果，尝试过中西医各种疗法",
    "physicalExamination": "扁桃体肿大，咽部充血，颈淋巴结肿大",
    "diagnosis": "急性上呼吸道感染",
    "doctorAdvice": "1.饮食规律宜清淡，忌烟酒，忌辛辣荤腥<br>2.多喝水，保持身体充足水分<br>3.少吃油炸、腌制、生冷、麻辣等刺激性食物",
    "syndrome": "",
    "therapy": "",
    "chineseExamination": "",
    "birthHistory": null,
    "oralExamination": "[{\"positions\":[{\"position\":\"top-left\",\"dataNo\":[\"5\"]}],\"describes\":[\"冷诊正常\"]},{\"positions\":[{\"position\":\"top-right\",\"dataNo\":[\"2\"]}],\"describes\":[\"残冠\"]}]",
    "epidemiologicalHistory": "{\"patientChecked\":true,\"attendantChecked\":false,\"suspiciousList\":[{\"label\":\"发热\",\"value\":\"无\"},{\"label\":\"干咳\",\"value\":\"无\"},{\"label\":\"乏力\",\"value\":\"无\"},{\"label\":\"鼻塞\",\"value\":\"无\"},{\"label\":\"流涕\",\"value\":\"无\"},{\"label\":\"咽痛\",\"value\":\"无\"},{\"label\":\"肌痛\",\"value\":\"无\"},{\"label\":\"腹泻\",\"value\":\"无\"},{\"label\":\"结膜炎\",\"value\":\"无\"},{\"label\":\"嗅觉味觉减退\",\"value\":\"无\"}],\"symptomList\":[{\"label\":\"患者可疑症状排查：\",\"isSuspicious\":true}]}",
    "auxiliaryExamination": null,
    "obstetricalHistory": "[{\"type\":\"pregnant\",\"birthCount\":1,\"pregnantCount\":1},{\"type\":\"menstruation\",\"menophaniaAge\":13,\"menstruationDays\":[5],\"menstrualCycle\":[28],\"menopauseTab\":1,\"menopauseDate\":\"2021-12-19\",\"menopauseAge\":\"\"}]",
    "chinesePrescription": null,
    "dentistryExaminations": null,
    "dentistryDiagnosisInfos": null,
    "treatmentPlans": null,
    "disposals": null
  },
  "healthCardNo": null,
  "healthCardPayLevel": null,
  "doctorName": "徐彩云",
  "doctorSignImgUrl": null,
  "diagnosedDate": "2021-12-15T03:41:58Z",
  "totalPrice": 1074.2300,
  "medicineTotalPrice": 171.23,
  "dispensedBy": null,
  "dispensedByName": null,
  "auditBy": null,
  "auditName": null,
  "revisitStatus": null,
  "barcode": null,
  "shebaoCardInfo": null,
  "productForms": [
    {
      "id": "ffffffff000000001d6fb8b006942000",
      "sort": 0,
      "sourceFormType": 2,
      "productFormItems": [
        {
          "id": "ffffffff000000001d6fb8b006942001",
          "productId": "ffffffff000000001d6fa16806a8c000",
          "name": "超敏C反应蛋白测定",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 20.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 20.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 0,
          "type": 3,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942002",
          "productId": "ffffffff000000001d6fa0e806a8c000",
          "name": "全血细胞分析",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 20.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 20.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 1,
          "type": 3,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942003",
          "productId": "5ad7300943824a78b353e74d97f682da",
          "name": "肺炎支原体抗体",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 50.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 50.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 2,
          "type": 3,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        }
      ]
    },
    {
      "id": "ffffffff000000001d6fb8b006942004",
      "sort": 1,
      "sourceFormType": 3,
      "productFormItems": [
        {
          "id": "ffffffff000000001d6fb8b006942005",
          "productId": "ffffffff000000001d70afa006a94000",
          "name": "骨伤、颈腰整脊手法",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 100.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 100.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 0,
          "type": 4,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942006",
          "productId": "455311173fe3484da26d6e45d85bf192",
          "name": "三位一体单次",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 168.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 168.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 1,
          "type": 4,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942007",
          "productId": "816f47cb74f343979e31f6acfb025805",
          "name": "穴位埋线（普通）",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 35.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 35.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 2,
          "type": 4,
          "subType": 2,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        }
      ]
    },
    {
      "id": "ffffffff000000001d70a9b006942000",
      "sort": 2,
      "sourceFormType": 11,
      "productFormItems": [
        {
          "id": "ffffffff000000001d70a9b006942001",
          "productId": "ffffffff000000001d70a5b806a96000",
          "name": "综合调养套餐",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 500.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 500.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 0,
          "type": 11,
          "subType": 1,
          "composeType": 1,
          "composeParentFormItemId": null,
          "composeChildren": [
            {
              "id": "ffffffff000000001d70a9b006942002",
              "productId": "17f37531a01a4471a76b4a1dc7e45738",
              "name": "穴位贴敷",
              "unitCount": 5.000,
              "unit": "次",
              "unitPrice": 20.0000,
              "fractionPrice": 0.0000,
              "sourceUnitPrice": null,
              "expectedUnitPrice": null,
              "useDismounting": 0,
              "sort": 0,
              "type": 4,
              "subType": 2,
              "composeType": 2,
              "composeParentFormItemId": "ffffffff000000001d70a9b006942001",
              "composeChildren": [],
              "chargeStatus": 1,
              "days": 1,
              "remark": null
            },
            {
              "id": "ffffffff000000001d70a9b006942003",
              "productId": "c49fb73100694d84b78ff57bed7bdd2f",
              "name": "电针灸",
              "unitCount": 5.000,
              "unit": "次",
              "unitPrice": 60.0000,
              "fractionPrice": 0.0000,
              "sourceUnitPrice": null,
              "expectedUnitPrice": null,
              "useDismounting": 0,
              "sort": 1,
              "type": 4,
              "subType": 1,
              "composeType": 2,
              "composeParentFormItemId": "ffffffff000000001d70a9b006942001",
              "composeChildren": [],
              "chargeStatus": 1,
              "days": 1,
              "remark": null
            },
            {
              "id": "ffffffff000000001d70a9b006942004",
              "productId": "ba9379e2403442788cacb63927083fd5",
              "name": "推拿",
              "unitCount": 5.000,
              "unit": "次",
              "unitPrice": 20.0000,
              "fractionPrice": 0.0000,
              "sourceUnitPrice": null,
              "expectedUnitPrice": null,
              "useDismounting": 0,
              "sort": 2,
              "type": 4,
              "subType": 1,
              "composeType": 2,
              "composeParentFormItemId": "ffffffff000000001d70a9b006942001",
              "composeChildren": [],
              "chargeStatus": 1,
              "days": 1,
              "remark": null
            }
          ],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        }
      ]
    }
  ],
  "prescriptionChineseForms": [
    {
      "id": "ffffffff000000001d6fb8b006942008",
      "type": 3,
      "specification": "中药饮片",
      "doseCount": 2,
      "dailyDosage": "1日1剂",
      "usage": "煎服",
      "freq": "1日3次",
      "requirement": "饭后1小时服用",
      "usageLevel": "每次150ml",
      "sort": 0,
      "isDecoction": true,
      "usageType": 1,
      "usageSubType": 1,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": "",
      "auditBy": null,
      "auditName": null,
      "contactMobile": "13900000000",
      "totalPrice": 8.52,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001d6fb8b00694200e",
          "goodsId": "4ec6d19a113d4a47adcd747565c10c3f",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "桔梗",
          "name": "桔梗",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.1000,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.1000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "4ec6d19a113d4a47adcd747565c10c3f",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "桔梗",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "桔梗",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.1,
            "packagePrice": 0.1,
            "packageCostPrice": 0.029,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 10002,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.029,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-01-30T08:15:40Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.1,
            "chainPiecePrice": 0.1,
            "chainPackageCostPrice": 0.05,
            "pieceCount": 10002,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.029,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.60000,
          "sourceTotalPrice": 0.60000
        },
        {
          "id": "ffffffff000000001d6fb8b00694200f",
          "goodsId": "f0fe317883274b5480cd6ead5d996a9b",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "黄芩",
          "name": "黄芩",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.2000,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.2000,
          "costUnitPrice": 0.0000,
          "sort": 6,
          "groupId": null,
          "productInfo": {
            "id": "f0fe317883274b5480cd6ead5d996a9b",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "黄芩",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "黄芩",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0.2,
            "packagePrice": 0.2,
            "packageCostPrice": 0.046,
            "inTaxRat": 1E+1,
            "outTaxRat": 2E+1,
            "stockPieceCount": 10012,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.046,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-02-12T00:46:00Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.2,
            "chainPiecePrice": 0.2,
            "chainPackageCostPrice": 0.046,
            "pieceCount": 10012,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 0.046,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 1.20000,
          "sourceTotalPrice": 1.20000
        },
        {
          "id": "ffffffff000000001d6fb8b006942010",
          "goodsId": "25d927bbeabf447fbb17606b07bd5b2b",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "知母",
          "name": "知母",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.1200,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.1200,
          "costUnitPrice": 0.0000,
          "sort": 7,
          "groupId": null,
          "productInfo": {
            "id": "25d927bbeabf447fbb17606b07bd5b2b",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "知母",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "知母",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.12,
            "packagePrice": 0.12,
            "packageCostPrice": 0.05,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 10002,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.05,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-01-30T07:58:34Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.12,
            "chainPiecePrice": 0.12,
            "chainPackageCostPrice": 0.06,
            "pieceCount": 10002,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.05,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.72000,
          "sourceTotalPrice": 0.72000
        },
        {
          "id": "ffffffff000000001d6fb8b006942011",
          "goodsId": "526b603ff4f94f9da0c05a5ea59fbc50",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "赤芍",
          "name": "赤芍",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.1300,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.1300,
          "costUnitPrice": 0.0000,
          "sort": 8,
          "groupId": null,
          "productInfo": {
            "id": "526b603ff4f94f9da0c05a5ea59fbc50",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "赤芍",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "赤芍",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.13,
            "packagePrice": 0.13,
            "packageCostPrice": 0.0251,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 10002,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.0251,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-04-10T15:15:43Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.13,
            "chainPiecePrice": 0.13,
            "chainPackageCostPrice": 0.065,
            "pieceCount": 10002,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.0251,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.78000,
          "sourceTotalPrice": 0.78000
        },
        {
          "id": "ffffffff000000001d6fb8b006942012",
          "goodsId": "3cbf9ec1576247719483ff6d34bcb142",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "玄参",
          "name": "玄参",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.0500,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.0500,
          "costUnitPrice": 0.0000,
          "sort": 9,
          "groupId": null,
          "productInfo": {
            "id": "3cbf9ec1576247719483ff6d34bcb142",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "玄参",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "玄参",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.05,
            "packagePrice": 0.05,
            "packageCostPrice": 0.017,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 1.001E+4,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.017,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "dd59fb0a539348f58df2e6f671a6164c",
            "lastModifiedUserId": "dd59fb0a539348f58df2e6f671a6164c",
            "lastModifiedDate": "2019-03-06T09:32:07Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.05,
            "chainPiecePrice": 0.05,
            "chainPackageCostPrice": 0.017,
            "pieceCount": 1.001E+4,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.017,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.30000,
          "sourceTotalPrice": 0.30000
        },
        {
          "id": "ffffffff000000001d6fb8b006942013",
          "goodsId": "15ac34381a9b448282dd2c1f95c2823c",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "连翘",
          "name": "连翘",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.1100,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.1100,
          "costUnitPrice": 0.0000,
          "sort": 10,
          "groupId": null,
          "productInfo": {
            "id": "15ac34381a9b448282dd2c1f95c2823c",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "连翘",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "连翘",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.11,
            "packagePrice": 0.11,
            "packageCostPrice": 0.064,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 9832,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.064,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-01-25T02:40:46Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.11,
            "chainPiecePrice": 0.11,
            "chainPackageCostPrice": 0.064,
            "pieceCount": 9832,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.064,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.66000,
          "sourceTotalPrice": 0.66000
        }
      ],
      "processUsageInfo": "煎药 - 人工煎药 - 1剂煎3袋",
      "cMSpec": "中药饮片"
    }
  ],
  "prescriptionWesternForms": [
    {
      "id": "ffffffff000000001d6f9c2806944000",
      "type": 1,
      "specification": "",
      "doseCount": 1,
      "dailyDosage": "",
      "usage": "",
      "freq": "",
      "requirement": "",
      "usageLevel": "",
      "sort": 0,
      "isDecoction": false,
      "usageType": null,
      "usageSubType": null,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": null,
      "auditBy": null,
      "auditName": null,
      "contactMobile": null,
      "totalPrice": 83.67,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001d6f9c2806944001",
          "goodsId": "ffffffff000000001d6f606006a8e000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿奇霉素颗粒",
          "name": "阿奇霉素颗粒",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "0.1",
          "dosageUnit": "g",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 1.6667,
          "useDismounting": 1,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "包",
          "unitPrice": 1.6667,
          "costUnitPrice": 0.0000,
          "sort": 0,
          "groupId": 1,
          "productInfo": {
            "id": "ffffffff000000001d6f606006a8e000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "阿奇霉素颗粒",
            "displaySpec": "0.1g*6包/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 6,
            "pieceUnit": "包",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "阿奇霉素颗粒",
            "medicineDosageNum": 0.1,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": "",
            "piecePrice": 1.6667,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:05Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 1.6667,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 1.66670,
          "sourceTotalPrice": 1.66670
        },
        {
          "id": "ffffffff000000001d6f9c2806944002",
          "goodsId": "f0d781e0399d4dacada32773bacf75f6",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "氨溴特罗口服溶液",
          "name": "氨溴特罗口服溶液(易坦静)",
          "specification": "",
          "manufacturer": "北京韩美",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "5",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 28.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 28.0000,
          "costUnitPrice": 0.0000,
          "sort": 1,
          "groupId": 1,
          "productInfo": {
            "id": "f0d781e0399d4dacada32773bacf75f6",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "易坦静",
            "displayName": "氨溴特罗口服溶液 (易坦静)",
            "displaySpec": "60ml*60ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "北京韩美",
            "pieceNum": 6E+1,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "氨溴特罗口服溶液",
            "medicineNmpn": "H20040317",
            "medicineDosageNum": 6E+1,
            "medicineDosageUnit": "ml",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0,
            "packagePrice": 28,
            "packageCostPrice": 1,
            "inTaxRat": 1E+1,
            "outTaxRat": 2E+1,
            "stockPieceCount": 0,
            "stockPackageCount": 1.1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-01-29T09:28:43Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20389,
            "chainPackagePrice": 28,
            "chainPiecePrice": 0,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1.1E+2,
            "manufacturerFull": "北京韩美药品有限公司",
            "medicineDosageForm": "溶液剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 28.00000,
          "sourceTotalPrice": 28.00000
        },
        {
          "id": "ffffffff000000001d6f9c2806944003",
          "goodsId": "ffffffff000000001d6f5ed806a8e000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 3,
          "medicineCadn": "四季抗病毒合剂",
          "name": "四季抗病毒合剂",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "5",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 10.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 10.0000,
          "costUnitPrice": 0.0000,
          "sort": 2,
          "groupId": 1,
          "productInfo": {
            "id": "ffffffff000000001d6f5ed806a8e000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "四季抗病毒合剂",
            "displaySpec": "120ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 16,
            "type": 1,
            "subType": 3,
            "pieceNum": 1.2E+2,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 1,
            "medicineCadn": "四季抗病毒合剂",
            "extendSpec": "",
            "position": "",
            "piecePrice": 0.0833,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 7,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:10Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 0.0833,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001d6f9c2806944004",
          "goodsId": "ffffffff000000001d6f5a1806a8c000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "小柴胡颗粒",
          "name": "小柴胡颗粒",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "tid",
          "dosage": "1",
          "dosageUnit": "包",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 10.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 10.0000,
          "costUnitPrice": 0.0000,
          "sort": 3,
          "groupId": 2,
          "productInfo": {
            "id": "ffffffff000000001d6f5a1806a8c000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "小柴胡颗粒",
            "displaySpec": "10g*10包/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 1E+1,
            "pieceUnit": "包",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "小柴胡颗粒",
            "medicineDosageNum": 1E+1,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": "",
            "piecePrice": 1,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:19Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 1,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001d6f9c2806944005",
          "goodsId": "ffffffff000000001d6f5b4806a8e000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "感冒灵颗粒(999)",
          "name": "感冒灵颗粒(999)",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "tid",
          "dosage": "1",
          "dosageUnit": "包",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 10.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 10.0000,
          "costUnitPrice": 0.0000,
          "sort": 4,
          "groupId": 2,
          "productInfo": {
            "id": "ffffffff000000001d6f5b4806a8e000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "感冒灵颗粒(999)",
            "displaySpec": "10g*9包/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 9,
            "pieceUnit": "包",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "感冒灵颗粒(999)",
            "medicineDosageNum": 1E+1,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": "",
            "piecePrice": 1.1111,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:14Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 1.1111,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001da6940006958000",
          "goodsId": "bccd2d2a2576480db2e7b1982742463a",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿莫西林分散片",
          "name": "阿莫西林分散片(阿赫林)",
          "specification": "",
          "manufacturer": "石药集团",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "片",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 24.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 24.0000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "bccd2d2a2576480db2e7b1982742463a",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "阿赫林",
            "displayName": "阿莫西林分散片 (阿赫林)",
            "displaySpec": "0.5g*13片/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "barCode": "6936292110261",
            "manufacturer": "石药集团",
            "pieceNum": 13,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "阿莫西林分散片",
            "medicineNmpn": "H20046510",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 24,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 9,
            "stockPackageCount": 3E+1,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "936944bc62d04d65ab8239e30a85e5f6",
            "lastModifiedDate": "2021-11-19T06:53:01Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20384,
            "chainPackagePrice": 24,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 9,
            "packageCount": 3E+1,
            "manufacturerFull": "石药集团中诺药业(石家庄)有限公司",
            "medicineDosageForm": "片剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 24.00000,
          "sourceTotalPrice": 24.00000
        }
      ],
      "processUsageInfo": "",
      "cMSpec": null
    },
    {
      "id": "ffffffff000000001da65c8806956000",
      "type": 1,
      "specification": "",
      "doseCount": 1,
      "dailyDosage": "",
      "usage": "",
      "freq": "",
      "requirement": "",
      "usageLevel": "",
      "sort": 1,
      "isDecoction": false,
      "usageType": null,
      "usageSubType": null,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": null,
      "auditBy": null,
      "auditName": null,
      "contactMobile": null,
      "totalPrice": 0.03,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001da65c8806956001",
          "goodsId": "fac01ed90db048d1997975a60123473b",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "卤米松乳膏",
          "name": "卤米松乳膏(澳能)",
          "specification": "",
          "manufacturer": "澳美制药",
          "ast": 0,
          "usage": "外用",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "支",
          "days": 3,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.0100,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 3.000,
          "unit": "支",
          "unitPrice": 0.0100,
          "costUnitPrice": 0.0000,
          "sort": 0,
          "groupId": null,
          "productInfo": {
            "id": "fac01ed90db048d1997975a60123473b",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "澳能",
            "displayName": "卤米松乳膏 (澳能)",
            "displaySpec": "5g*支/支",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "澳美制药",
            "pieceNum": 1,
            "pieceUnit": "支",
            "packageUnit": "支",
            "dismounting": 0,
            "medicineCadn": "卤米松乳膏",
            "medicineNmpn": "HC20150049",
            "medicineDosageNum": 5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 0.01,
            "packageCostPrice": 13,
            "inTaxRat": 1E+1,
            "outTaxRat": 2E+1,
            "stockPieceCount": 0,
            "stockPackageCount": 6,
            "lastPackageCostPrice": 13,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "fdd7cbf2524e4c929dbb99f14f04f178",
            "lastModifiedDate": "2021-03-30T11:49:43Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20388,
            "chainPackagePrice": 0.01,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 6,
            "manufacturerFull": "澳美制药厂",
            "medicineDosageForm": "膏剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 13,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.03000,
          "sourceTotalPrice": 0.03000
        }
      ],
      "processUsageInfo": "",
      "cMSpec": null
    }
  ],
  "prescriptionInfusionForms": [
    {
      "id": "ffffffff000000001d6f9c2806944006",
      "type": 2,
      "specification": "",
      "doseCount": 1,
      "dailyDosage": "",
      "usage": "",
      "freq": "",
      "requirement": "",
      "usageLevel": "",
      "sort": 0,
      "isDecoction": false,
      "usageType": null,
      "usageSubType": null,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": null,
      "auditBy": null,
      "auditName": null,
      "contactMobile": null,
      "totalPrice": 79.01,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001da65a7806956000",
          "goodsId": "ffffffff000000001c0a8b18067c8000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "氯化钠注射液9%",
          "name": "氯化钠注射液9%(双鹤)",
          "specification": "",
          "manufacturer": "四川科伦",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "250",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 3.0099,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 3.0099,
          "costUnitPrice": 0.0000,
          "sort": 0,
          "groupId": 1,
          "productInfo": {
            "id": "ffffffff000000001c0a8b18067c8000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "双鹤",
            "displayName": "氯化钠注射液9% (双鹤)",
            "displaySpec": "2.25g*250ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "四川科伦",
            "pieceNum": 2.5E+2,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "氯化钠注射液9%",
            "medicineNmpn": "H51021157",
            "medicineDosageNum": 2.25,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": null,
            "piecePrice": null,
            "packagePrice": 3.0099,
            "packageCostPrice": 2,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 2E+1,
            "lastPackageCostPrice": 2,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff0000000015549900040fc000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:12:26Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 3.0099,
            "chainPackageCostPrice": 2,
            "pieceCount": 0,
            "packageCount": 2E+1,
            "manufacturerFull": "四川科伦药业股份有限公司",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 2,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 3.00990,
          "sourceTotalPrice": 3.00990
        },
        {
          "id": "ffffffff000000001da65a7806956001",
          "goodsId": "217886ad6a554d8aba492affc786c65e",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "克林霉素磷酸酯注射液",
          "name": "克林霉素磷酸酯注射液(森迪)",
          "specification": "",
          "manufacturer": "山东方明药业",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "1.2",
          "dosageUnit": "g",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 7.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 4.000,
          "unit": "支",
          "unitPrice": 7.0000,
          "costUnitPrice": 0.0000,
          "sort": 1,
          "groupId": 1,
          "productInfo": {
            "id": "217886ad6a554d8aba492affc786c65e",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "森迪",
            "displayName": "克林霉素磷酸酯注射液 (森迪)",
            "displaySpec": "0.3g*2ml/支",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "山东方明药业",
            "pieceNum": 2,
            "pieceUnit": "ml",
            "packageUnit": "支",
            "dismounting": 0,
            "medicineCadn": "克林霉素磷酸酯注射液",
            "medicineNmpn": "H20045520",
            "medicineDosageNum": 0.3,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": null,
            "packagePrice": 7,
            "packageCostPrice": 0.71,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+1,
            "lastPackageCostPrice": 0.71,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:13:54Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 7,
            "chainPackageCostPrice": 3.5,
            "pieceCount": 0,
            "packageCount": 1E+1,
            "manufacturerFull": "山东方明药业集团股份有限公司",
            "medicineDosageForm": "注射剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.71,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 28.00000,
          "sourceTotalPrice": 28.00000
        },
        {
          "id": "ffffffff000000001da65a7806956002",
          "goodsId": "bd62c0ef60924a468590cd3b5b8e717f",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "葡萄糖氯化钠注射液",
          "name": "葡萄糖氯化钠注射液(科伦)",
          "specification": "",
          "manufacturer": "四川科伦",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "500",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 5.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 2.000,
          "unit": "瓶",
          "unitPrice": 5.0000,
          "costUnitPrice": 0.0000,
          "sort": 2,
          "groupId": 2,
          "productInfo": {
            "id": "bd62c0ef60924a468590cd3b5b8e717f",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "科伦",
            "displayName": "葡萄糖氯化钠注射液 (科伦)",
            "displaySpec": "12.5g*250ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "四川科伦",
            "pieceNum": 2.5E+2,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "葡萄糖氯化钠注射液",
            "medicineNmpn": "H51020630",
            "medicineDosageNum": 12.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": null,
            "packagePrice": 5,
            "packageCostPrice": 1.2,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+1,
            "lastPackageCostPrice": 1.2,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:14:18Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 5,
            "chainPackageCostPrice": 2.5,
            "pieceCount": 0,
            "packageCount": 1E+1,
            "manufacturerFull": "四川科伦药业股份有限公司",
            "medicineDosageForm": "注射剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1.2,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001da65a7806956003",
          "goodsId": "0bb6e3b137664f8e92d5931e6df4ceb2",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "维生素C注射液",
          "name": "维生素C注射液",
          "specification": "",
          "manufacturer": "西南药业",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "15",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 1.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 8.000,
          "unit": "支",
          "unitPrice": 1.0000,
          "costUnitPrice": 0.0000,
          "sort": 3,
          "groupId": 2,
          "productInfo": {
            "id": "0bb6e3b137664f8e92d5931e6df4ceb2",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "维生素C注射液",
            "displaySpec": "0.5g*2ml/支",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "西南药业",
            "pieceNum": 2,
            "pieceUnit": "ml",
            "packageUnit": "支",
            "dismounting": 0,
            "medicineCadn": "维生素C注射液",
            "medicineNmpn": "H50021469",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0,
            "packagePrice": 1,
            "packageCostPrice": 0.27,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+1,
            "lastPackageCostPrice": 0.27,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "912d9293bfe94ba8a4857c29debf7a98",
            "lastModifiedUserId": "912d9293bfe94ba8a4857c29debf7a98",
            "lastModifiedDate": "2019-02-11T09:09:41Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 1,
            "chainPiecePrice": 0,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+1,
            "manufacturerFull": "西南药业股份有限公司",
            "medicineDosageForm": "注射剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.27,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 8.00000,
          "sourceTotalPrice": 8.00000
        },
        {
          "id": "ffffffff000000001da65a7806956004",
          "goodsId": "ffffffff000000001d6f957006a8c000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "维生素B6注射液",
          "name": "维生素B6注射液(科伦)",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "100",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 30.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 30.0000,
          "costUnitPrice": 0.0000,
          "sort": 4,
          "groupId": 2,
          "productInfo": {
            "id": "ffffffff000000001d6f957006a8c000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "科伦",
            "displayName": "维生素B6注射液 (科伦)",
            "displaySpec": "100ml*瓶/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 1,
            "pieceUnit": "瓶",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "维生素B6注射液",
            "medicineDosageNum": 1E+2,
            "medicineDosageUnit": "ml",
            "extendSpec": "",
            "position": "",
            "piecePrice": null,
            "packagePrice": 3E+1,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 9.999999E+7,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:14:56Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 3E+1,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 0,
            "packageCount": 9.999999E+7,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 30.00000,
          "sourceTotalPrice": 30.00000
        }
      ],
      "processUsageInfo": "",
      "cMSpec": null
    }
  ],
  "prescriptionExternalForms": []
}
-->


<template>
    <div>
        <template v-for="(form, formIndex) in printData.prescriptionWesternForms">
            <outpatient-header
                data-type="header"
                :pharmacy-name="form.pharmacyName"
                :print-data="printData"
                :config="config"
                :organ-title="organTitle"
                print-title="处方笺"
                :print-jinma="form && form.psychotropicNarcoticType"
                :show-express="!!form.deliveryInfo && contentConfig.expressInfo"
                :data-pendants-index="`${formIndex}WP`"
            ></outpatient-header>
            <template v-if="form.isStandardForm">
                <template v-for="(formItems, formItemsIndex) in form.prescriptionFormItems">
                    <western-item
                        v-for="formItem in formItems"
                        is-landscape
                        :config="config"
                        :show-group-icon="checkExistedGroupId(form)"
                        :form-item="formItem"
                        :print-data="printData"
                    ></western-item>
                    <div
                        v-if="form.prescriptionFormItems.length - 1 !== formItemsIndex"
                        data-type="new-page"
                    ></div>
                </template>
            </template>
            <template v-else>
                <western-item
                    v-for="formItem in form.prescriptionFormItems"
                    is-landscape
                    :show-group-icon="checkExistedGroupId(form)"
                    :config="config"
                    :form-item="formItem"
                    :print-data="printData"
                ></western-item>
            </template>

            <template v-if="contentConfig.expressInfo && form.deliveryInfo">
                <delivery-info :delivery-info="form.deliveryInfo"></delivery-info>
            </template>

            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice :doctor-advice="doctorAdvice"></doctor-advice>
            </template>

            <next-is-blank v-if="!(contentConfig.expressInfo && form.deliveryInfo) && !(contentConfig.doctorAdvice && doctorAdvice.length)"></next-is-blank>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}WP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :print-config="config"
                    :print-time="printData.printTime"
                    :dispensing-info="form.dispensingInfo"
                    :pr-form="form"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>

        <template v-for="(form, formIndex) in printData.prescriptionInfusionForms">
            <outpatient-header
                data-type="header"
                :pharmacy-name="form.pharmacyName"
                :print-data="printData"
                :organ-title="organTitle"
                :config="config"
                print-title="处方笺"
                :print-jinma="form && form.psychotropicNarcoticType"
                :show-express="!!form.deliveryInfo && contentConfig.expressInfo"
                :data-pendants-index="`${formIndex}IP`"
            ></outpatient-header>
            <template v-if="form.isStandardForm">
                <template v-for="(formItems, formItemsIndex) in form.prescriptionFormItems">
                    <div
                        v-for="(groupFormItems, groupIndex) in getInfusionGroupItems(formItems)"
                        data-type="mix-box"
                    >
                        <infusion-item
                            :print-data="printData"
                            :config="config"
                            :group-form-items="groupFormItems"
                        ></infusion-item>
                    </div>
                    <div
                        v-if="form.prescriptionFormItems.length - 1 !== formItemsIndex"
                        data-type="new-page"
                    ></div>
                </template>
            </template>
            <template v-else>
                <div
                    v-for="(groupFormItems, groupIndex) in getInfusionGroupItems(form.prescriptionFormItems)"
                    data-type="mix-box"
                >
                    <infusion-item
                        :print-data="printData"
                        :config="config"
                        :group-form-items="groupFormItems"
                    ></infusion-item>
                </div>
            </template>

            <template v-if="contentConfig.expressInfo && form.deliveryInfo">
                <delivery-info :delivery-info="form.deliveryInfo"></delivery-info>
            </template>
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice :doctor-advice="doctorAdvice"></doctor-advice>
            </template>

            <next-is-blank v-if="!(contentConfig.expressInfo && form.deliveryInfo) && !(contentConfig.doctorAdvice && doctorAdvice.length)"></next-is-blank>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}IP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :pr-form="form"
                    :print-time="printData.printTime"
                    :print-config="config"
                    :dispensing-info="form.dispensingInfo"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>


        <template v-for="(chineseForm, formIndex) in printData.prescriptionChineseForms">
            <outpatient-header
                data-type="header"
                :pharmacy-name="getPharmacyName(chineseForm)"
                :print-data="printData"
                :organ-title="organTitle"
                :print-jinma="chineseForm && chineseForm.psychotropicNarcoticType"
                :show-express="!!chineseForm.deliveryInfo && contentConfig.expressInfo"
                :config="config"
                :extend-spec="chineseForm.specification"
                print-title="处方笺"
                :take-medication-time="getTakeMedicationTime(chineseForm)"
                is-landscape
                :data-pendants-index="`${formIndex}CP`"
                :show-process="!!chineseForm.processUsageInfo && contentConfig.processInfo"
            ></outpatient-header>

            <div
                data-type="mix-box"
            >
                <template v-for="groupItems in getChineseGroupItems(chineseForm.prescriptionFormItems, 4)">
                    <chinese-item
                        :config="config"
                        :form-items="groupItems"
                        :chinese-form="chineseForm"
                        :print-data="printData"
                    ></chinese-item>
                </template>
            </div>


            <!--用法-->

            <print-row
                class="remark-row first-remark-row"
            >
                <print-col
                    class="remark-col"
                    :span="24"
                >
                    <span class="label">用法：</span>
                    <span v-html="getChineseFormUsage(chineseForm, contentConfig).totalInfo"></span>
                </print-col>
            </print-row>

            <print-row
                v-if="getChineseFormUsage(chineseForm, contentConfig).usageInfo"
                class="remark-row"
                data-type="mix-box"
            >
                <print-col
                    class="remark-col"
                    :span="24"
                    v-html="getChineseFormUsage(chineseForm, contentConfig).usageInfo"
                >
                </print-col>
            </print-row>

            <!-- 加工-->
            <print-row
                v-if="(chineseForm.processUsageInfo || chineseForm.contactMobile) && contentConfig.processInfo"
                class="remark-row first-remark-row"
            >
                <print-col
                    class="remark-col"
                    :span="24"
                >
                    <span class="label">加工：</span>
                    {{ chineseForm.processUsageInfo }}
                    <template v-if="chineseForm.contactMobile">
                        ，联系人{{ chineseForm.contactMobile }}
                    </template>
                </print-col>
            </print-row>

            <template v-if="contentConfig.expressInfo && chineseForm.deliveryInfo">
                <delivery-info :delivery-info="chineseForm.deliveryInfo"></delivery-info>
            </template>
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice :doctor-advice="doctorAdvice"></doctor-advice>
            </template>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}CP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :pr-form="chineseForm"
                    is-chinese
                    :print-time="printData.printTime"
                    :print-config="config"
                    :dispensing-info="chineseForm.dispensingInfo"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>

        <template v-for="(externalForm, formIndex) in printData.prescriptionExternalForms">
            <outpatient-header
                data-type="header"
                :pharmacy-name="externalForm.pharmacyName"
                :organ-title="organTitle"
                :show-express="!!externalForm.deliveryInfo"
                :print-data="printData"
                :config="config"
                :print-jinma="externalForm && externalForm.psychotropicNarcoticType"
                :extend-spec="!contentConfig.mergeExternal && config.header && config.header.prescriptionType ? getUsageTypeStr(externalForm) : ''"
                print-title="处方笺"
                :data-pendants-index="`${formIndex}EP`"
            ></outpatient-header>
            <template v-for="(formItem, formItemIndex) in externalForm.prescriptionFormItems">
                <print-row>
                    <print-col
                        :span="18"
                        class="external-item"
                    >
                        <span>{{ formItemIndex + 1 }}.{{ formItem.name }}</span>
                        <span v-if="contentConfig.mergeExternal">({{ getUsageTypeStr(formItem) || getUsageTypeStr(externalForm) }})</span>
                        <span> / {{ formItem.unit }}</span>
                    </print-col>
                    <print-col
                        :span="6"
                        class="external-item"
                    >
                        ×{{ formItem.unitCount }}
                    </print-col>
                </print-row>
                <!--药品-->
                <external-item
                    v-for="(goodsItems, goodsIndex) in getChineseGroupItems(formItem.externalGoodsItems, 4)"
                    type="goods"
                    :config="config"
                    :form-items="goodsItems"
                ></external-item>


                <print-row
                    v-if="formatAcupoints(formItem.acupoints)"
                    class="remark-row first-remark-row external-usage-row"
                    data-type="mix-box"
                >
                    <print-col
                        class="remark-col"
                        :span="24"
                    >
                        <span class="label">{{ formItem.acupointUnit || '穴位' }}：</span>
                        {{ formatAcupoints(formItem.acupoints) }}
                    </print-col>
                </print-row>
                <print-row
                    class="remark-row first-remark-row external-usage-row"
                    data-type="mix-box"
                >
                    <print-col
                        class="remark-col"
                        :span="24"
                    >
                        <span class="label">用法：</span>
                        <template v-if="formItem.unitCount">
                            共 {{ formItem.dosage }} 次
                        </template>
                        <template v-if="formItem.freq">
                            ，{{ formItem.freq }}
                        </template>
                        <template v-if="formItem.specialRequirement">
                            ，{{ formItem.specialRequirement }}
                        </template>
                        <template
                            v-if="
                                formItem.unitCount &&
                                    formItem.dosage &&
                                    isBasedOnAcupoint(externalForm, formItem) &&
                                    formItem.acupoints &&
                                    formItem.acupoints.length"
                        >
                            ，每次{{ calcDosageCount(formItem) }}穴
                            ，共{{ formItem.dosage * calcDosageCount(formItem) }}穴
                        </template>
                    </print-col>
                </print-row>
            </template>

            <template v-if="contentConfig.expressInfo && externalForm.deliveryInfo">
                <delivery-info :delivery-info="externalForm.deliveryInfo"></delivery-info>
            </template>
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice :doctor-advice="doctorAdvice"></doctor-advice>
            </template>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}EP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :pr-form="externalForm"
                    :print-config="config"
                    :print-time="printData.printTime"
                    :dispensing-info="externalForm.dispensingInfo"
                    is-external
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>

        <template v-for="(glassesForm, glassesIndex) in printData.prescriptionGlassesForms">
            <glasses-prescription-header
                data-type="header"
                :print-data="printData"
                :config="glassesConfig"
                print-title="配镜处方"
                :organ-title="organTitle"
                :data-pendants-index="`${glassesIndex}GP`"
            ></glasses-prescription-header>

            <div class="glasses-prescription-wrapper">
                <table class="table">
                    <thead>
                        <tr>
                            <th
                                :style="glassesForm.glassesType !== 0 ? null : { width: '9.5%' }"
                            >
                                <template v-if="glassesForm.glassesType === 0">
                                    {{ glassesForm.usage }}
                                </template>
                            </th>
                            <th
                                v-for="formItem in glassesForm.chargeFormItems"
                                :key="formItem.key"
                                :style="glassesForm.glassesType !== 0 ? { width: '16.6%' } : formItem.key !== 'frameCva' ? { width: '9.5%' } : null"
                            >
                                {{ formItem.name }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>右眼</td>
                            <td
                                v-for="formItem in glassesForm.chargeFormItems"
                                :key="formItem.key"
                            >
                                {{ formItem.rightEyeValue }}
                            </td>
                        </tr>
                        <tr>
                            <td>左眼</td>
                            <td
                                v-for="formItem in glassesForm.chargeFormItems"
                                :key="formItem.key"
                            >
                                {{ formItem.leftEyeValue }}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="table-bottom">
                    <div class="requirement">
                        备注：{{ glassesForm.requirement }}
                    </div>
                    <div
                        v-if="glassesFooterConfig.optometristSignature"
                        class="optometristName"
                    >
                        验光师：{{ glassesForm.optometristName }}
                    </div>
                    <div class="clear-float"></div>
                </div>
                <div class="remark">
                    注：{{ glassesFooterConfig.remark }}
                </div>
            </div>
        </template>

        <div
            class="next-page"
            data-type="next-page"
        >
            (接下页)
        </div>
        <div
            class="prev-page"
            data-type="prev-page"
        >
            (接上页)
        </div>
    </div>
</template>

<script>
    import { formatAge, formatMoney } from './common/utils.js'

    import PrescriptionHandler from "./data-handler/prescription-handler.js";
    import OutpatientHeader from './components/medical-document-header/index.vue'
    import OutpatientFooter from './components/medical-document-footer/index.vue'
    import WesternItem from './components/prescription/western-item/index.vue';
    import ChineseItem from './components/prescription/chinese-item/index.vue';
    import InfusionItem from './components/prescription/infusion-item/index.vue';
    import ExternalItem from './components/prescription/external-item/index.vue';
    import PrintRow from './components/layout/print-row.vue';
    import PrintCol from './components/layout/print-col.vue';
    import {ExternalPRUsageTypeEnumRevert, PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import DoctorAdvice from './components/prescription/doctor-advice/index.vue';
    import NextIsBlank from './components/next-is-blank/index.vue';
    import DeliveryInfo from "./components/prescription/delivery-info/index.vue";

    import {
        getDoctorAdviceArray,
        getInfusionGroupItems,
        getChineseGroupItems,
        doseTotal,
        formatAcupoints,
        checkExistedGroupId,
        getUsageTypeStr, isBasedOnAcupoint, calcDosageCount
    } from "./common/medical-transformat.js";
    import clone from "./common/clone.js";
    import {GLASSES_TYPE, PharmacyTypeEnum} from "./common/constants";
    import GlassesPrescriptionHeader from "./components/medical-document-header/glasses-prescription-header.vue";
    import {filterGlassesUnit} from "./common/filters";

    export default {
        DataHandler: PrescriptionHandler,
        name: "Prescription",
        businessKey: PrintBusinessKeyEnum.PRESCRIPTION,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
        ],
        components: {
            GlassesPrescriptionHeader,
            OutpatientHeader,
            OutpatientFooter,
            WesternItem,
            ChineseItem,
            InfusionItem,
            ExternalItem,
            PrintCol,
            PrintRow,
            DoctorAdvice,
            NextIsBlank,
            DeliveryInfo,
        },
        filters: {
            filterGlassesUnit,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        data() {
            return {
                ExternalPRUsageTypeEnumRevert
            }
        },
        computed: {
            printData() {
                let res;
                this.renderData.printData.prescriptionWesternForms && this.renderData.printData.prescriptionWesternForms.forEach( form => {
                    form.prescriptionFormItems = this.groupMedicine(form.prescriptionFormItems);
                })
                if(this.renderData.config &&
                    this.renderData.config.medicalDocuments &&
                    this.renderData.config.medicalDocuments.prescription &&
                    this.renderData.config.medicalDocuments.prescription.content.standardKindCount) {
                    const {prescriptionWesternForms, prescriptionInfusionForms} = this.transStandardForm();
                    const data =  {
                        ...this.renderData.printData,
                        prescriptionWesternForms: prescriptionWesternForms,
                        prescriptionInfusionForms: prescriptionInfusionForms,
                    }

                    console.log('自动分页过后的printData', data)
                    res = data;
                } else {
                    res = this.renderData.printData || null;
                }

                // 处理配镜处方
                if (res && res.prescriptionGlassesForms && res.prescriptionGlassesForms.length) {
                    res.prescriptionGlassesForms.forEach((glassesForm) => {
                        const glassesFormItems = [];
                        glassesForm.glassesParams.items.forEach((item) => {
                            if (GLASSES_TYPE[glassesForm.glassesType].includes(item.key)) {
                                glassesFormItems.push(item);
                            }
                        });
                        delete glassesForm.glassesParams;
                        glassesForm.chargeFormItems = glassesFormItems;
                    });
                }

                return res;
            },
            config() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.prescription) {
                    return this.renderData.config.medicalDocuments.prescription;
                }
                return {};
            },
            isTakeMedicationTime() {
                return this.printData._dispensingConfig?.isTakeMedicationTime;
            },
            glassesConfig() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.glassesPrescription) {
                    return this.renderData.config.medicalDocuments.glassesPrescription;
                }
                return {};
            },
            glassesFooterConfig() {
                return this.glassesConfig.footer || {};
            },
            headerConfig() {
                return this.config && this.config.header || {};
            },
            contentConfig() {
                return this.config && this.config.content || {};
            },
            organ() {
                return this.printData && this.printData.organ;
            },
            clinicName() {
                return this.organ.name;
            },
            patient() {
                return this.printData.patient;
            },
            doctorAdvice() {
                return getDoctorAdviceArray(this.printData.doctorAdvice);
            },
            organTitle() {
                if (!this.headerConfig.title) {
                    return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.prescription || '';
                }
                return this.headerConfig.title;
            },
        },
        methods: {
            formatAge,
            formatMoney,
            getInfusionGroupItems,
            getChineseGroupItems,
            formatAcupoints,
            checkExistedGroupId,
            getUsageTypeStr,
            isBasedOnAcupoint,
            calcDosageCount,
            getTakeMedicationTime(form) {
                if(!this.isTakeMedicationTime) return '';
                return form.processInfo?.takeMedicationTime || '';
            },
            getPharmacyName(form) {
                const {
                    pharmacyType,
                    pharmacyName,
                } = form;
                if (pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) return '';
                return pharmacyName
            },
            // 获取中药处方用法
            getChineseFormUsage(form, prContentConfig = {}) {
                let _str = '';
                const {chineseMedicineTotalCount} = prContentConfig;
                _str += `共 <span class="bold-text">${form.doseCount || ''}</span> 剂`;
                if(chineseMedicineTotalCount) {
                    _str += `，${doseTotal(form.prescriptionFormItems).kind} 味，单剂 ${
                        doseTotal(form.prescriptionFormItems).count
                    } g，总重 ${(doseTotal(form.prescriptionFormItems).count * form.doseCount).toFixed(2)} g`
                }

                let usageStr = '';
                let usageArray = [];
                if(form.usage) {
                    usageArray.push(form.usage);
                }
                if(form.dailyDosage) {
                    usageArray.push(form.dailyDosage);
                }
                if(form.freq) {
                    usageArray.push(form.freq)
                }
                if(form.usageLevel) {
                    usageArray.push(form.usageLevel)
                }
                if(form.usageDays) {
                    usageArray.push(form.usageDays);
                }
                if(form.requirement) {
                    usageArray.push(form.requirement);
                }
                usageStr = usageArray.join('，')

                if(!chineseMedicineTotalCount) {
                    _str += '，' + usageStr;
                    usageStr = '';
                }
                return {
                    totalInfo:  _str,
                    usageInfo:   usageStr ,
                }
            },

            formatPatientOrderNo( value = '' ) {
                let srcStr = '00000000';
                if (!value)
                    return srcStr;
                return (srcStr + ('' + value)).slice( -8 );
            },
            splitFormItems(formItems, count = 5, isRepeat = true) {
                let tempFormItems = clone(formItems);
                let len = formItems.length;
                let res = [];
                if(isRepeat) {
                    // 不考虑去重，直接按照数量切分
                    let index = 0;
                    while(index < len) {
                        let tempItems = tempFormItems.slice(index, index += count);
                        res.push(tempItems);
                    }
                } else {
                    let groupSet = new Set();
                    let group = [];
                    tempFormItems.forEach( item => {
                        groupSet.add(item.goodsId);
                        if(groupSet.size < 6) {
                            group.push(item);
                        } else {
                            res.push(group);
                            groupSet.clear();
                            groupSet.add(item.goodsId);
                            group = [];
                            group.push(item);
                        }
                    })
                    if(group && group.length) {
                        res.push(group);
                    }
                }
                return res;
            },
            transStandardForm() {
                const wsForms = clone(this.renderData.printData.prescriptionWesternForms) || [];
                const inForms = clone(this.renderData.printData.prescriptionInfusionForms) || [];
                let resWsForm = [];
                let resInForm = [];
                wsForms.forEach( form => {
                    let tempForm = clone(form);
                    tempForm.prescriptionFormItems = [];
                    let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5,false);
                    resWsForm.push({
                        ...tempForm,
                        isStandardForm: true,
                        prescriptionFormItems: formItemsGroups
                    })
                })
                inForms.forEach( form => {
                    let tempForm = clone(form);
                    tempForm.prescriptionFormItems = [];
                    let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5, false);
                    resInForm.push({
                        ...tempForm,
                        isStandardForm: true,
                        prescriptionFormItems: formItemsGroups
                    })

                })
                return {
                    prescriptionWesternForms: resWsForm,
                    prescriptionInfusionForms: resInForm,
                }
            },
            groupMedicine( prescriptionFormItems ) {
                let _group = {};
                let tempFormItems = clone(prescriptionFormItems)
                let noGroupIdItems = [];
                //分组
                tempFormItems.forEach( ( medicine ) => {
                    if(!medicine.groupId) {
                        medicine.showGroupId = true;
                        noGroupIdItems.push(medicine);
                    } else {
                        if (!(_group[ Number( medicine.groupId ) ] instanceof Array)) {
                            _group[ Number( medicine.groupId ) ] = [];
                        }
                        if(!_group[ Number( medicine.groupId ) ].length ) {
                            medicine.showGroupId = true;
                        } else{
                            medicine.showGroupId = false;
                        }
                        _group[ Number( medicine.groupId ) ].push(  medicine );
                    }

                } );
                let res = [];
                for( let item in _group) {
                    res = res.concat(_group[item])
                }
                return res.concat(noGroupIdItems);
            }
        }
    }
</script>

<style lang="scss">
@import "./components/layout/print-layout.scss";
@import "./style/reset.scss";

.abc-page-content {
    box-sizing: border-box;
    padding: 8pt;
    overflow: hidden;
    font-family: "Microsoft YaHei", "微软雅黑";
}

.remark-col {
    position: relative;
    padding-left: 30pt;
    font-size: 10pt;
    font-weight: 300;
    line-height: 12pt;

    .label {
        position: absolute;
        top: 6pt;
        left: 0;
        width: 30pt;
    }
}

.bold-text {
    font-weight: bold;
}

.remark-row {
    margin-bottom: 6pt;
}

.first-remark-row {
    .remark-col {
        padding-top: 6pt;
        border-top: 1px dashed #000000;
    }

    &.external-usage-row {
        .remark-col {
            padding-top: 0;
            border-top: none;

            .label {
                top: 0;
            }
        }
    }
}

.last-remark-row {
    margin-bottom: 0;
}

.external-item {
    margin-bottom: 6pt;
    font-size: 10pt;
    font-weight: bold;
    line-height: 12pt;
}

[data-type~=footer] {
    padding-top: 10pt;
}

.next-page {
    position: relative;
    font-size: 8pt;
    font-weight: lighter;
    text-align: center;
}

.next-page_align_footer {
    position: absolute;
    top: -2pt;
    left: 0;
    width: 100%;
}

.prev-page {
    position: absolute;
    bottom: 8px;
    left: 60px;
    font-size: 8pt;
    font-weight: lighter;
}

.label {
    font-weight: normal;
}

.glasses-prescription-wrapper {
    padding-top: 6pt;

    .table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #a6a6a6;
    }

    th,
    td {
        height: 31px;
        font-size: 13px;
        line-height: 31px;
        color: #000000;
        text-align: center;
        border: 1px solid #a6a6a6;
    }

    .table-bottom {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        height: auto;
        border-right: 1px solid #a6a6a6;
        border-bottom: 1px solid #a6a6a6;
        border-left: 1px solid #a6a6a6;

        .requirement {
            display: inline-block;
            float: left;
            padding: 8px;
            font-size: 13px;
            line-height: 15px;
            color: #000000;
        }

        .optometristName {
            display: inline-block;
            float: right;
            padding: 8px 8px 8px 0;
            font-size: 13px;
            line-height: 15px;
            color: #000000;
        }

        .clear-float {
            clear: both;
        }
    }

    .remark {
        margin-top: 15px;
        font-size: 11px;
        line-height: 13px;
        color: #000000;
    }
}
</style>
