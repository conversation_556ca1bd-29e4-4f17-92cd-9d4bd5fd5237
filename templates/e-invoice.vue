<!--exampleData
{
  address: "杭州西湖x2",
  bankAccount: "1212143412312124124123412",
  bankAgent: "工商银行",
  invoiceChecker: "Bubblex",
  invoiceFee: 185.8,
  invoiceOpener: "刘喜",
  invoiceRemark: "2021-09-15就诊",
  patientId: "ffffffff0000000019b298100f340000",
  patientName: "电子发票",
  payee: "刘喜",
  sendRemarkType: 0,
  status: 2,
  taxName: "杭州店",
  taxNum: "***************",
  telephone: "***********",
  type: 1,
}
-->

<template>
  <div>
    <div class="print-e-invoice-preview">
      <div v-if="printData.invoiceFee < 0" class="refund-fee">
        销项负数
      </div>
      <div class="patient">
        {{ printData.patientName }}
      </div>
      <div class="product-name">
        <span class="name">详见清单</span>
        <span class="amount">{{ printData.invoiceFee | formatMoney }}</span>
      </div>

      <div class="price">
          {{ $t('currencySymbol') }}{{ printData.invoiceFee | formatMoney }}
      </div>
      <span class="upper-price">{{ digitUppercase(printData.invoiceFee) }}</span>
      <span class="lower-price">{{ $t('currencySymbol') }}{{ printData.invoiceFee | formatMoney }}</span>

      <div class="invoice-info">
        <p>{{ printData.taxName }}</p>
        <p>{{ printData.taxNum }}</p>
        <p>{{ printData.address }} {{ printData.telephone }}</p>
        <p>{{ printData.bankAgent }}</p>
      </div>

      <div class="invoice-remark">
        {{ printData.invoiceRemark }}
      </div>
      <div class="cashier">
        {{ printData.payee }}
      </div>
      <div class="review">
        {{ printData.invoiceChecker }}
      </div>
      <div class="open">
        {{ printData.invoiceOpener }}
      </div>
    </div>
  </div>
</template>

<script>
import CommonHandler from './data-handler/common-handler.js'
import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
import PageSizeMap, {Orientation} from "../share/page-size.js";
import { digitUppercase, formatMoney } from './common/utils.js';

export default {
  name: "EInvoice",
  DataHandler: CommonHandler,
  businessKey: PrintBusinessKeyEnum.E_INVOICE,
  filters: {
    formatMoney
  },
  props: {
    renderData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  pages: [
    {
      paper: PageSizeMap.MM186_101,
      isRecommend: false,
      defaultOrientation: Orientation.portrait,
      defaultHeightLevel: null,
    },
  ],
  computed: {
    printData() {
      return this.renderData.printData || null;
    },
  },
  methods: {
    digitUppercase
  }
}
</script>
<style lang="scss">
.abc-page_preview {
  background: url("/static/assets/print/e-invoice-bg.jpg");
  background-size: 186mm 101mm;
  color: #2a82e4;
}

.print-e-invoice-preview {
  position: relative;
  top: 0;
  bottom: 0;
  left: 0;
  width: 186mm;
  height: 100mm;
  font-size: 12px;
  line-height: 14px;

  .refund-fee {
    position: absolute;
    top: 10mm;
    left: 4mm;
    font-size: 11pt;
  }

  .patient {
    position: absolute;
    top: 22mm;
    left: 31mm;
  }

  .product-name {
    position: relative;
    top: 42mm;
    padding-left: 6mm;

    .amount {
      margin-left: 114mm;
    }
  }

  .price {
    position: absolute;
    top: 65mm;
    left: 132mm;
  }

  .upper-price,
  .lower-price {
    position: absolute;
    top: 71.5mm;
  }

  .upper-price {
    left: 52mm;
  }

  .lower-price {
    left: 150mm;
  }

  .invoice-info {
    position: absolute;
    top: 76mm;
    left: 32mm;
  }

  .invoice-remark {
    position: absolute;
    top: 76mm;
    left: 112mm;
  }

  .cashier,
  .review,
  .open {
    position: absolute;
    top: 91mm;
  }

  .cashier {
    left: 18mm;
  }

  .review {
    left: 67mm;
  }

  .open {
    left: 109mm;
  }
}
</style>
