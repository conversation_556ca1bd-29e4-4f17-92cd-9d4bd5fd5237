<!--exampleData
{}
-->

<template>
    <div></div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";

    export default {
        name: "PrescriptionXufang",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.PRESCRIPTION_XUFANG,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分' // 默认选择的等分纸
            },
        ],
    }
</script>
<style lang="scss">
@import "./style/medical-fee-list/national/index";
.abc-page_preview {
  background: #fff;
  color: #000;
}
</style>
