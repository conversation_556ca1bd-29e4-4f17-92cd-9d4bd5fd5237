<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    socialName: '诊费',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 100.11,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    socialName: 'HPV基因全套',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 320,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    socialName: '甲胎蛋白（AFP）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 40,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '针灸理疗套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
        },

        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    socialName: '四环素软膏（三益）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '支',
                    discountedPrice: 36.0,
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    socialName: '法莫替丁片（迪诺洛克）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '片',
                    discountedPrice: 6.0,
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    socialName: '胸腺肽肠溶片（奇莫欣）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '盒',
                    discountedPrice: 20.0,
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            curYearBalance: 8, // 当年账户余额
            allYearBalance: 10, // 历年账户余额
            curYearAccountPaymentFee: 7, // 本年账户支付
            allYearAccountPaymentFee: 9, // 历年账户支付
            fundPayment: 0, // 规定病种
            cashPayment: 0, // 规定病种
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 1, // 自负金额
            allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 3, // 自理金额
            allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 5, // 自费金额
            allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            sbzzPaymentFee: 0, // 商保赔付
        },
    },
}
-->

<template>
    <div>
        <template v-for="(page, pageIndex) in currentRenderPage">
            <div
                :key="pageIndex"
                class="shanxi-jin-medical-bill-content"
            >
                <div
                    class="shanxi-jin-medical-bill-page"
                >
                    <div
                        v-if="blueInvoiceData"
                        style="position: absolute; top: 0.35cm; left: 1cm;"
                    >
                        销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                    </div>
                    <refund-icon
                        v-if="isRefundBill"
                        top="0.8cm"
                        left="1cm"
                    ></refund-icon>


                    <block-box
                        top="90.5"
                        left="23"
                        class="department-name-length"
                    >
                        {{ shanxiJin.institutionName }}
                    </block-box>


                    <block-box
                        top="38.5"
                        left="24"
                    >
                        {{ patient.name }}
                    </block-box>

                    <block-box
                        top="39"
                        left="57"
                    >
                        {{ printData.chargedTime | parseTime('y-m-d') }}
                    </block-box>

                    <block-box
                        top="45.5"
                        left="29"
                    >
                        {{ registrationFee|formatMoney }}
                    </block-box>

                    <block-box
                        top="65"
                        left="29"
                    >
                        {{ westernMedicineFee|formatMoney }}
                    </block-box>
                    <block-box
                        top="58.5"
                        left="29"
                    >
                        {{ chineseComposeMedicineFee|formatMoney }}
                    </block-box>
                    <block-box
                        top="51.5"
                        left="29"
                    >
                        {{ chineseMedicineDrinksPieceFee|formatMoney }}
                    </block-box>
                    <block-box
                        top="78"
                        left="29"
                    >
                        {{ examinationInspectionFee|formatMoney }}
                    </block-box>

                    <block-box
                        top="71"
                        left="29"
                    >
                        {{ treatmentFee|formatMoney }}
                    </block-box>

                    <block-box
                        top="51.5"
                        left="61"
                    >
                        {{ examinationExaminationFee|formatMoney }}
                    </block-box>

                    <block-box
                        top="71"
                        left="48"
                    >
                        其他
                    </block-box>

                    <block-box
                        top="71.5"
                        left="61"
                    >
                        {{ otherFeeTotal|formatMoney }}
                    </block-box>

                    <block-box
                        top="83.5"
                        left="34"
                    >
                        {{ digitUppercase(finalFee) }}
                    </block-box>

                    <block-box
                        top="90.5"
                        left="60"
                    >
                        {{ printData.chargedByName }}
                    </block-box>

                    <block-box
                        top="110.5"
                        left="22"
                    >
                        {{ printData.chargedTime | parseTime('y-m-d') }}
                    </block-box>

                    <div
                        class="form-items-wrapper"
                    >
                        <div
                            v-for="(item, index) in page.formItems"
                            :key="index + pageIndex"
                            class="form-item-tr"
                        >
                            <template v-if="item.type === 'total'">
                                <span
                                    overflow
                                    class="item-name"
                                >{{ item.name }}</span>
                                <span
                                    overflow
                                    class="all-total-price"
                                >{{ $t('currencySymbol') }} {{ item.discountedPrice }}</span>
                            </template>
                            <template v-else>
                                <span
                                    overflow
                                    class="item-name"
                                >
                                    <template v-if="isShowSocialCode(item)">
                                        [{{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}]
                                    </template>
                                    {{ item.name }}
                                    <template v-if="item.printFormType === PrintFormTypeEnum.PRESCRIPTION_WESTERN && item.displaySpec">
                                        ({{ item.displaySpec }})
                                    </template>
                                </span>
                                <span
                                    overflow
                                    class="item-price"
                                >{{ item.discountedUnitPrice | formatMoney }}*{{ item.count }}{{ item.unit }}</span>
                                <span
                                    overflow
                                    class="total-price"
                                >{{ item.discountedPrice | formatMoney }}</span>
                            </template>
                        </div>

                        <div
                            v-if="hasOverPageTip"
                            class="only-one-page"
                        >
                            *** 因纸张限制，部分项目未打印 ***
                        </div>
                        <template v-else>
                            <div
                                v-if="pageIndex !== currentRenderPage.length - 1"
                                class="only-one-page"
                            >
                                *** 接下页 ***
                            </div>
                        </template>
                    </div>

                    <block-box
                        top="157.5"
                        left="23"
                        class="department-name-length"
                    >
                        {{ printData.departmentName }}
                    </block-box>
                    <block-box
                        top="157.5"
                        left="60"
                    >
                        {{ printData.chargedByName }}
                    </block-box>

                    <block-box
                        top="174"
                        left="22"
                    >
                        {{ printData.chargedTime | parseTime('y-m-d') }}
                    </block-box>

                    <div
                        class="form-items-wrapper-2"
                    >
                        <div
                            v-for="(item, index) in page.formItems"
                            :key="index + pageIndex"
                            class="form-item-tr"
                        >
                            <template v-if="item.type === 'total'">
                                <span
                                    overflow
                                    class="item-name"
                                >{{ item.name }}</span>
                                <span
                                    overflow
                                    class="all-total-price"
                                >{{ $t('currencySymbol') }} {{ item.discountedPrice }}</span>
                            </template>
                            <template v-else>
                                <span
                                    overflow
                                    class="item-name"
                                >
                                    <template v-if="isShowSocialCode(item)">
                                        [{{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}]
                                    </template>
                                    {{ item.name }}
                                    <template v-if="item.printFormType === PrintFormTypeEnum.PRESCRIPTION_WESTERN && item.displaySpec">
                                        ({{ item.displaySpec }})
                                    </template>
                                </span>
                                <span
                                    overflow
                                    class="item-price"
                                >{{ item.discountedUnitPrice | formatMoney }}*{{ item.count }}{{ item.unit }}</span>
                                <span
                                    overflow
                                    class="total-price"
                                >{{ item.discountedPrice | formatMoney }}</span>
                            </template>
                        </div>

                        <div
                            v-if="hasOverPageTip"
                            class="only-one-page"
                        >
                            *** 因纸张限制，部分项目未打印 ***
                        </div>
                        <template v-else>
                            <div
                                v-if="pageIndex !== currentRenderPage.length - 1"
                                class="only-one-page"
                            >
                                *** 接下页 ***
                            </div>
                        </template>
                    </div>

                    <block-box
                        top="221"
                        left="23"
                        class="department-name-length"
                    >
                        {{ printData.departmentName }}
                    </block-box>
                    <block-box
                        top="221"
                        left="60"
                    >
                        {{ printData.chargedByName }}
                    </block-box>
                </div>
            </div>
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import NationalBillData from "./mixins/national-bill-data";
    import {PrintFormTypeEnum} from "./common/constants";
    import Clone from "./common/clone";

    export default {
        name: "MedicalBillShanxiJin",
        components: {
            RefundIcon,
            BlockBox
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_SHANXI_JIN,
        pages: [
            {
                paper: PageSizeMap.MM93_229_SHANXI_JIN,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            PrintFormTypeEnum() {
                return PrintFormTypeEnum
            },
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                const totalItem = {
                    name: `合计：${this.chargeFormItems.length}项`,
                    discountedPrice: this.finalFee,
                    type: 'total',
                };
                // 开启了只打一页的配置且内容超过了一页,在第一页加上总计
                if (this.isOnlyOnePage) {
                    const resRenderPage = Clone(this.renderPage[0]);
                    resRenderPage.formItems.splice(resRenderPage.formItems.length - 1, 1, totalItem);
                    return [resRenderPage];
                } else if (this.extra.isPreview) {
                    // 在设置预览页面,未开启只打一页的配置,在第一页加上总计
                    const resRenderPage = Clone(this.renderPage[0]);
                    if (resRenderPage.formItems.length < 8) {
                        resRenderPage.formItems.push(totalItem);
                    } else {
                        resRenderPage.formItems.splice(resRenderPage.formItems.length - 1, 1, totalItem);
                    }
                    return [resRenderPage];
                }
                // 未开启只打一页,在最后加上总计
                const cacheRenderPage = Clone(this.renderPage);
                const resRenderPage = cacheRenderPage[cacheRenderPage.length - 1];
                if (resRenderPage.formItems.length < 8) {
                    resRenderPage.formItems.push(totalItem);
                } else {
                    const newRenderPage = {
                        formItems: [totalItem],
                        pageIndex: resRenderPage.pageIndex + 1,
                    }
                    cacheRenderPage.push(newRenderPage);
                }
                return cacheRenderPage;
            },
            splitType() {
                return this.shanxiJin.splitType;
            },
            isOnlyOnePage() {
                if (this.splitType === 1) {
                    const firstRenderPage = this.renderPage[0] || { formItems: [] };
                    return this.renderPage.length > 1 || firstRenderPage.formItems.length >= 8;
                }
                return false;
            },
            shanxiJin() {
                return this.config.shanxiJin || {}
            },
            medicalBills() {
                return this.printData.medicalBills &&this.printData.medicalBills.filter(billItem => {
                    if (billItem.name === this.$t('registrationFeeName')) {
                        return !!billItem.totalFee
                    }
                    return true
                }) || [];
            },

            renderPage() {
                return this.spliceFormItems(this.chargeFormItems, 8);
            },
            otherFeeTotal() {
                return this.materialFee + this.otherFee
            }
        },
        created() {
            this.initFee();
        },
    }
</script>
<style lang="scss">
* {
    padding: 0;
    margin: 0;
}

.shanxi-jin-medical-bill-content {
    @import "./components/refund-icon/refund-icon.scss";

    font-size: 9pt;

    .shanxi-jin-medical-bill-page {
        position: absolute;
        width: 93mm;
        height: 229mm;

        .department-name-length {
            width: 23mm;
            word-break: break-all;
            word-wrap: break-word;
        }

        &.is-electron {
            height: 127mm;
        }
        //border: 1pt solid #00ace9;
    }

    .trans-number,
    .patient,
    .form-items-wrapper,
    .form-items-wrapper-2,
    .medical-bill-wrapper {
        position: absolute;
        line-height: 11pt;
    }

    .trans-number {
        top: 47.5mm;
        left: 30mm;
    }

    .patient {
        top: 24mm;
    }

    .form-items-wrapper {
        top: 122mm;
        left: 13.5mm;
        width: 65mm;
        height: 34mm;
    }

    .form-items-wrapper-2 {
        top: 185.5mm;
        left: 13.5mm;
        width: 65mm;
        height: 34mm;
    }

    .next-page {
        position: absolute;
        bottom: -6mm;
        left: 0;
        width: 100%;
        text-align: center;
    }

    .form-item-tr {
        width: 100%;
        height: 4mm;
        font-size: 0;
        line-height: 11pt;
    }

    .item-name,
    .item-count,
    .item-price,
    .item-spec,
    .item-proportion,
    .total-price,
    .all-total-price {
        display: inline-block;
        *display: inline;
        overflow: hidden;
        font-size: 9pt;
        word-break: keep-all;
        white-space: nowrap;
        *zoom: 1;
    }

    .shebao-wrapper {
        position: absolute;
        top: 60mm;
        left: 112mm;
    }

    .item-name {
        width: 35mm;
    }

    .item-count {
        width: 36mm;
        text-align: center;
    }

    .item-proportion {
        width: 6mm;
        padding-left: 1mm;
        text-align: right;
    }

    .item-spec {
        padding-left: 2mm;
    }

    .item-price,
    .item-spec {
        width: 18mm;
        *width: 18mm;
        text-align: right;
    }

    .total-price {
        width: 11mm;
        *width: 11mm;
        text-align: right;
    }

    .all-total-price {
        width: 29mm;
        *width: 29mm;
        overflow: hidden;
        text-align: right;
    }

    .medical-bill-wrapper {
        top: 37mm;
        left: 23mm;
        width: 62mm;
        height: 31mm;
    }

    .medical-bill-item {
        position: absolute;
        width: 31mm;
    }

    .upper-money {
        position: absolute;
        top: 96mm;
    }

    .upper-money {
        left: 36mm;
    }

    .only-one-page {
        text-align: center;
    }
}

.abc-page_preview {
    color: #2a82e4;
    background: url("/static/assets/print/shanxi-jin.jpg");
    background-size: 93mm 229mm;
}
</style>
