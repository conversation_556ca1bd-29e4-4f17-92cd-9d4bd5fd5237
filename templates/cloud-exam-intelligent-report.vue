<template>
    <div class="intelligent-interpretation-detail-content">
        <div data-type="header"></div>

        <div class="header-content">
            <div
                style="gap:6px;align-items: center"
                class="header-content-left flex"
            >
                <img
                    src="data:image/png;base64,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"
                    alt="cloud"
                    width="14"
                    height="14"
                />
                <div style="font-size: 12px;color:#7a8794;margin-bottom: 4px;">
                    由ABC医疗大模型解读
                </div>
            </div>

            <div
                style="font-size: 28px;font-weight: bold"
                class="header-content-center"
            >
                AI 报告解读
            </div>

            <div class="examination-project-name">
                {{ printData.name }}
            </div>
        </div>
        
        <abc-row
            :gap-y="12"
            style="margin-bottom: 14px;"
        >
            <abc-col
                v-for="(list, index) in examRelateInfoList"
                :key="index"
                :span="24"
                class="exam-relate-info-row"
            >
                <abc-row
                    :gap-y="8"
                    :wrap="true"
                >
                    <abc-col
                        v-for="(item, itemIndex) in list.list"
                        :key="itemIndex"
                        :span="item.span"
                    >
                        <div
                            class="exam-relate-info-item"
                            style="display: flex;"
                        >
                            <span>
                                {{ item.label }}：
                            </span>

                            <span
                                v-if="!item.isHtml"
                                style="flex: 1"
                            >
                                {{ item.value }}
                            </span>
                            
                            <span
                                v-else
                                style="flex: 1"
                                :style="item.style"
                                overflow
                                v-html="item.value"
                            ></span>
                        </div>
                    </abc-col>
                </abc-row>
            </abc-col>
        </abc-row>

        <div style="font-size: 14px;font-weight: bold;margin-bottom: 4px">
            异常项目
        </div>

        <abnormal-table
            :data-list="abnormalResult"
        ></abnormal-table>

        <div
            class="dashed-line"
            style="margin: 16px 0"
        ></div>

        <div
            ref="analysisTitle"
        >
        </div>

        <div data-type="footer">
            <div
                class="solid-line"
                style="margin: 16px 0"
            ></div>

            <span style="font-size: 12px;color: #7A8794">
                *
                AI 解读结果仅供参考，不能替代医生权威结论，临床决策需医生综合判断。
            </span>
        </div>
    </div>
</template>

<script lang="ts">
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import CommonHandler from "./data-handler/common-handler.js";
    import AbnormalTable from "./components/cloud-exam-intelligent-report/abnorma-table.vue";
    import AbcRow from "./components/layout/abc-layout/abc-row.vue";
    import AbcCol from "./components/layout/abc-layout/abc-col.vue";
    import {
        formatAge,
    } from "./common/utils";

    export default {
        name: "CloudExamReport",
        components: { AbnormalTable, AbcRow, AbcCol },
        businessKey: PrintBusinessKeyEnum.EXAMINATION_CLOUD_INTELLIGENT_REPORT,
        getInjectGlobalStyle(data) {
            return data.style;
        },
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null
            }
        ],
        DataHandler: CommonHandler,
        props: {
            renderData: {
                type: Object,
                default: () => ({})
            }
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            examRelateInfoList() {
                return this.printData.examRelateInfoList || [];
            },
            abnormalResult() {
                return this.printData.abnormalResult ?? [];
            },
            medicalRecord() {
                return this.printData.medicalRecord ?? "";
            },
            patientInfo() {
                const patientInfo = this.printData.patient ?? {};
                const name = patientInfo.name || "-";
                const sex = patientInfo.sex || "-";
                return `${name} ${sex} ${formatAge(patientInfo.age || {})}`;
            },
            examinationAIAnalysisResultCommonStyle() {
                return this.printData.style || '';
            }
        },
        mounted() {
            this.$refs.analysisTitle &&
                this.$refs.analysisTitle.insertAdjacentHTML(
                    "afterend",
                    this.printData.parsedAnalysisResult
                );
        },
    };
</script>

<style lang="scss">
.intelligent-interpretation-detail-content {
    .flex {
        display: flex;
    }
    .header-content {
        &-center {
            margin: 8px 0;
            text-align: center;
            font-family: SimSun Han Serif CN;
            font-size: 28px;
            font-style: normal;
            font-weight: 700;
            line-height: 32px; /* 114.286% */
        }

        .examination-project-name {
            font-family: SimSun Han Serif CN;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
            padding-bottom: 28px;
            text-align: center;
        }
    }
   
   .exam-relate-info-row {
        border-bottom: 1px solid #7A8794;
        padding-bottom: 12px;
   }

   .exam-relate-info-item {
        display: flex;

        span {
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
   }
}
.dashed-line {
    width: 100%;
    height: 1px;
    border-bottom: 1px dashed #7A8794;
}
.solid-line {
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #7A8794;
}
.tag-warning {
    border: 1px solid #ffebd6;
    background: #fff4ea;
    color: #e5892d;
    font-size: 12px;
    padding: 0 3px;
    border-radius: 6px;
}
</style>
