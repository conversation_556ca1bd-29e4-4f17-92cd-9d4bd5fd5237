export const PrintGroupKeyEnum = {
    INVENTORY: 'inventory', // 库存
    PRESCRIPTION: 'prescription', // 医疗文书
    TICKET: 'ticket', //小票
    TAG: 'tag', //标签
    STAT: 'stat', // 统计
    MEIDICAL_BILL: 'medical_bill',
}

export const PrintBusinessKeyEnum = {
    GOODS_IN: 'goods-in', // 入库
    GOODS_PURCHASE: 'goods-purchase', // 采购
    GOODS_OUT: 'goods-out', // 出库
    GOODS_TRANS: 'goods-trans', // 调拨
    GOODS_CHECK: 'goods-check', // 盘点
    GOODS_APPLY: 'goods-apply', // 领用
    PRESCRIPTION_XUFANG: "prescription-xufang", //处方-续方
    PRESCRIPTION_YIBAO: "prescription-yibao", //处方-医保
    PRESCRIPTION_CIRCULATION: "prescription-circulation", // 电子处方流转
    PRESCRIPTION: 'prescription', // 处方
    PRESCRIPTION_V2: 'prescription-v2', // 新版处方
    PRESCRIPTION_SHANGHAI: 'prescription-shanghai', // 新版处方
    MEDICAL_RECORD: 'medical-record', // 病历
    SETTLEMENT_APPLICATION: 'settlement-application', // 采购结算单
    SETTLEMENT_REVIEW: 'settlement-review', // 采购结算明细
    SETTLEMENT_DETAIL: 'settlement-detail', // 药店付款明细
    TREATMENT_EXECUTE: 'treatment-execute', // 治疗理疗单
    TREATMENT_EXECUTE_NANNING: 'treatment-execute-nanning', // 南宁定制治疗理疗单
    INFUSION_EXECUTE: 'infusion-execute', // 输液注射单
    MEDICAL_CERTIFICATE: 'medical-certificate', // 病情证明书
    EXAMINATION_REPORT: 'examination-report', // 检验报告
    EXAMINATION_CLOUD_REPORT: 'examination-cloud-report', // 检验报告
    EXAMINATION_CLOUD_INTELLIGENT_REPORT: 'examination-cloud-intelligent-report', // 云检智能报告
    EXAMINATION_REPORT_PDF: 'examination-report-pdf', // 检验报告
    INSPECT_REPORT: 'inspect-report', // 检查报告
    EXAMINATION: 'examination', // 检查检验单
    CHILD_HEALTHY_REPORT: 'child-healthy-report', // 健康报告
    EXAMINATION_APPLY_SHEET: 'examination-apply-sheet', // 检查检验申请单
    CHILD_HEALTHY_TESTS: 'child-healthy-tests', // 儿保测试题
    CHILD_HEALTHY_TESTS_RESULT: 'child-healthy-tests-result', // 儿保测试题结果
    FAMILY_DOCTOR_AGREEMENT: 'family-doctor-agreement',

    REGISTRATION: 'registration', // 挂号
    RECHARGE: 'recharge', // 充值小票
    REFUND: 'refund', // 退款凭证
    CASHIER: 'cashier', // 收费小票
    PHARMACY_CASHIER_V2: 'pharmacyCashierV2', // 指令打印方式的收费小票
    EXECUTE_CERTIFICATE: 'execute-certificate', // 执行凭证
    CASHIER_A5: 'cashier-A5', // 收费单
    REFUND_CASHIER: 'refund-cashier', // 退费小票
    DISPENSE: 'dispense', // 发药小票
    DISPENSE_A5: 'dispense-A5', // 发药单

    MEDICINE_TAG: 'medicine-tag', // 用药标签
    MEDICINE_TAG_CUSTOMIZED: 'medicine-tag-customized', // 用药标签-定制版
    HOSPITAL_MEDICINE_TAG: 'hospital-medicine-tag', // 住院瓶贴
    PATIENT_TAG: 'patient-tag', // 患者标签
    EXAMINATION_TAG: 'examination-tag', // 检验条码
    EXAMINATION_LABEL: 'examination-label', // 检验标签
    REGISTRATION_TAG: 'registration-tag',//挂号标签
    MEDICAL_BILL_ANHUI: 'medical-bill-anhui',
    MEDICAL_BILL_CHENGDU: 'medical-bill-chengdu',
    MEDICAL_BILL_CHONGQING: 'medical-bill-chongqing',
    MEDICAL_BILL_CHONGQING_NATIONAL: 'medical-bill-chongqing-national',
    MEDICAL_BILL_GUANGDONG: 'medical-bill-guangdong',
    MEDICAL_BILL_HEFEI: 'medical-bill-hefei',
    MEDICAL_BILL_HENAN: 'medical-bill-henan',
    MEDICAL_BILL_NINGXIA: 'medical-bill-ningxia',
    MEDICAL_BILL_HUBEI: 'medical-bill-hubei',
    MEDICAL_BILL_JILIN: 'medical-bill-jilin',
    MEDICAL_BILL_HUNAN: 'medical-bill-hunan',
    MEDICAL_BILL_JIANGSU: 'medical-bill-jiangsu',
    MEDICAL_BILL_LIAONING: 'medical-bill-liaoning',
    MEDICAL_BILL_LIAONING_NATIONAL: 'medical-bill-liaoning-national',
    MEDICAL_BILL_NATIONAL: 'medical-bill-national',
    MEDICAL_BILL_QINGDAO: 'medical-bill-qingdao',
    MEDICAL_BILL_SHANGHAI: 'medical-bill-shanghai',
    MEDICAL_BILL_XIAMEN: 'medical-bill-xiamen',
    MEDICAL_BILL_XIAMEN_XIN: 'medical-bill-xiamen-xin',
    MEDICAL_BILL_HUANGSHAN: 'medical-bill-huangshan',
    MEDICAL_BILL_SHANXI: 'medical-bill-shanxi',
    MEDICAL_BILL_SHNEYANG: 'medical-bill-shenyang',
    MEDICAL_BILL_YUNAN: 'medical-bill-yunnan',
    MEDICAL_BILL_YUNANOLD: 'medical-bill-yunnanOld',
    MEDICAL_BILL_ZHEJIANG: 'medical-bill-zhejiang',
    MEDICAL_BILL_KUNSHAN: 'medical-bill-kunshan',
    MEDICAL_BILL_HEBEI: 'medical-bill-hebei',
    MEDICAL_BILL_TANGSHAN: 'medical-bill-tangshan',
    MEDICAL_BILL_ZHEJIANG_NATIONAL: 'medical-bill-zhejiang-national',
    MEDICAL_BILL_NINGBO: 'medical-bill-ningbo',
    MEDICAL_BILL_GUANGXI: 'medical-bill-guangxi',
    MEDICAL_BILL_HEILONGJIANG: 'medical-bill-heilongjiang',
    MEDICAL_BILL_JIANGXI: 'medical-bill-jiangxi',
    MEDICAL_BILL_HUHEHAOTE: 'medical-bill-huhehaote',
    MEDICAL_BILL_ZHEJIANG_VERTICAL: 'medical-bill-zhejiang-vertical',
    MEDICAL_BILL_TIANJIN: 'medical-bill-tianjin',
    MEDICAL_BILL_GANSU: 'medical-bill-gansu',
    MEDICAL_BILL_SHANXI_JIN: 'medical-bill-shanxi-jin',
    MEDICAL_BILL_GUANGDONG_NEW: 'medical-bill-guangdong-new',
    MEDICAL_BILL_SHANDONG: 'medical-bill-shandong',
    MEDICAL_BILL_WUHU: 'medical-bill-wuhu',
    MEDICAL_BILL_SHANXI_NATIONAL: 'medical-bill-shanxi-national',
    MEDICAL_BILL_WUHAN: 'medical-bill-wuhan',
    MEDICAL_BILL_ANHUIXUANCHENG: 'medical-bill-anhuixuancheng',
    MEDICAL_BILL_TAIYUAN: 'medical-bill-taiyuan',

    MEDICAL_HOSPITAL_BILL_QINGDAO: 'medical-hospital-bill-qingdao',
    MEDICAL_HOSPITAL_BILL_HEBEI: 'medical-hospital-bill-hebei',
    MEDICAL_HOSPITAL_BILL_YUNNAN: 'medical-hospital-bill-yunnan',



    MEDICAL_FEE_LIST_NORMAL: 'medical-fee-list-normal',
    MEDICAL_FEE_LIST_WUJIANG: 'medical-fee-list-wujiang',
    MEDICAL_FEE_LIST_JIANGSU: 'medical-fee-list-jiangsu',
    MEDICAL_FEE_LIST_HUANGSHI: 'medical-fee-list-huangshi',
    MEDICAL_FEE_LIST_HUAIAN: 'medical-fee-list-huaian',
    MEDICAL_FEE_LIST_CHANGSHU: 'medical-fee-list-changshu',
    MEDICAL_FEE_LIST_SHANXI: 'medical-fee-list-shanxi',
    MEDICAL_FEE_SOCIAL: 'medical-fee-social',
    HOSPITAL_MEDICAL_RECORD: 'hospital-medical-record',
    HOSPITAL_INSPECT: 'hospital-inspect',
    HOSPITAL_NURSE_PRESCRIPTION: 'hospital-nurse-prescription',
    HOSPITAL_DOCTOR_MEDICAL_PRESCRIPTION: 'hospital-doctor-medical-prescription', // 医生站打印，长期医嘱单 临时医嘱单
    HOSPITAL_ADVICE_SHANDONG: 'hospital-advice-shandong', // 山东定制医嘱
    HOSPITAL_NURSE_PATIENT_DISPENSING: 'hospital-nurse-patient-dispensing', // 药房 领药单，退药单 患者维度
    HOSPITAL_NURSE_APPLY_MEDICINE: 'hospital-nurse-apply-medicine', // 护士站打印 领药单，退药单 患者维度
    HOSPITAL_NURSE_GOODS_DISPENSING: 'hospital-nurse-goods-dispensing', // 护士站打印 领药单，退药单 药品维度
    HOSPITAL_INFUSION_RECORD: 'hospital-infusion-record',
    HOSPITAL_DEPOSIT_RECEIPT: 'hospital-deposit-receipt',
    HOSPITAL_PRESCRIPTION: 'hospital-prescription', // 住院-处方
    HOSPITALIZATION_CERTIFICATE:'hospitalization-certificate', //住院证
    HOSPITAL_BEDSIDE_CARD: 'hospital-bedside-card', // 床头卡
    // 商城
    MALL_AGREEMENT_REPORT: 'mall-agreement-report',
    //统计
    STAT_OPERATE_CASHIER_REPORT: 'stat-operate-cashier-report', // 收费日报
    STAT_HOSPITAL_CHARGE_REPORT: 'stat-hospital-charge-report', // 住院收费报表
    STAT_PE_CHARGE_REPORT: 'stat-pe-charge-report', // 体检收费日报表
    STAT_REPORT_TABLE_TEMPLATE:'stat-report-table-template', //报表类表格模版
    CASHIER_REPORT_GROUP_TEMPLATE:'cashier-report-group-template', //配置的收费报表模版

    // 电子发票
    E_INVOICE: 'e-invoice',
    MEDICAL_E_INVOICE_NEIMENGGU: 'medical-e-invoice-neimenggu',
    MEDICAL_E_INVOICE_JIANGSU: 'medical-e-invoice-jiangsu',
    MEDICAL_E_INVOICE_SHANXI: 'medical-e-invoice-shanxi',
    MEDICAL_E_INVOICE_YUNNAN: 'medical-e-invoice-yunnan',
    MEDICAL_E_INVOICE_GUANGXI: 'medical-e-invoice-guangxi',
    MEDICAL_E_INVOICE_GUIZHOU: 'medical-e-invoice-guizhou',
    MEDICAL_E_INVOICE_CHONGQING: 'medical-e-invoice-chongqing',
    MEDICAL_E_INVOICE_ANHUI: 'medical-e-invoice-anhui',
    MEDICAL_E_INVOICE_LIAONING: 'medical-e-invoice-liaoning',
    MEDICAL_E_INVOICE_HEBEI: 'medical-e-invoice-hebei',
    MEDICAL_E_INVOICE_GUANGDONG: 'medical-e-invoice-guangdong',
    MEDICAL_E_INVOICE_BEIJING: 'medical-e-invoice-beijing',
    TAX_FULL_DIGITAL_INVOICE: 'tax-full-digital-invoice', // 数电发票

    // 浙江挂号票
    ZHEJIANG_INVOICE: 'zhejiang-invoice',

    // 广东挂号票
    GUANGDONG_REG_INVOICE: 'guangdong-reg-invoice',

    // 天津挂号票
    TIANJIN_REG_INVOICE: 'tianjin-reg-invoice',
    // 天津中药处方
    TIANJIN_CHINESE_PRESCRIPTION: 'tianjin-chinese-prescription',
    // 天津非中药处方
    TIANJIN_WESTERN_PRESCRIPTION: 'tianjin-western-prescription',

    // 收费告知书
    CHARGE_NOTIFICATION: 'charge-notification',

    // 慢病报告
    CHRONIC_CARE: 'chronic-care',

    //血糖单
    BLOOD_SUGAR_TABLE_TEMPLATE:'blood-sugar-table-template', //血糖单模版

    // 体温单
    TEMPERATURE_GRAPH: 'temperature-graph',
    CHARGE_LIST: 'charge-list',
    CHARGE_LIST_BATCH: 'charge-list-batch',
    CHARGE_SHIFT: 'charge-shift',
    HOSPITAL_CASHIER: 'hospital-cashier',

    // 体检
    // 体检个人报告
    PE_INDIVIDUAL_REPORT: 'pe-individual-report',
    PE_GUIDE_SHEET: 'pe-guide-sheet',
    PE_GROUP_REPORT: 'pe-group-report',

    // 山东处方
    SHANDONG_PRESCRIPTION: 'shandong-prescription',
    // 眼科检查报告
    EYE_INSPECT_REPORT: 'eye-inspect-report',
    // 定制需求-眼科检查报告书
    EYE_INSPECT_REPORT_CUSTOM: 'eye-inspect-report-custom',

    INSPECT_LABEL: 'inspect-label',

    // 订单云
    // 发货单 A4、A5
    ORDER_CLOUD_SHIPMENT_A4A5: 'order-cloud-shipment-a4a5',
    ORDER_CLOUD_SHIPMENT_76: 'order-cloud-shipment-76',

    PE_CHARGE_FEE_LIST: 'pe-charge-fee-list',
    // 煎药工艺卡
    DECOCTION_CRAFT_CARD: 'decoction-craft-card',

    // 价签
    PRICE_TAG: 'price-tag',

    // PDF Lodop 直接打印
    PDF_LODOP_PRINT_TEMPLATE: 'pdf-lodop-print-template',

    POINTS_BILL: 'points-bill',
    PHARMACY_CASHIER: 'pharmacy-cashier',
}


export const PrintGroupMap = {
    [PrintGroupKeyEnum.INVENTORY]: {
        groupItems: [
            PrintBusinessKeyEnum.GOODS_PURCHASE,
            PrintBusinessKeyEnum.GOODS_IN,
            PrintBusinessKeyEnum.GOODS_OUT,
            PrintBusinessKeyEnum.GOODS_TRANS,
            PrintBusinessKeyEnum.GOODS_CHECK,
            PrintBusinessKeyEnum.GOODS_APPLY,
            PrintBusinessKeyEnum.SETTLEMENT_APPLICATION,
            PrintBusinessKeyEnum.SETTLEMENT_REVIEW,
        ],
    },
    [PrintGroupKeyEnum.PRESCRIPTION]: {
        groupItems: [
            PrintBusinessKeyEnum.PRESCRIPTION,
            PrintBusinessKeyEnum.MEDICAL_RECORD,
            PrintBusinessKeyEnum.CHARGE_LIST,
            PrintBusinessKeyEnum.HOSPITAL_PRESCRIPTION,
            PrintBusinessKeyEnum.CHARGE_SHIFT,
            PrintBusinessKeyEnum.HOSPITALIZATION_CERTIFICATE,
            PrintBusinessKeyEnum.PRESCRIPTION_V2,
            PrintBusinessKeyEnum.PRESCRIPTION_SHANGHAI,
            PrintBusinessKeyEnum.FAMILY_DOCTOR_AGREEMENT,
        ],
    },

    [PrintGroupKeyEnum.TICKET]: {
        groupItems: [
            PrintBusinessKeyEnum.CASHIER,
            PrintBusinessKeyEnum.CASHIER_A5,
            PrintBusinessKeyEnum.PHARMACY_CASHIER_V2,
            PrintBusinessKeyEnum.REGISTRATION,
            PrintBusinessKeyEnum.RECHARGE,
            PrintBusinessKeyEnum.MEDICAL_BILL_ANHUI,
            PrintBusinessKeyEnum.MEDICAL_BILL_CHENGDU,
            PrintBusinessKeyEnum.MEDICAL_BILL_CHONGQING,
            PrintBusinessKeyEnum.EXECUTE_CERTIFICATE,
            PrintBusinessKeyEnum.HOSPITAL_CASHIER,
        ],
    },

    [PrintGroupKeyEnum.TAG]: {
        groupItems: [
            PrintBusinessKeyEnum.MEDICINE_TAG,
            PrintBusinessKeyEnum.PATIENT_TAG,
            PrintBusinessKeyEnum.EXAMINATION_TAG,
            PrintBusinessKeyEnum.EXAMINATION_LABEL,
            PrintBusinessKeyEnum.MEDICINE_TAG_CUSTOMIZED,
            PrintBusinessKeyEnum.HOSPITAL_MEDICINE_TAG,
        ],
    },

    [PrintGroupKeyEnum.STAT]: {
        groupItems: [],
    },

    [PrintGroupKeyEnum.MEIDICAL_BILL]: {
        groupItems: [
            PrintBusinessKeyEnum.MEDICAL_BILL_ANHUI,
            PrintBusinessKeyEnum.MEDICAL_BILL_CHENGDU,
            PrintBusinessKeyEnum.MEDICAL_BILL_CHONGQING,
            PrintBusinessKeyEnum.MEDICAL_BILL_CHONGQING_NATIONAL,
            PrintBusinessKeyEnum.MEDICAL_BILL_GUANGDONG,
            PrintBusinessKeyEnum.MEDICAL_BILL_HEFEI,
            PrintBusinessKeyEnum.MEDICAL_BILL_HUBEI,
            PrintBusinessKeyEnum.MEDICAL_BILL_HUNAN,
            PrintBusinessKeyEnum.MEDICAL_BILL_JIANGSU,
            PrintBusinessKeyEnum.MEDICAL_BILL_LIAONING_NATIONAL,
            PrintBusinessKeyEnum.MEDICAL_BILL_NATIONAL,
            PrintBusinessKeyEnum.MEDICAL_BILL_QINGDAO,
            PrintBusinessKeyEnum.MEDICAL_BILL_SHANGHAI,
            PrintBusinessKeyEnum.MEDICAL_BILL_SHANXI,
            PrintBusinessKeyEnum.MEDICAL_BILL_SHNEYANG,
            PrintBusinessKeyEnum.MEDICAL_BILL_YUNAN,
            PrintBusinessKeyEnum.MEDICAL_BILL_YUNANOLD,
            PrintBusinessKeyEnum.MEDICAL_BILL_ZHEJIANG,
            PrintBusinessKeyEnum.MEDICAL_BILL_ZHEJIANG_NATIONAL,
            PrintBusinessKeyEnum.MEDICAL_BILL_GUANGXI,
            PrintBusinessKeyEnum.MEDICAL_BILL_HENAN,
            PrintBusinessKeyEnum.MEDICAL_BILL_TIANJIN,
            PrintBusinessKeyEnum.MEDICAL_BILL_GANSU,
            PrintBusinessKeyEnum.MEDICAL_FEE_SOCIAL,
            PrintBusinessKeyEnum.MEDICAL_BILL_WUHU,
            PrintBusinessKeyEnum.MEDICAL_BILL_GUANGDONG_NEW,
        ],
    },
}

export const ExternalPRUsageTypeEnum = Object.freeze({
    tieFu: 0, // 贴敷
    zhenCi: 1, // 针刺
    aiJiu: 2, // 艾灸
    baGuan: 3, // 拔罐
    guaSha: 4, // 刮痧
    tuiNa: 5, // 推拿
});


export const OutpatientChargeTypeEnum = Object.freeze({
    DEFAULT: 0, // 默认
    NO_CHARGE: 1, // 门诊标记自备，不参与划价
});


export const ExternalPRUsageTypeEnumRevert = Object.freeze({
    [ExternalPRUsageTypeEnum.tieFu]: '贴敷',
    [ExternalPRUsageTypeEnum.zhenCi]: '针刺',
    [ExternalPRUsageTypeEnum.aiJiu]: '艾灸',
    [ExternalPRUsageTypeEnum.baGuan]: '拔罐',
    [ExternalPRUsageTypeEnum.guaSha]: '刮痧',
    [ExternalPRUsageTypeEnum.tuiNa]: '推拿',

});

export const ClinicRoleEnum =  {
    DOCTOR : 1,             //医生
    NURSE : 2,              //护士
    EXAMINER : 3,           //检验师
    PHYSICAL_THERAPIST : 4, //理疗师
    DOCTOR_ASSISTANT : 5,   //医助
    OTHER : 6,              //其他
    OPTOMETRIST : 9,        //视光师
}

export const PsychotropicNarcoticTypeEnum = Object.freeze({
    NONE: 0,
    JING_1: 1,
    JING_2: 2,
    MA_ZUI: 3,
    DU: 4,
    GENERAL: 5, // 普通
    CHILD: 6, // 儿科
    CHRONIC: 7, // 慢病
    OLD: 8, // 老年病
    EMERGENCY: 9, // 急诊
    LONG_TERM: 10, // 长期
});

export const PsychotropicNarcoticTypeEnumStr = Object.freeze({
    [PsychotropicNarcoticTypeEnum.NONE]: '',
    [PsychotropicNarcoticTypeEnum.JING_1]: '精一',
    [PsychotropicNarcoticTypeEnum.JING_2]: '精二',
    [PsychotropicNarcoticTypeEnum.MA_ZUI]: '麻',
    [PsychotropicNarcoticTypeEnum.DU]: '毒',
    [PsychotropicNarcoticTypeEnum.GENERAL]: '普通',
    [PsychotropicNarcoticTypeEnum.CHILD]: '儿科',
    [PsychotropicNarcoticTypeEnum.CHRONIC]: '慢病',
    [PsychotropicNarcoticTypeEnum.OLD]: '老年病',
    [PsychotropicNarcoticTypeEnum.EMERGENCY]: '急诊',
    [PsychotropicNarcoticTypeEnum.LONG_TERM]: '长期',
});

export const HospitalPsychotropicNarcoticTypeEnum = Object.freeze({
    MA_ZUI: 2,
    JING_1: 3,
    JING_2: 4,
    DU: 5,
});

export const HospitalPsychotropicNarcoticTypeLabelEnum = Object.freeze({
    [HospitalPsychotropicNarcoticTypeEnum.MA_ZUI]: '麻醉',
    [HospitalPsychotropicNarcoticTypeEnum.JING_1]: '精一',
    [HospitalPsychotropicNarcoticTypeEnum.JING_2]: '精二',
    [HospitalPsychotropicNarcoticTypeEnum.DU]: '毒',
});

export const RegistType = {
    HAOYUANTYPE: 0, //号源模式
    LINGHUOTIMETYOE: 1, //灵活时间模式
}

// 号数确定时机
export const GENERATE_ORDER_NO_TIME_TYPE = {
    REGISTRATION: 0, // 预约取号
    SIGN_IN: 10, // 签到取号
};

// 预约时间
export const RESERVATION_TIME_TYPE = {
    ACCURATE: 1, // 精确时间预约
    OTHER: 0, // 其他(分段、上、下午、晚上)
};

// 检查设备类型
export const INSPECT_DEVICE_TYPE = {
    CT: 1,
    DR: 2,
    MR: 9,
    '彩超': 8,
    '心电图': 5,
    '内窥镜': 10,
    '其他': 13,
    '未知': 0,
    MDB: 6,
    NORMAL: 14,
    INTERNAL: 15,
    SURGERY: 16,
    ENT: 17,
    MOUTH: 18,
    EYE: 19,
    ASO: 21,
    GYNECOLOGY: 22,
    BODY_COMPOSITION: 24,
    MG: 25,
    C13_14:26,
};

// 检查设备类型
export const INSPECT_DEVICE_TYPE_TEXT = {
    [INSPECT_DEVICE_TYPE.CT]: 'CT',
    [INSPECT_DEVICE_TYPE.DR]: 'DR',
    [INSPECT_DEVICE_TYPE.MR]: 'MR',
    [INSPECT_DEVICE_TYPE['彩超']]: '彩色多普勒',
    [INSPECT_DEVICE_TYPE['心电图']]: '心电图',
    [INSPECT_DEVICE_TYPE['内窥镜']]: '内窥镜',
    [INSPECT_DEVICE_TYPE['其他']]: '其他检查',
    [INSPECT_DEVICE_TYPE['未知']]: '未知检查',
    [INSPECT_DEVICE_TYPE['MDB']]: '骨密度',
    [INSPECT_DEVICE_TYPE.NORMAL]: '一般检查',
    [INSPECT_DEVICE_TYPE.INTERNAL]: '内科检查',
    [INSPECT_DEVICE_TYPE.SURGERY]: '外科检查',
    [INSPECT_DEVICE_TYPE.ENT]: '耳鼻喉检查',
    [INSPECT_DEVICE_TYPE.MOUTH]: '口腔检查',
    [INSPECT_DEVICE_TYPE.EYE]: '眼科检查',
    [INSPECT_DEVICE_TYPE.ASO]: '动脉硬化',
    [INSPECT_DEVICE_TYPE.GYNECOLOGY]: '妇科检查',
    [INSPECT_DEVICE_TYPE.BODY_COMPOSITION]: '人体成分',
    [INSPECT_DEVICE_TYPE.MG]: 'MG',
    [INSPECT_DEVICE_TYPE.C13_14]: 'C13/C14',
};

export const ExamBusinessType = {
    Unknown: 0,
    Outpatient: 10,
    Cashier: 20,
    Hospital: 30,
    Examination: 40,
    Inspection: 50,
    PhysicalExamination: 200,
}

export const ExamBusinessTypeOptions = [
    { label: '未知',value: ExamBusinessType.Unknown },
    { label: '门诊',value: ExamBusinessType.Outpatient },
    { label: '收费',value: ExamBusinessType.Cashier },
    { label: '住院',value: ExamBusinessType.Hospital },
    { label: '检验',value: ExamBusinessType.Examination },
];


export const MDB_DIAGNOSIS_OPTION = Object.freeze([
    {
        label: '正常',
        url: 'https://static-common-cdn.abcyun.cn/img/lis/mdb-bone-1.png',
        value: '骨量正常',
    },
    {
        label: '减少',
        url: 'https://static-common-cdn.abcyun.cn/img/lis/mdb-bone-2.png',
        value: '骨量减少',
    },
    {
        label: '疏松',
        url: 'https://static-common-cdn.abcyun.cn/img/lis/mdb-bone-3.png',
        value: '骨质疏松',
    },
]);

export const MDB_RESULT_OPTION = [
    {
        name: 'T值', enName: 'T',
    },
    {
        name: 'SOS值', enName: 'SOS',
    },
    {
        name: 'Z值', enName: 'Z',
    },
];

export const MDB_RESULT_FILTER_KEY = ['position', 'diagnosis', 'checkResultStr', 'reportSheet'];

// MDB 相关
export const CIRCLE_RADIUS = 3;
export const MDB_SVG_WIDTH = 300;
export const MDB_SVG_HEIGHT = 200;
export const MDB_SVG_MARGIN_TOP = 5;
export const MDB_SVG_MARGIN_RIGHT = 20;
export const MDB_SVG_MARGIN_BOTTOM = 40;
export const MDB_SVG_MARGIN_LEFT = 40;

export const MDB_T_MIN = -5;
export const MDB_T_MAX = 3;
export const MDB_AGE_MAX = 100;
export const MDB_AGE_MIN = 20;

export const DASH_ARRAY = '4, 4';

export const MDB_RECT_DATA_SET = [
    {
        color: '#FF9700', x: 20, y: -2.5, h1: -5, h2: -2.5,
    },
    {
        color: '#FFE400', x: 20, y: -1, h1: -2.5, h2: -1,
    },
    {
        color: '#13D05D', x: 20, y: 3, h1: -1, h2: 3,
    },
];

export const EXAM_TYPE = Object.freeze({
    examination: 1,
    inspect: 2,
});

export const PromotionTypeEnum = Object.freeze({
    discount: 0, // 折扣
    special: 1, // 特价
    gift: 2, // 赠送
});

export const RuleTypeEnum = Object.freeze({
    fixed: 0, // 固定
    enoughN: 1, // 每买N件
    theN: 2, // 第N件
    gift: 3, // 买赠
});

// 初始化入库单类型
export const GoodsInInitTypeEnum = [3, 20, 21, 22];

// 初始化退货单类型
export const GoodsReturnInitTypeEnum = [12];
