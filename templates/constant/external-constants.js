export const ExternalPRUsageTypeEnum = Object.freeze({
    tieFu: 0, // 贴敷
    zhenCi: 1, // 针刺
    aiJiu: 2, // 艾灸
    baGuan: 3, // 拔罐
    guaSha: 4, // 刮痧
    tuiNa: 5, // 推拿
});
export const TieFuUsageSubTypeEnum = Object.freeze({
    chenPinTie: 0, // 成品贴
    xianPeiTie: 1, // 现配贴
});
export const ZhenCiUsageSubTypeEnum = Object.freeze({
    haoZhen: 0, // 毫针
    dianZhen: 1, // 电针
    sanLingZhen: 2, // 三棱针
    piFuZhen: 3, // 皮肤针
    meiHuaZhen: 4, // 梅花针
    qinZhen: 5, // 揿针
    piNeiZhen: 6, // 皮内针
    huoZhen: 7, // 火针
    touZhen: 8, // 头针
    erZhen: 9, // 耳针
    zhenDao: 10, // 针刀
    funZhen: 11, // 浮针
    maiXian: 12, // 埋线
    maiZhen: 13, // 埋针
});
export const AiJiuUsageSubTypeEnum = Object.freeze({
    aiZhuJiu: 0, // 艾炷灸
    aiTiaoJiu: 1, // 艾条灸
    wenZhenJiu: 2, // 温针灸
    wenZhiQiJiu: 3, // 温炙器灸
    puJiu: 4, // 铺灸
});

export const BaGuanUsageSubTypeEnum = Object.freeze({
    cupping: 0, // 火罐法
    waterCupping: 1, // 水罐法
    airCupping: 2, // 抽气罐法
});

export const TuiNaUsageSubTypeEnum = Object.freeze({
    adultTuiNa: 0, // 成人推拿
    childTuiNa: 1, // 小儿推拿
    fingerPoint: 2, // 手指点穴
});

export const UsageTypeOptions = [
    {
        value: ExternalPRUsageTypeEnum.tieFu,
        label: '贴敷',
    },
    {
        value: ExternalPRUsageTypeEnum.zhenCi,
        label: '针刺',
    },
    {
        value: ExternalPRUsageTypeEnum.aiJiu,
        label: '艾灸',
    },
    {
        value: ExternalPRUsageTypeEnum.baGuan,
        label: '拔罐',
    },
    {
        value: ExternalPRUsageTypeEnum.guaSha,
        label: '刮痧',
    },
    {
        value: ExternalPRUsageTypeEnum.tuiNa,
        label: '推拿',
    },
];

export const TieFuSubOptions = [
    {
        value: TieFuUsageSubTypeEnum.chenPinTie,
        label: '成品贴',
    },
    {
        value: TieFuUsageSubTypeEnum.xianPeiTie,
        label: '现配贴',
    },
];

export const ZhenCiSubOptions = [
    {
        value: ZhenCiUsageSubTypeEnum.haoZhen,
        label: '毫针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.dianZhen,
        label: '电针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.qinZhen,
        label: '揿针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.sanLingZhen,
        label: '三棱针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.piFuZhen,
        label: '皮肤针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.piNeiZhen,
        label: '皮内针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.meiHuaZhen,
        label: '梅花针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.huoZhen,
        label: '火针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.touZhen,
        label: '头针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.erZhen,
        label: '耳针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.zhenDao,
        label: '针刀',
    },
    {
        value: ZhenCiUsageSubTypeEnum.funZhen,
        label: '浮针',
    },
    {
        value: ZhenCiUsageSubTypeEnum.maiXian,
        label: '埋线',
    },
    {
        value: ZhenCiUsageSubTypeEnum.maiZhen,
        label: '埋针',
    },
];

export const AiJiuSubOptions = [
    {
        value: AiJiuUsageSubTypeEnum.aiZhuJiu,
        label: '艾炷灸',
    },
    {
        value: AiJiuUsageSubTypeEnum.aiTiaoJiu,
        label: '艾条灸',
    },
    {
        value: AiJiuUsageSubTypeEnum.wenZhenJiu,
        label: '温针灸',
    },
    {
        value: AiJiuUsageSubTypeEnum.wenZhiQiJiu,
        label: '温炙器灸',
    },
    {
        value: AiJiuUsageSubTypeEnum.puJiu,
        label: '铺灸',
    },
];

export const BaGuanSubOptions = [
    {
        value: BaGuanUsageSubTypeEnum.cupping,
        label: '火罐法',
    },
    {
        value: BaGuanUsageSubTypeEnum.waterCupping,
        label: '水罐法',
    },
    {
        value: BaGuanUsageSubTypeEnum.airCupping,
        label: '抽气罐法',
    },
];

export const TuiNaSubOptions = [
    {
        value: TuiNaUsageSubTypeEnum.adultTuiNa,
        label: '成人推拿',
    },
    {
        value: TuiNaUsageSubTypeEnum.childTuiNa,
        label: '小儿推拿',
    },
    {
        value: TuiNaUsageSubTypeEnum.fingerPoint,
        label: '手指点穴',
    },
];


export const ExternalAcupointTypeEnum = Object.freeze({
    ACUPOINT: 1,
    BODY_POSITION: 2,
});

