<!-- eslint-disable vue/no-v-html -->
<template>
    <div
        class="hospital-inspect-medical-wrapper"
        :class="`font-size-${fontSizeMode}`"
    >
        <template v-if="!isGastroscopy && !isMDB && !isASO && !isBodyComposition && !isC1314">
            <!--胃镜页眉因为镜型的原因单独处理-->
            <inspect-header
                :print-data="printData"
                data-type="header"
                :header-items="headerDisplayItems"
                :config="config"
                :layout-span-map="layoutSpanMap"
                :class="`font-size-${fontSizeMode}`"
            ></inspect-header>
        </template>

        <template v-if="isMDB || isASO || isBodyComposition || isC1314">
            <div data-type="header"></div>
        </template>

        <!-- 放射 -->
        <template v-if="isRadiology">
            <template v-for="(report, rIdx) in radiologyReportList">
                <div
                    v-for="(itemArr, index) in report.imageList"
                    :key="`hospital-inspect-report-img-list-${index}`"
                    :class="[
                        'report-img-list',
                    ]"
                    data-type="block"
                >
                    <div
                        v-for="(item, idx) in itemArr"
                        :key="`hospital-inspect-radio-img-${idx}`"
                        class="report-img"
                        :style="item.style"
                    >
                        <img
                            :src="item.url"
                            alt=""
                        />
                    </div>
                </div>

                <div
                    v-for="(item, i) in report.resultItemList"
                    :key="`hospital-inspect-radio-img-list-${i}`"
                    :style="{ 'min-height': item.height }"
                    class="result-block"
                    :data-type="item.fixedBottom ? 'fixed-bottom-box' : 'block'"
                >
                    <div class="title">
                        {{ item.label }}
                    </div>
                    <div
                        class="report-desc desc-info"
                        data-type="text"
                    >
                        <abc-html :value="item.value"></abc-html>
                    </div>
                </div>

                <div
                    v-if="rIdx !== gastroscopyReportList.length - 1"
                    :key="`radiology-split-page-${rIdx}`"
                    data-type="new-page"
                ></div>
            </template>
        </template>

        <!-- 彩超 -->
        <template v-if="isCDU">
            <template v-for="(report, cIdx) in cduReportList">
                <div
                    v-for="(itemArr, index) in report.imageList"
                    :key="`hospital-inspect-cdu-img-list-${index}`"
                    :class="[
                        'report-img-list',
                    ]"
                    data-type="block"
                >
                    <div
                        v-for="(item, idx) in itemArr"
                        :key="`hospital-inspect-cdu-img-${idx}`"
                        class="report-img"
                        :style="item.style"
                    >
                        <img
                            :src="item.url"
                            alt=""
                        />
                    </div>
                </div>

                <div
                    v-for="(item, i) in report.resultItemList"
                    :key="`hospital-inspect-cdu-item-${i}`"
                    :style="{ 'min-height': item.height }"
                    class="result-block"
                    :data-type="item.fixedBottom ? 'fixed-bottom-box' : 'block'"
                >
                    <div class="title">
                        {{ item.label }}
                    </div>
                    <div class="report-desc desc-info">
                        <abc-html :value="item.value"></abc-html>
                    </div>
                </div>

                <div
                    v-if="cIdx !== gastroscopyReportList.length - 1"
                    :key="`cdu-split-page-${cIdx}`"
                    data-type="new-page"
                ></div>
            </template>
        </template>

        <!-- 心电图 -->
        <template v-if="isECG">
            <div class="report-img is-full">
                <img
                    :src="ECGOrASOOrMDBImage"
                    alt=""
                />
            </div>

            <div
                v-for="(item, i) in ecgResultItemList"
                :key="`hospital-inspect-ecg-img-list-${i}`"
                data-type="block"
                :style="{ 'min-height': item.height }"
                class="result-block"
            >
                <div class="title">
                    {{ item.label }}
                </div>
                <div
                    class="report-desc desc-info"
                    data-type="text"
                >
                    <abc-html :value="item.value"></abc-html>
                </div>
            </div>
        </template>

        <!-- 动脉硬化 & 骨密度 & 人体成分-->
        <template v-if="isASO || isBodyComposition || isMDB || isC1314">
            <div class="report-img super-full">
                <img
                    :src="ECGOrASOOrMDBImage"
                    alt=""
                />
            </div>

            <div
                v-for="(item, i) in ecgResultItemList"
                :key="`hospital-inspect-ecg-img-list-${i}`"
                data-type="block"
                class="result-block"
            >
                <div class="title">
                    {{ item.label }}
                </div>
                <div
                    class="report-desc desc-info"
                    data-type="text"
                >
                    <abc-html :value="item.value"></abc-html>
                </div>
            </div>
        </template>

        <!-- 胃镜 -->
        <template v-if="isGastroscopy">
            <template v-for="(report, gIdx) in gastroscopyReportList">
                <inspect-header
                    :key="`gastroscopy-header-${gIdx}`"
                    :print-data="printData"
                    data-type="header"
                    :header-items="headerDisplayItems"
                    :config="config"
                    :data-pendants-index="gIdx"
                    :device-model-desc="report.deviceModelDesc"
                    :layout-span-map="layoutSpanMap"
                    :class="`font-size-${fontSizeMode}`"
                ></inspect-header>

                <div
                    v-for="(itemArr, index) in report.imageList"
                    :key="`hospital-inspect-gastroscopy-img-list-${index}`"
                    :class="[
                        'report-img-list',
                        'gastroscopy-img-list',
                    ]"
                    data-type="block"
                >
                    <div
                        v-for="(item, idx) in itemArr"
                        :key="`hospital-inspect-gastroscopy-img-${idx}`"
                        class="report-img"
                        :style="item.style"
                    >
                        <img
                            :src="item.url"
                            alt=""
                        />

                        <div class="report-img-name">
                            {{ item.name }}
                        </div>
                    </div>
                </div>

                <div
                    v-for="(item, i) in report.resultItemList"
                    :key="`hospital-inspect-gastroscopy-item-${i}`"
                    data-type="block"
                    :style="{ 'min-height': item.height }"
                    class="result-block"
                >
                    <div class="title">
                        {{ item.label }}
                    </div>
                    <div
                        class="report-desc desc-info"
                        data-type="text"
                    >
                        <abc-html :value="item.value"></abc-html>
                    </div>
                </div>

                <div
                    :key="`gastroscopy-custom-footer-${gIdx}`"
                    data-type="fixed-bottom-box"
                    class="gastroscopy-custom__footer"
                >
                    <abc-print-row :style="gastroscopyRenderItemStyle">
                        <abc-print-col
                            :span="10"
                            overflow
                            style="align-items: flex-start"
                        >
                            <span class="label">活检部位：</span>
                            <span class="value">{{ report.inspectionSite || '' }}</span>
                        </abc-print-col>

                        <abc-print-col
                            :span="14"
                            style="align-items: flex-start"
                            overflow
                        >
                            <span class="label">医生建议：</span>
                            <span class="value">{{ report.suggestion || '' }}</span>
                        </abc-print-col>
                    </abc-print-row>
                </div>

                <inspect-footer
                    :key="`gastroscopy-footer-${gIdx}`"
                    :print-data="printData"
                    :footer-items="footerDisplayItems"
                    :config="config"
                    data-type="footer"
                    :data-pendants-index="gIdx"
                ></inspect-footer>
            </template>
        </template>

        <!-- 一般检查 -->
        <template v-if="isNormal">
            <div style="margin-top: 8pt;"></div>

            <abc-table
                :columns="normalColumns"
                :list="normalList"
                border
            ></abc-table>

            <div
                data-type="block"
                class="result-block"
            >
                <div class="title">
                    {{ normalDiagnosisItem.label }}
                </div>
                <div
                    class="report-desc desc-info"
                    data-type="text"
                >
                    <span>{{ normalDiagnosisItem.value }}</span>
                </div>
            </div>
        </template>

        <!-- 临床检查（不含一般检查）检查打印字段 -->
        <template v-if="isClinical">
            <div style="margin-top: 8pt;"></div>
            <abc-table
                :columns="clinicalColumns"
                :list="clinicalList"
                border
            ></abc-table>

            <div
                data-type="block"
                class="result-block"
            >
                <div class="title">
                    {{ clinicalDiagnosisItem.label }}
                </div>
                <div
                    class="report-desc desc-info"
                    data-type="text"
                >
                    <span>{{ clinicalDiagnosisItem.value }}</span>
                </div>
            </div>
        </template>

        <!--胃镜页尾因为镜型的原因单独处理-->
        <template v-if="!isGastroscopy">
            <inspect-footer
                :print-data="printData"
                :footer-items="footerDisplayItems"
                :config="config"
                data-type="footer"
            ></inspect-footer>
        </template>
    </div>
</template>

<script>
    import { ExamBusinessType, INSPECT_DEVICE_TYPE, PrintBusinessKeyEnum } from './constant/print-constant.js'
    import PageSizeMap, { Orientation } from '../share/page-size.js'
    import { isPDF, parseTime } from './common/utils.js'

    import InspectHeader from './components/hospital-inspect/header.vue'
    import InspectFooter from './components/hospital-inspect/footer.vue'
    import AbcTable from './components/layout/abc-table.vue'
    import {
        CDUReportMixin,
        ClinicalReportMixin,
        ECGReportMixin,
        GastroscopyReportMixin,
        MDBReportMixin,
        NormalReportMixin,
        RadiologyReportMixin,
    } from './components/hospital-inspect/mixin/index.js'
    import AbcHtml from './components/layout/abc-html.vue'
    import AbcPrintCol from './components/layout/abc-layout/abc-col.vue'
    import AbcPrintRow from './components/layout/abc-layout/abc-row.vue'
    import HospitalInspectDataHandler from './data-handler/hospital-inspect'

    const defaultInspectReport = {
        'style': {
            'fontSize': 0, // 字体大小 0:小号 1:中号 2:大号
        },
        content: {
            patientName: 1, // 姓名
            patientSex: 1, //性别
            patientAge: 1, //年龄
            patientOrderNo: 1, // 门诊/住院/体检号
            inspectionOrderNo: 1, //检查单号
            inspectionApplyNo: 1,//检查编号
            applyDepartment: 1, //申请科室
            applyDoctor: 1, //申请医生
            applyDate: 1, //申请日期
            clinicalDiagnosis: 1, //临床诊断
            inspectionSite: 1, //检查部位
            inspectionHeader: "", //检查标题
            conclusionHeader: "", //结论标题
        },
        footer: {
            operateDoctor: 1, //操作医师
            recordDoctor: 1, //记录医师
            inspectionTime: 1, // 检查时间
            auditTime: 1, // 审核时间
            reportTime: 1, // 报告时间
            remark: "本检查结果仅反应受检查者当时的情况，仅供临床医生诊断时参考。", // 页尾备注
        },
        header: {
            barcode: 0,
            subtitleUseProductName: 1,
            logo: 1,
            title: "",
            isCustomLogo: 0,
            inspectionDevice: 1,
            assistantTitleUseProductName: 1,
        },
    };

    export default {
        name: "HospitalInspect",

        DataHandler: HospitalInspectDataHandler,

        businessKey: PrintBusinessKeyEnum.HOSPITAL_INSPECT,

        components: {
            AbcPrintRow,
            AbcPrintCol,
            AbcHtml,
            InspectFooter,
            InspectHeader,
            AbcTable,
        },

        mixins: [
            CDUReportMixin,
            ClinicalReportMixin,
            GastroscopyReportMixin,
            NormalReportMixin,
            RadiologyReportMixin,
            ECGReportMixin,
            MDBReportMixin,
        ],

        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },

        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],

        computed: {
            config() {
                const reportConfigEnum = Object.freeze({
                    [INSPECT_DEVICE_TYPE["彩超"]]:
                        this.renderData.config?.medicalDocuments?.cdusReport || {},
                    [INSPECT_DEVICE_TYPE.CT]:
                        this.renderData.config?.medicalDocuments?.ctReport || {},
                    [INSPECT_DEVICE_TYPE.DR]:
                        this.renderData.config?.medicalDocuments?.drReport || {},
                    [INSPECT_DEVICE_TYPE.MG]:
                        this.renderData.config?.medicalDocuments?.mgReport || {},
                    [INSPECT_DEVICE_TYPE.MR]:
                        this.renderData.config?.medicalDocuments?.mrReport || {},
                    [INSPECT_DEVICE_TYPE["内窥镜"]]:
                        this.renderData.config?.medicalDocuments?.endoscopeReport || {},
                });
                return reportConfigEnum[this.deviceType] || defaultInspectReport || {};
            },

            fontSizeMode() {
                const fontSizeMap = ['small', 'medium', 'large'];
                return fontSizeMap[this.config?.style?.fontSize || 0];
            },

            printData() {
                return this.renderData.printData;
            },

            reportList() {
                return [
                    this.printData.examinationSheetReport,
                    ...(this.printData.additionalExaminationSheetReports || []),
                ].filter(report => {
                    if(report.isNeedPrint !== undefined) {
                        return report.isNeedPrint
                    }
                    return true;
                });
            },

            imageFileList() {
                const imageFileList = this.printData.examinationSheetReport.imageFiles
                    .filter(item => !isPDF(item.url))
                    .map(item => {
                        item.url = this.formatOssImgUrl(item.url);
                        return item;
                    });

                if (imageFileList && imageFileList.length) {
                    const res = [];
                    let tempRes = [];
                    imageFileList.forEach((item, index) => {
                        if (tempRes.length === 3) {
                            res.push(tempRes);
                            tempRes = [];
                        }

                        tempRes.push(item);

                        if (index === imageFileList.length - 1 && tempRes.length) {
                            res.push(tempRes);
                        }
                    });

                    return res;
                }
                return [];
            },

            businessType() {
                return this.printData.businessType;
            },

            deviceType() {
                return this.printData.deviceType;
            },

            patient() {
                return this.printData.patient || {};
            },

            organ() {
                return this.printData.organ;
            },

            organTitle() {
                return (
                    (this.organ &&
                        this.organ.medicalDocumentsTitle &&
                        this.organ.medicalDocumentsTitle.inspection) ||
                    ""
                );
            },

            isRenderInspectionSite() {
                return [
                    INSPECT_DEVICE_TYPE.CT,
                    INSPECT_DEVICE_TYPE.DR,
                    INSPECT_DEVICE_TYPE.MR,
                    INSPECT_DEVICE_TYPE.MG,
                    INSPECT_DEVICE_TYPE['彩超'],
                    INSPECT_DEVICE_TYPE['内窥镜'],
                ].includes(+this.printData.deviceType);
            },

            layoutSpanMap() {
                let spanMap = [10, 7, 7];
                const isLargeFontSizeModel = this.fontSizeMode === 'large';
                // 字体模式为大的时候，布局为 1:1:1
                if(isLargeFontSizeModel) {
                    spanMap = [8, 8, 8];
                }
                return spanMap;
            },

            headerDisplayItems() {
                const diagnosisText = this.formatDentistry2Text(
                    this.printData.extendDiagnosisInfos || [],
                );
                const contentConfig = this.config.content || {};

                const outpatientHeader = [
                    {
                        label: "门诊号",
                        value: this.printData.patientOrderNumber,
                        isHidden: !contentConfig.patientOrderNo,
                    },
                    {
                        label: "检查单号",
                        value: this.printData.orderNo,
                        isHidden: !contentConfig.inspectionOrderNo,
                    },
                    {
                        label: "检查编号",
                        value: this.printData.examinationApplySheetNo,
                        isHidden: !contentConfig.inspectionApplyNo,
                    },
                    {
                        label: "申请科室",
                        value: this.printData.departmentName,
                        isHidden: !contentConfig.applyDepartment,
                    },
                    {
                        label: "申请医生",
                        value: this.printData.doctorName,
                        isHidden: !contentConfig.applyDoctor,
                    },
                    {
                        label: "申请日期",
                        value: parseTime(this.printData.created, "y-m-d h:i", true),
                        isHidden: !contentConfig.applyDate,
                    },
                    {
                        label: "临床诊断",
                        value: diagnosisText,
                        isHidden: !contentConfig.clinicalDiagnosis,
                    },
                    {
                        label: '检查部位',
                        value: this.printData.name,
                        isHidden: !contentConfig.inspectionSite || !this.isRenderInspectionSite,
                        labelStyle: {
                            fontWeight: 400,
                        },
                    },
                ];

                const inHospitalHeader = [
                    {
                        label: "病区",
                        value: this.printData.wardAreaName,
                    },
                    {
                        label: "床位号",
                        value: this.printData.bedNumber,
                    },
                    {
                        label: "住院号",
                        value: this.printData.patientOrderNumber,
                        isHidden: !contentConfig.patientOrderNo,
                    },
                    {
                        label: "检查单号",
                        value: this.printData.orderNo,
                        isHidden: !contentConfig.inspectionOrderNo,
                    },
                    {
                        label: "检查编号",
                        value: this.printData.examinationApplySheetNo,
                        isHidden: !contentConfig.inspectionApplyNo,
                    },
                    {
                        label: "申请科室",
                        value: this.printData.departmentName,
                        isHidden: !contentConfig.applyDepartment,
                    },
                    {
                        label: "申请日期",
                        value: parseTime(this.printData.created, "y-m-d h:i", true),
                        isHidden: !contentConfig.applyDate,
                    },
                    {
                        label: "临床诊断",
                        value: diagnosisText,
                        isHidden: !contentConfig.clinicalDiagnosis,
                    },
                    {
                        label: '检查部位',
                        value: this.printData.name,
                        isHidden: !contentConfig.inspectionSite || !this.isRenderInspectionSite,
                        labelStyle: {
                            fontWeight: 400,
                        },
                    },
                ];

                const physicalExaminationHeader = [
                    {
                        label: "检查单号",
                        value: this.printData.orderNo,
                        isHidden: !contentConfig.inspectionOrderNo,
                    },
                    {
                        label: "检查编号",
                        value: this.printData.examinationApplySheetNo,
                        isHidden: !contentConfig.inspectionApplyNo,
                    },
                    {
                        label: "申请科室",
                        value: this.printData.departmentName,
                        isHidden: !contentConfig.applyDepartment,
                    },
                    {
                        label: "体检号",
                        value: this.printData.peSheetSimpleView?.no,
                        isHidden: !contentConfig.patientOrderNo,
                    },
                    {
                        label: "申请日期",
                        value: parseTime(this.printData.created, "y-m-d h:i", true),
                        isHidden: !contentConfig.applyDate,
                    },
                    {
                        label: '检查部位',
                        value: this.printData.name,
                        isHidden: !contentConfig.inspectionSite || !this.isRenderInspectionSite,
                        labelStyle: {
                            fontWeight: 400,
                        },
                    },
                ];

                const map = {
                    [ExamBusinessType.Outpatient]: outpatientHeader,
                    [ExamBusinessType.Unknown]: outpatientHeader,
                    [ExamBusinessType.Cashier]: outpatientHeader,
                    [ExamBusinessType.Examination]: outpatientHeader,
                    [ExamBusinessType.Hospital]: inHospitalHeader,
                    [ExamBusinessType.Inspection]: outpatientHeader,
                    [ExamBusinessType.PhysicalExamination]: physicalExaminationHeader,
                };

                const spanMap = this.layoutSpanMap;

                const headerItems =  (map[this.businessType] || outpatientHeader).filter(
                    item => !item.isHidden,
                );


                return headerItems.map((item, idx) => {
                    item.span = spanMap[idx % 3];

                    if(item.label === '检查部位') {
                        item.span = 24;
                    } else if(item.label === '临床诊断') {
                        const isC3 = idx % 3 === 2;

                        item.span = isC3 ? 24 : item.span;
                    }

                    return item;
                });
            },

            footerDisplayItems() {
                const map = {
                    [INSPECT_DEVICE_TYPE.CT]: this.radiologyFooterItems,
                    [INSPECT_DEVICE_TYPE.DR]: this.radiologyFooterItems,
                    [INSPECT_DEVICE_TYPE.MG]: this.radiologyFooterItems,
                    [INSPECT_DEVICE_TYPE.MR]: this.radiologyFooterItems,
                    [INSPECT_DEVICE_TYPE["内窥镜"]]: this.gastroscopyFooterItems,
                    [INSPECT_DEVICE_TYPE["彩超"]]: this.cduFooterItems,
                    [INSPECT_DEVICE_TYPE["心电图"]]: this.ecgFooterItems,
                    [INSPECT_DEVICE_TYPE.C13_14]: this.ecgFooterItems,
                    [INSPECT_DEVICE_TYPE.ASO]: this.asoFooterItems,
                    [INSPECT_DEVICE_TYPE.BODY_COMPOSITION]: this.asoFooterItems,
                    [INSPECT_DEVICE_TYPE.NORMAL]: this.normalFooterItems,
                    [INSPECT_DEVICE_TYPE.INTERNAL]: this.clinicalFooterItems,
                    [INSPECT_DEVICE_TYPE.SURGERY]: this.clinicalFooterItems,
                    [INSPECT_DEVICE_TYPE.GYNECOLOGY]: this.clinicalFooterItems,
                    [INSPECT_DEVICE_TYPE.ENT]: this.clinicalFooterItems,
                    [INSPECT_DEVICE_TYPE.MOUTH]: this.clinicalFooterItems,
                    [INSPECT_DEVICE_TYPE.MDB]: this.mdbFooterItems,
                };

                const spanMap = this.layoutSpanMap;

                return (map[this.deviceType] || [])
                    .filter(item => !item.isHidden)
                    .map((item, idx) => {
                        item.span = spanMap[idx % 3];
                        return item;
                    });
            },

            gastroscopyRenderItemStyle() {
                // 胃肠镜 活检部位、医生建议只打两行
                const fontLineHeightMap = {
                    small: 18,
                    medium: 21,
                    large: 24,
                }

                return {
                    maxHeight: fontLineHeightMap[this.fontSizeMode] * 2 + 'px',
                    overflow: 'hidden',
                    alignItems: 'flex-start',
                }
            },
        },

        methods: {
            formatDentistry2Text(arr) {
                if (!arr) return "";
                const result = [];
                arr.forEach(item => {
                    if (item.value) {
                        let str = "";
                        if (item.toothNos && item.toothNos.length) {
                            str += `${this.formatToothNos2Text(item.toothNos)} `;
                        }
                        if (Array.isArray(item.value)) {
                            str += item.value.map(it => it.name).join("，");
                        } else {
                            str += item.value;
                        }
                        result.push(str);
                    }
                });
                return result.join("，");
            },

            formatToothNos2Text(toothNos) {
                if (!toothNos) return "";
                return toothNos
                    .slice()
                    .sort((a, b) => a - b)
                    .join("、");
            },

            generateImageRowList(images, step = 3) {
                const styleMap = [
                    {
                        width: '49%',
                        height: '184px',
                    },
                    {
                        width: 'calc(calc(100% - 12px) / 2)',
                        height: '184px',
                    },
                    {
                        width: 'calc(calc(100% - 24px) / 3)',
                        height: '150px',
                    },
                    {
                        width: 'calc(calc(100% - 36px) / 4)',
                        height: '150px',
                    },
                ];

                images.forEach((item) => {
                    if(images.length >= 4) {
                        item.style = styleMap[3];
                    } else {
                        item.style = styleMap[images.length % 4 - 1];
                    }
                    return item;
                });

                if (images && images.length) {
                    const res = [];
                    let tempRes = [];
                    images.forEach((item, index) => {
                        if (tempRes.length === step) {
                            res.push(tempRes);
                            tempRes = [];
                        }
                        tempRes.push(item);
                        if (index === images.length - 1 && tempRes.length) {
                            res.push(tempRes);
                        }
                    });

                    return res;
                }
                return [];
            },

            formatOssImgUrl(src) {
                if(!src) return '';
                return src.split('?')[0];
            },
        },
    };
</script>

<style lang="scss">
.abc-page-content {
    box-sizing: border-box;
}

.hospital-inspect-medical-wrapper {
    font-size: 13px;
    font-weight: 300;
    line-height: 18px;

    &.font-size-medium {
        font-size: 16px;
        line-height: 21px;

        .result-block {
            .title {
                font-size: 18px;
            }
        }
    }

    &.font-size-large {
        font-size: 18px;
        line-height: 24px;

        .result-block {
            .title {
                font-size: 20px;
            }
        }
    }

    .report-img-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
        margin-top: 12px;

        &.gastroscopy-img-list {
            .report-img {
                position: relative;
                box-sizing: border-box;

                .report-img-name {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    font-size: 10pt;
                    line-height: 12pt;
                    text-align: center;
                }

                img {
                    height: calc(100% - 12pt);
                }
            }
        }
    }

    .report-img {
        font-size: 0;

        &.is-full {
            width: 100%;
            height: 145mm;
            max-height: none;
            margin-top: 12pt;
        }

        &.super-full {
            width: 100%;
            height: 210mm;
        }

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    .result-block {
        padding-top: 16px;

        .title {
            padding: 0;
            margin-bottom: 8px;
            font-size: 15px;
            font-weight: 400;
        }
    }

    .result-block + .result-block {
        padding-top: 20px;
    }

    .report-desc {
        white-space: pre-wrap;
        padding-left: 16px;
    }

    .diagnosis-advice-item {
        padding-top: 6pt;
        margin-top: 6pt;
        border-top: 0.5pt dashed #a6a6a6;

        .diagnosis-advice-title {
            font-size: 10pt;
            font-weight: 400;
            color: #000000;
        }
    }

    .gastroscopy-custom__footer {
        padding: 8pt 0;
        border-bottom: 1pt solid #000000;

        .label {
            flex-shrink: 0;
            font-weight: bold;
        }

        .value {
            word-break: break-all;
        }
    }
}
</style>
