<!--exampleData
{
  "patient": {
    "id": "ffffffff00000000288fb6280fa72000",
    "name": "许赵飞",
    "namePy": null,
    "namePyFirst": null,
    "birthday": "1999-08-05",
    "mobile": "15445435436",
    "countryCode": null,
    "sex": "男",
    "idCard": null,
    "isMember": 1,
    "age": {
      "year": 24,
      "month": 4,
      "day": 28
    },
    "address": null,
    "sn": null,
    "remark": null,
    "profession": null,
    "company": null,
    "patientSource": null,
    "tags": null,
    "marital": null,
    "weight": null,
    "ethnicity": null,
    "wxOpenId": null,
    "wxHeadImgUrl": null,
    "wxNickName": null,
    "wxBindStatus": null,
    "isAttention": null,
    "appFlag": null,
    "shebaoCardInfo": null,
    "childCareInfo": null,
    "chronicArchivesInfo": null
  },
  "organ": {
    "id": "fff730ccc5ee45d783d82a85b8a0e52d",
    "name": "高新大原店",
    "shortName": "高新大原店",
    "addressDetail": "1231234",
    "contactPhone": "0851-8511132",
    "logo": null,
    "category": null,
    "hisType": 0,
    "medicalDocumentsTitle": null
  },
  "dispensingForms": [
    {
      "dispensingFormItems": [
        {
          "name": "T孟鲁司特钠片(顺尔宁)",
          "unit": "盒",
          "count": 2,
          "specialRequirement": null,
          "formatSpec": "5mg*5片/盒",
          "position": "",
          "manufacturer": "杭州默沙东制药有限公司",
          "productType": 1,
          "productSubType": 1,
          "sourceItemType": 0,
          "usageInfo": {
            "ast": null,
            "days": null,
            "freq": null,
            "ivgtt": null,
            "usage": null,
            "dosage": null,
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": null,
            "usageDays": null,
            "usageType": null,
            "dosageUnit": null,
            "usageLevel": null,
            "dailyDosage": null,
            "glassesType": null,
            "isDecoction": false,
            "requirement": null,
            "finishedRate": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "glassesParams": null,
            "optometristId": null,
            "processRemark": null,
            "specification": null,
            "processBagUnit": null,
            "finishedRateMin": null,
            "externalUnitCount": null,
            "totalProcessCount": null,
            "executedTotalCount": null,
            "specialRequirement": "",
            "processBagUnitCount": 0,
            "verifySignatureStatus": 0,
            "processBagUnitCountDecimal": 0
          },
          "receivedPrice": 0
        }
      ],
      "sourceFormType": 4,
      "processUsageInfo": null,
      "dispensingFormId": "ffffffff0000000034aaccf11bf78002",
      "pharmacyNo": 0,
      "pharmacyType": 0,
      "processBagUnitCount": 0,
      "processBagUnitCountDecimal": 0,
      "totalProcessCount": null,
      "processRemark": null,
      "processedStatus": 0,
      "auditName": null,
      "auditId": null,
      "compoundName": null,
      "compoundId": null,
      "isCombineForm": 0,
      "receivedPrice": 0,
      "cMSpec": null
    }
  ],
  "chargedByName": "宁铁桥",
  "chargedTime": "2024-01-02T07:08:57Z",
  "sellerName": null,
  "dispensedByName": "宁铁桥",
  "dispensedTime": "2024-01-02T07:09:10Z",
  "auditName": null,
  "auditId": null,
  "compoundName": null,
  "compoundId": null,
  "isDecoction": false,
  "contactMobile": null,
  "doctorName": null,
  "patientOrderNo": "",
  "netIncomeFee": 0,
  "deliveryInfo": {
    delivery: "22222"
  },
  "departmentName": null,
  "diagnose": "",
  "doctorAdvice": null,
  "barcode": "00024527-01",
  "pharmacyNo": 0,
  "pharmacyType": 0,
  "pharmacyName": "住院药房1",
  "openPharmacyFlag": 20,
  "processedStatus": 0,
  "deliveredStatus": 0,
  "isPatientSelfPay": 0,
  "receivedPrice": 0
}
-->

<template>
    <dispensing-component-a5
        :render-data="renderData"
        :is-undispense="isUndispense"
    >
    </dispensing-component-a5>
</template>

<script>
    import DispensingComponentA5 from './components/dispensing/dispensing-component-A5.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import DispensingHandler from './data-handler/dispensing-handler';

    export default {
        name: 'A5PrintDispensing',
        components: {
            DispensingComponentA5,
        },
        DataHandler: DispensingHandler,
        businessKey: PrintBusinessKeyEnum.DISPENSE_A5,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: {
                type: Object,
            },
            extra: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        computed: {
            isUndispense() {
                return !!this.extra.isUndispense;
            }
        },
    };
</script>
<style lang="scss">
@import 'style/reset.scss';
@import "./components/layout/print-layout";
* {
    font-family: "Microsoft YaHei", "微软雅黑";
}

.abc-page-content{
    padding: 8pt;
    box-sizing: border-box;
    overflow: hidden;
    font-family: "Microsoft YaHei", "微软雅黑";
}


.print-dispensing-usage-wrapper {
    display: flex;
    align-items: flex-start;
    padding: 6pt 0;
    font-size: 10pt;
    line-height: 14pt;
    border-top: 1pt dashed #000000;

    .label {
        width: 32pt;
        min-width: 32pt;
        max-width: 32pt;
        font-weight: 400;
    }

    .content {
        flex: 1;
        font-size: 10pt;

        .item {
            font-size: 9pt;
            line-height: 11pt;
        }
    }
    .has-margin {
        margin-bottom: 3pt;
    }
}
</style>
