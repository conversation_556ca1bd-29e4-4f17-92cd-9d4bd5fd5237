<!--exampleData
{
  "id": "ffffffff000000001d6f3f8806944000",
  "patient": {
    "id": "ffffffff000000001d6f9c200605e000",
    "name": "任盈盈",
    "namePy": "renyingying",
    "namePyFirst": "RYY",
    "birthday": "1999-12-15",
    "mobile": "13900000000",
    "sex": "女",
    "idCard": null,
    "isMember": 1,
    "age": {
      "year": 22,
      "month": 0,
      "day": 6
    },
    "address": null,
    "sn": "000881",
    "remark": null,
    "profession": null,
    "company": null,
    "patientSource": null,
    "tags": [],
    "marital": null,
    "weight": null,
    "wxOpenId": null,
    "wxHeadImgUrl": null,
    "wxNickName": null,
    "wxBindStatus": 0,
    "isAttention": 0,
    "shebaoCardInfo": null,
    "childCareInfo": null,
    "chronicArchivesInfo": null
  },
  "patientOrderNo": 6577,
  "organ": {
    "id": "9daeca0eab074c639892433ffef36f94",
    "name": "成都高新秉正堂中医门诊部",
    "shortName": "成都高新秉正堂中医门",
    "addressDetail": "惠民佳苑北路东南侧西部122号别墅路110室从火车北站下车后，往南走10公里，左转第二个红绿灯路口右转，前方100米就到了",
    "contactPhone": "",
    "logo": "https://cis-images-test.oss-cn-shanghai.aliyuncs.com/headimg_test/jVlBR6MsFsWZ9Nm3ShdLbE8CazFmpcpD_1603681040002.png",
    "category": "妇幼保健院"
  },
  "departmentName": "内科",
  "diagnosis": "急性上呼吸道感染",
  "doctorAdvice": "1.饮食规律宜清淡，忌烟酒，忌辛辣荤腥<br>2.多喝水，保持身体充足水分<br>3.少吃油炸、腌制、生冷、麻辣等刺激性食物",
  "syndrome": "",
  "medicalRecord": {
    "chiefComplaint": "咳嗽，夜咳，咽痛，咽干",
    "pastHistory": "既往体健",
    "familyHistory": "否认家族遗传病史",
    "presentHistory": "①头痛头晕；②失眠多梦；③噩梦<br>已经持续5年，久病不治，多方寻医未果，尝试过中西医各种疗法",
    "physicalExamination": "扁桃体肿大，咽部充血，颈淋巴结肿大",
    "diagnosis": "急性上呼吸道感染",
    "doctorAdvice": "1.饮食规律宜清淡，忌烟酒，忌辛辣荤腥<br>2.多喝水，保持身体充足水分<br>3.少吃油炸、腌制、生冷、麻辣等刺激性食物",
    "syndrome": "",
    "therapy": "",
    "chineseExamination": "",
    "birthHistory": null,
    "oralExamination": "[{\"positions\":[{\"position\":\"top-left\",\"dataNo\":[\"5\"]}],\"describes\":[\"冷诊正常\"]},{\"positions\":[{\"position\":\"top-right\",\"dataNo\":[\"2\"]}],\"describes\":[\"残冠\"]}]",
    "epidemiologicalHistory": "{\"patientChecked\":true,\"attendantChecked\":false,\"suspiciousList\":[{\"label\":\"发热\",\"value\":\"无\"},{\"label\":\"干咳\",\"value\":\"无\"},{\"label\":\"乏力\",\"value\":\"无\"},{\"label\":\"鼻塞\",\"value\":\"无\"},{\"label\":\"流涕\",\"value\":\"无\"},{\"label\":\"咽痛\",\"value\":\"无\"},{\"label\":\"肌痛\",\"value\":\"无\"},{\"label\":\"腹泻\",\"value\":\"无\"},{\"label\":\"结膜炎\",\"value\":\"无\"},{\"label\":\"嗅觉味觉减退\",\"value\":\"无\"}],\"symptomList\":[{\"label\":\"患者可疑症状排查：\",\"isSuspicious\":true}]}",
    "auxiliaryExamination": null,
    "obstetricalHistory": "[{\"type\":\"pregnant\",\"birthCount\":1,\"pregnantCount\":1},{\"type\":\"menstruation\",\"menophaniaAge\":13,\"menstruationDays\":[5],\"menstrualCycle\":[28],\"menopauseTab\":1,\"menopauseDate\":\"2021-12-19\",\"menopauseAge\":\"\"}]",
    "chinesePrescription": null,
    "dentistryExaminations": null,
    "dentistryDiagnosisInfos": null,
    "treatmentPlans": null,
    "disposals": null
  },
  "healthCardNo": null,
  "healthCardPayLevel": null,
  "doctorName": "徐彩云",
  "auditName": "徐彩云",
  "compoundName": "徐彩云",
  "dispensedByName": "徐彩云",
  "doctorSignImgUrl": "https://cis-images-test.oss-cn-shanghai.aliyuncs.com/prescription-sign/NarAakyglQKg3dbdZKyuQ5ew6cel8z2d_1681267817006",
  "diagnosedDate": "2021-12-15T03:41:58Z",
  "totalPrice": 1074.2300,
  "medicineTotalPrice": 171.23,
  "dispensedBy": null,
  "auditBy": null,
  "revisitStatus": null,
  "barcode": null,
  "shebaoCardInfo": {
      "feeType": "自费",
  },
  "productForms": [
    {
      "id": "ffffffff000000001d6fb8b006942000",
      "sort": 0,
      "sourceFormType": 2,
      "productFormItems": [
        {
          "id": "ffffffff000000001d6fb8b006942001",
          "productId": "ffffffff000000001d6fa16806a8c000",
          "name": "超敏C反应蛋白测定",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 20.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 20.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 0,
          "type": 3,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942002",
          "productId": "ffffffff000000001d6fa0e806a8c000",
          "name": "全血细胞分析",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 20.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 20.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 1,
          "type": 3,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942003",
          "productId": "5ad7300943824a78b353e74d97f682da",
          "name": "肺炎支原体抗体",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 50.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 50.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 2,
          "type": 3,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        }
      ]
    },
    {
      "id": "ffffffff000000001d6fb8b006942004",
      "sort": 1,
      "sourceFormType": 3,
      "productFormItems": [
        {
          "id": "ffffffff000000001d6fb8b006942005",
          "productId": "ffffffff000000001d70afa006a94000",
          "name": "骨伤、颈腰整脊手法",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 100.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 100.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 0,
          "type": 4,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942006",
          "productId": "455311173fe3484da26d6e45d85bf192",
          "name": "三位一体单次",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 168.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 168.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 1,
          "type": 4,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942007",
          "productId": "816f47cb74f343979e31f6acfb025805",
          "name": "穴位埋线（普通）",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 35.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 35.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 2,
          "type": 4,
          "subType": 2,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        }
      ]
    },
    {
      "id": "ffffffff000000001d70a9b006942000",
      "sort": 2,
      "sourceFormType": 11,
      "productFormItems": [
        {
          "id": "ffffffff000000001d70a9b006942001",
          "productId": "ffffffff000000001d70a5b806a96000",
          "name": "综合调养套餐",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 500.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 500.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 0,
          "type": 11,
          "subType": 1,
          "composeType": 1,
          "composeParentFormItemId": null,
          "composeChildren": [
            {
              "id": "ffffffff000000001d70a9b006942002",
              "productId": "17f37531a01a4471a76b4a1dc7e45738",
              "name": "穴位贴敷",
              "unitCount": 5.000,
              "unit": "次",
              "unitPrice": 20.0000,
              "fractionPrice": 0.0000,
              "sourceUnitPrice": null,
              "expectedUnitPrice": null,
              "useDismounting": 0,
              "sort": 0,
              "type": 4,
              "subType": 2,
              "composeType": 2,
              "composeParentFormItemId": "ffffffff000000001d70a9b006942001",
              "composeChildren": [],
              "chargeStatus": 1,
              "days": 1,
              "remark": null
            },
            {
              "id": "ffffffff000000001d70a9b006942003",
              "productId": "c49fb73100694d84b78ff57bed7bdd2f",
              "name": "电针灸",
              "unitCount": 5.000,
              "unit": "次",
              "unitPrice": 60.0000,
              "fractionPrice": 0.0000,
              "sourceUnitPrice": null,
              "expectedUnitPrice": null,
              "useDismounting": 0,
              "sort": 1,
              "type": 4,
              "subType": 1,
              "composeType": 2,
              "composeParentFormItemId": "ffffffff000000001d70a9b006942001",
              "composeChildren": [],
              "chargeStatus": 1,
              "days": 1,
              "remark": null
            },
            {
              "id": "ffffffff000000001d70a9b006942004",
              "productId": "ba9379e2403442788cacb63927083fd5",
              "name": "推拿",
              "unitCount": 5.000,
              "unit": "次",
              "unitPrice": 20.0000,
              "fractionPrice": 0.0000,
              "sourceUnitPrice": null,
              "expectedUnitPrice": null,
              "useDismounting": 0,
              "sort": 2,
              "type": 4,
              "subType": 1,
              "composeType": 2,
              "composeParentFormItemId": "ffffffff000000001d70a9b006942001",
              "composeChildren": [],
              "chargeStatus": 1,
              "days": 1,
              "remark": null
            }
          ],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        }
      ]
    }
  ],
  "prescriptionWesternForms": [
    {
      "id": "ffffffff000000001d6f9c2806944000",
      "type": 1,
      "specification": "",
      "doseCount": 1,
      "dailyDosage": "",
      "usage": "",
      "freq": "",
      "requirement": "",
      "usageLevel": "",
      "sort": 0,
      "isDecoction": false,
      "usageType": null,
      "usageSubType": null,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": null,
      "auditBy": null,
      "auditName": null,
      "contactMobile": null,
      "totalPrice": 83.67,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001d6f9c2806944001",
          "goodsId": "ffffffff000000001d6f606006a8e000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿奇霉素颗粒",
          "name": "阿奇霉素颗粒",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "0.1",
          "dosageUnit": "g",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 1.6667,
          "useDismounting": 1,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "包",
          "unitPrice": 1.6667,
          "costUnitPrice": 0.0000,
          "sort": 0,
          "groupId": 1,
          "productInfo": {
            "id": "ffffffff000000001d6f606006a8e000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "阿奇霉素颗粒",
            "displaySpec": "0.1g*6包/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 6,
            "pieceUnit": "包",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "阿奇霉素颗粒",
            "medicineDosageNum": 0.1,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": "",
            "piecePrice": 1.6667,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:05Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 1.6667,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 1.66670,
          "sourceTotalPrice": 1.66670
        },
        {
          "id": "ffffffff000000001d6f9c2806944002",
          "goodsId": "f0d781e0399d4dacada32773bacf75f6",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "氨溴特罗口服溶液",
          "name": "氨溴特罗口服溶液(易坦静)",
          "specification": "",
          "manufacturer": "北京韩美",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "5",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 28.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 28.0000,
          "costUnitPrice": 0.0000,
          "sort": 1,
          "groupId": 1,
          "productInfo": {
            "id": "f0d781e0399d4dacada32773bacf75f6",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "易坦静",
            "displayName": "氨溴特罗口服溶液 (易坦静)",
            "displaySpec": "60ml*60ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "北京韩美",
            "pieceNum": 6E+1,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "氨溴特罗口服溶液",
            "medicineNmpn": "H20040317",
            "medicineDosageNum": 6E+1,
            "medicineDosageUnit": "ml",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0,
            "packagePrice": 28,
            "packageCostPrice": 1,
            "inTaxRat": 1E+1,
            "outTaxRat": 2E+1,
            "stockPieceCount": 0,
            "stockPackageCount": 1.1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-01-29T09:28:43Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20389,
            "chainPackagePrice": 28,
            "chainPiecePrice": 0,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1.1E+2,
            "manufacturerFull": "北京韩美药品有限公司",
            "medicineDosageForm": "溶液剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 28.00000,
          "sourceTotalPrice": 28.00000
        },
        {
          "id": "ffffffff000000001d6f9c2806944003",
          "goodsId": "ffffffff000000001d6f5ed806a8e000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 3,
          "medicineCadn": "四季抗病毒合剂",
          "name": "四季抗病毒合剂",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "5",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 10.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 10.0000,
          "costUnitPrice": 0.0000,
          "sort": 2,
          "groupId": 1,
          "productInfo": {
            "id": "ffffffff000000001d6f5ed806a8e000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "四季抗病毒合剂",
            "displaySpec": "120ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 16,
            "type": 1,
            "subType": 3,
            "pieceNum": 1.2E+2,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 1,
            "medicineCadn": "四季抗病毒合剂",
            "extendSpec": "",
            "position": "",
            "piecePrice": 0.0833,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 7,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:10Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 0.0833,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001d6f9c2806944004",
          "goodsId": "ffffffff000000001d6f5a1806a8c000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "小柴胡颗粒",
          "name": "小柴胡颗粒",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "tid",
          "dosage": "1",
          "dosageUnit": "包",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 10.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 10.0000,
          "costUnitPrice": 0.0000,
          "sort": 3,
          "groupId": 2,
          "productInfo": {
            "id": "ffffffff000000001d6f5a1806a8c000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "小柴胡颗粒",
            "displaySpec": "10g*10包/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 1E+1,
            "pieceUnit": "包",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "小柴胡颗粒",
            "medicineDosageNum": 1E+1,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": "",
            "piecePrice": 1,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:19Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 1,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001d6f9c2806944005",
          "goodsId": "ffffffff000000001d6f5b4806a8e000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "感冒灵颗粒(999)",
          "name": "感冒灵颗粒(999)",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "tid",
          "dosage": "1",
          "dosageUnit": "包",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 10.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 10.0000,
          "costUnitPrice": 0.0000,
          "sort": 4,
          "groupId": 2,
          "productInfo": {
            "id": "ffffffff000000001d6f5b4806a8e000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "感冒灵颗粒(999)",
            "displaySpec": "10g*9包/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 9,
            "pieceUnit": "包",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "感冒灵颗粒(999)",
            "medicineDosageNum": 1E+1,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": "",
            "piecePrice": 1.1111,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:14Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 1.1111,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001da6940006958000",
          "goodsId": "bccd2d2a2576480db2e7b1982742463a",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿莫西林分散片",
          "name": "阿莫西林分散片(阿赫林)",
          "specification": "",
          "manufacturer": "石药集团",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "片",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 24.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 24.0000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "bccd2d2a2576480db2e7b1982742463a",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "阿赫林",
            "displayName": "阿莫西林分散片 (阿赫林)",
            "displaySpec": "0.5g*13片/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "barCode": "6936292110261",
            "manufacturer": "石药集团",
            "pieceNum": 13,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "阿莫西林分散片",
            "medicineNmpn": "H20046510",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 24,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 9,
            "stockPackageCount": 3E+1,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "936944bc62d04d65ab8239e30a85e5f6",
            "lastModifiedDate": "2021-11-19T06:53:01Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20384,
            "chainPackagePrice": 24,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 9,
            "packageCount": 3E+1,
            "manufacturerFull": "石药集团中诺药业(石家庄)有限公司",
            "medicineDosageForm": "片剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 24.00000,
          "sourceTotalPrice": 24.00000
        },
        {
          "id": "ffffffff000000001da6940006958000",
          "goodsId": "bccd2d2a2576480db2e7b1982742463a",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿莫西林分散片",
          "name": "阿莫西林分散片(阿赫林)",
          "specification": "",
          "manufacturer": "石药集团",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "片",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 24.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 24.0000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "bccd2d2a2576480db2e7b1982742463a",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "阿赫林",
            "displayName": "阿莫西林分散片 (阿赫林)",
            "displaySpec": "0.5g*13片/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "barCode": "6936292110261",
            "manufacturer": "石药集团",
            "pieceNum": 13,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "阿莫西林分散片",
            "medicineNmpn": "H20046510",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 24,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 9,
            "stockPackageCount": 3E+1,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "936944bc62d04d65ab8239e30a85e5f6",
            "lastModifiedDate": "2021-11-19T06:53:01Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20384,
            "chainPackagePrice": 24,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 9,
            "packageCount": 3E+1,
            "manufacturerFull": "石药集团中诺药业(石家庄)有限公司",
            "medicineDosageForm": "片剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 24.00000,
          "sourceTotalPrice": 24.00000
        },
        {
          "id": "ffffffff000000001da6940006958000",
          "goodsId": "bccd2d2a2576480db2e7b1982742463a",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿莫西林分散片",
          "name": "阿莫西林分散片(阿赫林)",
          "specification": "",
          "manufacturer": "石药集团",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "片",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 24.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 24.0000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "bccd2d2a2576480db2e7b1982742463a",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "阿赫林",
            "displayName": "阿莫西林分散片 (阿赫林)",
            "displaySpec": "0.5g*13片/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "barCode": "6936292110261",
            "manufacturer": "石药集团",
            "pieceNum": 13,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "阿莫西林分散片",
            "medicineNmpn": "H20046510",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 24,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 9,
            "stockPackageCount": 3E+1,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "936944bc62d04d65ab8239e30a85e5f6",
            "lastModifiedDate": "2021-11-19T06:53:01Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20384,
            "chainPackagePrice": 24,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 9,
            "packageCount": 3E+1,
            "manufacturerFull": "石药集团中诺药业(石家庄)有限公司",
            "medicineDosageForm": "片剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 24.00000,
          "sourceTotalPrice": 24.00000
        },
        {
          "id": "ffffffff000000001da6940006958000",
          "goodsId": "bccd2d2a2576480db2e7b1982742463a",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿莫西林分散片",
          "name": "阿莫西林分散片(阿赫林)",
          "specification": "",
          "manufacturer": "石药集团",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "片",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 24.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 24.0000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "bccd2d2a2576480db2e7b1982742463a",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "阿赫林",
            "displayName": "阿莫西林分散片 (阿赫林)",
            "displaySpec": "0.5g*13片/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "barCode": "6936292110261",
            "manufacturer": "石药集团",
            "pieceNum": 13,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "阿莫西林分散片",
            "medicineNmpn": "H20046510",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 24,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 9,
            "stockPackageCount": 3E+1,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "936944bc62d04d65ab8239e30a85e5f6",
            "lastModifiedDate": "2021-11-19T06:53:01Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20384,
            "chainPackagePrice": 24,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 9,
            "packageCount": 3E+1,
            "manufacturerFull": "石药集团中诺药业(石家庄)有限公司",
            "medicineDosageForm": "片剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 24.00000,
          "sourceTotalPrice": 24.00000
        },
        {
          "id": "ffffffff000000001da6940006958000",
          "goodsId": "bccd2d2a2576480db2e7b1982742463a",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿莫西林分散片",
          "name": "阿莫西林分散片(阿赫林)",
          "specification": "",
          "manufacturer": "石药集团",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "片",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 24.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 24.0000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "bccd2d2a2576480db2e7b1982742463a",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "阿赫林",
            "displayName": "阿莫西林分散片 (阿赫林)",
            "displaySpec": "0.5g*13片/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "barCode": "6936292110261",
            "manufacturer": "石药集团",
            "pieceNum": 13,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "阿莫西林分散片",
            "medicineNmpn": "H20046510",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 24,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 9,
            "stockPackageCount": 3E+1,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "936944bc62d04d65ab8239e30a85e5f6",
            "lastModifiedDate": "2021-11-19T06:53:01Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20384,
            "chainPackagePrice": 24,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 9,
            "packageCount": 3E+1,
            "manufacturerFull": "石药集团中诺药业(石家庄)有限公司",
            "medicineDosageForm": "片剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 24.00000,
          "sourceTotalPrice": 24.00000
        },
        {
          "id": "ffffffff000000001da6940006958000",
          "goodsId": "bccd2d2a2576480db2e7b1982742463a",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿莫西林分散片",
          "name": "阿莫西林分散片(阿赫林)",
          "specification": "",
          "manufacturer": "石药集团",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "片",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 24.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "unitPrice": 24.0000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "bccd2d2a2576480db2e7b1982742463a",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "阿赫林",
            "displayName": "阿莫西林分散片 (阿赫林)",
            "displaySpec": "0.5g*13片/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "barCode": "6936292110261",
            "manufacturer": "石药集团",
            "pieceNum": 13,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "阿莫西林分散片",
            "medicineNmpn": "H20046510",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 24,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 9,
            "stockPackageCount": 3E+1,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "936944bc62d04d65ab8239e30a85e5f6",
            "lastModifiedDate": "2021-11-19T06:53:01Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20384,
            "chainPackagePrice": 24,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 9,
            "packageCount": 3E+1,
            "manufacturerFull": "石药集团中诺药业(石家庄)有限公司",
            "medicineDosageForm": "片剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 24.00000,
          "sourceTotalPrice": 24.00000
        }
      ],
      "processUsageInfo": "",
      "cMSpec": null
    },
    {
      "id": "ffffffff000000001da65c8806956000",
      "type": 1,
      "specification": "",
      "doseCount": 1,
      "dailyDosage": "",
      "usage": "",
      "freq": "",
      "requirement": "",
      "usageLevel": "",
      "sort": 1,
      "isDecoction": false,
      "usageType": null,
      "usageSubType": null,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": null,
      "auditBy": null,
      "auditName": null,
      "contactMobile": null,
      "totalPrice": 0.03,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001da65c8806956001",
          "goodsId": "fac01ed90db048d1997975a60123473b",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "卤米松乳膏",
          "name": "卤米松乳膏(澳能)",
          "specification": "",
          "manufacturer": "澳美制药",
          "ast": 0,
          "usage": "外用",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "支",
          "days": 3,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.0100,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 3.000,
          "unit": "支",
          "unitPrice": 0.0100,
          "costUnitPrice": 0.0000,
          "sort": 0,
          "groupId": null,
          "productInfo": {
            "id": "fac01ed90db048d1997975a60123473b",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "澳能",
            "displayName": "卤米松乳膏 (澳能)",
            "displaySpec": "5g*支/支",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "澳美制药",
            "pieceNum": 1,
            "pieceUnit": "支",
            "packageUnit": "支",
            "dismounting": 0,
            "medicineCadn": "卤米松乳膏",
            "medicineNmpn": "HC20150049",
            "medicineDosageNum": 5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 0.01,
            "packageCostPrice": 13,
            "inTaxRat": 1E+1,
            "outTaxRat": 2E+1,
            "stockPieceCount": 0,
            "stockPackageCount": 6,
            "lastPackageCostPrice": 13,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "fdd7cbf2524e4c929dbb99f14f04f178",
            "lastModifiedDate": "2021-03-30T11:49:43Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20388,
            "chainPackagePrice": 0.01,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 6,
            "manufacturerFull": "澳美制药厂",
            "medicineDosageForm": "膏剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 13,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.03000,
          "sourceTotalPrice": 0.03000
        }
      ],
      "processUsageInfo": "",
      "cMSpec": null
    }
  ],
  "prescriptionInfusionForms": [
    {
      "id": "ffffffff000000001d6f9c2806944006",
      "type": 2,
      "specification": "",
      "doseCount": 1,
      "dailyDosage": "",
      "usage": "",
      "freq": "",
      "requirement": "",
      "usageLevel": "",
      "sort": 0,
      "isDecoction": false,
      "usageType": null,
      "usageSubType": null,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": null,
      "auditBy": null,
      "auditName": null,
      "contactMobile": null,
      "totalPrice": 79.01,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001da65a7806956000",
          "goodsId": "ffffffff000000001c0a8b18067c8000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "氯化钠注射液9%",
          "name": "氯化钠注射液9%(双鹤)",
          "specification": "",
          "manufacturer": "四川科伦",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "250",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 3.0099,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 3.0099,
          "costUnitPrice": 0.0000,
          "sort": 0,
          "groupId": 1,
          "productInfo": {
            "id": "ffffffff000000001c0a8b18067c8000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "双鹤",
            "displayName": "氯化钠注射液9% (双鹤)",
            "displaySpec": "2.25g*250ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "四川科伦",
            "pieceNum": 2.5E+2,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "氯化钠注射液9%",
            "medicineNmpn": "H51021157",
            "medicineDosageNum": 2.25,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": null,
            "piecePrice": null,
            "packagePrice": 3.0099,
            "packageCostPrice": 2,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 2E+1,
            "lastPackageCostPrice": 2,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff0000000015549900040fc000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:12:26Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 3.0099,
            "chainPackageCostPrice": 2,
            "pieceCount": 0,
            "packageCount": 2E+1,
            "manufacturerFull": "四川科伦药业股份有限公司",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 2,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 3.00990,
          "sourceTotalPrice": 3.00990
        },
        {
          "id": "ffffffff000000001da65a7806956001",
          "goodsId": "217886ad6a554d8aba492affc786c65e",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "克林霉素磷酸酯注射液",
          "name": "克林霉素磷酸酯注射液(森迪)",
          "specification": "",
          "manufacturer": "山东方明药业",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "1.2",
          "dosageUnit": "g",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 7.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 4.000,
          "unit": "支",
          "unitPrice": 7.0000,
          "costUnitPrice": 0.0000,
          "sort": 1,
          "groupId": 1,
          "productInfo": {
            "id": "217886ad6a554d8aba492affc786c65e",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "森迪",
            "displayName": "克林霉素磷酸酯注射液 (森迪)",
            "displaySpec": "0.3g*2ml/支",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "山东方明药业",
            "pieceNum": 2,
            "pieceUnit": "ml",
            "packageUnit": "支",
            "dismounting": 0,
            "medicineCadn": "克林霉素磷酸酯注射液",
            "medicineNmpn": "H20045520",
            "medicineDosageNum": 0.3,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": null,
            "packagePrice": 7,
            "packageCostPrice": 0.71,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+1,
            "lastPackageCostPrice": 0.71,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:13:54Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 7,
            "chainPackageCostPrice": 3.5,
            "pieceCount": 0,
            "packageCount": 1E+1,
            "manufacturerFull": "山东方明药业集团股份有限公司",
            "medicineDosageForm": "注射剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.71,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 28.00000,
          "sourceTotalPrice": 28.00000
        },
        {
          "id": "ffffffff000000001da65a7806956002",
          "goodsId": "bd62c0ef60924a468590cd3b5b8e717f",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "葡萄糖氯化钠注射液",
          "name": "葡萄糖氯化钠注射液(科伦)",
          "specification": "",
          "manufacturer": "四川科伦",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "500",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "好好休息",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 5.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 2.000,
          "unit": "瓶",
          "unitPrice": 5.0000,
          "costUnitPrice": 0.0000,
          "sort": 2,
          "groupId": 2,
          "productInfo": {
            "id": "bd62c0ef60924a468590cd3b5b8e717f",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "科伦",
            "displayName": "葡萄糖氯化钠注射液 (科伦)",
            "displaySpec": "12.5g*250ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "四川科伦",
            "pieceNum": 2.5E+2,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "葡萄糖氯化钠注射液",
            "medicineNmpn": "H51020630",
            "medicineDosageNum": 12.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": null,
            "packagePrice": 5,
            "packageCostPrice": 1.2,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+1,
            "lastPackageCostPrice": 1.2,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:14:18Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 5,
            "chainPackageCostPrice": 2.5,
            "pieceCount": 0,
            "packageCount": 1E+1,
            "manufacturerFull": "四川科伦药业股份有限公司",
            "medicineDosageForm": "注射剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1.2,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001da65a7806956003",
          "goodsId": "0bb6e3b137664f8e92d5931e6df4ceb2",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "维生素C注射液",
          "name": "维生素C注射液",
          "specification": "",
          "manufacturer": "西南药业",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "15",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 1.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 8.000,
          "unit": "支",
          "unitPrice": 1.0000,
          "costUnitPrice": 0.0000,
          "sort": 3,
          "groupId": 2,
          "productInfo": {
            "id": "0bb6e3b137664f8e92d5931e6df4ceb2",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "维生素C注射液",
            "displaySpec": "0.5g*2ml/支",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "西南药业",
            "pieceNum": 2,
            "pieceUnit": "ml",
            "packageUnit": "支",
            "dismounting": 0,
            "medicineCadn": "维生素C注射液",
            "medicineNmpn": "H50021469",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0,
            "packagePrice": 1,
            "packageCostPrice": 0.27,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+1,
            "lastPackageCostPrice": 0.27,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "912d9293bfe94ba8a4857c29debf7a98",
            "lastModifiedUserId": "912d9293bfe94ba8a4857c29debf7a98",
            "lastModifiedDate": "2019-02-11T09:09:41Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 1,
            "chainPiecePrice": 0,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+1,
            "manufacturerFull": "西南药业股份有限公司",
            "medicineDosageForm": "注射剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.27,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 8.00000,
          "sourceTotalPrice": 8.00000
        },
        {
          "id": "ffffffff000000001da65a7806956004",
          "goodsId": "ffffffff000000001d6f957006a8c000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "维生素B6注射液",
          "name": "维生素B6注射液(科伦)",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "100",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 30.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 30.0000,
          "costUnitPrice": 0.0000,
          "sort": 4,
          "groupId": 2,
          "productInfo": {
            "id": "ffffffff000000001d6f957006a8c000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "科伦",
            "displayName": "维生素B6注射液 (科伦)",
            "displaySpec": "100ml*瓶/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 1,
            "pieceUnit": "瓶",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "维生素B6注射液",
            "medicineDosageNum": 1E+2,
            "medicineDosageUnit": "ml",
            "extendSpec": "",
            "position": "",
            "piecePrice": null,
            "packagePrice": 3E+1,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 9.999999E+7,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:14:56Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 3E+1,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 0,
            "packageCount": 9.999999E+7,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 30.00000,
          "sourceTotalPrice": 30.00000
        }
      ],
      "processUsageInfo": "",
      "cMSpec": null
    }
  ],
  "prescriptionExternalForms": [
{
"id": "ffffffff00000000347eefde82ca8000",
"type": 4,
"specification": "",
"doseCount": 0,
"dailyDosage": "",
"usage": "",
"freq": "",
"requirement": "",
"usageLevel": "",
"sort": 0,
"isDecoction": false,
"processBagUnitCount": 0,
"processRemark": null,
"totalProcessCount": null,
"usageType": 0,
"usageSubType": 1,
"processPrice": null,
"ingredientPrice": null,
"usageDays": null,
"auditBy": null,
"auditName": null,
"contactMobile": null,
"totalPrice": 720,
"deliveryInfo": null,
"processUsageInfo": null,
"chargeStatus": 0,
"created": "2023-04-11T03:03:17Z",
"pharmacyType": 0,
"pharmacyNo": 0,
"pharmacyName": "本地药房",
"psychotropicNarcoticType": 6,
"chargedByName": null,
"chargedByHandSign": null,
"prescriptionFormItems": [
{
"id": "ffffffff00000000347eefde82ca8001",
"goodsId": "ffffffff000000001c9f444010a86000",
"domainMedicineId": "",
"type": 4,
"subType": 1,
"medicineCadn": "",
"name": "输液费（首组）",
"specification": "",
"manufacturer": "",
"ast": null,
"usage": "",
"ivgtt": 0,
"ivgttUnit": "",
"freq": "1日1次",
"dosage": "2",
"dosageUnit": "",
"days": 0,
"specialRequirement": "贴敷16小时",
"fractionPrice": 0,
"sourceUnitPrice": 20,
"totalPrice": 720,
"pharmacyType": 0,
"pharmacyNo": 0,
"pharmacyName": "本地药房",
"useDismounting": 0,
"cMSpec": null,
"unitCount": 36,
"unit": "贴",
"unitPrice": 20,
"costUnitPrice": 0,
"sort": 0,
"groupId": null,
"productInfo": {
"goodsVersion": 1,
"sourceFlag": 1,
"id": "ffffffff000000001c9f444010a86000",
"goodsId": null,
"status": 1,
"name": "输液费（首组）",
"displayName": "输液费（首组）",
"displaySpec": "次",
"organId": "6a869c22abee4ffbaef3e527bbb70aeb",
"typeId": 22,
"type": 4,
"subType": 1,
"pieceNum": 1,
"pieceUnit": null,
"packageUnit": "次",
"dismounting": 0,
"medicineCadn": "",
"extendSpec": "",
"position": null,
"piecePrice": null,
"packagePrice": 20,
"packageCostPrice": 0,
"inTaxRat": 0,
"outTaxRat": 0,
"dispGoodsCount": "0次",
"dispStockGoodsCount": "0次",
"dispOutGoodsCount": "0次",
"dispProhibitGoodsCount": "0次",
"dispLockingGoodsCount": "0次",
"needExecutive": 1,
"shortId": "60100001246",
"composeUseDismounting": 0,
"composeSort": 0,
"createdUserId": "6e45706922a74966ab51e4ed1e604641",
"lastModifiedUserId": "00000000000000000000000000000000",
"lastModifiedDate": "2022-07-14T07:46:52Z",
"combineType": 0,
"medicalFeeGrade": 0,
"disable": 0,
"chainDisable": 0,
"v2DisableStatus": 0,
"chainV2DisableStatus": 0,
"disableSell": 0,
"customTypeName": "其他治疗",
"isSell": 1,
"customTypeId": 1004343,
"chainPackagePrice": 20,
"chainPackageCostPrice": 0,
"chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
"supplier": null,
"pharmacyType": 0,
"pharmacyNo": 0,
"defaultInOutTax": 0,
"shebaoPayMode": 0,
"cMSpec": ""
},
"acupoints": [
{
"id": "6336458264991175146",
"name": "侠白",
"position": "双",
"type": 1
},
{
"id": "6339718703514587626",
"name": "孔最",
"position": "双",
"type": 1
},
{
"id": "6820869630394044906",
"name": "膝关",
"position": "双",
"type": 1
},
{
"id": "6814586780434764266",
"name": "太冲",
"position": "双",
"type": 1
},
{
"id": "6828870123674341866",
"name": "急脉",
"position": "双",
"type": 1
},
{
"id": "6824021406244868586",
"name": "阴包",
"position": "双",
"type": 1
},
{
"id": "6822441373676016106",
"name": "曲泉",
"position": "双",
"type": 1
},
{
"id": "6816153069108269546",
"name": "中封",
"position": "双",
"type": 1
},
{
"id": "6817721333466730986",
"name": "蠡沟",
"position": "双",
"type": 1
}
],
"externalGoodsItems": [
{
"keyId": "559e6cb5a9234c8d8aafdd21a6c2fe25",
"goodsId": "329690e75c6e417d85d145bb64cc3733",
"medicineCadn": "白芷",
"name": "白芷",
"manufacturer": "四川中庸药业有限公司",
"type": 1,
"subType": 2,
"unitCount": 2,
"unit": "g",
"useDismounting": 0,
"sort": 0,
"cMSpec": null
},
{
"keyId": "818be22ce8fb409f85736449928b3b29",
"goodsId": "a076bd2e3105d3aadc621ad89803a972",
"medicineCadn": "茯苓111",
"name": "茯苓111",
"manufacturer": "四川中庸药业有限公司",
"type": 1,
"subType": 2,
"unitCount": 2,
"unit": "g",
"useDismounting": 0,
"sort": 0,
"cMSpec": null
},
{
"keyId": "f5f3442f34a84db78ec8cb3fc5a90df5",
"goodsId": "775bb6d6c7281142254445536d66016d",
"medicineCadn": "白术",
"name": "白术",
"manufacturer": "四川中庸药业有限公司",
"type": 1,
"subType": 2,
"unitCount": 3,
"unit": "g",
"useDismounting": 0,
"sort": 0,
"cMSpec": null
},
{
"keyId": "a5b5c8e4f1c741bc9cb8616675782e4c",
"goodsId": "ffffffff000000000efba6500983a000",
"medicineCadn": "番泻叶",
"name": "番泻叶",
"manufacturer": null,
"type": 1,
"subType": 2,
"unitCount": 2,
"unit": "g",
"useDismounting": 0,
"sort": 0,
"cMSpec": null
},
{
"keyId": "2225cdc075634f178bd3d2814856a2f1",
"goodsId": "6f4fee0b33fe40e6ba2642c00fef81b7",
"medicineCadn": "玄参",
"name": "玄参",
"manufacturer": "四川中庸药业有限公司",
"type": 1,
"subType": 2,
"unitCount": 2,
"unit": "g",
"useDismounting": 0,
"sort": 0,
"cMSpec": null
},
{
"keyId": "3f0e95ef89ed44858d84e10106eecc33",
"goodsId": "3eece47bca324e2d8c9092cc6e300346",
"medicineCadn": "大青叶",
"name": "大青叶",
"manufacturer": "四川中庸药业有限公司",
"type": 1,
"subType": 2,
"unitCount": 2,
"unit": "g",
"useDismounting": 0,
"sort": 0,
"cMSpec": null
},
{
"keyId": "9eb6bd3349a14b8a9e9fba3487a78695",
"goodsId": "5af3449aa9384157820afd1c54db035f",
"medicineCadn": "栀子1",
"name": "栀子1",
"manufacturer": "四川中庸药业有限公司",
"type": 1,
"subType": 2,
"unitCount": 2,
"unit": "g",
"useDismounting": 0,
"sort": 0,
"cMSpec": null
},
{
"keyId": "286d305a5b42400e8a254ba21955afe4",
"goodsId": "37a0d47a412843f782cc5238ebd0745b",
"medicineCadn": "陈皮",
"name": "陈皮",
"manufacturer": "四川中庸药业有限公司",
"type": 1,
"subType": 2,
"unitCount": 2,
"unit": "g",
"useDismounting": 0,
"sort": 0,
"cMSpec": null
}
],
"payType": null,
"verifySignatureStatus": 0,
"verifySignatures": null,
"chargeType": 0,
"chargeStatus": 0,
"astResult": null,
"externalUnitCount": 1,
"sourceItemType": 0
}
],
"cMSpec": null
}]
}
-->
<template>
    <div>
        <template v-for="(form, formIndex) in westernForms">
            <template v-for="(splitFormItems, pageIndex) in form.splitPrescriptionFormItems">
                <div class="tianjin-western-prescription-wrapper">
                    <western-tpl
                        :organ-title="curOrganTitle"
                        :department-name="departmentName"
                        :patient-name="patient.name"
                        :patient-sex="patient.sex"
                        :patient-age="formatAge(patient.age, { monthYear: 12, dayYear: 1 })"
                        :diagnosis="diagnosis"
                        :diagnosis-date="parseTime(printData.diagnosedDate, 'd', true)"
                        :diagnosis-month="parseTime(printData.diagnosedDate, 'm', true)"
                        :diagnosis-year="parseTime(printData.diagnosedDate, 'y', true)"
                        :patient-order-no="printData.patientOrderNo"
                        :health-card-pay-level="printData.healthCardPayLevel"
                        :doctor-name="printData.doctorName"
                        :audit-name="printData.auditName"
                        :compound-name="printData.compoundName"
                        :doctor-sign-img-url="printData.doctorSignImgUrl"
                        :audit-hand-sign="printData.auditHandSign"
                        :compound-by-hand-sign="printData.compoundByHandSign"
                        :total-price="form.totalPrice"
                    ></western-tpl>

                    <div class="pr-content">
                        <print-row
                            v-for="formItem in splitFormItems"
                            class="medicine-tr"
                        >
                            <print-col span="1">
                                <div
                                    :style="{ visibility: formItem.showGroupId && checkExistedGroupId(form) ? 'visible' : 'hidden' }"
                                    style="padding-left: 2pt"
                                >
                                    {{ formItem.groupId ? NUMBER_ICONS[formItem.groupId] : '-' }}
                                </div>
                            </print-col>
                            <print-col span="7">
                                {{ formItem.name }}
                            </print-col>
                            <print-col span="5">
                                {{ formItem.productInfo && formItem.productInfo.displaySpec }}
                            </print-col>
                            <print-col
                                span="2"
                                style="text-align: right"
                            >
                                {{ formItem.dosage }}{{ formItem.dosageUnit }}
                            </print-col>
                            <print-col
                                span="3"
                                style="text-align: right"
                            >
                                {{ formItem.usage }}
                            </print-col>
                            <print-col
                                span="2"
                                style="text-align: right"
                            >
                                {{ formItem.unitCount }}{{ formItem.unit }}
                            </print-col>
                            <print-col
                                span="4"
                                style="text-align: right"
                            >
                                {{ formatMoney(formItem.unitPrice) }}
                            </print-col>
                            <print-col
                                v-if="!formItem.payType && formItem.specialRequirement || formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE"
                                span="24"
                                style="padding-left: 14pt"
                            >
                                备注：{{ formItem.specialRequirement }} {{ formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE ? '【自备】' : '' }}
                            </print-col>
                        </print-row>
                    </div>
                </div>

                <div
                    v-if="pageIndex !== form.splitPrescriptionFormItems.length - 1"
                    data-type="new-page"
                ></div>
            </template>
            <div
                v-if="formIndex !== westernForms.length - 1 || infusionForms.length || externalForms.length"
                data-type="new-page"
            ></div>
        </template>

        <template v-for="(form, formIndex) in infusionForms">
            <template v-for="(splitFormItems, pageIndex) in form.splitPrescriptionFormItems">
                <div class="tianjin-western-prescription-wrapper">
                    <western-tpl
                        :organ-title="curOrganTitle"
                        :department-name="departmentName"
                        :patient-name="patient.name"
                        :patient-sex="patient.sex"
                        :patient-age="formatAge(patient.age, { monthYear: 12, dayYear: 1 })"
                        :diagnosis="diagnosis"
                        :diagnosis-date="parseTime(printData.diagnosedDate, 'd', true)"
                        :diagnosis-month="parseTime(printData.diagnosedDate, 'm', true)"
                        :diagnosis-year="parseTime(printData.diagnosedDate, 'y', true)"
                        :patient-order-no="printData.patientOrderNo"
                        :health-card-pay-level="printData.healthCardPayLevel"
                        :doctor-name="printData.doctorName"
                        :audit-name="printData.auditName"
                        :compound-name="printData.compoundName"
                        :doctor-sign-img-url="printData.doctorSignImgUrl"
                        :audit-hand-sign="printData.auditHandSign"
                        :compound-by-hand-sign="printData.compoundByHandSign"
                        :total-price="form.totalPrice"
                    ></western-tpl>

                    <div class="pr-content">
                        <print-row
                            v-for="formItem in splitFormItems"
                            class="medicine-tr"
                        >
                            <print-col span="1">
                                <div
                                    :style="{ visibility: formItem.showGroupId && checkExistedGroupId(form) ? 'visible' : 'hidden' }"
                                    style="padding-left: 2pt"
                                >
                                    {{ formItem.groupId ? NUMBER_ICONS[formItem.groupId] : '-' }}
                                </div>
                            </print-col>
                            <print-col span="7">
                                {{ formItem.name }}
                            </print-col>
                            <print-col span="5">
                                {{ formItem.productInfo && formItem.productInfo.displaySpec }}
                            </print-col>
                            <print-col
                                span="2"
                                style="text-align: right"
                            >
                                {{ formItem.dosage }}{{ formItem.dosageUnit }}
                            </print-col>
                            <print-col
                                span="3"
                                style="text-align: right"
                            >
                                {{ formItem.usage }}
                            </print-col>
                            <print-col
                                span="2"
                                style="text-align: right"
                            >
                                {{ formItem.unitCount }}{{ formItem.unit }}
                            </print-col>
                            <print-col
                                span="4"
                                style="text-align: right"
                            >
                                {{ formatMoney(formItem.unitPrice) }}
                            </print-col>
                            <print-col
                                v-if="formItem.specialRequirement || formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE"
                                span="24"
                                style="padding-left: 14pt"
                            >
                                备注：{{ formItem.specialRequirement }} {{ formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE ? '【自备】' : '' }}
                            </print-col>
                        </print-row>
                    </div>
                </div>

                <div
                    v-if="pageIndex !== form.splitPrescriptionFormItems.length - 1"
                    data-type="new-page"
                ></div>
            </template>
            <div
                v-if="formIndex !== infusionForms.length - 1 || externalForms.length"
                data-type="new-page"
            ></div>
        </template>

        <template v-for="(form, formIndex) in externalForms">
            <template v-for="(spliceFormItems, pageIndex) in form.splitPrescriptionFormItems">
                <div class="tianjin-western-prescription-wrapper">
                    <western-tpl
                        :organ-title="curOrganTitle"
                        :department-name="departmentName"
                        :patient-name="patient.name"
                        :patient-sex="patient.sex"
                        :patient-age="formatAge(patient.age, { monthYear: 12, dayYear: 1 })"
                        :diagnosis="diagnosis"
                        :diagnosis-date="parseTime(printData.diagnosedDate, 'd', true)"
                        :diagnosis-month="parseTime(printData.diagnosedDate, 'm', true)"
                        :diagnosis-year="parseTime(printData.diagnosedDate, 'y', true)"
                        :patient-order-no="printData.patientOrderNo"
                        :health-card-pay-level="printData.healthCardPayLevel"
                        :doctor-name="printData.doctorName"
                        :compound-name="printData.compoundName"
                        :audit-name="printData.auditName"
                        :doctor-sign-img-url="printData.doctorSignImgUrl"
                        :audit-hand-sign="printData.auditHandSign"
                        :compound-by-hand-sign="printData.compoundByHandSign"
                        :total-price="form.totalPrice"
                    ></western-tpl>
                    <div class="pr-content">
                        <div
                            v-for="(formItem, formItemIndex) in spliceFormItems"
                            style="margin-bottom: 4pt"
                        >
                            <print-row style="margin-bottom: 4pt">
                                <print-col span="24">
                                    {{ formItemIndex + 1 }}. {{ formItem.name }}
                                </print-col>
                            </print-row>
                            <print-row v-if="formItem.externalGoodsItems && formItem.externalGoodsItems.length">
                                <print-col
                                    v-for="medicineItem in formItem.externalGoodsItems"
                                    span="6"
                                    style="margin-bottom: 4pt"
                                >
                                    {{ medicineItem.name }} {{ medicineItem.unitCount }}{{ medicineItem.unit }}
                                </print-col>
                            </print-row>
                            <print-row style="margin-bottom: 2pt">
                                <print-col span="24">
                                    穴位：{{ formatAcupoints(formItem.acupoints) }}
                                </print-col>
                            </print-row>
                            <print-row style="margin-bottom: 2pt">
                                <print-col :span="24">
                                    <span class="label">用法：</span>
                                    <template v-if="formItem.unitCount">
                                        共 {{ formItem.dosage }} 次
                                    </template>
                                    <template v-if="formItem.freq">
                                        ，{{ formItem.freq }}
                                    </template>
                                    <template v-if="formItem.specialRequirement">
                                        ，{{ formItem.specialRequirement }}
                                    </template>
                                    <template v-if="formItem.unitCount && formItem.dosage">
                                        ，每次{{ calcDosageCount(formItem) }}{{ form.usageType === 0 ? '贴' : '穴' }}
                                    </template>
                                    <template v-if="formItem.dosage">
                                        ，共{{ formItem.dosage * calcDosageCount(formItem) }}{{ form.usageType === 0 ? '贴' : '穴' }}
                                    </template>
                                    <template v-if="form.usageType === 0 && form.usageSubType === 0 && formItem.productInfo.type !== 4">
                                        ，共{{ formItem.unitCount }}{{ formItem.unit }}
                                    </template>
                                </print-col>
                            </print-row>
                        </div>
                    </div>
                </div>
                <div
                    v-if="pageIndex !== form.splitPrescriptionFormItems.length - 1"
                    data-type="new-page"
                ></div>
            </template>

            <div
                v-if="formIndex !== externalForms.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import PageSizeMap, {MM150_203, Orientation} from "../share/page-size.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PrescriptionMixin from "./mixins/prescription.js";
    import WesternTpl from "./components/tianjin-prescription/western-tpl.vue";
    import printRow from "./components/layout/print-row.vue";
    import PrintCol from "./components/layout/print-col.vue";
    export default {
        name: "TianjinWesternPrescription",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.TIANJIN_WESTERN_PRESCRIPTION,
        components: {
            WesternTpl,
            PrintCol,
            printRow
        },
        pages: [
            {
                paper: PageSizeMap.MM150_203,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        mixins: [PrescriptionMixin],
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
            extra: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
    }
</script>

<style lang="scss">
@import './components/layout/print-layout.scss';
.abc-page_preview {
    background: url("/static/assets/print/tianjin-western.jpg") no-repeat center / 100% 100% !important;
    color: #2a82e4;
}
.tianjin-western-prescription-wrapper {
    position: absolute;
    width: 150mm;
    height: 203mm;
    font-size: 9pt;
    line-height: 11pt;

    .pr-content {
        position: absolute;
        width: 120mm;
        height: 94mm;
        top: 70mm;
        left: 16mm;

        .medicine-tr {
            height: 12.5mm;
            overflow: hidden;
        }
    }
}

</style>
