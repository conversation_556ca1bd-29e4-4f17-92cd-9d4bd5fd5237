import {GoodsTypeEnum} from '../common/constants.js'
import clone from "../common/clone.js";
export const resetChargeItemComposeSocialFee = (chargeItems) => {
    if(chargeItems && chargeItems.length )  {
        const res = clone(chargeItems).map(item => {
            if(item.productType === GoodsTypeEnum.COMPOSE || item.composeType === 1) {
                item.ownExpenseRatio = '-';
                item.overlmtAmt = '-';
                item.inscpScpAmt = '-';
                item.ownExpenseFee = '-';
            }
            return item;
        })
        return res;
    }
    return [];
}