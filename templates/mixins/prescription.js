import { formatMoney, formatAge, formatAddress, parseTime} from '../common/utils.js';
import {doseTotal} from "../common/medical-transformat.js";
import clone from "../common/clone.js";
import { NUMBER_ICONS } from "../common/constants.js";
import {formatAcupoints} from "../common/medical-transformat.js";
import {OutpatientChargeTypeEnum} from "../constant/print-constant.js";
import {isNumber} from '../common/medical-transformat.js';


export default {
    data() {
        return {
            NUMBER_ICONS,
            westernPageCount: 8,
            externalPageCount: 4,
            chinesePageCount: 30,
            OutpatientChargeTypeEnum,
        }
    },
    computed: {
        printData() {
            const data = clone(this.renderData.printData)
            this.renderData.printData.prescriptionWesternForms && this.renderData.printData.prescriptionWesternForms.forEach( form => {
                form.prescriptionFormItems = this.groupMedicine(form.prescriptionFormItems);
            })
            this.renderData.printData.prescriptionInfusionForms && this.renderData.printData.prescriptionInfusionForms.forEach( form => {
                form.prescriptionFormItems = this.groupMedicine(form.prescriptionFormItems);
            })

            if(this.config && this.config.content && this.config.content.standardKindCount) {
                const {prescriptionWesternForms, prescriptionInfusionForms} = this.transStandardForm();
                data.prescriptionWesternForms = prescriptionWesternForms;
                data.prescriptionInfusionForms = prescriptionInfusionForms;
            } else {
                data.prescriptionWesternForms && data.prescriptionWesternForms.forEach( form => {
                    console.log('分页处方')
                    form.splitPrescriptionFormItems = this.splitFormItems(form.prescriptionFormItems, this.westernPageCount);
                })
                data.prescriptionInfusionForms && data.prescriptionInfusionForms.forEach( form => {
                    console.log('分页处方')
                    form.splitPrescriptionFormItems = this.splitFormItems(form.prescriptionFormItems, this.westernPageCount);
                })
            }

            this.renderData.printData.prescriptionChineseForms && this.renderData.printData.prescriptionChineseForms.forEach( form => {
                form.splitPrescriptionFormItems = this.splitFormItems(form.prescriptionFormItems, this.chinesePageCount);
            })

            this.renderData.printData.prescriptionExternalForms && this.renderData.printData.prescriptionExternalForms.forEach( form => {
                form.splitPrescriptionFormItems = this.splitFormItems(form.prescriptionFormItems, this.externalPageCount);
            })
            return data;
        },
        config() {
            if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.prescription) {
                return this.renderData.config.medicalDocuments.prescription;
            }
            return {};
        },
        headerConfig() {
            return this.config && this.config.header || {};
        },
        organ() {
            return this.printData && this.printData.organ;
        },
        organTitle() {
            if (!this.headerConfig.title) {
                return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.prescription || '';
            }
            return this.headerConfig.title;
        },
        curOrganTitle() {
            if(!this.organTitle) {
                return this.organ && this.organ.name || '';
            }
            return this.organTitle || '';
        },
        departmentName() {
            return this.printData.departmentName;
        },
        patient() {
            return this.printData.patient;
        },
        diagnosis() {
            return this.printData.diagnosis;
        },
        chineseForms() {
            return this.printData.prescriptionChineseForms || [];
        },
        westernForms() {
            return this.printData.prescriptionWesternForms || [];
        },
        infusionForms() {
            return this.printData.prescriptionInfusionForms || [];
        },
        externalForms() {
            return this.printData.prescriptionExternalForms || [];
        },
        shebaoCardInfo() {
            return this.printData.shebaoCardInfo || {};
        },
    },
    methods: {
        formatMoney,
        formatAge,
        formatAddress,
        parseTime,
        doseTotal,
        formatAcupoints,
        splitFormItems(formItems, count = 5, isRepeat = true) {
            let tempFormItems = clone(formItems);
            let len = formItems.length;
            let res = [];

            if(isRepeat) {
                // 不考虑去重，直接按照数量切分
                let index = 0;
                while(index < len) {
                    let tempItems = tempFormItems.slice(index, index + count);
                    index += count;
                    res.push(tempItems);
                }
            } else {
                let groupSet = new Set();
                let group = [];
                tempFormItems.forEach( item => {
                    groupSet.add(item.goodsId);
                    if(groupSet.size < count + 1) {
                        group.push(item);
                    } else {
                        res.push(group);
                        groupSet.clear();
                        groupSet.add(item.goodsId);
                        group = [];
                        group.push(item);
                    }
                })
                if(group && group.length) {
                    res.push(group);
                }
            }
            return res;
        },
        transStandardForm() {
            const wsForms = clone(this.renderData.printData.prescriptionWesternForms) || [];
            const inForms = clone(this.renderData.printData.prescriptionInfusionForms) || [];
            let resWsForm = [];
            let resInForm = [];
            wsForms.forEach( form => {
                let tempForm = clone(form);
                tempForm.prescriptionFormItems = [];
                let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5,false);
                let splitFormItems = [];
                formItemsGroups.forEach(group => {
                    splitFormItems = splitFormItems.concat(this.splitFormItems(group, this.westernPageCount))
                })
                resWsForm.push({
                    ...tempForm,
                    isStandardForm: true,
                    splitPrescriptionFormItems: splitFormItems,
                })
            })
            inForms.forEach( form => {
                let tempForm = clone(form);
                tempForm.prescriptionFormItems = [];
                let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5, false);
                let splitFormItems = [];
                formItemsGroups.forEach(group => {
                    splitFormItems = splitFormItems.concat(this.splitFormItems(group, this.westernPageCount))
                })
                resInForm.push({
                    ...tempForm,
                    isStandardForm: true,
                    splitPrescriptionFormItems: splitFormItems
                })

            })
            return {
                prescriptionWesternForms: resWsForm,
                prescriptionInfusionForms: resInForm,
            }
        },
        // 检查西药处方中是否存在组号
        checkExistedGroupId(form) {
            if(form.splitPrescriptionFormItems && form.splitPrescriptionFormItems.length && Array.isArray(form.splitPrescriptionFormItems[0])) {
                return form.splitPrescriptionFormItems.some(item => item.some(it => it.groupId !== null && it.groupId !== undefined))
            }
            const index = form.prescriptionFormItems && form.prescriptionFormItems.findIndex( item => {
                return item.groupId !== null && item.groupId !== undefined;
            })
            return index > -1;
        },
        groupMedicine( prescriptionFormItems ) {
            let _group = {};
            let tempFormItems = clone(prescriptionFormItems)
            let noGroupIdItems = [];
            //分组
            tempFormItems.forEach( ( medicine ) => {
                if(!medicine.groupId) {
                    medicine.showGroupId = true;
                    noGroupIdItems.push(medicine);
                } else {
                    if (!(_group[ Number( medicine.groupId ) ] instanceof Array)) {
                        _group[ Number( medicine.groupId ) ] = [];
                    }
                    if(!_group[ Number( medicine.groupId ) ].length ) {
                        medicine.showGroupId = true;
                    } else{
                        medicine.showGroupId = false;
                    }
                    _group[ Number( medicine.groupId ) ].push(  medicine );
                }

            } );
            let res = [];
            for( let item in _group) {
                res = res.concat(_group[item])
            }
            return res.concat(noGroupIdItems);
        },
        calcDosageCount(item) {
            if (item.acupointUnitCount && item.acupointUnit) {
                return item.acupointUnitCount;
            }
            let count = 0;
            (item.acupoints || []).forEach((it) => {
                if (it.name) {
                    if (isNumber(it.position)) {
                        count += parseFloat(it.position);
                    } else if (it.position === '双') {
                        count += 2;
                    } else if (it.position !== '-') {
                        count++;
                    }
                }
            });
            return count || 1;
        },
    },
};
