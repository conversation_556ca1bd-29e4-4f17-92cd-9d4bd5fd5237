import { splitChargeFormItems } from "../common/medical-bill.js";
import { GoodsFeeType, PrintFormTypeEnum } from "../common/constants";
import { medicalFeeGrade2PrintStr } from "../common/utils.js";
import { resetChargeItemComposeSocialFee } from "./reset-compose-social-fee";

export default {
    computed: {
        format() {
            return this.config.format ?? '';
        },
        // 是否分页打印
        splitType() {
            return this.currentConfig.splitType ?? 0;
        },
        currentConfig() {
            return this.config[this.format] || {};
        },
        // 没有类型对应的套餐配置
        composeChildrenConfig() {
            return this.currentConfig.composeType ?? 2;
        },
        // 项目信息规则配置
        productInfoRuleConfig() {
            return this.currentConfig.productInfoRule ?? 1;
        },
        showChargeFormItems() {
            return this.currentConfig.chargeItems ?? 1;
        },
        // 所有的chargeFormItems 集合
        chargeFormItems() {
            let res = [];
            this.chargeForms.forEach((form) => {
                // 如果为套餐
                if (form.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT) {
                    (form.chargeFormItems || []).forEach((formItem) => {
                        // 打印套餐名
                        if (this.composeChildrenConfig === 0) {
                            res.push(formItem);
                        } else {
                            // 打印套餐名及子项
                            if (this.composeChildrenConfig === 2) {
                                res.push(formItem);
                            }
                            // 打印套餐名及子项 or 打印套餐内子项
                            (formItem.composeChildren || []).forEach((children) => {
                                // 如果开启了医嘱收费项配置,目前只有医院管家开启
                                if (this.isFeeCompose) {
                                    res = this.createIsFeeComposeRes(res, children)
                                } else {
                                    res = this.createIsNotFeeComposeRes(res, children)
                                }
                            });
                        }
                    })
                } else {
                    (form.chargeFormItems || []).forEach((formItem) => {
                        // 如果开启了医嘱收费项配置,目前只有医院管家开启
                        if (this.isFeeCompose) {
                            res = this.createIsFeeComposeRes(res, formItem)
                        } else {
                            res = this.createIsNotFeeComposeRes(res, formItem)
                        }
                    })
                }
            });
            res =  res.filter(billItem => {
                if (billItem.name === '诊费' && res.length > 1) {
                    return !!billItem.discountedPrice
                }
                return true
            }) || [];
            if(this.isNeedResetComposeSocialFee) {
                res = resetChargeItemComposeSocialFee(res);
            }
            return res;
        },
        // 打印明细的分页数据
        pagesData() {
            return splitChargeFormItems(this.chargeFormItems, 16);
        },
        //渲染的页面数据
        renderPage() {
            if (this.showChargeFormItems) {
                if (this.splitType) {
                    return [this.pagesData[0]];
                }
                return this.extra.isPreview ? this.pagesData.slice(0, 1) : this.pagesData;
            }
            return [1];
        },
        // 只打印第一页数据
        isOnlyOnePage() {
            return this.splitType === 1 && this.pagesData.length > 1;
        },

        hasOverPageTip() {
            return this.isOnlyOnePage;
        },
        /*
        @desc: 是否显示自负比例
        @author: ff
        @time: 2025/3/26
        */
        showOwnExpenseRatio() {
            return this.currentConfig.isShowOwnExpenseRatio !== 0;
        },
    },
    methods: {
        // 开启了医嘱收费项配置的res,目前只有医院管家开启
        createIsFeeComposeRes(res, children) {
            // 如果为医嘱
            if (children.goodsFeeType === GoodsFeeType.FEE_PARENT) {
                if (this.productInfoRuleConfig) {
                    // 收费项
                    if (children.composeChildren && children.composeChildren.length) {
                        res = res.concat(children.composeChildren);
                    }
                } else {
                    // 医嘱
                    res.push(children);
                }
            } else {
                // 如果为费用项
                res.push(children);
            }
            return res
        },
        // 未开启医嘱收费项配置的res,目前只有医院管家开启
        createIsNotFeeComposeRes(res, children) {
            // 展示医保对码
            if (this.productInfoRuleConfig === 1) {
                if (children.composeChildren && children.composeChildren.length) {
                    children.composeChildren.map((item) => {
                        // 老对码展示名称同新对码一致取社保名称
                        item.name = item.socialName || item.name
                        return item
                    })
                    res = res.concat(children.composeChildren);
                } else {
                    // 老对码展示名称同新对码一致取社保名称
                    children.name = children.socialName || children.name
                    res.push(children);
                }
            } else {
                res.push(children);
            }
            return res
        },
        // 是否展示医保等级
        isShowSocialCode(chargeItem) {
            if (!medicalFeeGrade2PrintStr(chargeItem.medicalFeeGrade)) {
                // 商品无医保码时不展示医保等级
                return false
            }
            // 项目信息规则选展示收费项目时，诊疗项目不展示医保等级
            if (!this.productInfoRuleConfig
                && (chargeItem.productType == 3 || chargeItem.productType == 4 || chargeItem.productType == 19)
            ) {
                return false
            }
            // 药品有医保码或项目规则选择要展示等级时均显示医保等级
            return true
        },
        handleOwnExpensePrice(item) {
            if (item.ownExpenseRatio === '-' ) return '-';
            if (item.ownExpenseRatio === undefined || item.ownExpenseRatio === null) return item.discountedPrice;
            try {
                const ownExpenseRatio = Number(item.ownExpenseRatio);
                return item.discountedPrice * ownExpenseRatio;
            } catch (e) {
                console.warn(e);
                return item.discountedPrice;
            }
        },
    },
}
