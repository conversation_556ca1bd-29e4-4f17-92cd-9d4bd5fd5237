import clone from '../common/clone.js';
import {
    formatMoney,
    formatAddress,
    parseTime,
    medicalFeeGrade2PrintStr,
    formatPatientOrderNo,
    filterOwnExpenseRatio,
} from '../common/utils.js';
import { digitUppercase, formatAge } from "../common/utils.js";
import { PRINT_BILL_AMOUNT_TYPE } from "../common/constants.js";


export default {
    data() {
        return {
            registrationFee: 0, // 挂号费
            westernMedicineFee: 0, // 西药费
            chineseMedicineFee: 0,
            chineseMedicineDrinksPieceFee: 0, // 中药饮片费用
            chineseComposeMedicineFee: 0, // 中成药费用 + 中药颗粒
            treatmentFee: 0, // 治疗理疗费
            examinationInspectionFee: 0, // 检查费
            examinationExaminationFee: 0, // 化验费
            materialFee: 0, // 材料费
            examinationFee: 0, // 诊察费
            nursingFee: 0, // 护理费
            physiotherapyFee: 0, // 理疗费
            bedFee: 0, // 床位费
            operationFee: 0, // 手术费
            generalDiagnosisAndTreatmentFee: 0, // 一般诊疗费
            pharmacyServiceFee: 0, // 药事服务费
            otherFee: 0, // 一般诊疗费( 其他费用 )
            outerFlagFee: 0, // 非内置费用
        };
    },
    props: {
        renderData: {
            type: Object,
            default() {
                return {}
            },
        },
        extra: {
            type: Object,
            default() {
                return {}
            },
        },
    },
    computed: {
        $abcSocialSecurity() {
            return this.extra.$abcSocialSecurity
        },
        receiptConfig() {
            return this.extra.receiptConfig;
        },
        config() {
            return this.receiptConfig || this.renderData.config.billConfig || { hubei: {} };
        },
        printData() {
            return this.renderData.printData || {};
        },
        organ() {
            return this.printData.organ || {};
        },
        patient() {
            return this.printData.patient || {};
        },
        personalPaymentFee() {
            return this.printData.personalPaymentFee;
        },
        chargeForms() {
            return this.printData.chargeForms || [];
        },
        shebaoPayment() {
            return this.printData.shebaoPayment || {};
        },
        extraInfo() {
            return this.shebaoPayment.extraInfo || {};
        },
        medicalBills() {
            return this.printData.medicalBills &&this.printData.medicalBills.filter(billItem => {
                if (billItem.name === this.$t('registrationFeeName')) {
                    return !!billItem.totalFee
                }
                return true
            }) || [];
        },
        receivableFee() {
            return this.printData && this.printData.receivableFee ;
        },
        // 退费医疗票据 显示退费的费用，收费显示应收的费用
        finalFee() {
            return this.printData.IS_REFUND ? this.printData.refundFee : this.receivableFee;
        },
        isRefundBill() {
            return this.printData.IS_REFUND;
        },
        // 冲红发票数据展示，对应的正数发票数据信息
        blueInvoiceData() {
            return this.printData.blueInvoiceData || null;
        },
        normalInvoice() {
            return this.printData.normalInvoice || null;
        },
        diagnosisInfos() {
            return this.printData.diagnosisInfos || [];
        },
        diagnosisCodes() {
            const codes = this.diagnosisInfos.map((item) => {
                return item.code;
            });
            return codes.join('，');
        },
        year() {
            if(this.printData.chargedTime) {
                return new Date(this.printData.chargedTime).getFullYear();
            }
            return '';
        },
        month() {
            if(this.printData.chargedTime) {
                return new Date(this.printData.chargedTime).getMonth() + 1;
            }
            return '';
        },
        day() {
            if(this.printData.chargedTime) {
                return new Date(this.printData.chargedTime).getDate();
            }
            return '';
        },
        chargeTransactions() {
            return this.printData && this.printData.chargeTransactions;
        },
        isFeeCompose() {
            return !!this.printData.IS_FEE_COMPOSE;
        },
        prepaidSettleSummary() {
            return this.printData.prepaidSettleSummary || {};
        },
        payModeType() {
            const payTypeList = this.printData.chargeTransactions.filter((item) => {
                return item.payMode !== 5 && item.amount >= 0
            }) || [];
            return payTypeList.map(item => item.payModeName).join('、');
        },
    },
    methods: {
        digitUppercase,
        formatAge,
        filterOwnExpenseRatio,
        transAmount(amount) {
            const output = [];
            for (let i = 0; i < 8; i++) {
                output.push('零');
            }
            amount += '';
            amount = amount.replace(/\./g, '');
            const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
            let digitIndex = 7;
            for (let i = amount.length - 1; i >= 0; i--) {
                output[digitIndex] = digit[amount[i]];
                digitIndex--;
            }
            return output;
        },
        to8(value = '') {
            value === null && (value = '');
            const srcStr = '00000000';
            return srcStr.slice(0, -(`${value}`).length).concat(value);
        },
        spliceFormItems(formItems, pageCount, firstPageCount) {
            const page = [];
            const cacheFormItems = clone(formItems);
            let pageIndex = 0;
            while(cacheFormItems && cacheFormItems.length) {
                pageIndex++;
                let endIndex = 0;
                if(pageIndex === 1 && typeof firstPageCount === 'number') {
                    endIndex = cacheFormItems.length > firstPageCount ? firstPageCount : cacheFormItems.length;
                } else {
                    endIndex = cacheFormItems.length > pageCount ? pageCount : cacheFormItems.length;
                }
                page.push({
                    formItems: cacheFormItems.splice(0, endIndex),
                    pageIndex,
                });
            }
            return page;
        },
        initFee() {
            this.medicalBills.forEach((item) => {
                switch (item.printType) {
                case PRINT_BILL_AMOUNT_TYPE.westernMedicineFeeType:
                    this.westernMedicineFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.chineseMedicineDrinksPieceFeeType:
                    this.chineseMedicineDrinksPieceFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.chineseComposeMedicineFeeType:
                    this.chineseComposeMedicineFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.examinationInspectionFeeType:
                    this.examinationInspectionFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.examinationExaminationFeeType:
                    this.examinationExaminationFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.treatmentFeeType:
                    this.treatmentFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.registrationFeeType:
                    this.registrationFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.materialFeeType:
                    this.materialFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.otherFeeType:
                    this.otherFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.examinationType:
                    this.examinationFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.nursingType:
                    this.nursingFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.physiotherapyType:
                    this.physiotherapyFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.bedType:
                    this.bedFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.operationType:
                    this.operationFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.generalDiagnosisAndTreatmentType:
                    this.generalDiagnosisAndTreatmentFee = item.totalFee;
                    break;
                case PRINT_BILL_AMOUNT_TYPE.pharmacyServiceType:
                    this.pharmacyServiceFee = item.totalFee;
                    break;
                default:
                    break;
                }
                // 将自定义的费用类型合并到outerFlagFee中方便后续计算
                // 0 自定义  1 内置
                if (item.innerFlag === 0) {
                    this.outerFlagFee += item.totalFee;
                }
            });
        },
    },
    filters: {
        formatMoney,
        filterOwnExpenseRatio,
        formatAddress,
        parseTime,
        medicalFeeGrade2PrintStr,
        formatPatientOrderNo,
        formatPatientOrder(value = '') {
            if (value === '零售开单') return '';
            const srcStr = '00000000';
            if (!value) {
                return srcStr;
            }
            return (`${srcStr}${value}`).slice(-8);
        },
        filterIdCard(idCard) {
            if (!idCard) return '';
            if (typeof idCard === 'string') {
                return `${idCard.substr(0, 2)}********${idCard.substr(11, 4)}****`;
            }
            return idCard;
        },
        filterShebaoCard(idCard) {
            if (!idCard) return '';
            if (typeof idCard === 'string') {
                return `${idCard.substr(0, 2)}***${idCard.slice(-4)}`;
            }
            return idCard;
        },
    },
};
