<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '9410ffd3ece8439e9e12c8f3df396bc8',
            chargeFormItems: [
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                    name: '盐知母',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                    specialRequirement: '包煎',
                    ownExpenseFee: 36,
                },
                {
                    id: '7ef3ac794a034b4e952031d4b14b18c1',
                    name: '盐黄柏',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 0.03,
                    specialRequirement: '先煎',
                    ownExpenseFee: 0.63,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16.2,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9072',
                    name: '山药YG',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9073',
                    name: '牡丹皮YG',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9076',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                //     name: '盐知母',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 36.0,
                //     specialRequirement: '包煎',
                // },
                // {
                //     id: '7ef3ac794a034b4e952031d4b14b18c1',
                //     name: '盐黄柏',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 1.0,
                //     doseCount: 1.0,
                //     totalPrice: 0.03,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9072',
                //     name: '山药YG',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9073',
                //     name: '牡丹皮YG',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9076',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },{
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                //     name: '盐知母',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 36.0,
                //     specialRequirement: '包煎',
                // },
                // {
                //     id: '7ef3ac794a034b4e952031d4b14b18c1',
                //     name: '盐黄柏',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 1.0,
                //     doseCount: 1.0,
                //     totalPrice: 0.03,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9072',
                //     name: '山药YG',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9073',
                //     name: '牡丹皮YG',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9076',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
            ],
            sourceFormType: 6,
            specification: '中药饮片',
            doseCount: 1,
            dailyDosage: '1日1剂',
            usage: '煎服',
            freq: '1日3次',
            usageLevel: '每次150ml',
        },

        // {
        //     id: '338adf3126c141e0ab38d5de35e9305902',
        //     chargeFormItems: [
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //     ],
        //     sourceFormType: 2,
        // },

    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号
    diagnosisInfos: [
        {
            code: 'L55.900',
            diseaseType: null,
            name: '晒斑[晒伤]',
        },
    ],

    patientOrderNo: '**********',
    memberCardBalance: null,
    memberCardMobile: '',
    memberCardBeginningBalance: '', // 会员卡原有余额
    healthCardBeginningBalance: '567.68', // 社保卡原有余额
    healthCardOwnerRelationToPatient: '父女', // 持卡人关系
    healthCardBalance: '0.00', // 社保卡余额
    healthCardNo: '********', // 社保卡卡号
    healthCardOwner: '任我行', // 持卡人姓名"
    serialNo: '********', // 门诊流水号"

    healthCardId: '********', // 医保编号
    healthCardAccountPaymentFee: '19.99', // 帐户支付金额
    healthCardFundPaymentFee: 20, // 统筹支付金额
    healthCardOtherPaymentFee: '10', // 其它支付金额
    healthCardCardOwnerType: '职工', // 医保类型 职工 居民 离休干部
    healthCardSelfConceitFee: '11', // 自负金额
    healthCardSelfPayFee: '22', // 自费金额
    personalPaymentFee: '33', // 个人现金支付

    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
    ],

    shebaoPayment: {
        cardId: '********', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            // 青岛数据
            individualAffordabilityLine: '9.9', //个人负担起付线
        },
    },
}
-->

<template>
    <div>
        <div
            v-if="!qingdao.chargeItems"
            class="qingdao-medical-bill-content"
        >
            <qingdao-medical-bills :render-data="renderData"></qingdao-medical-bills>
        </div>
        <template
            v-for="(page, pageIndex) in currentRenderPage"
            v-else
        >
            <div class="qingdao-medical-bill-content">
                <div
                    :key="pageIndex"
                    class="qingdao-medical-bill-page"
                >
                    <div
                        v-for="count in 3"
                        :key="count"
                        class="stub-form"
                        :class="{ 'bookkeeping-sheet': count === 2, 'invoice' : count === 3 }"
                    >
                        <refund-icon
                            v-if="isRefundBill"
                            top="0.2cm"
                            left="0.6cm"
                        ></refund-icon>
                        <div
                            v-if="blueInvoiceData"
                            style="position: absolute; top: 0.2cm; left: 0.6cm; width: 6cm;"
                        >
                            销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                        </div>
                        <div class="organ-name">
                            {{ qingdao.institutionName }}
                        </div>
                        <div
                            class="patient"
                            style="top: 5mm; left: 9mm;"
                        >
                            发票号：{{ normalInvoice && normalInvoice.invoiceNumbers && normalInvoice.invoiceNumbers[pageIndex] }}
                        </div>
                        <div
                            class="patient"
                            style="top: 15mm"
                        >
                            {{ shebaoPayment.medType }}
                        </div>
                        <div class="patient">
                            <span>{{ patient.name }}</span>
                        </div>
                        <div
                            class="bill-detail"
                        >
                            <template>
                                <div class="not-chinese-form-items-wrapper">
                                    <span class="item-name">项目</span>
                                    <span class="unit-price"></span>
                                    <span class="item-count">数量</span>
                                    <span class="item-price">金额</span>
                                </div>
                                <template v-for="item in page.formItems">
                                    <template v-if="!item.isChineseFormItems">
                                        <not-chinese-form-items
                                            :item="item.items[0]"
                                        ></not-chinese-form-items>
                                    </template>
                                    <template v-else>
                                        <chinese-form-items
                                            :items="item.items"
                                        ></chinese-form-items>
                                    </template>
                                </template>

                                <div
                                    v-if="hasOverPageTip"
                                    class="only-one-page"
                                >
                                    *** 因纸张限制，部分项目未打印 ***
                                </div>
                            </template>
                        </div>


                        <div class="bill-shebao-info">
                            <template v-if="page.pageIndex === 1">
                                <div>
                                    <span>合计：</span>
                                    <span>{{ $t('currencySymbol') }}{{ finalFee | formatMoney }}</span>
                                    <span style="margin-left: 1mm;">基金支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}</span>
                                </div>
                                <div>
                                    <span>
                                        个账：{{ shebaoPayment.accountPaymentFee | formatMoney }}
                                    </span>
                                    <span
                                        v-for="(pay, index) in chargeTransactions"
                                        :key="index"
                                    >
                                        ({{ pay.payModeDisplayName }}){{ pay.amount | formatMoney }}
                                    </span>
                                </div>
                            </template>
                        </div>
                        <div class="charge-time">
                            {{ printData.chargedTime | parseTime('y-m-d') }}
                        </div>
                        <div class="charger">
                            {{ printData.chargedByName }}
                        </div>
                    </div>
                </div>
            </div>
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import clone from "./common/clone.js";
    import NotChineseFormItems from './components/medical-bill/not-chinese-form-item.vue';
    import ChineseFormItems from './components/medical-bill/chinese-form-item.vue';
    import { PrintFormTypeEnum, SourceFormTypeEnum } from "./common/constants.js";
    import QingdaoMedicalBills from './components/medical-bill/qingdao-medical-bills.vue';
    import NationalBillData from "./mixins/national-bill-data.js";

    export default {
        name: "MedicalBillQingdao",
        components: {
            RefundIcon,
            NotChineseFormItems,
            ChineseFormItems,
            QingdaoMedicalBills
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_QINGDAO,
        pages: [
            {
                paper: PageSizeMap.MM240_90_QINGDAO,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        // 社保总支付的应该是 shebaoPayment.receivedFee
        computed: {
            chargeTransactions() {
                return this.printData && this.printData.chargeTransactions && this.printData.chargeTransactions.filter( item => {
                    return item.payMode !== 5;
                }) || [];
            },
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                return this.isOnlyOnePage ? this.renderPage.slice(0, 1) : this.renderPage
            },
            splitType() {
                return this.qingdao.splitType;
            },
            isOnlyOnePage() {
                return this.splitType === 1 && (this.renderPage.length > 1 || this.extra.isPreview);
            },
            qingdao() {
                return this.config.qingdao || {};
            },
            chineseForms() {
                return this.chargeForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                });
            },
            chineseFormItems() {
                let res = this.getFormItems(this.chineseForms);
                return this.chunkArray(res, 2, true);
            },
            notChineseForms() {
                return this.chargeForms.filter((form) => {
                    return form.sourceFormType !== SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                });
            },
            notChineseFormItems() {
                const res =  this.getFormItems(this.notChineseForms)
                return this.chunkArray(res, 1);
            },
            renderPage() {
                const totalFormItems = this.notChineseFormItems.concat(this.chineseFormItems);
                return this.spliceFormItems(totalFormItems, 14, 10, true);
            },
        },
        methods: {
            chunkArray(array, chunkSize, isChineseFormItems = false) {
                if(!array) return [];
                const chunks = [];
                for (let i = 0; i < array.length; i += chunkSize) {
                    chunks.push({
                        items: array.slice(i, i + chunkSize),
                        isChineseFormItems: isChineseFormItems,
                    });
                }
                return chunks;
            },
            getFormItems(chargeForms) {
                let res = [];
                chargeForms.forEach((form) => {
                    // 如果为套餐
                    if (form.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT) {
                        (form.chargeFormItems || []).forEach((formItem) => {
                            // 打印套餐名
                            if (this.composeChildrenConfig === 0) {
                                res.push(formItem);
                            } else {
                                // 打印套餐名及子项
                                if (this.composeChildrenConfig === 2) {
                                    res.push(formItem);
                                }
                                // 打印套餐名及子项 or 打印套餐内子项
                                (formItem.composeChildren || []).forEach((children) => {
                                    // 如果开启了医嘱收费项配置,目前只有医院管家开启
                                    if (this.isFeeCompose) {
                                        res = this.createIsFeeComposeRes(res, children)
                                    } else {
                                        res = this.createIsNotFeeComposeRes(res, children)
                                    }
                                });
                            }
                        })
                    } else {
                        (form.chargeFormItems || []).forEach((formItem) => {
                            // 如果开启了医嘱收费项配置,目前只有医院管家开启
                            if (this.isFeeCompose) {
                                res = this.createIsFeeComposeRes(res, formItem)
                            } else {
                                res = this.createIsNotFeeComposeRes(res, formItem)
                            }
                        })
                    }
                });
                return res
            },
            spliceFormItems(formItems, pageCount, firstPageCount, isChineseForm) {
                const page = [];
                const cacheFormItems = clone(formItems);
                let pageIndex = 0;
                while(cacheFormItems && cacheFormItems.length) {
                    pageIndex++;
                    let endIndex = 0;
                    if(pageIndex === 1) {
                        endIndex = cacheFormItems.length > firstPageCount ? firstPageCount : cacheFormItems.length;
                    } else {
                        endIndex = cacheFormItems.length > pageCount ? pageCount : cacheFormItems.length;
                    }
                    page.push({
                        formItems: cacheFormItems.splice(0, endIndex),
                        pageIndex,
                        isChineseForm,
                    });
                }
                return page;
            },
        },
    }
</script>
<style lang="scss">
* {
  padding: 0;
  margin: 0;
}

*::-webkit-scrollbar {display:none}

.abc-page_compatible {
  .invoice {
    left: 124mm!important;
  }
}

.qingdao-medical-bill-content {
  @import './components/refund-icon/refund-icon.scss';

  box-sizing: border-box;
  padding: 0;
  margin: 0;

  div {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
  }

  .qingdao-medical-bill-page {
    position: absolute;
    width: 244mm;
    height: 92.5mm;
    padding: 0;
    margin: 0;
    font-size: 0;
    left: 4mm;
  }

  .bill-title {
    position: absolute;
    top: 30mm;
    left: 6mm;
  }

  .charge-item {
    display: inline-block;
    width: 48%;
  }

  .bill-items {
    width: 66mm;
  }

  .stub-form {
    position: absolute;
    top: 0;
    left: 3mm;
    width: 64mm;
    height: 92.5mm;
    overflow: hidden;
    font-size: 9pt;
    word-break: keep-all;
    vertical-align: top;
    &:first-child {
      .chinese-form-items-wrapper {
        .chinese-form-item {
          max-width: 30mm;
        }
        .name {
          max-width: 12mm;
        }
        .count {
          max-width: 8mm;
        }
        .price {
          max-width: 10mm;
        }
      }
    }
    &.bookkeeping-sheet {
      .chinese-form-items-wrapper {
        .chinese-form-item {
          max-width: 32mm;
        }
        .name {
          max-width: 14mm;
        }
        .count {
          max-width: 8mm;
        }
        .price {
          max-width: 10mm;
        }
      }
    }
    &.invoice {
      .chinese-form-items-wrapper {
        .chinese-form-item {
          max-width: 50mm;
        }
        .name {
          max-width: 23mm;
        }
        .count {
          max-width: 12mm;
        }
        .price {
          max-width: 15mm;
        }
      }
    }
  }

  .patient {
    position: absolute;
    top: 26mm;
    left: 14mm;
    width: 45mm;
  }

  .card-id {
    padding-left: 0.2cm;
  }

  .organ-name {
    position: absolute;
    top: 19mm;
    left: 3mm;
    width: 57mm;
    word-break: break-all;
    text-align: center;
  }

  .bill-detail {
    position: absolute;
    top: 30mm;
    left: 6mm;

    &.bill-items {
      top: 30mm;
    }
  }

  .bill-shebao-info {
    position: absolute;
    top: 75mm;
    left: 6mm;
    line-height: 1;
  }

  .charge-time,
  .charger {
    position: absolute;
  }

  .charge-time {
    top: 83mm;
    left: 21mm;
  }

  .charger {
    top: 83mm;
    left: 45mm;
  }

  //  中药收费项目部分
  .chinese-form-items-wrapper {
    font-size: 0;

    .chinese-form-item {
      display: inline-block;
      _display: inline;
      *display: inline;
      max-width: 35mm;

      overflow: hidden;
      word-break: keep-all;
      white-space: nowrap;
      > span {
        font-size: 9pt;
        line-height: 11pt;
        vertical-align: top;
      }
    }

    .name,
    .count,
    .price {
      display: inline-block;
      _display: inline;
      *display: inline;
      overflow-x: hidden;
      word-break: keep-all;
      white-space: nowrap;
      vertical-align: text-bottom;
    }
  }

  // 非中药收费项目排版
  .not-chinese-form-items-wrapper {
    font-size: 0;

    .item-name,
    .item-price,
    .unit-price,
    .item-count {
      display: inline-block;
      _display: inline;
      *display: inline;
      font-size: 9pt;
      line-height: 10pt;
    }

    .item-name {
      width: 24mm;
      overflow: hidden;
      word-break: keep-all;
      white-space: nowrap;
    }

    .item-price,
    .unit-price,
    .item-count {
      width: 1cm;
      max-width: 1cm;
      overflow-x: hidden;
      text-align: right;
      word-break: keep-all;
      white-space: nowrap;
    }
  }

  .bookkeeping-sheet {
    left: 64mm;
    width: 67mm;

    .patient {
      left: 14mm;
    }

    .organ-name {
      left: 4mm;
    }

    .bill-detail,
    .bill-title {
      left: 2mm;
    }

    .bill-shebao-info {
      left: 2mm;
    }

    .charge-time {
      left: 14mm;
    }

    .charger {
      left: 46mm;
    }
  }

  .invoice {
    left: 131mm;
    width: 113mm;
    max-width: 113mm;

    .organ-name {
      left: 20mm;
    }

    .patient {
      left: 14mm;
    }

    .bill-detail,
    .bill-title {
      left: 2mm;
    }

    .not-chinese-form-items-wrapper .item-name {
      width: 4cm;
    }

    .bill-shebao-info {
      left: 2mm;
    }

    .charge-time {
      left: 14mm;
    }

    .charger {
      left: 62mm;
    }
  }
  .only-one-page {
    text-align: center;
    margin-top: -1mm;
  }
}

.abc-page_preview {
  background: url("/static/assets/print/qingdao.jpg");
  background-size: 244mm 92.5mm;
  color: #2a82e4;
}
</style>
