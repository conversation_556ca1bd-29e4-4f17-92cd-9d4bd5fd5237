<template>
    <div>
        <template v-for="(page, pageIndex) in currentRenderPage">
            <div
                :key="pageIndex"
                class="xiamen-xin-medical-bill-content"
            >
                <div
                    class="xiamen-xin-medical-bill-page"
                >
                    <div
                        v-if="blueInvoiceData"
                        style="position: absolute; top: 2mm; left: 13mm;"
                    >
                        销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                    </div>
                    <refund-icon
                        v-if="isRefundBill"
                        top="8mm"
                        left="13mm"
                    ></refund-icon>

                    <template v-if="Object.keys(shebaoPayment).length">
                        <block-box
                            top="16.5"
                            left="13"
                        >
                            医保编号：{{ printData.healthCardNo }}
                        </block-box>
                        <block-box
                            top="21"
                            left="13"
                        >
                            医保结算号：{{ extraInfo.setlId }}
                        </block-box>
                        <block-box
                            top="21"
                            left="105"
                        >
                            医保类型：{{ extraInfo.insutype }}
                        </block-box>
                    </template>

                    <div class="trans-number">
                        {{ printData.patientOrderNo }}
                    </div>
                    <div
                        class="trans-number"
                        style="left: 117mm;"
                    >
                        {{ printData.patientOrderNo }}
                    </div>
                    <div
                        v-if="Object.keys(shebaoPayment).length"
                        class="trans-number"
                        style="top: 30.5mm; left: 25mm;"
                    >
                        {{ shebaoPayment.cardId }}
                    </div>

                    <div
                        class="patient"
                        style="left: 25mm;"
                    >
                        {{ patient.name }}
                    </div>
                    <div
                        class="patient"
                        style="left: 81.5mm;"
                    >
                        {{ printData.departmentName }}
                    </div>
                    <div
                        class="patient"
                        style="left: 150mm;"
                    >
                        {{ printData.doctorName }}
                    </div>
                    <div
                        v-if="Object.keys(shebaoPayment).length"
                        class="shebao-wrapper"
                    >
                        <div>
                            <!-- 账户支付 -->
                            <div>
                                {{ shebaoPayment.accountPaymentFee | formatMoney }}
                            </div>
                            <!--  医疗帐户 -->
                            <div style="padding-left: 15pt;">
                                {{ extraInfo.purAcctPay | formatMoney }}
                            </div>
                            <!-- 健康账户 -->
                            <div style="padding-left: 15pt;">
                                {{ extraInfo.hlAcctPay | formatMoney }}
                            </div>
                            <!-- 共济账户 -->
                            <div style="padding-left: 15pt;">
                                {{ extraInfo.acctMulaidPay | formatMoney }}
                            </div>

                            <!-- 统筹支付 -->
                            <div>
                                {{ shebaoPayment.fundPaymentFee | formatMoney }}
                            </div>
                            <!-- 统筹基金 -->
                            <div style="padding-left: 15pt;">
                                {{ extraInfo.hifpPay | formatMoney }}
                            </div>
                            <!-- 公务员补助 -->
                            <div style="padding-left: 24pt;">
                                {{ extraInfo.cvlservPay | formatMoney }}
                            </div>
                        </div>
                        <div style="position: absolute; top: 0; left: 39mm; width: 42mm;">
                            <!-- 个人自付 -->
                            <div>
                                {{ shebaoPayment.selfPaymentFee | formatMoney }}
                            </div>
                            <div>
                                &nbsp;
                            </div>
                            <div>
                                &nbsp;
                            </div>
                            <!-- 实付现金： -->
                            <div style="padding-left: 15pt;">
                                {{ printData.personalPaymentFee | formatMoney }}
                            </div>

                            <!-- 补充保险支付 -->
                            <div style="padding-left: 15pt;">
                                {{ extraInfo.hifesPay | formatMoney }}
                            </div>
                            <!-- 医疗账户余额 -->
                            <div style="padding-left: 15pt;">
                                {{ extraInfo.purAcctBalc | formatMoney }}
                            </div>
                            <!-- 健康账户余额 -->
                            <div style="padding-left: 15pt;">
                                {{ extraInfo.hlAcctBalc | formatMoney }}
                            </div>
                        </div>
                    </div>
                    <div>
                        <div
                            class="form-items-wrapper"
                        >
                            <div
                                v-for="(item, index) in page.formItems"
                                :key="index + pageIndex"
                                class="form-item-tr"
                            >
                                <span class="item-name">
                                    <template
                                        v-if="isShowSocialCode(item)"
                                    >
                                        [{{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}]
                                    </template>
                                    {{ item.name }}
                                </span>
                                <span class="item-count">{{ item.discountedUnitPrice | formatMoney }} * {{ item.count }}{{ item.unit }}</span>
                                <span class="total-price">{{ item.discountedPrice | formatMoney }}</span>
                                <div class="item-proportion">
                                    {{ item.ownExpenseRatio | filterOwnExpenseRatio }}
                                </div>
                            </div>

                            <div
                                v-if="hasOverPageTip"
                                class="only-one-page"
                            >
                                *** 因纸张限制，部分项目未打印 ***
                            </div>
                            <template v-else>
                                <div
                                    v-if="pageIndex !== renderPage.length - 1"
                                    class="next-page"
                                >
                                    *** 接下页 ***
                                </div>
                            </template>
                        </div>
                    </div>
                    <!-- 西药费 -->
                    <block-box
                        top="42"
                        left="29"
                    >
                        {{ westernMedicineFee|formatMoney }}
                    </block-box>
                    <!-- 中成药费 -->
                    <block-box
                        top="48.5"
                        left="29"
                    >
                        {{ chineseComposeMedicineFee|formatMoney }}
                    </block-box>
                    <!-- 中草药费 -->
                    <block-box
                        top="55"
                        left="29"
                    >
                        {{ chineseMedicineDrinksPieceFee|formatMoney }}
                    </block-box>
                    <!-- 检查费 -->
                    <block-box
                        top="61"
                        left="29"
                    >
                        {{ examinationInspectionFee|formatMoney }}
                    </block-box>
                    <!-- 治疗费 -->
                    <block-box
                        top="67.5"
                        left="29"
                    >
                        {{ treatmentFee|formatMoney }}
                    </block-box>
                    <!-- 手术费 -->
                    <block-box
                        v-if="isFeeCompose"
                        top="48.5"
                        left="59"
                    >
                        {{ operationFee|formatMoney }}
                    </block-box>
                    <!-- 化验费 -->
                    <block-box
                        top="55"
                        left="59"
                    >
                        {{ examinationExaminationFee|formatMoney }}
                    </block-box>
                    <!-- 诊察费 -->
                    <block-box
                        v-if="isFeeCompose"
                        top="42"
                        left="89"
                    >
                        {{ examinationFee|formatMoney }}
                    </block-box>
                    <!-- 床位费 -->
                    <block-box
                        v-if="isFeeCompose"
                        top="48.5"
                        left="89"
                    >
                        {{ bedFee|formatMoney }}
                    </block-box>
                    <!-- 挂号费 -->
                    <block-box
                        top="55"
                        left="89"
                    >
                        {{ registrationFee|formatMoney }}
                    </block-box>
                    <!-- 其他费用 -->
                    <block-box
                        top="61"
                        left="89"
                    >
                        {{ otherFeeTotal|formatMoney }}
                    </block-box>

                    <!-- 大写金额 -->
                    <div class="upper-money">
                        {{ digitUppercase(finalFee) }}
                    </div>
                    <!-- 小写金额 -->
                    <div
                        class="upper-money"
                        style="left: 151mm;"
                    >
                        {{ finalFee | formatMoney }}
                    </div>
                    <div class="organ">
                        {{ xiamenXin.institutionName }}
                    </div>
                    <div class="charger">
                        {{ printData.chargedByName }}
                    </div>
                    <div class="year">
                        {{ printData.chargedTime | parseTime('y') }}
                    </div>
                    <div class="month">
                        {{ printData.chargedTime | parseTime('m') }}
                    </div>
                    <div class="day">
                        {{ printData.chargedTime | parseTime('d') }}
                    </div>
                </div>
            </div>
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import NationalBillData from "./mixins/national-bill-data";

    export default {
        name: "MedicalBillXiamenXin",
        components: {
            RefundIcon,
            BlockBox
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_XIAMEN_XIN,
        pages: [
            {
                paper: PageSizeMap.MM210_127_XIAMEN_XIN,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            xiamenXin() {
                return this.config.xiamenXin || {}
            },
            splitType() {
                return this.xiamenXin.splitType;
            },
            isOnlyOnePage() {
                if (this.splitType === 1) {
                    return this.renderPage.length > 1;
                }
                return false;
            },
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                if (this.isOnlyOnePage || this.extra.isPreview) {
                    return this.renderPage.slice(0, 1);
                }
                return this.renderPage;
            },
            medicalBills() {
                return this.printData.medicalBills &&this.printData.medicalBills.filter(billItem => {
                    if (billItem.name === this.$t('registrationFeeName')) {
                        return !!billItem.totalFee
                    }
                    return true
                }) || [];
            },
            isNeedResetComposeSocialFee() {
                return true;
            },
            renderPage() {
                return this.spliceFormItems(this.chargeFormItems, 14);
            },
            otherFeeTotal() {
                if (this.isFeeCompose) {
                    return this.materialFee + this.nursingFee + this.physiotherapyFee + this.generalDiagnosisAndTreatmentFee + this.pharmacyServiceFee + this.otherFee + this.outerFlagFee;
                }
                return this.materialFee + this.examinationFee + this.nursingFee + this.physiotherapyFee + this.bedFee + this.operationFee + this.generalDiagnosisAndTreatmentFee + this.pharmacyServiceFee + this.otherFee + this.outerFlagFee;
            }
        },
        created() {
            this.initFee();
        },
    }
</script>
<style lang="scss">
* {
    padding: 0;
    margin: 0;
}

.xiamen-xin-medical-bill-content {
    @import "./components/refund-icon/refund-icon.scss";

    font-size: 9pt;

    .xiamen-xin-medical-bill-page {
        position: absolute;
        width: 210mm;
        height: 127mm;

        &.is-electron {
            height: 127mm;
        }
        //border: 1pt solid #00ace9;
    }

    .trans-number,
    .patient,
    .form-items-wrapper,
    .medical-bill-wrapper {
        position: absolute;
        line-height: 11pt;
    }

    .trans-number {
        top: 26mm;
        left: 55mm;
    }

    .patient {
        top: 26mm;
    }

    .form-items-wrapper {
        top: 93mm;
        left: 18mm;
        width: 179mm;
        height: 33mm;
    }

    .next-page {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        text-align: center;
    }

    .form-item-tr {
        display: inline-block;
        width: 46%;
        height: 4mm;
        font-size: 0;
        line-height: 11pt;

        &:nth-of-type(odd) {
            float: left;
        }

        &:nth-of-type(even) {
            float: right;
        }
    }

    .item-name,
    .item-count,
    .item-proportion,
    .total-price {
        display: inline-block;
        *display: inline;
        overflow: hidden;
        font-size: 9pt;
        word-break: keep-all;
        white-space: nowrap;
        *zoom: 1;
    }

    .shebao-wrapper {
        position: absolute;
        top: 42mm;
        left: 120mm;
        line-height: 12pt;
    }

    .item-name {
        width: 40%;
    }

    .item-count {
        width: 30%;
        text-align: right;
    }

    .item-proportion {
        width: 14%;
        text-align: right;
    }

    .total-price {
        width: 15%;
        *width: 15%;
        text-align: right;
    }

    .medical-bill-wrapper {
        top: 37mm;
        left: 23mm;
        width: 62mm;
        height: 31mm;
    }

    .medical-bill-item {
        position: absolute;
        width: 31mm;
    }

    .upper-money {
        position: absolute;
        top: 74mm;
        left: 46mm;
    }

    .organ,
    .charger, {
        position: absolute;
        top: 80mm;
        line-height: 12pt;
    }

    .year {
        position: absolute;
        top: 30mm;
        left: 162mm;
        line-height: 12pt;
    }

    .day {
        position: absolute;
        top: 30mm;
        left: 182.5mm;
        line-height: 12pt;
    }

    .month {
        position: absolute;
        top: 30mm;
        left: 174mm;
        line-height: 12pt;
    }

    .organ {
        left: 38mm;
    }

    .charger {
        left: 157mm;
    }

    .only-one-page {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        text-align: center;
    }
}

.abc-page_preview {
    color: #2a82e4;
    background: url("/static/assets/print/xiamen-new.png");
    background-size: 210mm 127mm;

    .xiamen-xin-medical-bill-page {
        top: -1mm;
    }
}
</style>
