<!--exampleData

{
    "id": "ffffffff000000001bc1721010b60000",
    "patient": {
        "id": "ffffffff000000001bc17db00f42e000",
        "name": "打印重构",
        "namePy": "dayinzhong<PERSON>u",
        "namePyFirst": "DYZG",
        "birthday": "1999-11-04",
        "mobile": null,
        "sex": "男",
        "idCard": null,
        "isMember": 0,
        "age": {
            "year": 22,
            "month": 0,
            "day": 25
        },
        "address": null,
        "sn": "000460",
        "remark": null,
        "profession": null,
        "company": null,
        "patientSource": null,
        "tags": [ ],
        "wxOpenId": null,
        "wxHeadImgUrl": null,
        "wxNickName": null,
        "wxBindStatus": 0,
        "isAttention": 0,
        "shebaoCardInfo": null,
        "childCareInfo": null,
        "chronicArchivesInfo": null
    },
    "patientOrderNo": 9298,
    "organ": {
        "id": "fff730ccc5ee45d783d82a85b8a0e52d",
        "name": "高新大源店",
        "shortName": "高新大源店",
        "addressDetail": "趵突泉",
        "contactPhone": "18910121190",
        "logo": "https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/headimg_dev/rGjBGBN61NXILXt6Yu2Z2OZNSGYXOcnM_1579403432353.jpg",
        "category": "社区卫生服务站"
    },
    "departmentName": "www",
    "diagnosis": "暂无现病史暂无现病史，感冒病",
    "doctorAdvice": "1.服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。<br>3.多喝热水<br>4.谨遵医嘱<br>5.饮食清淡",
    "syndrome": "表寒里饮，肺热咳嗽",
    "medicalRecord": {
        "chiefComplaint": "咳嗽，干咳",
        "pastHistory": "暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史",
        "familyHistory": "",
        "presentHistory": "暂无现病史，暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史暂无现病史",
        "physicalExamination": "",
        "diagnosis": "暂无现病史暂无现病史，感冒病",
        "doctorAdvice": "1.服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。<br>3.多喝热水<br>4.谨遵医嘱<br>5.饮食清淡",
        "syndrome": "表寒里饮，肺热咳嗽",
        "therapy": "暂无现病史暂无现病史",
        "chineseExamination": "暂无现病史暂无现病史暂无现病史",
        "birthHistory": null,
        "oralExamination": "[{\"positions\":[],\"describes\":[\"暂无现病史\"]},{\"positions\":[{\"position\":\"top-left\",\"dataNo\":[\"7\"]}],\"describes\":[\"松动0度\"]}]",
        "epidemiologicalHistory": "{\"patientChecked\":true,\"attendantChecked\":false,\"suspiciousList\":[],\"symptomList\":[{\"label\":\"暂无现病史暂无现病史暂无现病史\",\"value\":null}]}",
        "auxiliaryExamination": null,
        "obstetricalHistory": "[\"暂无现病史暂无现病史暂无现病史暂无现病史\"]",
        "chinesePrescription": "小青龙汤",
        "dentistryExaminations": null,
        "dentistryDiagnosisInfos": null,
        "treatmentPlans": null,
        "disposals": null
    },
    "healthCardNo": null,
    "healthCardPayLevel": null,
    "doctorName": "张雪峰",
    "doctorSignImgUrl": "https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/signature/5VfqVg93y8fH8mqlZX6sA9fnPGFXq3zJ_1582885332942",
    "diagnosedDate": "2021-11-04T08:56:22Z",
    "totalPrice": 522.89,
    "medicineTotalPrice": 137.09,
    "dispensedBy": null,
    "dispensedByName": null,
    "auditBy": null,
    "auditName": null,
    "shebaoCardInfo": null,
    "productForms": [],
    "prescriptionChineseForms": [],
    "prescriptionWesternForms": [],
    "prescriptionInfusionForms": [],
    "prescriptionExternalForms": []
}

-->

<template>
    <div>
        <outpatient-header
            :print-data="printData"
            print-title="诊断证明书"
            :config="config"
            :organ-title="organTitle"
            data-type="header"
        ></outpatient-header>

        <template v-for="item in sortedMrStruct">
            <div
                v-if="item.key === 'chiefComplaint' && medicalRecord.chiefComplaint && showChiefComplaint"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">主 诉</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                >
                    <div
                        v-for="(item, index) in chiefComplaintList"
                        :key="`chief-complaint-${index}`"
                    >
                        {{ item }}
                        <template v-if="medicalRecord.symptomTime && index === chiefComplaintList.length - 1">
                            ；发病日期：{{ medicalRecord.symptomTime | parseTime('y-m-d') }}
                        </template>
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'presentHistory' && medicalRecord.presentHistory && showPresentHistory"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">现 病 史</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    v-html="medicalRecord.presentHistory"
                >
                </div>
            </div>
            <div
                v-if="item.key === 'pastHistory' && medicalRecord.pastHistory && showPastHistory"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">既 往 史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos"
                    v-html="medicalRecord.pastHistory"
                >
                </div>
            </div>
            <div
                v-if="item.key === 'personalHistory' && medicalRecord.personalHistory && showPersonalHistory"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">个 人 史</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    v-html="medicalRecord.personalHistory"
                >
                </div>
            </div>
            <div
                v-if="item.key === 'familyHistory' && medicalRecord.familyHistory && showFamilyHistory"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">家 族 史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos"
                    v-html="medicalRecord.familyHistory"
                >
                </div>
            </div>
            <div
                v-if="item.key === 'obstetricalHistory' && medicalRecord.obstetricalHistory && showObstetricalHistory"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">月经婚育史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos obstetrical-history"
                    v-html="formatObstetricalHistory2Str(medicalRecord.obstetricalHistory)"
                >
                </div>
            </div>
            <div
                v-if="item.key === 'epidemiologicalHistory' && medicalRecord.epidemiologicalHistory && showEpidemiologicalHistory"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">流 行 病 史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos"
                    v-html="formatEpidemiologicalHistory2Str(medicalRecord.epidemiologicalHistory)"
                >
                </div>
            </div>
            <div
                v-if="item.key === 'physicalExamination' && medicalRecord.physicalExamination && showPhysicalExamination"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">体 格 检 查</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    v-html="medicalRecord.physicalExamination"
                >
                </div>
            </div>
            <div
                v-if="item.key === 'chineseExamination' && medicalRecord.chineseExamination && showChineseExamination"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">望 闻 切 诊</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    v-html="medicalRecord.chineseExamination"
                >
                </div>
            </div>

            <div
                v-if="item.key === 'tongue' && medicalRecord.tongue && tongueList.length && !showCustomizeMedical"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">舌 象</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(tongueText, idx) in tongueList"
                        :key="`tongue-${idx}`"
                        data-type="item"
                        v-html="tongueText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'pulse' && medicalRecord.pulse && pulseList.length && !showCustomizeMedical"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">脉 象</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(pulseText, idx) in pulseList"
                        :key="`pulse-${idx}`"
                        data-type="item"
                        v-html="pulseText"
                    >
                    </div>
                </div>
            </div>

            <template v-if="item.key === 'oralExamination' && (medicalRecord.oralExamination || medicalRecord.dentistryExaminations) && showOralExamination">
                <div
                    v-if="isDentistry"
                    :key="item.key"
                    class="medical-item"
                    data-type="mix-box"
                >
                    <div class="label-title">
                        <span class="label">口 腔 检 查</span>
                        <span>：</span>
                    </div>
                    <div
                        class="infos oral-examination-wrapper"
                        v-html="formatDentistry2Html(medicalRecord.dentistryExaminations)"
                    >
                    </div>
                </div>
                <div
                    v-else
                    :key="item.key"
                    class="medical-item"
                    data-type="mix-box"
                >
                    <div class="label-title">
                        <span class="label">口 腔 检 查</span>
                        <span>：</span>
                    </div>
                    <div
                        class="infos oral-examination-wrapper"
                        v-html="formatOralExamination2Html(medicalRecord.oralExamination)"
                    >
                    </div>
                </div>
            </template>
            <div
                v-if="item.key === 'auxiliaryExaminations' && medicalRecord.auxiliaryExaminations && showAuxiliaryExaminations"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">辅 助 检 查</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    v-html="formatDentistry2Html(medicalRecord.auxiliaryExaminations)"
                >
                </div>
            </div>
            <template v-if="item.key === 'diagnosis' && medicalRecord.extendDiagnosisInfos && showDiagnosis">
                <div
                    v-if="!isSeparateChinaWestDiagnosis"
                    :key="item.key"
                    class="medical-item"
                    data-type="mix-box"
                >
                    <div class="label-title">
                        <span class="label">诊 断</span>
                        <span>：</span>
                    </div>
                    <div class="infos">
                        <div
                            style="display: inline-block;"
                            v-html="formatDentistry2HtmlByWesternOrChinese(medicalRecord.extendDiagnosisInfos)"
                        ></div>
                        <template v-if="medicalRecord.syndrome">
                            （{{ medicalRecord.syndrome }}）
                        </template>
                    </div>
                </div>

                <template v-else>
                    <div
                        v-for="type in [1, 2]"
                        :key="`${item.key}-${type}`"
                        class="medical-item"
                        data-type="mix-box"
                    >
                        <div class="label-title">
                            <span class="label">{{ type === 1 ? '西 医' : '中 医' }} 诊 断</span>
                            <span>：</span>
                        </div>
                        <div class="infos">
                            <div
                                style="display: inline-block;"
                                v-html="formatDentistry2HtmlByWesternOrChinese(medicalRecord.extendDiagnosisInfos, type)"
                            ></div>
                            <template v-if="medicalRecord.syndrome">
                                （{{ medicalRecord.syndrome }}）
                            </template>
                        </div>
                    </div>
                </template>
            </template>
            <div
                v-if="item.key === 'treatmentPlans' && medicalRecord.treatmentPlans && medicalRecord.treatmentPlans.length && !showCustomizeMedical"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">治 疗 计 划</span>
                    <span>：</span>
                </div>
                <div class="infos">
                    <span v-html="formatDentistry2Html(medicalRecord.treatmentPlans)"></span>
                </div>
            </div>
            <div
                v-if="item.key === 'disposals' && medicalRecord.disposals && medicalRecord.disposals.length && showDisposals"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">处 置</span>
                    <span>：</span>
                </div>
                <div class="infos">
                    <span v-html="formatDentistry2Html(medicalRecord.disposals)"></span>
                </div>
            </div>
            <div
                v-if="item.key === 'therapy' && medicalRecord.therapy && showTherapy"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">治 法</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    v-html="medicalRecord.therapy"
                >
                </div>
            </div>
            <div
                v-if="item.key === 'chinesePrescription' && medicalRecord.chinesePrescription && showChinesePrescription"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">方 药</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    v-html="medicalRecord.chinesePrescription"
                >
                </div>
            </div>
        </template>
        <div
            v-if="medicalRecord.doctorAdvice && showDoctorAdvice"
            class="medical-item"
            data-type="mix-box"
        >
            <div class="label-title">
                <span class="label">医 嘱</span>
                <span>：</span>
            </div>
            <div
                class="infos"
                v-html="medicalRecord.doctorAdvice"
            >
            </div>
        </div>

        <next-is-blank></next-is-blank>

        <outpatient-footer
            :print-data="printData"
            data-type="footer"
            print-type="medicalCertificate"
            :config="config"
        ></outpatient-footer>
    </div>
</template>

<script>
    import OutpatientHeader from './components/medical-document-header/execute-header.vue';
    import OutpatientFooter from './components/medical-document-footer/index.vue';

    import PrintCommonHandler from './data-handler/print-handler.js'
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import {
        formatDentistry2Html, formatDentistry2HtmlByWesternOrChinese,
        formatEpidemiologicalHistory2Str,
        formatObstetricalHistory2Str,
        formatOralExamination2Html,
    } from "./common/medical-transformat.js";
    import { CLINIC_TYPE, MedicalRecordTypeEnum } from './common/constants';
    import { splitByBr } from "./common/utils";
    import NextIsBlank from './components/next-is-blank/index.vue';

    export default {
        DataHandler: PrintCommonHandler,
        name: "Infusion",
        components: {
            OutpatientHeader,
            OutpatientFooter,
            NextIsBlank,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
            injectConfig: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        businessKey: PrintBusinessKeyEnum.MEDICAL_CERTIFICATE,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM95_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分', // 默认选择的等分纸
            },
        ],
        computed: {
            clinicMrConfig() {
                return this.injectConfig && this.injectConfig.clinicMrConfig || {};
            },
            getMrTypePropertyKey() {
                return this.injectConfig && this.injectConfig.getMrTypePropertyKey;
            },
            viewDistributeOutpatientConfig() {
                return this.injectConfig && this.injectConfig.viewDistributeOutpatientConfig;
            },
            sortedMrStruct() {
                const {
                    multiMedicalRecord, defaultMedicalRecordType,
                } = this.viewDistributeOutpatientConfig || {};
                if (multiMedicalRecord === false) {
                    return this.clinicMrConfig[this.getMrTypePropertyKey()[defaultMedicalRecordType]];
                }
                return this.clinicMrConfig[this.getMrTypePropertyKey()[this.medicalRecord.type ?? 0]]
            },
            chiefComplaintList() {
                return splitByBr(this.medicalRecord.chiefComplaint);
            },
            printData() {
                return this.renderData && this.renderData.printData || {};
            },
            config() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.illnessCert) {
                    return this.renderData.config.medicalDocuments.illnessCert;
                }
                return {};
            },
            headerConfig() {
                return this.config.header || {};
            },
            contentConfig() {
                return this.config.content || {};
            },
            medicalRecord() {
                return this.printData && this.printData.medicalRecord || {};
            },
            organ() {
                return this.printData.organ;
            },
            organTitle() {
                if (!this.headerConfig.title) {
                    return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.illnessCert || '';
                }
                return this.headerConfig.title;
            },
            medicalRecordType() {
                return this.medicalRecord.type;
            },
            isDentistry() {
                return this.organ &&
                    ( this.organ.hisType === CLINIC_TYPE.DENTISTRY
                        || (this.organ.hisType === CLINIC_TYPE.HOSPITAL && this.medicalRecordType === MedicalRecordTypeEnum.ORAL));
            },
            tongueList() {
                return splitByBr(this.medicalRecord.tongue);
            },
            pulseList() {
                return splitByBr(this.medicalRecord.pulse);
            },
            // 区分打印中西医诊断
            isSeparateChinaWestDiagnosis() {
                return !!this.contentConfig.separateChinaWestDiagnosis;
            },
            // 是否展示自定义病历内容
            showCustomizeMedical() {
                return !!this.contentConfig.medical;
            },
            // 是否展示主诉
            showChiefComplaint() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.chiefComplaint;
                }
                return true;
            },
            // 是否展示现病史
            showPresentHistory() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.presentHistory;
                }
                return true;
            },
            // 是否展示既往史
            showPastHistory() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.pastHistory;
                }
                return true;
            },
            // 是否展示家族史
            showFamilyHistory() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.familyHistory;
                }
                return true;
            },
            // 是否展示个人史
            showPersonalHistory() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.personalHistory;
                }
                return true;
            },
            // 是否展示月经婚育史
            showObstetricalHistory() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.obstetricalHistory;
                }
                return true;
            },
            // 是否展示流行病史
            showEpidemiologicalHistory() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.epidemiologicalHistory;
                }
                return true;
            },
            // 是否展示体格检查
            showPhysicalExamination() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.physicalExamination;
                }
                return true;
            },
            // 是否展示望闻切诊
            showChineseExamination() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.chineseExamination;
                }
                return true;
            },
            // 是否展示口腔检查
            showOralExamination() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.oralExamination;
                }
                return true;
            },
            // 是否展示辅助检查
            showAuxiliaryExaminations() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.auxiliaryExaminations;
                }
                return true;
            },
            // 是否展示诊断
            showDiagnosis() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.diagnosis;
                }
                return true;
            },
            // 是否展示治法
            showTherapy() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.therapy;
                }
                return true;
            },
            // 是否展示方药
            showChinesePrescription() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.chinesePrescription;
                }
                return true;
            },
            // 是否展示处置
            showDisposals() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.disposals;
                }
                return true;
            },
            // 是否展示医嘱
            showDoctorAdvice() {
                if (this.showCustomizeMedical) {
                    return !!this.contentConfig.doctorAdvice;
                }
                return true;
            },
        },
        methods: {
            formatObstetricalHistory2Str,
            formatEpidemiologicalHistory2Str,
            formatOralExamination2Html,
            formatDentistry2Html,
            formatDentistry2HtmlByWesternOrChinese,
        },
    }
</script>

<style lang="scss">
@import "./components/layout/print-layout.scss";
@import "./style/reset.scss";

.abc-page-content {
    box-sizing: border-box;
    font-family: "Microsoft YaHei", 微软雅黑;
}

.medical-item {
    position: relative;
    min-height: 12pt;
    padding-left: 60pt;
    margin-top: 6pt;
    font-size: 10pt;
    font-weight: 300;
    line-height: 12pt;

    &.margin-top-item {
        margin-top: 6pt;
    }

    .label-title {
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        width: 60pt;
        font-size: 0;

        .label {
            position: relative;
            display: inline-block;
            width: 50pt;
            font-size: 10pt;
            font-weight: bold;
            line-height: 12pt;
            text-align: justify;
            text-align-last: justify;
            text-justify: distribute;
            word-break: break-all;

            &::after {
                display: inline-block;
                width: 100%;
                content: '';
            }

            & + span {
                position: absolute;
                top: 0;
                right: 0;
                font-size: 10pt;
                line-height: 12pt;
                vertical-align: inherit;
            }
        }
    }

    .infos {
        font-size: 10pt;
        line-height: 12pt;

        &.oral-examination-wrapper {
            span {
                display: inline-block;
                padding: 1pt;
                font-family: Roboto;
                font-size: 8pt;
                line-height: 8pt;
            }

            span.top-left {
                border-right: 1pt solid #878c92;
                border-bottom: 1pt solid #878c92;
            }

            span.top-right {
                border-bottom: 1pt solid #878c92;
                border-left: 1pt solid #878c92;
            }

            span.bottom-left {
                border-top: 1pt solid #878c92;
                border-right: 1pt solid #878c92;
            }

            span.bottom-right {
                border-top: 1pt solid #878c92;
                border-left: 1pt solid #878c92;
            }
        }

        &.obstetrical-history {
            .menstruation {
                display: inline-flex;
                align-items: center;

                > span {
                    display: inline-flex;
                    flex-direction: column;
                    margin: 0 4pt;
                    text-align: center;

                    span {
                        height: 9pt;
                        font-size: 9pt;
                        line-height: 9pt;
                    }

                    .frasl {
                        width: 100%;
                        height: 0;
                        border-bottom: 1pt solid rgba(0, 0, 0, 0.5);
                    }
                }
            }
        }
    }
}

.global-tooth-selected-quadrant {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    width: auto;
    min-width: 32pt;
    height: 17pt;
    vertical-align: middle;

    .top-tooth,
    .bottom-tooth {
        display: flex;
        align-items: center;
        width: 100%;
        min-width: 32pt;
    }

    .left-tooth,
    .right-tooth {
        display: flex;
        align-items: center;
        width: 50%;
        height: 7pt;
        padding: 0 1pt;
        font-family: 'MyKarlaRegular';
        font-size: 9pt;
        letter-spacing: 1px;
        user-select: none;
    }

    .left-tooth {
        justify-content: flex-end;
        border-right: 1pt solid #7a8794;
    }

    .top-tooth {
        min-width: 32pt;
        border-bottom: 1pt solid #7a8794;

        > div {
            padding-bottom: 1px;
        }
    }

    .bottom-tooth {
        > div {
            padding-top: 1px;
        }
    }

    &.all-tooth {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }

    .all-tooth {
        min-width: 18pt;
        height: 11pt;
        font-size: 9pt;
        line-height: 11pt;
    }

    &.no-data {
        .left-tooth {
            border-right: 1px dashed #000000;
        }

        .top-tooth {
            border-bottom: 1px dashed #000000;
        }
    }
}

</style>
