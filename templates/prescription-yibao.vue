<!--exampleData
{
  "id": "ffffffff000000001d6f3f8806944000",
  "patient": {
    "id": "ffffffff000000001d6f9c200605e000",
    "name": "任盈盈",
    "namePy": "renyingying",
    "namePyFirst": "RYY",
    "birthday": "1999-12-15",
    "mobile": "13900000000",
    "sex": "女",
    "idCard": null,
    "isMember": 1,
    "age": {
      "year": 22,
      "month": 0,
      "day": 6
    },
    "address": null,
    "sn": "000881",
    "remark": null,
    "profession": null,
    "company": null,
    "patientSource": null,
    "tags": [],
    "marital": null,
    "weight": null,
    "wxOpenId": null,
    "wxHeadImgUrl": null,
    "wxNickName": null,
    "wxBindStatus": 0,
    "isAttention": 0,
    "shebaoCardInfo": null,
    "childCareInfo": null,
    "chronicArchivesInfo": null
  },
  "patientOrderNo": '150200021984718528748800009388',
  "organ": {
    "id": "9daeca0eab074c639892433ffef36f94",
    "name": "重庆益牙堂口腔医院管理连锁有限公司大渡口杨渡路口腔诊所",
    "shortName": "重庆益牙堂口腔医院管理连锁有限公司大渡口杨渡路口腔诊所",
    "addressDetail": "惠民佳苑北路东南侧西部122号别墅路110室从火车北站下车后，往南走10公里，左转第二个红绿灯路口右转，前方100米就到了",
    "contactPhone": "",
    "logo": "https://cis-images-test.oss-cn-shanghai.aliyuncs.com/headimg_test/jVlBR6MsFsWZ9Nm3ShdLbE8CazFmpcpD_1603681040002.png",
    "category": "妇幼保健院",
    "hospitalCode": "H32050800195",
    "region": "jilin_changchun"
  },
  "mdtrtId": '202406071037174214',
  "departmentName": "全科医疗科",
  "prescriptionNo": "201808020001",
  "diagnosis": "急性上呼吸道感染",
  "valiDays": "1",
  "doctorAdvice": "1.饮食规律宜清淡，忌烟酒，忌辛辣荤腥<br>2.多喝水，保持身体充足水分<br>3.少吃油炸、腌制、生冷、麻辣等刺激性食物",
  "syndrome": "",
  "extendsReason": "",
  "medicalRecord": {
    "chiefComplaint": "咳嗽，夜咳，咽痛，咽干",
    "pastHistory": "既往体健",
    "familyHistory": "否认家族遗传病史",
    "presentHistory": "①头痛头晕；②失眠多梦；③噩梦<br>已经持续5年，久病不治，多方寻医未果，尝试过中西医各种疗法",
    "physicalExamination": "扁桃体肿大，咽部充血，颈淋巴结肿大",
    "diagnosis": "急性上呼吸道感染",
    "doctorAdvice": "1.饮食规律宜清淡，忌烟酒，忌辛辣荤腥<br>2.多喝水，保持身体充足水分<br>3.少吃油炸、腌制、生冷、麻辣等刺激性食物",
    "syndrome": "",
    "therapy": "",
    "chineseExamination": "",
    "birthHistory": null,
    "oralExamination": "[{\"positions\":[{\"position\":\"top-left\",\"dataNo\":[\"5\"]}],\"describes\":[\"冷诊正常\"]},{\"positions\":[{\"position\":\"top-right\",\"dataNo\":[\"2\"]}],\"describes\":[\"残冠\"]}]",
    "epidemiologicalHistory": "{\"patientChecked\":true,\"attendantChecked\":false,\"suspiciousList\":[{\"label\":\"发热\",\"value\":\"无\"},{\"label\":\"干咳\",\"value\":\"无\"},{\"label\":\"乏力\",\"value\":\"无\"},{\"label\":\"鼻塞\",\"value\":\"无\"},{\"label\":\"流涕\",\"value\":\"无\"},{\"label\":\"咽痛\",\"value\":\"无\"},{\"label\":\"肌痛\",\"value\":\"无\"},{\"label\":\"腹泻\",\"value\":\"无\"},{\"label\":\"结膜炎\",\"value\":\"无\"},{\"label\":\"嗅觉味觉减退\",\"value\":\"无\"}],\"symptomList\":[{\"label\":\"患者可疑症状排查：\",\"isSuspicious\":true}]}",
    "auxiliaryExamination": null,
    "obstetricalHistory": "[{\"type\":\"pregnant\",\"birthCount\":1,\"pregnantCount\":1},{\"type\":\"menstruation\",\"menophaniaAge\":13,\"menstruationDays\":[5],\"menstrualCycle\":[28],\"menopauseTab\":1,\"menopauseDate\":\"2021-12-19\",\"menopauseAge\":\"\"}]",
    "chinesePrescription": null,
    "dentistryExaminations": null,
    "dentistryDiagnosisInfos": null,
    "treatmentPlans": null,
    "disposals": null
  },
  "healthCardNo": null,
  "healthCardPayLevel": "处方外购",
  "doctorName": "徐彩云",
  "doctorCode": "D320505000000",
  "institutionalSeal": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000216fa080011b0000/basic/wecom-temp-16001-e728fceb9fa7d589c8f4efd7e24a5c0a_O91DJ7TeGjqB.png",
  "doctorSignImgUrl": "https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/YbYB5IN4f5BcddjoHoBBJwPUMu4YTYTp_1717603197409",
  "totalPrice": 1074.2300,
  "medicineTotalPrice": 171.23,
  "dispensedBy": null,
  "dispensedByName": null,
  "auditBy": null,
  "auditName": '张永平',
  "auditNameSignImgUrl": 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/g2jQcWgzMfGew9hzPqDrWrkA7FxBBb4a_1718166714429',
  "revisitStatus": null,
  "prscTime": '2025-03-10',
  "barcode": null,
  "traceabilityInfo": {
    rxTraceCode: '15145287080098922500',
    rxTraceQrCode: 'data:image/png;base64,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'
  },
  "shebaoCardInfo": null,
  "productForms": [
    {
      "id": "ffffffff000000001d6fb8b006942000",
      "sort": 0,
      "sourceFormType": 2,
      "productFormItems": [
        {
          "id": "ffffffff000000001d6fb8b006942001",
          "productId": "ffffffff000000001d6fa16806a8c000",
          "name": "超敏C反应蛋白测定",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 20.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 20.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 0,
          "type": 3,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942002",
          "productId": "ffffffff000000001d6fa0e806a8c000",
          "name": "全血细胞分析",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 20.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 20.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 1,
          "type": 3,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942003",
          "productId": "5ad7300943824a78b353e74d97f682da",
          "name": "肺炎支原体抗体",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 50.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 50.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 2,
          "type": 3,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        }
      ]
    },
    {
      "id": "ffffffff000000001d6fb8b006942004",
      "sort": 1,
      "sourceFormType": 3,
      "productFormItems": [
        {
          "id": "ffffffff000000001d6fb8b006942005",
          "productId": "ffffffff000000001d70afa006a94000",
          "name": "骨伤、颈腰整脊手法",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 100.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 100.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 0,
          "type": 4,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942006",
          "productId": "455311173fe3484da26d6e45d85bf192",
          "name": "三位一体单次",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 168.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 168.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 1,
          "type": 4,
          "subType": 1,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        },
        {
          "id": "ffffffff000000001d6fb8b006942007",
          "productId": "816f47cb74f343979e31f6acfb025805",
          "name": "穴位埋线（普通）",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 35.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 35.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 2,
          "type": 4,
          "subType": 2,
          "composeType": 0,
          "composeParentFormItemId": null,
          "composeChildren": [],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        }
      ]
    },
    {
      "id": "ffffffff000000001d70a9b006942000",
      "sort": 2,
      "sourceFormType": 11,
      "productFormItems": [
        {
          "id": "ffffffff000000001d70a9b006942001",
          "productId": "ffffffff000000001d70a5b806a96000",
          "name": "综合调养套餐",
          "unitCount": 1.000,
          "unit": "次",
          "unitPrice": 500.0000,
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 500.0000,
          "expectedUnitPrice": null,
          "useDismounting": 0,
          "sort": 0,
          "type": 11,
          "subType": 1,
          "composeType": 1,
          "composeParentFormItemId": null,
          "composeChildren": [
            {
              "id": "ffffffff000000001d70a9b006942002",
              "productId": "17f37531a01a4471a76b4a1dc7e45738",
              "name": "穴位贴敷",
              "unitCount": 5.000,
              "unit": "次",
              "unitPrice": 20.0000,
              "fractionPrice": 0.0000,
              "sourceUnitPrice": null,
              "expectedUnitPrice": null,
              "useDismounting": 0,
              "sort": 0,
              "type": 4,
              "subType": 2,
              "composeType": 2,
              "composeParentFormItemId": "ffffffff000000001d70a9b006942001",
              "composeChildren": [],
              "chargeStatus": 1,
              "days": 1,
              "remark": null
            },
            {
              "id": "ffffffff000000001d70a9b006942003",
              "productId": "c49fb73100694d84b78ff57bed7bdd2f",
              "name": "电针灸",
              "unitCount": 5.000,
              "unit": "次",
              "unitPrice": 60.0000,
              "fractionPrice": 0.0000,
              "sourceUnitPrice": null,
              "expectedUnitPrice": null,
              "useDismounting": 0,
              "sort": 1,
              "type": 4,
              "subType": 1,
              "composeType": 2,
              "composeParentFormItemId": "ffffffff000000001d70a9b006942001",
              "composeChildren": [],
              "chargeStatus": 1,
              "days": 1,
              "remark": null
            },
            {
              "id": "ffffffff000000001d70a9b006942004",
              "productId": "ba9379e2403442788cacb63927083fd5",
              "name": "推拿",
              "unitCount": 5.000,
              "unit": "次",
              "unitPrice": 20.0000,
              "fractionPrice": 0.0000,
              "sourceUnitPrice": null,
              "expectedUnitPrice": null,
              "useDismounting": 0,
              "sort": 2,
              "type": 4,
              "subType": 1,
              "composeType": 2,
              "composeParentFormItemId": "ffffffff000000001d70a9b006942001",
              "composeChildren": [],
              "chargeStatus": 1,
              "days": 1,
              "remark": null
            }
          ],
          "chargeStatus": 1,
          "days": 1,
          "remark": ""
        }
      ]
    }
  ],
  "prescriptionChineseForms": [
    {
      "id": "ffffffff000000001d6fb8b006942008",
      "type": 3,
      "specification": "中药饮片",
      "doseCount": 2,
      "dailyDosage": "1日1剂",
      "usage": "煎服",
      "freq": "1日3次",
      "requirement": "饭后1小时服用",
      "usageLevel": "每次150ml",
      "sort": 0,
      "isDecoction": true,
      "usageType": 1,
      "usageSubType": 1,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": "",
      "auditBy": null,
      "auditName": null,
      "contactMobile": "13900000000",
      "totalPrice": 8.52,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001d6fb8b00694200e",
          "goodsId": "4ec6d19a113d4a47adcd747565c10c3f",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "桔梗",
          "name": "桔梗",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.1000,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.1000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "4ec6d19a113d4a47adcd747565c10c3f",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "桔梗",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "桔梗",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.1,
            "packagePrice": 0.1,
            "packageCostPrice": 0.029,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 10002,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.029,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-01-30T08:15:40Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.1,
            "chainPiecePrice": 0.1,
            "chainPackageCostPrice": 0.05,
            "pieceCount": 10002,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.029,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.60000,
          "sourceTotalPrice": 0.60000
        },
        {
          "id": "ffffffff000000001d6fb8b00694200f",
          "goodsId": "f0fe317883274b5480cd6ead5d996a9b",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "黄芩",
          "name": "黄芩",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.2000,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.2000,
          "costUnitPrice": 0.0000,
          "sort": 6,
          "groupId": null,
          "productInfo": {
            "id": "f0fe317883274b5480cd6ead5d996a9b",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "黄芩",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "黄芩",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0.2,
            "packagePrice": 0.2,
            "packageCostPrice": 0.046,
            "inTaxRat": 1E+1,
            "outTaxRat": 2E+1,
            "stockPieceCount": 10012,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.046,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-02-12T00:46:00Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.2,
            "chainPiecePrice": 0.2,
            "chainPackageCostPrice": 0.046,
            "pieceCount": 10012,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 0.046,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 1.20000,
          "sourceTotalPrice": 1.20000
        },
        {
          "id": "ffffffff000000001d6fb8b006942010",
          "goodsId": "25d927bbeabf447fbb17606b07bd5b2b",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "知母",
          "name": "知母",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.1200,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.1200,
          "costUnitPrice": 0.0000,
          "sort": 7,
          "groupId": null,
          "productInfo": {
            "id": "25d927bbeabf447fbb17606b07bd5b2b",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "知母",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "知母",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.12,
            "packagePrice": 0.12,
            "packageCostPrice": 0.05,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 10002,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.05,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-01-30T07:58:34Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.12,
            "chainPiecePrice": 0.12,
            "chainPackageCostPrice": 0.06,
            "pieceCount": 10002,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.05,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.72000,
          "sourceTotalPrice": 0.72000
        },
        {
          "id": "ffffffff000000001d6fb8b006942011",
          "goodsId": "526b603ff4f94f9da0c05a5ea59fbc50",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "赤芍",
          "name": "赤芍",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.1300,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.1300,
          "costUnitPrice": 0.0000,
          "sort": 8,
          "groupId": null,
          "productInfo": {
            "id": "526b603ff4f94f9da0c05a5ea59fbc50",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "赤芍",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "赤芍",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.13,
            "packagePrice": 0.13,
            "packageCostPrice": 0.0251,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 10002,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.0251,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-04-10T15:15:43Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.13,
            "chainPiecePrice": 0.13,
            "chainPackageCostPrice": 0.065,
            "pieceCount": 10002,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.0251,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.78000,
          "sourceTotalPrice": 0.78000
        },
        {
          "id": "ffffffff000000001d6fb8b006942012",
          "goodsId": "3cbf9ec1576247719483ff6d34bcb142",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "玄参",
          "name": "玄参",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.0500,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.0500,
          "costUnitPrice": 0.0000,
          "sort": 9,
          "groupId": null,
          "productInfo": {
            "id": "3cbf9ec1576247719483ff6d34bcb142",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "玄参",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "玄参",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.05,
            "packagePrice": 0.05,
            "packageCostPrice": 0.017,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 1.001E+4,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.017,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "dd59fb0a539348f58df2e6f671a6164c",
            "lastModifiedUserId": "dd59fb0a539348f58df2e6f671a6164c",
            "lastModifiedDate": "2019-03-06T09:32:07Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.05,
            "chainPiecePrice": 0.05,
            "chainPackageCostPrice": 0.017,
            "pieceCount": 1.001E+4,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.017,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.30000,
          "sourceTotalPrice": 0.30000
        },
        {
          "id": "ffffffff000000001d6fb8b006942013",
          "goodsId": "15ac34381a9b448282dd2c1f95c2823c",
          "domainMedicineId": "",
          "type": 1,
          "subType": 2,
          "medicineCadn": "连翘",
          "name": "连翘",
          "specification": "",
          "manufacturer": "",
          "ast": null,
          "usage": "",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "",
          "dosage": "",
          "dosageUnit": "",
          "days": null,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.1100,
          "useDismounting": 0,
          "cMSpec": "中药饮片",
          "unitCount": 6.000,
          "unit": "g",
          "unitPrice": 0.1100,
          "costUnitPrice": 0.0000,
          "sort": 10,
          "groupId": null,
          "productInfo": {
            "id": "15ac34381a9b448282dd2c1f95c2823c",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "连翘",
            "displaySpec": "",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "连翘",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.11,
            "packagePrice": 0.11,
            "packageCostPrice": 0.064,
            "inTaxRat": 3,
            "outTaxRat": 4,
            "stockPieceCount": 9832,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.064,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-01-25T02:40:46Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.11,
            "chainPiecePrice": 0.11,
            "chainPackageCostPrice": 0.064,
            "pieceCount": 9832,
            "packageCount": 0,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.064,
            "cMSpec": "中药饮片"
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.66000,
          "sourceTotalPrice": 0.66000
        }
      ],
      "processUsageInfo": "煎药 - 人工煎药 - 1剂煎3袋",
      "cMSpec": "中药饮片"
    }
  ],
  "prescriptionWesternForms": [
    {
      "id": "ffffffff000000001d6f9c2806944000",
      "type": 1,
      "specification": "",
      "doseCount": 1,
      "dailyDosage": "",
      "usage": "",
      "freq": "",
      "requirement": "",
      "usageLevel": "",
      "sort": 0,
      "isDecoction": false,
      "usageType": null,
      "usageSubType": null,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": null,
      "auditBy": null,
      "auditName": null,
      "contactMobile": null,
      "totalPrice": 83.67,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001d6f9c2806944001",
          "goodsId": "ffffffff000000001d6f606006a8e000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿奇霉素颗粒",
          "name": "阿奇霉素颗粒",
          "specification": "0.8g*30片/瓶",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "0.1",
          "dosageUnit": "g",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 1.6667,
          "useDismounting": 1,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "包",
          "drugUnit": "包",
          "unitPrice": 1.6667,
          "costUnitPrice": 0.0000,
          "sort": 0,
          "groupId": 1,
          "productInfo": {
            "id": "ffffffff000000001d6f606006a8e000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "阿奇霉素颗粒",
            "displaySpec": "0.1g*6包/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 6,
            "pieceUnit": "包",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "阿奇霉素颗粒",
            "medicineDosageNum": 0.1,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": "",
            "piecePrice": 1.6667,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:05Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 1.6667,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 1.66670,
          "sourceTotalPrice": 1.66670
        },
        {
          "id": "ffffffff000000001d6f9c2806944002",
          "goodsId": "f0d781e0399d4dacada32773bacf75f6",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "氨溴特罗口服溶液",
          "name": "氨溴特罗口服溶液(易坦静)",
          "specification": "0.8g*30片/瓶",
          "manufacturer": "北京韩美",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "5",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 28.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "drugUnit": "瓶",
          "unitPrice": 28.0000,
          "costUnitPrice": 0.0000,
          "sort": 1,
          "groupId": 1,
          "productInfo": {
            "id": "f0d781e0399d4dacada32773bacf75f6",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "易坦静",
            "displayName": "氨溴特罗口服溶液 (易坦静)",
            "displaySpec": "60ml*60ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "北京韩美",
            "pieceNum": 6E+1,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "氨溴特罗口服溶液",
            "medicineNmpn": "H20040317",
            "medicineDosageNum": 6E+1,
            "medicineDosageUnit": "ml",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0,
            "packagePrice": 28,
            "packageCostPrice": 1,
            "inTaxRat": 1E+1,
            "outTaxRat": 2E+1,
            "stockPieceCount": 0,
            "stockPackageCount": 1.1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-01-29T09:28:43Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20389,
            "chainPackagePrice": 28,
            "chainPiecePrice": 0,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1.1E+2,
            "manufacturerFull": "北京韩美药品有限公司",
            "medicineDosageForm": "溶液剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 28.00000,
          "sourceTotalPrice": 28.00000
        },
        {
          "id": "ffffffff000000001d6f9c2806944003",
          "goodsId": "ffffffff000000001d6f5ed806a8e000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 3,
          "medicineCadn": "四季抗病毒合剂",
          "name": "四季抗病毒合剂",
          "specification": "0.8g*30片/瓶",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "5",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 10.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "drugUnit": "瓶",
          "unitPrice": 10.0000,
          "costUnitPrice": 0.0000,
          "sort": 2,
          "groupId": 1,
          "productInfo": {
            "id": "ffffffff000000001d6f5ed806a8e000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "四季抗病毒合剂",
            "displaySpec": "120ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 16,
            "type": 1,
            "subType": 3,
            "pieceNum": 1.2E+2,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 1,
            "medicineCadn": "四季抗病毒合剂",
            "extendSpec": "",
            "position": "",
            "piecePrice": 0.0833,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 7,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:10Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 0.0833,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001d6f9c2806944004",
          "goodsId": "ffffffff000000001d6f5a1806a8c000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "小柴胡颗粒",
          "name": "小柴胡颗粒",
          "specification": "0.8g*30片/瓶",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "tid",
          "dosage": "1",
          "dosageUnit": "包",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 10.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "drugUnit": "盒",
          "unitPrice": 10.0000,
          "costUnitPrice": 0.0000,
          "sort": 3,
          "groupId": 2,
          "productInfo": {
            "id": "ffffffff000000001d6f5a1806a8c000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "小柴胡颗粒",
            "displaySpec": "10g*10包/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 1E+1,
            "pieceUnit": "包",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "小柴胡颗粒",
            "medicineDosageNum": 1E+1,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": "",
            "piecePrice": 1,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:19Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 1,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001d6f9c2806944005",
          "goodsId": "ffffffff000000001d6f5b4806a8e000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "感冒灵颗粒(999)",
          "name": "感冒灵颗粒(999)",
          "specification": "0.8g*30片/瓶",
          "manufacturer": "",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "tid",
          "dosage": "1",
          "dosageUnit": "包",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 10.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "drugUnit": "盒",
          "unitPrice": 10.0000,
          "costUnitPrice": 0.0000,
          "sort": 4,
          "groupId": 2,
          "productInfo": {
            "id": "ffffffff000000001d6f5b4806a8e000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "感冒灵颗粒(999)",
            "displaySpec": "10g*9包/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 9,
            "pieceUnit": "包",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "感冒灵颗粒(999)",
            "medicineDosageNum": 1E+1,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": "",
            "piecePrice": 1.1111,
            "packagePrice": 1E+1,
            "packageCostPrice": 1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+2,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-15T03:10:14Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 1E+1,
            "chainPiecePrice": 1.1111,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+2,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001da6940006958000",
          "goodsId": "bccd2d2a2576480db2e7b1982742463a",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "阿莫西林分散片",
          "name": "阿莫西林分散片(阿赫林)",
          "specification": "0.8g*30片/瓶",
          "manufacturer": "石药集团",
          "ast": 0,
          "usage": "口服",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "片",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 24.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "盒",
          "drugUnit": "盒",
          "unitPrice": 24.0000,
          "costUnitPrice": 0.0000,
          "sort": 5,
          "groupId": null,
          "productInfo": {
            "id": "bccd2d2a2576480db2e7b1982742463a",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "阿赫林",
            "displayName": "阿莫西林分散片 (阿赫林)",
            "displaySpec": "0.5g*13片/盒",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "barCode": "6936292110261",
            "manufacturer": "石药集团",
            "pieceNum": 13,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "阿莫西林分散片",
            "medicineNmpn": "H20046510",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 24,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 9,
            "stockPackageCount": 3E+1,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "936944bc62d04d65ab8239e30a85e5f6",
            "lastModifiedDate": "2021-11-19T06:53:01Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20384,
            "chainPackagePrice": 24,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 9,
            "packageCount": 3E+1,
            "manufacturerFull": "石药集团中诺药业(石家庄)有限公司",
            "medicineDosageForm": "片剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 24.00000,
          "sourceTotalPrice": 24.00000
        }
      ],
      "processUsageInfo": "",
      "cMSpec": null
    },
    {
      "id": "ffffffff000000001da65c8806956000",
      "type": 1,
      "specification": "",
      "doseCount": 1,
      "dailyDosage": "",
      "usage": "",
      "freq": "",
      "requirement": "",
      "usageLevel": "",
      "sort": 1,
      "isDecoction": false,
      "usageType": null,
      "usageSubType": null,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": null,
      "auditBy": null,
      "auditName": null,
      "contactMobile": null,
      "totalPrice": 0.03,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001da65c8806956001",
          "goodsId": "fac01ed90db048d1997975a60123473b",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "卤米松乳膏",
          "name": "卤米松乳膏(澳能)",
          "specification": "",
          "manufacturer": "澳美制药",
          "ast": 0,
          "usage": "外用",
          "ivgtt": 0.0000,
          "ivgttUnit": "",
          "freq": "qd",
          "dosage": "1",
          "dosageUnit": "支",
          "days": 3,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 0.0100,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 3.000,
          "unit": "支",
          "drugUnit": "支",
          "unitPrice": 0.0100,
          "costUnitPrice": 0.0000,
          "sort": 0,
          "groupId": null,
          "productInfo": {
            "id": "fac01ed90db048d1997975a60123473b",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "澳能",
            "displayName": "卤米松乳膏 (澳能)",
            "displaySpec": "5g*支/支",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "澳美制药",
            "pieceNum": 1,
            "pieceUnit": "支",
            "packageUnit": "支",
            "dismounting": 0,
            "medicineCadn": "卤米松乳膏",
            "medicineNmpn": "HC20150049",
            "medicineDosageNum": 5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": null,
            "piecePrice": null,
            "packagePrice": 0.01,
            "packageCostPrice": 13,
            "inTaxRat": 1E+1,
            "outTaxRat": 2E+1,
            "stockPieceCount": 0,
            "stockPackageCount": 6,
            "lastPackageCostPrice": 13,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "fdd7cbf2524e4c929dbb99f14f04f178",
            "lastModifiedDate": "2021-03-30T11:49:43Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20388,
            "chainPackagePrice": 0.01,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 6,
            "manufacturerFull": "澳美制药厂",
            "medicineDosageForm": "膏剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 13,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 0.03000,
          "sourceTotalPrice": 0.03000
        }
      ],
      "processUsageInfo": "",
      "cMSpec": null
    }
  ],
  "prescriptionInfusionForms": [
    {
      "id": "ffffffff000000001d6f9c2806944006",
      "type": 2,
      "specification": "",
      "doseCount": 1,
      "dailyDosage": "",
      "usage": "",
      "freq": "",
      "requirement": "",
      "usageLevel": "",
      "sort": 0,
      "isDecoction": false,
      "usageType": null,
      "usageSubType": null,
      "processPrice": null,
      "ingredientPrice": null,
      "usageDays": null,
      "auditBy": null,
      "auditName": null,
      "contactMobile": null,
      "totalPrice": 79.01,
      "prescriptionFormItems": [
        {
          "id": "ffffffff000000001da65a7806956000",
          "goodsId": "ffffffff000000001c0a8b18067c8000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "氯化钠注射液9%",
          "name": "氯化钠注射液9%(双鹤)",
          "specification": "",
          "manufacturer": "四川科伦",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "250",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 3.0099,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 3.0099,
          "costUnitPrice": 0.0000,
          "sort": 0,
          "groupId": 1,
          "productInfo": {
            "id": "ffffffff000000001c0a8b18067c8000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "双鹤",
            "displayName": "氯化钠注射液9% (双鹤)",
            "displaySpec": "2.25g*250ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "四川科伦",
            "pieceNum": 2.5E+2,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "氯化钠注射液9%",
            "medicineNmpn": "H51021157",
            "medicineDosageNum": 2.25,
            "medicineDosageUnit": "g",
            "extendSpec": "",
            "position": null,
            "piecePrice": null,
            "packagePrice": 3.0099,
            "packageCostPrice": 2,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 2E+1,
            "lastPackageCostPrice": 2,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff0000000015549900040fc000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:12:26Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 3.0099,
            "chainPackageCostPrice": 2,
            "pieceCount": 0,
            "packageCount": 2E+1,
            "manufacturerFull": "四川科伦药业股份有限公司",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 2,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 3.00990,
          "sourceTotalPrice": 3.00990
        },
        {
          "id": "ffffffff000000001da65a7806956001",
          "goodsId": "217886ad6a554d8aba492affc786c65e",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "克林霉素磷酸酯注射液",
          "name": "克林霉素磷酸酯注射液(森迪)",
          "specification": "",
          "manufacturer": "山东方明药业",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "1.2",
          "dosageUnit": "g",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 7.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 4.000,
          "unit": "支",
          "unitPrice": 7.0000,
          "costUnitPrice": 0.0000,
          "sort": 1,
          "groupId": 1,
          "productInfo": {
            "id": "217886ad6a554d8aba492affc786c65e",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "森迪",
            "displayName": "克林霉素磷酸酯注射液 (森迪)",
            "displaySpec": "0.3g*2ml/支",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "山东方明药业",
            "pieceNum": 2,
            "pieceUnit": "ml",
            "packageUnit": "支",
            "dismounting": 0,
            "medicineCadn": "克林霉素磷酸酯注射液",
            "medicineNmpn": "H20045520",
            "medicineDosageNum": 0.3,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": null,
            "packagePrice": 7,
            "packageCostPrice": 0.71,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+1,
            "lastPackageCostPrice": 0.71,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:13:54Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 7,
            "chainPackageCostPrice": 3.5,
            "pieceCount": 0,
            "packageCount": 1E+1,
            "manufacturerFull": "山东方明药业集团股份有限公司",
            "medicineDosageForm": "注射剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.71,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 28.00000,
          "sourceTotalPrice": 28.00000
        },
        {
          "id": "ffffffff000000001da65a7806956002",
          "goodsId": "bd62c0ef60924a468590cd3b5b8e717f",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "葡萄糖氯化钠注射液",
          "name": "葡萄糖氯化钠注射液(科伦)",
          "specification": "",
          "manufacturer": "四川科伦",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "500",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 5.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 2.000,
          "unit": "瓶",
          "unitPrice": 5.0000,
          "costUnitPrice": 0.0000,
          "sort": 2,
          "groupId": 2,
          "productInfo": {
            "id": "bd62c0ef60924a468590cd3b5b8e717f",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "科伦",
            "displayName": "葡萄糖氯化钠注射液 (科伦)",
            "displaySpec": "12.5g*250ml/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "四川科伦",
            "pieceNum": 2.5E+2,
            "pieceUnit": "ml",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "葡萄糖氯化钠注射液",
            "medicineNmpn": "H51020630",
            "medicineDosageNum": 12.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": null,
            "packagePrice": 5,
            "packageCostPrice": 1.2,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+1,
            "lastPackageCostPrice": 1.2,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:14:18Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 5,
            "chainPackageCostPrice": 2.5,
            "pieceCount": 0,
            "packageCount": 1E+1,
            "manufacturerFull": "四川科伦药业股份有限公司",
            "medicineDosageForm": "注射剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1.2,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 10.00000,
          "sourceTotalPrice": 10.00000
        },
        {
          "id": "ffffffff000000001da65a7806956003",
          "goodsId": "0bb6e3b137664f8e92d5931e6df4ceb2",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "维生素C注射液",
          "name": "维生素C注射液",
          "specification": "",
          "manufacturer": "西南药业",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "15",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 1.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 8.000,
          "unit": "支",
          "unitPrice": 1.0000,
          "costUnitPrice": 0.0000,
          "sort": 3,
          "groupId": 2,
          "productInfo": {
            "id": "0bb6e3b137664f8e92d5931e6df4ceb2",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "",
            "displayName": "维生素C注射液",
            "displaySpec": "0.5g*2ml/支",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "西南药业",
            "pieceNum": 2,
            "pieceUnit": "ml",
            "packageUnit": "支",
            "dismounting": 0,
            "medicineCadn": "维生素C注射液",
            "medicineNmpn": "H50021469",
            "medicineDosageNum": 0.5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0,
            "packagePrice": 1,
            "packageCostPrice": 0.27,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 1E+1,
            "lastPackageCostPrice": 0.27,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "912d9293bfe94ba8a4857c29debf7a98",
            "lastModifiedUserId": "912d9293bfe94ba8a4857c29debf7a98",
            "lastModifiedDate": "2019-02-11T09:09:41Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 20385,
            "chainPackagePrice": 1,
            "chainPiecePrice": 0,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 1E+1,
            "manufacturerFull": "西南药业股份有限公司",
            "medicineDosageForm": "注射剂",
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 0.27,
            "cMSpec": null
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 8.00000,
          "sourceTotalPrice": 8.00000
        },
        {
          "id": "ffffffff000000001da65a7806956004",
          "goodsId": "ffffffff000000001d6f957006a8c000",
          "domainMedicineId": "",
          "type": 1,
          "subType": 1,
          "medicineCadn": "维生素B6注射液",
          "name": "维生素B6注射液(科伦)",
          "specification": "",
          "manufacturer": "",
          "ast": 0,
          "usage": "静脉滴注",
          "ivgtt": 60.0000,
          "ivgttUnit": "滴/分钟",
          "freq": "qd",
          "dosage": "100",
          "dosageUnit": "ml",
          "days": 1,
          "specialRequirement": "",
          "fractionPrice": 0.0000,
          "sourceUnitPrice": 30.0000,
          "useDismounting": 0,
          "cMSpec": null,
          "unitCount": 1.000,
          "unit": "瓶",
          "unitPrice": 30.0000,
          "costUnitPrice": 0.0000,
          "sort": 4,
          "groupId": 2,
          "productInfo": {
            "id": "ffffffff000000001d6f957006a8c000",
            "refGoodsId": null,
            "refGoodsName": null,
            "status": 1,
            "name": "科伦",
            "displayName": "维生素B6注射液 (科伦)",
            "displaySpec": "100ml*瓶/瓶",
            "organId": "628f2c02d90c480fa26fbed3d579ebc2",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 1,
            "pieceUnit": "瓶",
            "packageUnit": "瓶",
            "dismounting": 0,
            "medicineCadn": "维生素B6注射液",
            "medicineDosageNum": 1E+2,
            "medicineDosageUnit": "ml",
            "extendSpec": "",
            "position": "",
            "piecePrice": null,
            "packagePrice": 3E+1,
            "packageCostPrice": 1E+1,
            "inTaxRat": 5,
            "outTaxRat": 8,
            "stockPieceCount": 0,
            "stockPackageCount": 9.999999E+7,
            "lastPackageCostPrice": 1E+1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedUserId": "ffffffff000000000afc542801de0000",
            "lastModifiedDate": "2021-12-20T08:14:56Z",
            "combineType": 0,
            "bizExtensions": null,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 3E+1,
            "chainPackageCostPrice": 1E+1,
            "pieceCount": 0,
            "packageCount": 9.999999E+7,
            "chainId": "628f2c02d90c480fa26fbed3d579ebc2",
            "supplier": null,
            "allowSubClinicSetPrice": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 1E+1,
            "cMSpec": ""
          },
          "acupoints": null,
          "externalGoodsItems": null,
          "payType": null,
          "totalPrice": 30.00000,
          "sourceTotalPrice": 30.00000
        }
      ],
      "processUsageInfo": "",
      "cMSpec": null
    }
  ],
  "prescriptionExternalForms": []
}
-->


<template>
    <div>
        <template v-for="(form, formIndex) in printData.prescriptionWesternForms">
            <outpatient-header
                data-type="header"
                :print-data="printData"
                :config="config"
                print-title="处方笺"
                :organ-title="organTitle"
                :show-express="!!form.deliveryInfo && contentConfig.expressInfo"
                :data-pendants-index="`${formIndex}WP`"
            ></outpatient-header>
            <template v-if="form.isStandardForm">
                <template v-for="(formItems, formItemsIndex) in form.prescriptionFormItems">
                    <western-item
                        v-for="formItem in formItems"
                        :show-group-icon="checkExistedGroupId(form)"
                        :config="config"
                        :form-item="formItem"
                    ></western-item>
                    <div
                        v-if="form.prescriptionFormItems.length - 1 !== formItemsIndex"
                        data-type="new-page"
                    ></div>
                </template>
            </template>
            <template v-else>
                <western-item
                    v-for="formItem in form.prescriptionFormItems"
                    :show-group-icon="checkExistedGroupId(form)"
                    :config="config"
                    :form-item="formItem"
                ></western-item>
            </template>

            <template v-if="contentConfig.expressInfo && form.deliveryInfo">
                <delivery-info :delivery-info="form.deliveryInfo"></delivery-info>
            </template>

            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice :doctor-advice="doctorAdvice"></doctor-advice>
            </template>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}WP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :print-config="config"
                    :print-time="printData.printTime"
                    :pr-form="form"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>

        <template v-for="(form, formIndex) in printData.prescriptionInfusionForms">
            <outpatient-header
                data-type="header"
                :print-data="printData"
                :organ-title="organTitle"
                :config="config"
                print-title="处方笺"
                :show-express="!!form.deliveryInfo && contentConfig.expressInfo"
                :data-pendants-index="`${formIndex}IP`"
            ></outpatient-header>
            <template v-if="form.isStandardForm">
                <template v-for="(formItems, formItemsIndex) in form.prescriptionFormItems">
                    <div
                        v-for="(groupFormItems, groupIndex) in getInfusionGroupItems(formItems)"
                        data-type="mix-box"
                    >
                        <infusion-item
                            show-days
                            :config="config"
                            :group-form-items="groupFormItems"
                        ></infusion-item>
                    </div>
                    <div
                        v-if="form.prescriptionFormItems.length - 1 !== formItemsIndex"
                        data-type="new-page"
                    ></div>
                </template>
            </template>
            <template v-else>
                <div
                    v-for="(groupFormItems, groupIndex) in getInfusionGroupItems(form.prescriptionFormItems)"
                    data-type="mix-box"
                    :key="groupIndex"
                >
                    <infusion-item
                        show-days
                        :config="config"
                        :group-form-items="groupFormItems"
                    ></infusion-item>
                </div>
            </template>

            <template v-if="contentConfig.expressInfo && form.deliveryInfo">
                <delivery-info :delivery-info="form.deliveryInfo"></delivery-info>
            </template>
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice :doctor-advice="doctorAdvice"></doctor-advice>
            </template>

            <next-is-blank></next-is-blank>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}IP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :pr-form="form"
                    :print-time="printData.printTime"
                    :print-config="config"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>


        <template v-for="(chineseForm, formIndex) in printData.prescriptionChineseForms">
            <outpatient-header
                data-type="header"
                :print-data="printData"
                :organ-title="organTitle"
                :show-express="!!chineseForm.deliveryInfo && contentConfig.expressInfo"
                :config="config"
                print-title="处方笺"
                :show-virtual-pharmacy="chineseForm.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY"
                :data-pendants-index="`${formIndex}CP`"
                :show-process="!!chineseForm.processUsageInfo && contentConfig.processInfo"
            ></outpatient-header>

            <div data-type="mix-box">
                <template v-for="groupItems in getChineseGroupItems(chineseForm.prescriptionFormItems, 4)">
                    <chinese-item
                        :config="config"
                        :form-items="groupItems"
                        :chinese-form="chineseForm"
                        :print-data="printData"
                    ></chinese-item>
                </template>
            </div>


            <!--用法-->

            <print-row
                class="remark-row first-remark-row"
            >
                <print-col
                    class="remark-col"
                    :span="24"
                >
                    <span class="label">用法：</span>
                    <span v-html="getChineseFormUsage(chineseForm, contentConfig).totalInfo"></span>
                </print-col>
            </print-row>

            <print-row
                v-if="getChineseFormUsage(chineseForm, contentConfig).usageInfo"
                class="remark-row"
                data-type="mix-box"
            >
                <print-col
                    class="remark-col"
                    :span="24"
                    v-html="getChineseFormUsage(chineseForm, contentConfig).usageInfo"
                >
                </print-col>
            </print-row>

            <!-- 加工-->
            <print-row
                v-if="(chineseForm.processUsageInfo || chineseForm.contactMobile) && contentConfig.processInfo"
                class="remark-row first-remark-row"
            >
                <print-col
                    class="remark-col"
                    :span="24"
                >
                    <span class="label">加工：</span>
                    {{ chineseForm.processUsageInfo }}
                    <template v-if="chineseForm.contactMobile">
                        ，联系人{{ chineseForm.contactMobile }}
                    </template>
                </print-col>
            </print-row>

            <template v-if="contentConfig.expressInfo && chineseForm.deliveryInfo">
                <delivery-info :delivery-info="chineseForm.deliveryInfo"></delivery-info>
            </template>
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice :doctor-advice="doctorAdvice"></doctor-advice>
            </template>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}CP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :pr-form="chineseForm"
                    :print-time="printData.printTime"
                    :print-config="config"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>

        <template v-for="(externalForm, formIndex) in printData.prescriptionExternalForms">
            <outpatient-header
                data-type="header"
                :show-express="!!externalForm.deliveryInfo"
                :print-data="printData"
                :organ-title="organTitle"
                :config="config"
                :extend-spec="config.header && config.header.prescriptionType ? ExternalPRUsageTypeEnumRevert[externalForm.usageType] : ''"
                print-title="处方笺"
                :data-pendants-index="`${formIndex}EP`"
            ></outpatient-header>
            <template v-for="(formItem, formItemIndex) in externalForm.prescriptionFormItems">
                <print-row>
                    <print-col
                        :span="24"
                        class="external-item"
                    >
                        {{ formItemIndex + 1 }}.{{ formItem.name }}
                    </print-col>
                </print-row>
                <!--药品-->
                <external-item
                    v-for="(goodsItems, goodsIndex) in getChineseGroupItems(formItem.externalGoodsItems, 4)"
                    type="goods"
                    :config="config"
                    :form-items="goodsItems"
                ></external-item>


                <print-row
                    v-if="formatAcupoints(formItem.acupoints)"
                    class="remark-row first-remark-row external-usage-row"
                    data-type="mix-box"
                >
                    <print-col
                        class="remark-col"
                        :span="24"
                    >
                        <span class="label">穴位：</span>
                        {{ formatAcupoints(formItem.acupoints) }}
                    </print-col>
                </print-row>
                <print-row
                    class="remark-row first-remark-row external-usage-row"
                    data-type="mix-box"
                >
                    <print-col
                        class="remark-col"
                        :span="24"
                    >
                        <span class="label">用法：</span>
                        <template v-if="formItem.unitCount">
                            共 {{ formItem.dosage }} 次
                        </template>
                        <template v-if="formItem.freq">
                            ，{{ formItem.freq }}
                        </template>
                        <template v-if="formItem.specialRequirement">
                            ，{{ formItem.specialRequirement }}
                        </template>
                    </print-col>
                </print-row>
            </template>

            <template v-if="contentConfig.expressInfo && externalForm.deliveryInfo">
                <delivery-info :delivery-info="externalForm.deliveryInfo"></delivery-info>
            </template>
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice :doctor-advice="doctorAdvice"></doctor-advice>
            </template>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}EP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :pr-form="externalForm"
                    :print-config="config"
                    :print-time="printData.printTime"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>

        <div
            class="next-page"
            data-type="next-page"
        >
            (接下页)
        </div>
        <div
            class="prev-page"
            data-type="prev-page"
        >
            (接上页)
        </div>
    </div>
</template>

<script>
    import { formatAge, formatMoney } from './common/utils.js'

    import PrescriptionYibaoHandler from "./data-handler/prescription-yibao-handler";
    import OutpatientHeader from './components/medical-document-header/prescription-header.vue'
    import OutpatientFooter from './components/medical-document-footer/prescription-footer.vue'
    import WesternItem from './components/prescription/western-item/index-yibao.vue';
    import ChineseItem from './components/prescription/chinese-item/index.vue';
    import InfusionItem from './components/prescription/infusion-item/index.vue';
    import ExternalItem from './components/prescription/external-item/index.vue';
    import PrintRow from './components/layout/print-row.vue';
    import PrintCol from './components/layout/print-col.vue';
    import {ExternalPRUsageTypeEnumRevert, PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import DoctorAdvice from './components/prescription/doctor-advice/index.vue';
    import NextIsBlank from './components/next-is-blank/index.vue';
    import DeliveryInfo from "./components/prescription/delivery-info/index.vue";

    import { getDoctorAdviceArray, getInfusionGroupItems,getChineseGroupItems, doseTotal, formatAcupoints,checkExistedGroupId} from "./common/medical-transformat.js";
    import clone from "./common/clone.js";
    import { PharmacyTypeEnum } from "./common/constants.js";

    export default {
        DataHandler: PrescriptionYibaoHandler,
        name: "Prescription",
        businessKey: PrintBusinessKeyEnum.PRESCRIPTION_YIBAO,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分' // 默认选择的等分纸
            },
        ],
        components: {
            OutpatientHeader,
            OutpatientFooter,
            WesternItem,
            ChineseItem,
            InfusionItem,
            ExternalItem,
            PrintCol,
            PrintRow,
            DoctorAdvice,
            NextIsBlank,
            DeliveryInfo,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        data() {
            return {
                ExternalPRUsageTypeEnumRevert,
                PharmacyTypeEnum
            }
        },
        computed: {
            printData() {
                console.log('this.renderData', this.renderData)
                this.renderData.printData.prescriptionWesternForms && this.renderData.printData.prescriptionWesternForms.forEach( form => {
                    form.prescriptionFormItems = this.groupMedicine(form.prescriptionFormItems);
                })
                if(this.renderData.config &&
                    this.renderData.config.medicalDocuments &&
                    this.renderData.config.medicalDocuments.prescription &&
                    this.renderData.config.medicalDocuments.prescription.content.standardKindCount) {
                    const {prescriptionWesternForms, prescriptionInfusionForms} = this.transStandardForm();
                    const data =  {
                        ...this.renderData.printData,
                        prescriptionWesternForms: prescriptionWesternForms,
                        prescriptionInfusionForms: prescriptionInfusionForms,
                    }

                    console.log('自动分页过后的printData', data)
                    return data;
                } else {
                    return this.renderData.printData || null;
                }
            },
            config() {
                console.log('自动分页过后的config', this.renderData.config);
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.prescription) {
                    return this.renderData.config.medicalDocuments.prescription;
                }
                if (this.renderData.config && this.renderData.config.prescription) {
                    Object.assign(this.renderData.config.prescription, {
                        header: {
                            diagnosisNo: 0,
                        }
                    })
                    console.log('自动分页过后的config', this.renderData.config);
                    return this.renderData.config.prescription;
                }
                return {};
            },
            contentConfig() {
                return this.config && this.config.content || {};
            },
            organ() {
                return this.printData && this.printData.organ;
            },
            organTitle() {
                return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.prescription || '';
            },
            clinicName() {
                return this.organ.name;
            },
            patient() {
                return this.printData.patient;
            },
            doctorAdvice() {
                return getDoctorAdviceArray(this.printData.doctorAdvice);
            },
        },
        methods: {
            formatAge,
            formatMoney,
            getInfusionGroupItems,
            getChineseGroupItems,
            formatAcupoints,
            checkExistedGroupId,
            // 获取中药处方用法
            getChineseFormUsage(form, prContentConfig = {}) {
                let _str = '';
                const {chineseMedicineTotalCount} = prContentConfig;
                _str += `共 <span class="bold-text">${form.doseCount || ''}</span> 剂`;
                if(chineseMedicineTotalCount) {
                    _str += `，${doseTotal(form.prescriptionFormItems).kind} 味，单剂 ${
                        doseTotal(form.prescriptionFormItems).count
                    } g，总重 ${(doseTotal(form.prescriptionFormItems).count * form.doseCount).toFixed(2)} g`
                }

                let usageStr = '';
                let usageArray = [];
                if(form.usage) {
                    usageArray.push(form.usage);
                }
                if(form.dailyDosage) {
                    usageArray.push(form.dailyDosage);
                }
                if(form.freq) {
                    usageArray.push(form.freq)
                }
                if(form.usageLevel) {
                    usageArray.push(form.usageLevel)
                }
                if(form.usageDays) {
                    usageArray.push(form.usageDays);
                }
                if(form.requirement) {
                    usageArray.push(form.requirement);
                }
                usageStr = usageArray.join('，')

                if(!chineseMedicineTotalCount) {
                    _str += '，' + usageStr;
                    usageStr = '';
                }
                return {
                    totalInfo:  _str,
                    usageInfo:   usageStr ,
                }
            },

            formatPatientOrderNo( value = '' ) {
                let srcStr = '00000000';
                if (!value)
                    return srcStr;
                return (srcStr + ('' + value)).slice( -8 );
            },
            splitFormItems(formItems, count = 5, isRepeat = true) {
                let tempFormItems = clone(formItems);
                let len = formItems.length;
                let res = [];
                if(isRepeat) {
                    // 不考虑去重，直接按照数量切分
                    let index = 0;
                    while(index < len) {
                        let tempItems = tempFormItems.slice(index, index += count);
                        res.push(tempItems);
                    }
                } else {
                    let groupSet = new Set();
                    let group = [];
                    tempFormItems.forEach( item => {
                        groupSet.add(item.goodsId);
                        if(groupSet.size < 6) {
                            group.push(item);
                        } else {
                            res.push(group);
                            groupSet.clear();
                            groupSet.add(item.goodsId);
                            group = [];
                            group.push(item);
                        }
                    })
                    if(group && group.length) {
                        res.push(group);
                    }
                }
                return res;
            },
            transStandardForm() {
                const wsForms = clone(this.renderData.printData.prescriptionWesternForms) || [];
                const inForms = clone(this.renderData.printData.prescriptionInfusionForms) || [];
                let resWsForm = [];
                let resInForm = [];
                wsForms.forEach( form => {
                    let tempForm = clone(form);
                    tempForm.prescriptionFormItems = [];
                    let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5,false);
                    resWsForm.push({
                        ...tempForm,
                        isStandardForm: true,
                        prescriptionFormItems: formItemsGroups
                    })
                })
                inForms.forEach( form => {
                    let tempForm = clone(form);
                    tempForm.prescriptionFormItems = [];
                    let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5, false);
                    resInForm.push({
                        ...tempForm,
                        isStandardForm: true,
                        prescriptionFormItems: formItemsGroups
                    })

                })
                return {
                    prescriptionWesternForms: resWsForm,
                    prescriptionInfusionForms: resInForm,
                }
            },
            groupMedicine( prescriptionFormItems ) {
                let _group = {};
                let tempFormItems = clone(prescriptionFormItems)
                let noGroupIdItems = [];
                //分组
                tempFormItems.forEach( ( medicine ) => {
                    if(!medicine.groupId) {
                        medicine.showGroupId = true;
                        noGroupIdItems.push(medicine);
                    } else {
                        if (!(_group[ Number( medicine.groupId ) ] instanceof Array)) {
                            _group[ Number( medicine.groupId ) ] = [];
                        }
                        if(!_group[ Number( medicine.groupId ) ].length ) {
                            medicine.showGroupId = true;
                        } else{
                            medicine.showGroupId = false;
                        }
                        _group[ Number( medicine.groupId ) ].push(  medicine );
                    }

                } );
                let res = [];
                for( let item in _group) {
                    res = res.concat(_group[item])
                }
                return res.concat(noGroupIdItems);
            },
        }
    }
</script>

<style lang="scss">
@import "./components/layout/print-layout.scss";
@import "./style/reset.scss";

.abc-page-content{
    padding: 8pt;
    box-sizing: border-box;
    overflow: hidden;
}

.remark-col {
    position: relative;
    padding-left: 30pt;
    font-size: 8pt;
    line-height: 12pt;
    font-weight: 300;

    .label {
        position: absolute;
        top: 6pt;
        left: 0;
        width: 30pt;
    }
}
.bold-text {
    font-weight: bold;
}

.remark-row {
    margin-bottom: 6pt;
}

.first-remark-row {
    .remark-col {
        padding-top: 6pt;
        border-top: 1px dashed #000000;
    }
    &.external-usage-row {
        .remark-col {
            padding-top: 0;
            border-top: none;
            .label {
                top: 0;
            }
        }
    }
}

.last-remark-row {
    margin-bottom: 0;
}

.external-item {
    margin-bottom: 6pt;
    font-size: 10pt;
    line-height: 12pt;
    font-weight: bold;
}

[data-type~=footer] {
  padding-top: 10pt;
}

.next-page {
  font-size: 8pt;
  text-align: center;
  font-weight: lighter;
  position: relative;
}

.next-page_align_footer {
  position: absolute;
  top: -2pt;
  left: 0;
  width: 100%;
}

.prev-page {
  position: absolute;
  bottom: 8px;
  left: 60px;
  font-size: 8pt;
  font-weight: lighter;
}
.label {
  font-weight: normal;
}

.chinese-item {
    font-family: "Microsoft YaHei", "微软雅黑", serif;

    .chinese-name,
    .chinese-count,
    .special-position {
        font-size: 8pt;
    }

    .chinese-col {
        height: 30pt;
    }
}

.infusion-item-wrapper {
    .group-name,
    .group-spec,
    .usage,
    .usage-info,
    .dosage-count {
        font-size: 8pt !important;
    }
}

</style>
