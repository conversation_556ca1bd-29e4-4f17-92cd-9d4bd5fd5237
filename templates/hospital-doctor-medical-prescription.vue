<template>
    <div class="abc-hospital-doctor-medical-prescription">
        <div data-type="header">
            <div
                style="font-size: 16pt; line-height: 21pt;"
                class="clinic-name"
            >
                <template v-if="headerConfig.title">
                    {{ headerConfig.title }}
                    <template v-if="headerConfig.subtitle">
                        <br />{{ headerConfig.subtitle }}
                    </template>
                </template>
                <template v-else>
                    {{ clinicName }}
                </template>
            </div>
            <div
                style="font-size: 14pt; line-height: 18pt;"
                class="prescription-type"
            >
                {{ medicalPrescriptionTitle }}
            </div>
        </div>

        <div data-type="mix-box">
            <print-row class="table-header">
                <print-col
                    :span="7"
                    class="table-header-left"
                >
                    患者: {{ patient.name }} {{ patient.sex }} {{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}
                </print-col>
                <print-col
                    class="table-header-left"
                    :span="5"
                >
                    科室: {{ patientHospitalInfo.departmentName }}
                </print-col>
                <print-col
                    class="table-header-left"
                    :span="5"
                >
                    病区: {{ patientHospitalInfo.wardName }}
                </print-col>
                <print-col
                    class="table-header-left"
                    :span="3"
                >
                    床号: {{ patientHospitalInfo.bedNo }}
                </print-col>
                <print-col
                    class="table-header-left"
                    :span="4"
                >
                    住院号: {{ patientHospitalInfo.no }}
                </print-col>
            </print-row>

            <table border="1">
                <thead>
                    <tr>
                        <th
                            colspan="1"
                            class="align-center"
                        >
                            开始时间
                        </th>
                        <template v-if="contentConfig.executeBeforeMedical === 1">
                            <th
                                colspan="1"
                                class="align-center"
                            >
                                医生签名
                            </th>
                            <th
                                v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedTime) || (isLongTimeAdvice && contentConfig.longTimeCheckedTime)"
                                colspan="1"
                                class="align-center"
                            >
                                核对时间
                            </th>
                            <th
                                v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedOperatorName) || (isLongTimeAdvice && contentConfig.longTimeCheckedOperatorName)"
                                colspan="1"
                                class="align-center"
                            >
                                核对护士
                            </th>
                        </template>
                        <th
                            colspan="6"
                        >
                            <print-row>
                                <print-col
                                    :span="18"
                                    style="text-align: left;"
                                >
                                    医嘱内容
                                </print-col>
                                <print-col
                                    :span="6"
                                    style="text-align: right;"
                                >
                                    单剂剂量
                                </print-col>
                            </print-row>
                        </th>
                        <th
                            v-if="(isOneTimeAdvice && contentConfig.oneTimeUsage) || (isLongTimeAdvice && contentConfig.longTimeUsage)"
                            colspan="1"
                            class="align-center"
                        >
                            用法
                        </th>
                        <template v-if="contentConfig.executeBeforeMedical === 2">
                            <th
                                colspan="1"
                                class="align-center"
                            >
                                医生签名
                            </th>
                            <th
                                v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedTime) || (isLongTimeAdvice && contentConfig.longTimeCheckedTime)"
                                colspan="1"
                                class="align-center"
                            >
                                核对时间
                            </th>
                            <th
                                v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedOperatorName) || (isLongTimeAdvice && contentConfig.longTimeCheckedOperatorName)"
                                colspan="1"
                                class="align-center"
                            >
                                核对护士
                            </th>
                        </template>
                        <th
                            v-if="isLongTimeAdvice && contentConfig.longTimeStopTime"
                            colspan="1"
                            class="align-center"
                        >
                            停止时间
                        </th>
                        <th
                            v-if="isLongTimeAdvice && contentConfig.longTimeStopDoctor"
                            colspan="1"
                            class="align-center"
                        >
                            停止医生
                        </th>
                        <td
                            v-if="isOneTimeAdvice && contentConfig.oneTimeExecuteTime"
                            colspan="1"
                        >
                            执行时间
                        </td>
                        <th
                            v-if="(isOneTimeAdvice && contentConfig.oneTimeOperateNurseSignature) || (isLongTimeAdvice && contentConfig.longTimeOperateNurseSignature)"
                            colspan="1"
                            class="align-center"
                        >
                            {{ nurseNameTh }}
                        </th>
                        <th
                            v-if="(isOneTimeAdvice && contentConfig.oneTimeNurseSignature) || (isLongTimeAdvice && contentConfig.longTimeNurseSignature)"
                            colspan="1"
                            class="align-center"
                        >
                            护士签名
                        </th>
                    </tr>
                </thead>

                <tbody data-type="group">
                    <template v-for="advice in tableDataGroup">
                        <tr
                            :key="advice.id"
                            data-type="item"
                        >
                            <td colspan="1">
                                {{ advice.startTime | parseTime('m-d h:i', true) }}
                            </td>
                            <template v-if="contentConfig.executeBeforeMedical === 1">
                                <!-- 医生签名 -->
                                <td colspan="1">
                                    <!-- 电脑签名 -->
                                    <template v-if="contentConfig.doctorSignatureType === 2">
                                        {{ advice.createdByName }}
                                    </template>
                                    <!-- 手写签名 -->
                                    <template v-else-if="contentConfig.doctorSignatureType === 3">
                                        <img
                                            v-if="advice.createdByHandSign && isImgUrl(advice.createdByHandSign)"
                                            :src="advice.createdByHandSign"
                                            alt=""
                                            class="hand-sign-img"
                                        />
                                        <template v-else>
                                            {{ advice.createdByName }}
                                        </template>
                                    </template>
                                </td>
                                <!-- 核对时间 -->
                                <td
                                    v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedTime) || (isLongTimeAdvice && contentConfig.longTimeCheckedTime)"
                                    colspan="1"
                                >
                                    {{ advice.checkedTime | parseTime('m-d h:i', true) }}
                                </td>
                                <!-- 核对护士 -->
                                <td
                                    v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedOperatorName) || (isLongTimeAdvice && contentConfig.longTimeCheckedOperatorName)"
                                    colspan="1"
                                >
                                    <!-- 电脑签名 -->
                                    <template v-if="contentConfig.checkedNurseSignatureType === 2">
                                        {{ advice.checkedOperatorName }}
                                    </template>
                                    <!-- 手写签名 -->
                                    <template v-else-if="contentConfig.checkedNurseSignatureType === 3">
                                        <img
                                            v-if="advice.checkedOperatorHandSign && isImgUrl(advice.checkedOperatorHandSign)"
                                            :src="advice.checkedOperatorHandSign"
                                            alt=""
                                            class="hand-sign-img"
                                        />
                                        <template v-else>
                                            {{ advice.checkedOperatorName }}
                                        </template>
                                    </template>
                                </td>
                            </template>
                            <td
                                colspan="6"
                                class="more-right-padding"
                                :class="{ 'is-group-start': isChineseAdvice(advice) }"
                            >
                                <print-row
                                    v-for="item in advice.advices"
                                    :key="item.id"
                                    class="tr-content"
                                >
                                    <print-col
                                        :span="18"
                                        style="padding: 0 2pt; text-align: left; line-height: 12pt;"
                                    >
                                        <span>{{ renderAdviceContent(item, advice) }}</span>
                                    </print-col>
                                    <print-col
                                        :span="6"
                                        style="padding: 0 2pt; text-align: right;line-height: 12pt;"
                                    >
                                        <template v-if="item.isNeedHospitalExecute !== 0 && !isMaterialsAdvice(advice)">
                                            {{ item.singleDosageCount }}{{ item.singleDosageUnit }}
                                        </template>
                                    </print-col>
                                </print-row>
                            </td>
                            <!-- 用法 -->
                            <td
                                v-if="(isOneTimeAdvice && contentConfig.oneTimeUsage) || (isLongTimeAdvice && contentConfig.longTimeUsage)"
                                colspan="1"
                            >
                                {{ renderAdviceUsage(advice) }}
                            </td>
                            <template v-if="contentConfig.executeBeforeMedical === 2">
                                <!-- 医生签名 -->
                                <td colspan="1">
                                    <!-- 电脑签名 -->
                                    <template v-if="contentConfig.doctorSignatureType === 2">
                                        {{ advice.createdByName }}
                                    </template>
                                    <!-- 手写签名 -->
                                    <template v-else-if="contentConfig.doctorSignatureType === 3">
                                        <img
                                            v-if="advice.createdByHandSign && isImgUrl(advice.createdByHandSign)"
                                            :src="advice.createdByHandSign"
                                            alt=""
                                            class="hand-sign-img"
                                        />
                                        <template v-else>
                                            {{ advice.createdByName }}
                                        </template>
                                    </template>
                                </td>
                                <!-- 核对时间 -->
                                <td
                                    v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedTime) || (isLongTimeAdvice && contentConfig.longTimeCheckedTime)"
                                    colspan="1"
                                >
                                    {{ advice.checkedTime | parseTime('m-d h:i', true) }}
                                </td>
                                <!-- 核对护士 -->
                                <td
                                    v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedOperatorName) || (isLongTimeAdvice && contentConfig.longTimeCheckedOperatorName)"
                                    colspan="1"
                                >
                                    <!-- 电脑签名 -->
                                    <template v-if="contentConfig.checkedNurseSignatureType === 2">
                                        {{ advice.checkedOperatorName }}
                                    </template>
                                    <!-- 手写签名 -->
                                    <template v-else-if="contentConfig.checkedNurseSignatureType === 3">
                                        <img
                                            v-if="advice.checkedOperatorHandSign && isImgUrl(advice.checkedOperatorHandSign)"
                                            :src="advice.checkedOperatorHandSign"
                                            alt=""
                                            class="hand-sign-img"
                                        />
                                        <template v-else>
                                            {{ advice.checkedOperatorName }}
                                        </template>
                                    </template>
                                </td>
                            </template>
                            <!-- 停止时间 -->
                            <td
                                v-if="isLongTimeAdvice && contentConfig.longTimeStopTime"
                                colspan="1"
                            >
                                {{ advice.stopTime | parseTime('m-d h:i', true) }}
                            </td>
                            <!-- 停止医生 -->
                            <td
                                v-if="isLongTimeAdvice && contentConfig.longTimeStopDoctor"
                                colspan="1"
                            >
                                <!-- 电脑签名 -->
                                <template v-if="contentConfig.stopDoctorSignatureType === 2">
                                    {{ advice.stopDoctorName }}
                                </template>
                                <!-- 手写签名 -->
                                <template v-else-if="contentConfig.stopDoctorSignatureType === 3">
                                    <img
                                        v-if="advice.stopDoctorHandSign && isImgUrl(advice.stopDoctorHandSign)"
                                        :src="advice.stopDoctorHandSign"
                                        alt=""
                                        class="hand-sign-img"
                                    />
                                    <template v-else>
                                        {{ advice.stopDoctorName }}
                                    </template>
                                </template>
                            </td>
                            <!-- 执行时间 -->
                            <td
                                v-if="isOneTimeAdvice && contentConfig.oneTimeExecuteTime"
                                colspan="1"
                            >
                                {{ advice.executeTime | parseTime('m-d h:i', true) }}
                            </td>
                            <!-- 执行护士 -->
                            <td
                                v-if="(isOneTimeAdvice && contentConfig.oneTimeOperateNurseSignature) || (isLongTimeAdvice && contentConfig.longTimeOperateNurseSignature)"
                                colspan="1"
                            >
                                <template v-if="isLongTimeAdvice">
                                    <!-- 电脑签名 -->
                                    <template v-if="contentConfig.stopNurseSignatureType === 2">
                                        {{ advice.stopConfirmOperatorName }}
                                    </template>
                                    <!-- 手写签名 -->
                                    <template v-else-if="contentConfig.stopNurseSignatureType === 3">
                                        <img
                                            v-if="advice.stopConfirmOperatorHandSign && isImgUrl(advice.stopConfirmOperatorHandSign)"
                                            :src="advice.stopConfirmOperatorHandSign"
                                            alt=""
                                            class="hand-sign-img"
                                        />
                                        <template v-else>
                                            {{ advice.stopConfirmOperatorName }}
                                        </template>
                                    </template>
                                </template>
                                <template v-else>
                                    <!-- 电脑签名 -->
                                    <template v-if="contentConfig.operateNurseSignatureType === 2">
                                        {{ advice.executeOperatorName }}
                                    </template>
                                    <!-- 手写签名 -->
                                    <template v-else-if="contentConfig.operateNurseSignatureType === 3">
                                        <img
                                            v-if="advice.executeOperatorHandSign"
                                            :src="advice.executeOperatorHandSign"
                                            alt=""
                                            class="hand-sign-img"
                                        />
                                        <template v-else>
                                            {{ advice.executeOperatorName }}
                                        </template>
                                    </template>
                                </template>
                            </td>
                            <!-- 护士签名-->
                            <td
                                v-if="(isOneTimeAdvice && contentConfig.oneTimeNurseSignature) || (isLongTimeAdvice && contentConfig.longTimeNurseSignature)"
                                colspan="1"
                            >
                            </td>
                        </tr>
                        <template v-if="isChineseAdvice(advice)">
                            <!-- 中药饮片明细需要展示 -->
                            <tr
                                v-for="(chineseItemChunks, chunkIndex) in splitChineseMedicineItem(advice)"
                                :key="`${advice.id}-${chunkIndex}`"
                                data-type="item"
                            >
                                <td colspan="1"></td>
                                <template v-if="contentConfig.executeBeforeMedical === 1">
                                    <td colspan="1"></td>
                                    <td
                                        v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedTime) || (isLongTimeAdvice && contentConfig.longTimeCheckedTime)"
                                        colspan="1"
                                    ></td>
                                    <td
                                        v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedOperatorName) || (isLongTimeAdvice && contentConfig.longTimeCheckedOperatorName)"
                                        colspan="1"
                                    ></td>
                                </template>
                                <td
                                    colspan="6"
                                    class="more-right-padding"
                                    :class="{
                                        'is-group-end': chunkIndex === splitChineseMedicineItem(advice).length - 1,
                                        'is-group-process': chunkIndex !== splitChineseMedicineItem(advice).length - 1,
                                    }"
                                >
                                    <print-row class="tr-content">
                                        <print-col
                                            v-for="chineseItem in chineseItemChunks"
                                            :key="chineseItem.id"
                                            :span="8"
                                            style="padding: 0 2pt; text-align: left; word-break: break-all; word-wrap: break-word;"
                                        >
                                            <span>{{ chineseItem.goodsName }}</span>
                                            <span>{{ chineseItem.unitCount }}{{ chineseItem.unit }}</span>
                                        </print-col>
                                    </print-row>
                                </td>
                                <td
                                    v-if="(isOneTimeAdvice && contentConfig.oneTimeUsage) || (isLongTimeAdvice && contentConfig.longTimeUsage)"
                                    colspan="1"
                                ></td>
                                <template v-if="contentConfig.executeBeforeMedical === 2">
                                    <td colspan="1"></td>
                                    <td
                                        v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedTime) || (isLongTimeAdvice && contentConfig.longTimeCheckedTime)"
                                        colspan="1"
                                    ></td>
                                    <td
                                        v-if="(isOneTimeAdvice && contentConfig.oneTimeCheckedOperatorName) || (isLongTimeAdvice && contentConfig.longTimeCheckedOperatorName)"
                                        colspan="1"
                                    ></td>
                                </template>
                                <td
                                    v-if="isLongTimeAdvice && contentConfig.longTimeStopTime"
                                    colspan="1"
                                ></td>
                                <td
                                    v-if="isLongTimeAdvice && contentConfig.longTimeStopDoctor"
                                    colspan="1"
                                ></td>
                                <td
                                    v-if="isOneTimeAdvice && contentConfig.oneTimeExecuteTime"
                                    colspan="1"
                                ></td>
                                <td
                                    v-if="(isOneTimeAdvice && contentConfig.oneTimeOperateNurseSignature) || (isLongTimeAdvice && contentConfig.longTimeOperateNurseSignature)"
                                    colspan="1"
                                ></td>
                                <td
                                    v-if="(isOneTimeAdvice && contentConfig.oneTimeNurseSignature) || (isLongTimeAdvice && contentConfig.longTimeNurseSignature)"
                                    colspan="1"
                                ></td>
                            </tr>
                        </template>
                    </template>
                </tbody>
            </table>
        </div>

        <div
            class="footer"
            data-type="footer"
            style="padding-top: 8pt;"
        >
            <div class="table-footer">
                <print-row>
                    <print-col
                        v-if="footerConfig.footerDoctorSignature"
                        :span="8"
                        class="footer-content"
                    >
                        <span class="footer-hand-sign-title">医生签名：</span>
                        <!-- 电脑签名 -->
                        <template v-if="footerConfig.footerDoctorSignatureType === 2">
                            {{ doctorName }}
                        </template>
                        <!-- 手写签名 -->
                        <template v-else-if="footerConfig.footerDoctorSignatureType === 3">
                            <img
                                v-if="doctorHandSign && isImgUrl(doctorHandSign)"
                                :src="doctorHandSign"
                                alt=""
                                class="footer-hand-sign-img"
                            />
                            <span
                                v-else
                                class="footer-hand-sign-title"
                            >{{ doctorName }}</span>
                        </template>
                    </print-col>
                    <print-col
                        v-if="footerConfig.footerNurseSignature"
                        :span="8"
                        class="footer-content"
                    >
                        <span class="footer-hand-sign-title">护士签名：</span>
                        <!-- 电脑签名 -->
                        <template v-if="footerConfig.footerNurseSignatureType === 2">
                            {{ operateNurseName }}
                        </template>
                        <!-- 手写签名 -->
                        <template v-else-if="footerConfig.footerNurseSignatureType === 3">
                            <img
                                v-if="operateNurseHandSign && isImgUrl(operateNurseHandSign)"
                                :src="operateNurseHandSign"
                                alt=""
                                class="footer-hand-sign-img"
                            />
                            <span
                                v-else
                                class="footer-hand-sign-title"
                            >{{ operateNurseName }}</span>
                        </template>
                    </print-col>
                    <print-col
                        v-if="footerConfig.printDate"
                        :span="8"
                        class="footer-content"
                        :style="{ 'text-align': !footerConfig.footerDoctorSignature && !footerConfig.footerNurseSignature ? 'left' : 'right' }"
                    >
                        打印时间：{{ new Date() | parseTime('y-m-d h:i', true) }}
                    </print-col>
                </print-row>
            </div>
            <div class="pagination">
                <template v-if="extra.isPreview">
                    第1页/共1页
                </template>
                <template v-else>
                    第<span data-page-no="PageNo"></span>页/共<span data-page-count="PageCount"></span>页
                </template>
            </div>
        </div>
    </div>
</template>

<script>
    import { capitalizeFirstLetter, formatAge, isImgUrl, parseTime } from './common/utils.js';

    import CommonHandler from './data-handler/common-handler.js'
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import PrintCol from "./components/layout/print-col.vue";
    import PrintRow from "./components/layout/print-row.vue";
    import {
        AdviceRuleType,
        AdviceTagEnum,
        AdviceTagEnumSingleText,
        HospitalAstEnum,
        MedicalAdviceTypeEnum,
        TreatmentTypeEnum,
    } from "./common/constants.js";
    import Clone from "./common/clone";


    const initPrintConfig = {
        // 医嘱单前记
        header: {
            'title': '', // 抬头名称 字符串必填
            'subtitle': '', // 副抬头名称 可选值
        },
        // 医嘱单正文
        content: {
            'executeBeforeMedical': 2, // 下达、执行信息在医嘱前/后, 可选值 1 2, 默认 2
            'longTimeUsage': 1, // 长期医嘱单-用法, 可选值 0 1, 默认 1
            'longTimeCheckedTime': 1, // 长期医嘱单-核对时间, 可选值 0 1, 默认 1
            'longTimeCheckedOperatorName': 1, // 长期医嘱单-核对护士, 可选值 0 1, 默认 1
            'longTimeOperateNurseSignature': 1, // 长期医嘱单-护士签名, 可选值 0 1, 默认 1
            'longTimeStopTime': 1, // 长期医嘱单-停止时间, 可选值 0 1, 默认 1
            'longTimeStopDoctor': 1, // 长期医嘱单-停止医生, 可选值 0 1, 默认 1
            'longTimeRemark': 1, // 长期医嘱单-医嘱备注, 可选值 0 1, 默认 1
            'oneTimeUsage': 1, // 临时医嘱单-用法, 可选值 0 1, 默认 1
            'oneTimeCheckedTime': 1, // 临时医嘱单-核对时间, 可选值 0 1, 默认 1
            'oneTimeCheckedOperatorName': 1, // 临时医嘱单-核对护士, 可选值 0 1, 默认 1
            'oneTimeOperateNurseSignature': 1, // 临时医嘱单-护士签名, 可选值 0 1, 默认 1
            'oneTimeRemark': 1, // 临时医嘱单-医嘱备注, 可选值 0 1, 默认 1
            'doctorSignatureType': 2, // 医生签名, 可选值 1 2 3, 默认 2
            'checkedNurseSignatureType': 2, // 核对护士签名, 可选值 1 2 3, 默认 2
            'stopDoctorSignatureType': 2, // 停止医生签名, 可选值 1 2 3, 默认 2
            'operateNurseSignatureType': 2, // 护士签名, 可选值 1 2 3, 默认 2
            'oneTimeExecuteTime': 0, // 临时医嘱执行时间，可选值 0 1, 默认 0
            'longTimeNurseSignature': 0, // 长期医嘱护士签名 可选值 0 1, 默认 0
            'oneTimeNurseSignature': 0, // 临时医嘱护士签名 可选值 0 1, 默认0
            'stopNurseSignatureType': 2, //  停止护士签名 可选值 1 2 3, 默认 2
        },
        // 医嘱单后记
        footer: {
            'footerDoctorSignature': 1, // 医生签字, 可选值 0 1, 默认 1
            'footerNurseSignature': 1, // 护士签字, 可选值 0 1, 默认 1
            'printDate': 0, // 打印时间, 可选值 0 1, 默认 0
            'footerDoctorSignatureType': 2, // 医生签字, 可选值 1 2 3, 默认 2
            'footerNurseSignatureType': 2, // 护士签字, 可选值 1 2 3, 默认 2
        },
    };

    export default {
        name: "HospitalDoctorMedicalPrescription",
        DataHandler: CommonHandler,
        components: {
            PrintRow,
            PrintCol,
        },
        businessKey: PrintBusinessKeyEnum.HOSPITAL_DOCTOR_MEDICAL_PRESCRIPTION,
        filters: {
            parseTime,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                },
            },
            extra: {
                type: Object,
                default() {
                    return {}
                },
            },
        },
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        data() {
            return {
                formatAge,
            }
        },
        computed: {
            printData() {
                console.log('%crenderData\n', 'background: green; padding: 0 5px', Clone(this.renderData));
                return this.renderData.printData;
            },
            config() {
                if (this.renderData.config && this.renderData.config.hospitalMedicalDocuments && this.renderData.config.hospitalMedicalDocuments.advice) {
                    return this.renderData.config.hospitalMedicalDocuments.advice;
                }
                return initPrintConfig;
            },
            headerConfig() {
                return this.config.header || {};
            },
            contentConfig() {
                return this.config.content || {};
            },
            footerConfig() {
                return this.config.footer || {};
            },
            medicalPrescriptionTitle() {
                return this.printData.medicalPrescriptionTitle;
            },
            clinicName() {
                return this.printData.clinicName;
            },
            patient() {
                return this.printData.patient;
            },
            patientHospitalInfo() {
                return this.printData.patientHospitalInfo;
            },
            tableDataGroup() {
                return this.printData.medicalPrescriptionList || [];
            },
            isOneTimeAdvice() {
                return this.printData && this.printData.medicalAdviceType === MedicalAdviceTypeEnum.ONE_TIME;
            },
            isLongTimeAdvice() {
                return this.printData && this.printData.medicalAdviceType === MedicalAdviceTypeEnum.LONG_TIME;
            },
            operateNurseName() {
                return this.printData.operateNurseName;
            },
            operateNurseHandSign() {
                return this.printData.operateNurseHandSign;
            },
            doctorName() {
                return this.patientHospitalInfo.doctorName;
            },
            doctorHandSign() {
                return this.patientHospitalInfo.doctorHandSign;
            },
            nurseNameTh() {
                if (this.isLongTimeAdvice) {
                    return '停止护士'
                }
                return '执行护士'
            },

            showGroupTags() {
                return [
                    AdviceTagEnum.MA_ZUI,
                    AdviceTagEnum.JING_1,
                    AdviceTagEnum.JING_2,
                    AdviceTagEnum.DU,
                    AdviceTagEnum.OPERATE_ING,
                    AdviceTagEnum.OPERATE_AFTER,
                ]
            },
        },
        methods: {
            isImgUrl,
            isMaterialsAdvice(group) {
                return group.diagnosisTreatmentType === TreatmentTypeEnum.MATERIALS;
            },
            renderAdviceContent(advice, adviceGroup) {
                const item = advice;
                const group = adviceGroup;

                let str = item.name;
                if (group.type === MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE) {
                    str += '【出院带药】';
                    if (item.dosageCount && item.dosageUnit) {
                        str += ` 共${item.dosageCount}${item.dosageUnit}`;
                    }
                }
                if (group.diagnosisTreatmentType === TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE) {
                    if (item.name === '死亡出院') {
                        str += ` 死亡时间：${item.deathTime ? parseTime(item.deathTime, 'm-d h:i', true) : ''} `;
                    } else {
                        str += ` 出院时间：${item.dischargeHospitalTime ? parseTime(item.dischargeHospitalTime, 'm-d h:i', true) : ''} 出院原因：${item.dischargeHospitalReason || ''} `;
                    }
                } else if ((group.diagnosisTreatmentType === TreatmentTypeEnum.INSPECTION ||
                    group.diagnosisTreatmentType === TreatmentTypeEnum.INSPECTION) &&
                    item.examApplySheetPurpose) {
                    str += ` 目的：${item.examApplySheetPurpose} `;
                } else {
                    const adviceItem = item.adviceGoodsItems?.find?.((goodsItem) => {
                        return !goodsItem.type;
                    });
                    if ([TreatmentTypeEnum.MEDICINE, TreatmentTypeEnum.MATERIALS].includes(group.diagnosisTreatmentType) && adviceItem && adviceItem.goodsSpec) {
                        str += ` [${adviceItem.goodsSpec}] `;
                    }
                    if (this.isChineseAdvice(group)) {
                        str += ` 共${item.dosageCount}${item.dosageUnit}`;
                    }
                    // 非临时医嘱都有频率
                    if (this.isLongTimeAdvice || this.isChineseAdvice(group)) {
                        str += ` ${capitalizeFirstLetter( group.freq )} `;
                    }
                    // 出院带药医嘱不展示首次执行次数
                    if (group.type !== MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE && item.freqInfo && item.freqInfo.firstDayFrequency) {
                        str += ` 首日:${item.freqInfo.firstDayFrequency}次`;
                    }

                    const astResult = item.astResult?.result;
                    const displayAstResult = astResult ? astResult === '阴性' ? '-' : '+' : ' ';
                    if (item.astFlag === HospitalAstEnum.PI_SHI) {
                        str += ` 皮试( ${displayAstResult} ) `;
                    }

                    if (item.astFlag === HospitalAstEnum.MIAN_SHI) {
                        str += ` 免试`;
                    }

                    if (item.astFlag === HospitalAstEnum.XU_YONG) {
                        str += ` 续用`;
                    }
                }
                if (item.treatmentSites?.length) {
                    str += ` ${item.treatmentSites.join()}`
                }
                if (((this.isOneTimeAdvice && this.contentConfig.oneTimeRemark) || (this.isLongTimeAdvice && this.contentConfig.longTimeRemark)) && item.remark) {
                    str += ` ${item.remark}`
                }


                // 打印备注
                const showAdviceTag = this.isLongTimeAdvice ? this.contentConfig.longTimeAdviceTag : this.contentConfig.oneTimeAdviceTag;
                if(showAdviceTag && group.tags && group.tags.length) {
                    const tags = group.tags.filter(tag => this.showGroupTags.includes(tag.type));
                    const tagArr = tags.map(tag => AdviceTagEnumSingleText[tag.type]);
                    const res = tagArr.filter(Boolean).join('/');
                    if(res) {
                        str += `/${res}`;
                    }
                }
                return str;
            },

            renderAdviceUsage(advice) {
                const { usage, ivgtt } = advice;
                if (!usage)  return '执行';
                if (!ivgtt) return usage;
                return `${usage} ${ivgtt}滴/分钟`
            },
            /**
             * 判断医嘱是否是中药饮片
             */
            isChineseAdvice(advice) {
                if (!advice.advices?.length) return false;
                const item = advice.advices[0];
                return item.adviceRuleType === AdviceRuleType.CHINESE_MEDICINE_TABLETS || item.adviceRuleType === AdviceRuleType.CHINESE_MEDICINE_GRANULES;
            },
            /**
             * 拆分中药饮片明细
             */
            splitChineseMedicineItem(advice) {
                const { adviceGoodsItems = [] } = advice.advices[0];
                const res = [];
                // 将 adviceGoodsItems 数组拆分为 2 维数组，每个数组有 3 个元素
                for (let i = 0; i < adviceGoodsItems.length; i += 3) {
                    res.push(adviceGoodsItems.slice(i, i + 3));
                }
                return res;
            },
        },
    }
</script>
<style lang="scss">
@import "./components/layout/print-layout.scss";

.abc-page_preview {
    td,
    th {
        line-height: 1 !important;
    }
}

.abc-hospital-doctor-medical-prescription {
    font-family: 'SimSong', 'SimSun', 'Arial', 'Helvetica', 'sans-serif';
    font-size: 10pt;

    .clinic-name,
    .prescription-type {
        text-align: center;
    }

    .prescription-type {
        margin-bottom: 23pt;
        font-weight: normal;
    }

    .table-header {
        padding-bottom: 6pt;
        margin-bottom: 6pt;
        overflow: hidden;

        .table-header-left {
            float: left;
        }

        .table-header-right {
            float: right;
        }
    }

    .align-center {
        text-align: center;
    }

    .tr-content {
        & + .tr-content {
            margin-top: 8pt;
        }
    }

    .width-42 {
        width: 42pt;
    }

    .width-219 {
        width: 219pt;
    }

    table {
        width: 100%;
        table-layout: fixed;
        border-collapse: collapse;
        border: 1px solid #a6a6a6;
    }

    th {
        padding: 6pt 4pt;
        text-align: center;
    }

    td {
        padding: 6pt 1pt;
        text-align: center;
    }

    .more-right-padding {
        padding-right: 6pt;
    }

    .text-right {
        text-align: right;
    }

    .pagination {
        padding-top: 6px;
        margin-top: 6pt;
        text-align: center;
        border-top: 1pt solid #a6a6a6;
    }

    .hand-sign-img {
        width: 100%;
    }

    .footer-content {
        position: relative;
    }

    .footer-hand-sign-img {
        position: absolute;
        top: -3pt;
        left: 50pt;
        height: 16pt;
    }

    .is-group-start {
        position: relative;
        border-bottom: none;

        &::after {
            position: absolute;
            top: 50%;
            right: 4px;
            display: block;
            width: 1px;
            height: calc(50% + 1px);
            content: '';
            background: #000000;
        }

        &::before {
            position: absolute;
            top: 50%;
            right: 4px;
            display: block;
            width: 4px;
            height: 1px;
            content: '';
            background: #000000;
        }
    }

    .is-group-end {
        position: relative;

        &::after {
            position: absolute;
            top: -1px;
            right: 4px;
            display: block;
            width: 1px;
            height: calc(50% + 1px);
            content: '';
            background: #000000;
        }

        &::before {
            position: absolute;
            top: 50%;
            right: 4px;
            display: block;
            width: 4px;
            height: 1px;
            content: '';
            background: #000000;
        }
    }

    .is-group-process {
        position: relative;

        &::after {
            position: absolute;
            top: -1px;
            right: 4px;
            display: block;
            width: 1px;
            height: calc(100% + 2px);
            content: '';
            background: #000000;
        }
    }
}
</style>
<!--exampleData
{
	"medicalPrescriptionList": [{
			"id": "3780510621636968448",
			"chainId": "ffffffff00000000146808c695534000",
			"clinicId": "ffffffff00000000146808c695534004",
			"wardAreaId": "**********855243776",
			"departmentId": "ffffffff0000000034692fb595df0000",
			"patientId": "ffffffff00000000346c7c569fae0000",
			"patientOrderId": "ffffffff0000000034771240a1dd0000",
			"type": 0,
			"diagnosisTreatmentType": 20,
			"status": 10,
			"usage": "",
			"freq": "St",
			"freqInfo": null,
			"days": 1,
			"startTime": "2023-02-22T09:35:00Z",
			"stopTime": null,
			"created": "2023-02-22T09:36:11Z",
			"createdBy": "6e45706922a74966ab51e4ed1e604641",
			"tags": [],
			"createdByName": "张海滨",
			"advices": [{
				"id": "3780510621636968448",
				"chainId": "ffffffff00000000146808c695534000",
				"clinicId": "ffffffff00000000146808c695534004",
				"wardAreaId": "**********855243776",
				"departmentId": "ffffffff0000000034692fb595df0000",
				"patientId": "ffffffff00000000346c7c569fae0000",
				"patientOrderId": "ffffffff0000000034771240a1dd0000",
				"type": 0,
				"diagnosisTreatmentType": 20,
				"name": "血常规",
				"status": 10,
				"usage": "",
				"freq": "St",
				"freqInfo": null,
				"days": 1,
				"startTime": "2023-02-22T09:35:00Z",
				"stopTime": null,
				"createdBy": "6e45706922a74966ab51e4ed1e604641",
				"createdByName": "张海滨",
				"stopDoctorId": null,
				"stopDoctorName": null,
				"tags": [],
				"groupId": "3780510621636968448",
				"groupSort": 0,
				"checkedOperateId": "3782042474874994688",
				"remark": null,
				"adviceRuleType": 40,
				"astFlag": null,
				"astResult": null,
				"singleDosageCount": "1",
				"singleDosageUnit": "次",
				"pharmacyNo": 0,
				"pharmacyType": 0,
				"pharmacyInfo": null,
				"treatmentSites": [],
				"chargeFlag": 0,
				"dischargeHospitalReason": null,
				"isGoodsDefaultPharmacy": 1,
				"westernPrimaryItemSpec": null,
				"medicalFeeGrade": null,
				"examApplySheetPurpose": "aaaaa",
				"simpleExamSheet": null,
				"isNeedRefundDispensing": 0
			}],
			"checked": true
		},
		{
			"id": "3780510689819574272",
			"chainId": "ffffffff00000000146808c695534000",
			"clinicId": "ffffffff00000000146808c695534004",
			"wardAreaId": "**********855243776",
			"departmentId": "ffffffff0000000034692fb595df0000",
			"patientId": "ffffffff00000000346c7c569fae0000",
			"patientOrderId": "ffffffff0000000034771240a1dd0000",
			"type": 0,
			"diagnosisTreatmentType": 20,
			"status": 10,
			"usage": "",
			"freq": "St",
			"freqInfo": null,
			"days": 1,
			"startTime": "2023-02-22T09:38:00Z",
			"stopTime": null,
			"created": "2023-02-22T09:38:18Z",
			"createdBy": "6e45706922a74966ab51e4ed1e604641",
			"tags": [],
			"createdByName": "张海滨",
			"advices": [{
				"id": "3780510689819574272",
				"chainId": "ffffffff00000000146808c695534000",
				"clinicId": "ffffffff00000000146808c695534004",
				"wardAreaId": "**********855243776",
				"departmentId": "ffffffff0000000034692fb595df0000",
				"patientId": "ffffffff00000000346c7c569fae0000",
				"patientOrderId": "ffffffff0000000034771240a1dd0000",
				"type": 0,
				"diagnosisTreatmentType": 20,
				"name": "110",
				"status": 10,
				"usage": "",
				"freq": "St",
				"freqInfo": null,
				"days": 1,
				"startTime": "2023-02-22T09:38:00Z",
				"stopTime": null,
				"createdBy": "6e45706922a74966ab51e4ed1e604641",
				"createdByName": "张海滨",
				"stopDoctorId": null,
				"stopDoctorName": null,
				"tags": [],
				"groupId": "3780510689819574272",
				"groupSort": 0,
				"checkedOperateId": "3782042498497314816",
				"remark": null,
				"adviceRuleType": 40,
				"astFlag": null,
				"astResult": null,
				"singleDosageCount": "1",
				"singleDosageUnit": "次",
				"pharmacyNo": 0,
				"pharmacyType": 0,
				"pharmacyInfo": null,
				"treatmentSites": [],
				"chargeFlag": 0,
				"dischargeHospitalReason": null,
				"isGoodsDefaultPharmacy": 1,
				"westernPrimaryItemSpec": null,
				"medicalFeeGrade": null,
				"examApplySheetPurpose": "aaaaa",
				"simpleExamSheet": null,
				"isNeedRefundDispensing": 0
			}],
			"checked": true
		},
		{
			"id": "3780971879650590720",
			"chainId": "ffffffff00000000146808c695534000",
			"clinicId": "ffffffff00000000146808c695534004",
			"wardAreaId": "**********855243776",
			"departmentId": "ffffffff0000000034692fb595df0000",
			"patientId": "ffffffff00000000346c7c569fae0000",
			"patientOrderId": "ffffffff0000000034771240a1dd0000",
			"type": 0,
			"diagnosisTreatmentType": 1,
			"status": 10,
			"usage": "口服",
			"freq": "st",
			"freqInfo": null,
			"days": 1,
			"startTime": "2023-03-04T08:14:00Z",
			"stopTime": null,
			"created": "2023-03-04T08:15:32Z",
			"createdBy": "6e45706922a74966ab51e4ed1e604641",
			"tags": [],
			"createdByName": "张海滨",
			"advices": [{
				"id": "3780971879650590720",
				"chainId": "ffffffff00000000146808c695534000",
				"clinicId": "ffffffff00000000146808c695534004",
				"wardAreaId": "**********855243776",
				"departmentId": "ffffffff0000000034692fb595df0000",
				"patientId": "ffffffff00000000346c7c569fae0000",
				"patientOrderId": "ffffffff0000000034771240a1dd0000",
				"type": 0,
				"diagnosisTreatmentType": 1,
				"name": "阿莫西林胶囊（联邦阿莫仙）",
				"status": 10,
				"usage": "口服",
				"freq": "st",
				"freqInfo": null,
				"days": 1,
				"startTime": "2023-03-04T08:14:00Z",
				"stopTime": null,
				"createdBy": "6e45706922a74966ab51e4ed1e604641",
				"createdByName": "张海滨",
				"stopDoctorId": null,
				"stopDoctorName": null,
				"tags": [],
				"groupId": "3780971879650590720",
				"groupSort": 0,
				"checkedOperateId": "3780971885019299840",
				"remark": "本地药房",
				"adviceRuleType": 0,
				"astFlag": null,
				"astResult": null,
				"singleDosageCount": "50",
				"singleDosageUnit": "mg",
				"pharmacyNo": 0,
				"pharmacyType": 0,
				"pharmacyInfo": null,
				"treatmentSites": [],
				"chargeFlag": 0,
				"dischargeHospitalReason": null,
				"isGoodsDefaultPharmacy": 1,
				"westernPrimaryItemSpec": "20mg*20片/盒",
				"medicalFeeGrade": null,
				"examApplySheetPurpose": null,
				"simpleExamSheet": null,
				"isNeedRefundDispensing": 0
			}],
			"checked": true
		},
		{
			"id": "3788762619078410240",
			"chainId": "ffffffff00000000146808c695534000",
			"clinicId": "ffffffff00000000146808c695534004",
			"wardAreaId": "**********855243776",
			"departmentId": "ffffffff0000000034692fb595df0000",
			"patientId": "ffffffff00000000346c7c569fae0000",
			"patientOrderId": "ffffffff0000000034771240a1dd0000",
			"type": 0,
			"diagnosisTreatmentType": 1,
			"status": 10,
			"usage": "口服",
			"freq": "st",
			"freqInfo": null,
			"days": 1,
			"startTime": "2023-08-17T07:11:00Z",
			"stopTime": null,
			"created": "2023-08-19T07:11:55Z",
			"createdBy": "6e45706922a74966ab51e4ed1e604641",
			"tags": [{
					"type": 0,
					"name": "补开"
				},
				{
					"type": 1,
					"name": "加急"
				}
			],
			"createdByName": "张海滨",
			"advices": [{
				"id": "3788762619078410240",
				"chainId": "ffffffff00000000146808c695534000",
				"clinicId": "ffffffff00000000146808c695534004",
				"wardAreaId": "**********855243776",
				"departmentId": "ffffffff0000000034692fb595df0000",
				"patientId": "ffffffff00000000346c7c569fae0000",
				"patientOrderId": "ffffffff0000000034771240a1dd0000",
				"type": 0,
				"diagnosisTreatmentType": 1,
				"name": "阿莫君阿莫西林胶囊(阿莫西林)",
				"status": 10,
				"usage": "口服",
				"freq": "st",
				"freqInfo": null,
				"days": 1,
				"startTime": "2023-08-17T07:11:00Z",
				"stopTime": null,
				"createdBy": "6e45706922a74966ab51e4ed1e604641",
				"createdByName": "张海滨",
				"stopDoctorId": null,
				"stopDoctorName": null,
				"tags": [{
						"type": 0,
						"name": "补开"
					},
					{
						"type": 1,
						"name": "加急"
					}
				],
				"groupId": "3788762619078410240",
				"groupSort": 0,
				"checkedOperateId": "3788762657196244992",
				"remark": "aaaa",
				"adviceRuleType": 0,
				"astFlag": null,
				"astResult": null,
				"singleDosageCount": "0.5",
				"singleDosageUnit": "片",
				"pharmacyNo": 7,
				"pharmacyType": 0,
				"pharmacyInfo": {
					"chainId": null,
					"clinicId": null,
					"no": 7,
					"name": "药库2",
					"type": 0,
					"typeName": "本地药房",
					"stockCutType": 0,
					"sort": 0,
					"innerFlag": 0,
					"dispenseFlag": 0,
					"status": 1,
					"isDeleted": 0
				},
				"treatmentSites": [],
				"chargeFlag": 0,
				"dischargeHospitalReason": null,
				"isGoodsDefaultPharmacy": 0,
				"westernPrimaryItemSpec": "12片/盒",
				"medicalFeeGrade": 0,
				"examApplySheetPurpose": null,
				"simpleExamSheet": null,
				"isNeedRefundDispensing": 0
			}],
			"checked": true
		},
		{
			"id": "3789220148523122688",
			"chainId": "ffffffff00000000146808c695534000",
			"clinicId": "ffffffff00000000146808c695534004",
			"wardAreaId": "**********855243776",
			"departmentId": "ffffffff0000000034692fb595df0000",
			"patientId": "ffffffff00000000346c7c569fae0000",
			"patientOrderId": "ffffffff0000000034771240a1dd0000",
			"type": 0,
			"diagnosisTreatmentType": 10,
			"status": 10,
			"usage": "",
			"freq": "st",
			"freqInfo": null,
			"days": 1,
			"startTime": "2023-08-29T03:55:00Z",
			"stopTime": null,
			"created": "2023-08-29T03:55:30Z",
			"createdBy": "6e45706922a74966ab51e4ed1e604641",
			"tags": [],
			"createdByName": "张海滨",
			"advices": [{
				"id": "3789220148523122688",
				"chainId": "ffffffff00000000146808c695534000",
				"clinicId": "ffffffff00000000146808c695534004",
				"wardAreaId": "**********855243776",
				"departmentId": "ffffffff0000000034692fb595df0000",
				"patientId": "ffffffff00000000346c7c569fae0000",
				"patientOrderId": "ffffffff0000000034771240a1dd0000",
				"type": 0,
				"diagnosisTreatmentType": 10,
				"name": "CT-01",
				"status": 10,
				"usage": "",
				"freq": "st",
				"freqInfo": null,
				"days": 1,
				"startTime": "2023-08-29T03:55:00Z",
				"stopTime": null,
				"createdBy": "6e45706922a74966ab51e4ed1e604641",
				"createdByName": "张海滨",
				"stopDoctorId": null,
				"stopDoctorName": null,
				"tags": [],
				"groupId": "3789220148523122688",
				"groupSort": 0,
				"checkedOperateId": "3789920032235077632",
				"remark": "",
				"adviceRuleType": 30,
				"astFlag": null,
				"astResult": null,
				"singleDosageCount": "1",
				"singleDosageUnit": "次",
				"pharmacyNo": 0,
				"pharmacyType": 0,
				"pharmacyInfo": {
					"chainId": null,
					"clinicId": null,
					"no": 0,
					"name": "西药库",
					"type": 0,
					"typeName": "本地药房",
					"stockCutType": 0,
					"sort": 0,
					"innerFlag": 0,
					"dispenseFlag": 0,
					"status": 1,
					"isDeleted": 0
				},
				"treatmentSites": [],
				"chargeFlag": 0,
				"dischargeHospitalReason": null,
				"isGoodsDefaultPharmacy": 1,
				"westernPrimaryItemSpec": null,
				"medicalFeeGrade": 0,
				"examApplySheetPurpose": "",
				"simpleExamSheet": null,
				"isNeedRefundDispensing": 0
			}],
			"checked": true
		},
		{
			"id": "3789239849001238528",
			"chainId": "ffffffff00000000146808c695534000",
			"clinicId": "ffffffff00000000146808c695534004",
			"wardAreaId": "**********855243776",
			"departmentId": "ffffffff0000000034692fb595df0000",
			"patientId": "ffffffff00000000346c7c569fae0000",
			"patientOrderId": "ffffffff0000000034771240a1dd0000",
			"type": 0,
			"diagnosisTreatmentType": 90,
			"status": 10,
			"usage": "",
			"freq": "st",
			"freqInfo": null,
			"days": 1,
			"startTime": "2023-08-29T14:06:00Z",
			"stopTime": null,
			"created": "2023-08-29T14:07:04Z",
			"createdBy": "6e45706922a74966ab51e4ed1e604641",
			"tags": [],
			"createdByName": "张海滨",
			"advices": [{
				"id": "3789239849001238528",
				"chainId": "ffffffff00000000146808c695534000",
				"clinicId": "ffffffff00000000146808c695534004",
				"wardAreaId": "**********855243776",
				"departmentId": "ffffffff0000000034692fb595df0000",
				"patientId": "ffffffff00000000346c7c569fae0000",
				"patientOrderId": "ffffffff0000000034771240a1dd0000",
				"type": 0,
				"diagnosisTreatmentType": 90,
				"name": "转院出院",
				"status": 10,
				"usage": "",
				"freq": "st",
				"freqInfo": null,
				"days": 1,
				"startTime": "2023-08-29T14:06:00Z",
				"stopTime": null,
				"createdBy": "6e45706922a74966ab51e4ed1e604641",
				"createdByName": "张海滨",
				"stopDoctorId": null,
				"stopDoctorName": null,
				"tags": [],
				"groupId": "3789239849001238528",
				"groupSort": 0,
				"checkedOperateId": "3789239919868198912",
				"remark": "",
				"adviceRuleType": 91,
				"astFlag": null,
				"astResult": null,
				"singleDosageCount": "1",
				"singleDosageUnit": "次",
				"pharmacyNo": 0,
				"pharmacyType": 0,
				"pharmacyInfo": {
					"chainId": null,
					"clinicId": null,
					"no": 0,
					"name": "西药库",
					"type": 0,
					"typeName": "本地药房",
					"stockCutType": 0,
					"sort": 0,
					"innerFlag": 0,
					"dispenseFlag": 0,
					"status": 1,
					"isDeleted": 0
				},
				"treatmentSites": [],
				"chargeFlag": 0,
				"dischargeHospitalReason": "自动转院",
				"isGoodsDefaultPharmacy": 1,
				"westernPrimaryItemSpec": null,
				"medicalFeeGrade": 0,
				"examApplySheetPurpose": null,
				"simpleExamSheet": null,
				"isNeedRefundDispensing": 0
			}],
			"checked": true
		}
	],
	"medicalPrescriptionTitle": "临时医嘱单",
	"patientHospitalInfo": {
		"id": "ffffffff0000000034771240a1dd0000",
		"chainId": "ffffffff00000000146808c695534000",
		"clinicId": "ffffffff00000000146808c695534004",
		"patient": {
			"id": "ffffffff00000000346c7c569fae0000",
			"name": "徐传娥",
			"age": {
				"year": 64,
				"month": 9,
				"day": 21
			},
			"birthday": "1959-01-20",
			"sex": "女",
			"mobile": "18300249025",
			"namePy": "xuchuane",
			"namePyFirst": "XCE",
			"isMember": 0,
			"idCard": "",
			"pastHistory": "既往体健",
			"sn": "000774",
			"remark": "",
			"profession": "",
			"company": "",
			"companyAddress": null,
			"companyMobile": null,
			"marital": 1,
			"weight": null,
			"importFlag": 0,
			"ethnicity": "",
			"nationality": "",
			"contactName": "",
			"contactRelation": "",
			"contactMobile": "",
			"address": {
				"addressCityId": "",
				"addressCityName": "",
				"addressDetail": "",
				"addressDistrictId": "",
				"addressDistrictName": "",
				"addressGeo": null,
				"addressProvinceId": "",
				"addressProvinceName": "",
				"addressPostcode": null
			},
			"familyMobile": null,
			"patientSource": null,
			"tags": [{
					"tagId": "732122144163104",
					"tagName": "哮喘",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163105",
					"tagName": "支气管炎",
					"genMode": 0,
					"viewMode": 1,
					"style": {
						"viewMode": 0,
						"text": "支",
						"color": "#1489ff",
						"shape": "rect",
						"iconUrl": null
					}
				},
				{
					"tagId": "732122144163106",
					"tagName": "关节炎",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163107",
					"tagName": "颈椎病",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163108",
					"tagName": "骨质疏松",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163110",
					"tagName": "胃肠炎",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163111",
					"tagName": "便秘",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163112",
					"tagName": "鼻咽炎",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163113",
					"tagName": "慢性软组织损伤",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163114",
					"tagName": "高血脂",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163115",
					"tagName": "糖尿病",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163116",
					"tagName": "痛风",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163117",
					"tagName": "头痛",
					"genMode": 0,
					"viewMode": 1,
					"style": {
						"viewMode": 0,
						"text": "头",
						"color": "#1489ff",
						"shape": "rect",
						"iconUrl": null
					}
				},
				{
					"tagId": "732122144163118",
					"tagName": "偏头痛",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163119",
					"tagName": "鼻炎",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163120",
					"tagName": "鼻窦炎",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "732122144163121",
					"tagName": "咽喉炎",
					"genMode": 0,
					"viewMode": 0,
					"style": null
				},
				{
					"tagId": "ffffffff00000000347b82243fdf4000",
					"tagName": "心脏病",
					"genMode": 0,
					"viewMode": 1,
					"style": {
						"viewMode": 0,
						"text": "危",
						"color": "#6e23ed",
						"shape": "rect",
						"iconUrl": "https://static-common-cdn.abcyun.cn/img/patient-label/v2/virus.png"
					}
				},
				{
					"tagId": "ffffffff00000000347b8229bfdf4000",
					"tagName": "有钱人",
					"genMode": 0,
					"viewMode": 1,
					"style": {
						"viewMode": 1,
						"text": "有",
						"color": "#1489ff",
						"shape": "rect",
						"iconUrl": "https://static-common-cdn.abcyun.cn/img/patient-label/v2/money.png"
					}
				}
			],
			"shebaoCardInfo": null,
			"contactAddress": null,
			"birthAddress": null,
			"ancestralAddress": null,
			"registerAddress": null
		},
		"no": "000145",
		"status": 60,
		"inpatientTimeRequest": "2023-02-22T17:30:00Z",
		"departmentId": "ffffffff0000000034692fb595df0000",
		"departmentName": "妇产科",
		"wardId": "**********855243776",
		"wardName": "妇产科病区",
		"bedId": "3780125410849194029",
		"bedNo": "1",
		"adviceDeposit": null,
		"feeTypeName": "",
		"inpatientWay": 0,
		"inpatientSource": 2,
		"inpatientCondition": 0,
		"outpatientDoctorId": "6e45706922a74966ab51e4ed1e604641",
		"outpatientDoctorName": "张海滨",
		"firstInDiagnosedTime": "2023-03-03T16:00:00Z",
		"preDiagnosisInfos": [{
			"toothNos": null,
			"value": [{
				"code": "S23.100x071",
				"name": "胸腰椎脱位T12/L1",
				"diseaseType": null
			}]
		}],
		"primaryDiagnosisInfos": [{
			"toothNos": null,
			"value": [{
				"code": "Q78.401",
				"name": "奥利埃病",
				"diseaseType": "医保ICD10"
			}]
		}],
		"doctorId": "3a0615203ad5414bb99e7e1e536e096d",
		"doctorName": "彬鉴",
		"nurseId": "3a0615203ad5414bb99e7e1e536e096d",
		"nurseName": "彬鉴",
		"inpatientTime": "2023-02-22T09:32:20Z",
		"dischargeTime": "2023-08-29T14:06:00Z",
		"dischargeReason": "自动转院",
		"inpatientDays": 188,
		"times": 1,
		"totalPrice": null,
		"depositAvailableFee": null,
		"tags": [{
				"id": "2",
				"name": "出院"
			},
			{
				"id": "3",
				"name": "欠费"
			},
			{
				"id": "12",
				"name": "待核对"
			}
		],
		"departmentTransferRecord": null,
		"nurseLevel": null
	},
	"patient": {
		"id": "ffffffff00000000346c7c569fae0000",
		"name": "徐传娥",
		"namePy": "xuchuane",
		"namePyFirst": "XCE",
		"mobile": "18300249025",
		"countryCode": null,
		"sex": "女",
		"birthday": "1959-01-20",
		"age": {
			"year": 64,
			"month": 9,
			"day": 21
		},
		"chainId": "ffffffff00000000146808c695534000",
		"isMember": 0,
		"idCard": ""
	},
	"clinicName": "ABC医院"
}
-->
