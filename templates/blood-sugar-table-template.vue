<template>
    <div></div>
</template>

<script>
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import CommonHandler from "./data-handler/common-handler";
    import PageSizeMap, {Orientation} from "../share/page-size.js";

    export default {
        name: "BloodSugarTableTemplate",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.BLOOD_SUGAR_TABLE_TEMPLATE,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
        ],
    }
</script>

<style lang="scss">
.abc-flex-wrapper{
  display: flex;
}
.abc-flex-justify-normal {
  justify-content: normal;
}
.abc-flex-justify-space-between {
  justify-content: space-between;
}
.abc-flex-align-center {
  align-items: center;
}
.abc-flex-wrap-wrap {
  flex-wrap: wrap;
}
.abc-flex-wrap-nowrap {
  flex-wrap: nowrap;
}
.abc-flex-vertical {
  flex-direction: column;
}

table {
  width: 100% !important;
  font-size: 14px;
  border: 1px solid #aaa;
  table-layout: fixed;
  border-collapse: collapse;
  border-spacing: 0;

  thead {
    background: #f5f7fb;

    th {
      text-align: center;
      width: 235px;
      min-width: 235px;
      height: 40px !important;
      white-space: normal;
      word-break: normal;
      max-height: 40px !important;
      padding: 2px;
      font-weight: 400;
      color: #000000;
      border: 1px solid #aaa;
      border-left: none;
      border-top: none;
      box-sizing: border-box;
      background: #fff;
    }
  }

  tbody {
    tr {
      border-bottom: 1px solid #aaa;
      border-left: none;
      border-right: none;

      td {
        box-sizing: border-box;

        &:not(:last-child) {
          border-right: 1px solid #aaa;
        }

        div.cell {
          padding: 2px;
        }

        background: #fff;
      }
    }
  }
}
</style>
