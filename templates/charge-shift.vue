<template>
    <div class="charge-shift-wrapper">
        <div
            data-type="header"
            class="charge-shift-header"
        >
            <div class="charge-shift-title">
                {{ printData.clinicName }}护士交班记录
            </div>
            <div class="charge-shift-info-wrapper">
                <div class="charge-shift-ward">
                    病区：{{ printData.wardName }}
                </div>
                <div class="charge-shift-ward">
                    <div class="charge-shift-detail">
                        班次：{{ ShiftTypeToText[printData.shiftType] }}
                    </div>
                    <div class="charge-shift-detail charge-shift-date">
                        日期：{{ printData.shiftDate | parseTime('y-m-d') }}
                    </div>
                    <div class="charge-shift-clear-float"></div>
                </div>
            </div>

            <table class="charge-shift-table charge-shift-no-border">
                <tbody>
                    <!-- 顶部信息栏 -->
                    <tr>
                        <td colspan="7">
                            交班总结：{{ printData.summary }}
                        </td>
                        <td colspan="1">
                            <div class="charge-shift-table-header">
                                <div class="charge-shift-in-hospital-info">
                                    现有：{{ recordInfo.curPatientCount }}
                                </div>
                                <div class="charge-shift-in-hospital-info">
                                    入院：{{ patientEventStat.beHospitalizedCount }}
                                </div>
                                <div class="charge-shift-in-hospital-info">
                                    转入：{{ patientEventStat.transferToHospitalCount }}
                                </div>
                            </div>
                            <div class="charge-shift-table-header">
                                <div class="charge-shift-in-hospital-info">
                                    死亡：{{ patientEventStat.deathCount }}
                                </div>
                                <div class="charge-shift-in-hospital-info">
                                    手术：{{ patientEventStat.operationCount }}
                                </div>
                                <div class="charge-shift-in-hospital-info">
                                    出院：{{ patientEventStat.hospitalizationCount }}
                                </div>
                            </div>
                        </td>
                    </tr>

                    <tr class="charge-shift-no-border">
                        <td class="charge-shift-text-center charge-shift-beds charge-shift-no-border">
                            床号
                        </td>
                        <td class="charge-shift-text-center charge-shift-name charge-shift-no-border">
                            姓名
                        </td>
                        <td class="charge-shift-text-center charge-shift-sex charge-shift-no-border">
                            性别
                        </td>
                        <td class="charge-shift-text-center charge-shift-sex charge-shift-no-border">
                            年龄
                        </td>
                        <td class="charge-shift-text-center charge-shift-diagnosis charge-shift-no-border">
                            诊断
                        </td>
                        <td class="charge-shift-text-center charge-shift-beds charge-shift-no-border">
                            事件
                        </td>
                        <td class="charge-shift-text-center charge-shift-nurse-level charge-shift-no-border">
                            护理等级
                        </td>
                        <td class="charge-shift-text-center charge-shift-no-border">
                            病情情况及注意事项
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <table
            data-type="mix-box"
            class="charge-shift-table"
        >
            <tbody data-type="group">
                <tr
                    v-for="item in items"
                    :key="item.id"
                    data-type="item"
                >
                    <!-- 床号 -->
                    <td class="charge-shift-text-center charge-shift-beds">
                        {{ item.bedNo }}
                    </td>
                    <!-- 姓名 -->
                    <td class="charge-shift-name">
                        {{ item.patient && item.patient.name ? item.patient.name : '' }}
                    </td>
                    <!-- 性别 -->
                    <td class="charge-shift-text-center charge-shift-sex">
                        {{ item.patient && item.patient.sex ? item.patient.sex : '' }}
                    </td>
                    <!-- 年龄 -->
                    <td class="charge-shift-text-center charge-shift-sex">
                        {{ item.patient && item.patient.age && item.patient.age.year ? item.patient.age.year : '' }}
                    </td>
                    <!-- 诊断 -->
                    <td class="charge-shift-diagnosis">
                        {{ item.primaryDiagnosisDiseaseName }}
                    </td>
                    <!-- 事件 -->
                    <td class="charge-shift-text-center charge-shift-beds">
                        {{ (item.patientEventNames || []).join('、') }}
                    </td>
                    <!-- 护理等级 -->
                    <td class="charge-shift-text-center charge-shift-nurse-level">
                        {{ item.nurseLevelName | splitNurseLevel }}
                    </td>
                    <!-- 病情情况及注意事项 -->
                    <td>
                        {{ item.remark }}
                    </td>
                </tr>
            </tbody>
        </table>

        <div
            data-type="footer"
            class="charge-shift-footer-wrapper"
        >
            <div class="charge-shift-footer">
                交班护士：{{ printData.shiftEmployeeName }}
            </div>
            <div class="charge-shift-footer charge-shift-succession-nurse">
                接班护士：{{ printData.takeEmployeeName }}
            </div>
            <div class="charge-shift-footer charge-shift-page-num">
                <span data-page-no="PageNo"></span>
                <span>/</span>
                <span data-page-count="PageCount"></span>
                <span>页</span>
            </div>
            <div class="charge-shift-clear-float"></div>
        </div>
    </div>
</template>

<script>
    import CommonHandler from "./data-handler/common-handler";
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import Clone from "./common/clone";
    import {parseTime} from "./common/utils";
    import {ShiftTypeToText} from "./common/constants";

    export default {
        name: 'ChargeShift',
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.CHARGE_SHIFT,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
        ],
        filters: {
            parseTime,
            splitNurseLevel(text) {
                if (!text) return '';
                return text.slice(0, -2);
            },
        },
        props: {
            renderData: {
                type: Object,
                default: () => ({})
            },
            extra: {
                type: Object,
                default: () => ({})
            },
        },
        data() {
            return {
                ShiftTypeToText,
            };
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            recordInfo() {
                return this.printData.recordInfo || {};
            },
            patientEventStat() {
                return this.recordInfo.patientEventStat || {};
            },
            items() {
                return this.printData.items || [];
            },
        },
        created() {
            console.log('%crenderData\n', 'background: green; padding: 0 5px', Clone(this.renderData));
        },
    }
</script>

<style lang="scss">
.charge-shift-wrapper {
    font-family: SimSun sans-serif;

    .charge-shift-title {
        padding: 10pt 0;
        font-size: 16pt;
        text-align: center;
    }

    .charge-shift-clear-float {
        clear: both;
    }

    .charge-shift-info-wrapper {
        padding-bottom: 4pt;
        font-size: 0;
    }

    .charge-shift-ward {
        display: inline-block;
        width: 50%;
        font-size: 12pt;
        vertical-align: top;
    }

    .charge-shift-detail {
        float: right;
        text-align: right;
    }

    .charge-shift-date {
        margin-right: 57pt;
    }

    .charge-shift-footer-wrapper {
        padding-top: 8px;
    }

    .charge-shift-footer {
        display: inline-block;
    }

    .charge-shift-succession-nurse {
        margin-left: 57pt;
    }

    .charge-shift-page-num {
        float: right;
        text-align: right;
    }

    table,
    tr,
    td {
        border: 1px solid #000000;
    }

    td {
        padding: 0 4pt;
    }

    .charge-shift-no-border {
        border-bottom: none;
    }

    .charge-shift-table {
        width: 100%;
        border-collapse: collapse;
    }

    .charge-shift-table-header {
        font-size: 0;
    }

    .charge-shift-in-hospital-info {
        display: inline-block;
        width: 33%;
        overflow: hidden;
        font-size: 12pt;
        white-space: nowrap;
    }

    .charge-shift-text-center {
        text-align: center;
    }

    .charge-shift-beds {
        width: 32pt;
    }

    .charge-shift-name {
        width: 56pt;
    }

    .charge-shift-sex {
        width: 30pt;
    }

    .charge-shift-nurse-level {
        width: 54pt;
    }

    .charge-shift-diagnosis {
        width: 186pt;
    }
}
</style>