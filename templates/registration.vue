<!--exampleData
{
    patient: {
        id: 'c252b62caccd43ea96a82e8a22b1116b',
        name: '任盈盈',
        mobile: '',
        sex: '女',
        age: {
            year: 10,
            month: 2,
            day: 17,
        },
        isMember: 0,
        birthday: '2018-10-14',
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
    },
    departmentName: '中西医结合科',
    doctorName: '胡青牛',
    orderNo: '10',
    reserveShift: 1,
    reserveDate: '2019-12-11',
    reserveStart: '12:00',
    reserveEnd: '13:00',
    isReserved: 1,
    registrationFee: 100.0,
    consultingRoomName: '05诊室',
    dayOfWeek: '周三',
    created: '2019-12-10',
    signInTime: '2019-12-10',
    createdBy: '令狐冲',
    payStatus: 1,
    patientOrderNo: '00000009',
    chargeTransactions: [{ payMode: 2, payModeName: '微信', amount: 100.0 },{ payMode: 3, payModeName: '支付宝', amount: 100.0 }],
    receivedFee: 100.0,
};
-->

<template>
    <div>
        <registration-template
            :print-data="printData"
            :config="config"
        ></registration-template>
    </div>
</template>

<script>
    import RegistrationTemplate from './components/registration/index.vue'

    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PrintCommonDataHandler from "./data-handler/common-handler.js";

    export default {
        businessKey: PrintBusinessKeyEnum.REGISTRATION,
        DataHandler: PrintCommonDataHandler,
        components: {
            RegistrationTemplate
        },
        pages: [
            {
                paper: PageSizeMap.MM58,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM70_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80_100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_140,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_93_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM148_118_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM200_1397,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM175_94,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_200,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM90_120_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分' // 默认选择的等分纸
            },
        ],
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        computed: {
            printData() {
                return this.renderData.printData;
            },
            config() {
                if(this.renderData.config && this.renderData.config.registration) {
                    return this.renderData.config.registration;
                }
                return {};
            },
        }
    };
</script>
<style lang="scss">
@import "./style/registration/index";
</style>
