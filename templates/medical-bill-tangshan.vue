<template>
    <div class="tangshan-invoice-wrapper">
        <template v-for="(page, pageIndex) in viewPageList">
            <div class="tangshan-page">
                <div class="tangshan-institution-code-wrapper">
                    <div>医疗机构</div>
                    <div>{{ extraInfo.mdtrtareaAdmvs || '' }}{{ getCurrentTime() }}</div>
                    <div>{{ getRandomNumber() }}</div>
                </div>
                
                <div class="tangshan-title">
                    河北省医疗保障定点医疗机构门诊收费票据
                </div>

                <div class="tangshan-institution-wrapper">
                    <div
                        class="tangshan-institution-item"
                        style="left: 0; width: 78mm;"
                    >
                        医药机构：{{ tangshan.institutionName }}
                    </div>
                    <div
                        class="tangshan-institution-item"
                        style="left: 80mm; width: 57mm;"
                    >
                        机构编码：{{ printData.hospitalCode }}
                    </div>
                    <div
                        class="tangshan-institution-item"
                        style="left: 137mm; width: 57mm;"
                    >
                        医疗类别：{{ shebaoPayment.medType }}
                    </div>
                </div>
                
                <div class="tangshan-table">
                    <div class="tangshan-patient-info">
                        <div
                            class="tangshan-institution-item"
                            style="left: 0; width: 46mm;"
                        >
                            姓名：{{ patient.name }}
                        </div>
                        <div
                            class="tangshan-institution-item"
                            style="left: 48mm; width: 30mm;"
                        >
                            性别：{{ patient.sex }}
                        </div>
                        <div
                            class="tangshan-institution-item"
                            style="left: 79.7mm; width: 57mm;"
                        >
                            参保地区：{{ extraInfo.insuplcAdmdvsName }}
                        </div>
                        <div
                            class="tangshan-institution-item"
                            style="left: 136.7mm; width: 57mm;"
                        >
                            人员编号：{{ printData.healthCardId }}
                        </div>
                    </div>
                    
                    <div class="tangshan-charge-item-wrapper">
                        <div
                            v-for="itemsIndex in [0, 1]"
                            class="tangshan-item-wrapper"
                            :style="[itemsIndex ? { 'border-left': '1px solid #000000', width: '49.8%' } : {}, showOnlyOnePageTips ? { height: '114px' } : {}]"
                        >
                            <div class="tangshan-tr">
                                <div class="tangshan-td tangshan-name">
                                    项目/规格
                                </div>
                                <div class="tangshan-td tangshan-count">
                                    数量
                                </div>
                                <div class="tangshan-td tangshan-price">
                                    金额
                                </div>
                                <div
                                    class="tangshan-td tangshan-fee-type"
                                    style="text-align: right;"
                                >
                                    支付类型
                                </div>
                                <div style="clear: both;"></div>
                            </div>

                            <template v-if="page[itemsIndex] && page[itemsIndex].length">
                                <template v-for="item in page[itemsIndex]">
                                    <div class="tangshan-tr">
                                        <div class="tangshan-td tangshan-name">
                                            {{ item.name }}
                                        </div>
                                        <div class="tangshan-td tangshan-count">
                                            {{ item.count }}
                                        </div>
                                        <div class="tangshan-td tangshan-price">
                                            {{ item.discountedPrice | formatMoney }}
                                        </div>
                                        <div
                                            class="tangshan-td tangshan-fee-type"
                                            style="text-align: center;"
                                        >
                                            <template v-if="isShowSocialCode(item)">
                                                {{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}
                                            </template>
                                        </div>
                                        <div style="clear: both;"></div>
                                    </div>

                                    <div class="tangshan-tr">
                                        {{ item.socialCode }}
                                    </div>
                                </template>
                            </template>

                            <div
                                v-if="showOnlyOnePageTips"
                                class="tangshan-tr"
                                style="text-align: center;"
                            >
                                *** 因纸张限制，部分项目未打印 ***
                            </div>
                        </div>
                        
                        <div style="clear: both;"></div>
                    </div>
                    
                    <div class="tangshan-total-price-wrapper">
                        <div class="tangshan-total-price">
                            合计（大写）：{{ digitUppercase(finalFee) }}
                        </div>
                        <div
                            class="tangshan-total-price"
                            style="width: 49%;"
                        >
                            {{ $t('currencySymbol') }}{{ finalFee | formatMoney }}
                        </div>
                    </div>
                    
                    <div class="tangshan-shebao-info">
                        <div
                            v-for="(item, shebaoItemIndex) in shebaoInfoList"
                            class="tangshan-shebao-item"
                            :style="(shebaoItemIndex + 1) % 4 === 0 ? { width: '24%' } : {}"
                        >
                            {{ item.label }}：{{ item.value | formatMoney }}
                        </div>
                    </div>

                    <div class="tangshan-shebao-info">
                        诊断病种：{{ printData.diagnosis }}
                    </div>
                </div>
                
                <div class="tangshan-footer">
                    <div class="tangshan-footer-institution">
                        收款单位（章）：{{ tangshan.institutionName }}
                    </div>
                    <div class="tangshan-charge-time">
                        收款日期：{{ printData.chargedTime | parseTime }}
                    </div>
                    <div style="clear: both;"></div>
                </div>
            </div>

            <!-- 下一页 -->
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import BillDataMixins from "./mixins/bill-data";
    import NationalBillData from "./mixins/national-bill-data";
    import CommonHandler from "./data-handler/common-handler";
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import clone from "./common/clone";
    import {parseTime} from "./common/utils";

    const itemCount = 5;

    export default {
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_TANGSHAN,
        pages: [
            {
                paper: PageSizeMap.MM240_93_TANGSHAN,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        name: 'MedicalBillTangshan',
        mixins: [BillDataMixins, NationalBillData],
        props: {
            extra: {
                type: Object,
                default() {
                    return {};
                }
            }
        },
        computed: {
            tangshan() {
                return this.config.tangshan || {};
            },
            splitType() {
                return this.tangshan.splitType;
            },
            renderPage() {
                return this.spliceFormItems(this.chargeFormItems);
            },
            isOnlyOnePage() {
                return this.splitType === 1 || this.extra.isPreview;
            },
            currentRenderPage() {
                return this.isOnlyOnePage ? this.renderPage.slice(0, 2) : this.renderPage;
            },
            viewPageList() {
                return this.mergePage(this.currentRenderPage);
            },
            showOnlyOnePageTips() {
                return !this.extra.isPreview && this.splitType && this.renderPage.length > 2;
            },
            shebaoInfoList() {
                return [
                    {
                        label: '医保统筹支付',
                        value: this.shebaoPayment.fundPaymentFee,
                    },
                    {
                        label: '个人账户支出',
                        value: this.shebaoPayment.accountPaymentFee,
                    },
                    {
                        label: '现金支付金额',
                        value: this.printData.personalPaymentFee,
                    },
                    {
                        label: '个人账户余额',
                        value: this.shebaoPayment.cardBalance,
                    },
                    {
                        label: '起付标准',
                        value: this.extraInfo.currentDedcStd,
                    },
                    {
                        label: '起付标准累计',
                        value: this.extraInfo.dedcCum,
                    },
                    {
                        label: '基本医疗基金支出',
                        value: this.extraInfo.hifpPay,
                    },
                    {
                        label: '补助/补充基金支出',
                        value: this.extraInfo.hifobPay,
                    },
                    {
                        label: '大病/大额基金支出',
                        value: this.extraInfo.seriInsFund,
                    },
                    {
                        label: '医疗救助基金',
                        value: this.extraInfo.mafPay,
                    },
                    {
                        label: '其他基金',
                        value: this.shebaoPayment.otherPaymentFee,
                    },
                    {
                        label: '统筹累计支付',
                        value: this.extraInfo.beforeDedcCumulative,
                    },
                    {
                        label: '共济账户支付',
                        value: this.extraInfo.acctMulaidPay,
                    },
                ];
            },
        },
        methods: {
            getCurrentTime() {
                const currentTime = parseTime(new Date(), 'y m d');
                return currentTime.split(' ').join('');
            },
            // 生成一个12位长度的随机数
            getRandomNumber() {
                if (this.extra.isPreview) return '000972762751';
                let randomString = '';
                for (let i = 0; i < 12; i++) {
                    randomString += Math.floor(Math.random() * 10).toString();
                }
                return randomString;
            },
            spliceFormItems(formItems) {
                const pageList = [];
                const cacheFormItems = clone(formItems);
                let count = 0;
                let pages = [];
                for (let item of cacheFormItems) {
                    if (item.socialCode) {
                        count += 2;
                    } else {
                        count++;
                    }
                    if (count > itemCount) {
                        pageList.push(pages);
                        pages = [];
                        count = 0;
                        if (item.socialCode) {
                            count += 2;
                        } else {
                            count++;
                        }
                    }
                    pages.push(item);
                }
                if (pages.length) {
                    pageList.push(pages);
                }
                return pageList;
            },
            mergePage(pageList) {
                const res = [];
                for (let i = 0; i < pageList.length; i += 2) {
                    if (i + 1 >= pageList.length) {
                        res.push([pageList[i]]);
                    } else {
                        res.push([pageList[i], pageList[i + 1]]);
                    }
                }
                return res;
            },
        },
    }
</script>

<style lang="scss">
.tangshan-invoice-wrapper {
    height: 100%;
    font-size: 12px;
    line-height: 16px;
    background-color: #ffffff;

    .tangshan-page {
        height: 100%;
    }

    .tangshan-institution-code-wrapper {
        position: absolute;
        top: 6mm;
        right: 23mm;
        font-size: 12px;
        line-height: 14px;
        text-align: center;
    }

    .tangshan-title {
        width: 100%;
        padding-top: 8mm;
        padding-bottom: 2mm;
        font-size: 20px;
        line-height: 28px;
        text-align: center;
    }

    .tangshan-institution-wrapper {
        position: relative;
        width: 194mm;
        height: 16px;
        margin-left: 23mm;
    }

    .tangshan-institution-item {
        position: absolute;
        top: 0;
        display: inline-block;
        width: 33%;
        overflow: hidden;
        white-space: nowrap;
    }

    .tangshan-table {
        width: 194mm;
        margin-left: 23mm;
        border: 1px solid #000000;
    }

    .tangshan-patient-info {
        position: relative;
        width: 100%;
        height: 17px;
        border-bottom: 1px solid #000000;
    }

    .tangshan-charge-item-wrapper {
        width: 100%;
        border-bottom: 1px solid #000000;
    }

    .tangshan-item-wrapper {
        display: inline-block;
        float: left;
        width: 50%;
        height: 98px;
    }

    .tangshan-tr {
        width: 100%;
    }

    .tangshan-td {
        display: inline-block;
        float: left;
        overflow: hidden;
        white-space: nowrap;
        vertical-align: top;
    }

    .tangshan-name {
        width: 55%;
    }

    .tangshan-count {
        width: 15%;
    }

    .tangshan-price {
        width: 15%;
    }

    .tangshan-fee-type {
        width: 15%;
    }

    .tangshan-total-price-wrapper {
        width: 100%;
        border-bottom: 1px solid #000000;
    }

    .tangshan-total-price {
        display: inline-block;
        width: 50%;
        overflow: hidden;
        white-space: nowrap;
        vertical-align: top;
    }

    .tangshan-shebao-info {
        width: 100%;
    }

    .tangshan-shebao-item {
        display: inline-block;
        width: 25%;
        overflow: hidden;
        white-space: nowrap;
        vertical-align: top;
    }

    .tangshan-footer {
        width: 194mm;
        margin-left: 23mm;
    }

    .tangshan-footer-institution {
        float: left;
    }

    .tangshan-charge-time {
        float: right;
    }
}
</style>
