import DataHandler from "./data-handler";
import { Checkbox } from "../common/render-config-component";
import clone from "../common/clone";

export default class PrescriptionHandler extends DataHandler {
    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            return {
                printData: this.originData,
                config: this.globalConfig
            }
        }

        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers() {
        const getHospitalPrescriptionConfigHandler = (config) => {
            // 先判断是否存在西药、输注、中药处方
            const { printData = {} } = this.preProcessData;
            const { prescriptionList = [] } = printData;
            let hasWestern = false, hasInfusion = false, hasChinese = false;
            prescriptionList.forEach((prescription) => {
                if (prescription.western?.length > 0) {
                    hasWestern = true;
                }
                if (prescription.infusion?.length > 0) {
                    hasInfusion = true;
                }
                if (prescription.chinese?.length > 0) {
                    hasChinese = true;
                }
            });

            // 再根据有无对应的处方展示 checkbox
            if (hasWestern) {
                const wsConfig = new Checkbox({
                    label: `西药处方`,
                    value: true,
                    type: 'checkbox',
                });
                config.push(wsConfig);
            }
            if (hasInfusion) {
                const ifConfig = new Checkbox({
                    label: `输液处方`,
                    value: true,
                    type: 'checkbox',
                })
                config.push(ifConfig)
            }
            if (hasChinese) {
                const cnConfig = new Checkbox({
                    label: `中药处方`,
                    value: true,
                    type: 'checkbox',
                })
                config.push(cnConfig)
            }

            return config;
        }

        return [getHospitalPrescriptionConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterFormsHandler = (renderData) => {
            const { printData = {} } = renderData;
            const { prescriptionList = [] } = printData;
            prescriptionList.forEach((prescription) => {
                const westernRenderComponent = new Checkbox({
                    label: `西药处方`,
                    value: true,
                    type: 'checkbox',
                })
                if (!westernRenderComponent.getRenderComponent(this.renderConfig)?.value) {
                    prescription.western = [];
                }

                const infusionRenderComponent = new Checkbox({
                    label: `输液处方`,
                    value: true,
                    type: 'checkbox',
                })
                if (!infusionRenderComponent.getRenderComponent(this.renderConfig)?.value) {
                    prescription.infusion = [];
                }

                const chineseRenderComponent = new Checkbox({
                    label: `中药处方`,
                    value: true,
                    type: 'checkbox',
                })
                if (!chineseRenderComponent.getRenderComponent(this.renderConfig)?.value) {
                    prescription.chinese = [];
                }
            });

            return renderData;
        }
        return [filterFormsHandler];
    }
}
