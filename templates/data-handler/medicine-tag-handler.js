import <PERSON>Handler from "./data-handler";
import Clone from "../common/clone";
import { MEDICINE_USAGE, printCountDataObj } from "../common/constants";
import { MEDICINE_TAG_FREQ } from "../common/western-medicine-config";
import { deepClone, isStandardUsage } from "../common/utils";

/**
 * 计算总份数，频率*天数
 * @param usageInfo {Object} 用法
 * @return {number} 总份数
 */
const calcCountFromFreq = (usageInfo = {}) => {
    const { freq } = usageInfo;
    let { days = 1 } = usageInfo;
    days = Number(days);
    // 如果没有频率，则默认每天1次
    if (!freq) return 1;

    if (MEDICINE_TAG_FREQ[freq]) {
        return MEDICINE_TAG_FREQ[freq] * days;
    }
    if (freq === 'qod') {
        return Math.ceil(days / 2);
    }
    if (freq === 'qw') {
        return Math.ceil(days / 7);
    }
    if (freq === 'biw') {
        return Math.ceil(days / 7) * 2;
    }
    if (/^q\d+h$/.test(freq)) {
        const hour = +freq.replace(/[^0-9]/gi, '');
        if (hour <= 24) {
            return Math.ceil(24 / hour) * days;
        }
        return Math.ceil(days / Math.ceil(hour / 24));
    }
    if (/^q\d+d$/.test(freq)) {
        const daysCount = +freq.replace(/[^0-9]/gi, '');
        return Math.ceil(days / daysCount);
    }
    if (/^q\d+w$/.test(freq)) {
        const weekCount = +freq.replace(/[^0-9]/gi, '');
        const daysCount = weekCount * 7;
        return Math.ceil(days / daysCount);
    }
    if (/^w\d+$/.test(freq)) {
        const weekTimes = +freq.replace(/[^0-9]/gi, '');
        return Math.ceil(days / 7) * weekTimes;
    }

    return 1;
}

export default class MedicineTagHandler extends DataHandler {

    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            const renderData = {
                printData: deepClone(this.originData),
                config: this.globalConfig,
            };

            renderData.printData.forms.sort((a, b) => parseInt(a.bedNo) - parseInt(b.bedNo));

            if (renderData.printData.isManualPreview) {
                return renderData;
            }

            const printCountData = renderData.printData.printCountData || printCountDataObj;
            // 如果不打印中药处方
            if (!printCountData.decoction.isPrint) {
                renderData.printData.forms = renderData.printData.forms.filter((form) => form.sourceFormType !== 6);
            }
            renderData.printData.forms.forEach((form) => {
                if (form.sourceFormType !== 6) {
                    const cacheFormItems = [];
                    form.formItems.forEach((formItem) => {
                        if (!isStandardUsage(formItem)) {
                            cacheFormItems.push(formItem);
                            return;
                        }

                        for (const medicineUsageKey in MEDICINE_USAGE) {
                            if (MEDICINE_USAGE[medicineUsageKey].indexOf(formItem.usageInfo.usage) > -1) {
                                if (printCountData[medicineUsageKey].isPrint) {
                                    cacheFormItems.push(formItem);
                                }
                                break;
                            }
                        }
                    });
                    form.formItems = cacheFormItems;
                }
            });
            renderData.printData.forms = renderData.printData.forms.filter((form) => form.formItems.length);

            return renderData;
        }
        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers(renderData) {
        const getMedicineTagConfigHandler = (config) => {
            const printCountData = renderData.printData.printCountData || printCountDataObj;
            let globalCountId = 1;
            renderData.printData.forms.forEach((form) => {
                // 中药处方
                if (form.sourceFormType === 6) {
                    const countId = globalCountId;
                    globalCountId++;
                    let value = printCountData.decoction.printCopies;
                    if (value === -1) {
                        // 代煎
                        if (form.usageInfo?.usageType === 1) {
                            value = form.usageInfo?.totalProcessCount || 1;
                        } else {
                            value = form.usageInfo?.doseCount || 1;
                        }
                    }
                    const doseCount = form.leftDoseCount ? form.leftDoseCount : (form.usageInfo?.doseCount || 1);
                    const countData = {
                        countId,
                        value,
                        name: `${(form.usageInfo?.specification || '')}${doseCount}剂`,
                    };
                    form.countData = countData;
                    config.push(countData);
                } else {
                    // 西药处方 + 输注处方
                    form.formItems.forEach((formItem, index) => {
                        if (index === 0 || (index > 0 && formItem.groupId !== form.formItems[index - 1].groupId) || (index > 0 && formItem.groupId === form.formItems[index - 1].groupId && formItem.groupId === null)) {
                            const countId = globalCountId;
                            globalCountId++;
                            if (!isStandardUsage(formItem)) {
                                const countData = {
                                    countId,
                                    value: 1,
                                    name: `${formItem.name}`,
                                };
                                formItem.countData = countData;
                                config.push(countData);
                            } else {
                                let medicineUsageKey = 'injections';
                                for (const key in MEDICINE_USAGE) {
                                    if (MEDICINE_USAGE[key].indexOf(formItem.usageInfo?.usage) > -1) {
                                        medicineUsageKey = key;
                                        break;
                                    }
                                }
                                let value = printCountData[medicineUsageKey].printCopies;
                                if (value === -1) {
                                    value = calcCountFromFreq(formItem.usageInfo);
                                }
                                const countData = {
                                    countId,
                                    value,
                                    name: `${formItem.name}`,
                                };
                                formItem.countData = countData;
                                config.push(countData);
                            }
                        } else {
                            formItem.countData = Clone(form.formItems[index - 1].countData);
                        }
                    });
                }
            });
            return config;
        }
        return [getMedicineTagConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterFormHandler = (renderData) => {
            renderData.printData.forms.forEach((form) => {
                if (form.sourceFormType === 6) {
                    // 中药处方
                    const resultValue = this.renderConfig.find((item) => {
                        if (item.countId === form.countData.countId) {
                            item.value = +item.value;
                            return true;
                        }
                        return false;
                    })?.value;
                    if (+resultValue <= 0) {
                        form.countData.value = 1;
                    } else if (+resultValue > 99) {
                        form.countData.value = 99;
                    } else {
                        form.countData.value = +resultValue;
                    }
                } else {
                    form.formItems.forEach((formItem) => {
                        const resultValue = this.renderConfig.find((item) => {
                            if (item.countId === formItem.countData.countId) {
                                item.value = +item.value;
                                return true;
                            }
                            return false;
                        })?.value;
                        if (+resultValue <= 0) {
                            formItem.countData.value = 1;
                        } else if (+resultValue > 99) {
                            formItem.countData.value = 99;
                        } else {
                            formItem.countData.value = +resultValue;
                        }
                    });
                }
            });
            return renderData;
        }
        return [filterFormHandler];
    }
}
