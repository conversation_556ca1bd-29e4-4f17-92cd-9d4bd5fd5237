import { GoodsSubTypeEnum, GoodsTypeEnum } from "../common/constants.js";
import { Checkbox } from "../common/render-config-component";
import { INSPECT_DEVICE_TYPE_TEXT } from '../constant/print-constant';
import DataHandler from "./data-handler";
import { deepClone } from '../common/utils';

// 获取申请单名字
function getApplySheetName({ type, deviceType, extendSpec }) {
    if(+extendSpec === 20){
        return '眼科检查申请单'
    } else if(type === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect) {
        return '检验申请单'
    }else {
        return INSPECT_DEVICE_TYPE_TEXT[deviceType] + '申请单'
    }
}

export default class examinationApplySheetHandler extends DataHandler {
    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            const cloneOriginData = deepClone(this.originData);
            if (Array.isArray(cloneOriginData.rows) && cloneOriginData.rows.length && cloneOriginData.rows[0].bedNo) {
                cloneOriginData.rows.sort((a, b) => parseInt(a.bedNo) - parseInt(b.bedNo));
            }
            return {
                printData: cloneOriginData,
                config: this.globalConfig,
            }
        }
        return [normalHandler];
    }
    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers() {
        const getExaminationConfigHandler = (config) => {
            const applySheetList = this.preProcessData.printData.rows || [];

            applySheetList.forEach( (applySheet) => {
                const applySheetConfig = {
                    id: applySheet.id,
                    groupName: getApplySheetName({
                        type: applySheet.type,
                        deviceType: applySheet.deviceType,
                        extendSpec: applySheet.subType,
                    }),
                    createdDate: applySheet.created,
                    examItems: (applySheet.businessFormItems || []).map(item => {
                        const printExamItem = new Checkbox({
                            label: item.name || '',
                            value: true,
                            type: 'checkbox',
                            id: item.id || '',
                        })

                        return printExamItem;
                    }),
                }
                config.push(applySheetConfig)
            })

            console.log('%cconfig', 'color: blue; font-size: 20px;');
            console.log(config);
            return config;
        }
        return [getExaminationConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterFormsHandler = (renderData) => {
            console.log('%crenderData', 'color: blue; font-size: 20px;');
            console.log(renderData)

            // 过滤申请单中不需要打印的项目
            renderData.printData.rows = renderData.printData.rows.map(r => {
                const applySheetConfig = this.renderConfig.find(c => c.id === r.id);

                r.businessFormItems = (r.businessFormItems || []).map(item => {
                    const printExamItem = applySheetConfig.examItems.find(it => it.id === item.id);
                    item.noNeedPrint = !printExamItem?.value
                    return item;
                })

                return r;
            });

            // 过滤掉不需要打印的申请单
            renderData.printData.rows = renderData.printData.rows.map(r => {
                r.noNeedPrint = r.businessFormItems.every(item => item.noNeedPrint);
                return r;
            })

            console.log('%cupdatedRenderData', 'color: blue; font-size: 20px;');
            console.log(renderData)

            return renderData;
        }
        return [filterFormsHandler];
    }
}

