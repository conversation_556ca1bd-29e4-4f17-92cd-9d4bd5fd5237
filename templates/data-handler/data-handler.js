import {clone, pipe} from "../common/utils";

export default class DataHandler {
    constructor({originData, globalConfig}) {
        this.originData = originData;
        this.globalConfig = globalConfig;

        this.preProcessData = this.getPreProcessData();
        this.preProcessConfig = this.getPreProcessConfig();
        this.renderConfig = this.preProcessConfig;
        this.renderData = this.getRenderData();
    }

    getPreProcessData() {
        return pipe(...this.preProcessHandlers())({});
    }

    getPreProcessConfig() {
        return pipe(...this.preProcessConfigHandlers(this.preProcessData))([]);
    } 

    getRenderData() {
        return pipe(...this.renderDataHandlers())(this.preProcessData);
    }

    getStaticRenderConfig() {
        return clone(this.renderConfig)
    }

    setRenderConfig(renderConfig) {
        this.renderConfig = renderConfig
        this.renderData = this.getRenderData()
    }

    preProcessHandlers() {
        throw Error("preProcessHandlers 未实现");
    }

    preProcessConfigHandlers() {
        throw Error("preProcessConfigHandlers 未实现");
    }

    renderDataHandlers() {
        throw Error("renderDataHandlers 未实现");
    }
}
