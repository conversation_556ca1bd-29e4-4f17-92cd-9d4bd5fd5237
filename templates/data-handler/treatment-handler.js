import DataHandler from "./data-handler";
import {Checkbox} from "../common/render-config-component";
import clone from "../../templates/common/clone";

export default class TreatmentHandler extends DataHandler {
    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            return {
                printData: this.originData,
                config: this.globalConfig
            }
        }

        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers() {
        const getTreatmentConfigHandler = (config) => {
            const executeFormsList = this.preProcessData.printData.executeForms || [];
            const externalForms = this.preProcessData.printData.prescriptionExternalForms || [];

            let noChargeConfig = null;
            let chargedConfig = null;

            executeFormsList.forEach(form => {
                form.executeFormItems.forEach(formItem => {
                    formItem.groupItems.forEach(groupItem => {
                        if(groupItem.chargeStatus && !chargedConfig) {
                            chargedConfig = new Checkbox({
                                label: `已收费项目`,
                                value: true,
                                type: 'checkbox',
                            })
                        }

                        if(!groupItem.chargeStatus && !noChargeConfig) {
                            noChargeConfig = new Checkbox({
                                label: `未收费项目`,
                                value: true,
                                type: 'checkbox',
                            })
                        }
                    })
                })
            })

            externalForms.forEach(form => {
                if(form.chargeStatus && !chargedConfig) {
                    chargedConfig = new Checkbox({
                        label: `已收费项目`,
                        value: true,
                        type: 'checkbox',
                    })
                }

                if(!form.chargeStatus && !noChargeConfig) {
                    noChargeConfig = new Checkbox({
                        label: `未收费项目`,
                        value: true,
                        type: 'checkbox',
                    })
                }
            })
            if(chargedConfig) {
                config.push(chargedConfig)
            }
            if(noChargeConfig) {
                config.push(noChargeConfig)
            }
            return config;
        }

        return [getTreatmentConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterFormsHandler = (renderData) => {
            const executeFormsList = renderData.printData.executeForms || [];
            renderData.printData.treatmentNoChargeForms = clone(executeFormsList).map(form => {
                form.executeFormItems = form.executeFormItems.map(formItem => {
                    formItem.groupItems = formItem.groupItems.filter(groupItem => {
                        if (!groupItem.chargeStatus) {
                            const renderComponent = new Checkbox({
                                label: `未收费项目`,
                                value: true,
                                type: 'checkbox',
                            })
                            return renderComponent.getRenderComponent(this.renderConfig) && renderComponent.getRenderComponent(this.renderConfig).value;
                        }
                    })
                    return formItem
                }).filter(formItem => {
                    return formItem.groupItems.length;
                })
                return form
            })
            renderData.printData.treatmentChargeForms = clone(executeFormsList).map(form => {
                form.executeFormItems = form.executeFormItems.map(formItem => {
                    formItem.groupItems = formItem.groupItems.filter(groupItem => {
                        if (groupItem.chargeStatus) {
                            const renderComponent = new Checkbox({
                                label: `已收费项目`,
                                value: true,
                                type: 'checkbox',
                            })
                            return renderComponent.getRenderComponent(this.renderConfig) && renderComponent.getRenderComponent(this.renderConfig).value;
                        }
                    })
                    return formItem
                }).filter(formItem => {
                    return formItem.groupItems.length;
                })
                return form
            })
            renderData.printData.executeForms = renderData.printData.treatmentNoChargeForms.concat(renderData.printData.treatmentChargeForms)
            renderData.printData.executeForms = renderData.printData.executeForms.filter(form => {
                return form.executeFormItems.length;
            })

            const externalForms = renderData.printData.prescriptionExternalForms || [];
            renderData.printData.externalNoChargeForms = clone(externalForms).filter(form => {
                if (!form.chargeStatus) {
                    const renderComponent = new Checkbox({
                        label: `未收费项目`,
                        value: true,
                        type: 'checkbox',
                    })
                    return renderComponent.getRenderComponent(this.renderConfig) && renderComponent.getRenderComponent(this.renderConfig).value;
                }
            })
            renderData.printData.externalChargeForms = clone(externalForms).filter(form => {
                if (form.chargeStatus) {
                    const renderComponent = new Checkbox({
                        label: `已收费项目`,
                        value: true,
                        type: 'checkbox',
                    })
                    return renderComponent.getRenderComponent(this.renderConfig) && renderComponent.getRenderComponent(this.renderConfig).value;
                }
            })
            renderData.printData.prescriptionExternalForms = renderData.printData.externalNoChargeForms.concat(renderData.printData.externalChargeForms)

            return renderData;
        }
        return [filterFormsHandler];
    }
}

