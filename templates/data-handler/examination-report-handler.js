import DataHandler from "./data-handler";

const PRINT_MODE = {
    merge: 0,
    split: 1,
};

const PRINT_LAYOUT = {
    singleColumn: 1,
    doubleColumn: 2,
}

export default class examinationApplySheetHandler extends DataHandler {
    constructor(arg) {
        super(arg);
    }

    preProcessHandlers() {
        const normalHandler = () => {   
            return {
                printData: this.originData,
                config: this.globalConfig
            }
        }
        return [normalHandler];
    }
    
    // 检验报告打印选项
    preProcessConfigHandlers() {
        const getExaminationConfigHandler = () => {
            const printData = this.preProcessData.printData || {};
            // 所有指标
            const items = printData.itemsValue || [];

            // 根据 groupById 拆分项目
            const projectOptions = items.reduce((res, item) => {
                if(!res.find(r => r.value === item.groupById)) {
                    res.push({
                        label: item.groupBy,
                        value: item.groupById,
                    });
                }
                return res;
            }, [])

            return [
                {
                    type: 'radioGroup',
                    value: PRINT_MODE.merge,
                    options: [
                        {
                            label: '合并打印',
                            value: PRINT_MODE.merge,
                        },
                        {
                            label: '按项目分开打印',
                            value: PRINT_MODE.split,
                        },
                    ],
                    children: [
                        {
                            type: 'checkbox',
                            value: projectOptions.map(item => item.value), // 默认全选所有项目
                            options: projectOptions,
                        }
                    ]
                },
                {
                    type: 'radioGroup',
                    value: PRINT_LAYOUT.doubleColumn,
                    options: [
                        {
                            label: '一页两栏',
                            value: PRINT_LAYOUT.doubleColumn,
                        },
                        {
                            label: '一页一栏',
                            value: PRINT_LAYOUT.singleColumn,
                        },
                    ],
                    children: [
                        {
                            // 每栏指标数
                            type: 'input',
                            value: 17,
                            min: 1,
                            max: 18,
                        }
                    ]
                }
            ];
        }
        return [getExaminationConfigHandler];
    }

    // 选项变化时根据选项生成打印配置
    renderDataHandlers() {
        const filterFormsHandler = (renderData) => {
            const isMerge = this.renderConfig[0].value === PRINT_MODE.merge;
            const isSplit = this.renderConfig[0].value === PRINT_MODE.split;
            const printProjectIdListOnMergeMode = this.renderConfig[0].children[0].value;
            const isSingleColumn = this.renderConfig[1].value === PRINT_LAYOUT.singleColumn;
            const isDoubleColumn = this.renderConfig[1].value === PRINT_LAYOUT.doubleColumn;
            const columnNumber = this.renderConfig[1].children[0].value;

            renderData.printOptions = {
                isMerge,
                isSplit,
                printProjectIdListOnMergeMode,
                isSingleColumn,
                isDoubleColumn,
                columnNumber,
            }

            return renderData;
        }
        return [filterFormsHandler];
    }
}

