import DataHandler from "./data-handler.js"

export default class PrescriptionCirculationHandler extends DataHandler {
    constructor(arg) {
        super(arg)
    }

    // 预处理数据，初始化时调用
    preProcessHandlers() {
        const normalHandler = () => ({
            printData: this.originData,
            config: this.globalConfig
        })
        return [normalHandler]
    }

    // 根据预处理数据得到预览 config
    // 初始化时调用，遍历西药处方 中药处方
    preProcessConfigHandlers() {
        return [config => config]
    }

    // 根据 renderConfig 获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        return [renderData => renderData]
    }
}
