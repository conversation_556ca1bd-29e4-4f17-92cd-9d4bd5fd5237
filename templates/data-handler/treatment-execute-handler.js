import DataHandler from "./data-handler";
import {Checkbox} from "../common/render-config-component.js";
import {toHan} from "../common/utils.js";
import {ChargeStatusPrepEndEnum} from "../common/constants.js";

export default class TreatmentExecuteDataHandler extends DataHandler {

    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            return {
                printData: this.originData,
                config: this.globalConfig
            }
        }
        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers() {
        const getTreatmentExecuteConfigHandler = (config) => {
            // [
            //     {
            //         "type": 3,
            //         "executeFormItems": [
            //             {
            //                 "groupId": null,
            //                 "days": 1,
            //                 "groupItems": [
            //                     {
            //                         "name": "骨伤、颈腰整脊手法",
            //                         "medicineCadn": null,
            //                         "tradeName": "骨伤、颈腰整脊手法",
            //                         "unitCount": 1,
            //                         "unitPrice": 100,
            //                         "totalPrice": 100,
            //                         "unit": "次",
            //                         "composeType": 0,
            //                         "productInfo": {
            //                             "goodsVersion": 0,
            //                             "id": "395d239da474c3690a124a866615e8b8",
            //                             "goodsId": null,
            //                             "status": 1,
            //                             "name": "骨伤、颈腰整脊手法",
            //                             "displayName": "骨伤、颈腰整脊手法",
            //                             "displaySpec": "次",
            //                             "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            //                             "typeId": 22,
            //                             "type": 4,
            //                             "subType": 1,
            //                             "pieceNum": 1,
            //                             "pieceUnit": null,
            //                             "packageUnit": "次",
            //                             "dismounting": 0,
            //                             "medicineCadn": null,
            //                             "extendSpec": null,
            //                             "position": "",
            //                             "piecePrice": null,
            //                             "packagePrice": 100,
            //                             "packageCostPrice": 0,
            //                             "inTaxRat": 0,
            //                             "outTaxRat": 0,
            //                             "stockPieceCount": 0,
            //                             "stockPackageCount": 0,
            //                             "needExecutive": 1,
            //                             "shortId": "4025206948",
            //                             "composeUseDismounting": 0,
            //                             "composeSort": 0,
            //                             "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            //                             "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            //                             "lastModifiedDate": "2018-06-27T01:40:19Z",
            //                             "combineType": 0,
            //                             "bizExtensions": null,
            //                             "medicalFeeGrade": 0,
            //                             "disable": 0,
            //                             "chainDisable": 0,
            //                             "v2DisableStatus": 0,
            //                             "chainV2DisableStatus": 0,
            //                             "disableSell": 0,
            //                             "isSell": 1,
            //                             "customTypeId": 0,
            //                             "chainPackagePrice": 100,
            //                             "chainPackageCostPrice": 0,
            //                             "pieceCount": 0,
            //                             "packageCount": 0,
            //                             "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            //                             "supplier": null,
            //                             "pharmacyNo": 0,
            //                             "defaultInOutTax": 0,
            //                             "cMSpec": null
            //                         },
            //                         "isGift": 0,
            //                         "days": 1,
            //                         "executedCount": 0,
            //                         "remark": "",
            //                         "toothNos": []
            //                     }
            //                 ],
            //                 "composeType": 0,
            //                 "remark": "",
            //                 "created": "2022-03-23T03:51:59Z",
            //                 "totalPrice": 100
            //             },
            //             {
            //                 "groupId": null,
            //                 "days": 1,
            //                 "groupItems": [
            //                     {
            //                         "name": "针灸减肥",
            //                         "medicineCadn": null,
            //                         "tradeName": "针灸减肥",
            //                         "unitCount": 1,
            //                         "unitPrice": 200,
            //                         "totalPrice": 200,
            //                         "unit": "次",
            //                         "composeType": 0,
            //                         "productInfo": {
            //                             "goodsVersion": 0,
            //                             "id": "817821b5635e4cdbbc9ff4c2db2b1ef5",
            //                             "goodsId": null,
            //                             "status": 1,
            //                             "name": "针灸减肥",
            //                             "displayName": "针灸减肥",
            //                             "displaySpec": "次",
            //                             "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            //                             "typeId": 22,
            //                             "type": 4,
            //                             "subType": 1,
            //                             "pieceNum": 1,
            //                             "pieceUnit": null,
            //                             "packageUnit": "次",
            //                             "dismounting": 0,
            //                             "medicineCadn": null,
            //                             "extendSpec": null,
            //                             "position": "",
            //                             "piecePrice": null,
            //                             "packagePrice": 200,
            //                             "packageCostPrice": 0,
            //                             "inTaxRat": 0,
            //                             "outTaxRat": 0,
            //                             "stockPieceCount": 0,
            //                             "stockPackageCount": 0,
            //                             "needExecutive": 1,
            //                             "shortId": "4292712750",
            //                             "composeUseDismounting": 0,
            //                             "composeSort": 0,
            //                             "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            //                             "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            //                             "lastModifiedDate": "2018-06-27T01:41:22Z",
            //                             "combineType": 0,
            //                             "bizExtensions": null,
            //                             "medicalFeeGrade": 0,
            //                             "disable": 0,
            //                             "chainDisable": 0,
            //                             "v2DisableStatus": 0,
            //                             "chainV2DisableStatus": 0,
            //                             "disableSell": 0,
            //                             "isSell": 1,
            //                             "customTypeId": 0,
            //                             "chainPackagePrice": 200,
            //                             "chainPackageCostPrice": 0,
            //                             "pieceCount": 0,
            //                             "packageCount": 0,
            //                             "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            //                             "supplier": null,
            //                             "pharmacyNo": 0,
            //                             "defaultInOutTax": 0,
            //                             "cMSpec": null
            //                         },
            //                         "isGift": 0,
            //                         "days": 1,
            //                         "executedCount": 0,
            //                         "remark": "",
            //                         "toothNos": []
            //                     }
            //                 ],
            //                 "composeType": 0,
            //                 "remark": "",
            //                 "created": "2022-03-23T03:51:59Z",
            //                 "totalPrice": 200
            //             }
            //         ],
            //         "totalPrice": 300
            //     }
            // ]
            const executeForms = this.preProcessData.printData.executeForms;
            const hasChargedItems = executeForms.some(form => {
                const executeFormItems = form.executeFormItems;
                return executeFormItems.some(formItems => {
                    return true;
                })
            })

            if(hasChargedItems) {
                const hasChargedConfig = new Checkbox({
                    label: '已收费项目',
                    value: true,
                    type: 'checkbox',
                })
                config.push(hasChargedConfig);
            }

            return config;
        }
        return [getTreatmentExecuteConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const hasChargedItems = new Checkbox({
            label: '已收费项目',
            value: true,
            type: 'checkbox',
        }).getRenderComponent(this.renderConfig).value;
        console.log(hasChargedItems);
        return [];
    }
}
