import DataHandler from "./data-handler";
import {Checkbox} from "../common/render-config-component";


export default class examinationHandler extends DataHandler {
    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            return {
                printData: this.originData,
                config: this.globalConfig
            }
        }
        return [normalHandler];
    }
    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers() {
        const getExaminationConfigHandler = (config) => {
            const examinationForm = this.preProcessData.printData.examinationFormItems || [];

            examinationForm.forEach( (exItem) => {
                const exConfig = new Checkbox({
                    label: exItem.name || '',
                    value: true,
                    type: 'checkbox',
                    id: exItem.id || '',
                    append: exItem.chargeStatus === 1 ? '已收费' : '未收费',
                    show: exItem.chargeStatus === 1,
                })
                config.push(exConfig)
            })

            return config;
        }
        return [getExaminationConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterFormsHandler = (renderData) => {
            console.log(renderData)
            renderData.printData.examinationFormItems = renderData.printData && renderData.printData.examinationFormItems && renderData.printData.examinationFormItems.filter( (exItem) => {
                const renderComponent = new Checkbox({
                    label: exItem.name || '',
                    value: true,
                    id: exItem.id || '',
                    type: 'checkbox',
                })
                return renderComponent.getRenderComponentEx(this.renderConfig).value;
            })
            console.log(renderData)
            return renderData;
        }
        return [filterFormsHandler];
    }
}

