import DataHandler from "./data-handler";
import { Checkbox } from "../common/render-config-component.js";
import { toHan, toInfusionInjectionName } from "../common/utils.js";
import {ChargeStatusPrepEndEnum} from "../common/constants.js";

export default class InfusionExecuteHandler extends DataHandler {

    constructor(arg) {
        super(arg);
    }

    getFormType() {
        return {
            western: 1,
            infusion: 2,
            treatment: 3,
        }
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            return {
                printData: this.originData,
                config: this.globalConfig
            }
        }
        return [normalHandler];
    }

    formTypeText(form) {
        let str = '';
        switch (form.type) {
        case this.getFormType().western:
            str = '西药处方';
            break;
        case this.getFormType().infusion:
            str = '输液处方';
            break;
        default:
            str = '处方';
            break;
        }
        return str;
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers(renderData) {
        const getExecuteConfigHandler = (config) => {
            const westernForms = this.preProcessData.printData.executeForms.filter( (form) => {
                return form.type === this.getFormType().western;
            })
            const infusionForms = this.preProcessData.printData.executeForms.filter( (form) => {
                return form.type === this.getFormType().infusion;
            })

            // 判断是否拆分输注单
            const infusionSplitByUsage = renderData?.config?.medicalDocuments?.infusion?.content?.infusionSplitByUsage === 1;
            // 判断是否包含外治单
            const includeExternal = renderData?.config?.medicalDocuments?.infusion?.content?.includeExternal === 1;

            westernForms.forEach((form, index) => {
                if (infusionSplitByUsage && form.typeInfo === 'external' && !includeExternal) return;
                const label = infusionSplitByUsage ? `${this.formTypeText(form)}${toHan(form.subIndex + 1)}（${toInfusionInjectionName(form.typeInfo)}）` : `${this.formTypeText(form)}${toHan(index + 1)}` ;
                const tmpConfig = new Checkbox({
                    label,
                    value: true,
                    type: 'checkbox',
                    append: ChargeStatusPrepEndEnum[form.chargeStatus],
                })
                config.push(tmpConfig);
            })

            infusionForms.forEach((form, index) => {
                const label = infusionSplitByUsage ? `${this.formTypeText(form)}${toHan(form.subIndex + 1)}（${toInfusionInjectionName(form.typeInfo)}）` : `${this.formTypeText(form)}${toHan(index + 1)}` ;
                const tmpConfig = new Checkbox({
                    label,
                    value: true,
                    type: 'checkbox',
                    append: ChargeStatusPrepEndEnum[form.chargeStatus],
                })
                config.push(tmpConfig);
            })
            return config;
        }
        return [getExecuteConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterFormHandler = (renderData) => {
            let wsIndex = 0;
            let inIndex = 0;
            // 判断是否拆分输注单
            const infusionSplitByUsage = renderData?.config?.medicalDocuments?.infusion?.content?.infusionSplitByUsage === 1;
            // 判断是否包含外治单
            const includeExternal = renderData?.config?.medicalDocuments?.infusion?.content?.includeExternal === 1;
            renderData.printData.executeForms = renderData.printData.executeForms.filter( (form) => {
                if(form.type === this.getFormType().western ) {
                    if (infusionSplitByUsage && form.typeInfo === 'external' && !includeExternal) return;
                    wsIndex++;
                    const label = infusionSplitByUsage ? `${this.formTypeText(form)}${toHan(form.subIndex + 1)}（${toInfusionInjectionName(form.typeInfo)}）` : `${this.formTypeText(form)}${toHan(wsIndex)}`;
                    const renderComponent = new Checkbox( {
                        label,
                        value: true,
                        type: 'checkbox',
                    })
                    return renderComponent && renderComponent.getRenderComponent(this.renderConfig) && renderComponent.getRenderComponent(this.renderConfig).value;
                }

                if(form.type === this.getFormType().infusion ) {
                    inIndex++;
                    const label = infusionSplitByUsage ? `${this.formTypeText(form)}${toHan(form.subIndex + 1)}（${toInfusionInjectionName(form.typeInfo)}）` : `${this.formTypeText(form)}${toHan(inIndex)}`;
                    const renderComponent = new Checkbox( {
                        label,
                        value: true,
                        type: 'checkbox',
                    })
                    return renderComponent && renderComponent.getRenderComponent(this.renderConfig) && renderComponent.getRenderComponent(this.renderConfig).value;
                }
                return true;
            })
            return renderData;
        }
        return [filterFormHandler];
    }
}
