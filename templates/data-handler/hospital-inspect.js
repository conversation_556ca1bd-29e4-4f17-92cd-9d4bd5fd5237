import DataHandler from './data-handler'

export default class HospitalInspectDataHandler extends DataHandler {

    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            return {
                printData: this.originData,
                config: this.globalConfig
            }
        }
        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers() {
        const generateInspectReportPrintOptions = (config) => {
            const printData = this.preProcessData.printData;
            const reportList = [
                printData.examinationSheetReport,
                ...(printData.additionalExaminationSheetReports || []),
            ];

            if(reportList.length <= 1) {
                return [];
            }

            const numberToText =  [ '一', '二', '三', '四', '五', '六', '七', '八', '九', '十' ];

            const options = reportList.map((report, idx) => ({
                label: '报告' + numberToText[idx],
                key: report.id,
                checked: 1,
            }))

            config.push(...options);

            return config;
        }

        return [ generateInspectReportPrintOptions ];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterNotSelectInspectReport = (renderData) => {
            const printData = renderData.printData;
            const reportList = [
                printData.examinationSheetReport,
                ...(printData.additionalExaminationSheetReports || []),
            ];

            reportList.forEach(report => {
                report.isNeedPrint = this.renderConfig.find(item => item.key === report.id)?.checked;
            })

            return renderData;
        }

        return [ filterNotSelectInspectReport ];
    }
}
