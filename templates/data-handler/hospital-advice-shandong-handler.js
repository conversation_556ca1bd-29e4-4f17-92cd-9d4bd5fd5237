import DataHandler from "./data-handler";
import { deepClone } from '../common/utils';
import { hospitalAdviceShandongPageLimit, MedicalAdviceStatusEnum, MedicalAdviceTypeEnum } from '../common/constants';

const hospitalAdviceShandongInitConfig = {
    'title': '', // 抬头名称 字符串必填
    'subtitle': '', // 副抬头名称 可选值
    'startDoctor': 1, // 起始医生签名, 2:电脑签名 3:手写签名 1:不打印, 默认 1
    'startNurse': 1, // 起始护士签名, 2:电脑签名 3:手写签名 1:不打印, 默认 1
    'stopDoctor': 1, // 停止医生签名, 2:电脑签名 3:手写签名 1:不打印, 默认 1
    'stopNurse': 1, // 停止护士签名, 2:电脑签名 3:手写签名 1:不打印, 默认 1
    'longTimeStopTime': 1, // 长期医嘱单-停止时间, 0:不打印 1:打印, 默认 1
    'oneTimeExecuteTime': 1, // 临时医嘱执行时间, 0:不打印 1:打印, 默认 1
};

export default class HospitalAdviceShandongHandler extends DataHandler {
    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            const { adviceGroups, printPageLastRecords } = this.originData;

            const fullAdviceList = [];
            (adviceGroups || []).forEach((group) => {
                const cacheGroup = deepClone(group);
                const { advices } = cacheGroup;
                delete cacheGroup.advices;
                (advices || []).forEach((advice, adviceIndex) => {
                    // 已撤销的不打印
                    if (advice.status === MedicalAdviceStatusEnum.UNDONE) {
                        return;
                    }
                    const newAdvice = {
                        ...advice,
                        adviceGroupInfo: cacheGroup,
                    };
                    if (advices.length > 1) {
                        if (adviceIndex === 0) {
                            newAdvice.isStart = true;
                        } else if (adviceIndex === advices.length - 1) {
                            newAdvice.isEnd = true;
                        } else {
                            newAdvice.isMiddle = true;
                        }
                    }
                    fullAdviceList.push(newAdvice);
                });
            });

            (printPageLastRecords || []).forEach((page) => {
                const pageAdviceList = [];
                const { adviceGroups } = page;
                (adviceGroups || []).forEach((group) => {
                    const cacheGroup = deepClone(group);
                    const { advices, printedAdviceIds } = cacheGroup;
                    delete cacheGroup.advices;
                    (advices || []).forEach((advice, adviceIndex) => {
                        if (!printedAdviceIds.includes(advice.id)) return;

                        const newAdvice = {
                            ...advice,
                            adviceGroupInfo: cacheGroup,
                        };
                        if (advices.length > 1) {
                            if (adviceIndex === 0) {
                                newAdvice.isStart = true;
                            } else if (adviceIndex === advices.length - 1) {
                                newAdvice.isEnd = true;
                            } else {
                                newAdvice.isMiddle = true;
                            }
                        }
                        pageAdviceList.push(newAdvice);
                    });
                });
                page.pageAdviceList = pageAdviceList;
            });

            return {
                printData: {
                    ...this.originData,
                    fullAdviceList,
                },
                config: this.globalConfig,
            }
        }
        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers(renderData) {
        console.log('%c 处理前的renderData\n', 'background: green; padding: 0 5px', deepClone(renderData));
        const dataHandler = () => {
            const { printData, config: printConfig } = renderData;
            const { isPrintRecord, fullAdviceList, printPageLastRecords, existNeedReprintPage, firstNeedReprintPageIndex, firstNeedReprintPageReason, adviceType, latestPrintRecordType } = printData || {};

            const config = [{
                value: latestPrintRecordType ?? 0,
                list: [],
            }];

            // 渲染打印记录
            if (isPrintRecord) {
                const allPrint = {
                    label: '打印全部医嘱',
                    disabled: false,
                };

                const pages = [];
                (printPageLastRecords || []).forEach((pageRecord) => {
                    const { pageIndex, pageAdviceList, propertyConfig } = pageRecord;
                    pages.push({
                        label: `第${pageIndex + 1}页`,
                        value: true,
                        page: pageIndex,
                        list: pageAdviceList || [],
                        disabled: false,
                        printConfig: propertyConfig || deepClone(hospitalAdviceShandongInitConfig),
                    });
                });
                allPrint.pages = pages;

                config[0].list.push(allPrint);

                return config;
            }

            let disabledPage = 0;
            if (existNeedReprintPage) {
                disabledPage = firstNeedReprintPageIndex;
            }

            // ---- 处理打印全部医嘱 start ----
            const allPrint = {
                label: '打印全部医嘱',
                disabled: false,
            };
            const pages = [];
            let page = [], adviceCount = 0, pageCount = 0;
            (fullAdviceList || []).forEach((advice) => {
                if (adviceCount >= hospitalAdviceShandongPageLimit) {
                    pages.push({
                        label: `第${pageCount + 1}页`,
                        value: true,
                        page: pageCount,
                        list: page,
                        disabled: false,
                        printConfig: printConfig.hospitalMedicalDocuments?.advice?.shandong || deepClone(hospitalAdviceShandongInitConfig),
                    });
                    pageCount++;
                    page = [];
                    adviceCount = 0;
                }
                page.push({
                    ...advice,
                    isAllPrint: true,
                });
                adviceCount++;
            });
            if (page.length) {
                pages.push({
                    label: `第${pageCount + 1}页`,
                    value: true,
                    page: pageCount,
                    list: page,
                    disabled: false,
                    printConfig: printConfig.hospitalMedicalDocuments?.advice?.shandong || deepClone(hospitalAdviceShandongInitConfig),
                });
                pageCount++;
                page = [];
                adviceCount = 0;
            }
            allPrint.pages = pages;
            config[0].list.push(allPrint);
            // ---- 处理打印全部医嘱 end ----

            // ---- 处理续打医嘱 start ----
            const continuePrint = {
                label: '续打',
                disabled: false,
                disabledReasonTitle: '',
                disabledReasonContent: '',
            };

            // if (!printPageLastRecords.length) {
            //     continuePrint.disabled = true;
            // }
            if (existNeedReprintPage) {
                continuePrint.disabled = true;
                continuePrint.disabledReasonTitle = firstNeedReprintPageReason ? '有医嘱撤销' : '有补开医嘱插入';
                continuePrint.disabledPage = disabledPage;
                continuePrint.disabledReasonContent = firstNeedReprintPageReason ?
                    `<div>第 <span style="font-weight: bold;">${disabledPage + 1}</span> 页有医嘱撤销导致后续医嘱顺序变动，<br />为确保医嘱完整一致，请重新打印第 <span style="font-weight: bold;">${disabledPage + 1}</span> 页以及之后的全部医嘱。</div>` :
                    `<div>第 <span style="font-weight: bold;">${disabledPage + 1}</span> 页有补开医嘱插入导致后续医嘱顺序变动，<br />为确保医嘱完整一致，请重新打印第 <span style="font-weight: bold;">${disabledPage + 1}</span> 页以及之后的全部医嘱。</div>`;
                // 不能进入续打，默认选中切换到全部打印
                config.value = 0;
            } else {
                const continuePages = [];
                printPageLastRecords.forEach((page, pageRecordIndex) => {
                    const { pageAdviceList, printPropertyConfig } = page;

                    let isPageUpdated = false;
                    pageAdviceList.forEach((oldAdvice) => {
                        const newAdvice = fullAdviceList.find((advice) => advice.id === oldAdvice.id);
                        if (newAdvice) {
                            if (adviceType === MedicalAdviceTypeEnum.LONG_TIME) {
                                if (printPropertyConfig.startNurse !== 1 && oldAdvice.status < MedicalAdviceStatusEnum.CHECKED && newAdvice.status >= MedicalAdviceStatusEnum.CHECKED) {
                                    oldAdvice.adviceGroupInfo.newCheckedOperatorName = newAdvice.adviceGroupInfo.checkedOperatorName;
                                    oldAdvice.adviceGroupInfo.newCheckedOperatorHandSign = newAdvice.adviceGroupInfo.checkedOperatorHandSign;
                                    isPageUpdated = true;
                                }
                                if (oldAdvice.status < MedicalAdviceStatusEnum.STOPPED && newAdvice.status >= MedicalAdviceStatusEnum.STOPPED) {
                                    if (printPropertyConfig.longTimeStopTime !== 0) {
                                        oldAdvice.adviceGroupInfo.newStopTime = newAdvice.adviceGroupInfo.stopTime;
                                        isPageUpdated = true;
                                    }
                                    if (printPropertyConfig.stopDoctor !== 1) {
                                        oldAdvice.adviceGroupInfo.newStopDoctorName = newAdvice.adviceGroupInfo.stopDoctorName;
                                        oldAdvice.adviceGroupInfo.newStopDoctorHandSign = newAdvice.adviceGroupInfo.stopDoctorHandSign;
                                        isPageUpdated = true;
                                    }
                                }
                                if (printPropertyConfig.stopNurse !== 1 && !oldAdvice.adviceGroupInfo.stopConfirmOperatorName && newAdvice.adviceGroupInfo.stopConfirmOperatorName) {
                                    oldAdvice.adviceGroupInfo.newStopConfirmOperatorName = newAdvice.adviceGroupInfo.stopConfirmOperatorName;
                                    oldAdvice.adviceGroupInfo.newStopConfirmOperatorHandSign = newAdvice.adviceGroupInfo.stopConfirmOperatorHandSign;
                                    isPageUpdated = true;
                                }
                            } else {
                                if (oldAdvice.status < MedicalAdviceStatusEnum.EXECUTED && newAdvice.status >= MedicalAdviceStatusEnum.EXECUTED) {
                                    if (printPropertyConfig.oneTimeExecuteTime !== 0) {
                                        oldAdvice.adviceGroupInfo.newExecuteTime = newAdvice.adviceGroupInfo.executeTime;
                                        isPageUpdated = true;
                                    }
                                    if (printPropertyConfig.executeNurse !== 1) {
                                        oldAdvice.adviceGroupInfo.newExecuteOperatorName = newAdvice.adviceGroupInfo.executeOperatorName;
                                        oldAdvice.adviceGroupInfo.newExecuteOperatorHandSign = newAdvice.adviceGroupInfo.executeOperatorHandSign;
                                        isPageUpdated = true;
                                    }
                                }
                            }
                            oldAdvice.status = newAdvice.status;
                        }
                        oldAdvice.isContinueAdvice = true;
                    });

                    // 最后一页不做处理，之后再单独处理
                    if (pageRecordIndex < printPageLastRecords.length - 1) {
                        const pageAdviceLength = pageAdviceList.length;
                        if (pageAdviceLength < hospitalAdviceShandongPageLimit) {
                            const lastAdvice = pageAdviceList[pageAdviceLength - 1];
                            let newLastAdviceIndex = fullAdviceList.findIndex((advice) => advice.id === lastAdvice.id);
                            if (newLastAdviceIndex > -1) {
                                while (pageAdviceList.length < hospitalAdviceShandongPageLimit && (newLastAdviceIndex + 1) < fullAdviceList.length) {
                                    isPageUpdated = true;
                                    pageAdviceList.push({
                                        ...fullAdviceList[++newLastAdviceIndex],
                                        isAdviceNew: true,
                                    });
                                }
                            }
                        }
                    }

                    continuePages.push({
                        label: `第${page.pageIndex + 1}页`,
                        value: isPageUpdated,
                        page: page.pageIndex,
                        list: pageAdviceList,
                        isPageUpdated,
                        disabled: !isPageUpdated,
                        printConfig: printPropertyConfig,
                    });
                });

                const lastContinuePage = continuePages.length ? continuePages[continuePages.length - 1] : null;
                let currentContinuePageIndex = lastContinuePage ? lastContinuePage.page + 1 : 0;
                const lastPageLastAdvice = lastContinuePage ? lastContinuePage.list[lastContinuePage.list.length - 1] : null;
                let lastPageLastAdviceIndex = lastPageLastAdvice ? fullAdviceList.findIndex((advice) => advice.id === lastPageLastAdvice.id) : 0;
                // 如果新开了医嘱
                if (lastPageLastAdviceIndex > -1 && (lastPageLastAdviceIndex < fullAdviceList.length - 1 || !lastContinuePage)) {
                    if (lastPageLastAdvice) {
                        lastPageLastAdviceIndex++;
                    }
                    // 插入最后一页
                    if (lastContinuePage && lastContinuePage.list.length < hospitalAdviceShandongPageLimit) {
                        for (let i = lastContinuePage.list.length; i < hospitalAdviceShandongPageLimit && lastPageLastAdviceIndex < fullAdviceList.length; i++) {
                            lastContinuePage.list.push({
                                ...fullAdviceList[lastPageLastAdviceIndex++],
                                isAdviceNew: true,
                            })
                        }
                        lastContinuePage.isPageUpdated = true;
                        lastContinuePage.disabled = false;
                        lastContinuePage.value = true;
                    }
                    // 新起页
                    if (lastPageLastAdviceIndex < fullAdviceList.length) {
                        let newPage = [];
                        let adviceCount = 0;
                        for (let i = lastPageLastAdviceIndex; i < fullAdviceList.length; i++) {
                            newPage.push({
                                ...fullAdviceList[i],
                                isAdviceNew: true,
                            });
                            adviceCount++;

                            if (adviceCount >= hospitalAdviceShandongPageLimit) {
                                continuePages.push({
                                    label: `第${currentContinuePageIndex + 1}页`,
                                    value: true,
                                    page: currentContinuePageIndex,
                                    list: newPage,
                                    isPageNew: true,
                                    disabled: false,
                                    printConfig: printConfig.hospitalMedicalDocuments?.advice?.shandong || deepClone(hospitalAdviceShandongInitConfig),
                                });
                                newPage = [];
                                currentContinuePageIndex++;
                                adviceCount = 0;
                            }
                        }
                        if (newPage.length) {
                            continuePages.push({
                                label: `第${currentContinuePageIndex + 1}页`,
                                value: true,
                                page: currentContinuePageIndex,
                                list: newPage,
                                isPageNew: true,
                                disabled: false,
                                printConfig: printConfig.hospitalMedicalDocuments?.advice?.shandong || deepClone(hospitalAdviceShandongInitConfig),
                            });
                            newPage = [];
                            currentContinuePageIndex++;
                            adviceCount = 0;
                        }
                    }
                }

                // 续打默认选中有更新的页码，都没有更新的话选中第一页
                let continuePageValue = false;
                continuePages.forEach((continuePage) => {
                    if (continuePage.value) {
                        if (continuePageValue) {
                            continuePage.value = false;
                        } else {
                            continuePageValue = true;
                        }
                    }
                });
                if (!continuePageValue) {
                    continuePages[0].value = true;
                }

                continuePrint.pages = continuePages;
            }

            config[0].list.push(continuePrint);

            // 如果上次打印的方式是续打，同时此次续打是 disabled，那么默认打印方式修改为全部打印
            if (continuePrint.disabled && config[0].value) {
                config[0].value = 0;
            }
            // ---- 处理续打医嘱 end ----

            return config;
        };

        return [dataHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const dataHandler = (renderData) => {
            console.log('%c renderDataHandlers\n', 'background: green; padding: 0 5px', deepClone(renderData));
            console.log('%c renderConfig\n', 'background: green; padding: 0 5px', deepClone(this.renderConfig));

            const config = this.renderConfig[0];
            const printMethodConfig = config.list[config.value];
            const { pages } = printMethodConfig;
            const printPages = pages.filter((it) => it.value);

            return {
                printData: renderData.printData,
                printPages,
            };
        }
        return [dataHandler];
    }
}
