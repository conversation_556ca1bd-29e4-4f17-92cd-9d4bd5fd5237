import DataHandler from "./data-handler";
import { Checkbox } from "../common/render-config-component";

export default class PrescriptionHandler extends DataHandler {
    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            return {
                printData: this.originData,
                config: this.globalConfig,
            }
        }

        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    // 遍历西药处方 输注处方  中药处方 外治处方
    preProcessConfigHandlers() {
        const getPrescriptionConfigHandler = (config) => {
            const exForms = this.preProcessData.printData && this.preProcessData.printData.rows;
            const { id = '' } = this.preProcessData.printData
            console.log(id)
            exForms.forEach( (exItem) => {
                const exConfig = new Checkbox({
                    label: exItem.examinationName || '',
                    value: true,
                    type: 'checkbox',
                    id: exItem.id || '',
                    // append: (exItem.chargeFormItemStatus === undefined || exItem.chargeFormItemStatus === null) ?
                    //     '' : (exItem.chargeFormItemStatus === 1 ? '已收费' : '未收费'),
                    append: exItem.chargeFormItemStatusName,
                })
                config.push(exConfig)
            })

            return config;
        }

        return [getPrescriptionConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterFormsHandler = (renderData) => {

            renderData.printData = renderData.printData && renderData.printData.rows && renderData.printData.rows.filter( (exItem) => {
                const renderComponent = new Checkbox({
                    label: exItem.examinationName || '',
                    value: true,
                    id: exItem.id || '',
                    type: 'checkbox',
                })
                return renderComponent.getRenderComponentEx(this.renderConfig).value;
            })
            return renderData;
        }
        return [filterFormsHandler];
    }
}
