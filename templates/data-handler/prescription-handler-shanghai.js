import DataHandler from "./data-handler";
import { Checkbox } from "../common/render-config-component";
import { toHan } from "../common/utils.js";
import {ChargeStatusPrepEndEnum} from "../common/constants.js";
import clone from "../common/clone";

export default class PrescriptionHandlerShanghai extends DataHandler {
    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            if (this.originData.prescriptionExternalForms?.length > 1) {
                const cacheExternalForms = clone(this.originData.prescriptionExternalForms);
                const firstExternalForm = clone(cacheExternalForms[0]);
                firstExternalForm.prescriptionFormItems = [];
                firstExternalForm.displayTotalPrice = 0;
                for (let i = 0; i < cacheExternalForms.length; i++) {
                    const tempExternalForm = cacheExternalForms[i];
                    firstExternalForm.displayTotalPrice += tempExternalForm.displayTotalPrice;
                    const resFormItems = [];
                    tempExternalForm.prescriptionFormItems.forEach((item) => {
                        item.usageType = tempExternalForm.usageType;
                        item.usageSubType = tempExternalForm.usageSubType;
                        resFormItems.push(item);
                    })
                    firstExternalForm.prescriptionFormItems = firstExternalForm.prescriptionFormItems.concat(resFormItems);
                }
                this.originData.prescriptionExternalForms = [firstExternalForm];
            }
            return {
                printData: this.originData,
                config: this.globalConfig
            }
        }

        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    // 遍历西药处方 输注处方  中药处方 外治处方
    preProcessConfigHandlers() {
        const getPrescriptionConfigHandler = (config) => {
            const westernForms = this.preProcessData.printData.prescriptionWesternForms || [];
            westernForms.forEach( (form, index) => {
                const wsConfig = new Checkbox({
                    label: `西药处方${toHan(index + 1)}`,
                    value: true,
                    type: 'checkbox',
                    append: this.preProcessData.printData.isNeedRenderConfigItemAppend ? ChargeStatusPrepEndEnum[form.chargeStatus] : '',
                })
                config.push(wsConfig)
            })
            const infusionForms = this.preProcessData.printData.prescriptionInfusionForms || [];
            infusionForms.forEach( (form, index) => {
                const wsConfig = new Checkbox({
                    label: `输液处方${toHan(index + 1)}`,
                    value: true,
                    type: 'checkbox',
                    append: this.preProcessData.printData.isNeedRenderConfigItemAppend ? ChargeStatusPrepEndEnum[form.chargeStatus] : '',
                })
                config.push(wsConfig)
            })
            const chineseForms = this.preProcessData.printData.prescriptionChineseForms || [];
            chineseForms.forEach( (form, index) => {
                const wsConfig = new Checkbox({
                    label: `中药处方${toHan(index + 1)}`,
                    value: true,
                    type: 'checkbox',
                    append: this.preProcessData.printData.isNeedRenderConfigItemAppend ? ChargeStatusPrepEndEnum[form.chargeStatus] : '',
                })
                config.push(wsConfig)
            })
            const externalForms = this.preProcessData.printData.prescriptionExternalForms || [];

            externalForms.forEach( (form) => {
                const wsConfig = new Checkbox({
                    label:  '外治处方',
                    value: true,
                    type: 'checkbox',
                    append: this.preProcessData.printData.isNeedRenderConfigItemAppend ? ChargeStatusPrepEndEnum[form.chargeStatus] : '',
                })
                config.push(wsConfig)
            })
            const glassesForms = this.preProcessData.printData.prescriptionGlassesForms || [];
            glassesForms.forEach(() => {
                const wsConfig = new Checkbox({
                    label: `配镜处方`,
                    value: true,
                    type: 'checkbox',
                    append: '',
                })
                config.push(wsConfig);
            });

            return config;
        }

        return [getPrescriptionConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterFormsHandler = (renderData) => {
            renderData.printData.prescriptionWesternForms = renderData.printData.prescriptionWesternForms && renderData.printData.prescriptionWesternForms.filter( (form, index) => {
                const renderComponent = new Checkbox({
                    label: `西药处方${toHan(index + 1)}`,
                    value: true,
                    type: 'checkbox',
                })
                return renderComponent.getRenderComponent(this.renderConfig).value;
            })
            renderData.printData.prescriptionInfusionForms = renderData.printData.prescriptionInfusionForms && renderData.printData.prescriptionInfusionForms.filter( (form, index) => {
                const renderComponent = new Checkbox({
                    label: `输液处方${toHan(index + 1)}`,
                    value: true,
                    type: 'checkbox',
                })
                return renderComponent.getRenderComponent(this.renderConfig).value;
            })
            renderData.printData.prescriptionChineseForms = renderData.printData.prescriptionChineseForms && renderData.printData.prescriptionChineseForms.filter( (form, index) => {
                const renderComponent = new Checkbox({
                    label: `中药处方${toHan(index + 1)}`,
                    value: true,
                    type: 'checkbox',
                })
                return renderComponent.getRenderComponent(this.renderConfig).value;
            })

            renderData.printData.prescriptionExternalForms = renderData.printData.prescriptionExternalForms && renderData.printData.prescriptionExternalForms.filter( (form, index) => {
                const renderComponent = new Checkbox({
                    label:  '外治处方',
                    value: true,
                    type: 'checkbox',
                })
                return renderComponent.getRenderComponent(this.renderConfig).value;
            })
            renderData.printData.prescriptionGlassesForms = renderData.printData.prescriptionGlassesForms && renderData.printData.prescriptionGlassesForms.filter(() => {
                const renderComponent = new Checkbox({
                    label: '配镜处方',
                    value: true,
                    type: 'checkbox',
                })
                return renderComponent.getRenderComponent(this.renderConfig).value;
            });
            return renderData;
        }
        return [filterFormsHandler];
    }
}
