import DataHandler from "./data-handler";
import {extractBatchNo} from '../common/dispensing';
import {clone} from '../common/utils';

export default class DispensingHandler extends DataHandler {

    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            const cacheOriginData = clone(this.originData);
            const { dispensingSheets, dispensingForms } = cacheOriginData;
            if (Array.isArray(dispensingSheets)) {
                dispensingSheets.forEach((dispensingSheet) => {
                    dispensingSheet.dispensingForms = extractBatchNo(dispensingSheet.dispensingForms);
                });
            } else if (Array.isArray(dispensingForms)) {
                cacheOriginData.dispensingForms = extractBatchNo(dispensingForms);
            }
            return {
                printData: cacheOriginData,
                config: this.globalConfig
            }
        }
        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers() {
        return [];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        return [];
    }
}
