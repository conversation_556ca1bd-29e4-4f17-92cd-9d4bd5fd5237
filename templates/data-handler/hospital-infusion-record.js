import DataHandler from "./data-handler";
import { Checkbox } from "../common/render-config-component";

export default class HospitalInfusionRecord extends DataHandler {
    constructor(arg) {
        super(arg);
    }

    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            const { tableData } = this.originData;
            // 按照patientId分组
            const patientIdGroups = [];
            tableData.forEach((item) => {
                const { patientId, bedNo, patientOrderHospital, timeGroupViews } = item;
                const patientIdGroup = patientIdGroups.find((group) => group.patientId === patientId);
                if (patientIdGroup) {
                    patientIdGroup.data.push(item);
                } else {
                    patientIdGroups.push({
                        patientId,
                        patient: patientOrderHospital.patient,
                        patientName: item.patientName,
                        departmentName: patientOrderHospital.departmentName,
                        wardName: patientOrderHospital.wardName,
                        no: patientOrderHospital.no,
                        bedNo: parseInt(bedNo),
                        planExecuteTime: timeGroupViews[0].planExecuteTime,
                        data: [item],
                    });
                }
            });

            // 按照床位从小到大排序
            patientIdGroups.sort((a, b) => a.bedNo - b.bedNo);

            return {
                printData: {
                    ...this.originData,
                    tableData: patientIdGroups,
                },
                config: this.globalConfig,
            }
        }

        return [normalHandler];
    }

    // 根据预处理数据得到预览config
    preProcessConfigHandlers() {
        const getHospitalInfusionRecordfConfigHandler = () => {
            return this.preProcessData.printData.tableData.map((group) => {
                return new Checkbox({
                    label: `${group.patientName}`,
                    value: true,
                    id: group.patientId,
                    type: 'checkbox',
                });
            })
        }

        return [getHospitalInfusionRecordfConfigHandler];
    }

    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        const filterFormsHandler = (renderData) => {
            const { tableData } = renderData.printData;
            // 过滤掉不需要渲染的数据
            const filterTableData = tableData.filter((group) => {
                const renderComponent = new Checkbox({
                    label: `${group.patientName}`,
                    value: true,
                    id: group.patientId,
                    type: 'checkbox',
                });
                return renderComponent.getRenderComponentEx(this.renderConfig).value;
            });

            return {
                ...renderData,
                printData: {
                    ...renderData.printData,
                    tableData: filterTableData,
                },
            }
        }
        return [filterFormsHandler];
    }
}
