import DataHandler from "./data-handler.js";

export default class PrintHandler extends DataHandler {
    constructor(arg) {
        super(arg);
    }
    
    // 预处理数据
    // 初始化时调用
    preProcessHandlers() {
        const normalHandler = () => {
            return {
                printData: this.originData,
                config: this.globalConfig
            }
        }
        
        return [normalHandler];
    }
    
    // 根据预处理数据得到预览config
    // 初始化时调用
    preProcessConfigHandlers() {
        return [];
    }
    
    // 根据renderConfig获取实时渲染数据
    // 初始化或者更新时调用
    renderDataHandlers() {
        return [];
    }
}

