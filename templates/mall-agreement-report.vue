<!--exampleData
{}
-->

<template>
    <div></div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";

    export default {
        name: "MallAgreementReport",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MALL_AGREEMENT_REPORT,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM114_127,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分', // 默认选择的等分纸
            }
        ],
    }
</script>
<style lang="scss">
@import "./style/medical-fee-list/national/index";
.abc-page_preview {
  background: #fff;
  color: #000;
}
</style>
