<template>
    <div>
        <template v-for="(form, formIndex) in forms">
            <template v-if="form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE">
                <div
                    data-type="header"
                    :data-count="form.countData.value"
                    :data-prescription-group="formIndex"
                    :data-pendants-index="`${formIndex}normalGroup`"
                    class="medicine-tag-header medicine-tag-header-patient-container"
                >
                    <div class="hospital-medicine-tag-patient-info-wrapper">
                        <div class="font-bold">
                            {{ form.patient.name }}
                        </div>
                        <div>
                            {{ form.patient.sex }}
                        </div>
                        <div>
                            {{ formatAge(form.patient.age, {monthYear: 12, dayYear: 1}) }}
                        </div>
                        <div
                            class="medicine-tag-hospital-ward"
                            :style="{ visibility: config.ward ? 'visible' : 'hidden' }"
                        >
                            {{ form.wardName }}
                        </div>
                        <div
                            v-if="config.bedNo"
                            class="font-bold"
                        >
                            {{ form.bedNo | hospitalBedFormat }}床
                        </div>
                    </div>
                    <div
                        v-if="!isSettingPreview"
                        class="count-data-container"
                    >
                        <span style="line-height: 18px;">{{ NUMBER_ICONS[form.countData.countId] }}</span>
                        <span>&nbsp;× {{ form.countData.value }}份</span>
                    </div>
                </div>
                <div class="medicine-tag-chinese-content">
                    <div
                        v-if="form.usageInfo"
                        class="medicine-tag-chinese-usage"
                        overflow
                    >
                        {{ form.usageInfo.specification }} 共{{ getDoseCount(form) }}剂
                        <template v-if="form.usageInfo.totalProcessCount">
                            共{{ form.usageInfo.totalProcessCount }}袋
                        </template>
                    </div>
                    <div
                        v-if="form.usageInfo"
                        class="medicine-tag-chinese-usage"
                        overflow
                    >
                        <span class="item-label">用法：</span><span class="font-bold item-usage">{{
                            form.usageInfo.usage
                        }} {{ form.usageInfo.dailyDosage }} {{ form.usageInfo.freq }}</span>
                    </div>
                    <div
                        v-if="form.usageInfo"
                        class="medicine-tag-chinese-usage"
                        overflow
                    >
                        <span class="item-label">用量：</span><span class="font-bold item-usage">{{ form.usageInfo.usageLevel }}</span>
                    </div>
                    <div
                        class="medicine-tag-chinese-usage"
                        overflow
                    >
                        备注：{{ form.usageInfo && form.usageInfo.requirement }}
                    </div>
                </div>
                <div

                    class="medicine-tag-footer"
                    data-type="footer"
                    :data-pendants-index="`${formIndex}normalGroup`"
                >
                    <div
                        v-if="config.chineseRemark"
                        class="medicine-tag-footer-time medicine-tag-hospital-chinese-remark"
                    >
                        {{ config.chineseRemark }}
                    </div>
                    <div class="medicine-tag-footer-time">
                        <template v-if="config.printDateType">
                            {{ form.planExecuteTime | parseTime('y-m-d') }}
                        </template>
                        <template v-else>
                            {{ new Date() | parseTime('y-m-d h:i:s') }}
                        </template>
                    </div>
                </div>
            </template>

            <template v-else>
                <template v-if="getWesternMedicine(form).groupItems.length">
                    <template v-for="(formItems, formItemIndex) in getWesternMedicine(form).groupItems">
                        <div
                            data-type="header"
                            :data-count="formItems[0].countData.value"
                            :data-prescription-group="formIndex"
                            :data-pendants-index="`${formIndex}${formItemIndex}hasGroupId`"
                            class="medicine-tag-header medicine-tag-header-patient-container medicine-tag-header-group"
                        >
                            <div class="hospital-medicine-tag-patient-info-wrapper">
                                <div>
                                    {{ NUMBER_ICONS[formItems[0].groupId] }}
                                </div>
                                <div class="font-bold">
                                    {{ form.patient.name }}
                                </div>
                                <div>
                                    {{ form.patient.sex }}
                                </div>
                                <div>
                                    {{ formatAge(form.patient.age, {monthYear: 12, dayYear: 1}) }}
                                </div>
                                <div
                                    class="medicine-tag-hospital-ward"
                                    :style="{ visibility: config.ward ? 'visible' : 'hidden' }"
                                >
                                    {{ form.wardName }}
                                </div>
                                <div
                                    v-if="config.bedNo"
                                    class="font-bold"
                                >
                                    {{ form.bedNo | hospitalBedFormat }}床
                                </div>
                            </div>
                            <div
                                v-if="!isSettingPreview"
                                class="count-data-container"
                            >
                                <span style="line-height: 18px;">{{ NUMBER_ICONS[formItems[0].countData.countId] }}</span>
                                <span>&nbsp;× {{ formItems[0].countData.value }}份</span>
                            </div>
                        </div>

                        <div data-type="mix-box">
                            <div
                                data-type="group"
                                class="medicine-tag-western-content medicine-tag-content-group"
                            >
                                <template v-for="items in splitGroupItems(formItems)">
                                    <print-row
                                        v-for="item in items"
                                        data-type="item"
                                        class="western-group-item medicine-tag-item-group"
                                    >
                                        <print-col
                                            :span="17"
                                            overflow
                                            class="western-group-name"
                                        >
                                            {{ item.name }}
                                        </print-col>
                                        <print-col
                                            v-if="item.usageInfo"
                                            :span="7"
                                            overflow
                                            class="text-right western-group-dosage"
                                        >
                                            {{ item.usageInfo.dosage }}{{ item.usageInfo.dosageUnit }}/次
                                        </print-col>
                                        <print-col
                                            :span="24"
                                            class="western-group-spec"
                                            overflow
                                        >
                                            {{ hospitalGoodsSpec(item.productInfo) }}
                                            <template v-if="config.westernTotalAmount && item.unitCount && item.unit">
                                                ×{{ item.unitCount }}{{ item.unit }}
                                            </template>
                                            <template v-if="config.westernSkinTest && item.ast === AstEnum.PI_SHI">
                                                {{ item.astResult | filterSkinTest }}
                                            </template>
                                        </print-col>
                                    </print-row>
                                </template>
                            </div>
                        </div>
                        <div
                            v-if="formItems[0].usageInfo"
                            class="medicine-tag-group-footer"
                            data-type="footer"
                            :data-pendants-index="`${formIndex}${formItemIndex}hasGroupId`"
                        >
                            <div class="western-group-usage-footer font-bold">
                                {{ formItems[0].usageInfo.usage }}
                                <template v-if="formItems[0].usageInfo.freq !== 'st'">
                                    {{ freqFormat(formItems[0].usageInfo.freq) }}
                                </template>
                                <template v-if="config.westernDays && formItems[0].usageInfo.freq !== 'st' && formItems[0].usageInfo.days">
                                    {{ formItems[0].usageInfo.days }}天
                                </template>
                                {{ formItems[0].usageInfo.ivgtt || '' }}{{ formItems[0].usageInfo.ivgttUnit || '' }}
                            </div>
                            <div class="western-group-usage-footer">
                                <template v-if="config.printDateType">
                                    {{ form.planExecuteTime | parseTime('y-m-d') }}
                                </template>
                                <template v-else>
                                    {{ new Date() | parseTime('y-m-d h:i:s') }}
                                </template>
                            </div>
                        </div>
                    </template>
                </template>


                <template v-if="getWesternMedicine(form).formItems.length">
                    <template v-for="(item, itemIndex) in getWesternMedicine(form).formItems">
                        <div
                            data-type="header"
                            :data-count="item.countData.value"
                            :data-prescription-group="formIndex"
                            :data-pendants-index="`${formIndex}${itemIndex}noGroupId`"
                            class="medicine-tag-header medicine-tag-header-patient-container"
                        >
                            <div class="hospital-medicine-tag-patient-info-wrapper">
                                <div class="font-bold">
                                    {{ form.patient.name }}
                                </div>
                                <div>
                                    {{ form.patient.sex }}
                                </div>
                                <div>
                                    {{ formatAge(form.patient.age, {monthYear: 12, dayYear: 1}) }}
                                </div>
                                <div
                                    class="medicine-tag-hospital-ward"
                                    :style="{ visibility: config.ward ? 'visible' : 'hidden' }"
                                >
                                    {{ form.wardName }}
                                </div>
                                <div
                                    v-if="config.bedNo"
                                    class="font-bold"
                                >
                                    {{ form.bedNo | hospitalBedFormat }}床
                                </div>
                            </div>
                            <div
                                v-if="!isSettingPreview"
                                class="count-data-container"
                            >
                                <span style="line-height: 18px;">{{ NUMBER_ICONS[item.countData.countId] }}</span>
                                <span>&nbsp;× {{ item.countData.value }}份</span>
                            </div>
                        </div>

                        <div class="medicine-tag-western-content western-tag-content">
                            <div
                                class="western-no-group-item"
                                overflow
                            >
                                {{ item.name }}
                            </div>
                            <div
                                class="western-no-group-item"
                                overflow
                            >
                                <span class="item-label">规格：</span>{{ hospitalGoodsSpec(item.productInfo) }}
                                <template v-if="config.westernTotalAmount && item.unitCount && item.unit">
                                    &nbsp;×{{ item.unitCount }}{{ item.unit }}
                                </template>
                            </div>
                            <div
                                v-if="item.usageInfo"
                                class="western-no-group-item"
                                overflow
                            >
                                <span class="item-label">用法：</span><span class="font-bold item-usage">{{
                                    item.usageInfo.usage
                                }} <template v-if="item.usageInfo.freq !== 'st'">{{ freqFormat(item.usageInfo.freq) }}</template> <template v-if="config.westernDays && item.usageInfo.freq !== 'st' && item.usageInfo.days">{{
                                    item.usageInfo.days
                                }}天</template></span>
                            </div>
                            <div
                                v-if="item.usageInfo"
                                class="western-no-group-item"
                                overflow
                            >
                                <span class="item-label">用量：</span><span class="font-bold item-usage">每次{{
                                    item.usageInfo.dosage
                                }}{{ item.usageInfo.dosageUnit }} <template v-if="config.westernSkinTest && item.ast === AstEnum.PI_SHI">{{
                                    item.astResult | filterSkinTest
                                }}</template></span>
                            </div>
                        </div>
                        <div
                            class="medicine-tag-footer"
                            data-type="footer"
                            :data-pendants-index="`${formIndex}${itemIndex}noGroupId`"
                        >
                            <div class="medicine-tag-footer-time medicine-tag-hospital-chinese-remark">
                                备注：{{ item.specialRequirement || (item.usageInfo && item.usageInfo.specialRequirement) }}
                            </div>
                            <div class="medicine-tag-footer-time">
                                <template v-if="config.printDateType">
                                    {{ form.planExecuteTime | parseTime('y-m-d') }}
                                </template>
                                <template v-else>
                                    {{ new Date() | parseTime('y-m-d h:i:s') }}
                                </template>
                            </div>
                        </div>
                    </template>
                </template>
            </template>
        </template>
    </div>
</template>

<script>
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";

    import {deepClone, formatAge, hospitalBedFormat, obj2arr, parseTime} from "./common/utils.js";
    import {AstEnum, NUMBER_ICONS, SourceFormTypeEnum} from "./common/constants.js";
    import {freqFormat} from "./common/medical-transformat.js";

    import PageSizeMap, {Orientation} from "../share/page-size.js";

    import PrintRow from "./components/layout/print-row.vue";
    import PrintCol from "./components/layout/print-col.vue";
    import clone from "./common/clone.js";
    import MedicineTagHandler from "./data-handler/medicine-tag-handler";

    const ResetData = {
        mobile: 0, // 手机，默认 0，可选 0 1
        ward: 0, // 病区，默认 0，可选 0 1
        bedNo: 1, // 床号，默认 1，可选 0 1
        mobileType: 0, // 手机隐私保护，默认 0，可选 0(完整展示) 1(隐藏中间4位)
        westernDays: 1, // 天数，默认 1，可选 0 1
        westernTotalAmount: 1, // 总量，默认 1，可选 0 1
        westernSkinTest: 1, // 皮试，默认 1，可选 0 1
        chineseRemark: '', // 中药备注，默认空字符串
        printDateType: 0, // 日期类型，默认 0，可选 0(打印日期) 1(执行日期)
    };

    export default {
        name: "HospitalMedicineTag",
        DataHandler: MedicineTagHandler,
        businessKey: PrintBusinessKeyEnum.HOSPITAL_MEDICINE_TAG,
        components: {
            PrintCol,
            PrintRow
        },
        pages: [
            {
                paper: PageSizeMap.TAG40_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG40_60,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_70,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_40,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_60,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG70_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG70_50,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG80_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG80_50,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },

        ],
        filters: {
            parseTime,
            hospitalBedFormat,
            filterMobile(mobile, formatType = 0) {
                if(!mobile) {
                    return '';
                }
                if(formatType === 1) {
                    return mobile.slice(0, 3) + '****' + mobile.slice(7);
                }
                return mobile
            },
            filterSkinTest(value) {
                if (!value || !value.result) return '皮试()';
                if (value.result === '阳性') return '皮试(+)';
                return '皮试(-)';
            },
        },
        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
            extra: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                SourceFormTypeEnum,
                NUMBER_ICONS,
                AstEnum
            }
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            config() {
                return this.renderData.config.hospitalTags?.medicine ?? ResetData;
            },
            forms() {
                return clone(this.printData.forms).filter((item) => {
                    item.formItems = item.formItems.filter((item) => item.sourceItemType !== 1);
                    return item.formItems.length > 0
                }) || []
            },
            patient() {
                return this.printData.patient || {};
            },
            /**
             * 是否是打印设置页面的预览
             * 如果是的话需要隐藏份数标签
             * @returns {boolean}
             */
            isSettingPreview() {
                return this.extra?.isPreview === true;
            },
        },
        methods: {
            formatAge,
            freqFormat,
            hospitalGoodsSpec(goods) {
                if (!goods) return '';
                const {
                    displaySpec,
                    componentContentNum,
                    componentContentUnit,
                    medicineDosageNum,
                    medicineDosageUnit,
                    pieceNum,
                    pieceUnit,
                    packageUnit,
                } = goods;
                if (displaySpec){
                    return displaySpec
                }
                let displaySpecStr = '';
                if (componentContentNum && componentContentUnit) {
                    displaySpecStr = `${componentContentNum}${componentContentUnit}`
                }
                if (medicineDosageNum && medicineDosageUnit) {
                    displaySpecStr = `${displaySpecStr}:${medicineDosageNum}${medicineDosageUnit}`;
                }
                if (pieceNum && pieceUnit) {
                    displaySpecStr = `${displaySpecStr}*${pieceNum}${pieceUnit}`;
                }
                if (packageUnit) {
                    displaySpecStr = `${displaySpecStr}/${packageUnit}`;
                }
                return displaySpecStr;
            },
            /**
             * @desc 对西药和输液处方 根据 groupId 拆分
             * <AUTHOR>
             * @date 2022-01-26 19:32:55
             * @params
             * @return
             */
            getWesternMedicine(form) {
                let formItemsIncludeIdObj = {};
                let formItemsNoId = [];
                form.formItems.forEach(item => {
                    if (item.groupId) {
                        if (!formItemsIncludeIdObj[item.groupId]) {
                            formItemsIncludeIdObj[item.groupId] = [];
                        }
                        formItemsIncludeIdObj[item.groupId].push(item);
                    } else {
                        formItemsNoId.push(item);
                    }
                })
                return {
                    groupItems: obj2arr(formItemsIncludeIdObj),
                    formItems: formItemsNoId,
                }
            },
            splitGroupItems(formItems) {
                let res = [];
                const LEN = 3;
                let items = clone(formItems);
                while (items.length) {
                    if (items.length >= 3) {
                        res.push(items.splice(0, LEN));
                    } else {
                        res.push(items.splice(0, items.length));
                    }
                }
                return res;
            },
            getDoseCount(form) {
                return form.leftDoseCount ? form.leftDoseCount : form.usageInfo.doseCount;
            },
        }
    }
</script>

<style lang="scss">
@import "./templates/components/layout/_print-layout";

* {
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "微软雅黑", serif;
}

.abc-page_preview {
    &.abc-page {
        overflow: visible;
    }

    .count-data-container {
        display: flex !important;
    }
}

.font-bold {
    font-weight: bold;
}

.text-right {
    text-align: right;
}

.medicine-tag-header {
    padding: 3pt 4pt;
    border-bottom: 1pt solid #000000;
}

.medicine-tag-header-patient-container {
    position: relative;

    .count-data-container {
        position: absolute;
        top: 5px;
        right: -82px;
        display: none;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 34px;
        font-size: 14px;
        font-weight: 500;
        color: #88a6cb;
        vertical-align: middle;
        background-color: #ffffff;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }
}

.medicine-tag-footer {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    //height: 25px;
    padding: 3pt 4pt;
    border-top: 1pt solid #000000;
}

.medicine-tag-group-footer {
    padding: 0 4pt 3pt 4pt;
}

.hospital-medicine-tag-patient-info-wrapper {
    display: flex;
    gap: 4pt;
    align-items: center;
    font-size: 10pt;
    line-height: 12pt;
}

.medicine-tag-patient-info,
.medicine-tag-footer-info {
    flex: 1;
    overflow: hidden;
    font-size: 8pt;
    line-height: 12pt;
    word-break: break-all;
    white-space: nowrap;

    .size_40_30 {
        display: none;
    }
}

.medicine-tag-patient-info {
    font-size: 10pt;
    line-height: 12pt;
}

.medicine-tag-hospital-ward {
    flex: 1;
    width: 0;
    overflow: hidden;
    text-align: right;
    white-space: nowrap;
}

.medicine-tag-footer-info {
    font-size: 9pt;
    line-height: 12pt;
}

.medicine-tag-footer-time {
    font-size: 9pt;
    line-height: 12pt;
}

.medicine-tag-hospital-chinese-remark {
    flex: 1;
    width: 0;
    overflow: hidden;
    white-space: nowrap;
}

.western-group-usage-footer {
    font-size: 8pt;
    line-height: 10pt;
}

.medicine-tag-chinese-content {
    padding: 3pt 0 0 4pt;

    .medicine-tag-chinese-usage {
        margin-bottom: 4pt;
        overflow: hidden;
        font-size: 10pt;
        line-height: 14pt;
        word-break: break-all;
        white-space: nowrap;

        .item-usage {
            font-size: 11pt;
        }
    }
}

.medicine-tag-content-group {
    padding: 3pt 4pt 0 4pt !important;
}

.medicine-tag-western-content {
    padding: 3pt 4pt 0 4pt;

    .western-group-item {
        .western-group-name {
            max-height: 11pt;
            font-size: 9pt;
            line-height: 11pt;
        }

        .western-group-dosage,
        .western-group-spec {
            max-height: 10pt;
            font-size: 8pt;
            line-height: 10pt;
        }

        .western-group-name,
        .western-group-dosage,
        .western-group-spec {
            overflow: hidden;
            word-break: break-all;
            white-space: nowrap;
        }
    }

    .medicine-tag-item-group {
        &:not(:last-of-type) {
            margin-bottom: 1pt !important;
        }
    }

    .western-no-group-item {
        margin-bottom: 4pt;
        overflow: hidden;
        font-size: 10pt;
        line-height: 14pt;
        word-break: break-all;
        white-space: nowrap;

        .item-usage {
            font-size: 11pt;
        }
    }
}

[data-size=page_40mm×30mm] {
    .size_40_30 {
        display: inline-block;
    }

    .not_size_40_30 {
        display: none;
    }

    .medicine-tag-header {
        padding: 0 4pt;
    }

    .medicine-tag-header-group {
        padding-top: 0;
        padding-bottom: 0;
    }

    .medicine-tag-footer {
        padding: 1pt 4pt 0 4pt;
    }

    .medicine-tag-group-footer {
        padding: 0 4pt 1pt 4pt;
    }

    .medicine-tag-western-content {
        padding: 1pt 8pt 0 4pt;
    }

    .medicine-tag-content-group {
        padding: 2pt 4pt 0 4pt !important;
    }

    .medicine-tag-chinese-content {
        padding: 1pt 0 0 4pt;
    }

    .hospital-medicine-tag-patient-info-wrapper {
        font-size: 8pt;
        line-height: 11pt;
    }

    .medicine-tag-patient-info {
        font-size: 8pt;
        line-height: 11pt;
    }

    .medicine-tag-footer-info {
        font-size: 8pt;
        line-height: 12pt;
    }

    .medicine-tag-footer-time {
        font-size: 8pt;
        line-height: 12pt;
    }

    .western-group-usage-footer {
        font-size: 6pt;
        line-height: 7pt;
    }

    .medicine-tag-chinese-content {
        .medicine-tag-chinese-usage {
            margin-bottom: 1pt;
            font-size: 8pt;
            line-height: 13pt;

            .item-label {
                display: none;
            }

            .item-usage {
                font-size: 10pt;
            }
        }
    }

    .medicine-tag-western-content {
        .western-group-item {
            .western-group-name {
                max-height: 10pt;
                font-size: 7pt;
                line-height: 10pt;
            }

            .western-group-dosage,
            .western-group-spec {
                max-height: 8pt;
                font-size: 6pt;
                line-height: 8pt;
            }
        }

        .medicine-tag-item-group {
            margin-bottom: 0 !important;
        }

        .western-no-group-item {
            margin-bottom: 1pt;
            font-size: 8pt;
            line-height: 13pt;

            .item-label {
                display: none;
            }

            .item-usage {
                font-size: 10pt;
            }
        }
    }
}

[data-size=page_50mm×30mm],
[data-size=page_60mm×30mm] {
    .medicine-tag-header {
        padding: 0 4pt;
    }

    .medicine-tag-header-group {
        padding-top: 0;
        padding-bottom: 0;
    }

    .medicine-tag-footer {
        padding: 0 4pt 0 4pt;
    }

    .medicine-tag-group-footer {
        padding: 0 0 0 4pt;
    }

    .hospital-medicine-tag-patient-info-wrapper {
        font-size: 8pt;
        line-height: 12pt;
    }

    .medicine-tag-patient-info {
        font-size: 8pt;
        line-height: 12pt;
    }

    .medicine-tag-footer-info {
        font-size: 8pt;
        line-height: 12pt;
    }

    .medicine-tag-footer-time {
        font-size: 8pt;
        line-height: 12pt;
    }

    .western-group-usage-footer {
        font-size: 8pt;
        line-height: 9pt;
    }

    .medicine-tag-chinese-content {
        padding: 2pt 4pt 0 4pt !important;

        .medicine-tag-chinese-usage {
            margin-bottom: 1pt;
            font-size: 10pt;
            line-height: 13pt;

            .item-usage {
                font-size: 9pt;
            }
        }
    }

    .medicine-tag-western-content {
        padding: 2pt 4pt 0 4pt !important;

        .western-group-item {
            .western-group-name {
                max-height: 9pt;
                font-size: 8pt;
                line-height: 9pt;
            }

            .western-group-dosage,
            .western-group-spec {
                max-height: 8pt;
                font-size: 7pt;
                line-height: 8pt;
            }
        }

        .medicine-tag-item-group {
            margin-bottom: 0 !important;
        }

        .western-no-group-item {
            margin-bottom: 1pt;
            font-size: 10pt;
            line-height: 13pt;

            .item-usage {
                font-size: 9pt;
            }
        }
    }

    .medicine-tag-content-group {
        padding: 2pt 4pt 0 4pt !important;
    }
}

[data-size=page_70mm×50mm],
[data-size=page_50mm×70mm],
[data-size=page_80mm×50mm] {
    .medicine-tag-header {
        padding: 3pt 4pt;
    }

    .hospital-medicine-tag-patient-info-wrapper {
        font-size: 12pt;
        line-height: 14pt;
    }

    .medicine-tag-patient-info {
        font-size: 12pt;
        line-height: 14pt;
    }

    .medicine-tag-footer-info {
        font-size: 10pt;
        line-height: 14pt;
    }

    .medicine-tag-footer-time {
        font-size: 10pt;
        line-height: 14pt;
    }

    .western-group-usage-footer {
        font-size: 10pt;
        line-height: 12pt;
    }

    .medicine-tag-chinese-content {
        .medicine-tag-chinese-usage {
            font-size: 12pt;
            line-height: 18pt;

            .item-usage {
                font-size: 13pt;
            }
        }
    }

    .medicine-tag-western-content {
        .medicine-tag-item-group {
            &:not(:last-of-type) {
                margin-bottom: 2pt !important;
            }
        }

        .western-group-item {
            .western-group-name {
                max-height: 14pt;
                font-size: 12pt;
                line-height: 14pt;
            }

            .western-group-dosage,
            .western-group-spec {
                max-height: 13pt;
                font-size: 11pt;
                line-height: 13pt;
            }
        }

        .western-no-group-item {
            font-size: 12pt;
            line-height: 18pt;

            .item-usage {
                font-size: 13pt;
            }
        }
    }

    .medicine-tag-content-group {
        padding: 3pt 8pt 0 4pt !important;
    }
}
</style>
