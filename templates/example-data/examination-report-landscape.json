{"id": "3808185162180558861", "goodsType": 3, "type": 1, "subType": 0, "patientId": "ffffffff0000000034d3a3b3cb024000", "patientOrderId": "ffffffff0000000034d963ff215cc000", "examinationApplySheetId": "3808185162180558858", "wardAreaId": null, "relationPatientOrderId": null, "chargeSheetId": "ffffffff0000000034d963ff4ae3000b", "chargeFormItemId": "ffffffff0000000034d9641e0ae2800a", "chargeSheetType": 2, "outpatientFormItemId": "ffffffff0000000034d9641e03d34011", "adviceExecuteItemId": null, "peFormItemId": null, "peFormItemIds": [], "organPrintView": {"id": "ffffffff00000000349af4a9171ac001", "name": "呼和浩特振华医院", "contactPhone": "0471-3600966", "addressProvinceName": "内蒙古", "addressCityName": "呼和浩特市", "addressDistrictName": "赛罕区", "addressDetail": "内蒙古自治区呼和浩特市赛罕区昭乌达路街道88号凯旋广场综合楼呼和浩特振华医院1至4层", "logo": "https://cd-cis-static-assets.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000349af4a9171ac001/basic/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20231010140838_OzOLyafAcfxV.png", "qrUrl": "http://weixin.qq.com/q/02oJ00BiqtdRG10000007Q", "category": "综合医院", "addressProvinceId": "150000", "addressCityId": "150100", "addressDistrictId": "150105", "hisType": 100, "medicalDocumentsTitle": {"prescription": "呼和浩特振华医院", "medical": "呼和浩特振华医院", "infusion": "呼和浩特振华医院", "treatment": "呼和浩特振华医院", "examination": "呼和浩特振华医院", "illnessCert": "呼和浩特振华医院", "inspection": "呼和浩特振华医院"}}, "examinationId": "ffffffff0000000034b558ab0c50c000", "examinationIds": ["ffffffff0000000034b558ab0c50c000"], "supplierId": 0, "barCode": null, "businessType": 10, "orderNo": "202410110003", "status": 1, "sampleStatus": 20, "doctorId": "ffffffff0000000034c7a5c45319c000", "doctor": {"id": "ffffffff0000000034c7a5c45319c000", "name": "李冬梅", "mobile": "***********", "countryCode": "86", "status": 1, "created": "2024-06-25 16:17:39", "shortId": "3803190672964042752", "namePy": "<PERSON><PERSON><PERSON>", "namePyFirst": "LDM", "headImgUrl": null, "handSign": "https://cis-images.oss-cn-shanghai.aliyuncs.com/prescription-sign/6XjSNO5dyjVDogXwjndFXfglvxathSNI_1725610283468", "wechatOpenIdMp": "ogT1C04HAGLG7Te0NeayvQU-bbhM", "wechatNickName": ""}, "sellerId": "ffffffff0000000034c7a5c45319c000", "seller": {"id": "ffffffff0000000034c7a5c45319c000", "name": "李冬梅", "mobile": "***********", "countryCode": "86", "status": 1, "created": "2024-06-25 16:17:39", "shortId": "3803190672964042752", "namePy": "<PERSON><PERSON><PERSON>", "namePyFirst": "LDM", "headImgUrl": null, "handSign": "https://cis-images.oss-cn-shanghai.aliyuncs.com/prescription-sign/6XjSNO5dyjVDogXwjndFXfglvxathSNI_1725610283468", "wechatOpenIdMp": "ogT1C04HAGLG7Te0NeayvQU-bbhM", "wechatNickName": ""}, "doctorDepartmentId": "ffffffff00000000349cf37b59cbc000", "doctorDepartmentName": "内科", "sellerDepartmentId": "ffffffff00000000349cf37b59cbc000", "sellerDepartmentName": "内科", "executeDepartmentId": "", "executeDepartmentName": null, "attachments": [], "remark": null, "itemsValue": [{"id": "ffffffff0000000034d3585536990010", "goodsId": "ffffffff0000000034d3585532090009", "goodsName": "促甲状腺素", "type": 2, "name": "促甲状腺素", "enName": "TSH", "unit": "uIU/mL", "ref": "0.3～4.5", "refDetails": [{"itemId": "ffffffff0000000034d3585536990010", "ageUnit": "岁", "ref": "0.3～4.5"}], "itemCode": "000503", "itemType": 1, "resultDisplayScale": 2, "value": "0.001", "valueType": "STRING", "abnormalFlag": "L", "sort": 0, "groupBy": "甲功三项", "groupById": 0, "examinationSheetId": "ffffffff0000000034d9643a76af8009", "deviceModelId": "3806064977522229248", "deviceModelIds": ["3806064977522229248"], "updateItemsValueFlag": 1}, {"id": "ffffffff0000000034d35854f6990000", "goodsId": "ffffffff0000000034d35854d209000f", "goodsName": "游离甲状腺素", "type": 1, "name": "游离甲状腺素", "enName": "FT4", "unit": "pg/mL", "ref": "{\"max\":\"17.2\",\"min\":\"8.9\"}", "refDetails": [{"itemId": "ffffffff0000000034d35854f6990000", "ref": "{\"min\":\"8.9\",\"max\":\"17.2\"}"}], "itemCode": "000489", "itemType": 1, "resultDisplayScale": 2, "value": "23.83", "valueType": "STRING", "abnormalFlag": "H", "sort": 1, "groupBy": "甲功三项", "groupById": 0, "examinationSheetId": "ffffffff0000000034d9643a76af8009", "deviceModelId": "3806064977522229248", "deviceModelIds": ["3806064977522229248"], "updateItemsValueFlag": 1}, {"id": "ffffffff0000000034d35854d6990008", "goodsId": "ffffffff0000000034d35854d2090006", "goodsName": "游离三碘甲状腺原氨酸", "type": 1, "name": "游离三碘甲状腺原氨酸", "enName": "FT3", "unit": "pmol/L", "ref": "{\"max\":\"4.18\",\"min\":\"1.21\"}", "refDetails": [{"itemId": "ffffffff0000000034d35854d6990008", "ref": "{\"min\":\"1.21\",\"max\":\"4.18\"}"}], "itemCode": "000486", "itemType": 1, "resultDisplayScale": 2, "value": "6.52", "valueType": "STRING", "abnormalFlag": "H", "sort": 2, "groupBy": "甲功三项", "groupById": 0, "examinationSheetId": "ffffffff0000000034d9643a76af8009", "deviceModelId": "3806064977522229248", "deviceModelIds": ["3806064977522229248"], "updateItemsValueFlag": 1}], "deviceData": null, "preItemsValue": [{"id": null, "goodsId": null, "type": null, "name": null, "ref": "{\"min\":\"\",\"max\":\"\"}", "itemCode": null, "itemType": null, "value": null, "valueType": "STRING", "sort": 0, "updateItemsValueFlag": 0}, {"id": null, "goodsId": null, "type": null, "name": null, "ref": "{\"min\":\"\",\"max\":\"\"}", "itemCode": null, "itemType": null, "value": null, "valueType": "STRING", "sort": 0, "updateItemsValueFlag": 0}, {"id": null, "goodsId": null, "type": null, "name": null, "ref": "{\"min\":\"\",\"max\":\"\"}", "itemCode": null, "itemType": null, "value": null, "valueType": "STRING", "sort": 0, "updateItemsValueFlag": 0}], "examinationHistories": [], "created": "2024-10-11T00:27:00Z", "orderByDate": "2024-10-11T00:27:00Z", "lastModifiedBy": "ffffffff0000000034be6a2c65998000", "patientOrderNumber": "2991", "wardAreaName": null, "bedNumber": null, "testerId": "ffffffff0000000034be6a2c65998000", "testerName": "申健", "tester": {"id": "ffffffff0000000034be6a2c65998000", "name": "申健", "mobile": "13015019866", "countryCode": "86", "status": 1, "created": "2024-04-30 15:40:19", "shortId": "3800591874462941184", "namePy": "<PERSON><PERSON><PERSON>", "namePyFirst": "SJ", "headImgUrl": null, "handSign": "https://cis-images.oss-cn-shanghai.aliyuncs.com/prescription-sign/6XjSNO5dyjVDogXwjndFXfglvxathSNI_1725610283468", "wechatOpenIdMp": null, "wechatNickName": null}, "testTime": "2024-10-11T01:37:00Z", "sampleType": "血清", "checkerId": "ffffffff0000000034be6a2c65998000", "checkerName": "申健", "checker": {"id": "ffffffff0000000034be6a2c65998000", "name": "申健", "mobile": "13015019866", "countryCode": "86", "status": 1, "created": "2024-04-30 15:40:19", "shortId": "3800591874462941184", "namePy": "<PERSON><PERSON><PERSON>", "namePyFirst": "SJ", "headImgUrl": null, "handSign": "https://cis-images.oss-cn-shanghai.aliyuncs.com/prescription-sign/6XjSNO5dyjVDogXwjndFXfglvxathSNI_1725610283468", "wechatOpenIdMp": null, "wechatNickName": null}, "checkTime": "", "reportTime": "2024-10-11T01:53:28Z", "chargeFormItemStatus": 1, "chargeFormItemOnceFee": 130.5, "examinationChargeFormItems": [{"examinationSheetId": "ffffffff0000000034d9643a76af8009", "chargeFormItemSourceId": "ffffffff0000000034d9641e03d34011", "chargeFormItemId": "ffffffff0000000034d9641e0ae2800a", "chargeFormItemStatus": 20, "receivedUnitCount": 1, "receivedTotalPrice": 130.5, "chargeFormItemStatusName": "已收费", "receivedUnitPrice": 130.5}], "canExecute": 1, "diagnosis": null, "patient": {"id": "ffffffff0000000034d3a3b3cb024000", "name": "苏宁", "namePy": "suning", "namePyFirst": "SN", "mobile": "13847142777", "countryCode": null, "sex": "女", "birthday": "1981-05-04", "age": {"year": 43, "month": 5, "day": 8}, "isMember": 0, "idCard": "150102198105043021", "marital": null, "weight": null, "importFlag": 0, "ethnicity": null, "nationality": null, "contactName": null, "contactRelation": null, "contactMobile": null, "sn": "001473", "remark": "", "profession": "", "company": "交通银行内蒙古自治区分行", "companyMobile": null, "blockFlag": 0, "address": {"addressCityId": "", "addressCityName": "", "addressDetail": null, "addressDistrictId": "", "addressDistrictName": "", "addressGeo": null, "addressProvinceId": "", "addressProvinceName": "", "addressPostcode": null, "fullAddress": ""}, "familyMobile": null, "tags": null, "activeDate": "2024-10-11T01:53:05.000+00:00", "activeClinicId": "ffffffff00000000349af4a9171ac001", "lastOutpatientDate": "2024-10-11T00:23:12.000+00:00", "lastOutpatientClinicId": "ffffffff00000000349af4a9171ac001", "lastOutpatientDoctorId": "ffffffff0000000034c7a5c45319c000", "wxOpenId": null, "unionId": null, "wxUserId": null, "wxNickName": null, "wxHeadImgUrl": null, "isWxMainPatient": 0, "wxBindStatus": 0, "allergicHistory": "头孢类过敏"}, "registrationFormItem": null, "examinationSheetReport": {"id": "ffffffff0000000034d9643a76af8009", "goodsType": 3, "type": 1, "subType": 0, "deviceType": 2, "imageFiles": [], "method": null, "videoDescription": null, "resultInfo": null, "advice": null, "suggestion": null, "diagnosisFlag": null, "versionFlag": 0, "recordDoctorId": null, "consultationDoctorId": null, "principalDoctorId": null, "examinationName": "甲功三项", "diagnosisEntryItems": [{"id": "3808187948003704832", "type": 1, "deviceType": 2, "name": "促甲状腺素", "abnormalFlag": 10, "tag": 20}, {"id": "3808187948003704833", "diagnosisEntryId": "3795087528938373337", "type": 1, "name": "游离甲状腺素", "abnormalFlag": 10, "tag": 10}, {"id": "3808187948003704834", "diagnosisEntryId": "3795087528938373331", "type": 1, "name": "游离三碘甲状腺原氨酸", "abnormalFlag": 10, "tag": 10}], "principalDoctorName": null, "recordDoctorName": null, "consultationDoctorName": null, "principalDoctorMobile": null}, "samplePipe": null, "deviceModelId": "3806064977522229248", "deviceRoomId": null, "deviceView": {"id": "3806064977522229248", "deviceModelId": "3806064977522229248", "name": "美康全自动化学发光免疫分析仪MS-3080", "model": "MS-3080", "deviceUuid": "美康·MS-3080", "manufacture": "美康", "iconUrl": "https://cd-cis-static-assets.oss-cn-chengdu.aliyuncs.com/oa/lis-device/image/R7iGKGnd1eAKCPQAR6wZ187J7eRn9GQZ_1724657159931.png", "remarks": "可用于体外检测人血清、血浆、全血等样本，辅助临床诊断人心肌梗死，心肌损伤，心力衰竭，急性冠状动脉综合征，心血管炎症，静脉血栓栓塞，常规炎症，细菌/病毒感染鉴别，新生儿败血症，急慢性肾病，肾损伤等疾病的早期检测发现及早期治疗。", "goodsType": 3, "goodsSubType": 1, "goodsExtendSpec": null, "supplierId": 0, "deviceType": 3, "deviceTypeName": "免疫检验", "usageType": 4, "usageTypeName": "免疫分析", "innerFlag": 0, "sampleNoRule": {"prefix": null, "length": 1, "format": null}, "connectStatus": 20, "deviceModelStatus": 0, "lastModified": "2024-08-30T01:55:32Z", "lastModifiedBy": "00000000000000000000000000000000", "deviceStatus": 0, "clinicInfos": [{"organ": {"id": "ffffffff00000000349af4a9171ac001", "parentId": "ffffffff00000000349af4a8f71ac000", "clinicId": "ffffffff00000000349af4a9171ac001", "chainId": "ffffffff00000000349af4a8f71ac000", "name": "呼和浩特市振华医院有限公司", "shortName": "呼和浩特振华医院", "nodeType": 2, "viewMode": 1, "hisType": 100, "shortNameFirst": "呼和浩特振华医院"}, "deviceId": "305024129531943", "shortId": "000006", "deviceStatus": 10, "deviceStatusName": "使用中", "created": "2024-09-04T07:52:05Z"}], "extendInfos": {"connectMethod": "网口", "connectMode": "服务器模式", "dataGetWay": "从设备获取"}}, "examinationApplySheetView": {"id": "3808185162180558858", "chainId": "ffffffff00000000349af4a8f71ac000", "clinicId": "ffffffff00000000349af4a9171ac001", "doctorId": "ffffffff0000000034c7a5c45319c000", "departmentId": "ffffffff00000000349cf37b59cbc000", "patientId": "ffffffff0000000034d3a3b3cb024000", "patientOrderId": "ffffffff0000000034d963ff215cc000", "registrationFormItemId": null, "deviceId": null, "deviceRoomId": null, "no": "JY2410110001", "businessType": 20, "goodsType": 3, "type": 1, "subType": 0, "deviceType": null, "chiefComplaint": "甲亢复诊", "presentHistory": "甲亢复诊，甲巯咪唑治疗中", "physicalExamination": "生命体征平稳", "diagnosisInfos": [{"id": null, "diseaseName": "甲状腺功能亢进症", "diseaseCode": "E05.900x001"}, {"id": null, "diseaseName": "心律失常", "diseaseCode": "I49.900"}, {"id": null, "diseaseName": "心房颤动[心房纤颤]", "diseaseCode": "I48.900x004"}], "purpose": null, "planExecuteDate": "2024-10-11", "created": "2024-10-11T00:27:00Z", "status": 20, "patient": null, "dcm4cheeView": null, "doctorName": null}, "location": null, "peSheetSimpleView": null, "deviceType": 2, "isMerge": 0, "importFlag": 0, "updateItemsValueFlag": 1, "reportInvalidFlag": 0, "reportUrl": "https://cd-cis-static-private-assets.oss-cn-chengdu.aliyuncs.com/archive/2024-10-11/examination/3808185162180558861.pdf?OSSAccessKeyId=LTAI4G6HRsiB2yKkuA2vpS5F&Expires=**********&Signature=1Rf4oZ2PsIVEKSYL%2BLQhgUyUQPo%3D", "isMutualRecognition": 0, "departmentId": "ffffffff00000000349cf37b59cbc000", "innerFlag": 0, "departmentName": "内科", "deviceStatus": 0, "goodsSubType": 1, "examinationApplySheetNo": "JY2410110001", "doctorName": "李冬梅", "sellerName": "李冬梅", "clinicPrintName": "呼和浩特振华医院", "deviceName": "美康全自动化学发光免疫分析仪MS-3080", "lastModifiedMillsTime": "1728611611000", "lastModifiedTime": "2024-10-11T01:53:31Z", "name": "甲功三项", "modifier": {"id": "ffffffff0000000034be6a2c65998000", "name": "申健", "mobile": "13015019866", "countryCode": "86", "status": 1, "created": "2024-04-30 15:40:19", "shortId": "3800591874462941184", "namePy": "<PERSON><PERSON><PERSON>", "namePyFirst": "SJ", "headImgUrl": null, "handSign": "申健", "wechatOpenIdMp": null, "wechatNickName": null}, "extendDiagnosisInfos": [{"toothNos": null, "value": [{"code": "E05.900x001", "name": "甲状腺功能亢进症", "diseaseType": null, "hint": null}, {"code": "I49.900", "name": "心律失常", "diseaseType": null, "hint": null}, {"code": "I48.900x004", "name": "心房颤动[心房纤颤]", "diseaseType": null, "hint": null}]}], "modifierName": "申健"}