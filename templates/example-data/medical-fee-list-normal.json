{"id": "ffffffff0000000034d4775f47d00010", "patient": {"id": "958e99beb2fc4340b1cf4fd4418d530e", "chainId": "6a869c22abee4ffbaef3e527bbb70aeb", "name": "丁俊", "namePy": "<PERSON>g<PERSON>", "namePyFirst": "DJ", "mobile": "13808239734", "countryCode": "86", "sex": "男", "birthday": "1978-08-01", "idCard": "540101197801156013", "isMember": 1, "age": {"year": 46, "month": 1, "day": 10}, "address": {"addressCityId": "120100", "addressCityName": "天津市", "addressDetail": "13fdsafd", "addressDistrictId": "120101", "addressDistrictName": "和平区", "addressGeo": null, "addressProvinceId": "120000", "addressProvinceName": "天津", "addressPostcode": null}, "sn": "038832", "remark": "fdsaf", "profession": "", "company": "fdsfdasfsd", "memberInfo": null, "patientSource": {"parentId": null, "parentName": null, "id": "ffffffff000000002183a0700f7a4000", "chainId": null, "name": "医生推荐", "sourceFrom": "02ba6a3b409d45bfa29fd01311d7a87d", "sourceFromName": "瓦特", "relatedType": 1, "relatedId": null, "clueId": null}, "tags": null, "marital": null, "weight": 65, "ethnicity": "蒙古族", "wxBindStatus": 0, "isAttention": null, "appFlag": 0, "arrearsFlag": 1, "externalCodeId": "", "externalCodeRemark": null, "pastHistory": "既往体健，否认药物过敏史，否认备孕", "allergicHistory": "", "shebaoCardInfo": null, "chronicArchivesInfo": null, "childCareInfo": null, "patientPoints": null}, "organ": {"id": "fff730ccc5ee45d783d82a85b8a0e52d", "name": "高新大原店", "shortName": "高新大原店", "addressProvinceName": null, "addressCityName": null, "addressDistrictName": null, "addressDetail": "湖北省黄石市阳新县王英镇泉丰大道", "contactPhone": "0851-8511132", "logo": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/basic/%E8%AF%8A%E6%89%80Logo%E6%B2%99%E5%9D%AA%E5%9D%9D_ah8qQyhlUdmr.jpg", "category": "中西医结合医院", "addressProvinceId": null, "addressCityId": null, "addressDistrictId": null, "hisType": 0, "medicalDocumentsTitle": null}, "chargeForms": [{"id": "ffffffff0000000034d67afaa7df0000", "chargeFormItems": [{"id": "ffffffff0000000034d4775f47d0000f", "name": "诊费", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 0, "discountedPrice": 0, "discountedUnitPrice": 0, "unitPrice": 0, "composeType": 0, "goodsTypeId": null, "feeComposeType": 0, "feeTypeId": 5, "feeTypeName": "挂号费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": null, "cmSpec": null, "sourceItemType": 0, "socialCode": "001102000010000-110200001", "hisCode": "**********", "socialUnit": "次", "socialName": "诊费", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 0, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 5, "productSubType": 0}], "sourceFormType": 1, "printFormType": 1, "totalPrice": 0, "optometristId": null, "optometristName": null, "glassesType": null, "glassesParams": null, "processUsageInfo": null}, {"id": "ffffffff0000000034d67afaa7df0006", "chargeFormItems": [{"id": "ffffffff0000000034d4775f47d00012", "name": "打印测试治疗带医保项", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 110, "discountedPrice": 55, "discountedUnitPrice": 55, "unitPrice": 110, "composeType": 0, "goodsTypeId": 22, "feeComposeType": 0, "feeTypeId": 22, "feeTypeName": "治疗费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "次", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "次", "socialName": "打印测试治疗带医保项", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 55, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 4, "productSubType": 1}], "sourceFormType": 3, "printFormType": 3, "totalPrice": 110, "optometristId": null, "optometristName": null, "glassesType": null, "glassesParams": null, "processUsageInfo": null}, {"id": "ffffffff0000000034d67afaa7df0009", "chargeFormItems": [{"id": "ffffffff0000000034d4775f47d00014", "name": "测试治疗理疗检查检验套餐打印", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 299, "discountedPrice": 299, "discountedUnitPrice": 299, "unitPrice": 299, "composeType": 1, "goodsTypeId": 8, "feeComposeType": 10, "feeTypeId": 8, "feeTypeName": null, "goodsFeeType": 0, "composeChildren": [{"id": "ffffffff0000000034d4775f47d00017", "name": "关节腔内注射", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 10, "discountedPrice": 10, "discountedUnitPrice": 10, "unitPrice": 10, "composeType": 2, "goodsTypeId": 22, "feeComposeType": 0, "feeTypeId": 22, "feeTypeName": "治疗费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "次", "cmSpec": null, "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "次", "socialName": "关节腔内注射", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 10, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 4, "productSubType": 1}, {"id": "ffffffff0000000034d4775f47d00016", "name": "推拿-医保", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 18, "discountedPrice": 18, "discountedUnitPrice": 18, "unitPrice": 18, "composeType": 2, "goodsTypeId": 23, "feeComposeType": 0, "feeTypeId": 23, "feeTypeName": "理疗费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "次", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "次", "socialName": "推拿-医保", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 18, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 4, "productSubType": 2}, {"id": "ffffffff0000000034d4775f47d00015", "name": "治疗系统单位火针-医保失败", "unit": "单侧", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 15, "discountedPrice": 15, "discountedUnitPrice": 15, "unitPrice": 15, "composeType": 2, "goodsTypeId": 23, "feeComposeType": 0, "feeTypeId": 23, "feeTypeName": "理疗费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "单侧", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "单侧", "socialName": "治疗系统单位火针-医保失败", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 15, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 4, "productSubType": 2}, {"id": "ffffffff0000000034d4775f47d0001d", "name": "DR检查", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 46, "discountedPrice": 46, "discountedUnitPrice": 46, "unitPrice": 46, "composeType": 2, "goodsTypeId": 21, "feeComposeType": 0, "feeTypeId": 21, "feeTypeName": "检查费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "次", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "次", "socialName": "DR检查", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 46, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 3, "productSubType": 2}, {"id": "ffffffff0000000034d4775f47d0001c", "name": "CT检查", "unit": "项", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 100, "discountedPrice": 100, "discountedUnitPrice": 100, "unitPrice": 100, "composeType": 2, "goodsTypeId": 21, "feeComposeType": 0, "feeTypeId": 21, "feeTypeName": "检查费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "项", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "项", "socialName": "CT检查", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 100, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 3, "productSubType": 2}, {"id": "ffffffff0000000034d4775f47d0001b", "name": "抗双链DNA测定（免疫印迹法）", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 20, "discountedPrice": 20, "discountedUnitPrice": 20, "unitPrice": 20, "composeType": 2, "goodsTypeId": 20, "feeComposeType": 0, "feeTypeId": 20, "feeTypeName": "检验费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "次", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "次", "socialName": "抗双链DNA测定（免疫印迹法）", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 20, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 3, "productSubType": 1}, {"id": "ffffffff0000000034d4775f47d0001a", "name": "甲型肝炎抗原测定(荧光探针法)", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 20, "discountedPrice": 20, "discountedUnitPrice": 20, "unitPrice": 20, "composeType": 2, "goodsTypeId": 20, "feeComposeType": 0, "feeTypeId": 20, "feeTypeName": "检验费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "次", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "次", "socialName": "甲型肝炎抗原测定(荧光探针法)", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 20, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 3, "productSubType": 1}, {"id": "ffffffff0000000034d4775f47d00019", "name": "血清脂肪酶测定（干化学法）", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 10, "discountedPrice": 10, "discountedUnitPrice": 10, "unitPrice": 10, "composeType": 2, "goodsTypeId": 20, "feeComposeType": 0, "feeTypeId": 20, "feeTypeName": "检验费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "次", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "次", "socialName": "血清脂肪酶测定（干化学法）", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 10, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 3, "productSubType": 1}, {"id": "ffffffff0000000034d4775f47d00018", "name": "超导给药", "unit": "次", "count": 1, "unitCount": 1, "doseCount": 1, "totalPrice": 60, "discountedPrice": 60, "discountedUnitPrice": 60, "unitPrice": 60, "composeType": 2, "goodsTypeId": 22, "feeComposeType": 0, "feeTypeId": 22, "feeTypeName": "治疗费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "次", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "次", "socialName": "超导给药", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 60, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 4, "productSubType": 1}], "position": null, "displaySpec": "次", "cmSpec": "", "sourceItemType": 0, "socialCode": null, "hisCode": null, "socialUnit": "次", "socialName": "测试治疗理疗检查检验套餐打印", "medicalFeeGrade": null, "ownExpenseRatio": 1, "ownExpenseFee": 299, "inscpScpAmt": 0, "overlmtAmt": 0, "productType": 11, "productSubType": 1}], "sourceFormType": 11, "printFormType": 11, "totalPrice": 299, "optometristId": null, "optometristName": null, "glassesType": null, "glassesParams": null, "processUsageInfo": null}, {"id": "ffffffff0000000034d4775f47d0001e", "chargeFormItems": [{"id": "ffffffff0000000034d4775f47d0001f", "name": "酚氨咖敏片(克感敏)", "unit": "片", "count": 3, "unitCount": 3, "doseCount": 1, "totalPrice": 0.6, "discountedPrice": 0, "discountedUnitPrice": 0, "unitPrice": 0.2, "composeType": 0, "goodsTypeId": 12, "feeComposeType": 0, "feeTypeId": 12, "feeTypeName": "西药费", "goodsFeeType": 0, "composeChildren": null, "position": null, "displaySpec": "0.3g*1000片/瓶", "cmSpec": null, "sourceItemType": 0, "socialCode": "XN02BEF028A001010201001", "hisCode": "**********", "socialUnit": "片", "socialName": "酚氨咖敏片(克感敏)", "medicalFeeGrade": 2, "ownExpenseRatio": 1, "ownExpenseFee": 0, "inscpScpAmt": 0, "overlmtAmt": 0, "goodsStockInfos": [{"stockId": "100025499", "batchNo": "**********", "expiryDate": "2026-12-31", "manufacturer": "重庆申高生化制药有限公司", "manufacturerFull": "重庆申高生化制药有限公司", "supplierName": "供应商测试"}], "productType": 1, "productSubType": 1}], "sourceFormType": 4, "printFormType": 4, "totalPrice": 0.6, "optometristId": null, "optometristName": null, "glassesType": null, "glassesParams": null, "processUsageInfo": null}], "chargeTransactions": [{"payMode": 1, "paySubMode": 0, "payModeName": "现金", "paySubModeName": null, "payModeDisplayName": "现金", "amount": 354}], "totalFee": 409.6, "singlePromotionFee": null, "packagePromotionFee": null, "discountFee": -55.6, "receivableFee": 354, "netIncomeFee": 354, "refundFee": null, "oweFee": 0, "diagnosedDate": "2024-09-11T03:09:46Z", "diagnosis": "急性上呼吸道感染", "diagnosisInfos": [{"code": "J06.900", "name": "急性上呼吸道感染", "diseaseType": null, "hint": null}], "extendDiagnosisInfos": [{"toothNos": null, "value": [{"code": "J06.900", "name": "急性上呼吸道感染", "diseaseType": null, "hint": null}]}], "healthCardId": null, "healthCardAccountPaymentFee": null, "healthCardFundPaymentFee": null, "healthCardOtherPaymentFee": null, "healthCardCardOwnerType": null, "healthCardSelfConceitFee": null, "healthCardSelfPayFee": null, "personalPaymentFee": 354, "chargedByName": "丁柱", "chargedTime": "2024-09-11T03:10:11Z", "sellerName": "", "doctorName": "丁柱", "nationalDoctorCode": "", "patientOrderNo": "********", "departmentName": "小儿外科诊室", "departmentCaty": "02", "hospitalCode": "***************", "doctorWorkNo": null, "refundByName": null, "refundTime": null, "latestOwePaidTime": null, "shebaoPayment": null, "subTotals": {"registrationFee": 0, "westernMedicineFee": 0.6, "chineseMedicineFee": 0, "examinationFee": 0, "treatmentFee": 110, "materialFee": 0, "onlineConsultationFee": 0, "expressDeliveryFee": 0, "decoctionFee": 0, "otherFee": 0, "composeProductFee": 299, "eyeFee": 0, "nursingFee": 0, "surgeryFee": 0}, "medicalBills": [{"name": "西药费", "totalFee": 0, "totalCount": 1, "unit": "项", "printType": 1, "feeTypeId": null, "innerFlag": 1, "sort": 0}, {"name": "检查费", "totalFee": 146, "totalCount": 2, "unit": "项", "printType": 4, "feeTypeId": null, "innerFlag": 1, "sort": 0}, {"name": "化验费", "totalFee": 50, "totalCount": 3, "unit": "项", "printType": 5, "feeTypeId": null, "innerFlag": 1, "sort": 0}, {"name": "治疗费", "totalFee": 158, "totalCount": 5, "unit": "项", "printType": 6, "feeTypeId": null, "innerFlag": 1, "sort": 0}, {"name": "挂号费", "totalFee": 0, "totalCount": 1, "unit": "项", "printType": 7, "feeTypeId": null, "innerFlag": 1, "sort": 0}], "wholeMedicalBills": [{"name": "挂号费", "totalFee": 0, "totalCount": 1, "unit": "项", "printType": 0, "feeTypeId": 5, "innerFlag": 1, "sort": 1}, {"name": "西药费", "totalFee": 0, "totalCount": 1, "unit": "项", "printType": 0, "feeTypeId": 12, "innerFlag": 1, "sort": 2}, {"name": "检验费", "totalFee": 50, "totalCount": 3, "unit": "项", "printType": 0, "feeTypeId": 20, "innerFlag": 1, "sort": 6}, {"name": "检查费", "totalFee": 146, "totalCount": 2, "unit": "项", "printType": 0, "feeTypeId": 21, "innerFlag": 1, "sort": 7}, {"name": "治疗费", "totalFee": 125, "totalCount": 3, "unit": "项", "printType": 0, "feeTypeId": 22, "innerFlag": 1, "sort": 8}, {"name": "理疗费", "totalFee": 33, "totalCount": 2, "unit": "项", "printType": 0, "feeTypeId": 23, "innerFlag": 1, "sort": 9}], "giftCoupons": [], "promotionBalances": [], "owedStatus": 0, "healthCardPaymentFee": 0, "serialNo": "3806798836145324048"}