{
  "id": "3793821451669225472",
  "patientOrderId": "ffffffff0000000034a65c828d618000",
  institutionLogoUrl: 'https://cd-cis-static-assets.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001c000e7809c40001/physical-examination-print-logo/%E9%87%91%E6%A1%A5logo_y3Uayk7sfm3a.png',
  "patient": {
    "id": "ffffffff0000000034798ebacc93c000",
    "name": "出院患者1",
    "mobile": "***********",
    "countryCode": null,
    "sex": "男",
    "birthday": "1994-05-06",
    "age": {
      "year": 29,
      "month": 7,
      "day": 0
    },
    "isMember": 0,
    "idCard": "510122199405061915",
    "marital": 2
  },
  "peGroupOrderId": null,
  "name": "1206test1",
  "displayName": "个检-1206test1",
  "no": "TJ202312060002",
  "orderNo": "202312060002",
  "type": 0,
  "businessType": 0,
  "businessTime": "2023-12-06",
  "orderCreated": "2023-12-06T08:38:44Z",
  "reportGetWay": 0,
  "status": 30,
  "salesEmployee": null,
  "totalFee": 44.0000,
  "chargeStatus": 0,
  "sheetSubmitted": null,
  "reportApproved": null,
  "reportReleased": null,
  "printTime": null,
  "reportReleasedBy": null,
  "reportFinishedBy": null,
  "reportApprovedBy": null,
  "printStatus": 0,
  "address": {
    "addressCityId": "110100",
    "addressCityName": "北京市",
    "addressDetail": "东门大桥2号院",
    "addressDistrictId": "110102",
    "addressDistrictName": "西城区",
    "addressGeo": null,
    "addressProvinceId": "110000",
    "addressProvinceName": "北京",
    "addressPostcode": null
  },
  "patientImageUrl": "https://cd-cis-static-assets-test.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000347714011f71c004/physical-examination-avatar/_KVQcDij8bS1y.ttxRZDQONf2WUdFm33VQFCmsB8MjYCJ6",
  "departmentGroups": [
    {
      "departmentId": null,
      "departmentName": "其他",
      "executeItems": [
        {
          "id": "3793821451669225475",
          "goodsId": "ffffffff0000000034a658965672c000",
          "goodsName": "CT1",
          "status": 40,
          "tester": null,
          "address": '111122222',
          "scheduleTime": "2023-12-07",
          "chargeStatus": 0,
          "remark": ""
        },
        {
          "id": "3793821451669225476",
          "goodsId": "ffffffff0000000034a658965672c008",
          "goodsName": "骨密度1",
          "status": 20,
          "tester": null,
          "address": '111122222',
          "scheduleTime": null,
          "chargeStatus": 0,
          "remark": "哈哈"
        },
        {
          "id": "3793821451669225477",
          "goodsId": "ffffffff0000000034a658965672c004",
          "goodsName": "彩色多普勒超声",
          "status": 30,
          "tester": null,
          "address": '111122222',
          "scheduleTime": null,
          "chargeStatus": 90,
          "remark": "娃娃"
        },
        {
          "id": "3793821451669225479",
          "goodsId": "ffffffff0000000034a6584b5672c015",
          "goodsName": "风湿三项",
          "status": 0,
          "tester": null,
          "address": '111122222',
          "scheduleTime": null,
          "chargeStatus": 0,
          "remark": null
        }
      ]
    }
  ],
  "groupOrder": null,
  "additionalInfo": {
    "total": 1,
    "orderNo": "202312060002",
    "chargeStatus": 0,
    "receivableFee": 20.5000,
    "receivedFee": 0.0000,
    "peItemDesc": "风湿三项"
  }
}