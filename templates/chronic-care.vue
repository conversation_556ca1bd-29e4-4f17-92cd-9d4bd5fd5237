<template>
    <div class="print-chronic-care-wrapper">
        <p class="chronic-title">
            {{ printData.name }}
        </p>
        <div class="chronic-patient">
            <div class="row">
                <div class="text-readonly">
                    <div class="label">
                        姓名：
                    </div>
                    <div class="value">
                        {{ patient.name }}
                    </div>
                </div>
                <div class="text-readonly">
                    <div class="label">
                        性别：
                    </div>
                    <div class="value">
                        {{ patient.sex }}
                    </div>
                </div>
                <div class="text-readonly">
                    <div class="label">
                        年龄：
                    </div>
                    <div class="value">
                        {{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}
                    </div>
                </div>
                <div class="text-readonly" style="text-align: right;">
                    <div class="label">
                        手机：
                    </div>
                    <div class="value">
                        {{ patient.mobile }}
                    </div>
                </div>
            </div>
            <div v-if="readonly && (formType === 2 || formType === 3)" class="row" style="margin-top: 2pt;">
                <div class="text-readonly">
                    <div class="label">
                        {{ typeLabel }}:
                    </div>
                    <div class="value">
                        {{ printData.createdByName }}
                    </div>
                </div>
                <div class="text-readonly" style="min-width: 25%;">
                    <div class="label">
                        {{ typeValueLabel }}:
                    </div>
                    <div v-if="printData.createdDate" class="value">
                        {{ printData.createdDate | parseTime('y-m-d') }}
                    </div>
                </div>
            </div>
        </div>

        <div v-if="showTreatmentRecord || treatmentRecord" class="treatment-record">
            <div class="title">
                治疗方案
            </div>
            <div v-if="treatmentRecord" class="advice-content">
                <template v-if="treatmentRecord.sport">
                    <p v-html="treatmentRecord.sport"></p>
                </template>
                <template v-if="treatmentRecord.diet">
                    <p v-html="treatmentRecord.diet"></p>
                </template>
                <template v-if="treatmentRecord.lifeStyle">
                    <p v-html="treatmentRecord.lifeStyle"></p>
                </template>
                <template v-if="treatmentRecord.medication">
                    <p v-html="treatmentRecord.medication"></p>
                </template>
            </div>
            <template v-else>
                <div class="blank-block"></div>
                <div class="title">
                    随访计划
                </div>
                <div class="blank-block"></div>
            </template>
        </div>
        <AbcPrintForm v-model="clonedPrintData" :readonly="readonly"></AbcPrintForm>
    </div>
</template>

<script>
import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
import PageSizeMap, {Orientation} from "../share/page-size.js";
import PrintCommonDataHandler from "./data-handler/common-handler.js";
import {formatAge, parseTime} from './common/utils.js';
import {ChronicCareFormPrintType} from './common/constants.js';
import clone from "./common/clone.js";

export default {
  name: "ChronicCare",
  DataHandler: PrintCommonDataHandler,
  businessKey: PrintBusinessKeyEnum.CHRONIC_CARE,
  pages: [
    {
      paper: PageSizeMap.A4,
      isRecommend: true,
      defaultOrientation: Orientation.portrait,
      defaultHeightLevel: null,
    },
    {
      paper: PageSizeMap.A5,
      isRecommend: false,
      defaultOrientation: Orientation.portrait,
      defaultHeightLevel: null,
    },
  ],
  props: {
    renderData: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  methods: {
    formatAge,
  },
  filters: {
    parseTime
  },
  computed: {
    printData() {
      return {
        ...this.renderData.printData,
        fields: this.renderData.printData.fields.filter(item => item.type !== 'FileUploader')
      };
    },
    /**
     * @desc 区分 问诊单 档案 评估表
     * 1  问诊单 2 档案 3 评估表
     */
    formType() {
      return this.printData.printFormType;
    },
    readonly() {
      return this.printData.readonly || false;
    },
    patient() {
      const patientField = this.printData.fields.find((item) => {
        if (item.type === 'PatientInfo') {
          return item;
        }
      });
      if (patientField && patientField.value) {
        return patientField.value;
      }
      return {};
    },
    createdName() {
      return this.printData.createdByName || '';
    },
    createdData() {
      return this.printData.createdDate || '';
    },
    typeLabel() {
      if (this.formType === ChronicCareFormPrintType.ARCHIVES) {
        return '建档人';
      }
      if (this.formType === ChronicCareFormPrintType.EVALUATION) {
        return '评估人';
      }
      return '';
    },
    typeValueLabel() {
      if (this.formType === ChronicCareFormPrintType.ARCHIVES) {
        return '建档时间';
      }
      if (this.formType === ChronicCareFormPrintType.EVALUATION) {
        return '评估时间';
      }
      return '';
    },
    treatmentRecord() {
      return this.printData.treatmentRecord || null;
    },
    // 显示治疗方案
    showTreatmentRecord() {
      return this.printData.showTreatmentRecord;
    },
    abnormalItems() {
      return this.printData.abnormalItems || [];
    },
    clonedPrintData() {
      return clone(this.printData);
    },
  }
}
</script>
<style lang="scss">
.print-chronic-care-wrapper {
    * {
        box-sizing: border-box;
    }

    @import './node_modules/abc-form-engine/lib/print/print';

    .chronic-patient {
        width: 100%;
        padding: 6pt 4pt;
        border-top: 1pt solid #000000;
        border-bottom: 1pt solid #000000;
        font-size: 11pt;

        .row {
            display: flex;
        }

        .text-readonly {
            width: 25%;
            line-height: 14pt;

            .label, .value {
                display: inline-block;
            }
        }
    }

    .x-patient-info, .x-form-title {
        display: none;
    }

    .chronic-title {
        line-height: 40pt;
        text-align: center;
        font-size: 14pt;
        font-weight: 500;
    }

    .treatment-record {
        margin-top: 8pt;

        .title {
            font-size: 11pt;
            font-weight: 500;
            text-align: left;
            margin-top: 12pt;
            margin-bottom: 4pt;
        }

        .advice-content {
            font-size: 10pt;
            line-height: 12pt;

            p {
                margin-bottom: 4pt;
            }
        }

        .blank-block {
            height: 100pt;
            width: 90%;
        }
    }

    .x-table-input-number {
        > * {
            vertical-align: middle!important;
        }
    }

    .medication-table {
        .table-tr {
            align-items: stretch!important;
        }
    }
}
</style>
