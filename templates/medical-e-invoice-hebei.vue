<!--exampleData
{
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305903',
            chargeFormItems: [
                {
                    id: 'ffebc4a7da95425489aeeb456b0c43ec',
                    name: '推拿',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 50.0,
                },
                {
                    id: 'eb2a534087c34b18934c84f5af292fd6',
                    name: '肩周炎针灸治疗',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 90.0,
                },
            ],
            sourceFormType: 3,
        },
        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                    goodsStockInfos: [
                        {
                            'stockId': '100013473',
                            'batchNo': '',
                            'expiryDate': '',
                            'manufacturer': '贵州肾元',
                            'manufacturerFull': '贵州肾元制药有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f0200',
                    name: '复方丹参片（罗浮山）',
                    unit: '瓶',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
        {
            id: '9410ffd3ece8439e9e12c8f3df396bc8',
            chargeFormItems: [
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                    name: '盐知母',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                    specialRequirement: '包煎',
                },
                {
                    id: '7ef3ac794a034b4e952031d4b14b18c1',
                    name: '盐黄柏',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 0.03,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9072',
                    name: '山药YG',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9073',
                    name: '牡丹皮YG',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9076',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
            ],
            sourceFormType: 6,
            specification: '中药饮片',
            doseCount: 1,
            dailyDosage: '1日1剂',
            usage: '煎服',
            freq: '1日3次',
            usageLevel: '每次150ml',
        },
        {
            id: 'a96615e57a564538a4df211eee45278c',
            chargeFormItems: [
                {
                    id: '9fb64806a842400b8793dd02f699f2e6',
                    name: '热敏灸盒',
                    unit: '盒',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 9,
        },
        {
            id: 'a96615e57a564538a4df211eee45278c01',
            chargeFormItems: [
                {
                    id: '9fb64806a842400b8793dd02f699f2e7',
                    name: '一次性针灸针',
                    unit: '支',
                    count: 10.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 8.0,
                },
            ],
            sourceFormType: 9,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    memberCardBalance: null,
    memberCardMobile: '',
    memberCardBeginningBalance: '', // 会员卡原有余额
    healthCardBeginningBalance: '567.68', // 社保卡原有余额
    healthCardOwnerRelationToPatient: '父女', // 持卡人关系
    healthCardBalance: '0.00', // 社保卡余额
    healthCardNo: '********', // 社保卡卡号
    healthCardOwner: '任我行', // 持卡人姓名"
    serialNo: '********', // 门诊流水号"

    healthCardId: '********', // 医保编号
    healthCardAccountPaymentFee: '19.99', // 帐户支付金额
    healthCardFundPaymentFee: 20, // 统筹支付金额
    healthCardOtherPaymentFee: '10', // 其它支付金额
    healthCardCardOwnerType: '职工', // 医保类型 职工 居民 离休干部
    healthCardSelfConceitFee: '11', // 自负金额
    healthCardSelfPayFee: '22', // 自费金额
    personalPaymentFee: '33', // 个人现金支付

    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
    ],

    shebaoPayment: {
        cardId: '********', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            // 重庆社保数据
            civilServiceSubsidy: 0, // 公务员补助
            largeClaimAmount: 0, // 大额理赔金额
            civilServiceBack: 0, // 历史起付线公务员返还
            singleDiseaseSupport: 0, // 单病种定点医疗机构垫支
            civilAidAmount: 0, // 民政救助金额
            civilAidOutpatientBalance: 0, // 民政救助门诊余额
            totalSubstituteFee: 0, // 总共抵用费用
            healthHelpFundFee: 0, // 健康扶贫医疗基金
            precisionPovertyFee: 0, // 精准脱贫保险金额
            otherPovertyReliefFee: 0, // 其他扶贫报销金额

            // 杭州社保数据
            curYearBalance: 0, // 当年账户余额
            allYearBalance: 0, // 历年账户余额
            curYearAccountPaymentFee: 0, // 本年账户支付
            allYearAccountPaymentFee: 0, // 历年账户支付
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 0, // 自负金额
            allYearAccountPaymentSelfConceitFee: 0, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 0, // 自理金额
            allYearAccountPaymentPersonalHandled: 0, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 0, // 自费金额
            allYearAccountPaymentPersonalPayment: 0, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 0, // 本年门诊起付标准支付累计 （省医保为空）

            // 武汉数据
            communityThreeFree: 0, // 社区三免
            lowPaidLine: 0, // 起付线
            superCappingLine: 0, // 超顶封线
            medicalInsuranceFee: 0, // 列入医保费用
            selfPaymentFee: 0, // 纯自费项目金额
            settleOrderNo: 'MZJS000000000', // 结算序列号
            medicalRecordNumber: '000000', // 就诊记录号

            // 青岛数据
            individualAffordabilityLine: '9.9', //个人负担起付线
        },
    },
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
}
-->

<template>
    <div>
        <block-box
            :top="19"
            :left="40"
        >
            {{ renderInvoiceData.invoiceCodeNum }}
        </block-box>

        <block-box
            :top="23"
            :left="44"
        >
            {{ renderInvoiceData.invoiceNumber }}
        </block-box>

        <div class="detail-items-wrapper">
            <div
                v-for="(item, index) in chargeDetails"
                :key="index"
                :class="{ 'left-part': index % 2 === 0, 'right-part': index % 2 === 1 }"
                class="charge-item"
                :style="{ top: `${Math.floor(index / 2) * 4 + 4 }mm` }"
            >
                <div
                    class="item-name"
                    overflow
                >
                    {{ item.chargeName }}
                </div>
                <div class="item-count">
                    {{ item.number }}&nbsp;&nbsp;&nbsp;{{ item.unit }}
                </div>
                <div class="item-price">
                    {{ item.amt | formatMoney }}
                </div>
            </div>
        </div>
        <block-box
            :top="27"
            :left="54"
        >
        </block-box>

        <block-box
            :top="27.5"
            :left="25"
        >
            {{ printData.patientName }}
        </block-box>

        <block-box
            :top="31.5"
            :left="165"
        >
        </block-box>

        <block-box
            :top="83"
            :left="14"
        >
            业务流水号：{{ printData.busNo }}
        </block-box>

        <block-box
            :top="83"
            :left="68"
        >
            门诊号：{{ printData.patientOrderNo }}
        </block-box>

        <block-box
            :top="83"
            :left="157"
        >
            就诊日期：{{ parseTime(printData.outpatientDate, 'y-m-d', true) }}
        </block-box>

        <block-box
            :top="88"
            :left="14"
            style="width: 54mm"
        >
            医疗机构类型：{{ printData.medicalInstitution }}
        </block-box>

        <block-box
            :top="88"
            :left="68"
        >
            医保类型：{{ printData.medicalCareType }}
        </block-box>

        <block-box
            :top="88"
            :left="115"
        >
            医保编码：{{ printData.medicalInsuranceID }}
        </block-box>

        <block-box
            :top="88"
            :left="157"
        >
            性别：{{ printData.sex }}
        </block-box>

        <block-box
            :top="92"
            :left="14"
        >
            医保统筹基金支付：{{ printData.fundPay | formatMoney }}
        </block-box>

        <block-box
            :top="92"
            :left="68"
        >
            其他支付：{{ printData.otherfundPay | formatMoney }}
        </block-box>

        <block-box
            :top="92"
            :left="115"
        >
            个人账户支付：{{ printData.accountPay | formatMoney }}
        </block-box>

        <block-box
            :top="92"
            :left="157"
        >
            个人现金支付：{{ printData.selfCashPay | formatMoney }}
        </block-box>

        <block-box
            :top="96"
            :left="14"
        >
            个人自付：{{ printData.selfPayAmt | formatMoney }}
        </block-box>
        <block-box
            :top="96"
            :left="68"
        >
            个人自费：{{ printData.ownPay | formatMoney }}
        </block-box>
        <block-box
            :top="96"
            :left="115"
        >
            个人负担总金额：{{ printData.psnPartAmt| formatMoney }}
        </block-box>
        <block-box
            :top="96"
            :left="157"
        >
            个人账户余额：{{ printData.accountBalance | formatMoney }}
        </block-box>

        <block-box
            :top="100"
            :left="14"
        >
            实际支付起付线：{{ printData.actualPaymentLine | formatMoney }}
        </block-box>

        <block-box
            :top="100"
            :left="68"
        >
            起付标准累计：{{ printData.dedcCum | formatMoney }}
        </block-box>

        <block-box
            :top="100"
            :left="115"
        >
            基本统筹累计：{{ printData.optFundPayCum | formatMoney }}
        </block-box>

        <block-box
            :top="100"
            :left="157"
        >
            慢特病统筹累计：{{ printData.chrDisPayCum | formatMoney }}
        </block-box>

        <block-box
            :top="104"
            :left="14"
        >
            大病统筹支付：{{ printData.hifobPay | formatMoney }}
        </block-box>

        <block-box
            :top="104"
            :left="68"
        >
            公务员统筹支付：{{ printData.civilServantMBFPaid | formatMoney }}
        </block-box>


        <block-box
            :top="104"
            :left="115"
        >
            医疗救助基金支付：{{ printData.rescueMBFPaid | formatMoney }}
        </block-box>

        <block-box
            :top="104"
            :left="157"
        >
            个人账户共济支付金额：{{ printData.accountMulaidPaid | formatMoney }}
        </block-box>

        <block-box
            :top="109"
            :left="14"
        >
            符合政策范围金额：{{ printData.inscpScpAmt | formatMoney }}
        </block-box>

        <block-box
            :top="109"
            :left="68"
        >
            医疗类别：{{ printData.medicalType === '01' ? '住院' : '门诊' }}
        </block-box>

        <block-box
            :top="109"
            :left="115"
        >
            就诊科室：{{ printData.departmentName }}
        </block-box>
        <block-box
            :top="109"
            :left="157"
        >
            普通：{{ printData.diseaseName }}
        </block-box>



        <block-box
            :top="78.5"
            :left="37"
        >
            {{ digitUppercase(printData.invoiceFee) }}
        </block-box>

        <block-box
            :top="78.5"
            :left="136"
        >
            {{ printData.invoiceFee | formatMoney }}
        </block-box>

        <block-box
            :top="115"
            :left="37"
        >
            {{ printData.taxName }}
        </block-box>

        <block-box
            :top="115"
            :left="130"
        >
            {{ printData.invoiceChecker }}
        </block-box>
        <block-box
            :top="115"
            :left="168"
        >
            {{ printData.payee }}
        </block-box>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import BillDataMixins from "./mixins/bill-data.js";
    import NationalBillData from "./mixins/national-bill-data.js";

    import { parseTime } from "./common/utils.js";


    export default {
        name: "MedicalEInvoiceHebei",
        components: {
            BlockBox,
        },
        mixins: [BillDataMixins,NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_E_INVOICE_HEBEI,
        pages: [
            {
                paper: PageSizeMap.MM210_127_JIANGSU,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            chargeDetails() {
                return this.printData.chargeDetails;
            },
            renderInvoiceData() {
                return this.printData.renderInvoiceData;
            },
        },
        methods: {
            parseTime,
        },
    }
</script>
<style lang="scss">
* {
  padding: 0;
  margin: 0;
}
.abc-page_preview {
  background: url("/static/assets/print/e-invoice-hebei.png");
  background-size: 211mm 127mm;
  color: #2a82e4;
}

.detail-items-wrapper {
    position: absolute;
    width: 100%;
    height: 40mm;
    font-size: 10pt;
    top: 36mm;
    left: 0;

    .left-part,
    .right-part {
        position: absolute;
    }

    .left-part {
        left: 24mm;
    }

    .right-part {
        left: 110mm;
    }

    .item-name,
    .item-count,
    .item-price,
    .item-amount,
    .item-proportion {
        line-height: 14pt;
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
        position: absolute;
    }

    .item-name {
        left: 0;
        width: 26mm;
    }

    .item-count {
        left: 27mm;
        width: 14mm;
    }

    .item-price {
        left: 41mm;
        width: 12mm;
    }

    .item-amount {
        left: 55mm;
        width: 13mm;
    }

    .item-proportion {
        left: 68mm;
        width: 15mm;
    }

    .only-one-page {
        position: absolute;
        bottom: -5pt;
        left: 50%;
        margin-left: -94px;
    }

    &.no-self-rate {
        .item-proportion {
            display: none;
        }

        .item-name {
            left: 0;
            width: 41mm;
        }

        .item-count {
            left: 42mm;
            width: 14mm;
        }

        .item-price {
            left: 56mm;
            width: 12mm;
        }

        .item-amount {
            left: 70mm;
            width: 13mm;
        }
    }
}

</style>
