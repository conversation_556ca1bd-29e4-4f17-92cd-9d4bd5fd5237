<!--exampleData

{
    "organName":"高新大源店",
    "patientName":"X",
    "patient": {
        "name": '阿斯顿',
        "sex": "男",
        "birthday": '1994-03-08',
        "age": {"year": "23" },
        'sn': "123456"
    },
    "doctorName":"刘喜撒哒说",
    "reserveDate": "2022-06-16",
    'barCode': '12312',
    'visitSourceRemark': '呜呜呜呜',
}

-->

<template>
    <div class="registration-tag" :data-lodop-command="lodopCommand">
        <print-row class="registration-tag-head">
            <print-col
                :span="16"
                style="margin-top: 1pt"
            >
                <img
                    v-if="organ.logo"
                    class="logo"
                    :src="organ.logo"
                    alt=""
                />
                <div
                    v-else
                    class="logo"
                ></div>
            </print-col>
            <print-col
                :span="8"
                style="text-align: center;font-size: 14pt;margin-top: 16pt;padding-left: 6pt"
            >
                {{ patient.name && patient.name.slice(0,4) }}
            </print-col>
        </print-row>

        <print-row style="border-top: 1pt solid #000;padding-top: 5pt;font-size: 10pt">
            <print-col
                :span="10"
                style="text-align: center;"
            >
                {{ printData.reserveDate }}
            </print-col>
            <print-col
                :span="7"
            >
                {{ patient.birthday }}
            </print-col>
            <print-col
                :span="7"
                style="text-align: center"
            >
                {{ patient.sex }}&nbsp;
                {{ patient.age && patient.age.year }}岁
            </print-col>
        </print-row>
        <print-row>
            <print-col
                v-if="code"
                :span="24"
                style="text-align: center;"
            >
                <div class="barcode">
                    <div class="barcode-code">
                        {{ printData.visitSourceRemark && printData.visitSourceRemark.slice(0,19) }}
                    </div>
                    <div class="barcode-image">
                        <img
                            :src="barcodeSrc"
                            alt="检验条码"
                        />
                    </div>
                    <div class="barcode-code">
                        {{ code }}
                    </div>
                </div>
            </print-col>
        </print-row>
    </div>
</template>

<script>
    import CommonHandler from "./data-handler/common-handler.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import {formatAge, textToBase64BarCode} from "./common/utils.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";


    import { filterMobile } from "./common/medical-transformat.js";
    import PrintRow from "./components/layout/print-row.vue";
    import PrintCol from "./components/layout/print-col.vue";

    export default {
        name: "PatientTag",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.REGISTRATION_TAG,
        pages: [

            {
                paper: PageSizeMap.TAG75_50,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },

        ],
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            filterMobile,
        },
        props: {
            renderData: {
                type: Object,
            },
        },
        computed: {
            printData() {
                return this.renderData.printData;
            },
            config() {
                return this.renderData.config.patientTag || { };
            },
            patient() {
                return this.printData.patient || {};
            },
            code() {
                return this.to8(this.patient.sn);

            },
            organ() {
                return this.printData.organ || {};
            },
            doctorName() {
                return this.printData.doctorName;
            },
            clinicName() {
                return this.config.title || this.printData.organName;
            },
            barcodeSrc() {
                return textToBase64BarCode(this.code, {width: 1, displayValue: false, margin: 0});
            },
            lodopCommand() {
               const command = `
                    LODOP.ADD_PRINT_IMAGE(13, 13, 230, 58, '<img crossOrigin="anonymous" src="${this.organ.logo}"/>');
                    LODOP.SET_PRINT_STYLEA(0,"Stretch",1);
                    LODOP.SET_PRINT_STYLE("FontSize", 15);
                    LODOP.SET_PRINT_STYLE("Alignment", 3);
                    LODOP.ADD_PRINT_TEXT(33, 120, 140, 15, '${this.patient.name && this.patient.name.slice(0,4)}');

                    LODOP.ADD_PRINT_LINE(77, 0, 77, 280, 0, 1);
                    LODOP.SET_PRINT_STYLE("FontSize", 11);
                    LODOP.SET_PRINT_STYLE("Bold", 1);

                    LODOP.SET_PRINT_STYLE("Alignment", 1);
                    LODOP.ADD_PRINT_TEXT(85, 13, 118, 12, '${this.printData.reserveDate}');
                    LODOP.ADD_PRINT_TEXT(85, 118, 118, 12, '${this.patient.birthday}');

                    LODOP.SET_PRINT_STYLE("Alignment", 3);
                    LODOP.ADD_PRINT_TEXT(85, 175, 82, 12, '${this.patient.sex} ${this.patient.age && this.patient.age.year}岁');
                    LODOP.ADD_PRINT_BARCODE(128, 92, 100, 40, '128A', '${this.code}');
                    LODOP.SET_PRINT_STYLEA(0,'ShowBarText', 0);
                    LODOP.SET_PRINT_STYLE("FontSize", 9);
                    LODOP.ADD_PRINT_TEXT(168, 62, 100, 40, '${this.code}');

                    LODOP.SET_PRINT_STYLE("Alignment", 2);
                    LODOP.SET_PRINT_STYLE("FontSize", 11);
                    LODOP.ADD_PRINT_TEXT(110, 15, 240, 22, '${this.printData.visitSourceRemark && this.printData.visitSourceRemark.slice(0,19) || ''}')
               `;

               return `<lodop-command>${btoa(encodeURIComponent(command))}<lodop-command>`
            }
        },
        methods: {
            formatAge,
            to8(value = '') {
                value === null && (value = '');
                if (value) {
                    value = value.replace(/\n/g, '');
                }
                const srcStr = '000000';
                return srcStr.slice(0, -(`${value}`).length).concat(value);
            },
        }
    }
</script>

<style lang="scss">
@import "./components/layout/print-layout";
*{
    font-family: "Microsoft YaHei", "微软雅黑";
}
.registration-tag {
    background-size: 240mm 140mm;
    font-family: "Microsoft YaHei", "微软雅黑";
}
.registration-tag-head {
    height: 45pt;
}
.logo{
    height: 43pt;
    margin-left: 10pt;
}
.barcode {
    position: relative;
    margin-top: 2pt;
    .barcode-image {
        font-size: 0;
        margin: 4pt;
    }

    .barcode-image img {
        height: 40px;
    }

    .barcode-code {
        text-align: center;
        line-height: 12pt;
        font-size: 10pt;
        margin: 1pt 0;
    }

}
</style>
