<!--exampleData
{
  address: "杭州西湖x2",
  bankAccount: "1212143412312124124123412",
  bankAgent: "工商银行",
  invoiceChecker: "Bubblex",
  invoiceFee: 185.8,
  invoiceOpener: "刘喜",
  invoiceRemark: "2021-09-15就诊",
  patientId: "ffffffff0000000019b298100f340000",
  patientName: "电子发票",
  payee: "刘喜",
  sendRemarkType: 0,
  status: 2,
  taxName: "杭州店",
  taxNum: "***************",
  telephone: "***********",
  type: 1,
}
-->

<template>
    <div>
        <div class="zhejiang-registration-medical-bill-content">
            <div
                v-if="blueInvoiceData"
                style="position: absolute; top: 0.3cm; left: 1.4cm;"
            >
                销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[0] }}
            </div>
            <!--    浙江挂号发票打印-->
            <div
                v-for="it in 2"
                :key="it"
                class="invoice-info"
                :style="{ 'left': `${(it - 1) * 38 + 10}mm` }"
            >
                <div class="patient-item patient-no">
                    {{ printData.no }}
                </div>
                <div class="patient-item name">
                    {{ printData.name }}
                </div>
                <div class="patient-item date">
                    {{ printData.chargeDate }}
                </div>
                <div class="patient-item reg-fee">
                    {{ printData.registrationFee | formatMoney }}
                </div>
                <div class="patient-item treatment-fee">
                    0.00
                </div>

                <div class="reg-item">
                    <div class="patient-no">
                        {{ printData.departmentName }} <span class="big-font">{{ printData.orderNo }}</span>
                    </div>
                    <div class="patient-no">
                        医保号：{{ printData.socialCode }}
                    </div>
                    <div class="patient-no">
                        当日有效，隔日作废
                    </div>
                    <div class="patient-no">
                        收费员：{{ printData.chargedByName }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import { digitUppercase, formatMoney } from './common/utils.js';

    export default {
        name: "ZhejiangInvoice",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.ZHEJIANG_INVOICE,
        filters: {
            formatMoney
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        pages: [
            {
                paper: PageSizeMap.MM90,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            printData() {
                return this.renderData.printData || null;
            },
            blueInvoiceData() {
                return this.printData.blueInvoiceData || null;
            },
        },
    }
</script>
<style lang="scss">
.abc-page_preview {
  background: url("/static/assets/print/zhejiang-reg.png");
  background-size: 90mm 90mm;
  color: #2a82e4;
}

.zhejiang-registration-medical-bill-content {
  position: relative;
  width: 90mm;
  height: 90mm;
  font-size: 9pt;
  //border: 1px solid #1bd1cb;

  .invoice-info {
    position: absolute;
    top: 0;
    left: 0;
    width: 38mm;
    height: 90mm;
  }

  .patient-item,
  .reg-item {
    position: absolute;
  }

  .patient-item {
    left: 15mm;
  }

  .patient-no {
    top: 23mm;
  }

  .name {
    top: 29mm;
  }

  .date {
    top: 35mm;
  }

  .reg-fee {
    top: 41mm;
  }

  .treatment-fee {
    top: 47mm;
  }

  .reg-item {
    top: 50mm;
    left: 2mm;
  }

  .big-font {
    font-size: 12pt;
  }
}
</style>
