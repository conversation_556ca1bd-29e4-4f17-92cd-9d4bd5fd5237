<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '9410ffd3ece8439e9e12c8f3df396bc8',
            chargeFormItems: [
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                    name: '盐知母',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                    specialRequirement: '包煎',
                    ownExpenseFee: 36,
                },
                {
                    id: '7ef3ac794a034b4e952031d4b14b18c1',
                    name: '盐黄柏',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 0.03,
                    specialRequirement: '先煎',
                    ownExpenseFee: 0.63,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16.2,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9072',
                    name: '山药YG',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9073',
                    name: '牡丹皮YG',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9076',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                //     name: '盐知母',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 36.0,
                //     specialRequirement: '包煎',
                // },
                // {
                //     id: '7ef3ac794a034b4e952031d4b14b18c1',
                //     name: '盐黄柏',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 1.0,
                //     doseCount: 1.0,
                //     totalPrice: 0.03,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9072',
                //     name: '山药YG',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9073',
                //     name: '牡丹皮YG',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9076',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },{
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                //     name: '盐知母',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 36.0,
                //     specialRequirement: '包煎',
                // },
                // {
                //     id: '7ef3ac794a034b4e952031d4b14b18c1',
                //     name: '盐黄柏',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 1.0,
                //     doseCount: 1.0,
                //     totalPrice: 0.03,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9072',
                //     name: '山药YG',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9073',
                //     name: '牡丹皮YG',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9076',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
            ],
            sourceFormType: 6,
            specification: '中药饮片',
            doseCount: 1,
            dailyDosage: '1日1剂',
            usage: '煎服',
            freq: '1日3次',
            usageLevel: '每次150ml',
        },

        // {
        //     id: '338adf3126c141e0ab38d5de35e9305902',
        //     chargeFormItems: [
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //     ],
        //     sourceFormType: 2,
        // },

    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号
    diagnosisInfos: [
        {
            code: 'L55.900',
            diseaseType: null,
            name: '晒斑[晒伤]',
        },
    ],

    patientOrderNo: '**********',
    memberCardBalance: null,
    memberCardMobile: '',
    memberCardBeginningBalance: '', // 会员卡原有余额
    healthCardBeginningBalance: '567.68', // 社保卡原有余额
    healthCardOwnerRelationToPatient: '父女', // 持卡人关系
    healthCardBalance: '0.00', // 社保卡余额
    healthCardNo: '********', // 社保卡卡号
    healthCardOwner: '任我行', // 持卡人姓名"
    serialNo: '********', // 门诊流水号"

    healthCardId: '********', // 医保编号
    healthCardAccountPaymentFee: '19.99', // 帐户支付金额
    healthCardFundPaymentFee: 20, // 统筹支付金额
    healthCardOtherPaymentFee: '10', // 其它支付金额
    healthCardCardOwnerType: '职工', // 医保类型 职工 居民 离休干部
    healthCardSelfConceitFee: '11', // 自负金额
    healthCardSelfPayFee: '22', // 自费金额
    personalPaymentFee: '33', // 个人现金支付

    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
    ],

    shebaoPayment: {
        cardId: '********', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            // 青岛数据
            individualAffordabilityLine: '9.9', //个人负担起付线
        },
    },
}
-->

<template>
    <div>
        <div class="qingdao-medical-bill-content">
            <div
                v-if="printData.blueInvoiceCode && printData.blueInvoiceNumber"
                style="position: absolute; top: 3mm; left: 14mm;"
            >
                销项负数 对应正数发票代码：{{ printData.blueInvoiceCode }} 号码：{{ printData.blueInvoiceNumber }}
            </div>
            <refund-icon
                v-if="isRefundBill"
                top="0.6cm"
                left="1.4cm"
            ></refund-icon>

            <block-box
                :top="29.5"
                :left="25"
            >
                {{ patientName }}
            </block-box>

            <!-- 出院时间 -->
            <block-box
                v-if="printData.hospitalPatientOrder"
                :top="29.5"
                :left="83"
            >
                {{ hospitalPatientOrder.dischargeTime | parseTime('y-m-d') }}
            </block-box>
            <block-box
                v-else
                :top="29.5"
                :left="83"
            >
                {{ printData.outHospitalDate }}
            </block-box>

            <block-box
                :top="24.5"
                :left="32"
            >
                {{ currentConfig.institutionName }}
            </block-box>

            <block-box
                :top="37"
                :left="14"
            >
                住院号：{{ printData.caseNumber || (hospitalPatientOrder && hospitalPatientOrder.shebaoInfo && hospitalPatientOrder.shebaoInfo.inHospitalNo) }}
                <!-- 医保类别 -->
                {{ shebaoPayment.cardOwnerType }}
            </block-box>

            <block-box
                :top="37"
                :left="68"
            >
                住院科室：{{ printData.departmentName }}
            </block-box>

            <block-box
                v-if="printData.hospitalPatientOrder"
                :top="44"
                :left="14"
            >
                入院日期：{{ hospitalPatientOrder.registerTime | parseTime('y-m-d') }}
            </block-box>
            <block-box
                v-else
                :top="44"
                :left="14"
            >
                入院日期：{{ printData.inHospitalDate | parseTime('y-m-d') }}
            </block-box>



            <block-box
                v-if="printData.hospitalPatientOrder"
                :top="44"
                :left="68"
            >
                出院日期：{{ hospitalPatientOrder.dischargeTime | parseTime('y-m-d') }}
                共{{ hospitalPatientOrder.inpatientDays }}天
            <!-- 住院天数 -->
            </block-box>

            <block-box
                v-else
                :top="44"
                :left="68"
            >
                出院日期：{{ printData.outHospitalDate | parseTime('y-m-d') }}
                <!-- 住院天数 -->
                共{{ printData.inpatientDays }}天
            </block-box>

            <block-box
                :top="53"
                :left="20"
            >
                <span class="program-item">项目</span>
                <span class="program-item">金额</span>
                <span class="program-item">项目</span>
                <span class="program-item">金额</span>
                <div class="split-line"></div>
            </block-box>
            <div class="items-wrapper">
                <div
                    v-for="(item, index) in medicalBills"
                    :key="index"
                    class="charge-item"
                >
                    <span style="display: inline-block;width: 70pt">{{ item.name }}</span>
                    {{ item.totalFee | formatMoney }}
                </div>
            </div>
            <block-box
                :top="128"
                :left="20"
            >
                <div class="split-line"></div>
            </block-box>
            <block-box
                :top="133"
                :left="14"
            >
                总费用：
                {{ $t('currencySymbol') }}
                <template v-if="printData.receivedPriceWithCareFee">
                    {{ printData.receivedPriceWithCareFee | formatMoney }}
                </template>
                <template v-else>
                    {{ printData.invoiceFee | formatMoney }}
                </template>

                <span style="margin-left: 8px;">
                    大写：
                    <template v-if="printData.receivedPriceWithCareFee">
                        {{ digitUppercase(printData.receivedPriceWithCareFee) }}
                    </template>
                    <template v-else>
                        {{ digitUppercase(printData.invoiceFee) }}
                    </template>
                </span>
            </block-box>


            <block-box
                :top="138"
                :left="14"
            >
                基金支付总额：{{ $t('currencySymbol') }}{{ shebaoPayment.fundTotalAmount || shebaoPayment.fundPaymentFee || '0.00' }}
            </block-box>

            <block-box
                :top="138"
                :left="64"
            >
                统筹基金支付：{{ $t('currencySymbol') }}{{ extraInfo.hifpPay || '0.00' }}
            </block-box>

            <block-box
                :top="143"
                :left="14"
            >
                现金支付：{{ $t('currencySymbol') }}{{ shebaoPayment.cashPay || shebaoPayment.personalPaymentFee || '0.00' }}
            </block-box>

            <block-box
                :top="143"
                :left="64"
            >
                个人账户支付：{{ $t('currencySymbol') }}{{ shebaoPayment.acctPay || shebaoPayment.accountPaymentFee || '0.00' }}
            </block-box>

            <block-box
                :top="148"
                :left="14"
            >
                预缴金额：{{ $t('currencySymbol') }}{{ prepaidSettleSummary.prepaidFee | formatMoney }}
            </block-box>

            <!-- TODO juliet 实收字段待后台给出 -->
            <block-box
                :top="148"
                :left="54"
            >
                补缴：{{ $t('currencySymbol') }}{{ prepaidSettleSummary.repaidFee | formatMoney }}
            </block-box>

            <block-box
                :top="148"
                :left="87"
            >
                退费：{{ $t('currencySymbol') }}{{ prepaidSettleSummary.refundedFee | formatMoney }}
            </block-box>

            <block-box
                :top="157"
                :left="92"
            >
                {{ printData.lastChargedByName || printData.payee }}
            </block-box>
            <!--            <div class="created-date">-->
            <!--                {{ printData.lastChargedTime | parseTime('y-m-d') }}-->
            <!--            </div>-->
        </div>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import clone from "./common/clone.js";
    import { SourceFormTypeEnum } from "./common/constants.js";
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import NationalBillData from "./mixins/national-bill-data.js";

    export default {
        name: "MedicalHospitalBillQingdao",
        components: {
            RefundIcon,
            BlockBox,
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_HOSPITAL_BILL_QINGDAO,
        pages: [
            {
                paper: PageSizeMap.MM126_165_qingdao,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        // 社保总支付的应该是 shebaoPayment.receivedFee
        computed: {
            patientName() {
                return this.patient?.name || this.printData.buyerName;
            },
            chargeTransactions() {
                return this.printData && this.printData.chargeTransactions;
            },
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                return this.isOnlyOnePage ? this.renderPage.slice(0, 1) : this.renderPage
            },

            isOnlyOnePage() {
                return this.splitType === 1 && (this.renderPage.length > 1 || this.extra.isPreview);
            },
            format() {
                return this.config.format;
            },
            // 是否分页打印
            splitType() {
                return this.config[this.format].splitType;
            },
            config() {
                return this.renderData.config.hospitalBillConfig || { hubei: {} };
            },
            currentConfig() {
                return this.config[this.format] || {};
            },
            chineseForms() {
                return this.chargeForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                });
            },
            chineseFormItems() {
                return this.getFormItems(this.chineseForms)
            },
            notChineseForms() {
                return this.chargeForms.filter((form) => {
                    return form.sourceFormType !== SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                });
            },
            hospitalPatientOrder() {
                return this.printData.hospitalPatientOrder || {}
            },
            diagnosisStr() {
                let extendDiagnosisInfos = [];
                if(this.printData.hospitalPatientOrder) {
                    extendDiagnosisInfos = this.printData.hospitalPatientOrder.extendDiagnosisInfos;
                } else {
                    extendDiagnosisInfos = this.printData.extendDiagnosisInfos || [];
                }
                if (extendDiagnosisInfos &&
                    Array.isArray(extendDiagnosisInfos) &&
                    extendDiagnosisInfos.filter((it) => it.value.length).length) {
                    const _arr = [];
                    extendDiagnosisInfos.forEach((item) => {
                        item.value.forEach((it) => {
                            it.name && _arr.push(it.name);
                        });
                    });
                    return _arr.join('，');
                }
                return '';
            },
            notChineseFormItems() {
                return this.getFormItems(this.notChineseForms)
            },
            renderChineseFormItems() {
                return this.spliceFormItems(this.chineseFormItems, 13, 9, true);
            },
            renderNotChineseFormItems() {
                return this.spliceFormItems(this.notChineseFormItems, 13, 9, false);
            },
            renderPage() {
                return this.renderChineseFormItems.concat(this.renderNotChineseFormItems);
            },
        },
        methods: {
            getFormItems(form) {
                let res = [];
                form.forEach((form) => {
                    form.chargeFormItems && form.chargeFormItems.forEach((formItem) => {
                        if(this.composeChildrenConfig === 2) {
                            if ((formItem.productType === 3 || formItem.composeType > 0) && formItem.composeChildren && formItem.composeChildren.length) {
                                res = res.concat(formItem.composeChildren);
                            } else {
                                res.push(formItem);
                            }
                        } else if(this.composeChildrenConfig === 1) {
                            res.push(formItem);
                            if ((formItem.productType === 3 || formItem.composeType > 0) && formItem.composeChildren && formItem.composeChildren.length) {
                                res = res.concat(formItem.composeChildren);
                            }
                        } else {
                            res.push(formItem);
                        }
                    });
                });
                return res;
            },
            spliceFormItems(formItems, pageCount, firstPageCount, isChineseForm) {
                const page = [];
                const cacheFormItems = clone(formItems);
                let pageIndex = 0;
                while(cacheFormItems && cacheFormItems.length) {
                    pageIndex++;
                    let endIndex = 0;
                    if(pageIndex === 1) {
                        endIndex = cacheFormItems.length > firstPageCount ? firstPageCount : cacheFormItems.length;
                    } else {
                        endIndex = cacheFormItems.length > pageCount ? pageCount : cacheFormItems.length;
                    }
                    page.push({
                        formItems: cacheFormItems.splice(0, endIndex),
                        pageIndex,
                        isChineseForm,
                    });
                }
                return page;
            },
        },
    }
</script>
<style lang="scss">
* {
    padding: 0;
    margin: 0;
}

*::-webkit-scrollbar {display:none}

.abc-page_compatible {
    .invoice {
        left: 128mm!important;
    }
}

.qingdao-medical-bill-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 126mm;
    height: 165mm;
    overflow: hidden;
    font-size: 9pt;
    line-height: 11pt;

    .name,
    .sex,
    .card-type,
    .card-id,
    .shebao-wrapper,
    .total-fee,
    .total-fee-number,
    .organ,
    .card-number,
    .charger,
    .items-wrapper,
    .doctor-site,
    .patient-type,
    .med-type,
    .created-date {
        position: absolute;
    }

    .med-type {
        top: 14mm;
        left: 31mm;
    }

    .card-number,
    .name,
    .sex {
        top: 29.5mm;
    }

    .name {
        left: 25mm;
    }
    .card-number {
        left: 33mm;
    }
    .sex {
        left: 153mm;
    }
    .doctor-site,
    .patient-type,
    .card-type {
        top: 37mm;
    }
    .card-type {
        left: 153mm;
    }
    .patient-type {
        left: 112mm;
    }
    .doctor-site {
        left: 33mm;
    }
    .program-item {
        display: inline-block;
        width: 70pt;
    };
    .split-line {
        position: relative;
        width: 290pt;
        height: 5pt;
        left: -20pt;
        border-bottom: #1d1f21 solid 1.2pt;
    }

    .items-wrapper {
        top: 63mm;
        left: 20mm;
        width: 123mm;
        height: 34mm;
    }

    .charge-item {
        display: inline-block;
        width: 53mm;
        margin-bottom: 3pt;
        word-break: keep-all;
        white-space: nowrap;
    }

    .social-pay {
        display: inline-block;
        width: 33mm;

        &.part-pay {
            width: 50mm;
        }
    }

    .organ,
    .charger,
    {
        top: 24mm;
    }

    .organ {
        left: 34mm;
        width: 49mm;
    }

    .charger {
        left: 105mm;
    }

    .created-date {
        top: 29.5mm;
        left: 86mm;
    }
}

.abc-page_preview {
    background: url("/static/assets/print/qingdao-hospital.jpg");
    background-size: 126mm 165mm;
    color: #2a82e4;

}
</style>
