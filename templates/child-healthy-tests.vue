<!--exampleData
{
  data: [
    {
        "isPop": false,
        "kind": "GroupComponent",
        "children": [
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88bd91eccad00",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88b7e8eccad01",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b44d8ccad02",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88b94ffccacff",
                "label": "在家里会帮忙做简单的事",
                "tips": {
                    "content": null
                }
            },
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88b3325ccad04",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88beefeccad05",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88be413ccad06",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88bbf39ccad03",
                "label": "与检查者玩球",
                "tips": {
                    "content": null
                }
            },
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88bed2cccad08",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88bf6f2ccad09",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88be893ccad0a",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88b6978ccad07",
                "label": "用杯子喝水",
                "tips": {
                    "content": null
                }
            },
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88b6ed1ccad0c",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88b59feccad0d",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b983accad0e",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88b5ef8ccad0b",
                "label": "模仿做家务",
                "tips": {
                    "content": null
                }
            }
        ],
        "resultRules": [
            {
                "scoreRang": {
                    "begin": 0,
                    "end": 1
                },
                "_id": "5eec32f2e6b88b4e27ccacfd",
                "result": "个人-社交能力发育正常。相应年龄段的孩子在该能区中存在1项以下的迟缓项目。",
                "suggest": "在个人-社交发育正常的情况下，仍需有意识的加强孩子对周围人们的应答能力和料理自己生活的能力，积极教育给孩子良好的社交知识，使孩子能够熟练掌握个人-社交能力，从而进一步提升孩子的社交能力。"
            },
            {
                "scoreRang": {
                    "begin": 2,
                    "end": 1000
                },
                "_id": "5eec32f2e6b88b6563ccacfe",
                "result": "个人-社交能力发育相对迟缓。相应年龄段的孩子在该能区中存在2项或更多迟缓，需要进一步观察，并定期进行复查。",
                "suggest": "营造良好的社交环境，有意识的培养儿童的个人-社交能力。如多与儿童进行沟通，积极回应儿童的各种表达，鼓励宝宝参加集体游戏。父母在社交行为上以身作则，为儿童树立良好的榜样。促使孩子个人-社会能力的正常发育。"
            }
        ],
        "_id": "5eec32f2e6b88b3f8bccacfc",
        "label": "社交"
    },
    {
        "isPop": false,
        "kind": "GroupComponent",
        "children": [
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88b5f31ccad13",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88b0cfdccad14",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b8565ccad15",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88baf29ccad12",
                "label": "从瓶中倒出小丸（按示例）",
                "tips": {
                    "content": null
                }
            },
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88bb057ccad17",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88bf832ccad18",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b1a58ccad19",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88b31e4ccad16",
                "label": "自发乱画",
                "tips": {
                    "content": null
                }
            },
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88bd728ccad1b",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88b5817ccad1c",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88ba6b1ccad1d",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88bfd4accad1a",
                "label": "搭两层塔",
                "tips": {
                    "content": null
                }
            },
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88b5fe9ccad1f",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88b79e6ccad20",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b1b4eccad21",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88b1953ccad1e",
                "label": "倒小丸-自发的",
                "tips": {
                    "content": null
                }
            }
        ],
        "resultRules": [
            {
                "scoreRang": {
                    "begin": 0,
                    "end": 1
                },
                "_id": "5eec32f2e6b88b78beccad10",
                "result": "精细运动发育正常。相应年龄段的孩子在该能区中存在1项以下的迟缓项目。",
                "suggest": "对孩子的精细运动能力进行巩固训练和进一步提升。以孩子现有的精细运动能力为基础，结合孩子自身的发育状况，逐步给孩子进行下一阶段的精细运动训练。"
            },
            {
                "scoreRang": {
                    "begin": 2,
                    "end": 1000
                },
                "_id": "5eec32f2e6b88bd9feccad11",
                "result": "精细运动发育相对迟缓。相应年龄段的孩子在该能区中存在2项或更多迟缓，需要进一步观察，并定期进行复查。",
                "suggest": "提供给儿童更多的动手机会，避免完全辅助儿童的生活。有意识的通过刺球、木棍、玩具、吃食以及各种游戏来引导宝宝进行各种精细运动，增强宝宝的动手能力，促进宝宝的手眼协调和双手协调。"
            }
        ],
        "_id": "5eec32f2e6b88b0485ccad0f",
        "label": "精细运动"
    },
    {
        "isPop": false,
        "kind": "GroupComponent",
        "children": [
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88be700ccad26",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88bc052ccad27",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b0c41ccad28",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88ba236ccad25",
                "label": "会除了爸爸妈妈之外的第三个词语",
                "tips": {
                    "content": null
                }
            },
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88b9415ccad2a",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88b54a0ccad2b",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b1824ccad2c",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88b3767ccad29",
                "label": "指出一个说出名称的身体部分",
                "tips": {
                    "content": null
                }
            }
        ],
        "resultRules": [
            {
                "scoreRang": {
                    "begin": 0,
                    "end": 1
                },
                "_id": "5eec32f2e6b88b26d7ccad23",
                "result": "语言能力发育正常。相应年龄段的孩子该能区中存在1项以下的迟缓项目。",
                "suggest": "维持孩子语言发育的良好环境，增加孩子的语言信息，加强孩子对语言的理解和表达能力。"
            },
            {
                "scoreRang": {
                    "begin": 2,
                    "end": 1000
                },
                "_id": "5eec32f2e6b88b7bd1ccad24",
                "result": "儿童语言发育相对迟缓。相应年龄段的孩子在该能区中存在2项或更多迟缓，需要进一步观察，并定期进行复查。",
                "suggest": "为宝宝创造良好的语言环境。以加强父母与宝宝之间的语言交流，积极回应宝宝的问题，有意识的给宝宝讲故事，听儿歌，教宝宝说规范的语言等方式，来促进宝宝的语言正常发育。"
            }
        ],
        "_id": "5eec32f2e6b88b9500ccad22",
        "label": "语言"
    },
    {
        "isPop": false,
        "kind": "GroupComponent",
        "children": [
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88b7e8cccad31",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88bf09dccad32",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b20daccad33",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88bf8a8ccad30",
                "label": "走得好",
                "tips": {
                    "content": null
                }
            },
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88b1d5eccad35",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88b1b24ccad36",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b06f4ccad37",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88b6ef1ccad34",
                "label": "走，能向后倒退",
                "tips": {
                    "content": null
                }
            },
            {
                "required": true,
                "isPop": false,
                "kind": "Radio",
                "options": [
                    {
                        "_id": "5eec32f2e6b88b6763ccad39",
                        "name": "可以做到",
                        "value": "0"
                    },
                    {
                        "_id": "5eec32f2e6b88b6a9bccad3a",
                        "name": "不可以做到",
                        "value": "1"
                    },
                    {
                        "_id": "5eec32f2e6b88b6cd6ccad3b",
                        "name": "不配合",
                        "value": "0"
                    }
                ],
                "_id": "5eec32f2e6b88b78daccad38",
                "label": "弯腰能再站起来",
                "tips": {
                    "content": null
                }
            }
        ],
        "resultRules": [
            {
                "scoreRang": {
                    "begin": 0,
                    "end": 1
                },
                "_id": "5eec32f2e6b88b57eeccad2e",
                "result": "大运动发育正常。相应年龄段的孩子在该能区中存在1项以下的迟缓项目。",
                "suggest": "在大运动发育正常的基础上，在孩子的日常活动中对其进行巩固训练，通过合理的锻炼来加强孩子的力量和四肢协调。"
            },
            {
                "scoreRang": {
                    "begin": 2,
                    "end": 1000
                },
                "_id": "5eec32f2e6b88b1dcfccad2f",
                "result": "大运动发育相对迟缓。相应年龄段的孩子在该能区中存在2项或更多迟缓，需要进一步观察，并定期进行复查。",
                "suggest": "不同宝宝之间存在个体差异，父母可以对照基本规律合理引导孩子进行大运动训练。为孩子提供大运动的相应支持，引导孩子进行大运动的主动性。如鼓励孩子参与相关游戏，陪伴孩子一起进行大运动等，引导孩子学习新的动作，参与日常活动。"
            }
        ],
        "_id": "5eec32f2e6b88b7decccad2d",
        "label": "大运动"
    },
    {
        "isPop": true,
        "type": "String",
        "kind": "Execute",
        "_id": "5eec32f2e6b88b0017ccad3c",
        "key": "questionName",
        "formula": "${form.name}"
    },
    {
        "isPop": true,
        "type": "Number",
        "kind": "Execute",
        "_id": "5eec32f2e6b88b31c7ccad3d",
        "key": "totalScore",
        "formula": "${form.totalScore}"
    },
    {
        "isPop": true,
        "type": "Number",
        "kind": "Execute",
        "_id": "5eec32f2e6b88b83c7ccad3e",
        "key": "score",
        "formula": "${form.score}"
    },
    {
        "isPop": true,
        "type": "Array",
        "kind": "Execute",
        "_id": "5eec32f2e6b88b5a7cccad3f",
        "key": "groups",
        "formula": "${form.groupResults}"
    },
    {
        "isPop": true,
        "type": "Number",
        "kind": "Execute",
        "_id": "5eec32f2e6b88b64c5ccad40",
        "key": "hasRule",
        "formula": "1"
    }
],
name: "表格"
}
-->

<template>
  <div class="print-question-table">
    <h1>{{ printData.name }}</h1>
    <form-question v-model="value" data-type="mix-box">
      <question>
        <question-table :data="printData.data"></question-table>
      </question>
    </form-question>
  </div>
</template>

<script>
import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
import PageSizeMap, {Orientation} from "../share/page-size.js";
import PrintHandler from './data-handler/common-handler.js';
import {FormQuestion, Question, QuestionTable} from './components/child-health/intelligence-assessment/form-question';

export default {
  name: 'PrintHealthReport',
  components: {
    Question,
    QuestionTable,
    FormQuestion
  },
  DataHandler: PrintHandler,
  businessKey: PrintBusinessKeyEnum.CHILD_HEALTHY_TESTS,
  pages: [
    {
      paper: PageSizeMap.A4,
      isRecommend: true,
      defaultOrientation: Orientation.portrait, // 默认方向
      defaultHeightLevel: null,
    },
    {
      paper: PageSizeMap.A5,
      isRecommend: false,
      defaultOrientation: Orientation.portrait, // 默认方向
      defaultHeightLevel: null,
    },
  ],
  props: {
    renderData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      value: {}
    }
  },
  computed: {
    printData() {
      return this.renderData.printData || {};
    },
  }
};
</script>

<style lang="scss">
.print-question-table {
  font-size: 14px;
  font-weight: 400;

  .tips {
    display: none;
  }

  h1 {
    line-height: 40pt;
    text-align: center;
    font-size: 28px;
    font-weight: 400;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;

    td {
      padding: 0 8pt;
    }

    tr > td:last-child {
      min-width: 120pt;
    }

    tr, td {
      page-break-inside: avoid;
    }

    .question-radio {
      font-size: 0;

      .label {
        line-height: 14px;
        vertical-align: middle;
        font-size: 14px;
        margin-right: 16px;
        display: inline-block;
      }

      .circle {
        display: inline-block;
        height: 12px;
        width: 12px;
        border-radius: 50%;
        border: 1pt solid #000;
        vertical-align: middle;
        margin-right: 6px;
      }
    }
  }

  .form-question {
    td {
      padding: 6pt 8pt;
    }
  }
}
</style>

