<template>
    <div>
        <div class="medical-hospital-bill-yunnan">
            <block-box
                v-if="printData.blueInvoiceCode && printData.blueInvoiceNumber"
                :left="24"
                :top="4"
            >
                销项负数 对应正数发票代码：{{ printData.blueInvoiceCode }} 号码：{{ printData.blueInvoiceNumber }}
            </block-box>

            <!-- 票据代码 -->
            <block-box
                :top="32.5"
                :left="37"
            >
                {{ (normalInvoice && normalInvoice.invoiceCode) ? normalInvoice.invoiceCode : '' }}
            </block-box>

            <!-- 票据号码 -->
            <block-box
                :top="32.5"
                :left="162"
            >
                {{ (normalInvoice && normalInvoice.invoiceNumber) ? normalInvoice.invoiceNumber : '' }}
            </block-box>

            <!-- 交款人信用代码 -->
            <block-box
                :top="39.5"
                :left="55"
            >
                {{ printData.buyerTaxNum }}
            </block-box>

            <!-- 交款人 -->
            <block-box
                :top="43"
                :left="34"
            >
                {{ printData.buyerName }}
            </block-box>

            <!-- 开票日期 -->
            <block-box
                :top="43"
                :left="162"
            >
                {{ (printData.invoiceDate || new Date()) | parseTime('y-m-d h:i:s') }}
            </block-box>

            <!-- 费用明细内容 -->
            <block-box
                :top="56"
                :left="29"
                :font="8"
                style="width: 152mm; height: 33mm;"
            >
                <div
                    v-for="(item, index) in medicalBills"
                    :key="index"
                    style="display: inline-block;"
                    :style="{ width: index % 2 === 0 ? '54%' : '46%' }"
                >
                    <span
                        style="display: inline-block;"
                        :style="{ width: index % 2 === 0 ? '23%' : '30%' }"
                    >{{ item.name }}</span>
                    <span style="display: inline-block; width: 12%; text-align: right;">{{ item.totalCount }}{{ item.unit }}</span>
                    <span
                        style="display: inline-block; text-align: right;"
                        :style="{ width: index % 2 === 0 ? '34%' : '39%' }"
                    >{{ item.totalFee | formatMoney }}</span>
                </div>
            </block-box>

            <block-box
                :top="91"
                :left="46"
            >
                <template v-if="printData.receivedPriceWithCareFee">
                    {{ digitUppercase(printData.receivedPriceWithCareFee) }}
                </template>
                <template v-else>
                    {{ digitUppercase(printData.invoiceFee) }}
                </template>
            </block-box>

            <block-box
                :top="91"
                :left="146"
            >
                <template v-if="printData.receivedPriceWithCareFee">
                    {{ printData.receivedPriceWithCareFee | formatMoney }}
                </template>
                <template v-else>
                    {{ printData.invoiceFee | formatMoney }}
                </template>
            </block-box>

            <block-box
                :top="99"
                :left="32"
                :font="8"
                style="width: 165mm; height: 21mm;"
            >
                <div
                    v-for="(item, idx) in settlementInfos"
                    :key="`medical-hospital-bill-yunnan-${idx}`"
                    class="medical-hospital-bill-yunnan-settlement-item"
                    style="width: 24%;"
                    :style="item.customStyles"
                >
                    {{ item.label }}：{{ item.value }}
                </div>
            </block-box>

            <!-- 收费单位 -->
            <block-box
                :top="119"
                :left="45"
            >
                {{ currentConfig.institutionName }}
            </block-box>

            <!-- 收款人 -->
            <block-box
                :top="119"
                :left="157"
            >
                {{ printData.payee }}
            </block-box>
        </div>
    </div>
</template>

<script>
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import CommonHandler from './data-handler/common-handler.js';
    import BillDataMixins from './mixins/bill-data';
    import NationalBillData from "./mixins/national-bill-data.js";
    import { formatMoney, parseTime } from './common/utils';

    export default {
        name: "MedicalHospitalYunnan",
        components: {
            BlockBox,
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_HOSPITAL_BILL_YUNNAN,
        pages: [
            {
                paper: PageSizeMap.MM210_127_HOSPITAL_YUNNAN,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        // 社保总支付的应该是 shebaoPayment.receivedFee
        computed: {
            format() {
                return this.config.format;
            },
            config() {
                return this.renderData.config.hospitalBillConfig || { yunnan: {} };
            },
            currentConfig() {
                return this.config[this.format] || {};
            },
            settlementInfos() {
                return [
                    {
                        label: '住院号',
                        value: this.printData.caseNumber || '',
                    },
                    {
                        label: '科室',
                        value: `${this.printData.departmentName || ''}${this.printData.bedNo ? ' ' + this.printData.bedNo + '床' : ''}`,
                    },
                    {
                        label: '住院日期',
                        value: `${parseTime(this.printData.inHospitalDate, 'y-m-d')} ~ ${parseTime(this.printData.outHospitalDate, 'y-m-d')} 共${this.printData.inpatientDays}天`,
                        customStyles: {
                            width: '50%',
                        },
                    },
                    {
                        label: '预缴金额',
                        value: formatMoney(this.prepaidSettleSummary.prepaidFee),
                    },
                    {
                        label: '补缴金额',
                        value: formatMoney(this.prepaidSettleSummary.repaidFee),
                    },
                    {
                        label: '退费金额',
                        value: formatMoney(this.prepaidSettleSummary.refundedFee),
                    },
                    {
                        label: '医院垫支',
                        value: formatMoney(this.prepaidSettleSummary.hospitalAdvancePayment),
                    },
                    {
                        label: '医保统筹支付',
                        value: formatMoney(this.shebaoPayment.fundPaymentFee),
                    },
                    {
                        label: '个人账户支付',
                        value: formatMoney(this.shebaoPayment.accountPaymentFee),
                    },
                    {
                        label: '个人自付',
                        value: formatMoney(this.shebaoPayment.selfPaymentFee),
                    },
                    {
                        label: '个人自费',
                        value: formatMoney(this.shebaoPayment.selfHandledPaymentFee),
                    },
                    {
                        label: '个人账户余额',
                        value: formatMoney(this.shebaoPayment.cardBalance),
                    },
                    {
                        label: '统筹累计支付',
                        value: formatMoney(this.extraInfo.optFundPayCum),
                    },
                    {
                        label: '医疗救助支付',
                        value: formatMoney(this.extraInfo.mafPay),
                    },
                    {
                        label: '大病保险支付',
                        value: formatMoney(this.extraInfo.hifmiPay),
                    },
                ];
            },
        },
        methods: {
            parseTime,
        },
    }
</script>

<style lang="scss">
.medical-hospital-bill-yunnan {
    position: relative;
    width: 210mm;
    height: 127mm;

    .medical-hospital-bill-yunnan-settlement-item {
        display: inline-block;
    }
}

.abc-page_preview {
    background: url("/static/assets/print/hospital-bill/medical-hospital-bill-yunnan.png");
    background-size: 210mm 127mm;
    color: #2a82e4;
}
</style>
