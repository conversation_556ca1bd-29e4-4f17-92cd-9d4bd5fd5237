<template>
    <div class="pe-group-report-wrapper">
        <template v-if="isPreviewCover">
            <pe-group-report-cover
                :config="printConfig"
                :print-data="printData"
            ></pe-group-report-cover>
        </template>

        <template v-else-if="isPreviewIntroduction">
            <pe-group-report-header
                :config="printConfig"
                :print-data="printData"
            ></pe-group-report-header>
            <pe-group-report-guide :config="printConfig"></pe-group-report-guide>
            <pe-group-report-footer :config="printConfig"></pe-group-report-footer>
        </template>

        <template v-else>
            <!--封面-->
            <pe-group-report-cover
                :config="printConfig"
                :print-data="printData"
            ></pe-group-report-cover>

            <!--页头-->
            <pe-group-report-header
                :config="printConfig"
                :print-data="printData"
            ></pe-group-report-header>

            <!--导读-->
            <pe-group-report-guide
                :config="printConfig"
                :print-data="printData"
            ></pe-group-report-guide>

            <!--概述-->
            <template v-if="isShowSummary">
                <div data-type="new-page"></div>
                <pe-group-summary :print-data="printData"></pe-group-summary>
            </template>

            <!--年龄阶段分布情况-->
            <template v-if="isShowAgeStage || isShowSignSituation">
                <div data-type="new-page"></div>

                <pe-group-age-stage
                    :print-data="printData"
                    :is-show-age-stage="isShowAgeStage"
                    :is-show-sign-situation="isShowSignSituation"
                ></pe-group-age-stage>
            </template>

            <!--异常统计-->
            <template v-if="isShowAbnormalStatistic && !!abnormalStatisticOptionList.length">
                <div data-type="new-page"></div>

                <template
                    v-for="(abnormalStatisticOption, aIdx) in abnormalStatisticOptionList"
                >
                    <div
                        id="abnormal-statistic-position-mark"
                        :key="aIdx"
                        class="title-1"
                    >
                        异常统计
                    </div>

                    <abc-print-space
                        :key="aIdx"
                        :value="16"
                    ></abc-print-space>
                    
                    <statistic-rect
                        :key="aIdx"
                        :options="abnormalStatisticOption"
                        style="width: calc(100% - 100pt)"
                    ></statistic-rect>

                    <div
                        :key="aIdx"
                        data-type="new-page"
                    ></div>
                </template>
            </template>

            <!--异常明细-->
            <template v-if="isShowAbnormalDetail && !!abnormalDetailList.length">
                <div
                    v-if="(isShowAbnormalStatistic && !abnormalStatisticOptionList.length) || !isShowAbnormalStatistic"
                    data-type="new-page"
                ></div>

                <div data-type="multiply-sample-table">
                    <div
                        id="abnormal-detail-position-mark"
                        class="title-1"
                    >
                        异常明细
                    </div>

                    <abc-print-space :value="16"></abc-print-space>

                    <template v-for="(abnormalDetail, adIdx) in abnormalDetailList">
                        <div
                            :key="`abnormalDetail-${adIdx}`"
                            data-type="title-main"
                        >
                            <div
                                class="title-2"
                            >
                                {{ abnormalDetail.title }}
                            </div>

                            <abc-print-space
                                :key="adIdx"
                                :value="12"
                            ></abc-print-space>
                        </div>

                        <template v-for="(groupItem, gIdx) in abnormalDetail.groupList">
                            <div
                                :key="`abnormalDetail-sub-${gIdx}`"
                                data-type="title-sub"
                            >
                                <div
                                    :key="gIdx"
                                    class="item-statistic-title"
                                >
                                    <span class="circle"></span>

                                    <span>
                                        <span style="font-weight: 600">
                                            {{ groupItem.name }}
                                        </span>
                                        <span
                                            v-if="groupItem.tagName"
                                            :style="`margin: 0 4pt 0 2pt;color: ${groupItem.tagColor};fontWeight: 600;`"
                                        >
                                            {{ groupItem.tagName }}
                                        </span>
                                        （<span class="person-count">{{ groupItem.count }}</span>人 检出率
                                        <span class="abnormal-percentage">{{ groupItem.percentage }}</span>）
                                    </span>
                                </div>

                                <abc-print-space
                                    :value="8"
                                ></abc-print-space>
                            </div>

                            <exam-detail-table
                                :key="`abnormalDetail-table-${gIdx}`"
                                :data-source="groupItem.tableList"
                                :type="groupItem.type"
                            ></exam-detail-table>

                            <abc-print-space
                                v-if="gIdx !== abnormalDetail.groupList.length - 1"
                                :key="`abnormalDetail-table-space-${gIdx}`"
                                data-type="table-space"
                                :value="12"
                            ></abc-print-space>
                        </template>

                        <abc-print-space
                            v-if="adIdx !== abnormalDetailList.length - 1"
                            :key="`abnormalDetail-space-${adIdx}`"
                            data-type="group-space"
                            :value="24"
                        ></abc-print-space>
                    </template>
                </div>
            </template>

            <!--结束语-->
            <div
                data-type="new-page"
            ></div>

            <pe-group-report-end-guide
                :config="printConfig"
                :print-data="printData"
            ></pe-group-report-end-guide>

            <!--页尾-->
            <pe-group-report-footer
                :config="printConfig"
            ></pe-group-report-footer>
        </template>
    </div>
</template>

<script>
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import {PeGroupHandler} from "./data-handler/pe-group-handler";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import PeGroupReportCover from "./components/pe-group-report/cover.vue";
    import PeGroupReportHeader from "./components/pe-group-report/header.vue";
    import PeGroupReportGuide from "./components/pe-group-report/guide.vue";
    import PeGroupReportFooter from "./components/pe-group-report/footer.vue";
    import StatisticRect from "./components/pe-group-report/statistic-rect.vue";
    import AbcPrintSpace from "./components/layout/space.vue";
    import PeGroupSummary from "./components/pe-group-report/summary.vue";
    import PeGroupAgeStage from "./components/pe-group-report/age-stage.vue";
    import ExamDetailTable from "./components/pe-group-report/detail-table.vue";
    import PeGroupReportEndGuide from "./components/pe-group-report/end-guide.vue";
    import AbnormalStatistic from "./components/pe-group-report/mixins/abnormal-statistic";
    import AbnormalDetail from "./components/pe-group-report/mixins/abnormal-detail";

    export default {
        name: 'PEGroupReport',

        DataHandler: PeGroupHandler,

        components: {
            PeGroupReportEndGuide,
            ExamDetailTable,
            PeGroupAgeStage,
            PeGroupSummary,
            AbcPrintSpace,
            StatisticRect,
            PeGroupReportGuide,
            PeGroupReportCover,
            PeGroupReportHeader,
            PeGroupReportFooter,
        },

        mixins: [AbnormalStatistic, AbnormalDetail],

        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],

        businessKey: PrintBusinessKeyEnum.PE_GROUP_REPORT,

        getInjectGlobalStyle(data, config) {
            const background = config.peGroupReport?.cover?.background;
            return `
                .abc-page:first-child {
                    background-image: url('${background}');
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: cover;
                }

                .abc-page-content {
                    overflow: hidden;
                }

                .abc-page-content > .abc-print-space:nth-child(3) {
                    display: none;
                }
            `
        },

        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },

            extra: {
                type: Object,
                default() {
                    return {}
                }
            },
        },

        computed: {
            printData() {
                console.log(this.renderData, 'peGroupReport renderData');
                return this.renderData.printData;
            },

            printConfig() {
                return this.renderData.config?.peGroupReport || {
                    cover: {
                        organInfo: {},
                    },
                    introduction: {},
                    mainBody: {
                        content: {},
                    }
                };
            },
            // 是否展示概要
            isShowSummary() {
                return !!this.printConfig.mainBody.content.abstract;
            },
            // 是否展示年龄阶段
            isShowAgeStage() {
                return !!this.printConfig.mainBody.content.ageStage;
            },
            // 是否展示异常统计
            isShowAbnormalStatistic() {
                return !!this.printConfig.mainBody.content.abnormalStat;
            },
            // 是否展示异常明细
            isShowAbnormalDetail() {
                return !!this.printConfig.mainBody.content.abnormalDetail;
            },
            // 是否展示到检情况
            isShowSignSituation() {
                return !!this.printConfig.mainBody.content.examinedStat;
            },

            // 是否预览封面
            isPreviewCover() {
                return this.extra.isPreviewCover;
            },
            // 是否预览介绍页
            isPreviewIntroduction() {
                return this.extra.isPreviewIntroduction;
            },
        },

        mounted() {
            console.log(this.renderData, 'peGroupReport renderData');
        }
    }
</script>

<style lang="scss">
.title-1 {
    color: #37393F;
    font-size: 16pt;
    font-weight: 600;
    line-height: 18pt; /* 112.5% */
    text-align: center;
}

.title-2 {
    color: #000000;
    font-size: 12pt;
    font-style: normal;
    font-weight: 600;
    line-height: 12pt; /* 100% */
}

.item-statistic-title {
    color: #000000;
    font-size: 10pt;
    font-weight: 400;
    line-height: 12pt; /* 120% */
    display: flex;
    align-items: center;

    .circle {
        width: 4pt;
        height: 4pt;
        border-radius: 4pt;
        margin-right: 3pt;
        border: 1pt solid #1673EF;
    }

    .person-count,
    .abnormal-percentage {
        color: #1673EF;
        font-weight: 600;
    }
}
</style>