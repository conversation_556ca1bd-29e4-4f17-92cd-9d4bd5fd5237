<!--exampleData

{
    "patient": {
        "id": "ffffffff000000001bc17db00f42e000",
        "name": "打印重构",
        "namePy": null,
        "namePyFirst": null,
        "birthday": "1999-11-04",
        "mobile": null,
        "sex": "男",
        "idCard": null,
        "isMember": 0,
        "age": {
            "year": 22,
            "month": 0,
            "day": 0
        },
        "address": null,
        "sn": null,
        "remark": null,
        "profession": null,
        "company": null,
        "patientSource": null,
        "tags": null,
        "wxOpenId": null,
        "wxHeadImgUrl": null,
        "wxNickName": null,
        "wxBindStatus": null,
        "isAttention": null,
        "shebaoCardInfo": null,
        "childCareInfo": null,
        "chronicArchivesInfo": null
    },
    "organ": {
        "id": "fff730ccc5ee45d783d82a85b8a0e52d",
        "name": "高新大源店",
        "shortName": "高新大源店",
        "addressDetail": "趵突泉",
        "contactPhone": "18910121190",
        "logo": "https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/headimg_dev/rGjBGBN61NXILXt6Yu2Z2OZNSGYXOcnM_1579403432353.jpg",
        "category": "社区卫生服务站"
    },
    "diagnosis": "暂无现病史暂无现病史，感冒病",
    "syndrome": "表寒里饮，肺热咳嗽",
    "departmentName": "www",
    "doctorName": "张雪峰",
    "doctorSignImgUrl": "https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/signature/5VfqVg93y8fH8mqlZX6sA9fnPGFXq3zJ_1582885332942",
    "healthCardNo": null,
    "healthCardPayLevel": null,
    "executeForms": [
        {
            "type": 3,
            "executeFormItems": [
                {
                    "groupId": null,
                    "days": 1,
                    "groupItems": [
                        {
                            "name": "针灸减肥",
                            "medicineCadn": null,
                            "tradeName": "针灸减肥",
                            "unitCount": 1,
                            "unitPrice": 200,
                            "totalPrice": 200,
                            "unit": "次",
                            "composeType": 0,
                            "productInfo": {
                                "id": "817821b5635e4cdbbc9ff4c2db2b1ef5",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "针灸减肥",
                                "displayName": "针灸减肥",
                                "displaySpec": "次",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 22,
                                "type": 4,
                                "subType": 1,
                                "pieceNum": 1,
                                "pieceUnit": null,
                                "packageUnit": "次",
                                "dismounting": 0,
                                "medicineCadn": null,
                                "extendSpec": null,
                                "position": "",
                                "piecePrice": null,
                                "packagePrice": 200,
                                "packageCostPrice": 0,
                                "inTaxRat": 0,
                                "outTaxRat": 0,
                                "stockPieceCount": 0,
                                "stockPackageCount": 0,
                                "needExecutive": 1,
                                "smartDispense": 0,
                                "shortId": "**********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2018-06-27T01:41:22Z",
                                "combineType": 0,
                                "medicalFeeGrade": 0,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 200,
                                "chainPackageCostPrice": 0,
                                "pieceCount": 0,
                                "packageCount": 0,
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "days": 1,
                            "executedCount": 0,
                            "remark": ""
                        }
                    ],
                    "composeType": 0,
                    "remark": "",
                    "created": "2021-11-04T08:56:23Z",
                    "totalPrice": 200
                },
                {
                    "groupId": null,
                    "days": 1,
                    "groupItems": [
                        {
                            "name": "计算机X线摄影（Computed Radiography, CR）",
                            "medicineCadn": "",
                            "tradeName": "计算机X线摄影（Computed Radiography, CR）",
                            "unitCount": 1,
                            "unitPrice": 120,
                            "totalPrice": 120,
                            "unit": "次",
                            "composeType": 0,
                            "productInfo": {
                                "id": "f42cf2bf6c634f93aec821a9465c66e1",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "计算机X线摄影（Computed Radiography, CR）",
                                "displayName": "计算机X线摄影（Computed Radiography, CR）",
                                "displaySpec": "次",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 22,
                                "type": 4,
                                "subType": 1,
                                "pieceNum": 1,
                                "pieceUnit": "",
                                "packageUnit": "次",
                                "dismounting": 1,
                                "medicineCadn": "",
                                "extendSpec": null,
                                "position": "",
                                "piecePrice": null,
                                "packagePrice": 120,
                                "packageCostPrice": null,
                                "inTaxRat": 0,
                                "outTaxRat": 0,
                                "stockPieceCount": 0,
                                "stockPackageCount": 0,
                                "needExecutive": 1,
                                "smartDispense": 0,
                                "shortId": "**********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2020-01-16T03:52:44Z",
                                "combineType": 0,
                                "medicalFeeGrade": 0,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 120,
                                "pieceCount": 0,
                                "packageCount": 0,
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "cMSpec": ""
                            },
                            "isGift": 0,
                            "days": 1,
                            "executedCount": 0,
                            "remark": ""
                        }
                    ],
                    "composeType": 0,
                    "remark": "",
                    "created": "2021-11-04T08:56:23Z",
                    "totalPrice": 120
                }
            ],
            "totalPrice": 320
        },
        {
            "type": 1,
            "executeFormItems": [
                {
                    "groupId": null,
                    "usage": "静脉滴注",
                    "ivgtt": 0,
                    "freq": "qd",
                    "dosage": "1",
                    "dosageUnit": "片",
                    "days": 1,
                    "specialRequirement": "首次加倍",
                    "groupItems": [
                        {
                            "name": "酚咖片(芬必得)",
                            "medicineCadn": "酚咖片",
                            "tradeName": "芬必得",
                            "unitCount": 1,
                            "unitPrice": 0.64,
                            "totalPrice": 0.64,
                            "unit": "片",
                            "composeType": 0,
                            "productInfo": {
                                "id": "c74745c1cf364c78bcdbec7dcc33619a",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "芬必得",
                                "displayName": "酚咖片 (芬必得)",
                                "displaySpec": "20片/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "中美天津史克",
                                "pieceNum": 20,
                                "pieceUnit": "片",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "酚咖片",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 0.64,
                                "packagePrice": 12.8,
                                "packageCostPrice": 23,
                                "inTaxRat": 16,
                                "outTaxRat": 16,
                                "stockPieceCount": 19,
                                "stockPackageCount": 117,
                                "lastPackageCostPrice": 23,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "4292713741",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2018-08-09T04:00:16Z",
                                "combineType": 0,
                                "shebaoCode": "YP10072545",
                                "medicalFeeGrade": 2,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 12.8,
                                "chainPiecePrice": 0.64,
                                "chainPackageCostPrice": 23,
                                "pieceCount": 19,
                                "packageCount": 117,
                                "manufacturerFull": "中美天津史克制药有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 23,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "1",
                            "dosageUnit": "片",
                            "ast": 0,
                            "specialRequirement": "首次加倍",
                            "remark": null
                        }
                    ],
                    "composeType": 0,
                    "remark": null,
                    "created": "2021-11-04T08:56:23Z",
                    "totalPrice": 0.64
                },
                {
                    "groupId": null,
                    "usage": "静脉滴注",
                    "ivgtt": 0,
                    "freq": "st",
                    "dosage": "1",
                    "dosageUnit": "IU",
                    "days": 2,
                    "groupItems": [
                        {
                            "name": "制霉素片(a)",
                            "medicineCadn": "制霉素片",
                            "tradeName": "a",
                            "unitCount": 2,
                            "unitPrice": 0.11,
                            "totalPrice": 0.22,
                            "unit": "袋",
                            "composeType": 0,
                            "productInfo": {
                                "id": "fac97424eb684bf8bcc2e64bc671482b",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "a",
                                "displayName": "制霉素片 (a)",
                                "displaySpec": "50IU*231袋/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "barCode": "33",
                                "manufacturer": "浙江震元",
                                "pieceNum": 231,
                                "pieceUnit": "袋",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "制霉素片",
                                "medicineDosageNum": 50,
                                "medicineDosageUnit": "IU",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 0.11,
                                "packagePrice": 25,
                                "packageCostPrice": 11,
                                "inTaxRat": 16,
                                "outTaxRat": 16,
                                "stockPieceCount": 217,
                                "stockPackageCount": 30,
                                "lastPackageCostPrice": 11,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "4292713657",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-12-03T10:15:52Z",
                                "combineType": 0,
                                "shebaoCode": "YP00001564",
                                "medicalFeeGrade": 1,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 25,
                                "chainPiecePrice": 0.11,
                                "chainPackageCostPrice": 11,
                                "pieceCount": 217,
                                "packageCount": 30,
                                "manufacturerFull": "浙江震元制药有限责任公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 11,
                                "cMSpec": ""
                            },
                            "isGift": 0,
                            "dosage": "1",
                            "dosageUnit": "IU",
                            "ast": 0,
                            "remark": null
                        }
                    ],
                    "composeType": 0,
                    "remark": null,
                    "created": "2021-11-04T08:56:23Z",
                    "totalPrice": 0.22
                },
                {
                    "groupId": null,
                    "usage": "涂抹",
                    "ivgtt": 0,
                    "freq": "qd",
                    "dosage": "2",
                    "dosageUnit": "粒",
                    "days": 2,
                    "groupItems": [
                        {
                            "name": "龙泽熊胆胶囊(熊胆丸)",
                            "medicineCadn": "龙泽熊胆胶囊",
                            "tradeName": "熊胆丸",
                            "unitCount": 1,
                            "unitPrice": 15,
                            "totalPrice": 15,
                            "unit": "盒",
                            "composeType": 0,
                            "productInfo": {
                                "id": "c2a338c6ed68a9d5bbefdbea31cd5c66",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "熊胆丸",
                                "displayName": "龙泽熊胆胶囊 (熊胆丸)",
                                "displaySpec": "0.25g*20粒/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "长春普制药股份有限公司",
                                "pieceNum": 20,
                                "pieceUnit": "粒",
                                "packageUnit": "盒",
                                "dismounting": 0,
                                "medicineCadn": "龙泽熊胆胶囊",
                                "medicineNmpn": "国药准字Z22",
                                "medicineDosageNum": 0.25,
                                "medicineDosageUnit": "g",
                                "extendSpec": null,
                                "position": "",
                                "piecePrice": 0,
                                "packagePrice": 15,
                                "packageCostPrice": 25,
                                "inTaxRat": 2,
                                "outTaxRat": 0,
                                "stockPieceCount": 0,
                                "stockPackageCount": 12,
                                "lastPackageCostPrice": 25,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "**********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-07-16T12:22:10Z",
                                "combineType": 0,
                                "medicalFeeGrade": 0,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 15,
                                "chainPiecePrice": 0,
                                "chainPackageCostPrice": 25,
                                "pieceCount": 0,
                                "packageCount": 12,
                                "manufacturerFull": "长春普制药股份有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 25,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "2",
                            "dosageUnit": "粒",
                            "ast": 0,
                            "remark": null
                        }
                    ],
                    "composeType": 0,
                    "remark": null,
                    "created": "2021-11-04T08:56:23Z",
                    "totalPrice": 15
                }
            ],
            "totalPrice": 15.86
        },
        {
            "type": 2,
            "executeFormItems": [
                {
                    "groupId": 1,
                    "usage": "静脉滴注",
                    "ivgtt": 60,
                    "ivgttUnit": "滴/分钟",
                    "freq": "qd",
                    "dosage": "2",
                    "dosageUnit": "粒",
                    "days": 2,
                    "specialRequirement": "续用(皮试阴性)",
                    "groupItems": [
                        {
                            "name": "龙泽熊胆胶囊(熊胆丸)",
                            "medicineCadn": "龙泽熊胆胶囊",
                            "tradeName": "熊胆丸",
                            "unitCount": 1,
                            "unitPrice": 15,
                            "totalPrice": 15,
                            "unit": "盒",
                            "composeType": 0,
                            "productInfo": {
                                "id": "c2a338c6ed68a9d5bbefdbea31cd5c66",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "熊胆丸",
                                "displayName": "龙泽熊胆胶囊 (熊胆丸)",
                                "displaySpec": "0.25g*20粒/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "长春普制药股份有限公司",
                                "pieceNum": 20,
                                "pieceUnit": "粒",
                                "packageUnit": "盒",
                                "dismounting": 0,
                                "medicineCadn": "龙泽熊胆胶囊",
                                "medicineNmpn": "国药准字Z22",
                                "medicineDosageNum": 0.25,
                                "medicineDosageUnit": "g",
                                "extendSpec": null,
                                "position": "",
                                "piecePrice": 0,
                                "packagePrice": 15,
                                "packageCostPrice": 25,
                                "inTaxRat": 2,
                                "outTaxRat": 0,
                                "stockPieceCount": 0,
                                "stockPackageCount": 12,
                                "lastPackageCostPrice": 25,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "**********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-07-16T12:22:10Z",
                                "combineType": 0,
                                "medicalFeeGrade": 0,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 15,
                                "chainPiecePrice": 0,
                                "chainPackageCostPrice": 25,
                                "pieceCount": 0,
                                "packageCount": 12,
                                "manufacturerFull": "长春普制药股份有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 25,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "2",
                            "dosageUnit": "粒",
                            "ast": 0,
                            "specialRequirement": "续用(皮试阴性)",
                            "remark": null
                        },
                        {
                            "name": "十滴水胶丸(拾)",
                            "medicineCadn": "十滴水胶丸",
                            "tradeName": "拾",
                            "unitCount": 1,
                            "unitPrice": 3,
                            "totalPrice": 3,
                            "unit": "盒",
                            "composeType": 0,
                            "productInfo": {
                                "id": "e6a3d335cc1eabefd39d87a097602041",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "拾",
                                "displayName": "十滴水胶丸 (拾)",
                                "displaySpec": "2片/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 16,
                                "type": 1,
                                "subType": 3,
                                "pieceNum": 2,
                                "pieceUnit": "片",
                                "packageUnit": "盒",
                                "dismounting": 0,
                                "medicineCadn": "十滴水胶丸",
                                "extendSpec": null,
                                "position": "",
                                "piecePrice": 0,
                                "packagePrice": 3,
                                "packageCostPrice": 2,
                                "inTaxRat": 0,
                                "outTaxRat": 0,
                                "stockPieceCount": 0,
                                "stockPackageCount": 48,
                                "lastPackageCostPrice": 2,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "**********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-07-04T11:55:06Z",
                                "combineType": 0,
                                "medicalFeeGrade": 0,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 3,
                                "chainPiecePrice": 0,
                                "chainPackageCostPrice": 1,
                                "pieceCount": 0,
                                "packageCount": 48,
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 2,
                                "cMSpec": ""
                            },
                            "isGift": 0,
                            "dosage": "1",
                            "dosageUnit": "片",
                            "ast": 0,
                            "remark": null
                        },
                        {
                            "name": "布洛芬缓释胶囊(芬必得)",
                            "medicineCadn": "布洛芬缓释胶囊",
                            "tradeName": "芬必得",
                            "unitCount": 4,
                            "unitPrice": 0.95,
                            "totalPrice": 3.8,
                            "unit": "粒",
                            "composeType": 0,
                            "productInfo": {
                                "id": "64a3acba15da4c34b8c352fe43319c55",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "芬必得",
                                "displayName": "布洛芬缓释胶囊 (芬必得)",
                                "displaySpec": "300mg*20粒/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "中美天津史克",
                                "pieceNum": 20,
                                "pieceUnit": "粒",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "布洛芬缓释胶囊",
                                "medicineDosageNum": 300,
                                "medicineDosageUnit": "mg",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 0.95,
                                "packagePrice": 19,
                                "packageCostPrice": 20,
                                "inTaxRat": 16,
                                "outTaxRat": 16,
                                "stockPieceCount": 12,
                                "stockPackageCount": 8,
                                "lastPackageCostPrice": 20,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "4292712901",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-04-18T04:57:40Z",
                                "combineType": 0,
                                "shebaoCode": "YP10068222",
                                "medicalFeeGrade": 2,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 19,
                                "chainPiecePrice": 0.95,
                                "chainPackageCostPrice": 14.2,
                                "pieceCount": 12,
                                "packageCount": 8,
                                "manufacturerFull": "中美天津史克制药有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 20,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "2",
                            "dosageUnit": "粒",
                            "ast": 0,
                            "specialRequirement": "与抗生素间隔2小时",
                            "remark": null
                        },
                        {
                            "name": "氢溴酸右美沙芬分散片",
                            "medicineCadn": "氢溴酸右美沙芬分散片",
                            "tradeName": "",
                            "unitCount": 2,
                            "unitPrice": 1.5,
                            "totalPrice": 3,
                            "unit": "片",
                            "composeType": 0,
                            "productInfo": {
                                "id": "3a2231b3a94c4e359cd0a168591f31f6",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "",
                                "displayName": "氢溴酸右美沙芬分散片",
                                "displaySpec": "15mg*24片/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "石药集团欧意",
                                "pieceNum": 24,
                                "pieceUnit": "片",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "氢溴酸右美沙芬分散片",
                                "medicineNmpn": "H20040720",
                                "medicineDosageNum": 15,
                                "medicineDosageUnit": "mg",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 1.5,
                                "packagePrice": 17.5,
                                "packageCostPrice": 7.2,
                                "inTaxRat": 3,
                                "outTaxRat": 3,
                                "stockPieceCount": 19,
                                "stockPackageCount": 11,
                                "lastPackageCostPrice": 7.2,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "4292712494",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "367218bb13e44dc0ba16fd5b5df803e5",
                                "lastModifiedUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedDate": "2018-08-23T07:21:11Z",
                                "combineType": 0,
                                "shebaoCode": "YP10150965",
                                "medicalFeeGrade": 2,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 17.5,
                                "chainPiecePrice": 1.5,
                                "chainPackageCostPrice": 7,
                                "pieceCount": 19,
                                "packageCount": 11,
                                "manufacturerFull": "石药集团欧意药业有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 7.2,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "15.0000",
                            "dosageUnit": "mg",
                            "ast": 0,
                            "remark": null
                        },
                        {
                            "name": "口炎颗粒",
                            "medicineCadn": "口炎颗粒",
                            "tradeName": "",
                            "unitCount": 2,
                            "unitPrice": 1.5,
                            "totalPrice": 3,
                            "unit": "袋",
                            "composeType": 0,
                            "productInfo": {
                                "id": "f707903b227141e9b36124a4b4992764",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "",
                                "displayName": "口炎颗粒",
                                "displaySpec": "3g*10袋/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "四川光大",
                                "pieceNum": 10,
                                "pieceUnit": "袋",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "口炎颗粒",
                                "medicineNmpn": "国药准字Z20025341",
                                "medicineDosageNum": 3,
                                "medicineDosageUnit": "g",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 1.5,
                                "packagePrice": 15,
                                "packageCostPrice": 11.6,
                                "inTaxRat": 16,
                                "outTaxRat": 16,
                                "stockPieceCount": 9,
                                "stockPackageCount": 213,
                                "lastPackageCostPrice": 11.6,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "4292713963",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedDate": "2018-08-13T03:28:23Z",
                                "combineType": 0,
                                "shebaoCode": "YP00009030",
                                "medicalFeeGrade": 2,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 15,
                                "chainPiecePrice": 1.5,
                                "chainPackageCostPrice": 11.6,
                                "pieceCount": 9,
                                "packageCount": 213,
                                "manufacturerFull": "四川光大制药有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 11.6,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "1",
                            "dosageUnit": "g",
                            "ast": 0,
                            "remark": null
                        }
                    ],
                    "composeType": 0,
                    "remark": null,
                    "created": "2021-11-04T08:56:23Z",
                    "totalPrice": 27.8
                },
                {
                    "groupId": 2,
                    "usage": "静脉滴注",
                    "ivgtt": 60,
                    "ivgttUnit": "滴/分钟",
                    "freq": "qd",
                    "dosage": "1",
                    "dosageUnit": "片",
                    "days": 2,
                    "specialRequirement": "续用(皮试阴性)",
                    "groupItems": [
                        {
                            "name": "酚咖片(芬必得)",
                            "medicineCadn": "酚咖片",
                            "tradeName": "芬必得",
                            "unitCount": 2,
                            "unitPrice": 0.64,
                            "totalPrice": 1.28,
                            "unit": "片",
                            "composeType": 0,
                            "productInfo": {
                                "id": "c74745c1cf364c78bcdbec7dcc33619a",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "芬必得",
                                "displayName": "酚咖片 (芬必得)",
                                "displaySpec": "20片/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "中美天津史克",
                                "pieceNum": 20,
                                "pieceUnit": "片",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "酚咖片",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 0.64,
                                "packagePrice": 12.8,
                                "packageCostPrice": 23,
                                "inTaxRat": 16,
                                "outTaxRat": 16,
                                "stockPieceCount": 19,
                                "stockPackageCount": 117,
                                "lastPackageCostPrice": 23,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "4292713741",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2018-08-09T04:00:16Z",
                                "combineType": 0,
                                "shebaoCode": "YP10072545",
                                "medicalFeeGrade": 2,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 12.8,
                                "chainPiecePrice": 0.64,
                                "chainPackageCostPrice": 23,
                                "pieceCount": 19,
                                "packageCount": 117,
                                "manufacturerFull": "中美天津史克制药有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 23,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "1",
                            "dosageUnit": "片",
                            "ast": 1,
                            "specialRequirement": "续用(皮试阴性)",
                            "remark": null
                        },
                        {
                            "name": "龙泽熊胆胶囊(熊胆丸)",
                            "medicineCadn": "龙泽熊胆胶囊",
                            "tradeName": "熊胆丸",
                            "unitCount": 1,
                            "unitPrice": 15,
                            "totalPrice": 15,
                            "unit": "盒",
                            "composeType": 0,
                            "productInfo": {
                                "id": "c2a338c6ed68a9d5bbefdbea31cd5c66",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "熊胆丸",
                                "displayName": "龙泽熊胆胶囊 (熊胆丸)",
                                "displaySpec": "0.25g*20粒/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "长春普制药股份有限公司",
                                "pieceNum": 20,
                                "pieceUnit": "粒",
                                "packageUnit": "盒",
                                "dismounting": 0,
                                "medicineCadn": "龙泽熊胆胶囊",
                                "medicineNmpn": "国药准字Z22",
                                "medicineDosageNum": 0.25,
                                "medicineDosageUnit": "g",
                                "extendSpec": null,
                                "position": "",
                                "piecePrice": 0,
                                "packagePrice": 15,
                                "packageCostPrice": 25,
                                "inTaxRat": 2,
                                "outTaxRat": 0,
                                "stockPieceCount": 0,
                                "stockPackageCount": 12,
                                "lastPackageCostPrice": 25,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "**********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-07-16T12:22:10Z",
                                "combineType": 0,
                                "medicalFeeGrade": 0,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 15,
                                "chainPiecePrice": 0,
                                "chainPackageCostPrice": 25,
                                "pieceCount": 0,
                                "packageCount": 12,
                                "manufacturerFull": "长春普制药股份有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 25,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "2",
                            "dosageUnit": "粒",
                            "ast": 0,
                            "remark": null
                        },
                        {
                            "name": "布洛芬缓释胶囊(芬必得)",
                            "medicineCadn": "布洛芬缓释胶囊",
                            "tradeName": "芬必得",
                            "unitCount": 4,
                            "unitPrice": 0.95,
                            "totalPrice": 3.8,
                            "unit": "粒",
                            "composeType": 0,
                            "productInfo": {
                                "id": "35fcdb2527b494fb31c8d87305da9d0c",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "芬必得",
                                "displayName": "布洛芬缓释胶囊 (芬必得)",
                                "displaySpec": "300mg*20粒/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "中美天津史克",
                                "pieceNum": 20,
                                "pieceUnit": "粒",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "布洛芬缓释胶囊",
                                "medicineDosageNum": 300,
                                "medicineDosageUnit": "mg",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 0.95,
                                "packagePrice": 19,
                                "packageCostPrice": 22,
                                "inTaxRat": 16,
                                "outTaxRat": 16,
                                "stockPieceCount": 0,
                                "stockPackageCount": 2,
                                "lastPackageCostPrice": 22,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "1688448186",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-04-18T04:57:40Z",
                                "combineType": 0,
                                "shebaoCode": "YP10068222",
                                "medicalFeeGrade": 2,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 19,
                                "chainPiecePrice": 0.95,
                                "chainPackageCostPrice": 22,
                                "pieceCount": 0,
                                "packageCount": 2,
                                "manufacturerFull": "中美天津史克制药有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 22,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "2",
                            "dosageUnit": "粒",
                            "ast": 0,
                            "remark": null
                        },
                        {
                            "name": "小儿麻甘颗粒",
                            "medicineCadn": "小儿麻甘颗粒",
                            "tradeName": "",
                            "unitCount": 2,
                            "unitPrice": 0.9,
                            "totalPrice": 1.8,
                            "unit": "袋",
                            "composeType": 0,
                            "productInfo": {
                                "id": "68ef20535545475890abec61196ad3d2",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "",
                                "displayName": "小儿麻甘颗粒",
                                "displaySpec": "2.5g*12袋/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "云南楚雄云中",
                                "pieceNum": 12,
                                "pieceUnit": "袋",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "小儿麻甘颗粒",
                                "medicineNmpn": "国药准字Z53021087",
                                "medicineDosageNum": 2.5,
                                "medicineDosageUnit": "g",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 0.9,
                                "packagePrice": 10,
                                "packageCostPrice": 2.9,
                                "inTaxRat": 3,
                                "outTaxRat": 3,
                                "stockPieceCount": 0,
                                "stockPackageCount": 64,
                                "lastPackageCostPrice": 2.9,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "4292713421",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "34a01bccf80a4d4294a77b95d3d73ace",
                                "lastModifiedDate": "2018-08-10T03:58:11Z",
                                "combineType": 0,
                                "shebaoCode": "YP10174858",
                                "medicalFeeGrade": 3,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 10,
                                "chainPiecePrice": 0.9,
                                "chainPackageCostPrice": 2.8,
                                "pieceCount": 0,
                                "packageCount": 64,
                                "manufacturerFull": "云南楚雄云中制药有限责任公司   ",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 2.9,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "1",
                            "dosageUnit": "袋",
                            "ast": 0,
                            "remark": null
                        },
                        {
                            "name": "胞磷胆碱钠注射液",
                            "medicineCadn": "胞磷胆碱钠注射液",
                            "tradeName": "",
                            "unitCount": 2,
                            "unitPrice": 4,
                            "totalPrice": 8,
                            "unit": "支",
                            "composeType": 0,
                            "productInfo": {
                                "id": "330d2497287042fb9b6fae6020fb80f2",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "",
                                "displayName": "胞磷胆碱钠注射液",
                                "displaySpec": "0.25g*2ml/支",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "国药集团容生",
                                "pieceNum": 2,
                                "pieceUnit": "ml",
                                "packageUnit": "支",
                                "dismounting": 0,
                                "medicineCadn": "胞磷胆碱钠注射液",
                                "medicineNmpn": "55555",
                                "medicineDosageNum": 0.25,
                                "medicineDosageUnit": "g",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 0,
                                "packagePrice": 4,
                                "packageCostPrice": 0.7,
                                "inTaxRat": 16,
                                "outTaxRat": 16,
                                "stockPieceCount": 0,
                                "stockPackageCount": 4,
                                "lastPackageCostPrice": 0.7,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "*********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-07-16T15:56:24Z",
                                "combineType": 0,
                                "shebaoCode": "YP10030579",
                                "medicalFeeGrade": 1,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 4,
                                "chainPiecePrice": 0,
                                "chainPackageCostPrice": 0.7,
                                "pieceCount": 0,
                                "packageCount": 4,
                                "manufacturerFull": "国药集团容生制药有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 0.7,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "0.199",
                            "dosageUnit": "g",
                            "ast": 0,
                            "remark": null
                        },
                        {
                            "name": "注射用硫酸奈替米星",
                            "medicineCadn": "注射用硫酸奈替米星",
                            "tradeName": "",
                            "unitCount": 3,
                            "unitPrice": 5,
                            "totalPrice": 15,
                            "unit": "ml",
                            "composeType": 0,
                            "productInfo": {
                                "id": "98ddfbecb3ec4622aef6815d933e8615",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "",
                                "displayName": "注射用硫酸奈替米星",
                                "displaySpec": "10IU*2ml/支",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "以批次为准",
                                "pieceNum": 2,
                                "pieceUnit": "ml",
                                "packageUnit": "支",
                                "dismounting": 1,
                                "medicineCadn": "注射用硫酸奈替米星",
                                "medicineDosageNum": 10,
                                "medicineDosageUnit": "IU",
                                "extendSpec": null,
                                "position": "",
                                "piecePrice": 5,
                                "packagePrice": 10,
                                "packageCostPrice": 2,
                                "inTaxRat": 3,
                                "outTaxRat": 3,
                                "stockPieceCount": 1,
                                "stockPackageCount": 107,
                                "lastPackageCostPrice": 2,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "**********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-06-05T08:54:29Z",
                                "combineType": 0,
                                "medicalFeeGrade": 0,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 10,
                                "chainPiecePrice": 5,
                                "chainPackageCostPrice": 2,
                                "pieceCount": 1,
                                "packageCount": 107,
                                "manufacturerFull": "以批次为准",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 2,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "1.22",
                            "dosageUnit": "ml",
                            "ast": 0,
                            "remark": null
                        }
                    ],
                    "composeType": 0,
                    "remark": null,
                    "created": "2021-12-01T12:42:59Z",
                    "totalPrice": 44.88
                }
            ],
            "totalPrice": 72.68
        },
        {
            "type": 2,
            "executeFormItems": [
                {
                    "groupId": 1,
                    "usage": "静脉滴注",
                    "ivgtt": 30,
                    "ivgttUnit": "滴/分钟",
                    "freq": "prn",
                    "dosage": "1",
                    "dosageUnit": "片",
                    "days": 1,
                    "groupItems": [
                        {
                            "name": "酚咖片(芬必得)",
                            "medicineCadn": "酚咖片",
                            "tradeName": "芬必得",
                            "unitCount": 2,
                            "unitPrice": 0.64,
                            "totalPrice": 1.28,
                            "unit": "片",
                            "composeType": 0,
                            "productInfo": {
                                "id": "c74745c1cf364c78bcdbec7dcc33619a",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "芬必得",
                                "displayName": "酚咖片 (芬必得)",
                                "displaySpec": "20片/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "中美天津史克",
                                "pieceNum": 20,
                                "pieceUnit": "片",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "酚咖片",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 0.64,
                                "packagePrice": 12.8,
                                "packageCostPrice": 23,
                                "inTaxRat": 16,
                                "outTaxRat": 16,
                                "stockPieceCount": 19,
                                "stockPackageCount": 117,
                                "lastPackageCostPrice": 23,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "4292713741",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2018-08-09T04:00:16Z",
                                "combineType": 0,
                                "shebaoCode": "YP10072545",
                                "medicalFeeGrade": 2,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 12.8,
                                "chainPiecePrice": 0.64,
                                "chainPackageCostPrice": 23,
                                "pieceCount": 19,
                                "packageCount": 117,
                                "manufacturerFull": "中美天津史克制药有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 23,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "1",
                            "dosageUnit": "片",
                            "ast": 0,
                            "remark": null
                        },
                        {
                            "name": "氢溴酸右美沙芬分散片",
                            "medicineCadn": "氢溴酸右美沙芬分散片",
                            "tradeName": "",
                            "unitCount": 2,
                            "unitPrice": 1.5,
                            "totalPrice": 3,
                            "unit": "片",
                            "composeType": 0,
                            "productInfo": {
                                "id": "3a2231b3a94c4e359cd0a168591f31f6",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "",
                                "displayName": "氢溴酸右美沙芬分散片",
                                "displaySpec": "15mg*24片/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "石药集团欧意",
                                "pieceNum": 24,
                                "pieceUnit": "片",
                                "packageUnit": "盒",
                                "dismounting": 1,
                                "medicineCadn": "氢溴酸右美沙芬分散片",
                                "medicineNmpn": "H20040720",
                                "medicineDosageNum": 15,
                                "medicineDosageUnit": "mg",
                                "extendSpec": null,
                                "position": null,
                                "piecePrice": 1.5,
                                "packagePrice": 17.5,
                                "packageCostPrice": 7.2,
                                "inTaxRat": 3,
                                "outTaxRat": 3,
                                "stockPieceCount": 19,
                                "stockPackageCount": 11,
                                "lastPackageCostPrice": 7.2,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "4292712494",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "367218bb13e44dc0ba16fd5b5df803e5",
                                "lastModifiedUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedDate": "2018-08-23T07:21:11Z",
                                "combineType": 0,
                                "shebaoCode": "YP10150965",
                                "medicalFeeGrade": 2,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 17.5,
                                "chainPiecePrice": 1.5,
                                "chainPackageCostPrice": 7,
                                "pieceCount": 19,
                                "packageCount": 11,
                                "manufacturerFull": "石药集团欧意药业有限公司",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 7.2,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "15.0000",
                            "dosageUnit": "mg",
                            "ast": 0,
                            "remark": null
                        },
                        {
                            "name": "注射用硫酸奈替米星",
                            "medicineCadn": "注射用硫酸奈替米星",
                            "tradeName": "",
                            "unitCount": 3,
                            "unitPrice": 5,
                            "totalPrice": 15,
                            "unit": "ml",
                            "composeType": 0,
                            "productInfo": {
                                "id": "98ddfbecb3ec4622aef6815d933e8615",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "",
                                "displayName": "注射用硫酸奈替米星",
                                "displaySpec": "10IU*2ml/支",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "manufacturer": "以批次为准",
                                "pieceNum": 2,
                                "pieceUnit": "ml",
                                "packageUnit": "支",
                                "dismounting": 1,
                                "medicineCadn": "注射用硫酸奈替米星",
                                "medicineDosageNum": 10,
                                "medicineDosageUnit": "IU",
                                "extendSpec": null,
                                "position": "",
                                "piecePrice": 5,
                                "packagePrice": 10,
                                "packageCostPrice": 2,
                                "inTaxRat": 3,
                                "outTaxRat": 3,
                                "stockPieceCount": 1,
                                "stockPackageCount": 107,
                                "lastPackageCostPrice": 2,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "**********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-06-05T08:54:29Z",
                                "combineType": 0,
                                "medicalFeeGrade": 0,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 10,
                                "chainPiecePrice": 5,
                                "chainPackageCostPrice": 2,
                                "pieceCount": 1,
                                "packageCount": 107,
                                "manufacturerFull": "以批次为准",
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 2,
                                "cMSpec": null
                            },
                            "isGift": 0,
                            "dosage": "1.22",
                            "dosageUnit": "ml",
                            "ast": 0,
                            "remark": null
                        },
                        {
                            "name": "注射用阿魏酸钠",
                            "medicineCadn": "注射用阿魏酸钠",
                            "tradeName": "",
                            "unitCount": 2,
                            "unitPrice": 23,
                            "totalPrice": 46,
                            "unit": "盒",
                            "composeType": 0,
                            "productInfo": {
                                "id": "27775ce49f2e44e89f7d97f64b2df3fc",
                                "refGoodsId": null,
                                "refGoodsName": null,
                                "status": 1,
                                "name": "",
                                "displayName": "注射用阿魏酸钠",
                                "displaySpec": "11IU/盒",
                                "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "typeId": 12,
                                "type": 1,
                                "subType": 1,
                                "pieceNum": 11,
                                "pieceUnit": "IU",
                                "packageUnit": "盒",
                                "dismounting": 0,
                                "medicineCadn": "注射用阿魏酸钠",
                                "materialSpec": "中药饮片",
                                "extendSpec": null,
                                "position": "",
                                "piecePrice": 0,
                                "packagePrice": 23,
                                "packageCostPrice": 1,
                                "inTaxRat": 2,
                                "outTaxRat": 2,
                                "stockPieceCount": 0,
                                "stockPackageCount": 3,
                                "lastPackageCostPrice": 1,
                                "needExecutive": 0,
                                "smartDispense": 0,
                                "shortId": "*********",
                                "composeUseDismounting": 0,
                                "composeSort": 0,
                                "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                                "lastModifiedDate": "2019-08-05T09:27:20Z",
                                "combineType": 0,
                                "medicalFeeGrade": 0,
                                "nationalStandardCode": "",
                                "disable": 0,
                                "chainDisable": 0,
                                "v2DisableStatus": 0,
                                "chainV2DisableStatus": 0,
                                "disableSell": 0,
                                "isSell": 1,
                                "customTypeId": 0,
                                "chainPackagePrice": 23,
                                "chainPiecePrice": 0,
                                "chainPackageCostPrice": 1,
                                "pieceCount": 0,
                                "packageCount": 3,
                                "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                                "supplier": null,
                                "allowSubClinicSetPrice": null,
                                "pharmacyNo": 0,
                                "defaultInOutTax": 0,
                                "dispenseAveragePackageCostPrice": 1,
                                "cMSpec": "中药饮片"
                            },
                            "isGift": 0,
                            "dosage": "0.8999",
                            "dosageUnit": "IU",
                            "ast": 0,
                            "remark": null
                        }
                    ],
                    "composeType": 0,
                    "remark": null,
                    "created": "2021-12-01T12:42:59Z",
                    "totalPrice": 65.28
                }
            ],
            "totalPrice": 65.28
        }
    ],
    "diagnosedDate": "2021-11-04T08:56:22Z",
    "patientOrderNo": 9298,
    "totalPrice": 473.82
}

-->
<template>
    <div>
        <template v-if="headerConfig.signPosition === signPosition.bottom || headerConfig.signPosition === signPosition.none">
            <template v-for="(form, formIndex) in westernExecuteForms">
                <outpatient-header
                    data-type="header"
                    :organ-title="organTitle"
                    :print-data="printData"
                    :print-title="form.typeInfoZh ? form.typeInfoZh : '输液注射单'"
                    :config="config"
                    :data-pendants-index="`${formIndex}WP`"
                    :show-archives="false"
                ></outpatient-header>
                <div
                    style="height: 6pt"
                ></div>
                <template v-if="contentConfig.infusionSplitByUsage ? contentConfig.includeTreatment && treatmentExecuteForms.length && form.isFirstTreatment : contentConfig.includeTreatment && treatmentExecuteForms.length && formIndex === 0">
                    <template v-for="(treatmentForm, treatmentFormIndex) in treatmentExecuteForms">
                        <template v-for="(groupItem, pIndex) in treatmentForm.executeFormItems">
                            <print-row
                                v-if="groupItem.composeName"
                                class="product-form-item"
                            >
                                <print-col :span="24">
                                    【套】{{ groupItem.composeName }}
                                    <div
                                        v-if="groupItem.remark"
                                        class="remark-text"
                                    >
                                        {{ groupItem.remark }}
                                    </div>
                                </print-col>
                            </print-row>
                            <print-row
                                v-for="formItem in groupItem.groupItems"
                                class="product-form-item"
                                data-type="mix-box"
                                :span="24"
                            >
                                <print-col
                                    :span="16"
                                >
                                    {{ formItem.name }}
                                    <div
                                        v-if="formItem.remark"
                                        class="remark-text"
                                    >
                                        {{ formItem.remark }}
                                    </div>
                                </print-col>
                                <print-col
                                    :span="8"
                                >
                                    {{ formItem.executedCount || 0 }}/{{ formItem.unitCount || 0 }}{{ formatTreatmentUnit( formItem.unit, ' ' ) }}
                                </print-col>
                            </print-row>
                        </template>
                        <print-row
                            v-if="treatmentFormIndex === treatmentExecuteForms.length - 1"
                            class="split-form-line"
                        ></print-row>
                    </template>
                </template>
                <template v-for="(groupItem, pIndex) in form.executeFormItems">
                    <div
                        v-if="isInfusionUsage(groupItem.usage)"
                        data-type="mix-box"
                    >
                        <infusion-item
                            :print-data="printData"
                            :execute-form-item="groupItem"
                            :group-form-items="groupItem.groupItems"
                            :config="config"
                            :is-ivgtt-unit-latin="!!config.content.medicalLatin"
                            form-type="infusionExecute"
                        ></infusion-item>
                    </div>

                    <div
                        v-else
                        data-type="mix-box"
                    >
                        <div
                            data-type="group"
                            style="position: relative"
                        >
                            <span
                                v-if="groupItem.groupId"
                                class="western-item-icon"
                            >{{ NUMBER_ICONS[groupItem.groupId] }}</span>
                            <print-row
                                v-for="formItem in groupItem.groupItems"
                                class="product-form-item"
                                :span="24"
                                data-type="item"
                            >
                                <print-col
                                    :span="15"
                                >
                                    <div
                                        class="western-item-wrapper"
                                        overflow
                                    >
                                        <div
                                            class="western-item-name"
                                        >
                                            <template v-if="contentConfig.medicineTradeName">
                                                {{ formItem.name }}
                                            </template>
                                            <template v-else>
                                                {{ formItem.medicineCadn || formItem.name }}
                                            </template>
                                        </div>
                                        <div
                                            v-if="contentConfig.medicineSpec"
                                            class="western-item-spec"
                                        >
                                            {{ formItem.productInfo | goodsSpec }}
                                        </div>
                                        <div class="western-item-count">
                                            × {{ formItem.unitCount }}{{ formItem.unit }}
                                        </div>
                                    </div>
                                    <div
                                        v-if="formItem.specialRequirement"
                                        class="remark-text western-remark-text"
                                    >
                                        备注：{{ formItem.specialRequirement }}
                                    </div>
                                </print-col>

                                <print-col
                                    :span="9"
                                    class="western-item-usage"
                                    overflow
                                >
                                    {{ contentConfig.medicalLatin ? 'Sig：' : '每次' }}{{ formItem.dosage }}{{ formItem.dosageUnit }}
                                    {{ freqFormat(groupItem.freq, contentConfig.medicalLatin) }}&nbsp;
                                    {{ usageFormat(groupItem.usage, contentConfig.medicalLatin) }}&nbsp;
                                    {{ groupItem.days }}天
                                </print-col>
                            </print-row>
                        </div>
                    </div>
                </template>

                <div
                    data-type="footer"
                    :data-pendants-index="`${formIndex}WP`"
                >
                    <div
                        v-if="!extra.isPreview"
                        style="height: 6pt;"
                    ></div>

                    <div
                        v-if="!extra.isPreview && headerConfig.signPosition === signPosition.bottom && tableRowNumber"
                        style="padding: 0 8pt 8pt;"
                    >
                        <fill-box
                            :table-header="tableHeader"
                            :table-row-number="tableRowNumber"
                        ></fill-box>
                    </div>

                    <outpatient-footer
                        :print-data="printData"
                        print-type="infusionExecute"
                        :config="config"
                        :pr-form="form"
                    ></outpatient-footer>

                    <table
                        v-if="extra.isPreview && headerConfig.signPosition === signPosition.bottom"
                        class="infusion-sign-table infusion-sign-table__preview"
                    >
                        <thead>
                            <tr>
                                <td
                                    v-for="(item, index) in tableHeader"
                                    :key="index"
                                    colspan="2"
                                >
                                    {{ item }}
                                </td>
                            </tr>
                        </thead>
                        <tbody data-type="group">
                            <tr
                                v-for="item in tableRowNumber"
                                :key="`infusion-footer-bottom-table-tr-${item}`"
                                data-type="item"
                            >
                                <td
                                    v-for="(item, index) in tableHeader"
                                    :key="index"
                                    colspan="2"
                                ></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </template>

            <template v-for="(form, index) in infusionExecuteForms">
                <outpatient-header
                    data-type="header"
                    :organ-title="organTitle"
                    :print-data="printData"
                    :print-title="form.typeInfoZh ? form.typeInfoZh : '输液注射单'"
                    :data-pendants-index="`${index}IP`"
                    :config="config"
                    :show-archives="false"
                ></outpatient-header>
                <div
                    style="height: 6pt"
                ></div>
                <template v-if="!contentConfig.infusionSplitByUsage ? contentConfig.includeTreatment && !westernExecuteForms.length && treatmentExecuteForms.length && index === 0 : false">
                    <template v-for="(formIt, formIndex) in treatmentExecuteForms">
                        <template v-for="(groupItem, pIndex) in formIt.executeFormItems">
                            <print-row
                                v-if="groupItem.composeName"
                                class="product-form-item"
                            >
                                <print-col :span="24">
                                    【套】{{ groupItem.composeName }}
                                    <div
                                        v-if="groupItem.remark"
                                        class="remark-text"
                                    >
                                        {{ groupItem.remark }}
                                    </div>
                                </print-col>
                            </print-row>
                            <print-row
                                v-for="formItem in groupItem.groupItems"
                                class="product-form-item"
                                data-type="mix-box"
                                :span="24"
                            >
                                <print-col
                                    :span="16"
                                >
                                    {{ formItem.name }}
                                    <div
                                        v-if="formItem.remark"
                                        class="remark-text"
                                    >
                                        {{ formItem.remark }}
                                    </div>
                                </print-col>
                                <print-col
                                    :span="8"
                                >
                                    {{ formItem.executedCount || 0 }}/{{ formItem.unitCount || 0 }}{{ formatTreatmentUnit( formItem.unit, ' ' ) }}
                                </print-col>
                            </print-row>
                        </template>
                        <print-row
                            v-if="formIndex === treatmentExecuteForms.length - 1"
                            class="split-form-line"
                        ></print-row>
                    </template>
                </template>

                <div
                    v-for="(formItem, itemIndex) in form.executeFormItems"
                    data-type="mix-box"
                    style="overflow: hidden"
                >
                    <infusion-item
                        :print-data="printData"
                        :execute-form-item="formItem"
                        :group-form-items="formItem.groupItems"
                        :config="config"
                        :is-ivgtt-unit-latin="!!config.content.medicalLatin"
                        form-type="infusionExecute"
                    ></infusion-item>
                </div>
                <div
                    data-type="footer"
                    :data-pendants-index="`${index}IP`"
                >
                    <div
                        v-if="!extra.isPreview"
                        style="height: 6pt;"
                    ></div>

                    <div
                        v-if="!extra.isPreview && headerConfig.signPosition === signPosition.bottom && tableRowNumber"
                        style="padding: 0 8pt 8pt;"
                    >
                        <fill-box
                            :table-header="tableHeader"
                            :table-row-number="tableRowNumber"
                        ></fill-box>
                    </div>

                    <outpatient-footer
                        :print-data="printData"
                        print-type="infusionExecute"
                        :config="config"
                        :pr-form="form"
                    ></outpatient-footer>

                    <table
                        v-if="extra.isPreview && headerConfig.signPosition === signPosition.bottom"
                        class="infusion-sign-table infusion-sign-table__preview"
                    >
                        <thead>
                            <tr>
                                <td
                                    v-for="(item, index) in tableHeader"
                                    :key="index"
                                    colspan="2"
                                >
                                    {{ item }}
                                </td>
                            </tr>
                        </thead>
                        <tbody data-type="group">
                            <tr
                                v-for="item in tableRowNumber"
                                :key="`infusion-footer-bottom-table-tr-${item}`"
                                data-type="item"
                            >
                                <td
                                    v-for="(item, index) in tableHeader"
                                    :key="index"
                                    colspan="2"
                                ></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </template>

            <!--            兼容只开了治疗项目的情况，并且输注单打印需要包含治疗项目-->
            <template v-if="contentConfig.infusionSplitByUsage ? contentConfig.includeTreatment && treatmentExecuteForms.length && !isHasExternalForms : contentConfig.includeTreatment && !westernExecuteForms.length && !infusionExecuteForms.length && treatmentExecuteForms.length">
                <template v-for="(form, index) in treatmentExecuteForms">
                    <outpatient-header
                        data-type="header"
                        :organ-title="organTitle"
                        :print-data="printData"
                        :print-title="!contentConfig.infusionSplitByUsage ? '输液注射单' : form.typeInfoZh ? form.typeInfoZh : '外治单'"
                        :data-pendants-index="`${index}TP`"
                        :config="config"
                        :show-archives="false"
                    ></outpatient-header>
                    <div
                        style="height: 6pt"
                    ></div>
                    <template v-for="(formIt, formIndex) in treatmentExecuteForms">
                        <template v-for="(groupItem, pIndex) in formIt.executeFormItems">
                            <print-row
                                v-if="groupItem.composeName"
                                class="product-form-item"
                            >
                                <print-col :span="24">
                                    【套】{{ groupItem.composeName }}
                                    <div
                                        v-if="groupItem.remark"
                                        class="remark-text"
                                    >
                                        {{ groupItem.remark }}
                                    </div>
                                </print-col>
                            </print-row>
                            <print-row
                                v-for="formItem in groupItem.groupItems"
                                class="product-form-item"
                                data-type="mix-box"
                                :span="24"
                            >
                                <print-col
                                    :span="16"
                                >
                                    {{ formItem.name }}
                                    <div
                                        v-if="formItem.remark"
                                        class="remark-text"
                                    >
                                        {{ formItem.remark }}
                                    </div>
                                </print-col>
                                <print-col
                                    :span="8"
                                >
                                    {{ formItem.executedCount || 0 }}/{{ formItem.unitCount || 0 }}{{ formatTreatmentUnit( formItem.unit, ' ' ) }}
                                </print-col>
                            </print-row>
                        </template>
                        <print-row
                            v-if="formIndex === treatmentExecuteForms.length - 1"
                            class="split-form-line"
                        ></print-row>
                    </template>
                    <div
                        data-type="footer"
                        :data-pendants-index="`${index}TP`"
                    >
                        <div
                            v-if="!extra.isPreview"
                            style="height: 6pt;"
                        ></div>

                        <div
                            v-if="!extra.isPreview && headerConfig.signPosition === signPosition.bottom && tableRowNumber"
                            style="padding: 0 8pt 8pt;"
                        >
                            <fill-box
                                :table-header="tableHeader"
                                :table-row-number="tableRowNumber"
                            ></fill-box>
                        </div>

                        <outpatient-footer
                            :print-data="printData"
                            print-type="infusionExecute"
                            :config="config"
                            :pr-form="form"
                        ></outpatient-footer>

                        <table
                            v-if="extra.isPreview && headerConfig.signPosition === signPosition.bottom"
                            class="infusion-sign-table infusion-sign-table__preview"
                        >
                            <thead>
                                <tr>
                                    <td
                                        v-for="(item, index) in tableHeader"
                                        :key="index"
                                        colspan="2"
                                    >
                                        {{ item }}
                                    </td>
                                </tr>
                            </thead>
                            <tbody data-type="group">
                                <tr
                                    v-for="item in tableRowNumber"
                                    :key="`infusion-footer-bottom-table-tr-${item}`"
                                    data-type="item"
                                >
                                    <td
                                        v-for="(item, index) in tableHeader"
                                        :key="index"
                                        colspan="2"
                                    ></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </template>
            </template>
        </template>

        <template v-if="headerConfig.signPosition === signPosition.right">
            <template v-for="(form, index) in [...westernExecuteForms, ...infusionExecuteForms]">
                <outpatient-header
                    data-type="header"
                    :organ-title="organTitle"
                    :print-data="printData"
                    :print-title="form.typeInfoZh ? form.typeInfoZh : '输液注射单'"
                    :config="config"
                    :data-pendants-index="`${index}WP`"
                    :show-archives="false"
                ></outpatient-header>
                <div
                    style="height: 6pt"
                ></div>
                <template v-if="contentConfig.infusionSplitByUsage ? contentConfig.includeTreatment && treatmentExecuteForms.length && form.isFirstTreatment : contentConfig.includeTreatment && treatmentExecuteForms.length && index === 0">
                    <table
                        data-type="mix-box"
                        class="infusion-product-table"
                    >
                        <thead>
                            <tr>
                                <td colspan="6">
                                    治疗项目
                                </td>
                                <td
                                    class="product-sign-td"
                                    colspan="3"
                                >
                                    时间/签名
                                </td>
                            </tr>
                        </thead>

                        <tbody data-type="group">
                            <template v-for="treatmentForm in treatmentExecuteForms">
                                <template v-for="formItem in treatmentForm.executeFormItems">
                                    <tr v-if="formItem.composeName && formItem.groupItems.length">
                                        <td colspan="6">
                                            【套】{{ formItem.composeName }}
                                            <div
                                                v-if="formItem.remark"
                                                class="remark-text"
                                            >
                                                {{ formItem.remark }}
                                            </div>
                                        </td>
                                        <td
                                            class="product-sign-td"
                                            colspan="3"
                                        ></td>
                                    </tr>
                                    <tr
                                        v-for="item in formItem.groupItems"
                                        data-type="item"
                                    >
                                        <td colspan="6">
                                            <div>
                                                <print-row>
                                                    <print-col :span="20">
                                                        {{ item.name }}
                                                    </print-col>
                                                    <print-col
                                                        :span="4"
                                                        style="text-align: right"
                                                    >
                                                        {{ item.executedCount || 0 }}/{{ item.unitCount || 0 }}{{ formatTreatmentUnit( item.unit, ' ' ) }}
                                                    </print-col>
                                                </print-row>
                                            </div>
                                            <div
                                                v-if="item.remark"
                                                class="remark-text"
                                            >
                                                {{ item.remark }}
                                            </div>
                                        </td>
                                        <td
                                            class="product-sign-td"
                                            colspan="3"
                                        ></td>
                                    </tr>
                                </template>
                            </template>
                        </tbody>
                    </table>
                </template>
                <infusion-table
                    :form="form"
                    :content-config="contentConfig"
                    :is-hospital="isHospital"
                ></infusion-table>
                <div
                    data-type="footer"
                    :data-pendants-index="`${index}WP`"
                >
                    <outpatient-footer
                        :print-data="printData"
                        print-type="infusionExecute"
                        :config="config"
                        :pr-form="form"
                    ></outpatient-footer>
                </div>
            </template>
            <template v-if="contentConfig.infusionSplitByUsage ? contentConfig.includeTreatment && treatmentExecuteForms.length && !isHasExternalForms : contentConfig.includeTreatment && !westernExecuteForms.length && !infusionExecuteForms.length && treatmentExecuteForms.length">
                <template v-for="(form, index) in treatmentExecuteForms">
                    <outpatient-header
                        data-type="header"
                        :organ-title="organTitle"
                        :print-data="printData"
                        :print-title="!contentConfig.infusionSplitByUsage ? '输液注射单' : form.typeInfoZh ? form.typeInfoZh : '外治单'"
                        :config="config"
                        :data-pendants-index="`${index + [...westernExecuteForms, ...infusionExecuteForms].length}WP`"
                        :show-archives="false"
                    ></outpatient-header>`
                    <div
                        style="height: 6pt"
                    ></div>
                    <table
                        data-type="mix-box"
                        class="infusion-product-table"
                    >
                        <thead>
                            <tr>
                                <td colspan="6">
                                    治疗项目
                                </td>
                                <td
                                    class="product-sign-td"
                                    colspan="3"
                                >
                                    时间/签名
                                </td>
                            </tr>
                        </thead>

                        <tbody data-type="group">
                            <template v-for="formItem in form.executeFormItems">
                                <tr v-if="formItem.composeName && formItem.groupItems.length">
                                    <td colspan="6">
                                        【套】{{ formItem.composeName }}
                                        <div
                                            v-if="formItem.remark"
                                            class="remark-text"
                                        >
                                            {{ formItem.remark }}
                                        </div>
                                    </td>
                                    <td
                                        class="product-sign-td"
                                        colspan="3"
                                    ></td>
                                </tr>
                                <tr
                                    v-for="item in formItem.groupItems"
                                    data-type="item"
                                >
                                    <td colspan="6">
                                        <div>
                                            <print-row>
                                                <print-col :span="20">
                                                    {{ item.name }}
                                                </print-col>
                                                <print-col
                                                    :span="4"
                                                    style="text-align: right"
                                                >
                                                    {{ item.executedCount || 0 }}/{{ item.unitCount || 0 }}{{ formatTreatmentUnit( item.unit, ' ' ) }}
                                                </print-col>
                                            </print-row>
                                        </div>
                                        <div
                                            v-if="item.remark"
                                            class="remark-text"
                                        >
                                            {{ item.remark }}
                                        </div>
                                    </td>
                                    <td
                                        class="product-sign-td"
                                        colspan="3"
                                    ></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                    <div
                        data-type="footer"
                        :data-pendants-index="`${index + [...westernExecuteForms, ...infusionExecuteForms].length}WP`"
                    >
                        <outpatient-footer
                            :print-data="printData"
                            print-type="infusionExecute"
                            :config="config"
                            :pr-form="form"
                        ></outpatient-footer>
                    </div>
                </template>
            </template>
        </template>
    </div>
</template>

<script>
    import OutpatientHeader from './components/medical-document-header/execute-header.vue';
    import OutpatientFooter from './components/medical-document-footer/index.vue';
    import InfusionItem from './components/prescription/infusion-item/index.vue';
    import PrintRow from './components/layout/print-row.vue';
    import PrintCol from './components/layout/print-col.vue';
    import InfusionTable from './components/infusion-execute/infusion-table.vue';
    import FillBox from './components/infusion-execute/fill-box.vue';

    import InfusionExecuteHandler from './data-handler/infusion-execute-handler.js'

    import { goodsSpec, formatTreatmentUnit, calcTableRowNumber } from "./common/utils.js";
    import { transIvgttUnit, usageFormat, freqFormat, isExternalUsage, isInfusionUsage } from "./common/medical-transformat.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import clone from "./common/clone.js";
    import { NUMBER_ICONS } from "./common/constants.js";
    export default {
        DataHandler: InfusionExecuteHandler,
        name: "Infusion",
        components: {
            OutpatientHeader,
            OutpatientFooter,
            InfusionItem,
            PrintRow,
            PrintCol,
            InfusionTable,
            FillBox,
        },
        filters: {
            goodsSpec,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
            extra: {
                type: Object,
                default() {
                    return {}
                },
            },
            splitSize: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                NUMBER_ICONS,
            }
        },
        businessKey: PrintBusinessKeyEnum.INFUSION_EXECUTE,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM95_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分', // 默认选择的等分纸
            },
        ],
        computed: {
            splitHeight() {
                const splitHeight = parseFloat(this.splitSize.height);
                return isNaN(splitHeight) ? 0 : splitHeight;
            },
            /**
             * 小纸张打印时由于高度很小,表格行数过多导致分页失败
             * 所以小纸张打印时减少表格行数
             */
            tableRowNumber() {
                const maxRowNumber = calcTableRowNumber(this.splitHeight);
                if(this.signatureRowNumber && this.signatureRowNumber <= maxRowNumber) {
                    return this.signatureRowNumber
                }
                return maxRowNumber;
            },
            signPosition() {
                return {
                    bottom: 2,
                    right: 1,
                    none: 0,
                }
            },
            formType() {
                return {
                    treatment: 3,
                    western: 1,
                    infusion: 2,
                }
            },
            printData() {
                let dataPrint = clone(this.renderData.printData)
                dataPrint = this.filterMoneyForTrementAndCompose(dataPrint)
                return dataPrint|| null;
            },
            organ() {
                return this.printData.organ;
            },
            organTitle() {
                if (!this.headerConfig.title) {
                    return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.infusion || '';
                }
                return this.headerConfig.title;
            },
            config() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.infusion) {
                    return this.renderData.config.medicalDocuments.infusion;
                }
                return {};
            },
            headerConfig() {
                return this.config.header || {};
            },
            contentConfig() {
                return this.config.content || {};
            },
            tableHeader() {
                return this.isHospital ?
                    (this.contentConfig.hospitalTableHeader || ['组号', '配药时间', '配药签名', '核对签名', '滴速', '巡视时间', '巡视签名'] ) :
                    (this.contentConfig.tableHeader || ['执行时间', '执行签名', '患者签名','执行时间', '执行签名', '患者签名' ]);
            },
            // 签名栏打印行数
            signatureRowNumber() {
                return this.contentConfig.signatureRowNum ?? 7;
            },
            infusionExecuteForms() {
                return this.printData.executeForms.filter( form => {
                    return form.type === this.formType.infusion; // 去掉治疗项目的form
                })
            },
            westernExecuteForms() {
                let forms = clone(this.printData.executeForms.filter( form => {
                    return form.type === this.formType.western;
                }))
                if(!this.contentConfig.includeExternal) {
                    forms.forEach( form => {
                        this.filterExternalUsage(form)
                    })
                    forms = forms.filter( form => {
                        return form.executeFormItems && form.executeFormItems.length;
                    })
                }

                // 如果有外用,则在第一个外治单上加伤诊疗项目
                forms.findIndex(form => {
                    if (form.typeInfo === 'external') {
                        form.isFirstTreatment = true;
                        return true;
                    }
                    return false;
                });

                return forms;
            },
            treatmentExecuteForms() {
                return this.printData.executeForms.filter( form => {
                    return form.type === this.formType.treatment;
                }) || []
            },
            isHospital() {
                return this.printData.IS_HOSPITAL;
            },
            isHasExternalForms() {
                const res = this.westernExecuteForms.some((item) => {
                    return item.typeInfo === 'external';
                });
                return res;
            },
        },
        methods: {
            transIvgttUnit,
            usageFormat,
            freqFormat,
            isInfusionUsage,
            formatTreatmentUnit,
            filterExternalUsage(form) {
                if(form.executeFormItems && form.executeFormItems.length) {
                    form.executeFormItems = form.executeFormItems && form.executeFormItems.filter( item => {
                        return !isExternalUsage(item.usage);
                    })
                }
                return form;
            },
            /**
             * @Description: 筛选出form中治疗单以及套餐的总价并加入第一个处方
             * <AUTHOR> Cai
             * @date 2022/07/13 10:52:43
            */
            filterMoneyForTrementAndCompose (data) {

                let totalPrice = 0
                data.executeForms.sort((a,b) => {
                    return a.type - b.type
                })
                data.executeForms.forEach(formItem => {
                    if(formItem.type === 3) {
                        totalPrice += formItem.totalPrice
                    }
                })
                let index = -1;
                if (this.contentConfig.infusionSplitByUsage) {
                    index = data.executeForms.findIndex(formItem => {
                        return formItem.typeInfo === 'external';
                    })
                } else {
                    index = data.executeForms.findIndex(formItem => {
                        return formItem.type !== 3
                    })
                }
                if(this.contentConfig.includeTreatment === 1 && data.executeForms[index]) {
                    data.executeForms[index].totalPrice = data.executeForms[index].totalPrice + totalPrice

                }
                return data
            },
        },
    }
</script>

<style lang="scss">
@import "./components/layout/print-layout.scss";
@import "./style/reset.scss";

.abc-page-content{
    padding: 8pt;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "微软雅黑";
    overflow: hidden;
    position: relative;
    .__execute-overflow-blank{
        position: absolute;
        top: 70pt;
        right: 0;
        height: 88%;
        width: 8pt;
        z-index: 1;
        background-color: #ffffff;
    }
    * {
        background-color: #ffffff;
    }
}

.remark-text {
    font-size: 9pt;
    //overflow: hidden;
    //word-break: keep-all;
    //white-space: nowrap;
}

.product-form-item {
    margin-bottom: 6pt;
    font-weight: 300;
    position: relative;

    .print-col {
        font-size: 10pt;
        line-height: 12pt;
    }
}
.western-item-wrapper{
    font-size: 0;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    .western-item-name,
    .western-item-spec,
    .western-item-count {
        font-size: 10pt;
        display: inline-block;
        word-break: keep-all;
        white-space: nowrap;
        overflow: hidden;
        position: relative;
    }
    .western-item-name {
        max-width: 70%;
    }
    .western-item-spec {
        max-width: 15%;
    }
    .western-item-count {
        min-width: 15%;
    }
}

.western-item-icon{
    position: absolute;
    top: 0;
    left: 0;
    width: 16pt;
    z-index: 1;
    font-size: 10pt;
    line-height: 11pt;
}
.western-remark-text{
    padding-left: 16pt;
}

.split-form-line {
    height: 2pt;
    margin-bottom: 6pt;
    border-bottom: 1px dashed #000000;
}

.infusion-sign-table {
    width: 100%;
    font-size: 10pt;
    text-align: center;
    table-layout: fixed;

    td {
        height: 20pt;
        border: 1px solid #000000;
    }
}

.infusion-sign-table__preview {
    position: absolute;
    top: -8pt;
    left: 0;
    transform: translateY(-100%);
}
.western-item-usage {
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-align: right;
}

.infusion-product-table {
    width: 100%;
    margin-top: 6pt;
    font-weight: 300;
    font-size: 10pt;
    table-layout: fixed;
    overflow: hidden;

    td {
        padding: 6pt;
        border: 1px solid #000000;
        overflow: hidden;
    }


    .product-item-td {
        width: 100%;
        font-size: 0;
        margin-bottom: 6pt;
        &.td-title{
            margin-bottom: 0;
        }
        .remark-text {
            padding-left: 16pt;
        }
    }
    .product-item-usage{
        padding-left: 16pt;
        font-size: 10pt;
        font-weight: bold;
    }

    .dosage-unit,
    .item-wrapper {
        display: inline-block;
        vertical-align: middle;
    }

    .item-wrapper{
        font-size: 0;
        width: 82%;
        max-width: 82%;
    }
    .dosage-unit{
        font-size: 10pt;
        width: 17%;
        max-width: 17%;
        text-align: right;
         >span{
             font-size: 10pt;
         }
    }


    .number-icon {
        display: inline-block;
        width: 16pt;
        font-size: 10pt;
        line-height: 12pt;
        vertical-align: middle;
    }

    .no-number-icon {
        .product-infusion-item {
            padding-left: 16pt;
        }
    }

    .product-infusion-item {
        box-sizing: border-box;
        position: relative;
        display: inline-block;
        width: 80%;
        max-width: 80%;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        vertical-align: middle;
        &.product-western-item{
            width: 99%;
            max-width: 99%;
            &.has-ast{
                width: 83%;
                max-width: 83%;
            }
        }
        &.has-ast {
            width: 64%;
            max-width: 64%;
        }
        &.no-total-count {
            width: 100%;
            max-width: 100%;
            &.has-ast {
                width: 85%;
                max-width: 85%;
            }
        }
    }

    .execute-count {
        width: 20%;
        max-width: 20%;
    }
    .item-ast {
        width: 16%;
        max-width: 16%;
    }

    .execute-content,
    .execute-count,
    .item-ast {
        display: inline-block;
        overflow: hidden;
        font-size: 10pt;
        line-height: 12pt;
        word-break: keep-all;
        white-space: nowrap;
        vertical-align: middle;
    }

    .group-spec {
        display: inline-block;
        font-size: 9pt;
        line-height: 12pt;
        vertical-align: middle;
    }
}
</style>
