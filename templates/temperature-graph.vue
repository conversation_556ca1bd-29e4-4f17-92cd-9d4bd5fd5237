<!--exampleData
{}
-->

<template>
    <div></div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";

    export default {
        name: "TemperatureGraph",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.TEMPERATURE_GRAPH,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
    }
</script>
