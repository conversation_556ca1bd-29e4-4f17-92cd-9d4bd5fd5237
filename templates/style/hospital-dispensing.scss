@import "../components/layout/print-layout.scss";

.abc-page_preview {
  td, th {
    line-height: 1!important;
  }
}

.abc-hospital-dispensing {
  font-size: 13px;
  font-family: 'Sim<PERSON><PERSON>', 'SimSun', 'Arial', 'Helvetica', 'sans-serif';

  .clinic-name, .prescription-type {
    text-align: center;
  }

  .clinic-name {
    font-size: 14pt;
    line-height: 20pt;
  }

  .prescription-type {
    font-weight: normal;
    margin-bottom: 12pt;
    font-size: 12pt;
    line-height: 20pt
  }

  .table-header {
    overflow: hidden;
    margin-bottom: 2pt;
    padding-bottom: 2pt;

    .table-header-left {
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .table-header-right {
      text-align: right;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .align-center {
    text-align: center;
  }

  .align-left {
    text-align: left;
  }

  .align-right {
    text-align: right;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ccc;
    table-layout: fixed;
  }

  th {
    padding: 8px 5px;
    font-size: 13px;
    line-height: 15px;
    border: 1px solid #ccc;
  }

  td {
    padding: 3px;
    text-align: center;
    font-size: 13px;
    line-height: 1.1;
    border: 1px solid #ccc;
  }

  .text-right {
    text-align: right;
  }

  .pagination {
    border-top: 1pt solid #A6A6A6;
    padding-top: 6px;
    margin-top: 6pt;
    text-align: center;
    font-size: 8pt;
  }

  .table-footer {
    font-size: 9pt;
  }
}

.is-group-start {
  position: relative;
  border-bottom: none;
  &:after {
    content: '';
    display: block;
    width: 1px;
    height: calc(50% + 1px);
    right: 4px;
    top: 50%;
    background: #000;
    position: absolute;
  }
  &:before {
    content: '';
    display: block;
    width: 4px;
    height: 1px;
    right: 4px;
    top: 50%;
    background: #000;
    position: absolute;
  }
}

.is-group-end {
  position: relative;
  &:after {
    content: '';
    display: block;
    width: 1px;
    height: calc(50% + 1px);
    right: 4px;
    top: -1px;
    background: #000;
    position: absolute;
  }
  &:before {
    content: '';
    display: block;
    width: 4px;
    height: 1px;
    right: 4px;
    top: 50%;
    background: #000;
    position: absolute;
  }
}

.is-group-process {
  position: relative;
  &:after {
    content: '';
    display: block;
    width: 1px;
    height: calc(100% + 2px);
    right: 4px;
    top: -1px;
    background: #000;
    position: absolute;
  }
}
