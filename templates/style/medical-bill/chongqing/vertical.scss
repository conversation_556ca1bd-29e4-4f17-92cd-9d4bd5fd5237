
html,
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
textarea,
p,
blockquote,
th,
td {
    padding: 0;
    margin: 0;
}

.medical-bill-content-vertical {
    @import "../../../components/refund-icon/refund-icon.scss";

    position: absolute;
    width: 84mm;
    height: 90mm;
    font-size: 9pt;

    .bill-item {
        position: absolute;
        top: 0;
        width: 70mm;
        height: 100%;
    }

    .row {
        position: absolute;
    }

    .patient-card-id {
        top: 22mm;
        left: 9mm;
    }

    .patient-name {
        top: 25.5mm;
        left: 9mm;
    }

    .patient-sex {
        top: 25.5mm;
        left: 22mm;
    }

    .patient-age {
        top: 25.5mm;
        left: 28mm;
    }

    .fee-info {
        position: absolute;
        top: 30mm;
        left: 4mm;
        width: 74mm;
        height: 36mm;

        .fee-item {
            position: absolute;
        }

        .col.name {
            display: inline-block;
            _display: inline;
            margin-right: 1mm;
            vertical-align: middle;
        }

        .col.price {
            display: inline-block;
            _display: inline;
            vertical-align: middle;
        }

        .social-item {
            position: absolute;
            top: 20mm;
            left: 0;
        }
    }

    .total {
        top: 70mm;
        left: 20mm;
    }

    .total-case {
        top: 76mm;
        left: 20mm;
    }

    .charge-date {
        top: 86mm;
        left: 46mm;
    }

    .charge-name {
        top: 86mm;
        left: 12mm;
    }

    .institution-name {
        top: 13mm;
        left: 0;
        text-align: center;
        width: 100%;
    }
}
