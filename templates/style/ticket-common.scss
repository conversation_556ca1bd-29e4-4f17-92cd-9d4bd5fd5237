@import "./reset";
@import '../components/layout/print-layout.scss';
.print-ticket-content{
    font-family: "Microsoft YaHei", "微软雅黑";

    .print-col {
        font-size: 9pt;
        line-height: 13pt;
    }
    .overflow-hidden-item {
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
        .print-col {
            overflow: hidden;
            word-break: keep-all;
            white-space: nowrap;
        }
    }

    .overflow-text{
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
    }
    .organ-name {
        margin-top: 4pt;
        font-size: 12pt;
        text-align: center;
    }

    .content-item {
        padding: 0 2pt;
    }

    .get-medicine-place {
        padding: 0 2pt;
        font-weight: bold;
    }

    .get-medicine-time {
        padding: 0 2pt;
    }

    .print-split-line {
        height: 0;
        margin: 2pt 0;
        font-size: 0;
        border-bottom: 1pt dashed #000000;
    }

    .type {
        font-size: 12pt;
        margin: 2pt 0;
        text-align: center;
    }

    .receivable-fee{
        font-size: 11pt;
        line-height: 12pt;
    }

    .text-info {
        font-size: 9pt;
        line-height: 14pt;
        min-height: 14pt;
    }
    .word-break {
        position: relative;
        max-height: 100% !important;
        word-break: break-word !important;
        white-space: normal !important;
        overflow: visible !important;

    }
    .no-change {
        position: relative;
        white-space: nowrap !important;
        overflow: hidden;

    }

    .child-row-wrapper,
    .child-row,
    .compose-child-wrapper {
        .text-info {
            line-height: 12pt;
        }
    }

    .child-row {
        padding-left: 7pt;
    }

    .remark-row .print-col {
        word-break: break-all;
    }

    .print-col {
        &.child-name {
            padding-left: 7pt;
        }
    }

    .compose-child,
    .child-row {
        .print-col {
            padding-top: 0;
            padding-bottom: 0;
        }
    }

    .two-item-row .print-col {
        _height: 28pt;
        max-height: 28pt;
        overflow: hidden;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
    }

    .text-right {
        text-align: right;
    }

    .text-left {
        text-align: left;
    }

    .print-cashier-title {
        font-size: 12pt;
        text-align: center;
    }

    .print-cashier-type {
        margin: 2pt 0 4pt;
        font-size: 11pt;
        text-align: center;
    }

    .qr-code-wrapper {
        margin-top: 8pt;
        text-align: center;
    }

    .qr-code {
        width: 68pt;
        height: 68pt;
        vertical-align: text-top;
    }

    .qr-code-tips {
        display: inline-block;
        _display: inline;
        width: 48pt;
        font-size: 0;
        font-weight: bold;
        text-align: left;
        vertical-align: text-top;

        div {
            display: inline-block;
            _display: inline;
            height: 18pt;
            font-size: 10pt;
        }
    }

    .small-font {
        font-size: 8pt;
    }
}
[data-size=page_热敏小票（80mm）] {
    font-family: "Microsoft YaHei", "微软雅黑";

    .print-col {
        font-size: 10pt;
        line-height: 13pt;
    }

    .overflow-hidden-item {
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
        .print-col {
            overflow: hidden;
            word-break: keep-all;
            white-space: nowrap;
        }
    }

    .overflow-text{
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
    }
    .organ-name {
        margin-top: 4pt;
        font-size: 13pt;
        text-align: center;
    }

    .content-item {
        padding: 0 2pt;
    }

    .print-split-line {
        height: 0;
        margin: 3pt 0;
        font-size: 0;
        border-bottom: 1pt dashed #000000;
    }

    .type {
        font-size: 13pt;
        margin: 2pt 0;
        text-align: center;
    }

    .receivable-fee{
        font-size: 12pt;
        line-height: 12pt;
    }

    .text-info {
        font-size: 10pt;
        line-height: 14pt;
        min-height: 14pt;
    }

    .child-row-wrapper,
    .child-row,
    .compose-child-wrapper {
        .text-info {
            line-height: 12pt;
        }
    }

    .child-row {
        padding-left: 7pt;
    }

    .remark-row .print-col {
        word-break: break-all;
    }

    .print-col {
        &.child-name {
            padding-left: 7pt;
        }
    }

    .compose-child,
    .child-row {
        .print-col {
            padding-top: 0;
            padding-bottom: 0;
        }
    }

    .two-item-row .print-col {
        _height: 28pt;
        max-height: 28pt;
        overflow: hidden;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
    }

    .text-right {
        text-align: right;
    }

    .text-left {
        text-align: left;
    }

    .print-cashier-title {
        font-size: 13pt;
        text-align: center;
    }

    .print-cashier-type {
        margin: 2pt 0 4pt;
        font-size: 11pt;
        text-align: center;
    }

    .qr-code-wrapper {
        margin-top: 8pt;
        text-align: center;
    }

    .qr-code {
        width: 68pt;
        height: 68pt;
        vertical-align: text-top;
    }

    .qr-code-tips {
        display: inline-block;
        _display: inline;
        width: 48pt;
        font-size: 0;
        font-weight: bold;
        text-align: left;
        vertical-align: text-top;

        div {
            display: inline-block;
            _display: inline;
            height: 18pt;
            font-size: 11pt;
        }
    }

    .small-font {
        font-size: 9pt;
    }
}