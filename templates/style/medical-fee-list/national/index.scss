@import "../../reset";

.print-medical-fee-list-wrapper {
    padding: 4mm;
}

.print-medical-fee-list {
    width: 100%;
    font-size: 9pt;
    font-weight: normal;

    th {
        padding-left: 2pt;
        padding-right: 2pt;
    }

    .item-title {
        padding: 4pt;
        font-size: 12pt;
        font-weight: 500;
        text-align: center;
    }

    .border-left-line {
        border-left: 1pt solid #000000;
    }

    .item-head {
        > th {
            padding-bottom: 4px;
        }
    }

    thead {
        tr:nth-last-child(2) th {
            padding-bottom: 8px;
        }
        tr:last-child {
            padding-bottom: 8px;
            th:last-child {
                padding-right: 4px;
            }
        }
    }

    tbody {
        tr:first-child td {
            padding-top: 4px;
        }

        tr:last-child td {
            padding-bottom: 4px;
        }
        tr {
            td:last-child {
                padding-right: 4px;
            }
        }
    }

    tfoot {
        tr:first-child td {
            border-top: 1pt solid #000000;
        }
    }

    td {
        padding-left: 4pt;
        font-size: 9pt;
        line-height: 12pt;
        vertical-align: top;

        &.is-child {
            padding-left: 14pt;
        }
    }

    .border-line {
        > th {
            padding-top: 2px;
            padding-bottom: 2px;
            white-space: nowrap;
            border-top: 1pt solid #000000;
            border-bottom: 1pt solid #000000;
        }
    }

    .tfoot-item {
        width: 100%;

        div {
            display: inline-block;
            width: 25%;
            padding-top: 4px;
            vertical-align: top;
        }
    }

    .page-footer-number {
        position: absolute;
        left: 50%;
    }

    .text-right {
        padding-right: 2pt;
        text-align: right;
    }
    .text-center {
        text-align: center;
    }

    .global-tooth-selected-quadrant {
        position: relative;
        display: inline-flex;
        flex-direction: column;
        width: auto;
        min-width: 32pt;
        height: 17pt;
        vertical-align: middle;

        .top-tooth,
        .bottom-tooth {
            display: flex;
            align-items: center;
            width: 100%;
            min-width: 32pt;
        }

        .left-tooth,
        .right-tooth {
            display: flex;
            align-items: center;
            width: 50%;
            height: 7pt;
            padding: 0 1pt;
            font-family: 'MyKarlaRegular';
            font-size: 9pt;
            letter-spacing: 1px;
            user-select: none;
        }

        .left-tooth {
            justify-content: flex-end;
            border-right: 1pt solid #7a8794;
        }

        .top-tooth {
            min-width: 32pt;
            border-bottom: 1pt solid #7a8794;

            > div {
                padding-bottom: 1px;
            }
        }

        .bottom-tooth {
            > div {
                padding-top: 1px;
            }
        }

        &.all-tooth {
            display: inline-flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }

        .all-tooth {
            min-width: 18pt;
            height: 11pt;
            font-size: 9pt;
            line-height: 11pt;
        }

        &.no-data {
            .left-tooth {
                border-right: 1px dashed #000000;
            }

            .top-tooth {
                border-bottom: 1px dashed #000000;
            }
        }
    }
}