<!--exampleData
{
    "id": "2718141281295646720",
    "outpatientSheetId": "ffffffff0000000025b8b7d810f3c000",
    "formAbstract": {
        "formCategoryId": "5ee9eb9dd4402d06a5c0972a",
        "name": "0-8岁 发育筛查测验(DST）",
        "description": "是我国的一种标准化儿童发育筛查方法，可以用发育商来评价孩子的智能发育速率，也可用智龄来表明其发育水平，为智能超常或发育迟缓提供了可靠的早期诊断依据。，适用于0-8岁儿童。",
        "detail": null
    },
    "indicators": null,
    "formDataDetailResults": [
        {
            "questionName": "0-8岁 发育筛查测验(DST）",
            "totalScore": 4,
            "score": 0,
            "groups": [
                {
                    "groupName": "运动",
                    "totalScore": 1,
                    "score": 0,
                    "result": "运动能力发育正常。目前可在自己的年龄段中可以完成1个及以上检测运动。",
                    "suggest": "在正常的运动水平下，仍然可以加强平时的运动训练，让孩子多去活动，休息与运动结合进行，适量的运动训练可以刺激孩子的能动性，协调性。"
                },
                {
                    "groupName": "社会适应",
                    "totalScore": 1,
                    "score": 0,
                    "result": "社会适应能力正常。目前可在自己的年龄段中可以完成1个及以上检测内容。",
                    "suggest": "在正常的社会适应水平下，还需要加强该类型的日常训练，对小养成孩子独立的人格有很好的帮助，让孩子的性格变得开朗与乐于助人，多让孩子尝试一些自己力所能及的事，试着鼓励他去表达与感受他人的情绪。"
                },
                {
                    "groupName": "智力",
                    "totalScore": 2,
                    "score": 0,
                    "result": "智力发育正常。目前可在自己的年龄段中可以完成1个及以上检测内容。",
                    "suggest": "智力的发育在孩子早期发育中尤为重要，智力的领先可以让孩子在今后的生长中快人一步，因此征程的智力往往不是家长追求的结果，日常针对性的训练时必不可少的，家中可以利用一些玩具工具进行训练，比如：积木，拨浪鼓等，年龄较大的儿童可以通过更具有学科性的测题，如语言类，数学类的测试进行日常训练。"
                }
            ]
        }
    ],
    "hasRule": 1,
    "patient": {
        "id": "ffffffff000000001160d7580ac40000",
        "name": "李琪",
        "namePy": null,
        "namePyFirst": null,
        "birthday": "2022-04-20",
        "mobile": "18910121190",
        "sex": "男",
        "idCard": null,
        "isMember": 1,
        "age": {
            "year": 0,
            "month": 2,
            "day": 14
        },
        "address": null,
        "sn": null,
        "remark": null,
        "profession": null,
        "company": null,
        "patientSource": null,
        "tags": null,
        "marital": null,
        "weight": null,
        "wxOpenId": null,
        "wxHeadImgUrl": null,
        "wxNickName": null,
        "wxBindStatus": null,
        "isAttention": null,
        "shebaoCardInfo": null,
        "childCareInfo": null,
        "chronicArchivesInfo": null
    },
    "patientId": "ffffffff000000001160d7580ac40000",
    "created": "2022-07-04T06:32:03Z"
}
-->
<template>
  <div class="print-child-health-wrapper">
    <h6 class="title">
      {{ printData['formAbstract'].name }}
    </h6>
    <div class="patient-wrapper">
      <div class="patient-item">
        <div>姓名：{{ patient.name }}</div>
        <div>年龄：{{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}</div>
        <div>手机：{{ patient.mobile }}</div>
      </div>
      <div class="patient-item">
        <div>测试日期：{{ printData.created | parseTime('y-m-d') }}</div>
      </div>
    </div>
    <question-result data-type="mix-box" style="margin-top: 24px;" :data="printData"></question-result>
  </div>
</template>

<script>
import {formatAge, parseTime} from "./common/utils.js";
import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
import PageSizeMap, {Orientation} from "../share/page-size.js";
import PrintHandler from './data-handler/common-handler.js';
import QuestionResult from './components/question-result/index.vue';

export default {
  name: 'PrintHealthReport',
  components: {
    QuestionResult
  },
  DataHandler: PrintHandler,
  businessKey: PrintBusinessKeyEnum.CHILD_HEALTHY_TESTS_RESULT,
  filters: {
    parseTime
  },
  pages: [
    {
      paper: PageSizeMap.A4,
      isRecommend: true,
      defaultOrientation: Orientation.portrait, // 默认方向
      defaultHeightLevel: null,
    },
    {
      paper: PageSizeMap.A5,
      isRecommend: false,
      defaultOrientation: Orientation.portrait, // 默认方向
      defaultHeightLevel: null,
    },
  ],
  props: {
    renderData: {
      type: Object,
      required: true,
    },
  },
  computed: {
    printData() {
      return this.renderData.printData || {};
    },
    patient() {
      return this.printData.patient || {};
    },
  },
  methods: {
    formatAge
  },
};
</script>

<style lang="scss">
.print-child-health-wrapper {
  background-color: #ffffff;

  .card-quota {
    page-break-inside: avoid;
  }

  .title {
    text-align: center;
    margin-bottom: 12pt;
    font-size: 14pt;
  }

  .patient-wrapper {
    margin-top: 8pt;
    padding: 4pt;
    border-bottom: 1pt solid #000000;

    .patient-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6pt 0;

      & > div {
        flex: 1;
      }
    }
  }

  .print-result-item {
    page-break-before: auto;
    page-break-after: auto;
    padding: 24pt 0 0;
    page-break-inside: avoid;

    > h3 {
      font-weight: bolder;
      margin-bottom: 12pt;
      font-size: 13pt;
    }

    .chart-wrapper {
      display: flex;
      align-items: center;
    }
  }

  .print-care-record {
    page-break-inside: avoid;
  }

  .card-quota {
    border-bottom: 1pt dashed #000000;
    margin-bottom: 14pt;

    &:last-child {
      border-bottom: none;
    }

    &-name {
      height: 14pt;
      font-size: 10pt;
      font-weight: bold;
      line-height: 14pt;
      margin-bottom: 8pt;
    }

    &-level {
      height: 14pt;
      font-size: 10pt;
      line-height: 14pt;
      margin-bottom: 2pt;
    }

    &-description {
      font-size: 14px;
      line-height: 20px;
      margin-top: 4px;
    }
  }


  .table {
    table {
      width: 100%;
      border-collapse: collapse;
      border-spacing: 0;
    }

    table, td, th {
      border: 1pt solid #000000;
      padding: 4pt;
      color: black;
    }

    td {
      vertical-align: top;

      &:first-child {
        white-space: nowrap;
      }
    }
  }


  .child-health-cell {
    display: flex;
    border: solid 1pt #000000;
    border-bottom: none;
    line-height: 20pt;

    &:last-child {
      border: solid 1pt #000000;
    }

    label {
      display: inline-flex;
      position: relative;
      width: 54pt;
      padding: 4pt;
      align-items: center;
      text-align: center;
      color: black;
      border-right: 1pt solid black;
    }

    div.content {
      flex: 1;
      padding: 2pt 4pt;
      min-height: 14pt;
    }
  }
}
</style>

