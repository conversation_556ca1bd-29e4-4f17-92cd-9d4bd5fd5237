<!--exampleData
{
    rows: [
    {
        "id":"3781566305847443457",
        "patientId":"ffffffff00000000347a87e73fcec000",
        "patientOrderId":"ffffffff00000000347a87e721eec000",
        "patient":{
            "id":"ffffffff00000000347a87e73fcec000",
            "name":"张继科",
            "namePy":"zhangjike",
            "namePyFirst":"ZJK",
            "mobile":"",
            "sex":"男",
            "birthday":"2023-03-02",
            "age":{
                "year":0,
                "month":0,
                "day":15
            },
            "isMember":0,
            "idCard":"",
            "marital":null,
            "weight":null,
            "importFlag":0,
            "ethnicity":"",
            "nationality":null,
            "contactName":null,
            "contactRelation":null,
            "contactMobile":null,
            "sn":"000959",
            "remark":null,
            "profession":null,
            "company":null,
            "blockFlag":0,
            "address":null,
            "tags":null,
            "activeDate":"2023-03-15T09:22:02.000+00:00",
            "activeClinicId":"ffffffff00000000146808c695534004",
            "lastOutpatientDate":null,
            "lastOutpatientClinicId":null,
            "wxOpenId":null,
            "unionId":null,
            "wxUserId":null,
            "wxNickName":null,
            "wxHeadImgUrl":null,
            "isWxMainPatient":0,
            "wxBindStatus":0,
            "patientPoints":null,
            "patientSource":null
        },
        "orderNo":"HG20230912002",
        "businessType":30,
        "bedNumber":null,
        "sampleStatus":0,
        "sampleType":"全血全血全血",
        "patientDepartment":"部门名称",
        "samplePipe":{
            "id":"3777814813552132096",
            "code":"#1",
            "name":"紫色管",
            "color":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAj1JREFUSEvdlU9r03AYx58kv8R0WdPYKrSnsnjQk+AaAl4mevK0swdFyjpfhC/A9yCbMJinXXb1pj1JQ6Z4c7ChkLK0tHQlW7ImaVp5YqOdzmWwpKLJIZA/z4fv9/k+TyiIObaubuWOx8cfSIbI7DwLZI4AXrMLWeCv82DtWeAcOHDSOvnq2/6dar/aP68kFQdc59eXaI6uk3kCrMACEUgIFWXxFNA2bXB77v3Vweq7ywOv0HUEhcA/KESg3/MfrAxW3iYORPhZCpMDTls6R0JbxRsi8Nd+9jB5hRMrf/QQgQUerP3vobEPbPAPU7L0rJQmq3BiaRgYgf19LMyJwn8yNGvC2m2GYT6FY3HO4KOlXt9brDm1j5caC/x4+9H2S7EsPsuKWRjjSY2BEzjICBk4OjwCq2eB03ReLW8u1+IWSeymwQKNRuNpPp/fKJVK4Ps+eJ4HLMtCLpeDbrcLrVYLXNetqqq6kSiwWCzCcDgMoQzDgCRJ0Ol0oN1upwNEhagOgTNR+FeAUQ8JIaGlqfcQgdhHBKYempmnNOohKoxS+v9aGoUm1TnEwZ95aHDwp3uYikJN0x5LkrSJoUHYr7vUNE2890RV1deJ7FJd1xc5jtuRZTm0dHqXYkqbzSZylEqlspMUkA2CYLdcLi9wHHdqlxqGgcv7C8MwNxVF8RMBYhFN0+5RFPWmUCjwPM+HmyYIAjAMYwAAD1VVrcfB8PmF/odRIV3Xb41GoxcAcHdy7z1N088VRfl8ERi+8w3VLvwsXqFQQAAAAABJRU5ErkJggg=="
        },
        "examinationName":"血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规血常规",
        "doctorId":"6e45706922a74966ab51e4ed1e604641",
        "doctorName":null,
        "doctorDepartmentId":"ffffffff0000000034692fa655df0000",
        "doctorDepartmentName":null,
        "diagnosisInfos":[

        ],
        "samplerId":null,
        "samplerName":null,
        "sampleTime":null,
        "collectorId":null,
        "collectorName":null,
        "collectTime":null,
        "rejectReason":"",
        "created":"2023-03-17T03:48:56Z",
        "checked":true,
        "finished":false,
        "no":"202303170001",
        "tube":{
            "id":"3777814813552132096",
            "code":"#1",
            "name":"紫色管",
            "color":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAj1JREFUSEvdlU9r03AYx58kv8R0WdPYKrSnsnjQk+AaAl4mevK0swdFyjpfhC/A9yCbMJinXXb1pj1JQ6Z4c7ChkLK0tHQlW7ImaVp5YqOdzmWwpKLJIZA/z4fv9/k+TyiIObaubuWOx8cfSIbI7DwLZI4AXrMLWeCv82DtWeAcOHDSOvnq2/6dar/aP68kFQdc59eXaI6uk3kCrMACEUgIFWXxFNA2bXB77v3Vweq7ywOv0HUEhcA/KESg3/MfrAxW3iYORPhZCpMDTls6R0JbxRsi8Nd+9jB5hRMrf/QQgQUerP3vobEPbPAPU7L0rJQmq3BiaRgYgf19LMyJwn8yNGvC2m2GYT6FY3HO4KOlXt9brDm1j5caC/x4+9H2S7EsPsuKWRjjSY2BEzjICBk4OjwCq2eB03ReLW8u1+IWSeymwQKNRuNpPp/fKJVK4Ps+eJ4HLMtCLpeDbrcLrVYLXNetqqq6kSiwWCzCcDgMoQzDgCRJ0Ol0oN1upwNEhagOgTNR+FeAUQ8JIaGlqfcQgdhHBKYempmnNOohKoxS+v9aGoUm1TnEwZ95aHDwp3uYikJN0x5LkrSJoUHYr7vUNE2890RV1deJ7FJd1xc5jtuRZTm0dHqXYkqbzSZylEqlspMUkA2CYLdcLi9wHHdqlxqGgcv7C8MwNxVF8RMBYhFN0+5RFPWmUCjwPM+HmyYIAjAMYwAAD1VVrcfB8PmF/odRIV3Xb41GoxcAcHdy7z1N088VRfl8ERi+8w3VLvwsXqFQQAAAAABJRU5ErkJggg=="
        },
        "status":0,
        "statusName":"待采集"
    },
    {
        "id":"3781566305847443457",
        "patientId":"ffffffff00000000347a87e73fcec000",
        "patientOrderId":"ffffffff00000000347a87e721eec000",
        "patient":{
            "id":"ffffffff00000000347a87e73fcec000",
            "name":"张继科",
            "namePy":"zhangjike",
            "namePyFirst":"ZJK",
            "mobile":"",
            "sex":"男",
            "birthday":"2023-03-02",
            "age":{
                "year":0,
                "month":0,
                "day":15
            },
            "isMember":0,
            "idCard":"",
            "marital":null,
            "weight":null,
            "importFlag":0,
            "ethnicity":"",
            "nationality":null,
            "contactName":null,
            "contactRelation":null,
            "contactMobile":null,
            "sn":"000959",
            "remark":null,
            "profession":null,
            "company":null,
            "blockFlag":0,
            "address":null,
            "tags":null,
            "activeDate":"2023-03-15T09:22:02.000+00:00",
            "activeClinicId":"ffffffff00000000146808c695534004",
            "lastOutpatientDate":null,
            "lastOutpatientClinicId":null,
            "wxOpenId":null,
            "unionId":null,
            "wxUserId":null,
            "wxNickName":null,
            "wxHeadImgUrl":null,
            "isWxMainPatient":0,
            "wxBindStatus":0,
            "patientPoints":null,
            "patientSource":null
        },
        "orderNo":"202303170001",
        "businessType":30,
        "bedNumber":null,
        "sampleStatus":0,
        "sampleType":"全血",
        "samplePipe":{
            "id":"3777814813552132096",
            "code":"#1",
            "name":"紫色管",
            "color":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAj1JREFUSEvdlU9r03AYx58kv8R0WdPYKrSnsnjQk+AaAl4mevK0swdFyjpfhC/A9yCbMJinXXb1pj1JQ6Z4c7ChkLK0tHQlW7ImaVp5YqOdzmWwpKLJIZA/z4fv9/k+TyiIObaubuWOx8cfSIbI7DwLZI4AXrMLWeCv82DtWeAcOHDSOvnq2/6dar/aP68kFQdc59eXaI6uk3kCrMACEUgIFWXxFNA2bXB77v3Vweq7ywOv0HUEhcA/KESg3/MfrAxW3iYORPhZCpMDTls6R0JbxRsi8Nd+9jB5hRMrf/QQgQUerP3vobEPbPAPU7L0rJQmq3BiaRgYgf19LMyJwn8yNGvC2m2GYT6FY3HO4KOlXt9brDm1j5caC/x4+9H2S7EsPsuKWRjjSY2BEzjICBk4OjwCq2eB03ReLW8u1+IWSeymwQKNRuNpPp/fKJVK4Ps+eJ4HLMtCLpeDbrcLrVYLXNetqqq6kSiwWCzCcDgMoQzDgCRJ0Ol0oN1upwNEhagOgTNR+FeAUQ8JIaGlqfcQgdhHBKYempmnNOohKoxS+v9aGoUm1TnEwZ95aHDwp3uYikJN0x5LkrSJoUHYr7vUNE2890RV1deJ7FJd1xc5jtuRZTm0dHqXYkqbzSZylEqlspMUkA2CYLdcLi9wHHdqlxqGgcv7C8MwNxVF8RMBYhFN0+5RFPWmUCjwPM+HmyYIAjAMYwAAD1VVrcfB8PmF/odRIV3Xb41GoxcAcHdy7z1N088VRfl8ERi+8w3VLvwsXqFQQAAAAABJRU5ErkJggg=="
        },
        "examinationName":"血常规",
        "doctorId":"6e45706922a74966ab51e4ed1e604641",
        "doctorName":null,
        "doctorDepartmentId":"ffffffff0000000034692fa655df0000",
        "doctorDepartmentName":null,
        "diagnosisInfos":[

        ],
        "samplerId":null,
        "samplerName":null,
        "sampleTime":null,
        "collectorId":null,
        "collectorName":null,
        "collectTime":null,
        "rejectReason":"",
        "created":"2023-03-17T03:48:56Z",
        "checked":true,
        "finished":false,
        "no":"202303170001",
        "tube":{
            "id":"3777814813552132096",
            "code":"#1",
            "name":"紫色管",
            "color":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAj1JREFUSEvdlU9r03AYx58kv8R0WdPYKrSnsnjQk+AaAl4mevK0swdFyjpfhC/A9yCbMJinXXb1pj1JQ6Z4c7ChkLK0tHQlW7ImaVp5YqOdzmWwpKLJIZA/z4fv9/k+TyiIObaubuWOx8cfSIbI7DwLZI4AXrMLWeCv82DtWeAcOHDSOvnq2/6dar/aP68kFQdc59eXaI6uk3kCrMACEUgIFWXxFNA2bXB77v3Vweq7ywOv0HUEhcA/KESg3/MfrAxW3iYORPhZCpMDTls6R0JbxRsi8Nd+9jB5hRMrf/QQgQUerP3vobEPbPAPU7L0rJQmq3BiaRgYgf19LMyJwn8yNGvC2m2GYT6FY3HO4KOlXt9brDm1j5caC/x4+9H2S7EsPsuKWRjjSY2BEzjICBk4OjwCq2eB03ReLW8u1+IWSeymwQKNRuNpPp/fKJVK4Ps+eJ4HLMtCLpeDbrcLrVYLXNetqqq6kSiwWCzCcDgMoQzDgCRJ0Ol0oN1upwNEhagOgTNR+FeAUQ8JIaGlqfcQgdhHBKYempmnNOohKoxS+v9aGoUm1TnEwZ95aHDwp3uYikJN0x5LkrSJoUHYr7vUNE2890RV1deJ7FJd1xc5jtuRZTm0dHqXYkqbzSZylEqlspMUkA2CYLdcLi9wHHdqlxqGgcv7C8MwNxVF8RMBYhFN0+5RFPWmUCjwPM+HmyYIAjAMYwAAD1VVrcfB8PmF/odRIV3Xb41GoxcAcHdy7z1N088VRfl8ERi+8w3VLvwsXqFQQAAAAABJRU5ErkJggg=="
        },
        "status":0,
        "statusName":"待采集"
    }
]
}
-->

<template>
    <div>
        <div
            v-for="(exItem,idx) in printData"
            :key="idx"
            class="examination-tag"
        >
            <div class="top-code">
                <div class="barcode">
                    <div class="barcode-image">
                        <img
                            :src="textToBase64BarCode(exItem.orderNo,{width: 1, displayValue: false, margin: 0})"
                            alt="检验条码"
                        />
                    </div>
                    <div class="barcode-code">
                        {{ exItem.orderNo }}
                    </div>
                </div>
            </div>

            <div class="sample-info">
                <span>{{ getBusinessType(exItem.businessType) }}</span>
                <span>{{ exItem.samplePipe && exItem.samplePipe.name }}</span>
                <span>{{ exItem.sampleType }}</span>
                <span
                    v-if="showDepartmentInfo && !(exItem.patientDepartment && exItem.patientDepartment.name) && (exItem.departmentName || exItem.doctorDepartmentName)"
                >{{ exItem.departmentName || exItem.doctorDepartmentName }}</span>
                <span v-if="exItem.patientDepartment && exItem.patientDepartment.name">{{ exItem.patientDepartment.name }}</span>
            </div>

            <div class="patient-info">
                <strong>{{ exItem.patient && exItem.patient.name }}</strong>
                <strong>{{ exItem.patient && exItem.patient.sex }}</strong>
                <strong>{{ formatAge( exItem.patient && exItem.patient.age, { monthYear: 12, dayYear: 1 } ) }}</strong>
            </div>

            <div class="examination-name">
                {{ exItem.examinationName }}
            </div>
        </div>
    </div>
</template>

<script>
    import barcodeHandler from "./data-handler/barcode-handler.js";
    import { PrintBusinessKeyEnum, ExamBusinessTypeOptions } from "./constant/print-constant.js";
    import { formatAge, isNull, textToBase64BarCode } from "./common/utils.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";

    export default {
        name: "ExaminationTag",
        DataHandler: barcodeHandler,
        businessKey: PrintBusinessKeyEnum.EXAMINATION_TAG,
        pages: [
            {
                paper: PageSizeMap.TAG40_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG40_60,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_70,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_40,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG70_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG70_50,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG80_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG80_50,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
            options: {
                type: Object,
                default: () => ({}),
            },
        },

        data() {
            return {
                ExamBusinessTypeOptions,
            }
        },
        computed: {
            printData() {
                return this.renderData.printData;
            },
            showDepartmentInfo() {
                let pageWidth = this.options.page.width;
                if (isNull(pageWidth)) {
                    const findPage = this.options.template.pages.find((x) => x.paper.name === this.options.page.size);
                    if (findPage) {
                        pageWidth = findPage.paper.width;
                    }
                }
                if (!pageWidth) return true;
                return parseFloat(pageWidth) > 40;
            },
        },
        methods: {
            formatAge,
            textToBase64BarCode,
            getBusinessType(type) {
                const res = ExamBusinessTypeOptions.find(item => {
                    return item.value === type;
                })
                return res?.label;
            }
        }
    }
</script>

<style lang="scss">
// .abc-page_preview {
//   width: 154px !important;
//   height: 116px !important;
//   padding: 0 !important;
// }

.examination-tag {
    * {
        box-sizing: border-box;
    }

    .top-code {
        padding-top: 4pt;
        padding-left: 4pt;
        overflow: hidden;

        .barcode {
            .barcode-image {
                height: 30px;
                overflow: hidden;
                font-size: 0;
            }

            .barcode-image img {
                height: 40px;
            }

            .barcode-code {
                margin-top: 1pt;
                font-size: 9pt;
                line-height: 1;
            }
        }
    }

    .sample-info {
        display: flex;
        flex-wrap: nowrap;
        width: 100%;
        padding-left: 4pt;
        line-height: 9pt;
        white-space: nowrap;

        >span {
            font-size: 8pt;
            white-space: nowrap;

            &:not(:first-child) {
                margin-left: 4pt;
            }
        }
    }

    .patient-info {
        padding: 2pt 4pt 3pt;
        margin-top: 2pt;
        font-size: 9pt;
        font-weight: bold;
        line-height: 1;
        border-top: 1pt solid #000000;
    }

    .examination-name {
        height: 18pt;
        padding: 0 4pt;
        overflow-y: hidden;
        font-size: 8pt;
        line-height: 9pt;
    }
}
</style>
