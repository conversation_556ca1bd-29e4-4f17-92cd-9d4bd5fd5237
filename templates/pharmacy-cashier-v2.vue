<template>
    <div
        class="cashier-v2-wrapper"
        :style="styles"
    >
        <ticket-row v-if="config.titleStyle && config.titleStyleCustomLogo" text-align="center">
            <ticket-image
                :src="config.titleStyleCustomLogo"
                :width="300"
            >
            </ticket-image>
        </ticket-row>

        <ticket-space-line></ticket-space-line>

        <ticket-row
            text-align="center"
            bold
            size="large"
        >
            {{ config.title || organ.name }}
        </ticket-row>

        <ticket-space-line></ticket-space-line>

        <ticket-row v-if="clinicInfo.address && organ.addressDetail" text-align="center">
            {{ organ.addressDetail }}
        </ticket-row>

        <ticket-row v-if="clinicInfo.mobile && organ.contactPhone" text-align="center">
            {{ organ.contactPhone }}
        </ticket-row>

        <ticket-space-line v-if="(clinicInfo.address && organ.addressDetail) || (clinicInfo.mobile && organ.contactPhone)"></ticket-space-line>

        <ticket-row v-if="patientInfo.sellerName">
            销售员：{{ printData.sellerName }}
        </ticket-row>

        <ticket-row v-if="patientInfo.pharmacistName">
            药师姓名：{{ printData.pharmacistName }}
        </ticket-row>

        <ticket-row v-if="patientInfo.pharmacistCode">
            药师编码：{{ printData.pharmacistNationalDoctorCode }}
        </ticket-row>

        <template v-if="feeInfo.chargeItem">
            <ticket-divider-line></ticket-divider-line>
            <ticket-row>
                <ticket-column :span="is80mm ? 26 : 10">
                    名称
                </ticket-column>
                <ticket-column
                    :span="6"
                    text-align="right"
                >
                    数量
                </ticket-column>
                <ticket-column
                    :span="8"
                    text-align="right"
                >
                    单价
                </ticket-column>
                <ticket-column
                    :span="8"
                    text-align="right"
                >
                    小计
                </ticket-column>
            </ticket-row>
            <ticket-divider-line></ticket-divider-line>
            <template v-for="(form, formIndex) in chargeForms">
                <template v-if="form.printFormType === 6 && feeInfo.prescriptionType">
                    <ticket-divider-line
                        v-if="chargeForms.length > 1"
                        :key="`${formIndex}-prescription-divider`"
                    ></ticket-divider-line>
                    <ticket-row
                        v-for="(_, rowIndex) in Math.ceil(form.chargeFormItems.length / 3)"
                        :key="`${formIndex}-${rowIndex}-prescription`"
                    >
                        <template v-for="(col, colIndex) in form.chargeFormItems.slice(rowIndex * 3, rowIndex * 3 + 3)">
                            <ticket-column
                                :key="`${formIndex}-${rowIndex}-${colIndex}-prescription-item`"
                                :span="(() => {
                                    let span = is80mm ? colIndex ? 17 : 14 : colIndex ? 11 : 10;
                                    if (col.itemPrintSetting.singleGoodsCountPerPrescription) span -= 4
                                    return span;
                                })()"
                                :padding-left-span="colIndex ? is80mm ? 3 : 1 : 0"
                            >
                                {{ col.name }}
                            </ticket-column>
                            <ticket-column
                                v-if="col.itemPrintSetting.singleGoodsCountPerPrescription"
                                :key="`${formIndex}-${rowIndex}-${colIndex}-prescription-item-unit`"
                                :span="4"
                                text-align="right"
                            >
                                {{ `${col.unitCount}${col.unit}` }}
                            </ticket-column>
                        </template>
                    </ticket-row>
                </template>
                <template v-else>
                    <template v-for="(item, itemIndex) in form.chargeFormItems">
                        <template v-if="(item.printGoodsType === 'chinese' && !feeInfo.itemNo && !item.itemPrintSetting.medicalFeeGrade && !item.itemPrintSetting.ownExpenseRatio) || (item.printGoodsType === 'nonFormulatedPrescription' && !item.itemPrintSetting.medicalFeeGrade && !item.itemPrintSetting.ownExpenseRatio) || is80mm || (!is80mm && !feeInfo.autoChangeLine)">
                            <ticket-row :key="`${formIndex}-${itemIndex}-info`">
                                <ticket-column
                                    :span="form.printFormType === 6 ? is80mm ? 24 : 8 : is80mm ? 26 : 10"
                                    :is-line-break="!!feeInfo.autoChangeLine"
                                >
                                    {{ item.printName }}
                                </ticket-column>
                                <ticket-column
                                    :span="form.printFormType === 6 ? 8 : 6"
                                    text-align="right"
                                >
                                    {{ `${item.unitCount}${item.unit}${form.printFormType === 6 ? `/${item.count}${item.unit}` : ''}` }}
                                </ticket-column>
                                <ticket-column
                                    :span="8"
                                    text-align="right"
                                >
                                    {{ item.unitPrice | formatMoney }}
                                </ticket-column>
                                <ticket-column
                                    :span="8"
                                    text-align="right"
                                >
                                    {{ item.totalPrice | formatMoney }}
                                </ticket-column>
                            </ticket-row>
                        </template>
                        <template v-else>
                            <ticket-row :key="`${formIndex}-${itemIndex}-printName`">
                                {{ item.printName }}
                            </ticket-row>

                            <ticket-row :key="`${formIndex}-${itemIndex}-info`">
                                <ticket-column
                                    :span="is80mm ? 32 : 16"
                                    text-align="right"
                                >
                                    {{ `${item.unitCount}${item.unit}${form.printFormType === 6 ? `/${item.count}${item.unit}` : ''}` }}
                                </ticket-column>
                                <ticket-column
                                    :span="8"
                                    text-align="right"
                                >
                                    {{ item.unitPrice | formatMoney }}
                                </ticket-column>
                                <ticket-column
                                    :span="8"
                                    text-align="right"
                                >
                                    {{ item.totalPrice | formatMoney }}
                                </ticket-column>
                            </ticket-row>
                        </template>

                        <template v-if="item.showGoodsDetailNum || item.promotionName || (clinicInfo.traceCodeQrCode === 2 && item.traceableCode)">
                            <template v-if="item.promotionName">
                                <ticket-row :key="`${formIndex}-${itemIndex}-promotionName`">
                                    <ticket-column
                                        :span="is80mm ? 40 : 24"
                                        text-align="right"
                                    >
                                        {{ item.promotionName }}
                                    </ticket-column>
                                    <ticket-column
                                        :span="8"
                                        text-align="right"
                                    >
                                        {{ item.totalPromotionFee | formatMoney }}
                                    </ticket-column>
                                </ticket-row>
                            </template>
                            <template v-if="is80mm">
                                <ticket-row
                                    v-if="item.itemPrintSetting.spec || item.itemPrintSetting.dosageForm || item.itemPrintSetting.position"
                                    :key="`${formIndex}-${itemIndex}-spec`"
                                >
                                    <ticket-column
                                        v-if="item.itemPrintSetting.spec"
                                        :span="26"
                                        :padding-left-span="2"
                                    >
                                        规格：{{ item.productInfo ? item.productInfo.displaySpec : '' }}
                                    </ticket-column>
                                    <ticket-column
                                        v-if="item.itemPrintSetting.dosageForm"
                                        :span="item.itemPrintSetting.spec ? 22 : 26"
                                        :padding-left-span="2"
                                    >
                                        剂型：{{ item.productInfo && item.productInfo.dosageFormTypeName }}
                                    </ticket-column>
                                    <ticket-column
                                        v-if="!(item.itemPrintSetting.spec && item.itemPrintSetting.dosageForm) && item.itemPrintSetting.position && item.position"
                                        :span="item.itemPrintSetting.spec || item.itemPrintSetting.dosageForm ? 22 : 48"
                                        :padding-left-span="2"
                                    >
                                        柜号：{{ item.position }}
                                    </ticket-column>
                                </ticket-row>
                                <ticket-row
                                    v-if="item.itemPrintSetting.spec && item.itemPrintSetting.dosageForm && item.itemPrintSetting.position && item.position"
                                    :key="`${formIndex}-${itemIndex}-dosageForm`"
                                >
                                    <ticket-column :padding-left-span="2">
                                        柜号：{{ item.position }}
                                    </ticket-column>
                                </ticket-row>

                                <template v-for="(batch, batchIndex) in item.goodsStockInfos">
                                    <ticket-row
                                        v-if="item.itemPrintSetting.batchNumber || item.itemPrintSetting.validityDate"
                                        :key="`${formIndex}-${itemIndex}-${batchIndex}-batch`"
                                    >
                                        <ticket-column
                                            v-if="item.itemPrintSetting.batchNumber"
                                            :span="26"
                                            :padding-left-span="2"
                                        >
                                            批号：{{ batch.batchNo }}
                                        </ticket-column>
                                        <ticket-column
                                            v-if="item.itemPrintSetting.validityDate"
                                            :span="item.itemPrintSetting.batchNumber ? 22 : 48"
                                            :padding-left-span="2"
                                        >
                                            效期：{{ batch.expiryDate }}
                                        </ticket-column>
                                    </ticket-row>
                                </template>

                                <ticket-row
                                    v-if="item.itemPrintSetting.shebaoCode"
                                    :key="`${formIndex}-${itemIndex}-shebaoCode`"
                                >
                                    <ticket-column
                                        :padding-left-span="2"
                                        :is-line-break="true"
                                    >
                                        医保：{{ item.socialCode || '' }}
                                    </ticket-column>
                                </ticket-row>

                                <ticket-row
                                    v-if="item.itemPrintSetting.manufacturer"
                                    :key="`${formIndex}-${itemIndex}-manufacturer`"
                                >
                                    <ticket-column
                                        :padding-left-span="2"
                                        :is-line-break="true"
                                    >
                                        厂家：{{ item.productInfo ? item.productInfo.manufacturerFull : '' }}
                                    </ticket-column>
                                </ticket-row>

                                <ticket-row
                                    v-if="item.itemPrintSetting.mha"
                                    :key="`${formIndex}-${itemIndex}-mha`"
                                >
                                    <ticket-column
                                        :padding-left-span="2"
                                        :is-line-break="true"
                                    >
                                        上市许可持有人：{{ item.productInfo ? item.productInfo.mha : '' }}
                                    </ticket-column>
                                </ticket-row>

                                <ticket-row
                                    v-if="clinicInfo.traceCodeQrCode === 2 && item.traceableCode"
                                    :key="`${formIndex}-${itemIndex}-trace-code`"
                                >
                                    <ticket-column
                                        :padding-left-span="2"
                                        :span="10"
                                    >
                                        追溯码：
                                    </ticket-column>
                                    <ticket-column
                                        :span="38"
                                        :is-line-break="true"
                                    >
                                        {{ item.traceableCode }}
                                    </ticket-column>
                                </ticket-row>
                            </template>
                            <template v-else>
                                <ticket-row
                                    v-if="item.goodsDetailInfo"
                                    :key="`${formIndex}-${itemIndex}-spec`"
                                >
                                    <ticket-column
                                        :padding-left-span="2"
                                        :is-line-break="true"
                                    >
                                        {{ item.goodsDetailInfo }}
                                    </ticket-column>
                                </ticket-row>
                            </template>

                            <ticket-space-line
                                v-if="formIndex !== chargeForms.length - (hasChinesePrescription ? 2 : 1) || itemIndex !== form.chargeFormItems.length - 1"
                                :key="`${formIndex}-${itemIndex}-spaceLine`"
                            ></ticket-space-line>
                        </template>
                    </template>
                </template>

                <template v-if="form.printFormType === 6">
                    <template v-if="form.doseCount">
                        <ticket-row :key="`${formIndex}-doseCount`">
                            <ticket-column :span="is80mm ? 39 : 23">
                                共{{ form.doseCount }}剂({{ form.chargeFormItems.length }}味)，{{ form.totalPrice / form.doseCount | formatMoney }}/剂
                            </ticket-column>
                            <ticket-column
                                :span="9"
                                text-align="right"
                            >
                                {{ form.totalPrice | formatMoney }}
                            </ticket-column>
                        </ticket-row>
                    </template>

                    <template v-if="form.totalPromotionFee">
                        <ticket-row :key="`${formIndex}-promotionFee`">
                            <ticket-column :span="is80mm ? 38 : 22">
                                处方优惠
                            </ticket-column>
                            <ticket-column
                                :span="10"
                                text-align="right"
                            >
                                {{ form.totalPromotionFee | formatMoney }}
                            </ticket-column>
                        </ticket-row>
                    </template>
                </template>
            </template>
        </template>

        <template v-if="cashierInfo.totalFee || (cashierInfo.singlePromotionFee === 1 && !isEmpty(printData.singlePromotionFee)) || cashierInfo.receivableFee || cashierInfo.netIncomeFee">
            <ticket-divider-line></ticket-divider-line>
            <template v-if="cashierInfo.totalFee || (cashierInfo.singlePromotionFee === 1 && !isEmpty(printData.singlePromotionFee))">
                <ticket-row v-if="cashierInfo.totalFee">
                    <ticket-column :span="8">
                        合计
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ cashierInfo.singlePromotionFee === 2 ? printData.singlePromotionedTotalFee : printData.totalFee | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="cashierInfo.singlePromotionFee === 1 && !isEmpty(printData.singlePromotionFee)">
                    <ticket-column :span="8">
                        优惠
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ printData.singlePromotionFee | formatMoney }}
                    </ticket-column>
                </ticket-row>
            </template>

            <template v-if="cashierInfo.receivableFee || cashierInfo.netIncomeFee">
                <ticket-row v-if="cashierInfo.receivableFee">
                    <ticket-column
                        :span="8"
                        bold
                    >
                        应收
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        bold
                        text-align="right"
                    >
                        {{ printData.receivableFee | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="cashierInfo.netIncomeFee">
                    <ticket-column :span="4">
                        实收
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 44 : 28"
                        text-align="right"
                        :is-line-break="true"
                    >
                        {{ netIncomeFee }}
                    </ticket-column>
                </ticket-row>
            </template>
        </template>

        <template
            v-if="hasCustomerInfo ||
                (memberInfoConfig.mobileType && patient.mobile) ||
                (memberInfo && (memberInfoConfig.level || memberInfoConfig.balance || memberInfoConfig.changePoints || memberInfoConfig.points))"
        >
            <ticket-divider-line></ticket-divider-line>

            <ticket-row v-if="hasCustomerInfo">
                <ticket-column :span="8">
                    会员
                </ticket-column>
                <ticket-column
                    :span="is80mm ? 40 : 24"
                    text-align="right"
                >
                    {{ customerInfo }}
                </ticket-column>
            </ticket-row>

            <ticket-row v-if="memberInfoConfig.mobileType && patient.mobile">
                <ticket-column :span="8">
                    手机号
                </ticket-column>

                <ticket-column
                    :span="is80mm ? 40 : 24"
                    text-align="right"
                >
                    {{ patient.mobile | filterMobileV2(memberInfoConfig.mobileType) }}
                </ticket-column>
            </ticket-row>

            <template v-if="memberInfo">
                <ticket-row v-if="memberInfoConfig.level">
                    <ticket-column :span="8">
                        会员等级
                    </ticket-column>

                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ memberInfo.memberType.name }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="memberInfoConfig.balance">
                    <ticket-column :span="8">
                        会员余额
                    </ticket-column>

                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ memberInfo.cardBalance | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="memberInfoConfig.changePoints">
                    <ticket-column :span="8">
                        本次增加积分
                    </ticket-column>

                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ memberInfo.changePoints }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="memberInfoConfig.points">
                    <ticket-column :span="8">
                        剩余积分
                    </ticket-column>

                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ memberInfo.points }}
                    </ticket-column>
                </ticket-row>
            </template>
        </template>

        <template v-if="shebaoPayment && (healthCardInfo.hospitalInfo || healthCardInfo.cardInfo || healthCardInfo.settlementInfo || healthCardInfo.balanceInfo)">
            <ticket-divider-line></ticket-divider-line>

            <template v-if="healthCardInfo.hospitalInfo">
                <ticket-row>
                    <ticket-column :span="8">
                        定点编号
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.hospitalCode }}
                    </ticket-column>
                </ticket-row>

                <ticket-row>
                    <ticket-column :span="8">
                        定点名称
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                        :is-line-break="true"
                        :padding-left-span="2"
                    >
                        {{ shebaoPayment.extraInfo.hospitalName }}
                    </ticket-column>
                </ticket-row>
            </template>

            <template v-if="healthCardInfo.cardInfo">
                <ticket-row>
                    <ticket-column :span="8">
                        持卡人
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ shebaoPayment.cardOwner }}
                    </ticket-column>
                </ticket-row>

                <ticket-row>
                    <ticket-column :span="8">
                        医保卡号
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ shebaoPayment.cardId }}
                    </ticket-column>
                </ticket-row>

                <ticket-row>
                    <ticket-column :span="8">
                        人员编号
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                        :is-line-break="true"
                        :padding-left-span="2"
                    >
                        {{ shebaoPayment.extraInfo.psnNo }}
                    </ticket-column>
                </ticket-row>
            </template>

            <template v-if="healthCardInfo.settlementInfo">
                <ticket-row>
                    <ticket-column :span="8">
                        医疗类别
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.insutype }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.extraInfo.actPayDedc) || healthCardInfo.settlementInfo === 1">
                    <ticket-column :span="8">
                        本次起付线
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.actPayDedc | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.fundPaymentFee) || healthCardInfo.settlementInfo === 1">
                    <ticket-column :span="8">
                        基金支付
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ shebaoPayment.fundPaymentFee | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.extraInfo.hifpPay) || healthCardInfo.settlementInfo === 1">
                    <ticket-column
                        :span="10"
                        :padding-left-span="2"
                    >
                        基本统筹
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 38 : 22"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.hifpPay | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.extraInfo.cvlservPay) || healthCardInfo.settlementInfo === 1">
                    <ticket-column
                        :span="10"
                        :padding-left-span="2"
                    >
                        公务员补助
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 38 : 22"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.cvlservPay | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.extraInfo.hifobPay) || healthCardInfo.settlementInfo === 1">
                    <ticket-column
                        :span="10"
                        :padding-left-span="2"
                    >
                        职工大额补助
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 38 : 22"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.hifobPay | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.extraInfo.hifmiPay) || healthCardInfo.settlementInfo === 1">
                    <ticket-column
                        :span="10"
                        :padding-left-span="2"
                    >
                        居民大病保险
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 38 : 22"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.hifmiPay | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.extraInfo.mafPay) || healthCardInfo.settlementInfo === 1">
                    <ticket-column
                        :span="10"
                        :padding-left-span="2"
                    >
                        医疗救助
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 38 : 22"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.mafPay | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.extraInfo.hifesPay) || healthCardInfo.settlementInfo === 1">
                    <ticket-column
                        :span="10"
                        :padding-left-span="2"
                    >
                        企业补充医疗保险
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 38 : 22"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.hifesPay | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.extraInfo.othPay) || healthCardInfo.settlementInfo === 1">
                    <ticket-column
                        :span="10"
                        :padding-left-span="2"
                    >
                        其他
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 38 : 22"
                        text-align="right"
                    >
                        {{ shebaoPayment.extraInfo.othPay | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.accountPaymentFee) || healthCardInfo.settlementInfo === 1">
                    <ticket-column :span="8">
                        个账支付
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 40 : 24"
                        text-align="right"
                    >
                        {{ shebaoPayment.accountPaymentFee | formatMoney }}
                    </ticket-column>
                </ticket-row>

                <ticket-row v-if="!isEmpty(shebaoPayment.extraInfo.acctMulaidPay) || healthCardInfo.settlementInfo === 1">
                    <ticket-column
                        :span="10"
                        :padding-left-span="2"
                    >
                        共济账户
                    </ticket-column>
                    <ticket-column
                        :span="is80mm ? 38 : 22"
                        text-align="right"
                        :is-line-break="true"
                        :padding-left-span="2"
                    >
                        {{ shebaoPayment.extraInfo.acctMulaidPay | formatMoney }}
                    </ticket-column>
                </ticket-row>
            </template>

            <ticket-row v-if="healthCardInfo.balanceInfo">
                <ticket-column :span="8">
                    个账余额
                </ticket-column>
                <ticket-column
                    :span="is80mm ? 40 : 24"
                    text-align="right"
                >
                    {{ `(结算前${formatMoney(shebaoPayment.beforeCardBalance)})${formatMoney(shebaoPayment.cardBalance)}` }}
                </ticket-column>
            </ticket-row>
        </template>

        <template v-if="clinicInfo.chargeOperator || clinicInfo.chargeDate || clinicInfo.printDate || remark || clinicInfo.patientSign">
            <ticket-divider-line></ticket-divider-line>

            <ticket-row v-if="clinicInfo.chargeOperator">
                <ticket-column :span="8">
                    收费员
                </ticket-column>
                <ticket-column
                    :span="is80mm ? 40 : 24"
                    text-align="right"
                >
                    {{ printData.chargedByName }}
                </ticket-column>
            </ticket-row>

            <ticket-row v-if="clinicInfo.chargeDate">
                <ticket-column :span="8">
                    收费时间
                </ticket-column>
                <ticket-column
                    :span="is80mm ? 40 : 24"
                    text-align="right"
                >
                    {{ printData.chargedTime | formatDate('YYYY-MM-DD HH:mm:ss') }}
                </ticket-column>
            </ticket-row>

            <ticket-row v-if="clinicInfo.printDate">
                <ticket-column :span="8">
                    打印时间
                </ticket-column>
                <ticket-column
                    :span="is80mm ? 40 : 24"
                    text-align="right"
                >
                    {{ new Date() | formatDate('YYYY-MM-DD HH:mm:ss') }}
                </ticket-column>
            </ticket-row>

            <ticket-row v-if="remark">
                备注：{{ remark }}
            </ticket-row>

            <ticket-row v-if="clinicInfo.patientSign">
                顾客签字：
            </ticket-row>
        </template>

        <template v-if="notice.length">
            <ticket-divider-line></ticket-divider-line>
            <ticket-row
                v-for="(item, index) in notice"
                :key="index"
            >
                {{ item }}
            </ticket-row>
        </template>

        <template v-if="clinicInfo.replacementReminder || (config.invoiceCode && invoiceQrcode) || (clinicInfo.traceCodeQrCode === 1 && traceCodeQrCodeUrl)">
            <ticket-divider-line></ticket-divider-line>

            <template v-if="clinicInfo.replacementReminder">
                <ticket-row
                    text-align="center"
                    bold
                >
                    药品离柜，概不退换
                </ticket-row>
            </template>

            <template v-if="config.invoiceCode && invoiceQrcode">
                <ticket-space-line></ticket-space-line>

                <ticket-row text-align="center">
                    <ticket-image
                        :src="invoiceQrcode"
                        :width="is80mm ? 144 : 120"
                    >
                    </ticket-image>
                </ticket-row>

                <ticket-space-line></ticket-space-line>

                <ticket-row text-align="center">
                    扫码查看电子发票
                </ticket-row>
            </template>

            <template v-if="clinicInfo.traceCodeQrCode === 1 && traceCodeQrCodeUrl">
                <ticket-space-line></ticket-space-line>

                <ticket-row text-align="center">
                    <ticket-image
                        :src="traceCodeQrCodeUrl"
                        :width="is80mm ? 144 : 120"
                    >
                    </ticket-image>
                </ticket-row>

                <ticket-space-line></ticket-space-line>

                <ticket-row text-align="center">
                    扫码查看药品追溯码
                </ticket-row>
            </template>

            <template v-if="is80mm">
                <ticket-space-line></ticket-space-line>
                <ticket-space-line></ticket-space-line>
            </template>
        </template>
    </div>
</template>

<script>
    import CommonDataHandler from "./data-handler/common-handler.js";
    import {PrintBusinessKeyEnum, PromotionTypeEnum, RuleTypeEnum} from './constant/print-constant';
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import TicketRow from './components/ticket-esc-pos-components/ticket-row.vue';
    import TicketDividerLine from './components/ticket-esc-pos-components/ticket-divider-line.vue';
    import TicketColumn from './components/ticket-esc-pos-components/ticket-column.vue';
    import TicketSpaceLine from './components/ticket-esc-pos-components/ticket-space-line.vue';
    import TicketImage from './components/ticket-esc-pos-components/ticket-image.vue';

    import {formatAge, formatMoney, medicalFeeGrade2PrintStr, clone, add} from './common/utils';
    import {filterMobileV2, filterName} from "./common/medical-transformat";
    import {formatDate} from '@tool/date';

    export default {
        name: 'PharmacyCashierV2',
        components: {
            TicketImage,
            TicketSpaceLine,
            TicketColumn,
            TicketDividerLine,
            TicketRow,
        },
        filters: {
            filterMobileV2,
            formatDate,
            formatMoney,
            filterName,
        },
        DataHandler: CommonDataHandler,
        businessKey: PrintBusinessKeyEnum.PHARMACY_CASHIER_V2,
        pages: [
            {
                paper: PageSizeMap.MM58,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        provide() {
            return {
                baseWidth: this.baseWidth,
                characterCount: this.characterCount,
            };
        },
        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
            extra: {
                type: Object,
                default: () => ({}),
            },
            options: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            baseWidth() {
                return this.extra.TICKET_ESC_POS_BASE_WIDTH || 12; // 一个格子的宽度设置为 12px
            },
            ticketPageSize() {
                const pageSize = this.options.page?.size;
                return pageSize || PageSizeMap.MM58.name;
            },
            is80mm() {
                return this.ticketPageSize === PageSizeMap.MM80.name;
            },
            characterCount() {
                return this.extra.TicketCharacterCountMap?.[this.ticketPageSize] || 32; // 58mm 对应 32 个格子，80mm 对应 48 个格子
            },
            styles() {
                return {
                    width: `${this.baseWidth * this.characterCount}px`,
                    maxWidth: `${this.baseWidth * this.characterCount}px`,
                    minWidth: `${this.baseWidth * this.characterCount}px`,
                    fontSize: `${2 * this.baseWidth}px`,
                };
            },

            config() {
                if(this.renderData.config && this.renderData.config.cashier) {
                    return this.renderData.config.cashier;
                }
                return {};
            },
            clinicInfo() {
                return this.config.clinicInfo || {};
            },
            notice() {
                return (this.config.remark || '')
                    .replaceAll('<br />', '<br>')
                    .replaceAll('<br/>', '<br>')
                    .split('<br>')
                    .filter(one => one);
            },
            patientInfo() {
                return this.config.patientInfo || {};
            },
            feeInfo() {
                return this.config.feeInfo || {};
            },
            cashierInfo() {
                return this.config.cashierInfo || {};
            },
            memberInfoConfig() {
                return this.config.memberInfo || {};
            },
            healthCardInfo() {
                return this.config.healthCardInfo || {};
            },
            itemDetail() {
                const itemDetail = clone(this.feeInfo.itemDetail) || []
                return itemDetail.reduce((obj, current) => {
                    if (Array.isArray(current.items)) {
                        current.items = (current.items || []).reduce((pre, cur) => {
                            pre[cur.key] = cur.value;
                            return pre;
                        }, {});
                    }
                    obj[current.type] = current.items;
                    return obj;
                }, {});
            },

            printData() {
                return this.renderData.printData
            },
            organ() {
                return this.printData.organ || {};
            },
            patient() {
                return this.printData.patient || {};
            },
            memberInfo() {
                return this.printData.memberInfo;
            },
            shebaoPayment() {
                return this.printData.shebaoPayment;
            },
            netIncomeFee() {
                return this.printData.chargeTransactions.reduce((str, cur) => str + `(${cur.payModeName})${formatMoney(cur.amount)}`, '');
            },
            invoiceQrcode() {
                return this.printData.invoiceQrcode || '';
            },
            traceCodeQrCodeUrl() {
                return this.printData.traceCodeQrCodeUrl || '';
            },
            remark() {
                let remark = [];
                if (this.clinicInfo.retailRemark) {
                    remark.push(this.printData.remarks);
                }
                if (this.clinicInfo.remark) {
                    remark.push(this.printData.latestChargeComment);
                }
                return remark.filter(item => item).join('；');
            },

            hasChinesePrescription() {
                return this.chargeForms.some(form => form.printFormType === 6);
            },

            chargeForms() {
                const chargeForms = (this.printData.chargeForms || []).sort((a, b) => {
                    // 顺序： 中西成药、非配方饮片、商品、其他、赠品、配方饮片、
                    const priorityOrder = { 4: 0, 28: 1, 9: 2, 10: 4, 6: 5 };
                    const getPriority = (type) => priorityOrder[type] !== undefined ? priorityOrder[type] : 3;

                    const priorityA = getPriority(a.printFormType);
                    const priorityB = getPriority(b.printFormType);

                    if (priorityA !== priorityB) return priorityA - priorityB;
                    return a.printFormType - b.printFormType;
                });
                let index = 0;
                chargeForms.forEach(form => {
                    form.chargeFormItems = form.chargeFormItems.map(item => {
                        const itemInfo = {
                            ...item,
                            index: ++index,
                        }
                        itemInfo.printGoodsType = this.getChargeFormType(item.productType, item.productSubType, item.goodsTypeId);
                        itemInfo.showGoodsDetailNum = this.calcShowGoodsDetailNum(itemInfo.printGoodsType);
                        itemInfo.itemPrintSetting = this.itemDetail[itemInfo.printGoodsType] || {};
                        itemInfo.socialInfo = this.createSocialInfo(itemInfo);
                        itemInfo.printName = this.createName(form, itemInfo);
                        itemInfo.promotionName = this.createPromotionInfo(form, itemInfo);
                        if (this.clinicInfo.traceCodeQrCode === 2 && itemInfo.traceableCodeList?.length) {
                            itemInfo.traceableCode = itemInfo.traceableCodeList.map(item => item.no).join('，');
                        }
                        if (!this.is80mm) {
                            itemInfo.goodsDetailInfo = this.createGoodsDetailInfo(itemInfo);
                        }
                        return itemInfo;
                    });
                    if (form.printFormType === 6 && this.cashierInfo.singlePromotionFee === 2 && this.feeInfo.prescriptionType) {
                        form.totalPromotionFee = form.chargeFormItems.reduce((pre, cur) => add(pre, cur.totalPromotionFee), 0);
                    }
                });
                return chargeForms;
            },
            customerInfo() {
                const { nameType } = this.memberInfoConfig;
                const name = nameType ? filterName(this.patient.name, nameType) : '';
                const sex = this.memberInfoConfig.sex ? this.patient.sex : '';
                const age = this.memberInfoConfig.age ? formatAge(this.patient.age, { monthYear: 12, dayYear: 1 }) : ''
                return [name, sex, age].join(' ');
            },
            hasCustomerInfo() {
                return this.customerInfo.replace(' ', '');
            },
        },
        methods: {
            add,
            formatAge,
            formatMoney,
            medicalFeeGrade2PrintStr,
            isEmpty(value) {
                return value === undefined || value === null || value === '' || value === '0' || value === 0
            },
            getChargeFormType(productType, productSubType, goodsTypeId) {
                if (productType === 1 && productSubType === 2) {
                    if (goodsTypeId === 14) return 'chinese';
                    return 'nonFormulatedPrescription';
                }
                if (productType === 1) return 'westernMedicine';
                if (productType === 2) return 'materialGoods';
                return 'productGoods';
            },
            createSocialInfo(item) {
                let info = '';
                const isExitMedicalFeeGrade = item.itemPrintSetting.medicalFeeGrade && item.medicalFeeGrade;
                const isExitOwnExpenseRatio = item.itemPrintSetting.ownExpenseRatio && item.productInfo.selfPayProp;
                if (isExitMedicalFeeGrade || isExitOwnExpenseRatio) {
                    info += '[';
                }
                if (isExitMedicalFeeGrade) {
                    info += this.medicalFeeGrade2PrintStr(item.medicalFeeGrade);
                }
                if (isExitOwnExpenseRatio) {
                    info += this.getSelfPayProp(item.productInfo.selfPayProp);
                }
                if (isExitMedicalFeeGrade || isExitOwnExpenseRatio) {
                    info += ']';
                }
                return info;
            },
            createName(form, item) {
                let name = '';
                if (this.feeInfo.itemNo) {
                    name += item.index ? `${item.index}.` : '';
                }
                if (form.printFormType === 10) {
                    name += '[赠品]';
                }
                name += item.socialInfo;
                name += item.name;
                return name;
            },
            calcShowGoodsDetailNum(formType) {
                return (this.feeInfo.itemDetail?.find(item => item.type === formType)?.items || []).reduce((pre, cur) => {
                    if (cur.key !== 'singleGoodsCountPerPrescription' && cur.key !== 'medicalFeeGrade'  && cur.key !== 'ownExpenseRatio') {
                        pre += (cur.value ? 1 : 0);
                    }
                    return pre;
                }, 0);
            },
            getSelfPayProp(selfPayList) {
                const target = selfPayList[this.shebaoPayment?.extraInfo?.insutypeCode];
                if (target) return `${target}%`;
                const keys = Object.keys(selfPayList);
                return `${selfPayList[keys[0]]}%`
            },
            createPromotionInfo(form, item) {
                let promotionName = null;
                if (this.cashierInfo.singlePromotionFee === 2) {
                    if (form.printFormType === 10 || item.unitAdjustmentFee) {
                        return '单品优惠';
                    }
                    if (item.singlePromotionFee) {
                        const target = (item.singlePromotions || []).find(item => item.checked);
                        if (target) {
                            const {
                                hitRuleDetail = {},
                            } = target;
                            return this.transCalc2promotionStr({
                                ...hitRuleDetail,
                                displayPackageUnit: item.unit,
                                goodsId: item.productId,
                            }) || target.name;
                        }
                    }
                }
                return promotionName;
            },
            transCalc2promotionStr(data) {
                if (!data) return '';
                const {
                    type = 2, // 折扣类型 1:类型; 2:商品
                    discountWay = PromotionTypeEnum.discount,
                    ruleType = RuleTypeEnum.fixed,
                    displayPackageUnit = '盒',
                    goodsTypeName,
                    discount,
                    enoughNRuleDetails,
                    theNRuleDetail,
                    giftBySingleGoodsRuleDetails,
                    goodsId,
                } = clone(data);

                let displayUnit = '';
                if (discountWay === PromotionTypeEnum.discount) {
                    displayUnit = '折';
                } else if (discountWay === PromotionTypeEnum.special) {
                    displayUnit = '元';
                }

                // 分类折扣特殊展示
                if (
                    discountWay === PromotionTypeEnum.discount &&
                    ruleType === RuleTypeEnum.fixed &&
                    type === 1
                ) {
                    const discountValue = this.getDiscountValue(discountWay, discount);
                    return `${goodsTypeName}${discountValue}${displayUnit}`;
                }

                switch (ruleType) {
                    case RuleTypeEnum.fixed: {
                        if (!discount) return '';
                        const discountValue = this.getDiscountValue(discountWay, discount);
                        return `每${displayPackageUnit}${discountValue}${displayUnit}`;
                    }
                    case RuleTypeEnum.enoughN: {
                        const {
                            isCycle,
                            thresholdCount,
                            discountValue,
                        } = enoughNRuleDetails || {};
                        if (!discountValue) return '';
                        const _value = this.getDiscountValue(discountWay, discountValue) ;
                        let res = '';
                        if (isCycle) {
                            res += '每';
                        } else {
                            res += `买满${thresholdCount}${displayPackageUnit}，前`;
                        }
                        res += `${thresholdCount}${displayPackageUnit}${discountWay === PromotionTypeEnum.special ? '共' : ''}${_value}${displayUnit}`;
                        return res;
                    }
                    case RuleTypeEnum.theN: {
                        const {
                            isCycle,
                            thresholdCount,
                            discountValue,
                        } = theNRuleDetail || {};
                        if (!discountValue) return '';
                        const _value = this.getDiscountValue(discountWay, discountValue) ;
                        let res = '';
                        if (isCycle) {
                            res += `每买${thresholdCount}${displayPackageUnit}，`;
                        } else {
                            res += '仅';
                        }
                        res += `第${thresholdCount}${displayPackageUnit}${_value}${displayUnit}`;
                        return res;
                    }
                    case RuleTypeEnum.gift: {
                        const {
                            isCycle,
                            thresholdCount,
                            discountValue,
                            giftGoodsId,
                            giftGoodsName,
                            giftGoodsUnit,
                        } = giftBySingleGoodsRuleDetails || {};

                        if (!discountValue) return '';
                        const isSameGoods = goodsId === giftGoodsId;
                        const _value = this.getDiscountValue(discountWay, discountValue) ;
                        if (isSameGoods) {
                            if (isCycle) {
                                return `每满${thresholdCount}${displayPackageUnit}，赠${_value}${displayPackageUnit}`;
                            }
                            return `买${thresholdCount}赠${_value}`;
                        }
                        const disGiftGoods = `${giftGoodsName.slice(0,3)}${giftGoodsName.slice.length > 3 ? '...' : ''}*${_value}${giftGoodsUnit}`;
                        if (isCycle) {
                            return `每满${thresholdCount}${displayPackageUnit}，赠${disGiftGoods}`;
                        }
                        return `买满${thresholdCount}${displayPackageUnit}赠${disGiftGoods}`;
                    }
                    default:
                        return '';
                }
            },
            getDiscountValue(discountType, discountValue) {
                if (discountType === PromotionTypeEnum.discount && discountValue) {
                    discountValue *= 10;
                    discountValue = discountValue.toFixed(2);
                    discountValue = +discountValue;
                }
                return discountValue;
            },
            createGoodsDetailInfo(item) {
                let info = [];
                if (item.itemPrintSetting.spec && item.productInfo?.displaySpec) {
                    info.push(item.productInfo.displaySpec);
                }
                if (item.itemPrintSetting.dosageForm && item.productInfo?.dosageFormTypeName) {
                    info.push(item.productInfo.dosageFormTypeName);
                }
                if (item.itemPrintSetting.position && item.position) {
                    info.push(item.position);
                }
                (item.goodsStockInfos || []).forEach(stock => {
                    if (item.itemPrintSetting.batchNumber && stock.batchNo) {
                        info.push(stock.batchNo);
                    }
                    if (item.itemPrintSetting.validityDate && stock.expiryDate) {
                        info.push(stock.expiryDate);
                    }
                });
                if (item.itemPrintSetting.shebaoCode && item?.socialCode) {
                    info.push(item.socialCode);
                }
                if (item.itemPrintSetting.manufacturer && item.productInfo?.manufacturerFull) {
                    info.push(item.productInfo.manufacturerFull);
                }
                if (item.itemPrintSetting.mha && item.productInfo?.mha) {
                    info.push(item.productInfo.mha);
                }
                if (this.clinicInfo.traceCodeQrCode === 2 && item.traceableCode) {
                    info.push(item.traceableCode);
                }
                return info.join(' / ');
            },
        },
    }
</script>

<style lang="scss">
.cashier-v2-wrapper {
    box-sizing: border-box;
    font-family: 'Sarasa Mono SC', monospace;

    * {
        box-sizing: border-box;
    }
}
</style>
