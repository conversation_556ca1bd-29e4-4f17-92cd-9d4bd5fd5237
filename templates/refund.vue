<!--exampleData
{
    patient: {
        id: '622c7a3a31e14b72b67e7c717f65ab79',
        name: 'wxd1109',
        namePy: null,
        namePyFirst: null,
        birthday: null,
        mobile: '19912312321',
        sex: '男',
        age: {
            year: 1,
            month: 2,
            day: 16,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: null,
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '一二三四五六七八九十一二三四五六七八九十一二三四五六七八九',
        shortName: '高新大源店',
        addressDetail: '大源北支路28号大源北支路28号',
        contactPhone: '**********',
        logo: null,
    },
    memberType: '挂号7折卡',
    memberCardMobile: '19912312321',
    principal: 12.0,
    present: 12.0,
    payMode: 1,
    netIncomeFee: 12.0,
    memberCardBalance: 24.0,
    chargedByName: '刘喜喜喜',
    chargedTime: '2020-02-03T11:15:10Z',
};
-->
<template>
    <div>
        <div class="print-recharge-content">
            <print-row>
                <print-col
                    :span="24"
                    class="print-clinic-name"
                >
                    {{ clinicName }}
                </print-col>
            </print-row>
            <print-row>
                <print-col
                    :span="24"
                    class="print-recharge-type"
                >
                    {{ printData.cardName ? '卡项退款凭证' : '会员退款凭证' }}
                </print-col>
            </print-row>
            <div class="content-item">
                <print-row>
                    <print-col :span="16">
                        姓名：{{ patient.name }}
                    </print-col>
                    <print-col :span="8">
                        性别：{{ patient.sex }}
                    </print-col>
                </print-row>
            </div>
            <print-row v-if="patient.mobile">
                <print-col :span="24">
                    <span class="text-bold">手机号</span>&nbsp;{{ patient.mobile | filterMobile }}
                </print-col>
            </print-row>

            <div class="print-split-line"></div>

            <div class="content-item">
                <print-row v-if="card.name">
                    <print-col :span="24">
                        <span class="text-bold">卡名称：</span>&nbsp;{{ card.name }}
                    </print-col>
                </print-row>
                <print-row v-if="printData.cardBalance || printData.cardBalance === 0">
                    <print-col :span="24">
                        <span class="text-bold">卡余额：</span>&nbsp;
                        <span v-if="printData.openCard">
                            <span style="margin-right: 20px;">本金 {{ $t('currencySymbol') }}{{ printData.principal | formatMoney }}</span>
                            <span>赠金 {{ $t('currencySymbol') }}{{ printData.present | formatMoney }}</span></span>
                        <span v-else>{{ printData.cardBalance | formatMoney }}</span>
                    </print-col>
                </print-row>

                <print-row v-if="rechargeFormat(card.rechargeRights).length">
                    <print-col :span="24">
                        <span class="text-bold">充送权益：</span>&nbsp;<span
                            v-for="(str,index) in rechargeFormat(card.rechargeRights)"
                            :key="str + index"
                            class="discount-item charge-right"
                        >{{ str }}</span>
                    </print-col>
                </print-row>

                <print-row v-if="discountFormat(card.discountRights).length">
                    <print-col :span="24">
                        <span class="text-bold">折扣权益：</span>&nbsp;{{ discountFormat(card.discountRights).join('，') }}
                    </print-col>
                </print-row>

                <print-row v-if="card.presentRights && card.presentRights.isOpen">
                    <print-col :span="24">
                        <span class="text-bold">抵扣权益：</span>&nbsp;<span
                            v-for="(str,index) in presentFormat(printData.patientPresents)"
                            :key="str + index"
                            class="discount-item"
                        >
                            {{ str }}
                        </span>
                    </print-col>
                </print-row>

                <print-row v-if="printData.beginDate">
                    <print-col :span="24">
                        <span class="text-bold">生效日期：</span>&nbsp;<span>{{ printData.beginDate | parseTime }}</span>
                    </print-col>
                </print-row>

                <print-row v-if="printData.openCard">
                    <print-col :span="24">
                        <span class="text-bold">到期日期：</span>&nbsp;{{ printData.endDate || '永久有效' | parseTime }}
                    </print-col>
                </print-row>
                <print-row v-if="printData.memberType">
                    <print-col :span="24">
                        <span class="text-bold">会员类型：</span>&nbsp;{{ printData.memberType }}
                    </print-col>
                </print-row>
                <print-row v-if="printData.memberCardMobile">
                    <print-col :span="24">
                        <span class="text-bold">会员卡号：</span>&nbsp;{{ printData.memberCardMobile | filterMobile }}
                    </print-col>
                </print-row>
                <print-row v-if="!printData.openCard && printData.principal">
                    <print-col :span="24">
                        <span class="text-bold">退款金额：</span>&nbsp;{{ printData.principal | formatMoney }}
                    </print-col>
                </print-row>
                <print-row v-if="!printData.openCard && printData.present">
                    <print-col :span="24">
                        <span class="text-bold">赠金（退款取消）：</span>&nbsp;{{ printData.present | formatMoney }}
                    </print-col>
                </print-row>

                <print-row v-if="printData.cardFee">
                    <print-col :span="24">
                        <span class="text-bold">开卡金额：</span>&nbsp;{{ printData.cardFee | formatMoney }}
                    </print-col>
                </print-row>
                <print-row>
                    <print-col :span="24">
                        <span class="text-bold">{{ printData.openCard ? '开卡地点' : "退款地点" }}：</span>&nbsp;{{ printData.organ && printData.organ.shortName || clinicName }}
                    </print-col>
                </print-row>
                <print-row v-if="printData.remark">
                    <print-col :span="24">
                        <span class="text-bold">备注：</span>&nbsp;{{ printData.remark || '暂无备注信息' }}
                    </print-col>
                </print-row>
            </div>

            <div class="print-split-line"></div>

            <div class="content-item">
                <print-row v-if="!printData.openCard && (payModeDisplayName || netIncomeFee) ">
                    <print-col :span="10">
                        实退
                    </print-col>
                    <print-col
                        :span="14"
                        class="text-right"
                    >
                        ({{ payModeDisplayName }}){{ netIncomeFee | formatMoney }}
                    </print-col>
                </print-row>
                <print-row v-if="printData.openCard && printData.paidFee">
                    <print-col :span="10">
                        实退
                    </print-col>
                    <print-col
                        :span="14"
                        class="text-right"
                    >
                        ({{ printData.payTypeName }}){{ printData.paidFee | formatMoney }}
                    </print-col>
                </print-row>
                <print-row v-if="memberCardBalance">
                    <print-col :span="10">
                        会员卡余额
                    </print-col>
                    <print-col
                        :span="14"
                        class="text-right"
                    >
                        {{ memberCardBalance }}
                    </print-col>
                </print-row>

                <print-row>
                    <print-col :span="10">
                        退款时间
                    </print-col>
                    <print-col
                        :span="14"
                        class="text-right"
                    >
                        {{ chargedTime | parseTime('y-m-d h:i:s') }}
                    </print-col>
                </print-row>
                <print-row v-if="printData.chargeComment">
                    <print-col :span="24">
                        备注：{{ printData.chargeComment }}
                    </print-col>
                </print-row>
            </div>
        </div>
    </div>
</template>

<script>
    import PrintCommonDataHandler from "./data-handler/common-handler.js";
    import PrintRow from './components/layout/print-row.vue';
    import PrintCol from './components/layout/print-col.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { formatMoney, parseTime } from "./common/utils.js";
    export default {
        DataHandler: PrintCommonDataHandler,
        name: "Refund",
        components: {
            PrintRow,
            PrintCol,
        },
        businessKey: PrintBusinessKeyEnum.REFUND,
        pages: [
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM58,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM70_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80_100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_140,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_93_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM148_118_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM200_1397,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM175_94,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_200,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM90_120_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分', // 默认选择的等分纸
            },
        ],
        filters: {
            filterMobile(val) {
                if(val){
                    return val.substr(0,3) + '****' + val.substr(7);
                }
                return "";
            },
            formatMoney,
            parseTime,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                },
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            payModeDisplayName() {
                return this.printData.payModeDisplayName || this.printData.payTypeName;
            },
            clinicName() {
                return this.printData.organ && this.printData.organ.name || '';
            },
            patient () {
                return this.printData.patient || {};
            },
            // 实退
            netIncomeFee() {
                return this.printData.netIncomeFee || this.printData.principal;
            },
            // 会员卡余额
            memberCardBalance () {
                return this.printData.memberCardBalance;
            },
            // 退款员
            chargedByName () {
                return this.printData.chargedByName;
            },
            // 时间
            chargedTime () {
                return this.printData.chargedTime;
            },
            card() {
                return this.printData.cardInfo || {};
            },
        },
        methods: {

            rechargeFormat(rechargeRights) {
                if (!rechargeRights) {
                    return [];
                }
                if (!rechargeRights.isOpen) {
                    return [];
                }

                if (Array.isArray(rechargeRights.detail.rechargeRules)) {
                    return rechargeRights.detail.rechargeRules.map((item) => `充 ${item.pay} 送 ${item.present}`);
                }

                return [];
            },
            discountFormat(discountRights) {
                if (!discountRights) {
                    return [];
                }
                if (!discountRights.isOpen) {
                    return [];
                }
                if (Array.isArray(discountRights.detail.goodsList)) {
                    const { goodsList } = discountRights.detail;
                    if (!goodsList?.length) {
                        return [];
                    }
                    return goodsList.map(({
                        name, discountType, discount, goods,
                    }) => {
                        if (!name && goods) {
                            name = goods.displayName || goods.name;
                        }
                        // 折扣
                        if (discountType === 0) {
                            return `${name}${parseFloat((discount * 10).toFixed(1))}折`;
                        }
                        // 金额
                        if (discountType === 1) {
                            return `${name}￥${discount}`;
                        }
                    });
                }

                return [];
            },
            presentFormat(patientPresents) {
                if (!Array.isArray(patientPresents)) {
                    return [];
                }

                return patientPresents.map(({
                    totalCount,isGivingLimit, usedCount, name, goods,
                }) => {
                    if (!name && goods) {
                        name = goods.displayName || goods.name;
                    }
                    if (isGivingLimit === 0) {
                        return `${name?.length > 8 ? `${name.slice(0,8)}...` : name}：无限次`;
                    }

                    if (isGivingLimit === 1) {
                        return `${name?.length > 8 ? `${name.slice(0,8)}...` : name}：剩${totalCount - usedCount}次 (共${totalCount}次)`;
                    }
                    return '';
                }).filter((str) => !!str);
            },
        },

    }
</script>
<style lang="scss">
@import "./components/layout/print-layout";

.print-recharge-content {
    font-family: "Microsoft YaHei", "微软雅黑";
    font-size: 10pt;

    .print-clinic-name {
        font-size: 12pt;
        text-align: center;
    }

    .print-recharge-type {
        margin: 2pt 0 4pt;
        font-size: 11pt;
        text-align: center;
    }

    .text-bold {
        font-weight: bold;
    }

    .text-right {
        text-align: right;
    }

    .content-item {
        padding: 0 2pt;
    }

    .print-split-line {
        height: 0;
        margin: 2pt 0;
        font-size: 0;
        border-bottom: 1px dashed #000000;
    }
}
</style>
