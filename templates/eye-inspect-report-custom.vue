<template>
    <div>
        <print-common-header
            data-type="header"
            :organ-name="organName"
            :header-config="headerConfig"
            :logo="logo"
            :barcode="printData.patientOrderNo"
            :filed-list="headerFiledList"
            show-logo
            show-barcode
        ></print-common-header>

        <template v-if="eyeExaminationItems.length">
            <abc-print-space :value="6"></abc-print-space>

            <abc-print-text weight="boldest" size="normal"> 眼部检查 </abc-print-text>

            <abc-print-space :value="6"></abc-print-space>

            <eye-inspect-table
                :list="eyeExaminationItems"
                current-page-is-a4
                :style-config="styleConfig"
            ></eye-inspect-table>
        </template>

        <template v-if="!!eyeInspectTableLength">
            <abc-print-space :value="6"></abc-print-space>

            <abc-print-text weight="boldest" size="normal"> 检查报告 </abc-print-text>

            <abc-print-space :value="6"></abc-print-space>

            <eye-inspect-merge-item-table
                :items-value="getItemsValue()"
                current-page-is-a4
                :style-config="styleConfig"
                class="eye-inspect-report-custom-table"
                @get-table-length="handleGetTableLength"
            ></eye-inspect-merge-item-table>
        </template>

        <abc-print-space :value="16"></abc-print-space>

        <div class="eye-report-diagnosis-advice">
            <div class="label" :style="`background: ${styleConfig.themeColor}`">诊断意见</div>

            <abc-html class="content"></abc-html>
        </div>

        <div data-type="last-page-footer" style="border-top: 1px solid #a6a6a6">
            <abc-print-row>
                <abc-print-col
                    v-for="(field, idx) in bottomFiledList"
                    :key="idx"
                    :style="field.style || {}"
                    :span="field.span"
                >
                    <abc-print-text size="normal" weight="normal">
                        {{ field.label }}：
                    </abc-print-text>

                    <abc-print-text size="normal" weight="normal">
                        {{ field.value }}
                    </abc-print-text>
                </abc-print-col>
            </abc-print-row>

            <div class="bottom-remark-box">
                <div class="address">
                    <div>
                        <span>电话：</span>
                        <span>
                            {{ organInfo.contactPhone }}
                        </span>
                    </div>
                    <div>
                        <span>地址：</span>
                        <span>
                            {{ address }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { EyeInspectDataHandler } from './data-handler/eye-inspect'
    import PageSizeMap, { Orientation } from '../share/page-size'
    import { PrintBusinessKeyEnum } from './constant/print-constant'
    import AbcPrintText from './components/layout/abc-print-text/index.vue'
    import EyeInspectTable from './components/medical-record/eye-inspect-table.vue'
    import EyeInspectMergeItemTable from './components/eye-inspect-report/merge-item-table.vue'
    import AbcHtml from './components/layout/abc-html.vue'
    import PrintCommonHeader from './components/layout/common-header/index.vue'
    import AbcPrintSpace from './components/layout/space.vue'
    import AbcPrintCol from './components/layout/abc-layout/abc-col.vue'
    import AbcPrintRow from './components/layout/abc-layout/abc-row.vue'
    import { convertFiledList, formatAge, updateEyeInspectItemsPrintable } from './common/utils'
    import { formatDate } from '@tool/date'

    export default {
        name: 'EyeInspectReportCustom',
        components: {
            AbcPrintRow,
            AbcPrintCol,
            AbcPrintSpace,
            PrintCommonHeader,
            AbcHtml,
            EyeInspectMergeItemTable,
            EyeInspectTable,
            AbcPrintText
        },

        DataHandler: EyeInspectDataHandler,

        props: {
            renderData: {
                type: Object,
                default: () => ({})
            }
        },

        businessKey: PrintBusinessKeyEnum.EYE_INSPECT_REPORT_CUSTOM,

        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null
            }
        ],

        data() {
            return {
                eyeInspectTableLength: 1
            }
        },

        computed: {
            printData() {
                return this.renderData.printData
            },

            printConfig() {
                return this.renderData.config.medicalDocuments.eyeInspectReport || {}
            },

            headerConfig() {
                return this.printConfig.header || {}
            },

            styleConfig() {
                return this.printConfig.style || {}
            },

            organName() {
                return this.printData.organ?.name
            },

            organInfo() {
                return this.printData?.organ || {}
            },

            address() {
                const { addressProvinceName, addressCityName, addressDistrictName, addressDetail } =
                    this.organInfo
                return addressProvinceName + addressCityName + addressDistrictName + addressDetail
            },

            logo() {
                return this.printData.organ?.logo
            },

            patient() {
                return this.printData.patient || {}
            },

            headerFiledList() {
                const style = { paddingTop: '6pt' }

                return convertFiledList([
                    {
                        label: '姓名',
                        value: `${this.patient.name} ${this.patient.sex} ${formatAge(this.patient.age)}`,
                        style
                    },
                    {
                        label: '出生日期',
                        value: `${this.patient.birthday}`,
                        style
                    },
                    {
                        label: '手机',
                        value: this.patient.mobile,
                        style,
                        valueStyle: {
                            display: 'inline-block',
                            width: '85px'
                        }
                    },
                    {
                        label: '日期',
                        value: `${formatDate(this.printData.diagnosedDate, 'YYYY-MM-DD')}`,
                        style
                    }
                ])
            },

            eyeExaminationItems() {
                return this.medicalRecord?.eyeExamination?.items || []
            },

            eyeInspectReportItemsValue() {
                const reportList = this.printData.reportDetailList || []
                return reportList.reduce((res, item) => {
                    const itemsValue = item.examinationItemsValue || []

                    return res.concat(itemsValue)
                }, [])
            },

            medicalRecord() {
                return this.printData.medicalRecord
            },

            bottomFiledList() {
                const style = { paddingTop: '6pt', fontWeight: 300 }

                return convertFiledList(
                    [
                        {
                            label: '视光师',
                            value: '',
                            style
                        },
                        {
                            label: '报告人',
                            value: '',
                            style
                        },
                        {
                            label: '打印时间',
                            value: `${formatDate(Date.now(), 'YYYY-MM-DD')}`,
                            style
                        }
                    ],
                    [8, 6, 10]
                )
            }
        },

        methods: {
            getItemsValue() {
                return updateEyeInspectItemsPrintable(this)
            },

            handleGetTableLength(v) {
                this.eyeInspectTableLength = v
            }
        }
    }
</script>

<style lang="scss">
    .eye-report-diagnosis-advice {
        display: flex;
        font-size: 10pt;
        border: 1px solid #a6a6a6;

        .label {
            border-right: 1px solid #a6a6a6;
            width: 60pt;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: #8ead24;
            color: #ffffff;
            font-family: 'Microsoft YaHei UI';
            font-size: 10pt;
            font-style: normal;
            font-weight: 600;
            line-height: 12pt; /* 120% */
        }

        .content {
            flex: 1;
            padding: 10pt 8pt;
            background: #ffffff;
            color: #2b2d32;
            font-family: 'Microsoft YaHei UI';
            font-size: 10pt;
            font-style: normal;
            font-weight: 400;
            line-height: 12pt; /* 120% */
        }
    }

    .eye-inspect-report-custom-table {
        &.page-is-a4 {
            tbody {
                .table-cell {
                    font-weight: 300;
                }
            }
        }
    }

    .bottom-remark-box {
        padding-top: 4pt;
        font-size: 8pt;
        font-weight: 300;
        display: flex;
        justify-content: space-between;

        .address {
            width: 100%;
            text-align: right;
        }
    }
</style>
