<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    socialName: '诊费',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 100.11,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    socialName: 'HPV基因全套',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 320,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    socialName: '甲胎蛋白（AFP）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 40,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '针灸理疗套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
        },

        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    socialName: '四环素软膏（三益）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '支',
                    discountedPrice: 36.0,
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    socialName: '法莫替丁片（迪诺洛克）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '片',
                    discountedPrice: 6.0,
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    socialName: '胸腺肽肠溶片（奇莫欣）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '盒',
                    discountedPrice: 20.0,
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: 'F',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            curYearBalance: 8, // 当年账户余额
            allYearBalance: 10, // 历年账户余额
            curYearAccountPaymentFee: 7, // 本年账户支付
            allYearAccountPaymentFee: 9, // 历年账户支付
            fundPayment: 0, // 规定病种
            cashPayment: 0, // 规定病种
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 1, // 自负金额
            allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 3, // 自理金额
            allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 5, // 自费金额
            allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            sbzzPaymentFee: 0, // 商保赔付
        },
    },
}
-->

<template>
    <national-medical-bill
        :detail-top="36.5"
        :detail-height="42"
        :split-count="18"
        :normal-invoice-code-top="20"
        :normal-invoice-number-top="16"
        is-need-reset-compose-social-fee
        :disable-proportion="!showOwnExpenseRatio"
        v-bind="$options.propsData"
        :font-size="fontSize"
        :offset-style="{
            'fontSize': '9pt'
        }"
    >
        <block-box
            :top="16.5"
            :left="20"
            :font="fontSize"
        >
            票据代码：
        </block-box>
        <block-box
            :top="20.5"
            :font="fontSize"
            :left="20"
        >
            电子票据代码：
        </block-box>
        <block-box
            :top="24.5"
            :font="fontSize"
            :left="20"
        >
            交款人统一社会信用代码： {{ patient.idCard | filterIdCard }}
        </block-box>

        <block-box
            :top="28.5"
            :left="20"
            :font="fontSize"
        >
            交款人： {{ patient.name }}
        </block-box>
        <block-box
            :font="fontSize"
            :top="16.5"
            :left="130"
        >
            票据号码：
        </block-box>
        <block-box
            :top="20.5"
            :left="130"
            :font="fontSize"
        >
            电子票据号码：
        </block-box>
        <block-box
            :top="24.5"
            :left="130"
            :font="fontSize"
        >
            校验码
        </block-box>

        <block-box
            :top="28.5"
            :left="130"
            :font="fontSize"
        >
            开票时间： {{ printData.chargedTime | parseTime('y-m-d h:i:s') }}
        </block-box>

        <block-box
            :top="81.5"
            :left="46"
            :font="fontSize"
        >
            金额合计（大写）{{ digitUppercase(finalFee) }}
        </block-box>

        <block-box
            :top="81.5"
            :left="136"
            :font="fontSize"
        >
            （小写）{{ finalFee | formatMoney }}
        </block-box>

        <block-box
            :top="130.5"
            :left="20"
            :font="fontSize"
        >
            收款单位（章）：{{ currentConfig.institutionName }}
        </block-box>

        <block-box
            :top="130.5"
            :left="120"
            :font="fontSize"
        >
            复核人：
        </block-box>

        <block-box
            :top="130.5"
            :left="164"
            :font="fontSize"
        >
            收款人：{{ printData.chargedByName }}
        </block-box>
        <block-box
            :top="86"
            :left="20"
            :font="fontSize"
        >
            <div class="split-style"></div>
        </block-box>

        <block-box
            :top="87"
            :left="19.5"
            :font="fontSize"
            class="write-break"
        >
            业务流水号：{{ printData.serialNo }}
        </block-box>

        <block-box
            :top="87"
            :left="62"
            :font="fontSize"
        >
            门诊号： {{ printData.patientOrderNo }}
        </block-box>

        <block-box
            :top="87"
            :left="98"
            :font="fontSize"
        >
            门诊类型：{{ shebaoPayment.medType }}
        </block-box>

        <block-box
            :top="87"
            :left="132"
            :font="fontSize"
        >
            性别：{{ patient.sex }}
        </block-box>

        <block-box
            :top="87"
            :left="167"
            :font="fontSize"
        >
            就诊日期：{{ printData.chargedTime | parseTime('y-m-d') }}
        </block-box>

        <!--第二行-->
        <block-box
            v-if="currentConfig.medicalOrganizationType"
            :top="94"
            :left="19.5"
            :font="fontSize"
            class="write-break"
        >
            医疗机构类型：{{ organ.category }}
        </block-box>

        <block-box
            :top="94"
            :left="62"
            class="write-break"
            style="width: 38mm"
            :font="fontSize"
        >
            医保类型：{{ extraInfo.insutype }}
        </block-box>

        <block-box
            :top="94"
            :left="98"
            class="write-break"
            :font="fontSize"
        >
            医保编号：{{ extraInfo.psnNo }}
        </block-box>

        <block-box
            :top="94"
            :left="167"
            :font="fontSize"
        >
            参保地：{{ extraInfo.insuplcAdmdvsName }}
        </block-box>

        <!--第三行-->
        <block-box
            :top="101"
            :left="19.5"
            :font="fontSize"
        >
            人员类别：{{ shebaoPayment.cardOwnerType }}
        </block-box>

        <block-box
            :top="101"
            :left="62"
            :font="fontSize"
        >
            个人自费：{{ extraInfo.shebaoFulamtOwnpayAmt | formatMoney }}
        </block-box>
        <block-box
            :top="101"
            :left="98"
            style="width: 150mm"
            :font="fontSize"
        >
            个人自付：{{ shebaoPayment.selfPaymentFee | formatMoney }}
        </block-box>
        <block-box
            :top="101"
            :left="132"
            style="width: 150mm"
            :font="fontSize"
        >
            乙类先行自付：{{ extraInfo.preselfpayAmt | formatMoney }}
        </block-box>
        <block-box
            :top="101"
            :left="167"
            style="width: 150mm"
            :font="fontSize"
        >
            超限先行自付：{{ extraInfo.overlmtSelfpay | formatMoney }}
        </block-box>

        <!--第四行-->
        <block-box
            :top="106"
            :left="19.5"
            :font="fontSize"
        >
            医保统筹基金支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}
        </block-box>

        <block-box
            :top="106"
            :left="132"
            :font="fontSize"
        >
            大病基金支付：{{ extraInfo.seriInsFund | formatMoney }}
        </block-box>

        <!--第五行-->
        <block-box
            :top="111"
            :left="19.5"
            :font="fontSize"
        >
            个人现金支付：{{ printData.personalPaymentFee | formatMoney }}
        </block-box>

        <block-box
            :top="111"
            :left="62"
            style="width: 150mm"
            :font="fontSize"
        >
            大病费用：{{ extraInfo.seriIllFee | formatMoney }}
        </block-box>
        <block-box
            :top="111"
            :left="98"
            style="width: 150mm"
            :font="fontSize"
        >
            罕见病用药：
        </block-box>
        <block-box
            :top="111"
            :left="132"
            style="width: 150mm"
            :font="fontSize"
        >
            此前起付线累计：{{ extraInfo.beforeDedcCumulative | formatMoney }}
        </block-box>
        <block-box
            :top="111"
            :left="167"
            style="width: 150mm"
            :font="fontSize"
        >
            本次起付线：{{ extraInfo.actPayDedc | formatMoney }}
        </block-box>

        <!--第六行-->
        <block-box
            :top="116"
            :left="19.5"
            :font="fontSize"
        >
            个人账户支付：{{ shebaoPayment.accountPaymentFee | formatMoney }}
            ( 本年支付：{{ extraInfo.curYearAccountPaymentFee | formatMoney }}
            历年支付：{{ extraInfo.allYearAccountPaymentFee | formatMoney }}
            亲属历账：{{ extraInfo.acctMulaidPay | formatMoney }}
            本年账户余额：{{ extraInfo.curYearBalance | formatMoney }}
            历年账户余额：{{ extraInfo.allYearBalance | formatMoney }})
        </block-box>

        <!--第七行-->
        <block-box
            :top="120"
            :left="19.5"
            :font="fontSize"
        >
            其他支付：{{ extraInfo.otherSubsidy | formatMoney }}
            (救助支付：{{ extraInfo.mafPay | formatMoney }}
            优抚支付：{{ extraInfo.hifdmPay | formatMoney }}
            商保支付：{{ extraInfo.comPay | formatMoney }}
            公补：{{ extraInfo.cvlservPay | formatMoney }}
            罕见病基金：{{ extraInfo.rareIllFund | formatMoney }})
        </block-box>

        <block-box
            v-if="currentConfig.isShowOwnPaySign"
            :top="124"
            :left="19.5"
        >
            自费金额已知晓，签名：
        </block-box>
    </national-medical-bill>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {MM210_139_ningbo, Orientation} from "../share/page-size.js";
    import NationalMedicalBill from './components/medical-bill/national-medical-bill/index.vue';
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import BillDataMixins from './mixins/bill-data';
    import NationalBillData from "./mixins/national-bill-data.js";
    export default {
        name: "MedicalBillNingxiaNational",
        components: {
            NationalMedicalBill,
            BlockBox
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_NINGBO,
        pages: [

            {
                paper: PageSizeMap.MM210_139_ningbo,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            //是否医保支付
            isShebaoPayment() {
                return Object.keys(this.shebaoPayment).length
            },
            fontSize() {
                return 8;
            }
        }
    }
</script>

<style lang="scss">
* {
  padding: 0;
  margin: 0;
}
.abc-page_preview {
    background: url("/static/assets/print/ningbo.jpg") no-repeat;
    background-size: 100%;
    color: #2a82e4;
}
.abc-page_preview[data-pagesize="浙江医疗票据(21×13.9cm)"] {
  background: #fff;
}
.split-style {
    border-bottom: #2a82e4 solid 1pt;
    width: 485pt;
}

.write-break {
    word-break: break-all;
}
.national-medical-bill-content {
    width: 210mm;
    height: 197mm;
}
</style>
