<template>
    <div
        class="inspect-label-wrapper"
    >
        <abc-print-space :value="8"></abc-print-space>

        <div style="padding: 0 8pt">
            <abc-print-text-wrapper
                style="display: flex;justify-content: space-between"
                overflow-hidden
            >
                <abc-print-text
                    size="small"
                    overflow
                >
                    {{ printData.organName }}
                </abc-print-text>


                <abc-print-text
                    size="small"
                    :custom-style="{ whiteSpace: 'nowrap' }"
                >
                    {{ printData.departmentName }}
                </abc-print-text>
            </abc-print-text-wrapper>

            <abc-print-space :value="3"></abc-print-space>

            <abc-print-barcode
                :value="printData.inspectSheetNo"
                :height="30"
            ></abc-print-barcode>

            <abc-print-space :value="2"></abc-print-space>
            <!--患者信息-->
            <abc-print-text-wrapper overflow-hidden>
                <abc-print-text
                    size="large"
                    weight="bold"
                    :value="printData.patientName"
                    :max-length="4"
                >
                </abc-print-text>

                <abc-print-text>
                    {{ printData.patientSex }}
                </abc-print-text>

                <abc-print-text>
                    {{ printData.patientAge }}
                </abc-print-text>

                <abc-print-space
                    :value="4"
                    direction="h"
                ></abc-print-space>

                <abc-print-text>
                    {{ filterMobileV2(printData.mobile, 1) }}
                </abc-print-text>
            </abc-print-text-wrapper>
        </div>

        <abc-print-split></abc-print-split>

        <div style="padding: 0 8pt">
            <!--检查科室-->
            <abc-print-text-wrapper
                style="display: flex"
                overflow-hidden
            >
                <abc-print-text size="small">
                    申请科室：
                </abc-print-text>

                <abc-print-text
                    size="small"
                    weight="boldest"
                >
                    {{ printData.doctorDepartmentName }}
                </abc-print-text>

                <abc-print-space
                    :value="2"
                    direction="h"
                ></abc-print-space>

                <abc-print-text
                    size="small"
                    weight="boldest"
                >
                    {{ printData.doctorName }}
                </abc-print-text>
            </abc-print-text-wrapper>

            <abc-print-space :value="2"></abc-print-space>

            <!--检查部位-->
            <abc-print-text-wrapper
                style="display: flex"
                overflow-hidden
            >
                <abc-print-text
                    size="small"
                    :custom-style="{ whiteSpace: 'nowrap' }"
                >
                    检查部位：
                </abc-print-text>

                <abc-print-text
                    size="small"
                    weight="boldest"
                    :custom-style="{ whiteSpace: 'nowrap' }"
                >
                    {{ printData.deviceTypeName }}
                </abc-print-text>

                <abc-print-space
                    :value="2"
                    direction="h"
                ></abc-print-space>

                <abc-print-text
                    size="small"
                    weight="boldest"
                    overflow
                >
                    {{ printData.inspectProjectName }}
                </abc-print-text>
            </abc-print-text-wrapper>
        </div>

        <abc-print-split></abc-print-split>

        <abc-print-text
            tag="div"
            size="small"
            align="right"
            :custom-style="{ padding: '0 8pt' }"
        >
            {{ printData.printDate }}
        </abc-print-text>
    </div>
</template>

<script>
    import {INSPECT_DEVICE_TYPE_TEXT, PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import AbcPrintBarcode from "./components/layout/abc-print-barcode.vue";
    import AbcPrintText from "./components/layout/abc-print-text/index.vue";
    import AbcPrintSpace from "./components/layout/space.vue";
    import AbcPrintSplit from "./components/layout/abc-print-split/index.vue";
    import AbcPrintTextWrapper from "./components/layout/abc-print-text/abc-print-text-wrapper.vue";
    import {formatAge} from "./common/utils";
    import {formatDate} from "@tool/date";
    import {filterMobileV2} from "./common/medical-transformat";

    export default {
        name: 'InspectLabel',
        components: {
            AbcPrintTextWrapper,
            AbcPrintSplit,
            AbcPrintSpace,
            AbcPrintText,
            AbcPrintBarcode
        },

        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            printData() {
                return {
                    organName: this.renderData.organPrintView?.name,
                    departmentName: this.renderData?.registrationFormItem?.departmentName,
                    inspectSheetNo: this.renderData.no,
                    patientName: this.renderData.patient.name,
                    patientSex: this.renderData.patient.sex,
                    patientAge: formatAge(this.renderData.patient.age),
                    mobile: this.renderData.patient.mobile,
                    doctorName: this.renderData.doctorName,
                    doctorDepartmentName: this.renderData.doctorDepartmentName,
                    deviceTypeName: INSPECT_DEVICE_TYPE_TEXT[this.renderData.deviceType],
                    inspectProjectName: (this.renderData.examSheetStatusItems || []).map(item => item.examinationName).join('、'),
                    printDate: formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss'),
                }
            }
        },
        methods: {filterMobileV2},

        businessKey: PrintBusinessKeyEnum.INSPECT_LABEL,

        pages: [
            {
                paper: PageSizeMap.TAG60_40,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ]
    }
</script>