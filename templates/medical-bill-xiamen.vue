<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    socialName: '诊费',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 100.11,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    socialName: 'HPV基因全套',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 320,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    socialName: '甲胎蛋白（AFP）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 40,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '针灸理疗套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
        },

        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    socialName: '四环素软膏（三益）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '支',
                    discountedPrice: 36.0,
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    socialName: '法莫替丁片（迪诺洛克）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '片',
                    discountedPrice: 6.0,
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    socialName: '胸腺肽肠溶片（奇莫欣）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '盒',
                    discountedPrice: 20.0,
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            curYearBalance: 8, // 当年账户余额
            allYearBalance: 10, // 历年账户余额
            curYearAccountPaymentFee: 7, // 本年账户支付
            allYearAccountPaymentFee: 9, // 历年账户支付
            fundPayment: 0, // 规定病种
            cashPayment: 0, // 规定病种
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 1, // 自负金额
            allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 3, // 自理金额
            allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 5, // 自费金额
            allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            sbzzPaymentFee: 0, // 商保赔付
        },
    },
}
-->

<template>
    <div>
        <template v-for="(page, pageIndex) in currentRenderPage">
            <div
                :key="pageIndex"
                class="xiamen-medical-bill-content"
            >
                <div
                    class="xiamen-medical-bill-page"
                >
                    <div
                        v-if="blueInvoiceData"
                        style="position: absolute; top: 0.5cm; left: 1.8cm;"
                    >
                        销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                    </div>
                    <refund-icon
                        v-if="isRefundBill"
                        top="1cm"
                        left="1.8cm"
                    ></refund-icon>

                    <block-box
                        top="36.5"
                        left="18"
                    >
                        医保编号：{{ printData.healthCardNo }}
                    </block-box>
                    <block-box
                        top="42"
                        left="18"
                    >
                        医保结算号：{{ extraInfo.setlId }}
                    </block-box>
                    <block-box
                        top="42"
                        left="114"
                    >
                        医保类型：{{ extraInfo.insutype }}
                    </block-box>
                    <div class="trans-number">
                        {{ printData.patientOrderNo }}
                    </div>
                    <div
                        class="trans-number"
                        style="left: 80mm"
                    >
                        {{ printData.patientOrderNo }}
                    </div>
                    <div
                        class="trans-number"
                        style="left: 127mm"
                    >
                        {{ shebaoPayment.cardId }}
                    </div>

                    <div
                        class="patient"
                        style="left: 30mm;"
                    >
                        {{ patient.name }}
                    </div>
                    <div
                        class="patient"
                        style="left: 61.5mm;"
                    >
                        科室： {{ printData.departmentName }}
                    </div>
                    <div
                        class="shebao-wrapper"
                    >
                        <div
                            class="shebao-info"
                            style="font-size: 7pt"
                        >
                            <div>
                                账户支付： {{ shebaoPayment.accountPaymentFee | formatMoney }}
                            </div>
                            <div style="padding-left: 6pt">
                                共济账户：{{ extraInfo.acctMulaidPay | formatMoney }}
                            </div>
                            <div style="padding-left: 6pt">
                                医疗帐户：{{ extraInfo.purAcctPay | formatMoney }}
                            </div>
                            <div style="padding-left: 6pt">
                                健康账户：{{ extraInfo.hlAcctPay | formatMoney }}
                            </div>

                            <div>
                                统筹支付： {{ shebaoPayment.fundPaymentFee | formatMoney }} <span style="display: inline-block; width: 15mm"></span>
                            </div>
                            <div>
                                医疗救助： {{ extraInfo.mafPay | formatMoney }} <span style="display: inline-block; width: 15mm"></span>
                            </div>

                            <div>
                                大病保险： {{ extraInfo.hiinsPay | formatMoney }}<span style="display: inline-block; width: 15mm"></span>
                            </div>

                            <div>
                                公务员补助： {{ extraInfo.cvlservPay | formatMoney }} <span style="display: inline-block; width: 15mm"></span>
                            </div>
                        </div>
                        <div style="position: absolute;top: 0mm;width: 180mm;left: 33mm">
                            <div>
                                其他支付： {{ shebaoPayment.otherPaymentFee | formatMoney }}
                            </div>
                            <div>个人支付：{{ extraInfo.acctPay | formatMoney }}</div>
                            <div>个人自费：{{ extraInfo.ownpayAmt | formatMoney }}</div>
                            <div style="padding-left: 6pt">
                                个人自付：{{ extraInfo.ownselfPay | formatMoney }}
                            </div>
                            <div style="padding-left: 6pt">
                                先行自付：{{ extraInfo.preselfpayAmt | formatMoney }}
                            </div>

                            <div>
                                共济账户余额： {{ extraInfo.fmAcctBalc | formatMoney }}<span style="display: inline-block; width: 15mm"></span>
                            </div>
                            <div>
                                医疗账户余额： {{ extraInfo.purAcctBalc | formatMoney }}
                            </div>
                            <div>
                                健康账户余额： {{ extraInfo.hlAcctBalc | formatMoney }}
                            </div>

                            <div>
                                保健基金： {{ extraInfo.hlCareFund | formatMoney }}
                            </div>
                        </div>
                    </div>
                    <div>
                        <div
                            class="form-items-wrapper"
                            :style="{'left': `${8}mm`}"
                        >
                            <div style="position:absolute;left: 77mm; top: -6mm ;font-size: 9pt;width: 40pt">
                                医保比例
                            </div>
                            <div style="position:absolute;left: 167mm; top: -6mm ;font-size: 9pt;width: 40pt">
                                医保比例
                            </div>
                            <div
                                v-for="(item, index) in page.formItems"
                                :key="index + pageIndex"
                                class="form-item-tr"
                            >
                                <span class="item-name">
                                    <template v-if="isShowSocialCode(item)">
                                        [{{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}]
                                    </template>
                                    {{ item.name }}
                                </span>
                                <span class="item-count">{{ item.discountedUnitPrice | formatMoney }} * {{ item.count }}{{ item.unit }}</span>
                                <span class="total-price">{{ item.discountedPrice | formatMoney }}</span>
                                <div class="item-proportion">
                                    {{ item.ownExpenseRatio | filterOwnExpenseRatio }}
                                </div>
                            </div>

                            <div
                                v-if="hasOverPageTip"
                                class="only-one-page"
                            >
                                *** 因纸张限制，部分项目未打印 ***
                            </div>
                            <template v-else>
                                <div
                                    v-if="pageIndex !== renderPage.length - 1"
                                    class="next-page"
                                >
                                    *** 接下页 ***
                                </div>
                            </template>
                        </div>
                    </div>
                    <block-box
                        top="66.5"
                        left="34"
                    >
                        {{ westernMedicineFee|formatMoney }}
                    </block-box>
                    <block-box
                        top="73"
                        left="34"
                    >
                        {{ chineseComposeMedicineFee|formatMoney }}
                    </block-box>
                    <block-box
                        top="79"
                        left="34"
                    >
                        {{ chineseMedicineDrinksPieceFee|formatMoney }}
                    </block-box>
                    <block-box
                        top="86"
                        left="34"
                    >
                        {{ examinationExaminationFee|formatMoney }}
                    </block-box>
                    <template v-if="isFeeCompose">
                        <block-box
                            top="68"
                            left="64"
                        >
                            {{ treatmentFee|formatMoney }}
                        </block-box>
                        <block-box
                            top="80"
                            left="64"
                        >
                            {{ operationFee|formatMoney }}
                        </block-box>
                    </template>
                    <block-box
                        top="86"
                        left="64"
                    >
                        {{ examinationInspectionFee|formatMoney }}
                    </block-box>
                    <block-box
                        top="73"
                        left="94"
                    >
                        <template v-if="isFeeCompose">
                            {{ examinationFee|formatMoney }}
                        </template>
                        <template v-else>
                            {{ registrationFee|formatMoney }}
                        </template>
                    </block-box>
                    <block-box
                        top="86"
                        left="94"
                    >
                        {{ otherFeeTotal|formatMoney }}
                    </block-box>

                    <div class="upper-money">
                        {{ digitUppercase(finalFee) }}
                    </div>
                    <div
                        class="upper-money"
                        style="left: 153mm"
                    >
                        {{ finalFee | formatMoney }}
                    </div>
                    <div class="organ">
                        {{ xiamen.institutionName }}
                    </div>
                    <div class="charger">
                        {{ printData.chargedByName }}
                    </div>
                    <div class="year">
                        {{ printData.chargedTime | parseTime('y') }}
                    </div>
                    <div class="month">
                        {{ printData.chargedTime | parseTime('m') }}
                    </div>
                    <div class="day">
                        {{ printData.chargedTime | parseTime('d') }}
                    </div>
                </div>
            </div>
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import NationalBillData from "./mixins/national-bill-data";

    export default {
        name: "MedicalBillXiamen",
        components: {
            RefundIcon,
            BlockBox
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_XIAMEN,
        pages: [
            {
                paper: PageSizeMap.MM191_151,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                return this.isOnlyOnePage ? this.renderPage.slice(0, 1) : this.renderPage
            },
            splitType() {
                return this.xiamen.splitType;
            },
            isOnlyOnePage() {
                return this.splitType === 1 && (this.renderPage.length > 1 || this.extra.isPreview);
            },
            xiamen() {
                return this.config.xiamen || {}
            },
            medicalBills() {
                return this.printData.medicalBills &&this.printData.medicalBills.filter(billItem => {
                    if (billItem.name === this.$t('registrationFeeName')) {
                        return !!billItem.totalFee
                    }
                    return true
                }) || [];
            },

            renderPage() {
                return this.spliceFormItems(this.chargeFormItems, 8);
            },
            otherFeeTotal() {
                if (this.isFeeCompose) {
                    return this.registrationFee + this.materialFee + this.nursingFee + this.physiotherapyFee + this.bedFee + this.generalDiagnosisAndTreatmentFee + this.pharmacyServiceFee + this.outerFlagFee;
                }
                return this.materialFee + this.otherFee
            },
            isNeedResetComposeSocialFee() {
                return true;
            }
        },
        created() {
            this.initFee();
        },
    }
</script>
<style lang="scss">
* {
  padding: 0;
  margin: 0;
}

.xiamen-medical-bill-content {
  @import "./components/refund-icon/refund-icon.scss";

  font-size: 9pt;

  .xiamen-medical-bill-page {
    position: absolute;
    width: 211mm;
    height: 127.5mm;

    &.is-electron {
      height: 127mm;
    }
    //border: 1pt solid #00ace9;
  }

  .trans-number,
  .patient,
  .form-items-wrapper,
  .medical-bill-wrapper {
    position: absolute;
    line-height: 11pt;
  }

  .trans-number {
    top: 47.5mm;
    left: 30mm;
  }

  .patient {
    top: 53mm;
  }

  .form-items-wrapper {
    top: 115mm;
    left: 16mm;
    width: 208mm;
    height: 75mm;
  }

  .next-page {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    text-align: center;
  }

  .form-item-tr {
      display: inline-block;
    width: 45.9%;
    height: 4mm;
    font-size: 0;
    line-height: 11pt;
  }

  .item-name,
  .item-count,
  .item-price,
  .item-spec,
  .item-proportion,
  .total-price {
    display: inline-block;
    *display: inline;
    overflow: hidden;
    font-size: 9pt;
    word-break: keep-all;
    white-space: nowrap;
    *zoom: 1;
  }
    .shebao-wrapper {
        position: absolute;
        left: 112mm;
        top: 58mm;
    }

  .item-name {
    width: 34mm;
  }

  .item-count {
    width: 26mm;
    text-align: right;
  }
    .item-proportion {
        width: 12mm;
        text-align: right;
    }

  .item-price,
  .item-spec {
    width: 13mm;
    *width: 12.8mm;
    text-align: right;
  }

  .total-price {
    width: 16mm;
    *width: 15.8mm;
    text-align: right;
  }

  .medical-bill-wrapper {
    top: 37mm;
    left: 23mm;
    width: 62mm;
    height: 31mm;
  }

  .medical-bill-item {
    position: absolute;
    width: 31mm;
  }

  .upper-money {
    position: absolute;
    top: 94mm;
  }

  .upper-money {
    left: 63mm;
  }

  .organ,
  .charger,
   {
    position: absolute;
    top: 101.5mm;
    line-height: 12pt;
  }

    .year {
        position: absolute;
        top: 52.5mm;
        left: 127.5mm;
        line-height: 12pt;

    }
    .day {
        top: 52.5mm;
        position: absolute;
        left: 162.5mm;
    }
    .month {
        position: absolute;
        left: 145.5mm;
        top: 52.5mm;

    }
  .organ {
    left: 43mm;
  }

  .charger {
      left: 148.5mm;
  }


  .only-one-page {
    text-align: center;
  }
}
.abc-page_preview {
  background: url("/static/assets/print/xiamen.jpg");
  background-size: 191mm 151mm;
  color: #2a82e4;
  .xiamen-medical-bill-page {
    top: -1mm;
  }
}
</style>
