<!--exampleData
{
	"id": *********,
	"orderNo": "RK2021020200001",
	"outOrderNo": null,
	"settlementOrderId": null,
	"supplierId": "ffffffff000000000cf445f00769a000",
	"createdDate": "2021-02-02T03:15:55.000Z",
	"isConfirm": 1,
	"isRecon": 0,
	"isScan": null,
	"kindCount": 3,
	"isReview": 1,
	"confirmDate": "2021-02-02T03:15:55.000Z",
	"reviewDate": "2021-02-02T03:15:55.000Z",
	"inDate": "2021-02-02T03:15:55.000Z",
	"status": 2,
	"sum": 300,
	"count": 300,
	"amount": 500,
	"amountExcludingTax": 500,
	"toOrganId": "ffffffff000000000d4ab0e8079a4000",
	"comment": [{
		"time": "2021-02-02T03:15:55.000Z",
		"content": "",
		"employeeId": "71dbfd8322d0426ca4c428c1da07e9bf"
	}],
	"createdUserId": "71dbfd8322d0426ca4c428c1da07e9bf",
	"lastModifiedDate": "2021-02-02T03:15:55.000Z",
	"mallOrderId": null,
	"pharmacyNo": 0,
	"supplierObj": {
		"id": "ffffffff000000000cf445f00769a000",
		"name": "我的ABC",
		"status": 1
	},
	"supplier": "我的ABC",
	"list": [{
		"id": *********,
		"orderId": *********,
		"goodsId": "ffffffff000000000cf438c80769a000",
		"goods": {
			"id": "ffffffff000000000cf438c80769a000",
			"py": "FMSBSLEP|fumasuanbisuoluoerpian",
			"atc": null,
			"name": "苏莱乐",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "6950425900079",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110008",
			"subType": 1,
			"domainId": 206665,
			"inTaxRat": 0,
			"pieceNum": 18,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "片",
			"extendSpec": null,
			"piecePrice": 0.28,
			"combineType": 0,
			"dismounting": 1,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "成都苑东生物",
			"materialSpec": "",
			"medicineCadn": "富马酸比索洛尔片富马酸比索洛尔片富马酸比索洛尔片富马酸比索洛尔片",
			"medicineNmpn": "H20083008",
			"packagePrice": 5,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0.28,
			"manufacturerFull": "成都苑东生物制药股份有限公司",
			"packageCostPrice": null,
			"chainPackagePrice": 5,
			"medicineDosageNum": 5,
			"medicineDosageForm": "片剂",
			"medicineDosageUnit": "mg",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 18,
		"batchNo": "1",
		"expiryDate": "2023-04-30",
		"productionDate": "2021-01-31",
		"packageCostPrice": 2,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 2,
		"useTotalCostPrice": 200,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003913,
		"stock": {
			"id": 100003913,
			"batchId": 100003913
		},
		"useTotalCostPriceE": 200,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}, {
		"id": 100002769,
		"orderId": *********,
		"goodsId": "ffffffff000000000e69b46008fb2000",
		"goods": {
			"id": "ffffffff000000000e69b46008fb2000",
			"py": "TBABP|toubaoanbianpian",
			"atc": null,
			"name": "丹",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110033",
			"subType": 1,
			"domainId": 257334,
			"inTaxRat": 0,
			"pieceNum": 30,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "片",
			"extendSpec": null,
			"piecePrice": 0,
			"combineType": 0,
			"dismounting": 0,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "丹东医创",
			"materialSpec": "",
			"medicineCadn": "头孢氨苄片",
			"medicineNmpn": "H21020353",
			"packagePrice": 2,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0,
			"manufacturerFull": "丹东医创药业有限责任公司",
			"packageCostPrice": null,
			"chainPackagePrice": 2,
			"medicineDosageNum": 0.25,
			"medicineDosageForm": "片剂",
			"medicineDosageUnit": "g",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 30,
		"batchNo": "2",
		"expiryDate": "2023-05-31",
		"productionDate": "2021-01-31",
		"packageCostPrice": 1,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 1,
		"useTotalCostPrice": 100,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003914,
		"stock": {
			"id": 100003914,
			"batchId": 100003914
		},
		"useTotalCostPriceE": 100,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}, {
		"id": 100002769,
		"orderId": *********,
		"goodsId": "ffffffff000000000e69b46008fb2000",
		"goods": {
			"id": "ffffffff000000000e69b46008fb2000",
			"py": "TBABP|toubaoanbianpian",
			"atc": null,
			"name": "丹",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110033",
			"subType": 1,
			"domainId": 257334,
			"inTaxRat": 0,
			"pieceNum": 30,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "片",
			"extendSpec": null,
			"piecePrice": 0,
			"combineType": 0,
			"dismounting": 0,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "丹东医创",
			"materialSpec": "",
			"medicineCadn": "头孢氨苄片",
			"medicineNmpn": "H21020353",
			"packagePrice": 2,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0,
			"manufacturerFull": "丹东医创药业有限责任公司",
			"packageCostPrice": null,
			"chainPackagePrice": 2,
			"medicineDosageNum": 0.25,
			"medicineDosageForm": "片剂",
			"medicineDosageUnit": "g",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 30,
		"batchNo": "2",
		"expiryDate": "2023-05-31",
		"productionDate": "2021-01-31",
		"packageCostPrice": 1,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 1,
		"useTotalCostPrice": 100,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003914,
		"stock": {
			"id": 100003914,
			"batchId": 100003914
		},
		"useTotalCostPriceE": 100,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}, {
		"id": 100002769,
		"orderId": *********,
		"goodsId": "ffffffff000000000e69b46008fb2000",
		"goods": {
			"id": "ffffffff000000000e69b46008fb2000",
			"py": "TBABP|toubaoanbianpian",
			"atc": null,
			"name": "丹",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110033",
			"subType": 1,
			"domainId": 257334,
			"inTaxRat": 0,
			"pieceNum": 30,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "片",
			"extendSpec": null,
			"piecePrice": 0,
			"combineType": 0,
			"dismounting": 0,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "丹东医创",
			"materialSpec": "",
			"medicineCadn": "头孢氨苄片",
			"medicineNmpn": "H21020353",
			"packagePrice": 2,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0,
			"manufacturerFull": "丹东医创药业有限责任公司",
			"packageCostPrice": null,
			"chainPackagePrice": 2,
			"medicineDosageNum": 0.25,
			"medicineDosageForm": "片剂",
			"medicineDosageUnit": "g",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 30,
		"batchNo": "2",
		"expiryDate": "2023-05-31",
		"productionDate": "2021-01-31",
		"packageCostPrice": 1,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 1,
		"useTotalCostPrice": 100,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003914,
		"stock": {
			"id": 100003914,
			"batchId": 100003914
		},
		"useTotalCostPriceE": 100,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}, {
		"id": 100002769,
		"orderId": *********,
		"goodsId": "ffffffff000000000e69b46008fb2000",
		"goods": {
			"id": "ffffffff000000000e69b46008fb2000",
			"py": "TBABP|toubaoanbianpian",
			"atc": null,
			"name": "丹",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110033",
			"subType": 1,
			"domainId": 257334,
			"inTaxRat": 0,
			"pieceNum": 30,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "片",
			"extendSpec": null,
			"piecePrice": 0,
			"combineType": 0,
			"dismounting": 0,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "丹东医创",
			"materialSpec": "",
			"medicineCadn": "头孢氨苄片",
			"medicineNmpn": "H21020353",
			"packagePrice": 2,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0,
			"manufacturerFull": "丹东医创药业有限责任公司",
			"packageCostPrice": null,
			"chainPackagePrice": 2,
			"medicineDosageNum": 0.25,
			"medicineDosageForm": "片剂",
			"medicineDosageUnit": "g",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 30,
		"batchNo": "2",
		"expiryDate": "2023-05-31",
		"productionDate": "2021-01-31",
		"packageCostPrice": 1,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 1,
		"useTotalCostPrice": 100,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003914,
		"stock": {
			"id": 100003914,
			"batchId": 100003914
		},
		"useTotalCostPriceE": 100,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}, {
		"id": 100002769,
		"orderId": *********,
		"goodsId": "ffffffff000000000e69b46008fb2000",
		"goods": {
			"id": "ffffffff000000000e69b46008fb2000",
			"py": "TBABP|toubaoanbianpian",
			"atc": null,
			"name": "丹",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110033",
			"subType": 1,
			"domainId": 257334,
			"inTaxRat": 0,
			"pieceNum": 30,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "片",
			"extendSpec": null,
			"piecePrice": 0,
			"combineType": 0,
			"dismounting": 0,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "丹东医创",
			"materialSpec": "",
			"medicineCadn": "头孢氨苄片",
			"medicineNmpn": "H21020353",
			"packagePrice": 2,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0,
			"manufacturerFull": "丹东医创药业有限责任公司",
			"packageCostPrice": null,
			"chainPackagePrice": 2,
			"medicineDosageNum": 0.25,
			"medicineDosageForm": "片剂",
			"medicineDosageUnit": "g",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 30,
		"batchNo": "2",
		"expiryDate": "2023-05-31",
		"productionDate": "2021-01-31",
		"packageCostPrice": 1,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 1,
		"useTotalCostPrice": 100,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003914,
		"stock": {
			"id": 100003914,
			"batchId": 100003914
		},
		"useTotalCostPriceE": 100,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}, {
		"id": 100002769,
		"orderId": *********,
		"goodsId": "ffffffff000000000e69b46008fb2000",
		"goods": {
			"id": "ffffffff000000000e69b46008fb2000",
			"py": "TBABP|toubaoanbianpian",
			"atc": null,
			"name": "丹",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110033",
			"subType": 1,
			"domainId": 257334,
			"inTaxRat": 0,
			"pieceNum": 30,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "片",
			"extendSpec": null,
			"piecePrice": 0,
			"combineType": 0,
			"dismounting": 0,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "丹东医创",
			"materialSpec": "",
			"medicineCadn": "头孢氨苄片",
			"medicineNmpn": "H21020353",
			"packagePrice": 2,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0,
			"manufacturerFull": "丹东医创药业有限责任公司",
			"packageCostPrice": null,
			"chainPackagePrice": 2,
			"medicineDosageNum": 0.25,
			"medicineDosageForm": "片剂",
			"medicineDosageUnit": "g",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 30,
		"batchNo": "2",
		"expiryDate": "2023-05-31",
		"productionDate": "2021-01-31",
		"packageCostPrice": 1,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 1,
		"useTotalCostPrice": 100,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003914,
		"stock": {
			"id": 100003914,
			"batchId": 100003914
		},
		"useTotalCostPriceE": 100,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}, {
		"id": 100002769,
		"orderId": *********,
		"goodsId": "ffffffff000000000e69b46008fb2000",
		"goods": {
			"id": "ffffffff000000000e69b46008fb2000",
			"py": "TBABP|toubaoanbianpian",
			"atc": null,
			"name": "丹",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110033",
			"subType": 1,
			"domainId": 257334,
			"inTaxRat": 0,
			"pieceNum": 30,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "片",
			"extendSpec": null,
			"piecePrice": 0,
			"combineType": 0,
			"dismounting": 0,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "丹东医创",
			"materialSpec": "",
			"medicineCadn": "头孢氨苄片",
			"medicineNmpn": "H21020353",
			"packagePrice": 2,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0,
			"manufacturerFull": "丹东医创药业有限责任公司",
			"packageCostPrice": null,
			"chainPackagePrice": 2,
			"medicineDosageNum": 0.25,
			"medicineDosageForm": "片剂",
			"medicineDosageUnit": "g",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 30,
		"batchNo": "2",
		"expiryDate": "2023-05-31",
		"productionDate": "2021-01-31",
		"packageCostPrice": 1,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 1,
		"useTotalCostPrice": 100,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003914,
		"stock": {
			"id": 100003914,
			"batchId": 100003914
		},
		"useTotalCostPriceE": 100,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}, {
		"id": 100002769,
		"orderId": *********,
		"goodsId": "ffffffff000000000e69b46008fb2000",
		"goods": {
			"id": "ffffffff000000000e69b46008fb2000",
			"py": "TBABP|toubaoanbianpian",
			"atc": null,
			"name": "丹",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110033",
			"subType": 1,
			"domainId": 257334,
			"inTaxRat": 0,
			"pieceNum": 30,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "片",
			"extendSpec": null,
			"piecePrice": 0,
			"combineType": 0,
			"dismounting": 0,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "丹东医创",
			"materialSpec": "",
			"medicineCadn": "头孢氨苄片",
			"medicineNmpn": "H21020353",
			"packagePrice": 2,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0,
			"manufacturerFull": "丹东医创药业有限责任公司",
			"packageCostPrice": null,
			"chainPackagePrice": 2,
			"medicineDosageNum": 0.25,
			"medicineDosageForm": "片剂",
			"medicineDosageUnit": "g",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 30,
		"batchNo": "2",
		"expiryDate": "2023-05-31",
		"productionDate": "2021-01-31",
		"packageCostPrice": 1,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 1,
		"useTotalCostPrice": 100,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003914,
		"stock": {
			"id": 100003914,
			"batchId": 100003914
		},
		"useTotalCostPriceE": 100,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}, {
		"id": 100002770,
		"orderId": *********,
		"goodsId": "ffffffff000000000e69c02808fb2000",
		"goods": {
			"id": "ffffffff000000000e69c02808fb2000",
			"py": "YFSXDEY|yangfushaxingdierye",
			"atc": null,
			"name": "绿霏",
			"type": 1,
			"cMSpec": "",
			"enName": null,
			"isSell": 1,
			"origin": "",
			"remark": "",
			"status": 1,
			"typeId": 12,
			"barCode": "6943118000088",
			"disable": 0,
			"gradeId": null,
			"organId": "ffffffff000000000c5a1308069aa000",
			"shortId": "110035",
			"subType": 1,
			"domainId": 216379,
			"inTaxRat": 0,
			"pieceNum": 1,
			"position": null,
			"outTaxRat": 0,
			"pieceUnit": "盒",
			"extendSpec": null,
			"piecePrice": 0,
			"combineType": 0,
			"dismounting": 0,
			"packageUnit": "盒",
			"customTypeId": null,
			"manufacturer": "南京天朗",
			"materialSpec": "",
			"medicineCadn": "氧氟沙星滴耳液",
			"medicineNmpn": "H20094236",
			"packagePrice": 2,
			"standardName": null,
			"standardUnit": null,
			"bizExtensions": null,
			"certificateNo": "",
			"needExecutive": 0,
			"smartDispense": null,
			"customTypeName": null,
			"certificateName": "",
			"chainPiecePrice": 0,
			"manufacturerFull": "南京天朗制药有限公司",
			"packageCostPrice": null,
			"chainPackagePrice": 2,
			"medicineDosageNum": 5,
			"medicineDosageForm": "耳用制剂",
			"medicineDosageUnit": "ml",
			"chainPackageCostPrice": null,
			"allowSubClinicSetPrice": true
		},
		"pieceNum": 1,
		"batchNo": "1",
		"expiryDate": "2023-04-30",
		"productionDate": "2021-01-31",
		"packageCostPrice": 2,
		"packageCount": 100,
		"pieceCount": 0,
		"useUnit": "盒",
		"useCount": 100,
		"returnPieceCount": 0,
		"returnPackageCount": 0,
		"useUnitCostPrice": 2,
		"useTotalCostPrice": 200,
		"lastModifiedDate": "2021-02-02T03:15:55.000Z",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"batchId": 100003915,
		"stock": {
			"id": 100003915,
			"batchId": 100003915
		},
		"useTotalCostPriceE": 200,
		"returnLeft": 0,
		"disable": 0,
		"v2DisableStatus": 0,
		"editable": {
			"readOnly": 1,
			"reason": "只能修改近三个月的入库单"
		}
	}],
	"logs": [{
		"id": 6581,
		"action": "创建入库单",
		"comment": "",
		"createdDate": "2021-02-02T03:15:55.000Z",
		"createdUserId": "71dbfd8322d0426ca4c428c1da07e9bf",
		"detail": null,
		"creator": {
			"id": "71dbfd8322d0426ca4c428c1da07e9bf",
			"name": "唐启涛",
			"mobile": "18190004085"
		}
	}],
	"mallOrderInfo": null,
	"totalPrice": 900,
	"toOrgan": {
		"id": "ffffffff000000000d4ab0e8079a4000",
		"parentId": "ffffffff000000000c5a1308069aa000",
		"nodeType": 2,
		"name": "杭州",
		"shortName": null,
		"addressProvinceName": "浙江",
		"addressCityName": "杭州市",
		"addressDistrictName": "下城区",
		"addressDetail": ""
	},
	"createdUser": {
		"id": "71dbfd8322d0426ca4c428c1da07e9bf",
		"name": "唐启涛",
		"mobile": "18190004085"
	}
}
-->

<template>
    <div>
        <div data-type="header">
            <div class="order-title">
                {{ tableTitle }}
            </div>
            <template v-if="multiPharmacyCanUse">
                <print-row class="print-stock-header">
                    <print-col
                        v-if="header.goodsInStore"
                        :span="8"
                    >
                        入库门店：{{ order && order.toOrgan.shortName || order.toOrgan.name || '' }}
                    </print-col>
                    <print-col
                        v-if="header.goodsInWarehouse"
                        :span="8"
                    >
                        入库库房：{{ order && order.pharmacy && order.pharmacy.name || '' }}
                    </print-col>
                    <print-col
                        v-if="header.goodsInDate"
                        :span="8"
                    >
                        入库日期：{{ order.inDate | parseTime('y-m-d') }}
                    </print-col>
                    <print-col
                        v-if="header.goodsInOrder"
                        :span="8"
                    >
                        入库单号：{{ order && order.orderNo || '' }}
                    </print-col>
                    <print-col
                        v-if="header.supplier"
                        :span="8"
                    >
                        供应商：{{ order && order.supplier || '' }}
                    </print-col>
                    <print-col
                        v-if="header.supplierOrder"
                        :span="headerOutOrderNoSpan ? 16 : 8"
                        :custom-style="{
                            maxHeight: '24pt',
                            overflow: 'hidden',
                        }"
                    >
                        随货单号：{{ order && order.outOrderNo || ' ' }}
                    </print-col>
                </print-row>
            </template>
            <template v-else>
                <print-row class="print-stock-header">
                    <print-col
                        v-if="header.goodsInStore"
                        :span="8"
                    >
                        入库门店：{{ order && order.toOrgan.shortName || order.toOrgan.name || '' }}
                    </print-col>
                    <print-col
                        v-if="header.goodsInDate"
                        :span="8"
                    >
                        入库日期：{{ order.inDate | parseTime('y-m-d') }}
                    </print-col>
                    <print-col
                        v-if="header.goodsInOrder"
                        :span="8"
                    >
                        入库单号：{{ order && order.orderNo || '' }}
                    </print-col>
                    <print-col
                        v-if="header.supplier"
                        :span="8"
                    >
                        供应商：{{ order && order.supplier || '' }}
                    </print-col>
                    <print-col
                        v-if="header.supplierOrder"
                        :span="headerOutOrderNoSpan ? 16 : 8"
                        :custom-style="{
                            maxHeight: '24pt',
                            overflow: 'hidden',
                        }"
                    >
                        随货单号：{{ order && order.outOrderNo || ' ' }}
                    </print-col>
                </print-row>
            </template>
        </div>

        <div data-type="big-table">
            <table
                class="print-stock-table-wrapper"
            >
                <thead>
                    <tr class="table-title">
                        <td
                            v-if="goodsInfo.goodsCode"
                            colspan="2"
                        >
                            药品编码
                        </td>
                        <td
                            v-if="goodsInfo.goodsName"
                            colspan="4"
                        >
                            药品名称
                        </td>
                        <td
                            v-if="goodsInfo.goodsSpec"
                            colspan="2"
                        >
                            规格
                        </td>
                        <td
                            v-if="goodsInfo.goodsManufacturer"
                            colspan="4"
                        >
                            厂家
                        </td>
                        <td
                            v-if="goodsInfo.goodsMedicineNmpn"
                            colspan="2"
                        >
                            批准文号
                        </td>
                        <td
                            v-if="goodsInfo.goodsDosageFormType"
                            colspan="2"
                        >
                            剂型
                        </td>
                        <td
                            v-if="goodsInfo.goodsType"
                            colspan="2"
                        >
                            类型
                        </td>
                        <td
                            v-if="goodsInfo.goodsSecondClass"
                            colspan="2"
                        >
                            二级分类
                        </td>
                        <td
                            v-if="goodsInfo.goodsPrice"
                            colspan="2"
                            class="number-right"
                        >
                            售价
                        </td>
                        <td
                            v-if="goodsInfo.goodsTaxRate"
                            colspan="2"
                            class="number-right"
                        >
                            税率
                        </td>
                        <td
                            v-if="goodsInInfo.goodsBatch"
                            colspan="2"
                        >
                            生产批号
                        </td>
                        <td
                            v-if="goodsInInfo.goodsProductionDate"
                            colspan="2"
                            style="width: 70px"
                        >
                            生产日期
                        </td>
                        <td
                            v-if="goodsInInfo.goodsExpiryDate"
                            colspan="2"
                            style="width: 70px"
                        >
                            有效日期
                        </td>
                        <td
                            v-if="goodsInInfo.goodsNumber"
                            colspan="2"
                            class="number-right"
                        >
                            数量
                        </td>
                        <td
                            v-if="goodsInInfo.goodsPurchasePrice"
                            colspan="2"
                            class="number-right"
                        >
                            进价
                        </td>
                        <td
                            v-if="goodsInInfo.goodsNoTaxAmount"
                            colspan="2"
                            class="number-right"
                        >
                            无税金额
                        </td>
                        <td
                            v-if="goodsInInfo.goodsTaxAmount"
                            colspan="2"
                            class="number-right"
                        >
                            含税金额
                        </td>
                        <td
                            v-if="goodsInInfo.goodsGrossProfit"
                            colspan="3"
                            class="number-right no-right-border"
                        >
                            预计毛利润
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(item) in order.list"
                        :key="item.id"
                        class="table-tr"
                    >
                        <!--            药品编码-->
                        <td
                            v-if="goodsInfo.goodsCode"
                            colspan="2"
                        >
                            {{ item.goods.shortId || '' }}
                        </td>

                        <!--            药品名称-->
                        <td
                            v-if="goodsInfo.goodsName"
                            colspan="4"
                        >
                            <span>{{ item.goods | goodsFullName }}</span>
                        </td>

                        <!--            规格-->
                        <td
                            v-if="goodsInfo.goodsSpec"
                            colspan="2"
                        >
                            {{ item.goods | goodsSpec }}
                        </td>

                        <!--            厂家-->
                        <td
                            v-if="goodsInfo.goodsManufacturer"
                            colspan="4"
                        >
                            {{ item.goods && ((item.goods.manufacturer && item.goods.manufacturer.slice(0,30)) || (item.goods.manufacturerFull && item.goods.manufacturerFull.slice(0,30))) || '' }}
                        </td>
                        <!--            批准文号-->
                        <td
                            v-if="goodsInfo.goodsMedicineNmpn"
                            colspan="2"
                        >
                            {{ item.goods.medicineNmpn || '' }}
                        </td>
                        <!--            剂型-->
                        <td
                            v-if="goodsInfo.goodsDosageFormType"
                            colspan="2"
                        >
                            {{ item.goods.dosageFormTypeName || '' }}
                        </td>
                        <!--            类型-->
                        <td
                            v-if="goodsInfo.goodsType"
                            colspan="2"
                        >
                            {{ formatTypeName(item.goods) || '' }}
                        </td>
                        <!--            二级分类-->
                        <td
                            v-if="goodsInfo.goodsSecondClass"
                            colspan="2"
                            class="no-right-border"
                        >
                            {{ item.goods.customTypeName || '' }}
                        </td>
                        <!--售价-->
                        <td
                            v-if="goodsInfo.goodsPrice"
                            colspan="2"
                            class="number-right"
                        >
                            {{ formatSellPrice(item.goods) }}
                        </td>
                        <!--税率-->
                        <td
                            v-if="goodsInfo.goodsTaxRate"
                            colspan="2"
                            class="number-right"
                        >
                            {{ item.goods.inTaxRat }}%
                        </td>
                        <!--            批号-->
                        <td
                            v-if="goodsInInfo.goodsBatch"
                            colspan="2"
                        >
                            {{ item.batchNo || '' }}
                        </td>
                        <!--            生产日期-->
                        <td
                            v-if="goodsInInfo.goodsProductionDate"
                            colspan="2"
                        >
                            {{ formatExpiryDate(item.productionDate) }}
                        </td>
                        <!--            有效日期-->
                        <td
                            v-if="goodsInInfo.goodsExpiryDate"
                            colspan="2"
                        >
                            {{ formatExpiryDate(item.expiryDate) }}
                        </td>

                        <!--            数量-->
                        <td
                            v-if="goodsInInfo.goodsNumber"
                            colspan="2"
                            class="number-right"
                        >
                            {{ item|complexCount }}
                        </td>

                        <!--            进价-->
                        <td
                            v-if="goodsInInfo.goodsPurchasePrice"
                            colspan="2"
                            class="number-right"
                        >
                            {{ moneyDigit(item.useUnitCostPrice, 5) }}/{{ item.useUnit }}
                        </td>

                        <!--无税金额-->
                        <td
                            v-if="goodsInInfo.goodsNoTaxAmount"
                            colspan="2"
                            class="number-right"
                        >
                            {{ item.useTotalCostPriceE | formatMoney(false) }}
                        </td>

                        <!--含税金额-->
                        <td
                            v-if="goodsInInfo.goodsTaxAmount"
                            colspan="2"
                            class="number-right"
                        >
                            {{ item.useTotalCostPrice | formatMoney(false) }}
                        </td>
                        <!--预计毛利润-->
                        <td
                            v-if="goodsInInfo.goodsGrossProfit"
                            colspan="3"
                            class="number-right no-right-border"
                        >
                            {{ item.grossProfit | formatMoney(false) }}
                        </td>
                    </tr>
                </tbody>
            </table>

            <print-row>
                <print-row data-last-page="LastPage">
                    <print-col
                        v-if="goodsInInfo.comment"
                        :span="24"
                    >
                        备注：{{ order && order.comment || '' }}
                    </print-col>
                </print-row>
                <print-row>
                    <print-col
                        v-if="statisticInfo.totalAmountChinese"
                        :span="8"
                    >
                        合计金额(大写)：{{ digitUppercase(order.amount) }}
                    </print-col>
                    <print-col
                        v-if="statisticInfo.totalAmount"
                        :span="4"
                    >
                        合计金额: {{ order.amount | formatMoney(false) }}
                    </print-col>
                    <print-col
                        v-if="statisticInfo.noTaxTotal"
                        :span="4"
                    >
                        无税合计: {{ order.amountExcludingTax | formatMoney(false) }}
                    </print-col>
                    <print-col
                        v-if="statisticInfo.totalVariety"
                        :span="4"
                    >
                        合计品种：{{ order && order.kindCount || '' }}
                    </print-col>
                </print-row>
                <print-row
                    data-last-page="LastPage"
                    class="signInfo-wrapper"
                >
                    <print-col
                        v-if="signInfo.consignee"
                        :span="4"
                    >
                        收货人：
                    </print-col>
                    <print-col
                        v-if="signInfo.quality"
                        :span="4"
                    >
                        质量情况：
                    </print-col>
                    <print-col
                        v-if="signInfo.acceptor"
                        :span="4"
                    >
                        验收人：{{ order && order.inspectUser && order.inspectUser.name }}
                    </print-col>
                    <print-col
                        v-if="signInfo.custodian"
                        :span="4"
                    >
                        保管人：
                    </print-col>
                    <print-col
                        v-if="signInfo.maker"
                        :span="4"
                    >
                        制单人：{{ order && order.createdUser && order.createdUser.name || '' }}
                    </print-col>
                    <print-col
                        :span="3"
                        class="pageInfo"
                    >
                        第 <span
                            data-page-no="PageNo"
                        ></span>页/共 <span data-page-count="PageCount"></span>页
                    </print-col>
                </print-row>
            </print-row>
        </div>
    </div>
</template>

<script>
    import {
        goodsSpec,
        goodsTypeName,
        digitUppercase,
        complexCount,
        parseTime,
        moneyDigit,
        formatMoney,
        goodsFullName, isChineseMedicine, isChineseMedicineV2,
    } from "./common/utils.js";
    import PrintCol from "./components/layout/print-col.vue";
    import PrintRow from "./components/layout/print-row.vue";
    import { PrintBusinessKeyEnum, GoodsInInitTypeEnum, GoodsReturnInitTypeEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import RKDataHandler from "./data-handler/RK-handler.js";

    export default {
        DataHandler: RKDataHandler,
        name: "RK",
        components: {
            PrintCol,
            PrintRow,
        },
        businessKey: PrintBusinessKeyEnum.GOODS_IN,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分', // 默认选择的等分纸
            },
        ],
        filters: {
            goodsSpec,
            complexCount,
            parseTime,
            formatMoney,
            goodsFullName,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        computed: {
            order() {
                return this.renderData.printData;
            },
            config() {
                if(this.renderData.config && this.renderData.config.inventoryGoodsIn) {
                    return this.renderData.config.inventoryGoodsIn;
                }
                return {};
            },
            header() {
                if (this.config && this.config.header){
                    return this.config.header;
                }
                return {}
            },
            headerOutOrderNoSpan() {
                for (let headerKey in this.header) {
                    if (this.header[headerKey] === 0) {
                        return true;
                    }
                }
                return false;
            },
            goodsInfo() {
                if (this.config && this.config.content && this.config.content.goodsInfo){
                    return this.config.content.goodsInfo;
                }
                return {}
            },
            goodsInInfo() {
                if (this.config && this.config.content && this.config.content.goodsInInfo){
                    return this.config.content.goodsInInfo;
                }
                return {}
            },
            statisticInfo() {
                if (this.config && this.config.footer && this.config.footer.statisticInfo){
                    return this.config.footer.statisticInfo;
                }
                return {}
            },
            signInfo() {
                if (this.config && this.config.footer && this.config.footer.signInfo){
                    return this.config.footer.signInfo;
                }
                return {}
            },
            // 是否初始化入库单
            isInitStockInOrder() {
                return GoodsInInitTypeEnum.includes(this.order?.type);
            },
            // 是否初始化退货单
            isInitStockOutOrder() {
                return GoodsReturnInitTypeEnum.includes(this.order?.type);
            },
            tableTitle() {
                let text = '采购入库单'

                if (this.isInitStockInOrder) {
                    text = '初始化入库单';
                }
                if (this.isInitStockOutOrder) {
                    text = '初始化退货单';
                }
                if (this.order) {
                    return `${this.order.toOrgan && this.order.toOrgan.name} ${text} `;
                } else {
                    return `${text}`
                }
            },
            multiPharmacyCanUse() {
                return this.order.multiPharmacyCanUse;
            },
            needTransGoodsClassificationName() {
                return this.order.needTransGoodsClassificationName;
            },
        },
        methods: {
            isChineseMedicine,
            formatMoney,
            moneyDigit,
            goodsTypeName,
            digitUppercase,
            formatExpiryDate(expiryDate) {
                if(expiryDate) {
                    return expiryDate.replace(/-/g,'/')
                }
                return ''
            },
            formatTypeName(goods) {
                let typeName = goodsTypeName(goods);
                if (this.needTransGoodsClassificationName && typeName === '中药饮片') {
                    return '配方饮片'
                }
                return typeName;
            },
            formatSellPrice(goods) {
                return  isChineseMedicineV2(goods) ? `${moneyDigit(goods.piecePrice, 5)}/${goods.pieceUnit}` :
                    `${moneyDigit(goods.packagePrice)}/${goods.packageUnit}`
            },
        },

    }
</script>

<style lang="scss">
@import "./style/inventory-common.scss";
.order-title {
  padding-bottom: 2pt;
}
.signInfo-wrapper {
  position: relative;
  .pageInfo {
    position: absolute;
    right: 0;
  }
}

</style>
