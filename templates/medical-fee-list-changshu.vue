<template>
    <div>
        <template v-for="(page, pageIndex) in currentRenderPage">
            <div
                :key="pageIndex"
                class="changsu-medical-fee-list"
            >
                <div
                    class="changshu-medical-fee-list-page"
                >
                    <div
                        v-if="blueInvoiceData"
                        class="blue-invoice"
                    >
                        销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                    </div>
                    <refund-icon
                        v-if="isRefundBill"
                        top="12mm"
                        left="24mm"
                    ></refund-icon>

                    <div class="organ">
                        {{ config.institutionName }}
                    </div>
                    <div class="year">
                        {{ year }}
                    </div>
                    <div class="month">
                        {{ month }}
                    </div>
                    <div class="day">
                        {{ day }}
                    </div>
                    <div class="charge-type">
                        就诊类别：{{ shebaoPayment.cardOwner ? '医保' : '自费' }}
                    </div>
                    <div class="patient-type">
                        人员类别：{{ shebaoPayment.cardOwnerType }}
                    </div>
                    <div class="patient">
                        {{ patient.name }}
                    </div>
                    <div class="id-card">
                        {{ patient.idCard }}
                    </div>

                    <!-- 项目 -->
                    <div class="form-items-wrapper">
                        <div
                            v-for="(item, index) in page.formItems"
                            :key="index + pageIndex"
                            class="form-item-tr"
                        >
                            <span
                                class="item-name"
                                overflow
                            >
                                <template v-if="item.medicalFeeGrade">[{{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}]</template>
                                {{ item.name }}
                                <template v-if="item.printFormType === PrintFormTypeEnum.PRESCRIPTION_WESTERN && item.displaySpec">（{{ item.displaySpec }}）</template>
                            </span>
                            <span
                                class="item-count"
                                overflow
                            >{{ item.discountedUnitPrice | formatMoney }}*{{ item.count }}{{ item.unit }}</span>
                            <span
                                class="total-price"
                                overflow
                            >{{ item.discountedPrice | formatMoney }}</span>
                        </div>

                        <div
                            v-if="hasOverPageTip"
                            class="only-one-page"
                        >
                            *** 因纸张限制，部分项目未打印 ***
                        </div>
                        <template v-else>
                            <div
                                v-if="pageIndex !== renderPage.length - 1"
                                class="only-one-page"
                            >
                                *** 接下页 ***
                            </div>
                        </template>
                    </div>

                    <!-- 费用类别 -->
                    <div class="medical-bill-wrapper">
                        <div
                            v-for="(item, index) in medicalBills"
                            :key="index"
                            class="medical-bill-item"
                        >
                            <div class="fee-type-name">
                                {{ item.name }}
                            </div>
                            <div class="fee-type-price">
                                {{ item.totalFee | formatMoney }}
                            </div>
                        </div>
                    </div>

                    <div class="upper-money">
                        {{ digitUppercase(finalFee) }}
                    </div>
                    <div class="money">
                        {{ finalFee | formatMoney }}
                    </div>
                    <div class="charger">
                        {{ printData.chargedByName }}
                    </div>
                    <div class="serial-no">
                        {{ printData.serialNo }}
                    </div>
                    
                    <!-- 社保信息 -->
                    <div class="shebao-wrapper">
                        <div class="shebao-content">
                            <div class="shebao-item">
                                统筹支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}
                            </div>
                            <div class="shebao-item">
                                大病支付：{{ extraInfo.hifmiPay | formatMoney }}
                            </div>
                            <div class="shebao-item">
                                其他支付：{{ shebaoPayment.otherPaymentFee | formatMoney }}
                            </div>
                        </div>
                        <div class="shebao-content">
                            <div class="shebao-item">
                                救助支付：{{ extraInfo.mafPay | formatMoney }}
                            </div>
                            <div class="shebao-item">
                                个人账户：{{ shebaoPayment.accountPaymentFee | formatMoney }}
                            </div>
                            <div class="shebao-item">
                                共济账户支付：{{ extraInfo.acctMulaidPay | formatMoney }}
                            </div>
                        </div>
                        <div class="shebao-content">
                            <div class="shebao-item">
                                现金支付：{{ printData.personalPaymentFee | formatMoney }}
                            </div>
                            <div class="shebao-item">
                                个人自付：{{ shebaoPayment.selfPaymentFee | formatMoney }}
                            </div>
                            <div class="shebao-item">
                                个人自费：{{ extraInfo.ownpayAmt | formatMoney }}
                            </div>
                        </div>
                        <div class="shebao-content">
                            <div class="shebao-item">
                                账户余额：{{ shebaoPayment.cardBalance | formatMoney }}
                            </div>
                            <div class="shebao-item">
                                合规自费：{{ extraInfo.inscpScpAmt | formatMoney }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import {PrintFormTypeEnum} from "./common/constants";
    import NationalBillData from "./mixins/national-bill-data";

    export default {
        name: "MedicalFeeListChangshu",
        components: {
            RefundIcon
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_FEE_LIST_CHANGSHU,
        pages: [
            {
                paper: PageSizeMap.MM160_101,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            PrintFormTypeEnum() {
                return PrintFormTypeEnum
            },
            medicalListConfig() {
                return this.renderData.config.medicalListConfig || { changshu: {} };
            },
            config() {
                return this.medicalListConfig.changshu || {};
            },
            splitType() {
                return this.config.splitType;
            },
            isOnlyOnePage() {
                if (this.splitType === 1) {
                    return this.renderPage.length > 1;
                }
                return false;
            },
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                if (this.isOnlyOnePage || this.extra.isPreview) {
                    return this.renderPage.slice(0, 1);
                }
                return this.renderPage;
            },
            composeChildrenConfig() {
                return this.config.composeChildren || 0;
            },
            chargeFormItems() {
                let res = [];
                this.chargeForms.forEach((form) => {
                    // 如果为套餐
                    if (form.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT) {
                        (form.chargeFormItems || []).forEach((formItem) => {
                            // 打印套餐名
                            if (this.composeChildrenConfig === 0) {
                                res.push(formItem);
                            } else {
                                // 打印套餐名及子项
                                if (this.composeChildrenConfig === 1) {
                                    res.push(formItem);
                                }
                                // 打印套餐名及子项 or 打印套餐内子项
                                (formItem.composeChildren || []).forEach((children) => {
                                    // 如果开启了医嘱收费项配置,目前只有医院管家开启
                                    if (this.isFeeCompose) {
                                        res = this.createIsFeeComposeRes(res, children)
                                    } else {
                                        res = this.createIsNotFeeComposeRes(res, children)
                                    }
                                });
                            }
                        })
                    } else {
                        (form.chargeFormItems || []).forEach((formItem) => {
                            // 如果开启了医嘱收费项配置,目前只有医院管家开启
                            if (this.isFeeCompose) {
                                res = this.createIsFeeComposeRes(res, formItem)
                            } else {
                                res = this.createIsNotFeeComposeRes(res, formItem)
                            }
                        })
                    }
                });
                return res.filter(billItem => {
                    if (billItem.name === '诊费' && res.length > 1) {
                        return !!billItem.discountedPrice;
                    }
                    return true;
                }) || [];
            },
            renderPage() {
                return this.spliceFormItems(this.chargeFormItems, 9);
            },
            year() {
                let date;
                if (this.printData.chargedTime) {
                    date = new Date(this.printData.chargedTime);
                } else {
                    date = new Date();
                }
                return date.getFullYear();
            },
            month() {
                let date;
                if (this.printData.chargedTime) {
                    date = new Date(this.printData.chargedTime);
                } else {
                    date = new Date();
                }
                let month = date.getMonth() + 1;
                if (month < 10) {
                    month = `0${month}`;
                }
                return month;
            },
            day() {
                let date;
                if (this.printData.chargedTime) {
                    date = new Date(this.printData.chargedTime);
                } else {
                    date = new Date();
                }
                let day = date.getDate();
                if (day < 10) {
                    day = `0${day}`;
                }
                return day;
            },
        },
    }
</script>
<style lang="scss">
* {
    padding: 0;
    margin: 0;
}

.changsu-medical-fee-list {
    @import "./components/refund-icon/refund-icon.scss";

    font-size: 9pt;

    .changshu-medical-fee-list-page {
        position: absolute;
        width: 160mm;
        height: 101mm;

        .blue-invoice,
        .organ,
        .year,
        .month,
        .day,
        .charge-type,
        .patient-type,
        .patient,
        .id-card,
        .form-items-wrapper,
        .upper-money,
        .money,
        .charger,
        .serial-no {
            position: absolute;
            line-height: 11pt;
        }

        .blue-invoice {
            top: 1mm;
            left: 24mm;
        }

        .organ {
            top: 13mm;
            left: 75mm;
        }

        .year {
            top: 21mm;
            left: 23mm;
        }

        .month {
            top: 21mm;
            left: 36mm;
        }

        .day {
            top: 21mm;
            left: 46mm;
        }

        .charge-type {
            top: 21mm;
            left: 60mm;
        }

        .patient-type {
            top: 21mm;
            left: 88mm;
        }

        .patient {
            top: 27mm;
            left: 41mm;
        }

        .id-card {
            top: 27mm;
            left: 75mm;
        }

        .form-items-wrapper {
            top: 38mm;
            left: 59mm;
            width: 81mm;
            height: 41mm;

            .form-item-tr {
                width: 100%;
                height: 11pt;
                font-size: 0;
                line-height: 11pt;

                .item-name,
                .item-count,
                .total-price {
                    display: inline-block;
                    *display: inline;
                    overflow: hidden;
                    font-size: 9pt;
                    white-space: nowrap;
                    *zoom: 1;
                }

                .item-name {
                    width: 57%;
                }

                .item-count {
                    width: 25%;
                    text-align: right;
                }

                .total-price {
                    width: 18%;
                    text-align: right;
                }
            }

            .only-one-page {
                text-align: center;
            }

            .next-page {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                text-align: center;
            }
        }

        .medical-bill-wrapper {
            position: absolute;
            top: 38mm;
            left: 20mm;
            width: 38mm;
            height: 41mm;
            font-size: 0;

            .medical-bill-item {
                position: relative;
                display: inline-block;
                width: 100%;
                height: 11pt;
                font-size: 0;
                line-height: 11pt;

                .fee-type-name,
                .fee-type-price {
                    position: absolute;
                    display: inline-block;
                    font-size: 9pt;
                }

                .fee-type-name {
                    left: 3mm;
                }

                .fee-type-price {
                    right: 3mm;
                }
            }
        }

        .upper-money {
            top: 78.5mm;
            left: 42mm;
        }

        .money {
            top: 78.5mm;
            left: 116mm;
        }

        .charger {
            top: 96.5mm;
            left: 89mm;
        }

        .serial-no {
            top: 96.5mm;
            left: 32mm;
        }

        .shebao-wrapper {
            position: absolute;
            top: 83.5mm;
            left: 33mm;
            width: 102mm;
            height: 16mm;
            font-size: 0;
            transform: scale(0.8);
            transform-origin: 0 0;

            .shebao-content {
                .shebao-item {
                    display: inline-block;
                    width: 33%;
                    font-size: 9pt;
                    line-height: 11pt;
                    vertical-align: top;
                }
            }
        }
    }
}

.abc-page_preview {
    color: #2a82e4;
    background: url("/static/assets/print/changshu.jpg");
    background-size: 160mm 101mm;

    .changshu-medical-fee-list-page {
        top: -1mm;
    }
}
</style>
