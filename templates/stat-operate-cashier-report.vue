<!--exampleData

{
    PRINT_REGION: 'hangzhou'
}

-->
<template>
    <div class="print-operate-cashier-report">
        <div class="report-title">
            {{ reportTitle }}
        </div>
        <abc-p
            :small="true"
            class="report-sub-title"
        >
            统计时间：{{ `${parseTime(fetchParams.dateRange[0], 'y-m-d h:i')} ～ ${parseTime(fetchParams.dateRange[1], 'y-m-d h:i')}` }}
        </abc-p>

        <table class="report-table">
            <tbody>
                <tr>
                    <td colspan="1">
                        医保支付合计
                    </td>
                    <td colspan="1">
                        {{ dailyData.summary.totalMedicalInsurance || 0 }}
                    </td>
                    <td colspan="1">
                        现金营收合计
                    </td>
                    <td colspan="1">
                        {{ dailyData.summary.totalCashRevenue || 0 }}
                    </td>
                    <td colspan="1">
                        充值合计
                    </td>
                    <td
                        colspan="1"
                        :title="dailyData.summary.totalRecharge || 0"
                    >
                        {{ dailyData.summary.totalRecharge || 0 }}
                    </td>
                    <td colspan="1">
                        实收合计
                    </td>
                    <td colspan="1">
                        {{ dailyData.summary.totalAmount || 0 }}
                    </td>
                </tr>
            </tbody>
        </table>

        <table
            class="report-table"
            style="margin-top: 20pt;"
        >
            <tbody>
                <tr>
                    <td
                        colSpan="6"
                        class="title-td"
                    >
                        费用分类
                    </td>
                </tr>
                <tr v-for="(rowItem,index) in feeTypeList">
                    <template v-for="(colItem,indexItem) in rowItem">
                        <td colspan="1">
                            {{ colItem.name }}
                        </td>
                        <td :colspan="(index * 3 + indexItem) === feeTypes.length - 1 ? lastRowCol : 1">
                            {{ colItem.value }}
                        </td>
                    </template>
                </tr>
            </tbody>
        </table>

        <hangzhou-table
            v-if="isZhejiangHangzhou"
            :budget-index="dailyData.budgetIndex"
            :medical-insurance="dailyData.medicalInsurance"
        ></hangzhou-table>
        <qingdao-table
            v-else-if="isShandongQingdao"
            :pay-mode="payModes"
            :extra-info="dailyData.budgetIndex.shebaoStatSummaryReportRsp.extraInfo"
        ></qingdao-table>
        <national-table
            v-else
            :pay-mode="payModes"
            :is-neimenggu="isNeimenggu"
            :national-medical-insurance="nationalMedicalInsurance"
        ></national-table>



        <table
            class="report-table"
            style="margin-top: 20pt;"
        >
            <tbody>
                <tr>
                    <td
                        colspan="6"
                        class="title-td"
                    >
                        挂号
                    </td>
                </tr>
                <tr>
                    <td colspan="1">
                        挂号发票启号
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.registrationFirstNumber || 0 }}
                    </td>
                    <td colspan="1">
                        挂号发票止号
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.registrationLastNumber || 0 }}
                    </td>
                    <td colspan="1">
                        挂号开票总数
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.registrationCount || 0 }}
                    </td>
                </tr>
                <tr>
                    <td colspan="1">
                        挂号作废发票数
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.registrationInvalidCount || 0 }}
                    </td>
                    <td colspan="1">
                        挂号作废发票金额
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.registrationInvalidAmount || 0 }}
                    </td>
                    <td colspan="1"></td>
                    <td colspan="1"></td>
                </tr>
                <tr>
                    <td colspan="1">
                        挂号作废发票号
                    </td>
                    <td colspan="5">
                        {{ dailyData.invoice.registrationInvalidNumber || 0 }}
                    </td>
                </tr>
                <tr>
                    <td colspan="1">
                        门诊发票启号
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.outpatientFirstNumber || 0 }}
                    </td>
                    <td colspan="1">
                        门诊发票止号
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.outpatientLastNumber || 0 }}
                    </td>
                    <td colspan="1">
                        门诊开票总数
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.outpatientCount || 0 }}
                    </td>
                </tr>
                <tr>
                    <td colspan="1">
                        门诊作废发票数
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.outpatientInvalidCount || 0 }}
                    </td>
                    <td colspan="1">
                        门诊作废发票金额
                    </td>
                    <td colspan="1">
                        {{ dailyData.invoice.outpatientInvalidAmount || 0 }}
                    </td>
                    <td colspan="1"></td>
                    <td colspan="1"></td>
                </tr>
                <tr>
                    <td colspan="1">
                        门诊作废发票号
                    </td>
                    <td
                        colspan="5"
                        style="word-wrap: break-word; word-break: break-all"
                    >
                        {{ dailyData.invoice.outpatientInvalidNumber || 0 }}
                    </td>
                </tr>
            </tbody>
        </table>

        <table class="report-table no-border-table">
            <tbody>
                <tr>
                    <td colspan="2">
                        收费员：{{ dailyData.cashierName }}
                    </td>
                    <td colspan="2">
                        制表人：
                    </td>
                    <td
                        colspan="4"
                        :title="dailyData.tabulationTime || 0"
                    >
                        制表时间：{{ dailyData.tabulationTime }}
                    </td>
                    <td colspan="2">
                        审核人：
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
    import HangzhouTable from './components/operate-cashier-report/hangzhou.vue';
    import QingdaoTable from './components/operate-cashier-report/qingdao.vue';
    import NationalTable from "./components/operate-cashier-report/national.vue";

    import PrintHandler from "./data-handler/print-handler.js";

    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import {
        parseTime,
    } from "./common/utils";
    export default {
        name: "OperateCashierReport",
        DataHandler: PrintHandler,
        components: {
            HangzhouTable,
            QingdaoTable,
            NationalTable,
        },
        businessKey: PrintBusinessKeyEnum.STAT_OPERATE_CASHIER_REPORT,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: Object,
        },
        data() {
            return {
                lastRow: false,
            }
        },
        computed: {
            feeTypeList() {
                return this.splitFeeType(this.printData.feeTypes, 3)
            },
            feeTypes() {
                return this.printData.feeTypes
            },
            lastRowCol() {
                return this.lastRow === 2 ? 3 : this.lastRow === 1 ? 5 : 1
            },
            printData() {
                return this.renderData.printData;
            },
            dailyReportTime() {
                return this.printData.dailyReportTime;
            },
            fetchParams() {
                return this.printData.fetchParams || {};
            },
            payModes() {
                return this.printData.payModes;
            },
            nationalMedicalInsurance() {
                return this.printData.nationalMedicalInsurance;
            },
            dailyData() {
                return this.printData.dailyData || {
                    summary: {},
                    invoice: {},
                    feeType: {},
                    budgetIndex: {
                        shebaoStatSummaryReportRsp: {},
                    },
                };
            },
            reportTitle() {
                let title = this.printData.clinicName || '';
                title += `收费日报`;
                return title;
            },
            isZhejiangHangzhou() {
                return this.printData.isZhejiangHangzhou;
            },
            isShandongQingdao() {
                return this.printData.isShandongQingdao;
            },
            isNeimenggu() {
                return this.printData.isNeimenggu;
            },
        },
        methods: {
            parseTime,
            splitFeeType(feeTypes, spiltLength) {
                let length = feeTypes.length;
                let newFeeTypes = [];
                for (let i = 0; i < length; i += spiltLength) {
                    newFeeTypes.push(feeTypes.slice(i,i + spiltLength))
                    if (i + spiltLength > length) {
                        this.lastRow = length % 3
                    }
                }
                return newFeeTypes
            },
        },
    }
</script>

<style lang="scss">
@import "./style/reset.scss";
.print-operate-cashier-report {
    margin: 0 auto;
    font-family: "Microsoft YaHei", "微软雅黑";

    .report-title {
        font-size: 16pt;
        line-height: 21pt;
        text-align: center;
        font-weight: 600;
    }
    .report-sub-title{
      margin:12pt 0 14pt 0;
    }

    .report-table {
        width: 100%;
        table-layout: fixed;

        &.no-border-table {
            td {
                border: none;
            }
        }

        td {
            padding: 7pt 6pt;
            font-size: 10pt;
            line-height: 11pt;
            border: 1px solid #000000;
        }

        .title-td {
            font-size: 10pt;
            font-weight: bold;
            line-height: 11pt;
            background-color: #f5f7fb;
        }
    }
}


</style>