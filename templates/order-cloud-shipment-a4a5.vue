<template>
    <div class="order-shipment-wrapper">
        <div data-type="header">
            <div class="order-title">
                发货单
            </div>
            <div class="order-info">
                <div
                    class="order-info-item"
                    style="min-width: 33%;padding-right: 8px"
                >
                    订单编号：{{ displayOrderNo }}
                </div>
                <div
                    class="order-info-item"
                    style="min-width: 25%"
                >
                    订单支付时间：{{ orderInfo.payTime | parseTime('y-m-d h:i') }}
                </div>
                <div
                    v-if="printData.receiverNameEnable"
                    class="order-info-item"
                    style="min-width: 16%"
                >
                    收件人：{{ receiverInfo.receiverNameMask }}
                </div>
                <div
                    v-if="printData.receiverPhoneEnable"
                    class="order-info-item"
                    style="min-width: 24%"
                >
                    收件人电话：{{ receiverInfo.receiverPhoneMask }}
                </div>
                <div
                    v-if="printData.receiverAddressEnable"
                    class="order-info-item"
                >
                    收件地址：{{ receiverInfo.receiverAddressMask }}
                </div>
            </div>
        </div>

        <div data-type="big-table">
            <table
                class="print-stock-table-wrapper shipment-order-table-wrapper"
                :class="{
                    'a4-table': isA4
                }"
            >
                <thead>
                    <tr class="table-title">
                        <td class="goods-name">
                            商品简称/商品名称
                        </td>
                        <td class="goods-spec">
                            规格简称/规格名称
                        </td>
                        <td class="unit-price">
                            单价
                        </td>
                        <td class="unit-count">
                            数量
                        </td>
                        <td class="total-price">
                            总价
                        </td>
                        <td class="real-price">
                            实收金额
                        </td>
                        <td
                            v-if="isA4"
                            class="goods-id"
                        >
                            商品ID
                        </td>
                        <!--                        <td-->
                        <!--                            v-if="isA4"-->
                        <!--                            class="goods-spec-id"-->
                        <!--                        >-->
                        <!--                            规格ID-->
                        <!--                        </td>-->
                        <td class="goods-no">
                            商品编码
                        </td>
                        <!--                        <td-->
                        <!--                            v-if="isA4"-->
                        <!--                            class="goods-spec-no"-->
                        <!--                        >-->
                        <!--                            规格编码-->
                        <!--                        </td>-->
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(order, index) in detailOrders"
                        :key="order.id"
                        class="table-tr"
                    >
                        <td class="goods-name">
                            {{ getGoodsInfo(order).goodsName || '' }}
                        </td>

                        <td class="goods-spec">
                            {{ getGoodsInfo(order).goodsSpec || '' }}
                        </td>

                        <td class="unit-price">
                            {{ (getGoodsInfo(order).goodsPrice || 0) | formatMoney }}
                        </td>

                        <td class="unit-count">
                            {{ getGoodsInfo(order).goodsCount || '' }}
                        </td>

                        <td class="total-price">
                            {{ order.totalPrice | formatMoney }}
                        </td>

                        <td class="real-price">
                            {{ order.actualAmount | formatMoney }}
                        </td>

                        <td
                            v-if="isA4"
                            class="goods-id"
                        >
                            {{ getGoodsInfo(order).goodsId || '' }}
                        </td>

                        <!--                        <td-->
                        <!--                            v-if="isA4"-->
                        <!--                            class="goods-spec-id"-->
                        <!--                        >-->
                        <!--                        </td>-->

                        <td class="goods-no">
                            {{ getGoodsInfo(order).skuId || '' }}
                        </td>

                        <!--                        <td-->
                        <!--                            v-if="isA4"-->
                        <!--                            class="goods-spec-no"-->
                        <!--                        >-->
                        <!--                        </td>-->
                    </tr>
                </tbody>
            </table>
        </div>

        <div
            data-type="footer"
            class="order-footer"
        >
            <print-row>
                <print-col
                    v-if="printData.mallName"
                    :span="6"
                >
                    店铺名称：{{ printData.mallName }}
                </print-col>
                <print-col
                    v-if="printData.sellerPhone"
                    :span="5"
                >
                    卖家电话：{{ printData.sellerPhone || '' }}
                </print-col>
                <print-col
                    v-if="sellerAddressStr"
                    :span="10"
                >
                    发货地址：{{ sellerAddressStr }}
                </print-col>
                <print-col
                    v-if="!isPreview"
                    :span="3"
                    class="order-page-total"
                >
                    <span data-page-no="PageNo"></span>
                    <span>/</span>
                    <span data-page-count="PageCount"></span>
                    <span>页</span>
                </print-col>
            </print-row>
        </div>
    </div>
</template>
<script>
    import PrintRow from "./components/layout/print-row.vue";
    import PrintCol from "./components/layout/print-col.vue";
    import CommonHandler from "./data-handler/common-handler";
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import {parseTime} from "./common/utils";

    export default {
        name: 'OrderCloudShipmentA4a5',
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.ORDER_CLOUD_SHIPMENT_A4A5,
        components: {PrintCol, PrintRow},
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
        ],
        filters: {
            parseTime
        },
        props: {
            options: {
                type: Object,
                default() {
                    return {}
                }
            },
            renderData: {
                type: Object,
                default: () => ({
                    printData: {},
                }),
            },
            extra: {
                type: Object,
                default: () => ({})
            },
        },
        computed: {
            isPreview() {
                return this.extra.isPreview
            },
            printData() {
                return this.renderData.printData || {};
            },
            printConfig() {
                return this.renderData.config || {};
            },
            orderInfo() {
                return this.printData.orderInfo || {}
            },
            detailOrders() {
                return this.orderInfo.detailOrders || []
            },
            displayOrderNo() {
                return this.detailOrders.map(it => it.orderNo).join('，')
            },
            receiverInfo() {
                return this.orderInfo.receiverInfo || {}
            },
            shipper() {
                return this.printData.shipper || {}
            },
            isA4() {
                return this.options.page?.size === 'A4';
            },
            sellerAddressStr() {
                if(!this.printData.sellerAddress) return ''
                const {
                    city,
                    detail,
                    district,
                    province,
                } = this.printData.sellerAddress.address;
                const _arr = [];
                if (province) {
                    _arr.push(`${province}`);
                }
                if (city) {
                    _arr.push(`${city}`);
                }
                if (district) {
                    _arr.push(district);
                }
                if (detail) {
                    _arr.push(detail);
                }
                return _arr.join('');
            },
        },
        methods: {
            getGoodsInfo(order) {
                const goods = order.goodsList[0]
                return goods || {}
            },
        }
    }
</script>

<style lang="scss">
@import "./style/inventory-common.scss";
.order-shipment-wrapper {
  .order-title {
    padding-bottom: 24px;
  }
  .order-info {
    .order-info-item {
      display: inline-block;
      font-size: 10pt;
      line-height: 12pt;
    }
  }

  .shipment-order-table-wrapper {
    margin-top: 10pt;
    .align-center {
      text-align: center;
    }
    .table-title {
      background: #F2F4F7;
    }
    td,
    th {
      padding: 6pt;
      font-size: 10pt;
    }

    .order-no,
    .unit-count {
      width: 6%;
      padding: 6pt 2pt;
      text-align: center;
    }
    .goods-name {
      width: 25%;
    }
    .unit-price,
    .real-price,
    .total-price {
      width: 10.8%;
    }
    .goods-spec,
    .goods-no,
    .goods-spec-no {
      width: 14.5%;
    }

    &.a4-table {
      .order-no,
      .unit-count {
        width: 5%;
      }
      .goods-name {
        width: 23%;
      }
      .unit-price,
      .total-price,
      .real-price {
        width: 7%;
      }
      .goods-id,
      .goods-spec-id,
      .goods-spec,
      .goods-no,
      .goods-spec-no {
        width: 9%;
      }
    }
  }
  .order-footer {
    padding-top: 12pt;
    border-top: 1px solid #000;
  }
  .order-page-total {
    text-align: right;
  }
}

</style>