<template>
    <div class="settlement-detail-wrapper">
        <div data-type="header">
            <div class="settlement-detail-title">
                付款明细
            </div>
            <print-row class="settlement-detail-info">
                <print-col
                    :span="8"
                >
                    供应商：{{ supplierName }}
                </print-col>
                <print-col
                    :span="8"
                >
                    销售员：{{ supplierSellerName }}
                </print-col>
                <print-col
                    :span="8"
                >
                    付款单号：{{ printData && printData.orderNo || '' }}
                </print-col>
                <print-col
                    :span="8"
                >
                    申请日期：{{ printData.createdDate | parseTime('y-m-d') }}
                </print-col>
                <print-col
                    :span="8"
                >
                    提交人：{{ createdUserName }}
                </print-col>
            </print-row>
        </div>


        <template v-if="settlements.length">
            <table class="settlement-total-table settlement-table">
                <tr class="settlement-total">
                    <td
                        colspan="28"
                        style="border-bottom: none"
                    >
                        <print-row>
                            <print-col
                                :span="4"
                            >
                                按购进数量付款的项目
                            </print-col>
                            <print-col
                                :span="20"
                                style="text-align: right;"
                            >
                                价税合计(小写): {{
                                    buySummary.amount | formatMoney
                                }}, 价税合计(大写): {{
                                    digitUppercase(buySummary.amount)
                                }}, 金额合计: {{
                                    buySummary.amountExcludingTax | formatMoney
                                }}, 税额合计: {{
                                    buySummary.tax | formatMoney
                                }}
                            </print-col>
                        </print-row>
                    </td>
                </tr>
            </table>
            <table
                class="settlement-table"
                data-type="complex-table"
            >
                <thead>
                    <tr>
                        <th colspan="2">
                            商品编码
                        </th>
                        <th colspan="5">
                            商品名称
                        </th>
                        <th colspan="2">
                            生产批号
                        </th>
                        <th colspan="2">
                            效期
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            进价
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            付款数量
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            价税合计
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            金额
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            税额
                        </th>
                        <th colspan="2">
                            单号
                        </th>
                        <th colspan="2">
                            出/入库时间
                        </th>
                        <th colspan="3">
                            出/入库门店
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(item) in settlements"
                        :key="item.id"
                    >
                        <td colspan="2">
                            {{ item.goods && item.goods.shortId || '' }}
                        </td>
                        <td colspan="5">
                            <span style="font-weight: 600;">{{ item.goods && item.goods.displayName || '' }}</span>
                            <div style="font-size: 10px;font-weight: 400;">
                                <span>{{ item.goods && item.goods.displaySpec || '' }}</span>
                                <span> {{ item.goods && item.goods.manufacturerFull || '' }}</span>
                            </div>
                        </td>
                        <td colspan="2">
                            {{ item.batchNo }}
                        </td>
                        <td colspan="2">
                            {{ item.expiryDate | parseTime('y-m-d') }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right"
                        >
                            {{ item.packageCostPrice | formatMoney(false) }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right"
                        >
                            {{ `${item.useCount || ''}${item.useUnit || ''}` }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right;padding: 4px 2px;"
                        >
                            {{ item.useTotalCostPrice | formatMoney }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right;padding: 4px 2px;"
                        >
                            {{ item.useTotalCostPriceE | formatMoney }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right"
                        >
                            {{ item.useTotalCostPriceTax | formatMoney }}
                        </td>
                        <td colspan="2">
                            {{ item.orderNo }}
                        </td>
                        <td colspan="2">
                            {{ item.orderInDate | parseTime('y-m-d') }}
                        </td>
                        <td colspan="3">
                            {{ item.toOrgan && item.toOrgan.name || '' }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </template>
        <template v-if="settlementBatches.length">
            <table class="settlement-total-table settlement-table">
                <tr class="settlement-total">
                    <td
                        colspan="27"
                        style="border-bottom: none"
                    >
                        <print-row>
                            <print-col
                                :span="4"
                            >
                                按销售数量付款的项目
                            </print-col>
                            <print-col
                                :span="20"
                                style="text-align: right;"
                            >
                                价税合计(小写): {{
                                    sellSummary.amount | formatMoney
                                }}, 价税合计(大写): {{
                                    digitUppercase(sellSummary.amount)
                                }}, 金额合计: {{
                                    sellSummary.amountExcludingTax | formatMoney
                                }}, 税额合计: {{
                                    sellSummary.tax | formatMoney
                                }}
                            </print-col>
                        </print-row>
                    </td>
                </tr>
            </table>
            <table
                class="settlement-table"
                data-type="complex-table"
            >
                <thead>
                    <tr>
                        <th colspan="2">
                            商品编码
                        </th>
                        <th colspan="5">
                            商品名称
                        </th>
                        <th colspan="2">
                            销售周期
                        </th>
                        <th colspan="2">
                            生产批号
                        </th>
                        <th colspan="2">
                            效期
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            进价
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            付款数量
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            价税合计
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            金额
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            税额
                        </th>
                        <th
                            colspan="2"
                            style="text-align: right"
                        >
                            进货数量
                        </th>
                        <th colspan="2">
                            生产日期
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr
                        v-for="(item) in settlementBatches"
                        :key="item.id"
                    >
                        <td colspan="2">
                            {{ item.shortId || '' }}
                        </td>
                        <td colspan="5">
                            <span style="font-weight: 600;">{{ item.displayName || '' }}</span>
                            <div style="font-size: 10px;font-weight: 400;">
                                <span>{{ item.displaySpec || '' }}</span>
                                <span> {{ item.manufacturerFull || '' }}</span>
                            </div>
                        </td>
                        <td
                            colspan="2"
                            style="padding: 4px 2px"
                        >
                            {{ `${item.saleStartDate || ''} ~ ${item.saleEndDate || ''}` }}
                        </td>
                        <td colspan="2">
                            {{ item.batchNo || '' }}
                        </td>
                        <td colspan="2">
                            {{ item.expiryDate | parseTime('y-m-d') }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right"
                        >
                            {{ item.packageCostPrice | formatMoney(false) }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right"
                        >
                            {{ `${item.payCount || ''}${item.useUnit || ''}` }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right;padding: 4px 2px;"
                        >
                            {{ item.amount | formatMoney }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right;padding: 4px 2px;"
                        >
                            {{ item.amountExcludingTax | formatMoney }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right"
                        >
                            {{ item.tax | formatMoney }}
                        </td>
                        <td
                            colspan="2"
                            style="text-align: right"
                        >
                            {{ `${item.stockInCount || ''}${item.useUnit || ''}` }}
                        </td>
                        <td colspan="2">
                            {{ item.productionDate | parseTime('y-m-d') }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </template>
        <print-row
            v-if="printData.remark"
            class="settlement-detail-remark"
            data-last-page="LastPage"
        >
            <print-col
                :span="24"
            >
                备注：{{ printData.remark || '' }}
            </print-col>
        </print-row>

        <div
            data-type="footer"
            class="settlement-detail-footer"
        >
            <print-row class="settlement-detail-total-info">
                <print-col
                    :span="8"
                >
                    价税合计(小写)：{{ settlementOrderSummary.amount | formatMoney }}
                </print-col>
                <print-col
                    :span="8"
                >
                    金额(小写)：{{ settlementOrderSummary.amountExcludingTax | formatMoney }}
                </print-col>
                <print-col
                    :span="8"
                >
                    税额(小写)：{{ settlementOrderSummary.tax | formatMoney }}
                </print-col>

                <print-col
                    :span="8"
                >
                    价税合计(大写)：{{ digitUppercase(settlementOrderSummary.amount) }}
                </print-col>
                <print-col
                    :span="8"
                >
                    金额(大写)：{{ digitUppercase(settlementOrderSummary.amountExcludingTax) }}
                </print-col>
                <print-col
                    :span="8"
                >
                    <div style="display: flex; justify-content: space-between;align-items: flex-end;">
                        <span>
                            税额(大写)：{{ digitUppercase(settlementOrderSummary.tax) }}
                        </span>
                        <div class="settlement-page-no">
                            第<span data-page-no="PageNo"></span>页/共<span data-page-count="PageCount"></span>页
                        </div>
                    </div>
                </print-col>
            </print-row>
        </div>
    </div>
</template>

<script>
    import {
        digitUppercase,
        parseTime,
        formatMoney,
    } from "./common/utils.js";
    import CommonHandler from "./data-handler/common-handler";
    import { PrintBusinessKeyEnum } from "./constant/print-constant";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import PrintCol from "./components/layout/print-col.vue";
    import PrintRow from "./components/layout/print-row.vue";

    export default {
        name: 'SettlementReview',
        components: { PrintRow, PrintCol },
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.SETTLEMENT_DETAIL,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分', // 默认选择的等分纸
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        filters: {
            parseTime,
            formatMoney,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            settlements() {
                return this.printData.settlements || [];
            },
            settlementBatches() {
                return this.printData.settlementBatches || [];
            },
            buySummary(){
                return this.printData.buySummary||{}
            },
            sellSummary(){
                return this.printData.sellSummary||{}
            },
            settlementOrderSummary(){
                return this.printData.settlementOrderSummary||{}
            },
            supplierName() {
                return this.printData.supplierObj?.name || '';
            },
            supplierSellerName() {
                return this.printData.supplierSeller?.name || this.printData.supplierSellerName || ''
            },
            createdUserName() {
                return this.printData.createdUser?.name || '';
            },
            reviewUserName() {
                return this.printData.reviewUser?.name || '';
            },
            isApplication() {
                return this.printData.type === 'application';
            },
        },
        methods: {
            digitUppercase,

        },
    }
</script>

<style lang="scss">
@import "./style/inventory-common.scss";

.settlement-detail-wrapper {
  box-sizing: border-box;

  * {
    box-sizing: border-box;
  }

  .settlement-detail-title {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 4px;
    color: var(--abc-color-T1, #000);
    font-family: SimSun, serif;
    font-size: 19px;
    font-weight: 700;
    line-height: 27px;
  }

  .settlement-detail-info {
    padding-bottom: 4px;

    .print-col{
      font-size: 11px;
      font-weight: 300;
    }
  }
  .settlement-total-table {
    margin-bottom: 0 !important;

    .print-col{
      font-size: 11px;
      font-weight: 400;
    }
  }

  .settlement-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    font-size: 11px;
    margin-bottom: 4px;

    th, td {
      flex-wrap: wrap;
      border: 1px solid #000;
      padding: 3px 4px 4px;
      word-break: break-all;
      font-size: 11px;
      font-style: normal;
    }

    th {
      font-weight: 350;
    }

    td {
      font-weight: 400;
    }
  }

  .settlement-detail-remark .print-col{
    font-size: 11px;
    font-weight: 300;
  }
  
  .settlement-detail-footer {

    .settlement-detail-total-info .print-col{
      font-size: 11px;
      font-style: normal;
      font-weight: 400;
      height: 15px;
      line-height: 15px;
    }

    .settlement-page-no {
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
      height: 13px;
      line-height: 13px;
      white-space: nowrap;
    }
  }
}
</style>
