export const MEDICINE_USAGE = Object.freeze( [
    {
        name: '口服',
        type: 1,
        latin: 'po',
    },
    {
        name: '含服',
        type: 1,
        latin: '含服',
    },
    {
        name: '嚼服',
        type: 1,
        latin: '嚼服',
    },
    {
        name: '晨服',
        type: 1,
        latin: '晨服',
    },
    {
        name: '餐前服',
        type: 1,
        latin: '餐前服',
    },
    {
        name: '餐中服',
        type: 1,
        latin: '餐中服',
    },
    {
        name: '餐后服',
        type: 1,
        latin: '餐后服',
    },
    {
        name: '睡前服',
        type: 1,
        latin: '睡前服',
    },

    {
        name: '静脉滴注',
        type: 2,
        latin: 'ivgtt',
    },
    {
        name: '静脉注射',
        type: 2,
        latin: 'iv',
    },
    {
        name: '肌内注射',
        type: 2,
        latin: 'im',
    },
    {
        name: '腔内注射',
        type: 2,
        latin: '腔内注射',
    },

    {
        name: '雾化吸入',
        type: 2,
        latin: '雾化吸入',
        isNebulizer: true,
    },
    {
        name: '皮下注射',
        type: 2,
        latin: 'ih',
    },
    {
        name: '皮内注射',
        type: 2,
        latin: 'id',
    },
    {
        name: '穴位注射',
        type: 2,
        latin: '穴位注射',
    },

    {
        name: '直肠滴注',
        type: 2,
        latin: '直肠滴注',
    },
    {
        name: '椎管内注射',
        type: 2,
        latin: '椎管内注射',
    },
    {
        name: '局部注射',
        type: 2,
        latin: '局部注射',
    },
    {
        name: '局部麻醉',
        type: 2,
        latin: '局部麻醉',
    },
    {
        name: '超声透药',
        type: 2,
        latin: '超声透药',
    },
    {
        name: '入壶静滴',
        type: 2,
        latin: '入壶静滴',
    },
    {
        name: '输液冲管',
        type: 2,
        latin: '输液冲管',
    },
    {
        name: '静脉泵入',
        type: 2,
        latin: '静脉泵入',
    },
    {
        name: '封管',
        type: 2,
        latin: '封管',
    },
    {
        name: '鼻饲',
        type: 2,
        latin: '鼻饲',
    },
    {
        name: '膀胱给药',
        type: 2,
        latin: '膀胱给药',
    },

    {
        name: '溶媒用',
        type: 3,
        latin: '溶媒用',
    },
    {
        name: '外用',
        type: 3,
        latin: 'us ext',
    },
    {
        name: '滴眼',
        type: 3,
        latin: '滴眼',
    },
    {
        name: '滴鼻',
        type: 3,
        latin: '滴鼻',
    },
    {
        name: '滴耳',
        type: 3,
        latin: '滴耳',
    },
    {
        name: '口腔喷入',
        type: 3,
        latin: '口腔喷入',
    },
    {
        name: '吸入',
        type: 3,
        latin: '吸入',
    },
    {
        name: '鼻腔喷入',
        type: 3,
        latin: '鼻腔喷入',
    },
    {
        name: '含漱',
        type: 3,
        latin: '含漱',
    },
    {
        name: '涂抹',
        type: 3,
        latin: '涂抹',
    },
    {
        name: '塞肛',
        type: 3,
        latin: '塞肛',
    },
    {
        name: '直肠给药',
        type: 3,
        latin: '直肠给药',
    },
    {
        name: '阴道给药',
        type: 3,
        latin: '阴道给药',
    },
    {
        name: '膀胱冲洗',
        type: 3,
        latin: '膀胱冲洗',
    },
    {
        name: '灌肠',
        type: 3,
        latin: '灌肠',
    },
])

export const MEDICINE_FREQ = Object.freeze( [
    {
        id: 1,
        en: 'qd',
        name: '每天1次',
        namePY: 'meitian1ci',
        namePYFirst: 'MT1C',
        time: 24,
    },
    {
        id: 2,
        en: 'bid',
        name: '每天2次',
        namePY: 'meitian2ci',
        namePYFirst: 'MT2C',
        time: 12,
    },
    {
        id: 3,
        en: 'tid',
        name: '每天3次',
        namePY: 'meitian3ci',
        namePYFirst: 'MT3C',
        time: 8,
    },
    {
        id: 4,
        en: 'qid',
        name: '每天4次',
        namePY: 'meitian4ci',
        namePYFirst: 'MT4C',
        time: 6,
    },
    {
        id: 5,
        en: 'qod',
        name: '隔日1次',
        namePY: 'geri1ci',
        namePYFirst: 'GR1C',
        time: 48,
    },
    {
        id: 6,
        en: 'qw',
        name: '每周1次',
        namePY: 'meizhou1ci',
        namePYFirst: 'MZ1C',
        time: 168,
    },
    {
        id: 7,
        en: 'biw',
        name: '每周2次',
        namePY: 'meizhou2ci',
        namePYFirst: 'MZ2C',
        time: 84,
    },
    {
        id: 8,
        en: 'hs',
        name: '临睡前',
        namePY: 'linshuiqian',
        namePYFirst: 'LSQ',
        time: 24,
    },
    {
        id: 9,
        en: 'qn',
        name: '每晚1次',
        namePY: 'meiwan1ci',
        namePYFirst: 'MW1C',
        time: 24,
    },
    {
        id: 10,
        en: 'q2h',
        name: '每2小时1次',
        namePY: 'mei2xiaoshi1ci',
        namePYFirst: 'M2XS1C',
        time: 2,
    },
    {
        id: 11,
        en: 'q4h',
        name: '每4小时1次',
        namePY: 'mei4xiaoshi1ci',
        namePYFirst: 'M4XS1C',
        time: 4,
    },
    {
        id: 12,
        en: 'q6h',
        name: '每6小时1次',
        namePY: 'mei6xiaoshi1ci',
        namePYFirst: 'M6XS1C',
        time: 6,
    },
    {
        id: 13,
        en: 'q8h',
        name: '每8小时1次',
        namePY: 'mei8xiaoshi1ci',
        namePYFirst: 'M8XS1C',
        time: 8,
    },
    {
        id: 14,
        en: 'q12h',
        name: '每12小时1次',
        namePY: 'mei12xiaoshi1ci',
        namePYFirst: 'M12XS1C',
        time: 12,
    },
    {
        id: 15,
        en: 'st',
        name: '立即',
        namePY: 'liji',
        namePYFirst: 'LJ',
        time: null,
    },
    {
        id: 16,
        en: 'sos',
        name: '需要时',
        namePY: 'xuyaoshi',
        namePYFirst: 'XYS',
        time: null,
    },
    {
        id: 17,
        en: 'prn',
        name: '需要时(长期)',
        namePY: 'xuyaoshi(changqi)',
        namePYFirst: 'XYS(CQ)',
        time: null,
    },
])

export  const TreatmentSystemUnit =  Object.freeze([
    {
        id: '0',
        name: '次',
        type: 0,
    },
    {
        id: '1',
        name: '项',
        type: 0,
    },
    {
        id: '2',
        name: '针',
        type: 0,
    },
    {
        id: '3',
        name: '根',
        type: 0,
    },
    {
        id: '4',
        name: '条',
        type: 0,
    },
    {
        id: '5',
        name: '颗',
        type: 0,
    },
    {
        id: '6',
        name: '贴',
        type: 0,
    },
    {
        id: '7',
        name: '副',
        type: 0,
    },
    {
        id: '8',
        name: '袋',
        type: 0,
    },
    {
        id: '9',
        name: '组',
        type: 0,
    },
    {
        id: '10',
        name: '分钟',
        type: 0,
    },
    {
        id: '11',
        name: '小时',
        type: 0,
    },
    {
        id: '12',
        name: '穴位',
        type: 0,
    },
    {
        id: '13',
        name: '部位',
        type: 0,
    },
    {
        id: '14',
        name: '壮',
        type: 0,
    },
    {
        id: '15',
        name: '牙',
        type: 0,
    },
    {
        id: '16',
        name: '洞',
        type: 0,
    },
    {
        id: '17',
        name: '根管',
        type: 0,
    },
    {
        id: '18',
        name: '单颌',
        type: 0,
    },
    {
        id: '19',
        name: '单侧',
        type: 0,
    },
],
)

export const MEDICINE_TAG_FREQ = Object.freeze({
    qd: 1,
    bid: 2,
    tid: 3,
    qid: 4,
    hs: 1,
    qn: 1,
    st: 1,
    sos: 1,
    prn: 1,
});
