/**
 * 配镜处方字段加上单位
 * @param val
 * @param param
 * @returns {*|string}
 */
export function filterGlassesUnit(val, param) {
    if (!val) return '';
    let result = val;
    switch (param) {
    case 'frameSpherical':
        result += 'D';
        break;
    case 'frameLenticular':
        result += 'D';
        break;
    case 'contactLenticular':
        result += 'D';
        break;
    case 'frameAxial':
        result += '°';
        break;
    case 'contactAxial':
        result += '°';
        break;
    case 'framePrism':
        result += 'Δ';
        break;
    case 'frameAdd':
        result += 'D';
        break;
    case 'framePupilDistance':
        result += 'mm';
        break;
    case 'framePupilHeight':
        result += 'mm';
        break;
    case 'contactFocalLength':
        result += 'D';
        break;
    case 'contactBozr':
        result += 'mm';
        break;
    case 'contactDiameter':
        result += 'mm';
        break;
    }
    return result;
}