export class Checkbox {
    constructor({label, value, type, append, id, show}) {
        this.label = label;
        this.value = value;
        this.id = id;
        this.type = type;
        this.append = append;
        this.show = show
    }
    getRenderComponent(renderConfig = []) {
        console.log(renderConfig)
        const renderComponentOptions = renderConfig.find(it => it.label === this.label  && it.type === this.type)
        if(renderComponentOptions) {
            return new Checkbox(renderComponentOptions)
        }

        return null
    }
    getRenderComponentEx(renderConfig = []) {

        const renderComponentOptions = renderConfig.find(it => it.label === this.label && it.id === this.id && it.type === this.type)
        if(renderComponentOptions) {
            return new Checkbox(renderComponentOptions)
        }

        return null
    }
}
