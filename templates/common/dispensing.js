import {SourceFormTypeEnum} from './constants';

export function extractBatchNo(dispensingForms) {
    dispensingForms.forEach((form) => {
        const modifiedDispensingForms = [];
        if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
            form.dispensingFormItems.forEach((item) => {
                modifiedDispensingForms.push(item);
            });
        } else {
            form.dispensingFormItems.forEach((item) => {
                if (item.dispensingFormItemBatches?.length) {
                    item.dispensingFormItemBatches.forEach((batch) => {
                        const newItem = {
                            ...item,
                            batchNo: batch.batchNo,
                            count: batch.unitCount,
                        };
                        modifiedDispensingForms.push(newItem);
                    });
                } else {
                    modifiedDispensingForms.push(item);
                }
            });
        }

        form.dispensingFormItems = modifiedDispensingForms;
    });

    return dispensingForms;
}
