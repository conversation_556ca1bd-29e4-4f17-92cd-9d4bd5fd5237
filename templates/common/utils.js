import Big from 'big.js';
import {
    CLINIC_NODE_TYPE, DispenseOrderFormItemTypeEnum,
    EXAMINATION_RESULT_ENUM,
    GOODS_TYPE_NAME, GoodsFeeType,
    GoodsSubTypeEnum,
    GoodsTypeEnum, MARTIAL_LABEL_ENUM, MedicalFeeGradeEnum,
    MEDICINE_USAGE,
    PROCESS_TYPES_ENUM, TITLE_MAX_LENGTH,
} from "./constants.js";
import { TreatmentSystemUnit } from "./western-medicine-config.js";
import { calNature, formatAst, formatAstResult } from './medical-transformat';

export function compose(...fns) {
    return function (initialArg) {
        let res = initialArg
        for (let i = fns.length - 1; i > -1; i--) {
            res = fns[i](typeof res === 'object' ? clone(res) : res)
        }
        return res
    }
}

export function pipe(...fns) {
    return function (initialAgr) {
        let res = initialAgr
        for (let i = 0; i < fns.length; i++) {
            res = fns[i](typeof res === 'object' ? clone(res) : res)
        }
        return res
    }
}

export function clone(obj) {
    if (null == obj || "object" != typeof obj) return obj
    if (obj instanceof Array) {
        let copy = [];
        for (let i = 0, len = obj.length; i < len; ++i) {
            copy[i] = clone(obj[i])
        }
        return copy
    }
    if (obj instanceof Object) {
        let copy = {}
        for (let attr in obj) {
            if (obj.hasOwnProperty(attr)) copy[attr] = clone(obj[attr])
        }
        return copy
    }
}


export function safeGet(fn, arg = {}) {
    try {
        return fn(arg)
    } catch (error) {
        console.warn('error: get from undefined: ', error);
        return ''
    }
}

//格式化年龄
// monthYear 岁以下显示 月份
// dayYear 岁以下显示天数
export function formatAge(ageObj, options) {
    let monthYear = 0;
    let dayYear = 0;
    let defaultValue = '';
    try {
        if (options) {
            monthYear = options.monthYear || 0;
            dayYear = options.dayYear || 0;
            defaultValue = options.defaultValue || '';
        }
        if (!ageObj) return defaultValue;
        //JSON字符串需要转换
        if (typeof ageObj === 'string' && (/^\{.*\}$/).test(ageObj))
            ageObj = JSON.parse(ageObj)

        let { year, month, day } = ageObj;

        if (year)
            return `${year} 岁` + (month && year < monthYear ? `${month} 月` : '') + (day && year < dayYear ? `${day} 天` : '');
        if (month)
            return `${month} 月` + (day && year < dayYear ? `${day} 天` : '');
        if (day)
            return `${day} 天`;

        return defaultValue;
    } catch (err) {
        return defaultValue;
    }
}

export function toMoney(number) {
    if (!number) number = 0;
    return ((Math.round((number || 0) * 100)) / 100).toFixed(2).toString().replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','));
}

// 进销存成本算法调整需求新增-金额格式化规则(出库-调拨-领用-盘点)
export function toMoneyByLocaleString(money, options = {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4,
}) {
    money = Number(money) || 0;
    return money.toLocaleString('en-US', options);
}

/**
 * @desc 格式化金额，支持 2 - 4 位小数
 * <AUTHOR>
 * @date 2019/03/26 14:11:12
 */
export function formatMoney(num, toFixed2 = true) {
    if (num === '-') return '';
    if (num === null || num === undefined || num === '') return '0.00';
    num = Number((+num).toFixed(4));
    let numStr = '' + num;

    // 是否为负数
    let isMinus = false;
    if (/^-/.test(numStr)) {
        isMinus = true;
        numStr = numStr.slice(1);
    }

    if (toFixed2) {
        numStr = Number(numStr).toFixed(2);
    } else {
        let decimal = numStr.split('.')[1];
        let decimalLength = decimal ? decimal.length : 0;
        if (decimalLength > 4) {
            numStr = Number(numStr).toFixed(4);
        } else if (decimalLength > 2) {
            numStr = Number(numStr).toFixed(decimalLength)
        } else {
            numStr = Number(numStr).toFixed(2);
        }
    }

    numStr = (isMinus ? '-' : '') + numStr;
    return numStr;
}

/**
 * 价格位数
 * @param toFixedDigit 保留指定位数，不传时保留2位
 */
export function moneyDigit(money, toFixedDigit = 0) {
    if (money === '' || money === null || money === undefined || isNaN(money)) {
        return toFixedDigit ? (0).toFixed(2) : 0;
    }

    // 是否为负数
    let isMinus = false;
    if (/^\-/.test(money)) {
        isMinus = true;
        money = ('' + money).slice(1);
    }

    if (toFixedDigit) {
        //不能保留5位小数以上
        money = toFixedDigit > 5 ? Number(money).toFixed(5) : money = Number(money).toFixed(toFixedDigit);
        //去除末尾无效的0
        money = parseFloat(money);

        // 如果是整数，小数点后补两个0，若只有一位小数，则再补一个0。
        money = '' + money;
        if (money.includes('.')) {
            let decimal = money.split('.')[1];
            const decimalLength = decimal ? decimal.length : 0;
            if (decimalLength < 2) {
                money = Number(money).toFixed(2);
            }
        } else {
            money = Number(money).toFixed(2);
        }
    } else {
        money = Number(money).toFixed(2);
    }

    money = (isMinus ? '-' : '') + money;
    return money;
}

export function paddingMoney(money, toFixed = false) {
    if (money === '' || money === null || money === undefined || isNaN(money)) {
        return toFixed ? (0).toFixed(2) : 0;
    }

    money = Number(money).toFixed(5);
    // 后面的0抹去
    money = money.match(/\-?[0-9]+\.[0-9]{2}/) + (money).substring(money.length - 3).replace(/0+$/, '');

    // 是否为负数
    let isMinus = false;
    if (/^\-/.test(money)) {
        isMinus = true;
        money = ('' + money).slice(1);
    }

    let integer = money.split('.')[0];
    let decimal = money.split('.')[1];

    let result = '';
    // while (integer.length > 3) {
    //     result = ',' + integer.slice(-3) + result;
    //     integer = integer.slice(0, integer.length - 3);
    // }
    if (integer) {
        result = integer + result;
    }

    const _result = (isMinus ? '-' : '') + `${result}.${decimal}`;

    if (toFixed) {
        return (+_result).toFixed(2);
    }
    return _result;
}

export function filterOwnExpenseRatio(val) {
    if (val === '-') return '';
    if (val === null || val === undefined || val === '') return '100%';
    val = Number(val) * 100;
    return val + '%';
}


/**
 * @desc 格式化治疗项目的展示方式
 * <AUTHOR>
 * @date 2021-12-03 11:41:48
 */

export function formatTreatmentUnit(unit, format = '') {
    const index = TreatmentSystemUnit.findIndex((item) => {
        return item.name === unit;
    });
    return index > -1 ? unit : `${format}${unit}`;
}

export function medicalFeeGrade2PrintStr(medicalFeeGrade) {
    if (!medicalFeeGrade) return '';
    switch (medicalFeeGrade) {
    case 1:
        return '甲';
    case 2:
        return '乙';
    case 3:
        return '丙';
    default:
        return '';
    }
}

export const SelfPayPropEnum = Object.freeze({
    310: '城镇职工',
    390: '城乡居民',
    330: '离休人员',
    99: '其他',
})

export function getSelfPayPropValue(selfPayProp, shebaoCardInfo) {
    if (!shebaoCardInfo) return { key: 99, value: '' };
    const { extend } = shebaoCardInfo || {};
    const { cardInfo } = extend || {};
    const { insuinfo } = cardInfo || {};
    const { psn_type } = insuinfo || {};

    let key = '';

    if (/^11|^12/.test(psn_type)) {
        key = 310;
    } else if (/^13/.test(psn_type)) {
        key = 330;
    } else if (/^14|^15|^16/.test(psn_type)) {
        key = 390;
    } else {
        key = 99;
    }
    return {
        key,
        value: selfPayProp[key],
    };
}

// 读取自付比例，有刷卡读取指定的，没有刷卡 按 城镇职工 - 城乡居民 - 离休人员 - 其他的顺序进行读取
export function getSelfPayPropNumber(goods, shebaoCardInfo) {
    const { selfPayProp } = goods || {};
    if (!selfPayProp) return '';
    let { value } = getSelfPayPropValue(selfPayProp, shebaoCardInfo)
    // 有读卡信息读指定的比例
    if (value) {
        if (typeof value === 'number') {
            value = `${value * 100}%`;
        } else {
            value = '';
        }
        return value;
    }

    const arr = [];
    for (const selfPayPropKey of [310, 390, 330, 99]) {
        if (SelfPayPropEnum.hasOwnProperty(selfPayPropKey)) {
            let val = selfPayProp[selfPayPropKey];
            if (typeof val === 'number') {
                val = `${val * 100}%`;
            } else {
                val = '';
            }
            arr.push({
                label: SelfPayPropEnum[selfPayPropKey],
                val,
                key: selfPayPropKey,
            });
        }
    }
    const result = arr.filter(it => it.val)
    return result[0] ? result[0].val : '';
}

export function isImgUrl(value) {
    if (value && value.indexOf('data:image') > -1) {
        // base64 图片操作
        return true;
    }
    var v = new RegExp('^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$', 'i');
    return v.test(value);
}

export function isToday(val) {
    if (!val) return false;
    // 创建一个表示当前日期的 Date 对象
    const today = new Date();

    // 创建另一个日期对象，这里假设是要检查的日期
    const checkDate = new Date(val);

    // 判断两个日期对象的年、月、日是否相同
    if (
        today.getFullYear() === checkDate.getFullYear() &&
        today.getMonth() === checkDate.getMonth() &&
        today.getDate() === checkDate.getDate()
    ) {
        return true;
    } else {
        return false;
    }

}

/**
 * @desc 格式化时间
 * <AUTHOR>
 * @date 2018/11/26 21:26:30
 */
export function parseTime(time, cFormat) {
    if (time === '永久有效') {
        return time
    }
    if (arguments.length === 0) {
        return null;
    }

    if (arguments.length === 0) {
        return null;
    }

    let date;
    if (time instanceof Date) {
        date = time;
    } else if (typeof time === 'string') {
        date = new Date(time);
    } else if (typeof time === 'number') {
        date = new Date(parseInt(time));
    } else {
        return '';
    }

    const format = cFormat || 'y-m-d h:i:s';
    const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay(),
    };

    return format.replace(/(y|m|d|h|i|s|a)+/g, (result, key) => {
        let value = formatObj[key];
        if (key === 'a') return ['一', '二', '三', '四', '五', '六', '日'][value - 1];
        if (result.length > 0 && value < 10) {
            value = '0' + value;
        }
        return value || 0;
    });
}

// 格式化地址
export function formatAddress(address) {
    if (!address) return '';
    const {
        addressCityName,
        addressDetail,
        addressDistrictName,
        addressProvinceName,
    } = address || {};
    let str = '';
    str += addressProvinceName || '';
    str += addressCityName || '';
    str += addressDistrictName || '';
    str += addressDetail || '';
    return str;
}

// 格式化门诊号显示
export function formatPatientOrderNo(value = '') {
    let srcStr = '00000000';
    if (!value)
        return srcStr;
    return (srcStr + ('' + value)).slice(-8);
}

export function goodsFullName(goods) {
    if (!goods) return '';
    let name;
    switch (goods.type) {
    case 1:
        name = goods.medicineCadn;
        if (goods.name) {
            if (name) {
                name += `（${goods.name}）`;
            } else {
                name = `${goods.name}`;
            }
        }
        return name;
    default:
        return goods.name;
    }
}

export function goodsSpec(goods, showPackageUnit = true) {
    if (!goods) return '';
    let {
        medicineDosageNum,
        medicineDosageUnit,
        componentContentNum,
        componentContentUnit,
        pieceNum,
        pieceUnit,
        packageUnit,
        type,
        subType,
        materialSpec,
        cMSpec,
        displaySpec,
    } = goods;
    if (displaySpec && showPackageUnit) {
        return displaySpec
    }
    if (type === 3 || type === 4 || type === 11) {
        return '';
    }
    medicineDosageNum = Number(medicineDosageNum) || 0;

    // 中药
    if (type === 1 && subType === 2) {
        return cMSpec || '';
    }

    let goodsSep = `${pieceNum || 1}`;

    if (pieceUnit) {
        goodsSep += pieceUnit;
    }
    if (packageUnit && showPackageUnit) {
        goodsSep += '/' + packageUnit;
    }

    // 物资
    if (type === 2 || type === 7) {
        if (materialSpec) {
            goodsSep = materialSpec + '*' + goodsSep;
        }
        return goodsSep;
    }
    // 容量
    const componentContent = componentContentNum && componentContentUnit ? `${componentContentNum}${componentContentUnit}` : '';
    // 成分含量
    const medicineDosage = medicineDosageNum && medicineDosageUnit ? `${medicineDosageNum}${medicineDosageUnit}` : '';
    let spec = '';
    if (componentContent && medicineDosage) {
        spec = `${componentContent}:${medicineDosage}`;
    } else {
        spec = medicineDosage;
    }
    if (spec && goodsSep) {
        return spec + '*' + goodsSep;
    }
    return `${spec || ''}${goodsSep || ''}`;
}

export function goodsSpecV2(goods) {
    if (!goods) return '';
    let {
        // medicineDosage,
        medicineDosageNum,
        medicineDosageUnit,
        pieceNum,
        pieceUnit,
        // packageUnit,
        type,
        subType,
        materialSpec,
        cMSpec,
        displaySpec,
    } = goods;
    if (displaySpec) {
        return displaySpec
    }
    if (type === 3 || type === 4 || type === 11) {
        return '';
    }
    medicineDosageNum = Number(medicineDosageNum) || 0;

    // 中药
    if (type === 1 && subType === 2) {
        return cMSpec || '';
    }

    let goodsSep = `${pieceNum || 1}`;

    if (pieceUnit) {
        goodsSep += pieceUnit;
    }

    // 物资
    if (type === 2 || type === 7) {
        if (materialSpec) {
            goodsSep = materialSpec + '×' + goodsSep;
        }
        return goodsSep;
    }

    let spec = medicineDosageNum && medicineDosageUnit ? `${medicineDosageNum}${medicineDosageUnit}` : '';
    if (spec && goodsSep) {
        return spec + '×' + goodsSep;
    }
    return `${spec || ''}${goodsSep || ''}`;
}


export function digitUppercase(price) {
    if (!price) {
        return
    }
    const fraction = ['角', '分'];
    const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const unit = [
        ['元', '万', '亿'],
        ['', '拾', '佰', '仟'],
    ];
    let isMinus = price < 0;
    let num = Math.abs(price);
    let s = '';
    fraction.forEach((item, index) => {
        s += (digit[Math.floor((num * 10 * (10 ** index)).toFixed(4)) % 10] + item).replace(/零./, '');
    });
    s = s || '整';
    num = Math.floor(num);
    for (let i = 0; i < unit[0].length && num > 0; i += 1) {
        let p = '';
        for (let j = 0; j < unit[1].length && num > 0; j += 1) {
            p = digit[num % 10] + unit[1][j] + p;
            num = Math.floor(num / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }

    s = s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整')
    if (isMinus) {
        s = `负${s}`;
    }
    return s;

}

export function toHan(val) {
    return ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一'][+val];
}

export function toInfusionInjectionName(val) {
    return {
        injections: '注射单',
        external: '外治单',
        infusion: '输液单',
        atomization: '雾化单',
    }[val];
}

function isFloat(n) {
    return Number(n) !== parseInt(n);
}

export function complexCount(stock, toFix = false) {
    return _complexCount(stock, toFix) || 0;
}

export function _complexCount(stock, toFix = false) {
    if (!stock) return '';
    let { type, subType, pieceUnit, pieceCount, packageCount, packageUnit, goods } = stock || {};
    if (packageCount < 0 || pieceCount < 0) {
        return (
            '-' +
            complexCount(
                {
                    type,
                    subType,
                    pieceUnit,
                    pieceCount: Math.abs(pieceCount),
                    packageCount: Math.abs(packageCount),
                    packageUnit,
                    goods,
                },
                toFix,
            )
        );
    }

    if (goods) {
        packageUnit = packageUnit || goods.packageUnit;
        pieceUnit = pieceUnit || goods.pieceUnit;
        type = type || goods.type;
        subType = subType || goods.subType;
    }

    if (type === 1 && subType === 2) {
        pieceUnit = pieceUnit || 'g';
    }

    pieceCount = Number(pieceCount) || 0;
    packageCount = Number(packageCount) || 0;
    if (toFix) {
        // 盘点如果有小数 存在位数很多的小数 则保留三位小数  整数不需要保留后面的 0
        pieceCount = isFloat(pieceCount) ? pieceCount.toFixed(4) : pieceCount;
        packageCount = isFloat(packageCount) ? packageCount.toFixed(4) : packageCount;
    }
    let out = '';
    if (packageCount > 0) {
        out += packageCount + packageUnit;
    }
    if (pieceCount > 0) {
        out += pieceCount + pieceUnit;
    }
    if (packageCount === 0 && pieceCount === 0) {
        if (type === 1 && subType === 2) {
            out += pieceCount + (pieceUnit || '');
        } else {
            out += packageCount + (packageUnit || '');
        }
    }
    return out || 0;
}

export function object2Array(obj) {
    let __result__ = [];
    let __last__ = [];
    for (let p in obj) {
        if (obj.hasOwnProperty(p) && p - 0 !== 0)
            __result__.push(obj[p])
        else
            __last__.push(obj[p])
    }
    return [...__result__, ...__last__]
};

export function textToBase64BarCode(text, options) {
    if (!text) return;
    const canvas = document.createElement('canvas');
    window.JsBarcode && window.JsBarcode(canvas, text, {
        width: 14,
        height: 40,
        displayValue: false,
        margin: 0,
        fontSize: 14,
        ...options,
    });

    return canvas.toDataURL('image/png');
}



// 判断字符串是否是json格式
export function isJSON(str) {
    if (typeof str == 'string') {
        try {
            const obj = JSON.parse(str);
            return typeof obj == 'object' && obj;
        } catch (e) {
            return false;
        }
    }
}

// 诊断 list转str
export function getDiagnosis2Str(list) {
    if (!Array.isArray(list)) return '';
    if (list && list.length === 0) return '';

    return list.join('，');
}

// 格式化 诊断
export function formatDiagnosis2Str(str) {
    if (!str) return '';
    if (isJSON(str)) {
        return getDiagnosis2Str(JSON.parse(str));
    }
    return str;
}

export function obj2arr(obj) {
    let result = [];
    for (let p in obj) {
        if (obj.hasOwnProperty(p)) result.push(obj[p]);
    }
    return result;
}


export function goodsTypeName(goods) {
    if (!goods) return '';
    const { type, subType, cMSpec } = goods;
    // 提前return其他类型的商品
    if (GOODS_TYPE_NAME[type]) {
        return GOODS_TYPE_NAME[type];
    }
    const typeName = GOODS_TYPE_NAME[`${type}-${subType}`];
    if (typeName === '中药') {
        return cMSpec || '';
    }
    return typeName;
}

export function formatGoodsDosageSpec(goods) {
    if (!goods) {
        return '';
    }
    let {
        specType, // 规格类型
        componentContentNum,
        componentContentUnit,
        medicineDosageNum,
        medicineDosageUnit,
        materialSpec,
        type,
    } = goods;
    if (type !== GoodsTypeEnum.MEDICINE) {
        return materialSpec
    }
    // 容量成分
    if (specType === 1) {
        return `${componentContentNum || ''}${componentContentUnit || ''}:${medicineDosageNum || ''}${medicineDosageUnit || ''}`;
    } else {
        return `${medicineDosageNum || ''}${medicineDosageUnit || ''}`;
    }
}

export function handleSpec(good) {
    const { goodsSnapshotVersion, productInfo } = good;

    let goodsInfo = {};
    if (goodsSnapshotVersion) {
        goodsInfo = goodsSnapshotVersion;
    }
    if (productInfo) {
        goodsInfo = productInfo;
    }

    let {
        medicineDosageNum,
        medicineDosageUnit,
        pieceNum,
        pieceUnit,
        packageUnit,
        displaySpec,
    } = goodsInfo;

    if (displaySpec) {
        return displaySpec;
    }

    medicineDosageNum = Number(medicineDosageNum) || 0;
    let goodSep = `${pieceNum || 1}`;
    if (pieceUnit) {
        goodSep += pieceUnit;
    }
    if (packageUnit) {
        goodSep += '/' + packageUnit;
    }
    let spec = medicineDosageNum && medicineDosageUnit ? `${medicineDosageNum}${medicineDosageUnit}` : '';
    if (spec && goodSep) {
        return spec + '*' + goodSep;
    }
    return `${spec || ''}${goodSep || ''}`;
}

export function capitalizeFirstLetter(str) {
    if (typeof str !== 'string') return '';
    return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 计算字符串长度
 * 分别与全角与半角
 * 全角计2 半角计1
 * @param str
 * @param count
 * @returns {{splitLength: number, fullCharacterLength: number}}
 */
export function getLengthWithFullCharacter(str, count = 12) {
    if (typeof str !== 'string') {
        return {
            fullCharacterLength: 0, splitLength: 0,
        };
    }
    let fullCharacterLength = 0, splitLength = 0;
    for (let i = 0; i < str.length; i++) {
        const charCode = str.charCodeAt(i);
        if ((charCode >= 0xFF00 && charCode <= 0xFFEF) || (charCode >= 0x4E00 && charCode <= 0x9FA5)) {
            fullCharacterLength++;
        } else {
            fullCharacterLength += 0.5;
        }
        if (fullCharacterLength <= count) {
            splitLength++;
        }
    }
    return {
        fullCharacterLength, splitLength,
    };
}

export function isStandardUsage(medicine) {
    if (!medicine.usageInfo?.usage) return false;
    const { usage } = medicine.usageInfo;
    for (const usageType in MEDICINE_USAGE) {
        if (MEDICINE_USAGE[usageType].includes(usage)) {
            return true;
        }
    }
    return false;
}

export function isChineseMedicine(goods) {
    if (!goods) return false;
    return goods.productType === GoodsTypeEnum.MEDICINE && goods.productSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine;
}

export function isChineseMedicineV2(goods) {
    return goods.type === 1 && goods.subType === 2;
}

// count是处理过的小单位数量，需要格式化展示
export function formatCount(goods, count, useDismounting = false) {
    let packageCount = 0;
    let pieceCount = 0;
    if (isChineseMedicine(goods)) {
        pieceCount = count;
    } else {
        if (!useDismounting) {
            const x = count % (goods.pieceNum || 1);
            packageCount = ~~(count / (goods.pieceNum || 1));
            pieceCount = Big(x).round(4, Big.roundDown).toNumber();
        } else {
            packageCount = 0;
            pieceCount = count;
        }
    }
    return complexCount({
        ...goods,
        packageCount,
        pieceCount,
    });
}

export function formatDentistry2Text(arr) {
    if (!arr) return '';
    const result = [];
    arr.forEach((item) => {
        if (item.value) {
            let str = '';
            if (item.toothNos && item.toothNos.length) {
                str += `${formatToothNos2Text(item.toothNos)} `;
            }
            if (Array.isArray(item.value)) {
                str += item.value.map((it) => it.name).join('，');
            } else {
                str += item.value;
            }
            result.push(str);
        }
    });
    return result.join('，');
}

/**
 * @description 判断 goods 是否展示规格
 */
export function isShowDisplaySpec(item) {
    const { goodsType } = item;
    const showTypeList = [GoodsTypeEnum.MEDICINE, GoodsTypeEnum.MATERIAL, GoodsTypeEnum.GOODS, GoodsTypeEnum.EYEGLASSES];
    return showTypeList.includes(goodsType);
}


/**
 * @description 计算西药的应发数量(待发+已发+拒发)
 */
export function calcDispensingCount(item, isReturnOrder) {
    // 退药
    if (isReturnOrder) {
        let returnCount = 0, returnDoneCount = 0, rejectReturnCount = 0;
        if (item.renderData.returnCount) {
            const returnCountRes = parseFloat(item.renderData.returnCount);
            returnCount = isNaN(returnCountRes) ? 0 : returnCountRes;
        }
        if (item.renderData.returnDoneCount) {
            const returnDoneCountRes = parseFloat(item.renderData.returnDoneCount);
            returnDoneCount = isNaN(returnDoneCountRes) ? 0 : returnDoneCountRes;
        }
        if (item.renderData.rejectReturnCount) {
            const rejectReturnCountRes = parseFloat(item.renderData.rejectReturnCount);
            rejectReturnCount = isNaN(rejectReturnCountRes) ? 0 : rejectReturnCountRes;
        }
        const res = formatCount(item.productInfo, returnCount + returnDoneCount + rejectReturnCount, item.useDismounting);
        if (parseFloat(res) === 0) {
            return `-`;
        }
        return res;
    }

    // 发药
    let commitCount = 0, pushCount = 0, rejectCount = 0;
    if (item.renderData.commitCount) {
        let commitCountRes = parseFloat(item.renderData.commitCount);
        commitCount = isNaN(commitCountRes) ? 0 : commitCountRes;
    }
    if (item.renderData.pushCount) {
        let pushCountRes = parseFloat(item.renderData.pushCount);
        pushCount = isNaN(pushCountRes) ? 0 : pushCountRes;
    }
    if (item.renderData.rejectCount) {
        let rejectCountRes = parseFloat(item.renderData.rejectCount);
        rejectCount = isNaN(rejectCountRes) ? 0 : rejectCountRes;
    }
    const res = formatCount(item.productInfo, commitCount + pushCount + rejectCount, item.useDismounting);
    if (parseFloat(res) === 0) {
        return `-`;
    }
    return res;
}

/**
 * 计算实发/实退 (已发/已退)
 */
export function calcPushDispensingCount(item, isReturnOrder) {
    // 退药
    if (isReturnOrder) {
        let returnDoneCount = 0;
        if (item.renderData.returnDoneCount) {
            const returnDoneCountRes = parseFloat(item.renderData.returnDoneCount);
            returnDoneCount = isNaN(returnDoneCountRes) ? 0 : returnDoneCountRes;
        }
        const res = formatCount(item.productInfo, returnDoneCount, item.useDismounting);
        if (parseFloat(res) === 0) {
            return `-`;
        }
        return res;
    }

    // 发药
    let pushCount = 0;
    if (item.renderData.pushCount) {
        let pushCountRes = parseFloat(item.renderData.pushCount);
        pushCount = isNaN(pushCountRes) ? 0 : pushCountRes;
    }
    const res = formatCount(item.productInfo, pushCount, item.useDismounting);
    if (parseFloat(res) === 0) {
        return `-`;
    }
    return res;
}

export function getProcessUsageInfo(form) {
    if (form.usageInfo && form.usageInfo.processUsage) {
        // 收费处换了字段，一剂几袋用processBagUnitCountDecimal，老数据用processBagUnitCount
        const processBagUnitCount = form.usageInfo?.processBagUnitCountDecimal || form.usageInfo?.processBagUnitCount;
        const totalProcessCount = form.usageInfo?.totalProcessCount || Math.ceil(form.usageInfo?.doseCount * processBagUnitCount);
        const {
            processUsage, usageType, processRemark,
        } = form.usageInfo || {};
        return '加工：' + `${processUsage}` +
            `${usageType === PROCESS_TYPES_ENUM.DECOCTION ?
                `（${processBagUnitCount}袋/剂，共${totalProcessCount}袋 ${processRemark ? `，${processRemark}` : ''}）` :
                `${processRemark ? `（${processRemark}）` : ''}`} `;
    }
    return '';
}

export function getRequirement(form) {
    if (!form.usageInfo) return '';
    const {
        dailyDosage,
        freq,
        usageLevel,
        usage,
    } = form.usageInfo;

    let usageStr = '';
    if (dailyDosage) usageStr += `${dailyDosage}，`;
    if (freq) usageStr += `${freq}，`;
    if (usageLevel) usageStr += `${usageLevel}`;
    if (usage) usageStr += `${usage}`;

    return usageStr;
}

export function getDispensingFormItemInfo(dispensingFormItem) {
    const dispensingInfo = `共 ${dispensingFormItem.doseCount} 剂，${dispensingFormItem.dispenseItemCount}味`;
    const unitCountInfo = dispensingFormItem.unitCountPerDose ?
        `，单剂${dispensingFormItem.unitCountPerDose}g，总计${dispensingFormItem.totalCount}g` :
        '';
    return `${dispensingInfo}${unitCountInfo}`;
}

/**
 * 对药品进行分组
 */
export function groupDispensingList(dispensingList) {
    dispensingList.forEach((form, formIndex) => {
        if (formIndex === 0) {
            if (dispensingList.length > 1) {
                if (form.groupId === dispensingList[formIndex + 1].groupId) {
                    form.isGroupStart = true;
                }
            }
        } else if (formIndex === dispensingList.length - 1) {
            if (dispensingList.length > 1) {
                if (form.groupId === dispensingList[formIndex - 1].groupId) {
                    form.isGroupEnd = true;
                }
            }
        } else {
            if (form.groupId === dispensingList[formIndex - 1].groupId && form.groupId === dispensingList[formIndex + 1].groupId) {
                form.isGroupProcess = true;
            } else if (form.groupId === dispensingList[formIndex - 1].groupId && form.groupId !== dispensingList[formIndex + 1].groupId) {
                form.isGroupEnd = true;
            } else if (form.groupId !== dispensingList[formIndex - 1].groupId && form.groupId === dispensingList[formIndex + 1].groupId) {
                form.isGroupStart = true;
            }
        }
    });
    return dispensingList;
}

/**
 * 将非中药且 id 、单次剂量、发药量单位都相同的 goods 进行合并
 * @param dispensingList
 * @param {boolean} showDispenseTime
 * @return {*[]}
 */
export function mergeSameMedicine(dispensingList, showDispenseTime = false) {
    const sameIdMedicineMap = new Map();
    const chineseDispensingList = [];
    dispensingList.forEach((item) => {
        if (item.isChineseForm) {
            chineseDispensingList.push(item);
            return;
        }
        const cacheItem = clone(item);
        if (sameIdMedicineMap.has(cacheItem.productId)) {
            sameIdMedicineMap.get(cacheItem.productId).push(cacheItem);
        } else {
            sameIdMedicineMap.set(cacheItem.productId, [cacheItem]);
        }
    });

    let resDispensingList = [];
    sameIdMedicineMap.forEach((groupItems) => {
        const cacheGroupItems = clone(groupItems);
        const cacheMergeDispensingList = [];
        cacheGroupItems.forEach((cacheGroupItem, index) => {
            if (index === 0) {
                cacheMergeDispensingList.push(cacheGroupItem);
            } else {
                const cacheUnit = cacheGroupItem.unit;
                const cacheStatus = cacheGroupItem.status;
                const cacheLastModified = cacheGroupItem.lastModified;
                const cacheRenderData = cacheGroupItem.renderData;
                const cacheDoseCount = cacheRenderData.doseCount;
                const cacheDosageUnit = cacheRenderData.dosageUnit;
                let flag = false;
                for (let i = 0; i < cacheMergeDispensingList.length; i++) {
                    const cacheMergeUnit = cacheMergeDispensingList[i].unit;
                    const cacheMergeStatus = cacheMergeDispensingList[i].status;
                    const cacheMergeLastModified = cacheMergeDispensingList[i].lastModified;
                    const cacheMergeRenderData = cacheMergeDispensingList[i].renderData;
                    const cacheMergeDoseCount = cacheMergeRenderData.doseCount;
                    const cacheMergeDosageUnit = cacheMergeRenderData.dosageUnit;
                    if (
                        cacheDoseCount === cacheMergeDoseCount &&
                        cacheDosageUnit === cacheMergeDosageUnit &&
                        cacheUnit === cacheMergeUnit
                    ) {
                        if (
                            showDispenseTime &&
                            (
                                cacheStatus === DispenseOrderFormItemTypeEnum.UNDISPENSED ||
                                cacheStatus === DispenseOrderFormItemTypeEnum.DISPENSED ||
                                cacheMergeStatus === DispenseOrderFormItemTypeEnum.UNDISPENSED ||
                                cacheMergeStatus === DispenseOrderFormItemTypeEnum.DISPENSED
                            ) &&
                            (
                                cacheStatus !== cacheMergeStatus ||
                                cacheLastModified !== cacheMergeLastModified
                            )
                        ) {
                            continue;
                        }
                        cacheMergeRenderData.commitCount = parseFloat(cacheMergeRenderData.commitCount || 0) + parseFloat(cacheRenderData.commitCount || 0);
                        cacheMergeRenderData.pushCount = parseFloat(cacheMergeRenderData.pushCount || 0) + parseFloat(cacheRenderData.pushCount || 0);
                        cacheMergeRenderData.rejectCount = parseFloat(cacheMergeRenderData.rejectCount || 0) + parseFloat(cacheRenderData.rejectCount || 0);
                        cacheMergeRenderData.returnCount = parseFloat(cacheMergeRenderData.returnCount || 0) + parseFloat(cacheRenderData.returnCount || 0);
                        cacheMergeRenderData.returnDoneCount = parseFloat(cacheMergeRenderData.returnDoneCount || 0) + parseFloat(cacheRenderData.returnDoneCount || 0);
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    cacheMergeDispensingList.push(cacheGroupItem);
                }
            }
        });
        resDispensingList = resDispensingList.concat(cacheMergeDispensingList);
    });
    resDispensingList = resDispensingList.concat(chineseDispensingList);
    return resDispensingList;
}


/**
 * 格式化检查诊断意见
 * @date 2024/2/5 - 15:54:43
 * <AUTHOR>
 *
 * @export
 */
export function formatInspectDiagnosisAdvice(diagnosisEntryItems) {
    return (diagnosisEntryItems || [])
        .filter((o) => !!o.name)
        .map((o) => `${o.name}`)
        .join('\n');
}

export function isDisplaySocialCode(chargeItem, isFeeCompose, productInfoRuleConfig) {
    // 如果开启了医嘱收费项配置,目前只有医院管家开启,直接展示医保码和等级
    if (isFeeCompose) {
        return chargeItem.goodsFeeType !== GoodsFeeType.FEE_PARENT;
    }
    // 展示收费项目时，诊疗项目不展示医保码和等级
    return !(!productInfoRuleConfig && (chargeItem.productType === 3 || chargeItem.productType === 4 || chargeItem.productType === 19));

}

export function calcTableRowNumber(splitHeight) {
    // 如果未获取到纸张高度,则默认展示行数
    if (splitHeight === 0) return 7;
    if (splitHeight <= 93) return 0;
    if (splitHeight <= 140) return 3;
    if (splitHeight <= 190) return 4;
    return 7;
}

export function checkInspectResultIsAbnormal(item) {
    const AbnormalTipTypeEnum = {
        text: 10,
        arrowUp: 20,
        arrowDown: 30,
    };

    const MAX = 99999;
    const MIN = 0;

    const currentResult = item.value;

    const currentRefList = item.refDetails || [];

    if (['', undefined, null].includes(currentResult)) {
        return '';
    }

    const getMax = (v) => {
        let res = ['', undefined, null].includes(v) ? MAX : v;
        res = Number(res);
        return isNaN(res) ? MAX : res;
    };

    const getMin = (v) => {
        let res = ['', undefined, null].includes(v) ? MIN : v;
        res = Number(res);
        return isNaN(res) ? MIN : res;
    };

    const res = currentRefList.find((r) => {
        const {
            ref,
            refValueMaxExclusive = 0,
            refValueMinExclusive = 0,
        } = r;

        if (!ref) {
            return false;
        }

        const curRef = {
            min: getMin(ref.min),
            max: getMax(ref.max),
        };

        const curResult = Number(currentResult);

        let flag;
        if (refValueMinExclusive) {
            flag = curResult > curRef.min;
        } else {
            flag = curResult >= curRef.min;
        }

        if (refValueMaxExclusive) {
            flag = flag && curResult < curRef.max;
        } else {
            flag = flag && curResult <= curRef.max;
        }

        return flag;
    }) || {};

    const { abnormality = {} } = res;
    const type = abnormality.style?.type;

    return {
        isText: type === AbnormalTipTypeEnum.text,
        isArrowUp: type === AbnormalTipTypeEnum.arrowUp,
        isArrowDown: type === AbnormalTipTypeEnum.arrowDown,
        style: {
            color: abnormality.style?.color,
        },
        content: abnormality.text || '',
        isAbnormal: res.abnormality !== undefined,
    };
}

export function checkExaminationResultIsAbnormal(item, result) {
    const ABNORMAL_FLAG = {
        normal: 'N',
        height: 'H',
        low: 'L',
        doubleHeight: 'HH',
        doubleLow: 'LL',
        abnormal: 'A',
    };

    // 优先使用传入的结果
    const currentResult = result || calNature(item);

    const { abnormalFlag } = item;

    const isUseAbnormalFlag = ![undefined, null, ''].includes(abnormalFlag);

    if (isUseAbnormalFlag) {
        return {
            isNormal: abnormalFlag === ABNORMAL_FLAG.normal,
            isUp: [ABNORMAL_FLAG.height, ABNORMAL_FLAG.doubleHeight].includes(abnormalFlag),
            isDown: [ABNORMAL_FLAG.low, ABNORMAL_FLAG.doubleLow].includes(abnormalFlag),
            upCount: abnormalFlag === ABNORMAL_FLAG.doubleHeight ? 2 : 1,
            lowCount: abnormalFlag === ABNORMAL_FLAG.doubleLow ? 2 : 1,
            isBad: abnormalFlag === ABNORMAL_FLAG.abnormal,
        };
    }

    return {
        isNormal: currentResult === EXAMINATION_RESULT_ENUM.NONE,
        isUp: currentResult === EXAMINATION_RESULT_ENUM.UP,
        isDown: currentResult === EXAMINATION_RESULT_ENUM.DOWN,
        isBad: currentResult === EXAMINATION_RESULT_ENUM.BAD,
        upCount: 1,
        lowCount: 1,
    };
}

/**
 * 把 1 位床号格式化为 2 位
 * @param bed
 * @return {string}
 */
export function hospitalBedFormat(bed) {
    if (!bed) return '';
    bed = `${bed}`;
    if (bed.length > 1) return bed;
    return bed.padStart(2, '0');
}


/**
 * Generates the rendered organ name for display.
 *
 * @param {object} param0 - Destructured object with organName, printOrganName, and printOrganSubName properties
 * @return {object} Object with renderFirstLineName and renderSecondLineName properties representing the rendered organ name
 */
export function getRenderOrganName({
    organName,
    printOrganName,
    printOrganSubName,
}) {
    let renderFirstLineName = '', renderSecondLineName = '';

    if (printOrganName) {
        renderFirstLineName = printOrganName;
        renderSecondLineName = printOrganSubName;
    } else {
        const {
            fullCharacterLength, splitLength,
        } = getLengthWithFullCharacter(organName, TITLE_MAX_LENGTH);
        if (fullCharacterLength > TITLE_MAX_LENGTH) {
            renderFirstLineName = organName.slice(0, splitLength);
            renderSecondLineName = organName.slice(splitLength);
        } else {
            renderFirstLineName = organName;
        }
    }

    return {
        renderFirstLineName,
        renderSecondLineName,
    }
}

export function getPatientMaterialName(marital) {
    let status = MARTIAL_LABEL_ENUM.find(item => {
        return item.value === marital;
    });
    return (status && status.label) || '';
}

export function convertFiledList(list, spanMap = [10, 8, 6]) {
    return list.map((item, idx) => {
        const sortNum = idx % 3;

        item.span = spanMap[sortNum];
        if (sortNum === 2) {
            item.style = {
                ...(item.style || {}),
                justifyContent: 'flex-end',
            };
        }

        return item;
    })
}

function setResultItemsPrintable(result, items = []) {
    if (result.items?.length) {
        result.items.forEach(item => {
            item.disablePrint = items.find(it => it.id === item.id)?.disablePrint || 0;
        })

        return;
    }

    if (result.children?.length) {
        result.children.forEach(child => {
            setResultItemsPrintable(child, items);
        })
    }
}

// 合并眼科检查指标的打印选项
export function updateEyeInspectItemsPrintable(vm) {
    if (vm.itemsValue) {
        return vm.itemsValue;
    }

    const reportList = vm.printData.reportDetailList || [];
    const goodsDetailList = (vm.printData.goodsDetailList || []).map(detail => {
        return {
            combineType: detail.examGoodsInfo?.combineType,
            goodsId: detail.examGoodsInfo?.id,
            children: detail.examGoodsInfo?.childrenViews || [],
            items: detail.items || [],
        }
    });

    reportList.forEach(report => {
        const goodsId = report.productIds?.[0];
        if (goodsId) {
            const goodsDetail = goodsDetailList.find(g => g.goodsId === goodsId);
            if (goodsDetail) {
                (report.examinationItemsValue || []).forEach(singleGoodsResult => {
                    if (goodsDetail.combineType === 1) {
                        // 组合项 - 设置单项的打印选项
                        const singleGoods = (goodsDetail.children || []).find(c => c.id === singleGoodsResult.goodsId) || {};
                        singleGoodsResult.goodsDisableComposePrint = singleGoods.disableComposePrint || 0;
                        setResultItemsPrintable(singleGoodsResult, singleGoods.items || [])
                    } else {
                        // 单项 - 只有一个结果
                        const curItems = goodsDetail.items.reduce((res, item) => {
                            return res.concat(item.ids.map(id => ({
                                ...item,
                                id,
                            })))
                        }, [])
                        setResultItemsPrintable(singleGoodsResult, curItems)
                    }
                })
            }
        }
    })

    vm.itemsValue = reportList.reduce((res, item) => {
        const itemsValue = item.examinationItemsValue || [];

        return res.concat(itemsValue);
    }, [])

    return vm.itemsValue;
}



/**
 * @desc 获取单据类型
 * @param {Object} item 结算项目
 * @param {number} item.type 1 出库单  0 入库单
 * @return {string}
 */
export function formatOrderType(item) {
    const map = {
        0: '入库',
        1: '出库',
        2: '入库修正',
        3: '出库修正',
    };

    return map[item.type];
}

export function formatAmount(type, amount) {
    return type ? Math.abs(amount) * -1 : Math.abs(amount);
}

export function deepClone(value, map = new WeakMap()) {
    // 检查value是否是基本数据类型
    if (value === null || typeof value !== 'object') {
        return value;
    }

    // 检查value是否是日期对象
    if (value instanceof Date) {
        return new Date(value);
    }

    // 检查value是否是正则表达式对象
    if (value instanceof RegExp) {
        return new RegExp(value);
    }

    // 检查value是否是函数
    if (typeof value === 'function') {
        return value; // 函数通常不拷贝
    }

    // 检查value是否是Map
    if (value instanceof Map) {
        const clonedMap = new Map();
        value.forEach((v, k) => clonedMap.set(k, deepClone(v, map)));
        return clonedMap;
    }

    // 检查value是否是Set
    if (value instanceof Set) {
        const clonedSet = new Set();
        value.forEach(v => clonedSet.add(deepClone(v, map)));
        return clonedSet;
    }

    // 检查value是否是对象，并且是否已经拷贝过
    if (map.has(value)) {
        return map.get(value);
    }

    // 检查value是否是数组
    if (Array.isArray(value)) {
        const clonedArray = [];
        map.set(value, clonedArray);
        value.forEach((item, index) => {
            clonedArray[index] = deepClone(item, map);
        });
        return clonedArray;
    }

    // 处理对象
    const clonedObj = Object.create(Object.getPrototypeOf(value));
    map.set(value, clonedObj);

    // 遍历对象的所有可枚举属性
    for (const key in value) {
        if (value.hasOwnProperty(key)) {
            clonedObj[key] = deepClone(value[key], map);
        }
    }

    // 拷贝对象的Symbol属性
    const symbols = Object.getOwnPropertySymbols(value);
    symbols.forEach(sym => {
        if (value.propertyIsEnumerable(sym)) {
            clonedObj[sym] = deepClone(value[sym], map);
        }
    });

    return clonedObj;
}

/**
 * 将字符串按照 br 换行符拆分成数组
 * @param {string} str
 * @return {Array<string>}
 */
export function splitByBr(str) {
    let res = str.split(/<br\s*\/?>/i);
    res = res.map((it) => it.trim()).filter((it) => !!it);
    return res;
}

export function formatAstInfo(formItem) {
    let str = '';
    str += `${formatAst(formItem.ast)}`;
    if (formItem.ast === 1) {
        str += `(${formatAstResult(formItem.astResult)})`;
    }
    return str;
}

/**
 * 空
 * @returns {Boolean}
 */
export function isNull(val) {
    return val === null || val === undefined || val === '';
}

/**
 * 非空
 * @return {boolean}
 */
export function isNotNull(val) {
    return !isNull(val);
}

export function getDaysByAdvice(goodsId, otherInfo) {
    if (!goodsId || !otherInfo) return '';
    const adviceInfo = otherInfo[goodsId];
    return adviceInfo.days;
}

/**
 * @desc 连锁总部
 */
export function isChainAdminClinic(clinic) {
    const {
        nodeType,
        clinicType,
        viewMode,
    } = clinic || {};
    return !viewMode &&
        (nodeType === CLINIC_NODE_TYPE.CHAIN_ADMIN ||
            clinicType === CLINIC_NODE_TYPE.CHAIN_ADMIN);
}

/**
 * @desc 转换诊所名称
 */
export function formatClinicName(clinic) {
    if (!clinic) return '';
    if (isChainAdminClinic(clinic)) {
        return '总部';
    }
    return clinic.shortName || clinic.name || '';
}


export function isPDF(url) {
    if (typeof url !== 'string') return false;
    return new URL(url).pathname.toLowerCase().endsWith('.pdf');
}
export function formatDispenseTime(item, isReturnOrder) {
    const { status, lastModified } = item;
    if (isReturnOrder && status === DispenseOrderFormItemTypeEnum.UNDISPENSED) {
        return parseTime(lastModified, 'm-d h:i');
    }
    if (!isReturnOrder && status === DispenseOrderFormItemTypeEnum.DISPENSED) {
        return parseTime(lastModified, 'm-d h:i');
    }
    return '-';
}

export function splitDispenseTime(item, isReturnOrder) {
    const time = formatDispenseTime(item, isReturnOrder);
    return time.split(' ');
}


/**
 * 加法函数
 * <AUTHOR>
 * @date 2022-05-11
 * @returns {Number}
 */
export const add = (...args) => {
    if (args.length === 0) {
        return 0
    } else if (args.length === 1) {
        return args[0]
    } else if (args.length === 2) {
        let [a, b] = args
        if (!a) a = 0
        if (!b) b = 0
        a += ''
        b += ''
        let al = a.split('.')[1]
        let bl = b.split('.')[1]
        al = al ? al.length : 0
        bl = bl ? bl.length : 0
        let c = Math.abs(al - bl)
        let m = Math.pow(10, Math.max(al, bl))
        if (c > 0) {
            var cm = Math.pow(10, c);
            if (al > bl) {
                a = Number(a.replace('.', ''));
                b = Number(b.replace('.', '')) * cm;
            } else {
                a = Number(a.replace('.', '')) * cm;
                b = Number(b.replace('.', ''));
            }
        } else {
            a = Number(a.replace('.', ''));
            b = Number(b.replace('.', ''));
        }
        return (a + b) / m;
    } else {
        return args.reduce((a, b) => add(a, b))
    }
}

/**
 * 减法函数
 * <AUTHOR>
 * @date 2022-05-11
 * @returns {Number}
 */
export const red = (...args) => {
    if (args.length === 0) {
        return 0
    } else if (args.length === 1) {
        return args[0]
    } else if (args.length === 2) {
        let [a, b] = args
        if (!a) a = 0
        if (!b) b = 0
        a += ''
        b += ''
        return add(a, -b)
    } else {
        return args.reduce((a, b) => red(a, b))
    }
}

/**
 * 获取身份证件类型的简化字符串
 * @param {string} idCardType - 原始身份证件类型
 * @returns {string} 简化后的身份证件类型字符串
 * @description
 * 根据输入的身份证件类型返回一个简化或更易读的字符串。
 * 如果输入为空，默认返回"身份证"。
 * 对于特定的证件类型，返回其简称或通用名称。
 * 其他情况下返回原始输入。
 */
export const getIdCardTypeStr = (idCardType) => {
    if (!idCardType) return '身份证';
    if (idCardType === '港澳台居住证') return '居住证';
    if (idCardType === '外国人永久居留证') return '外国人永居证';
    if (idCardType === '港澳居民来往内地通行证') return '回乡证';
    if (idCardType === '台湾居民来往内地通行证') return '台胞证';
    return idCardType;
}

/**
 * 获取 forms 的价格合计
 * @param {*[]} forms forms数组
 * @return {number} 合计
 */
export const getTotalPriceByForms = (forms) => {
    return (forms || []).reduce((sum, form) => sum + (form.totalPrice || 0), 0);
}


export function displayMedicalFeeGradeText(formItem, showGrade = true, showRadio = true) {
    let str = '';
    if (showGrade) {
        if (formItem.medicalFeeGrade && formItem.medicalFeeGrade !== MedicalFeeGradeEnum.D) {
            str += medicalFeeGrade2PrintStr(formItem.medicalFeeGrade);
        } else if (formItem.productInfo && formItem.productInfo.medicalFeeGrade && formItem.productInfo.medicalFeeGrade !== MedicalFeeGradeEnum.D) {
            str += medicalFeeGrade2PrintStr(formItem.productInfo.medicalFeeGrade);
        }
        if (showRadio && formItem.ownExpenseRatio) {
            if (str) {
                str += ' ';
            }
            str += filterOwnExpenseRatio(formItem.ownExpenseRatio);
        }
    }
    if (str) {
        str = `[${str}]`;
    }
    return str;
}

/**
 * 将毫米转换为像素
 * @param {number} mm - 毫米值
 * @param {number} dpi - 每英寸像素数（可选，默认为96）
 * @returns {number} - 转换后的像素值
 */
export function mmToPx(mm, dpi = 96) {
    // 1英寸 = 25.4毫米
    const INCH_TO_MM = 25.4;

    // 毫米转像素公式: px = (mm / 25.4) * dpi
    return (mm / INCH_TO_MM) * dpi;
}

/**
 * 将 canvas 元素转换为 img 元素，并保持尺寸一致
 * @param {HTMLCanvasElement} canvas - 要转换的 canvas 元素
 * @param {number | string} width 转换后的 img 的宽度，不传使用 canvas 的宽度
 * @param {number | string} height 转换后的 img 的高度，不传使用 canvas 的高度
 * @returns {HTMLImageElement} - 转换后的 img 元素
 */
export function canvasToImage(canvas, width) {
    // 1. 获取 canvas 的数据 URL
    const dataURL = canvas.toDataURL('image/png');

    // 2. 创建新的 img 元素
    const img = new Image();

    const canvasRadio = canvas.width / canvas.height;

    const imgWidth = parseFloat(width);

    // 3. 设置 img 的尺寸与 canvas 一致
    img.width = Number.isNaN(imgWidth) ? canvas.width : mmToPx(imgWidth);
    img.height = Number.isNaN(imgWidth) ? canvas.height : mmToPx(imgWidth / canvasRadio);

    // 4. 设置 img 的样式，确保尺寸精确匹配
    img.style.width = `${Number.isNaN(imgWidth) ? canvas.width : mmToPx(imgWidth)}px`;
    img.style.height = `${Number.isNaN(imgWidth) ? canvas.height : mmToPx(imgWidth / canvasRadio)}px`;

    // 5. 设置图像源为 canvas 的数据 URL
    img.src = dataURL;

    return img;
}
