import clone from "./clone.js";

/**
 * @desc 按照 count 拆分数组
 * <AUTHOR>
 * @date 2022-01-24 17:03:18
 * @params
 * @return
 */
export function splitChargeFormItems(formItems, pageCount, firstPageCount) {
    const page = [];
    const cacheFormItems = clone(formItems);
    let pageIndex = 0;
    while(cacheFormItems && cacheFormItems.length) {
        pageIndex++;
        let endIndex = 0;
        if(pageIndex === 1 && typeof firstPageCount === 'number') {
            endIndex = cacheFormItems.length > firstPageCount ? firstPageCount : cacheFormItems.length;
        } else {
            endIndex = cacheFormItems.length > pageCount ? pageCount : cacheFormItems.length;
        }
        page.push({
            formItems: cacheFormItems.splice(0, endIndex),
            pageIndex,
        });
    }
    return page;
}