// 诊所类型
export const CLINIC_TYPE = {
    NORMAL: 0, // 普通诊所
    DENTISTRY: 1, // 牙科诊所
    OPHTHALMOLOGY: 2, // 眼科诊所
    HOSPITAL: 100, // 医院管家
};
export const MedicalRecordTypeEnum = Object.freeze({
    // 西医格式
    WESTERN: 0,
    // 中医格式
    CHINESE: 1,
    // 口腔格式
    ORAL: 2,
    // 眼科格式
    OPHTHALMOLOGY: 3,
});

export const ChargeStatusEnum = Object.freeze({
    RETAIL: -1, // 零售收费创建
    UN_CHARGE: 0, // 待收
    PART_CHARGED: 1, // 部分收费
    CHARGED: 2, // 已收
    PART_REFUND: 3, // 部分退费
    REFUND: 4, // 已退费
    CLOSED: 999, // 前端状态，标识收费单已经被关闭，由收费详情中的 isClosed 决定
});

export const ChargeFormStatusEnum = Object.freeze({
    UNCHARGED: 0, // 未收费
    CHARGED: 1, // 已收费
    REFUNDED: 2, // 已退费
});

export const ChargeItemStatusEnum = Object.freeze({
    UN_CHARGE: 0, // 未收费
    CHARGED: 1, // 已收费
    REFUND: 2, // 已退费
    RETURNED: 3, // 已退单
    PART_REFUND: 4, // 套餐部分退费
});

export const ChargeStatusPrepEndEnum = Object.freeze({
    [ChargeFormStatusEnum.UNCHARGED]: '<span style="font-size: 14px; color: #FF9933;">未收费</span>',
    [ChargeFormStatusEnum.CHARGED]: '<span style="font-size: 14px; color: #0EBA52;">已收费</span>',
    [ChargeFormStatusEnum.REFUNDED]: '<span style="font-size: 14px; color: #FF3366;">已退费</span>',
})


export const PrintFormTypeEnum = Object.freeze({
    NONE: 0,
    // 挂号费
    REGISTRATION: 1,
    // 检验检查
    EXAMINATION: 2,
    // 治疗理疗
    TREATMENT: 3,
    // 西药
    PRESCRIPTION_WESTERN: 4,
    // 输注
    PRESCRIPTION_INFUSION: 5,
    // 中药
    PRESCRIPTION_CHINESE: 6,
    // 门诊收费 新增加的收费项目单独放在一个form 7
    ADDITIONAL_FORM: 7,
    // 门诊收费新增商品 8 // 商品都在这个form里面
    ADDITIONAL_SALE_PRODUCT_FORM: 8,
    //材料
    MATERIAL: 9,
    //赠品
    GIFT_PRODUCT: 10,
    //套餐
    COMPOSE_PRODUCT: 11,
    //在线问诊
    ONLINE_CONSULTATION: 12,
    //快递费用
    EXPRESS_DELIVERY: 13,
    //加工费
    PROCESS: 14,
    //空中药房
    AIR_PHARMACY: 15,
    //外治处方
    PRESCRIPTION_EXTERNAL: 16,
    //家庭医生签约
    FAMILY_DOCTOR_SIGN: 17,
    // 卡项开通费用
    PROMOTION_CARD_OPEN: 18,
    // 卡项充值费用
    PROMOTION_CARD_RECHARGE: 19,
    // 会员卡充值
    MEMBER_CARD_RECHARGE: 20,
    // 其他费用
    OTHER: 21,
    // 眼镜
    GLASSES: 22,
    // 护理
    NURSING: 23,
    // 配镜处方
    PRESCRIPTION_GLASSES: 24,
    // 单品优惠赠品 25
    SINGLE_PROMOTION_GIFT: 25,
    // 手术
    SURGERY: 26,
});

// 费用类型
export const GoodsFeeType = Object.freeze({
    FEE_OWN: 0, // 直接被开出来的费用项
    FEE_PARENT: 1, // 医嘱
    FEE_CHILD: 2, // 医嘱下的费用项
});

export const GoodsTypeEnum = Object.freeze({
    // 药品 1
    MEDICINE: 1,
    // 物资 2
    MATERIAL: 2,
    // 检查检验 3
    EXAMINATION: 3,
    // 治疗理疗 4
    TREATMENT: 4,
    // 挂号费 5
    REGISTRATION: 5,
    // 商品 7
    GOODS: 7,
    // 套餐 11
    COMPOSE: 11,
    // 配送费 13
    EXPRESS_DELIVERY: 13,
    // 加工费 14
    DECOCTION: 14,
    // 辅料费 15
    INGREDIENT: 15,
    // 家庭医生签约费 16
    FAMILY_DOCTOR_SIGN: 16,
    // 其他费用 19
    OTHER: 19,
    // 加工费 20
    PROCESSING: 20,
    // 护理费
    NURSE: 21,
    // 出院转院
    LEAVE_HOSPITAL: 23,
    // 眼镜
    EYEGLASSES: 24,
    // 会诊
    CONSULTATION: 25,
    // 采样组
    SAMPLE: 26,
    // 体检
    PHYSICAL_EXAMINATION: 27,
    // 体检套餐
    PHYSICAL_EXAMINATION_COMPOSE: 28,
    // 手术
    SURGERY: 29,
});


// key 为对应 goods Type
export const GoodsSubTypeEnum = Object.freeze({
    [GoodsTypeEnum.MEDICINE]: {
        // 西药
        WesternMedicine: 1,
        // 中药
        ChineseMedicine: 2,
        // 中成药
        CPM: 3,
    },

    [GoodsTypeEnum.MATERIAL]: {
        // 医疗器械
        MedicalMaterials: 1,
        // 后勤材料
        LogisticalMaterials: 2,
        // 固定资产
        FixedAssets: 3,
        // 消毒用品
        Disinfectant: 4,
    },

    [GoodsTypeEnum.EXAMINATION]: {
        // 检验
        Inspect: 1,
        // 检查
        Test: 2,
    },

    [GoodsTypeEnum.TREATMENT]: {
        // 治疗
        Treatment: 1,
        // 理疗
        Physiotherapy: 2,
    },

    [GoodsTypeEnum.GOODS]: {
        // 自制成品
        ManufacturedGoods: 1,
        // 保健药品
        HealthCareMedicine: 2,
        // 保健食品
        HealthFoods: 3,
        // 其他商品
        Other: 4,
        // 化妆品
        Cosmetic: 5,
    },

    [GoodsTypeEnum.OTHER]: {
        // 默认
        NORMAL: 0,
    },

    [GoodsTypeEnum.PROCESSING]: {
        // 义齿加工
        DENTURE: 1,
    },
    [GoodsTypeEnum.LEAVE_HOSPITAL]: {
        // 出院
        LEAVE: 1,
        // 转院
        TRANS: 2,
    },
    [GoodsTypeEnum.NURSE]: {
        // 护理费
        NURSE: 1,
        // 护理等级
        NURSE_LEVEL: 2,
    },
    [GoodsTypeEnum.EYEGLASSES]: {
        // 镜片
        LENS: 1,
        // 镜架
        BRACKET: 2,
        // 角膜塑性镜
        OK_LENS: 3,
        // 软性亲水镜
        SOFT_LENS: 4,
        // 硬性透氧镜
        RIGID_LENS: 5,
        // 太阳镜
        SUNGLASSES: 6,
    },
    // 会诊
    [GoodsTypeEnum.CONSULTATION]: {
        CONSULTATION: 1,
    },
    // 采样组
    [GoodsTypeEnum.SAMPLE]: {
        SAMPLE: 1,
    },
    // 体检
    [GoodsTypeEnum.PHYSICAL_EXAMINATION]: {
        // 检验
        EXAMINATION: 1,
        // 检查
        INSPECT: 2,
    },
    // 体检套餐
    [GoodsTypeEnum.PHYSICAL_EXAMINATION_COMPOSE]: {
        // 普通体检
        NORMAL: 1,
        // 公卫体检
        PUBLIC_HEALTH: 2,
        // 职业病体检
        INDUSTRIAL_DISEASE: 3,
        // 大学生体检
        COLLEGE_STUDENT: 4,
        // 驾照体检
        DRIVING_LICENSE: 5,
    },
    // 手术
    [GoodsTypeEnum.SURGERY]: {
        // 手术医嘱
        SURGERY: 1,
        // 术后医嘱
        AFTER_SURGERY: 2,
    },
});

export const GOODS_TYPE_NAME = Object.freeze({
    [[GoodsTypeEnum.MEDICINE, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine].join('-')]: '西药',
    [[GoodsTypeEnum.MEDICINE, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine].join('-')]: '中药',
    [[GoodsTypeEnum.MEDICINE, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].join('-')]: '中成药',
    [[GoodsTypeEnum.MATERIAL, GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials].join('-')]: '医疗器械',
    [[GoodsTypeEnum.MATERIAL, GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].LogisticalMaterials].join('-')]: '后勤材料',
    [[GoodsTypeEnum.MATERIAL, GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].FixedAssets].join('-')]: '固定资产',
    [[GoodsTypeEnum.MATERIAL, GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].Disinfectant].join('-')]: '消毒用品',
    [[GoodsTypeEnum.GOODS, GoodsSubTypeEnum[GoodsTypeEnum.GOODS].ManufacturedGoods].join('-')]: '自制成品',
    [[GoodsTypeEnum.GOODS, GoodsSubTypeEnum[GoodsTypeEnum.GOODS].HealthCareMedicine].join('-')]: '保健药品',
    [[GoodsTypeEnum.GOODS, GoodsSubTypeEnum[GoodsTypeEnum.GOODS].HealthFoods].join('-')]: '保健食品',
    [[GoodsTypeEnum.GOODS, GoodsSubTypeEnum[GoodsTypeEnum.GOODS].Other].join('-')]: '其他商品',
    [[GoodsTypeEnum.GOODS, GoodsSubTypeEnum[GoodsTypeEnum.GOODS].Cosmetic].join('-')]: '化妆品',
    [[GoodsTypeEnum.EXAMINATION, GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test].join('-')]: '检查',
    [[GoodsTypeEnum.EXAMINATION, GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect].join('-')]: '检验',
    [[GoodsTypeEnum.TREATMENT, GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Treatment].join('-')]: '治疗',
    [[GoodsTypeEnum.TREATMENT, GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy].join('-')]: '理疗',
    [[GoodsTypeEnum.EYEGLASSES, GoodsSubTypeEnum[GoodsTypeEnum.EYEGLASSES].LENS].join('-')]: '镜片',
    [[GoodsTypeEnum.EYEGLASSES, GoodsSubTypeEnum[GoodsTypeEnum.EYEGLASSES].BRACKET].join('-')]: '镜架',
    [[GoodsTypeEnum.EYEGLASSES, GoodsSubTypeEnum[GoodsTypeEnum.EYEGLASSES].OK_LENS].join('-')]: '角膜塑形镜',
    [[GoodsTypeEnum.EYEGLASSES, GoodsSubTypeEnum[GoodsTypeEnum.EYEGLASSES].SOFT_LENS].join('-')]: '软性亲水镜',
    [[GoodsTypeEnum.EYEGLASSES, GoodsSubTypeEnum[GoodsTypeEnum.EYEGLASSES].RIGID_LENS].join('-')]: '硬性透氧镜',
    [[GoodsTypeEnum.EYEGLASSES, GoodsSubTypeEnum[GoodsTypeEnum.EYEGLASSES].SUNGLASSES].join('-')]: '太阳镜',
    [[GoodsTypeEnum.NURSE, GoodsSubTypeEnum[GoodsTypeEnum.NURSE].NURSE].join('-')]: '护理',
    [[GoodsTypeEnum.NURSE, GoodsSubTypeEnum[GoodsTypeEnum.NURSE].NURSE_LEVEL].join('-')]: '护理等级',
    [[GoodsTypeEnum.SURGERY, GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY].join('-')]: '手术',
    [GoodsTypeEnum.OTHER]: '其他',
});
export const SheBaoRegionPayment = {
    chengdu: null,
    chongqing: {
        civilServiceSubsidy: '公务员补助',
        largeClaimAmount: '大额理赔金额',
        civilServiceBack: '历史起付线公务员返还',
        // singleDiseaseSupport: '单病种定点医疗机构垫支',
        civilAidAmount: '民政救助金额',
        civilAidOutpatientBalance: '民政救助门诊余额',
        totalSubstituteFee: '总共抵用费用',
        healthHelpFundFee: '健康扶贫医疗基金',
        precisionPovertyFee: '精准脱贫保险金额',
        otherPovertyReliefFee: '其他扶贫报销金额',
    },
    wuhan: {
        communityThreeFree: '社区三免',
        lowPaidLine: '起付线',
        superCappingLine: '超顶封线',
        medicalInsuranceFee: '列入医保费用',
        selfPaymentFee: '纯自费项目金额',
    },
    hangzhou: {
    // beforeCurYearBalance: '刷卡前当年账户余额', // 刷卡前当年账户余额
    // beforeAllYearBalance: '刷卡前历年账户余额', // 刷卡前历年账户余额

        curYearBalance: '本年余额', // 当年账户余额
        allYearBalance: '历年余额', // 历年账户余额
        curYearAccountPaymentFee: '本年支付', // 本年账户支付
        allYearAccountPaymentFee: '历年支付', // 历年账户支付

        cashPaymentFee: '医保现金支付', // 医保现金支付

        selfConceitFee: '自负', // 自负金额
        allYearAccountPaymentSelfConceitFee: '其中历年账户', // 历年账户支付自负部分 （省医保为空）

        personalHandledAmount: '自理', // 自理金额
        allYearAccountPaymentPersonalHandled: '其中历年账户', // 历年账户支付自理 （省医保为空）

        personalPaymentAmount: '自费', // 自费金额
        allYearAccountPaymentPersonalPayment: '其中历年账户', // 历年账户支付自费 （省医保为空）

        curYearOutpatientStartingPointStandardAmount: '本年门诊起付标准支付累计 （省医保为空）', // 本年门诊起付标准支付累计 （省医保为空）

        sbzzPaymentFee: '商保赔付', // 商保赔付
        specialDiseaseMark: '规定病种', // 规定病种
        fundPayment: '基金支付', // 规定病种
        cashPayment: '现金支付', // 规定病种
    },
    qingdao: {
        individualAffordabilityLine: '个人负担起付线', // 个人负担起付线
    },
};

export const PRINT_TYPES = Object.freeze({
    TREATMENT_EXECUTE: 'treatmentExecute', // 治疗执行单
    INFUSION_EXECUTE: 'infusionExecute', // 输注执行单
    PRESCRIPTION: 'prescription', // 处方笺
    EXAMINATION: 'examination', // 检查检验单
    MEDICAL: 'medical', // 病历
    MEDICAL_CERTIFICATE: 'medicalCertificate', // 病情证明书
    CHILD_TEST_RESULT: 'childTestResult', // 儿童测试报告
    CHILD_HEALTH_REPORT: 'childHealthReport', // 儿童健康报告
    CHILD_QUESTION_TABLE: 'childQuestionTable', // 儿童问卷
    CHRONIC_CARE: 'chronicCare', // 慢病管理
    A5_DISPENSING: 'A5Dispensing', // A5发药单打印
    STAT: 'statPrint', // 统计打印
    FAMILY_DOCTOR_AGREEMENT: 'familyDoctorAgreementPrint', // 家庭医生打印
});

export const NUMBER_ICONS = Object.freeze([
    '',
    '①',
    '②',
    '③',
    '④',
    '⑤',
    '⑥',
    '⑦',
    '⑧',
    '⑨',
    '⑩',
    '⑪',
    '⑫',
    '⑬',
    '⑭',
    '⑮',
    '⑯',
    '⑰',
    '⑱',
    '⑲',
    '⑳',
    '㉑',
    '㉒',
    '㉓',
    '㉔',
    '㉕',
    '㉖',
    '㉗',
    '㉘',
    '㉙',
    '㉚',
    '㉛',
    '㉜',
    '㉝',
    '㉞',
    '㉟',
    '㊱',
    '㊲',
    '㊳',
    '㊴',
    '㊵',
    '㊶',
    '㊷',
    '㊸',
    '㊹',
    '㊺',
    '㊻',
    '㊼',
    '㊽',
    '㊾',
    '㊿',
]);

export const EXAMINATION_RESULT_ENUM = Object.freeze({
    NONE: 0,
    UP: 1,
    DOWN: 2,
    BAD: 3,
});

export const REFS_ENUM = Object.freeze({
    NUMBER: 1,
    TEXT: 2,
    YIN_YANG: 3,
});

export const MARITAL_ENUM = Object.freeze({
    UNMARRIED: 1, //未婚
    MARRIED: 2, // 已婚
    DIVORCED: 3, // 离异
    WIDOWED: 4, // 丧偶
})
export const MARTIAL_LABEL_ENUM = Object.freeze([
    {
        value: MARITAL_ENUM.UNMARRIED, //未婚
        label: '未婚',
    },
    {
        value: MARITAL_ENUM.MARRIED, // 已婚
        label: '已婚',
    },
    {
        value: MARITAL_ENUM.DIVORCED, // 离异
        label: '离异',
    },
    {
        value: MARITAL_ENUM.WIDOWED,// 丧偶
        label: '丧偶',
    },
])


export const SourceFormTypeEnum = Object.freeze({
    // 挂号 1
    REGISTRATION: 1,
    // 检查检验 2
    EXAMINATION: 2,
    // 治疗理疗 3
    TREATMENT: 3,
    // 西药处方 4
    PRESCRIPTION_WESTERN: 4,
    // 输液处方 5
    PRESCRIPTION_INFUSION: 5,
    // 中药处方 6
    PRESCRIPTION_CHINESE: 6,
    // 门诊收费 新增加的收费项目单独放在一个form 7
    ADDITIONAL_FORM: 7,
    // 门诊收费新增商品 8 // 商品都在这个form里面
    ADDITIONAL_SALE_PRODUCT_FORM: 8,
    // 物资 9
    MATERIAL: 9,
    // 赠品 10
    GIFT: 10,
    // 套餐 11
    COMPOSE: 11,
    // 咨询费 12
    ONLINE_CONSULTATION: 12,
    // 配送费 13
    EXPRESS_DELIVERY: 13,
    // 代煎费 14
    DECOCTION: 14,
    // 空中药房 15
    AIR_PHARMACY: 15,
    // 外治处方 16
    PRESCRIPTION_EXTERNAL: 16,
    // 家庭医生签约 17
    FAMILY_DOCTOR_SIGN: 17,
    // 卡项开通费用
    PROMOTION_CARD_OPEN: 18,
    // 卡项充值费用
    PROMOTION_CARD_RECHARGE: 19,
    // 会员卡充值
    MEMBER_CARD_RECHARGE: 20,
    // 其他费用
    OTHER_FEE: 21,
    // 眼镜
    EYEGLASSES: 22,
    // 护理医嘱
    NURSING: 23,
    // 眼镜处方
    PRESCRIPTION_GLASSES: 24,
    // 单品优惠赠品 25
    SINGLE_PROMOTION_GIFT: 25,
    // 手术
    SURGERY: 26,
});

/**
 * 诊所管家和医院管家的含义不同,但是printType相同(做了兼容处理)
 * 左边的是诊所管家的含义
 * 右边的是医院管家的含义
 */
export const PRINT_BILL_AMOUNT_TYPE = {
    westernMedicineFeeType: 1, // 西药费
    chineseMedicineDrinksPieceFeeType: 2, // 中药饮片费用 or 中药费
    chineseComposeMedicineFeeType: 3, // 中成药费
    examinationInspectionFeeType: 4, // 检查费
    examinationExaminationFeeType: 5, // 化验费 or 检验费
    treatmentFeeType: 6, // 治疗理疗费 or 治疗费
    registrationFeeType: 7, // 挂号费
    materialFeeType: 8, // 材料费 or 卫生材料费
    otherFeeType: 9, // 其他费用
    examinationType: 10, // 诊察费
    nursingType: 11, // 护理费
    physiotherapyType: 12, // 理疗费
    bedType: 13, // 床位费
    operationType: 14, // 手术费
    generalDiagnosisAndTreatmentType: 15, // 一般诊疗费
    pharmacyServiceType: 16, // 药事服务费
};

export const AstEnum = Object.freeze({
    PI_SHI: 1, // 皮试
    XU_YONG: 2, //续用
    MIAN_SHI: 3, //免试
});

// 药房类型
export const PharmacyTypeEnum = Object.freeze({
    LOCAL_PHARMACY: 0,
    AIR_PHARMACY: 1,
    VIRTUAL_PHARMACY: 2,
});

export const ChronicCareFormPrintType = Object.freeze({
    CONSULTATION: 1, // 问诊单
    ARCHIVES: 2, // 建档
    EVALUATION: 3, // 评估
});

export const TreatmentTypeEnum = Object.freeze({
    /**
     * 未知
     */
    UN_KNOW: 0,
    /**
     * 药品
     */
    MEDICINE: 1,
    /**
     * 检查
     */
    INSPECTION: 10,
    /**
     * 检验
     */
    ASSAY: 20,
    /**
     * 输血
     */
    BLOOD_TRANSFUSION: 30,
    /**
     * 会诊
     */
    DOCTORS_CONSULTATION: 40,
    /**
     * 诊断
     */
    DIAGNOSIS: 50,
    /**
     * 护理
     */
    NURSE: 60,
    /**
     * 手术
     */
    SURGERY: 70,
    /**
     * 转科
     */
    TRANSFER_DEPARTMENT: 80,
    /**
     * 出院
     */
    DISCHARGE_WITH_MEDICINE: 90,
    /**
     * 转院
     */
    TRANSFER_WITH_MEDICINE: 91,
    /**
     * 会诊
     */
    CONSULTATION: 100,
    /**
     * 物资
     */
    MATERIALS: 110,
});

export const HospitalAstEnum = Object.freeze({
    MIAN_SHI: 0, //免试
    PI_SHI: 1, // 皮试
    XU_YONG: 2, //续用
});

export const TreatmentTypeStr = Object.freeze({
    [TreatmentTypeEnum.UN_KNOW]: '未知',
    [TreatmentTypeEnum.MEDICINE]: '药品',
    [TreatmentTypeEnum.INSPECTION]: '检查',
    [TreatmentTypeEnum.ASSAY]: '检验',
    [TreatmentTypeEnum.BLOOD_TRANSFUSION]: '输血',
    [TreatmentTypeEnum.DOCTORS_CONSULTATION]: '会诊',
    [TreatmentTypeEnum.DIAGNOSIS]: '诊断',
    [TreatmentTypeEnum.NURSE]: '护理',
    [TreatmentTypeEnum.SURGERY]: '手术',
    [TreatmentTypeEnum.TRANSFER_DEPARTMENT]: '转科',
    [TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE]: '出院',
    [TreatmentTypeEnum.TRANSFER_WITH_MEDICINE]: '转院', // 转院同出院
    [TreatmentTypeEnum.CONSULTATION]: '会诊',
    [TreatmentTypeEnum.MATERIALS]: '物资',
});


export const HospitalDepositReceiptTypeEnum = Object.freeze({
    /**
     * 支付
      */
    PAY: 1,
    /**
     * 退款
     */
    REFUND: 2,
})

// 用药标签默认打印设置
export const printCountDataObj = {
    // 口服
    oral: {
        printCopies: 1,
        isPrint: 1,
    },
    // 注射
    injections: {
        printCopies: 1,
        isPrint: 1,
    },
    // 外用
    external: {
        printCopies: 1,
        isPrint: 1,
    },
    // 输液
    infusion: {
        printCopies: 1,
        isPrint: 1,
    },
    // 雾化
    atomization: {
        printCopies: 1,
        isPrint: 1,
    },
    // 煎服
    decoction: {
        printCopies: 1,
        isPrint: 1,
    },
};

export const MEDICINE_USAGE = {
    // 口服
    oral: ['口服', '含服', '嚼服', '晨服', '餐前服', '餐中服', '餐后服', '睡前服'],
    // 注射
    injections: ['静脉注射', '肌内注射', '腔内注射', '皮下注射', '皮内注射', '穴位注射', '局部注射', '局部麻醉', '超声透药', '静脉泵入', '椎管内注射'],
    // 外用
    external: ['溶媒用', '外用', '滴眼', '滴鼻', '滴耳', '口腔喷入', '吸入', '鼻腔喷入', '含漱', '涂抹', '塞肛', '直肠给药', '阴道给药', '膀胱冲洗', '灌肠'],
    // 输液
    infusion: ['静脉滴注', '直肠滴注', '入壶静滴', '输液冲管', '鼻饲', '膀胱给药', '封管'],
    // 雾化
    atomization: ['雾化吸入'],
};

// 标题最大长度
export const TITLE_MAX_LENGTH = 15;

/**
 * 配镜处方的类型字段
 * 0 镜框  1 隐形眼镜
 * @type {{"0": string[], "1": string[]}}
 */
export const GLASSES_TYPE = {
    0: ['frameSpherical', 'frameLenticular', 'frameAxial', 'framePrism', 'frameBase', 'frameCva', 'frameAdd', 'framePupilDistance', 'framePupilHeight'],
    1: ['contactFocalLength', 'contactBozr', 'contactDiameter', 'contactLenticular', 'contactAxial'],
};



export const DispenseOrderFormItemTypeEnum = Object.freeze({
    WAITING: 0,// 待发
    DISPENSED: 1, //已发
    UNDISPENSED: 2,// 已退
    CLOSE: 3,// 拒绝发药
    PART_DISPENSE: 4,// 部分发
    RECORD_NOT_DISPENSE: 5,// 补记录，无需发药扣库
    STOCK_NOT_DISPENSE: 6,// 仅扣库，无需发药
    APPLY_DISPENSE_REJECT: 11, //拒绝发药
    RESET_DISPENSED: 12, //重新发药
    APPLY_UNDISPENSE: 20,// 待退
    APPLY_UNDISPENSE_REJECT: 21 ,// 拒绝退药
    RESET_UNDISPENSED: 22 ,// 重新退药
    PART_DISPENSED: 2023,// 部分发药，前端自定义的，用于药品维度展示使用
});


export const MedicalAdviceTypeEnum = Object.freeze({
    /**
     * 一次性/临时
     */
    ONE_TIME: 0,

    /**
     * 长期
     */
    LONG_TIME: 10,

    /**
     * 出院带药
     */
    DISCHARGE_WITH_MEDICINE: 20,
});

// 医嘱单个项目类型
export const AdviceRuleType = Object.freeze({
    /**
     * 西成药
     */
    WESTERN_MEDICINE: 0,
    /**
     * 中药饮片
     */
    CHINESE_MEDICINE_TABLETS: 10,
    /**
     * 中药颗粒
     */
    CHINESE_MEDICINE_GRANULES: 20,
    /**
     * 检查
     */
    INSPECTION: 30,
    /**
     * 检验
     */
    ASSAY: 40,
    /**
     * 护理
     */
    NURSE: 50,
    /**
     * 护理等级
     */
    NURSE_LEVEL: 51,
    /**
     * 治疗
     */
    TREATMENT: 60,
    /**
     * 理疗
     */
    PHYSIOTHERAPY: 70,
    /**
     * 转科
     */
    TRANSFER_DEPARTMENT: 80,
    /**
     * 出院
     */
    DISCHARGE_WITH_MEDICINE: 90,
    /**
     * 转院
     */
    TRANSFER_WITH_MEDICINE: 91,
    /**
     * 会诊
     */
    CONSULTATION: 100,
    /**
     * 手术医嘱
     */
    SURGERY: 110,
    /**
     * 术后医嘱
     */
    POSTOPERATIVE: 111,
    /**
     * 医疗器械
     */
    MEDICINE_MATERIAL: 120,
    /**
     * 自制成品
     */
    SELF_PRODUCT: 121,
    /**
     * 保健药品
     */
    HEALTH_MEDICINE: 122,
    /**
     * 保健食品
     */
    HEALTH_FOOD: 123,
    /**
     * 其他商品
     */
    OTHER_PRODUCT: 124,

});

// 医嘱状态
export const MedicalAdviceStatusEnum = Object.freeze({
    /**
     * 下达/初始化
     */
    INIT: 0,
    /**
     * 已核对/待执行
     */
    CHECKED: 10,
    /**
     * 已执行
     */
    EXECUTED: 20,
    /**
     * 已停止
     */
    STOPPED: 30,
    /**
     * 停止确认
     */
    STOPPED_CONFIRM: 31,
    /**
     * 已撤销
     */
    UNDONE: 90,

    STOPPED_UNDONE: 3090, // 停止/撤销
});

export const PROCESS_TYPES_ENUM = Object.freeze({
    DECOCTION: 1, //煎药
    CREAM: 2, //制膏
    POWDER: 3, //打粉
    PILL: 4,  //制丸
})


export const MedicalFeeGradeEnum = Object.freeze({
    // 甲等
    A: 1,
    // 乙等
    B: 2,
    // 丙等
    C: 3,
    // 深圳特殊等级, 无意义
    D: 4,
});

// 交班日期
export const SHIFT_TYPE = {
    DAY: 0,
    MIDDLE: 1,
    NIGHT: 2,
};

// 交班日期
export const ShiftTypeToText = {
    [SHIFT_TYPE.DAY]: '白班',
    [SHIFT_TYPE.MIDDLE]: '中班',
    [SHIFT_TYPE.NIGHT]: '夜班',
};

// 小于10000都是内置费用类型ID
export const FEE_TYPE_ID_ABC_INNER_MAX = 10000;

/**
 * 内置费用类型id
 * 小于10000都是内置费用类型ID
 */
export const FEE_TYPE_ID_ENUM = {
    WEST_MED: 12, // 西药费
    CM_PIECE: 14, // 中药饮片费
    CM_GRANULE: 15, // 中药颗粒费
    CHINESE_PATENT: 16, // 中成药
    MATERIAL: 2, // 材料大类商品ID
    REGISTRATION: 5, // 挂号费
    ADDITION: 7, // 商品大类商品ID
    CHINESE_MEDICINE: 13, // 中药
    MATERIAL_MEDICINE_MATERIAL: 17, // 医用材料
    MATERIAL_LOGISTICS_MATERIAL: 18, // 后勤材料
    MATERIAL_FIXED_ASSETS: 19, // 固定资产
    EXAMINATION_ASSAY: 20, // 检验
    EXAMINATION_INSPECTION: 21, // 检查
    TREATMENT_TREATMENT: 22, // 治疗
    TREATMENT_PHYSIOTHERAPY: 23, // 理疗
    ADDITIONAL_SELF_PRODUCT: 25, // 自制成品
    ADDITIONAL_HEALTH_MEDICINE: 26, //保健药品
    ADDITIONAL_HEALTH_FOOD: 27, // 保健食品
    ADDITIONAL_OTHER_PRODUCT: 28, // 其他商品
    FEE: 33, // 费用类型 也就是以前的其他类型
    DENTURE_PROCESSING: 54, // 义齿加工
    NURSE_NURSE: 56, // 护理子类型的typeId
    EYE: 63,
    MEDICINE_SERVICE: 34, // 药事服务费 加工费、快递费、辅料费
    PROMOTION_CARD_RECHARGE: 32, // 卡项充值费
    PROMOTION_CARD_OPEN: 31, // 卡项开通费
    MEMBER_CARD_RECHARGE: 6, // 会员充值
    FAMILY_DOCTOR_SIGN: 30, // 家庭医生签约费
    ONLINE_CONSULTATION: 9, // 在线咨询费
    BED: 58, // 床位费
    DIAGNOSTIC: 1001, // 诊察费
    OPERATION: 1002, // 手术费
    GENERAL_TREATMENT: 1003, // 一般诊疗费
    HYGIENIC_MATERIAL: 1004, // 卫生材料费
    AIR_PHARMACY: 1005, // 空中药房费
    CONSULTATION_CONSULTATION: 71, // 会诊子类型typeId
    SAMPLE: 73, // 采样组typeId
};

/**
 * 内置费用类型名称枚举值
 */
// export const FEE_TYPE_NAME_ENUM = {
//     [FEE_TYPE_ID_ENUM.WEST_MED]: '西药费', // 西药费
//     // [FEE_TYPE_ID_ENUM.CM_PIECE]: 'CM_PIECE', // 中药饮片费
//     // [FEE_TYPE_ID_ENUM.CM_GRANULE]: 'CM_GRANULE', // 中药颗粒费
//     [FEE_TYPE_ID_ENUM.CHINESE_PATENT]: '中成药费', // 中成药
//     // [FEE_TYPE_ID_ENUM.MATERIAL]: 'MATERIAL', // 材料大类商品ID
//     [FEE_TYPE_ID_ENUM.REGISTRATION]: '挂号费', // 挂号费
//     // [FEE_TYPE_ID_ENUM.ADDITION]: 'ADDITION', // 商品大类商品ID
//     [FEE_TYPE_ID_ENUM.CHINESE_MEDICINE]: '中药费', // 中药
//     // [FEE_TYPE_ID_ENUM.MATERIAL_MEDICINE_MATERIAL]: 'MATERIAL_MEDICINE_MATERIAL', // 医用材料
//     // [FEE_TYPE_ID_ENUM.MATERIAL_LOGISTICS_MATERIAL]: 'MATERIAL_LOGISTICS_MATERIAL', // 后勤材料
//     // [FEE_TYPE_ID_ENUM.MATERIAL_FIXED_ASSETS]: 'MATERIAL_FIXED_ASSETS', // 固定资产
//     [FEE_TYPE_ID_ENUM.EXAMINATION_ASSAY]: '检验费', // 检验
//     [FEE_TYPE_ID_ENUM.EXAMINATION_INSPECTION]: '检查费', // 检查
//     [FEE_TYPE_ID_ENUM.TREATMENT_TREATMENT]: '治疗费', // 治疗
//     // [FEE_TYPE_ID_ENUM.TREATMENT_PHYSIOTHERAPY]: 'TREATMENT_PHYSIOTHERAPY', // 理疗
//     // [FEE_TYPE_ID_ENUM.ADDITIONAL_SELF_PRODUCT]: 'ADDITIONAL_SELF_PRODUCT', // 自制成品
//     // [FEE_TYPE_ID_ENUM.ADDITIONAL_HEALTH_MEDICINE]: 'ADDITIONAL_HEALTH_MEDICINE', //保健药品
//     // [FEE_TYPE_ID_ENUM.ADDITIONAL_HEALTH_FOOD]: 'ADDITIONAL_HEALTH_FOOD', // 保健食品
//     // [FEE_TYPE_ID_ENUM.ADDITIONAL_OTHER_PRODUCT]: 'ADDITIONAL_OTHER_PRODUCT', // 其他商品
//     // [FEE_TYPE_ID_ENUM.FEE]: 'FEE', // 费用类型 也就是以前的其他类型
//     // [FEE_TYPE_ID_ENUM.DENTURE_PROCESSING]: 'DENTURE_PROCESSING', // 义齿加工
//     [FEE_TYPE_ID_ENUM.NURSE_NURSE]: '护理费', // 护理子类型的typeId
//     // [FEE_TYPE_ID_ENUM.EYE]: 'EYE',
//     [FEE_TYPE_ID_ENUM.MEDICINE_SERVICE]: '药事服务费', // 药事服务费 加工费、快递费、辅料费
//     // [FEE_TYPE_ID_ENUM.PROMOTION_CARD_RECHARGE]: 'PROMOTION_CARD_RECHARGE', // 卡项充值费
//     // [FEE_TYPE_ID_ENUM.PROMOTION_CARD_OPEN]: 'PROMOTION_CARD_OPEN', // 卡项开通费
//     // [FEE_TYPE_ID_ENUM.MEMBER_CARD_RECHARGE]: 'MEMBER_CARD_RECHARGE', // 会员充值
//     // [FEE_TYPE_ID_ENUM.FAMILY_DOCTOR_SIGN]: 'FAMILY_DOCTOR_SIGN', // 家庭医生签约费
//     // [FEE_TYPE_ID_ENUM.ONLINE_CONSULTATION]: 'ONLINE_CONSULTATION', // 在线咨询费
//     [FEE_TYPE_ID_ENUM.BED]: '床位费', // 床位费
//     [FEE_TYPE_ID_ENUM.DIAGNOSTIC]: '诊察费', // 诊察费
//     [FEE_TYPE_ID_ENUM.OPERATION]: '手术费', // 手术费
//     [FEE_TYPE_ID_ENUM.GENERAL_TREATMENT]: '一般诊疗费', // 一般诊疗费
//     [FEE_TYPE_ID_ENUM.HYGIENIC_MATERIAL]: '卫生材料费', // 卫生材料费
//     // [FEE_TYPE_ID_ENUM.AIR_PHARMACY]: 'AIR_PHARMACY', // 空中药房费
//     // [FEE_TYPE_ID_ENUM.CONSULTATION_CONSULTATION]: 'CONSULTATION_CONSULTATION', // 会诊子类型typeId
//     // [FEE_TYPE_ID_ENUM.SAMPLE]: 'SAMPLE', // 采样组typeId
// };

// 内置费用类型展示顺序
export const FEE_TYPE_SORT = {
    [FEE_TYPE_ID_ENUM.REGISTRATION]: 1, // 挂号费
    [FEE_TYPE_ID_ENUM.EXAMINATION_ASSAY]: 2, // 检验
    [FEE_TYPE_ID_ENUM.EXAMINATION_INSPECTION]: 3, // 检查
    [FEE_TYPE_ID_ENUM.TREATMENT_TREATMENT]: 4, // 治疗
    [FEE_TYPE_ID_ENUM.NURSE_NURSE]: 5, // 护理子类型的typeId
    [FEE_TYPE_ID_ENUM.WEST_MED]: 6, // 西药费
    [FEE_TYPE_ID_ENUM.CHINESE_PATENT]: 7, // 中成药
    [FEE_TYPE_ID_ENUM.CHINESE_MEDICINE]: 8, // 中药
    [FEE_TYPE_ID_ENUM.HYGIENIC_MATERIAL]: 9, // 卫生材料费
    [FEE_TYPE_ID_ENUM.MEDICINE_SERVICE]: 10, // 药事服务费 加工费、快递费、辅料费
    [FEE_TYPE_ID_ENUM.BED]: 11, // 床位费
    [FEE_TYPE_ID_ENUM.DIAGNOSTIC]: 12, // 诊察费
    [FEE_TYPE_ID_ENUM.OPERATION]: 13, // 手术费
    [FEE_TYPE_ID_ENUM.GENERAL_TREATMENT]: 14, // 一般诊疗费
    [FEE_TYPE_ID_ABC_INNER_MAX]: 15, // 其他费用
};

export const specialUsages = ['制膏', '制丸', '打粉'];

// 门店类型
export const CLINIC_NODE_TYPE = {
    SINGLE: 0, // 单店
    CHAIN_ADMIN: 1, // 连锁总店
    CHAIN_SUB: 2, // 连锁子店
};

export const hospitalAdviceShandongPageLimit = 25;

export const TreatmentExecuteUsageRenderType = {
    noUsage: 0,
    countAndDays: 2,
    allUsage: 1,
}
export const HospitalPrescriptionDiagnosisTypeEnum = Object.freeze({
    first: 0, // 初步诊断
    inHospital: 10, // 住院诊断
    leave: 20, // 出院诊断
    outpatient: 30, // 门诊诊断
});

/**
 * 检验来源
 */
export const BusinessTypeToFormLabel = {
    0: '未知',
    10: '门诊',
    20: '门诊',
    30: '住院',
    40: '门诊',
    50: '门诊',
    100: '其他',
    200: '体检',
};

export const InsutypeCodeEnum = Object.freeze({
    staff: 310, // 职工
    resident: 390, // 居民
});

export const InsutypeCodeLabelEnum = Object.freeze({
    [InsutypeCodeEnum.staff]: '职工',
    [InsutypeCodeEnum.resident]: '居民',
});




export const AdviceTagEnum = Object.freeze({
    SUPPLEMENT: 0, // 补录
    URGENT: 1, // 紧急
    MA_ZUI: 2, //麻醉
    JING_1: 3, // 精1
    JING_2: 4, // 精2
    DU: 5,// 毒
    OPERATE_ING: 6, // 术中
    OPERATE_AFTER: 7, // 术后
});


export const AdviceTagEnumSingleText = Object.freeze({
    [AdviceTagEnum.SUPPLEMENT]: '补',
    [AdviceTagEnum.URGENT]: '急',
    [AdviceTagEnum.OPERATE_ING]: '术中',
    [AdviceTagEnum.OPERATE_AFTER]: '术后',
    [AdviceTagEnum.JING_1]: '精一',
    [AdviceTagEnum.JING_2]: '精二',
    [AdviceTagEnum.DU]: '毒',
    [AdviceTagEnum.MA_ZUI]: '麻醉',
});