import {
    AiJiuSubOptions, BaGuanSubOptions,
    ExternalAcupointTypeEnum,
    ExternalPRUsageTypeEnum,
    TieFuSubOptions, TuiNaSubOptions,
    UsageTypeOptions,
    ZhenCiSubOptions,
} from '../constant/external-constants';
import clone from './clone.js';
import { AstEnum, EXAMINATION_RESULT_ENUM, REFS_ENUM, SourceFormTypeEnum } from './constants.js';
import { formatTreatmentUnit, isNull, object2Array } from './utils.js';
import { MEDICINE_FREQ, MEDICINE_USAGE } from './western-medicine-config.js';

export function extractNumbers(text, options) {
    let numbers
    options = options || {}
    if (!text || typeof text !== 'string') {
        return []
    }

    numbers = text.match(/(-\d+|\d+)(,\d+)*(\.\d+)*/g)

    if (options.string === false) {
        numbers = numbers.map((n) => Number(n.replace(/,/g, '')))
    }

    return numbers
}

export function getDoctorAdviceArray(doctorAdviceStr) {
    if (!doctorAdviceStr) return []
    let doctorAdvice = doctorAdviceStr.replace(/(<br>|<br\/>|<br\s\/>)+/g, '<br>')
    doctorAdvice = doctorAdvice.split('<br>').filter((item) => {
        return !!item
    })
    return doctorAdvice || []
}

// 根据groupId 进行分组，返回二维数组
export function getInfusionGroupItems(prescriptionFormItems) {
    let _group = {}
    //分组
    prescriptionFormItems.forEach((medicine) => {
        if (!(_group[Number(medicine.groupId)] instanceof Array))
            _group[Number(medicine.groupId)] = []

        _group[Number(medicine.groupId)].push(medicine)
    })
    return object2Array(_group)
}

// 根据groupId 进行分组，返回二维数组
export function getInfusionGroupHospitalItems(infusionPrescription) {
    let res = [],
        group = new Map()
    ;(infusionPrescription.goods || []).forEach((good) => {
        const groupId = infusionPrescription.otherInfo[good.id].groupId
        if (!group.has(groupId)) {
            group.set(groupId, [])
        }
        group.get(groupId).push(good)
    })
    // 循环 group 取值
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    group.forEach((value, _key) => {
        res.push(value)
    })
    return res
}
// 将中药的数据拆分成每行4个展示
export function getChineseGroupItems(prescriptionFormItems, groupCount) {
    //将没有groupId放到最后
    let tmp = clone(prescriptionFormItems)
    let index = 0
    let newArray = []
    if (!tmp || !tmp.length) {
        return newArray
    }
    while (index < tmp.length) {
        let pageData = tmp.slice(index, (index += groupCount))
        newArray.push(pageData)
    }
    return newArray
}

export function doseTotal(prescriptionFormItems) {
    let count = 0
    const kinds = []
    prescriptionFormItems.forEach((item) => {
        const name = item.name || item.goodsName
        if (isNull(item.unit) || item.unit === 'g') {
            count += item.unitCount || 0
        }
        if (kinds.indexOf(name) === -1) {
            kinds.push(name)
        }
    })
    return {
        kind: kinds.length,
        count: count.toFixed(2),
    }
}

// 获取中药处方加工
export function getChineseFormProcess(form) {
    let _str = ''
    if (form.processUsage) {
        _str += `${form.processUsage}`
    }
    if (form.processUsage && form.processUsage.indexOf('煎药') > -1) {
        _str += `，1剂煎${form.processBagUnitCount}袋，共 ${
            form.doseCount * form.processBagUnitCount
        } 袋`
    }
    if (form.contactMobile) {
        _str += `，联系人 ${form.contactMobile}`
    }
    return _str
}

// formatType 0 全部隐藏  1 部分展示  2 完整展示
export function filterMobile(mobile, formatType) {
    if (!mobile) {
        return ''
    }
    if (formatType === 2) {
        return mobile
    }
    if (formatType === 1) {
        return mobile.substr(0, 3) + '****' + mobile.substr(7, 11)
    }
    if (formatType === 0) {
        return '***********'
    }
    return mobile
}

/**
 * @desc V2 格式化手机号码显示方式
 * @param mobile {string}
 * @param formatType {number} 0 不展示  1 隐藏中间4位  2 完整展示
 * @returns {string}
 */
export function filterMobileV2(mobile, formatType = 1) {
    if (!mobile) {
        return ''
    }
    if (formatType === 2) {
        return mobile
    }
    if (formatType === 1) {
        return mobile.substr(0, 3) + '****' + mobile.substr(7, 11)
    }
    if (formatType === 0) {
        return ''
    }
    return mobile
}

/**
 * 手机号是否脱敏
 * @param {string} mobile
 * @param {0 | 1} formatType
 * @return {string}
 */
export function formatMobile(mobile, formatType = 0) {
    if (!mobile) return '';
    return formatType ? mobile.slice(0, 3) + '****' + mobile.slice(7) : mobile;
}

/**
 * 身份证脱敏
 * @param idCard {string}
 * @param formatType {number} 0:不脱敏 1:脱敏
 * @return {string}
 */
export function filterIdCard(idCard, formatType = 1) {
    if (!idCard) return ''
    if (formatType) {
        return idCard.slice(0, 4) + '***********' + idCard.slice(15)
    }
    return idCard
}

/**
 * 获取诊断
 * @param val
 * @return {*|string}
 */
export function filterDiagnose(val) {
    if (!val) return ''
    const valArr = val.split('<br>')
    return valArr.join('')
}

/**
 * @desc 是否使用拉丁文显示用法
 * <AUTHOR>
 * @date 2021-11-25 20:06:54
 * @params
 * @return
 */
export function usageFormat(usage, useLatin = 0) {
    if (!useLatin) return usage
    if (!usage) return ''
    const usages = MEDICINE_USAGE
    const usageMap = new Map()
    usages.forEach((item) => {
        usageMap.set(item.name, item.latin)
    })
    return usageMap.get(usage) || usage
}

/**
 * @desc 是否使用拉丁文显示频率
 * <AUTHOR>
 * @date 2021-11-25 20:06:54
 * @params
 * @return
 */
export function freqFormat(freq, useLatin = 0) {
    if (useLatin) return freq
    if (!freq) return ''

    const freqs = MEDICINE_FREQ
    const freqMap = new Map()
    freqs.forEach((item) => {
        freqMap.set(item.en, item.name)
    })

    let res = freq
    if (freqMap.get(freq)) {
        res = freqMap.get(freq)
    } else {
        const _num = +freq.replace(/[^0-9]/gi, '')
        if (/^q\d+d$/.test(freq)) {
            res = `每${_num}天1次`
        } else if (/^q\d+w$/.test(freq)) {
            res = `每${_num}周1次`
        } else if (/^q\d+h$/.test(freq)) {
            res = `每${_num}小时1次`
        }
    }
    return res
}

/**
 * @desc 注射单位使用拉丁文
 * <AUTHOR>
 * @date 2021-11-30 11:34:57
 */
export function transIvgttUnit(ivgttunit, useLatin = 0) {
    if (useLatin && ivgttunit) {
        if (ivgttunit.indexOf('滴/分钟') > -1) {
            return ivgttunit.replace('滴/分钟', 'd/min')
        }
        return ivgttunit
    }
    return ivgttunit
}

/**
 * @desc 判断是否是外用用法
 * <AUTHOR>
 * @date 2021-12-19 17:22:15
 */
export function isExternalUsage(usage) {
    if (!usage) return false
    const externalUsages = MEDICINE_USAGE.filter((usage) => {
        return usage.type === 3
    })
    const index = externalUsages.findIndex((item) => {
        return item.name === usage
    })
    return index > -1
} /**
 * @desc 判断是否是输液用法
 * <AUTHOR>
 * @date 2021-12-19 17:22:15
 */
export function isInfusionUsage(usage) {
    if (!usage) return false
    const infusionUsages = MEDICINE_USAGE.filter((usage) => {
        return usage.type === 2
    })
    const index = infusionUsages.findIndex((item) => {
        return item.name === usage
    })
    return index > -1
}

function isNumberString(x) {
    const NUMBER = /^\-?\d+(\.\d+)?$/
    return NUMBER.test(x)
}

export function isNumber(a) {
    return parseFloat(a).toString() != 'NaN'
}

export function calValue2(item = {}) {
    const { value } = item
    // 文字性、阴阳性参考范围，直接返回值，不计算小数位
    if (item.type === REFS_ENUM.TEXT || item.type === REFS_ENUM.YIN_YANG) {
        return value
    }
    if (!isNumberString(value)) return value
    const v = +value
    if (isNaN(v)) return value
    const resultDisplayScale = isNumber(item.resultDisplayScale) ? item.resultDisplayScale : 2
    return v.toFixed(resultDisplayScale)
}

/**
 * JSON parse
 * @param any
 * @returns {any | Object}
 */
export function tryJSONParse(any) {
    try {
        return JSON.parse(any)
    } catch (e) {
        return any
    }
}

export function isObject(x) {
    return typeof x === 'object' && x !== null
}

export function calRange(itemRef) {
    const item = clone(itemRef)
    item.ref = tryJSONParse(item.ref)

    if (isObject(item.ref)) {
        const min = isNumberString(item.ref.min) ? item.ref.min : ''
        const max = isNumberString(item.ref.max) ? item.ref.max : ''
        // 显示为区间时，当任意值只有一个时且该值为数字
        // > 最小值
        // < 最大值
        if (isNumberString(min) && !isNumberString(max)) {
            return `>${min}`
        } else if (isNumberString(max) && !isNumberString(min)) {
            return `<${max}`
        } else if (!isNumberString(min) && !isNumberString(max)) {
            return ''
        } else {
            return `${min}~${max}`
        }
    } else {
        return item.ref
    }
}

function transNumberToEnum(number) {
    if (number > 0) {
        return EXAMINATION_RESULT_ENUM.UP
    }
    if (number < 0) {
        return EXAMINATION_RESULT_ENUM.DOWN
    }
    return EXAMINATION_RESULT_ENUM.NONE
}

export function calNature(itemRef) {
    const item = clone(itemRef)
    item.ref = tryJSONParse(item.ref)

    // 图片指标
    if (item.valueType === 'IMAGE') {
        return EXAMINATION_RESULT_ENUM.NONE
    }

    // 阴阳型
    if (item.value && item.ref && item.type === REFS_ENUM.YIN_YANG && item.value !== item.ref) {
        return EXAMINATION_RESULT_ENUM.BAD
    }

    let value = item.value

    // 尝试提取数字
    if (!isNumberString(value) && extractNumbers(value && value.toString())) {
        value = extractNumbers(value)[0]
        console.log(value)
    }

    // 输入为数字
    if (isNumberString(value)) {
        // ref为范围
        if (isObject(item.ref)) {
            const min = isNumberString(item.ref.min) ? item.ref.min : ''
            const max = isNumberString(item.ref.max) ? item.ref.max : ''

            if (isNumberString(min) && max === '') {
                return transNumberToEnum(value - min)
            } else if (isNumberString(max) && min === '') {
                return transNumberToEnum(value - max)
            } else if (isNumberString(max) && isNumberString(min)) {
                return value - min < 0
                    ? EXAMINATION_RESULT_ENUM.DOWN
                    : value - max > 0
                        ? EXAMINATION_RESULT_ENUM.UP
                        : EXAMINATION_RESULT_ENUM.NONE
            }
        } else if (isNumberString(item.ref)) {
            return transNumberToEnum(value - item.ref)
        }
        return EXAMINATION_RESULT_ENUM.NONE
    }
    return EXAMINATION_RESULT_ENUM.NONE
}

// 月经婚育史 转成str
export function getObstetricalHistoryStr(list) {
    return list
        .map((it) => {
            if (typeof it === 'object') {
                if (it.type === 'pregnant') {
                    return (
                        `<span class="pregnant">孕 ${it.pregnantCount} ` +
                        `产 ${it.birthCount}</span>`
                    )
                }
                if (it.type === 'menstruation') {
                    let $str = ''
                    if (it.menopauseAge) {
                        $str += `${it.menopauseAge || ''}`
                    } else {
                        $str += `LMP ${it.menopauseDate}`
                    }
                    const menstruationDays = formatRangeStr(it.menstruationDays)
                    const menstrualCycle = formatRangeStr(it.menstrualCycle)
                    return `<span class="menstruation">
                            ${it.menophaniaAge || ''}
                            <span>
                                <span>${menstruationDays}</span>
                                <span class="frasl"></span>
                                <span>${menstrualCycle}</span>
                            </span>
                            ${$str}
                        </span>`
                }
                return ''
            }
            return it
        })
        .join('，')
}

// 格式化 月经婚育史
export function formatObstetricalHistory2Str(obstetricalHistory) {
    if (!obstetricalHistory) return ''
    if (isJSON(obstetricalHistory)) {
        return getObstetricalHistoryStr(JSON.parse(obstetricalHistory))
    }

    return obstetricalHistory
}

export const ToothQuadrant = Object.freeze({
    TOP_LEFT: [11, 12, 13, 14, 15, 16, 17, 18, 51, 52, 53, 54, 55],
    TOP_RIGHT: [21, 22, 23, 24, 25, 26, 27, 28, 61, 62, 63, 64, 65],
    BOTTOM_LEFT: [41, 42, 43, 44, 45, 46, 47, 48, 81, 82, 83, 84, 85],
    BOTTOM_RIGHT: [31, 32, 33, 34, 35, 36, 37, 38, 71, 72, 73, 74, 75],
})
export const ToothChildMap = Object.freeze({
    1: 'A',
    2: 'B',
    3: 'C',
    4: 'D',
    5: 'E',
})
export function getToothNosInfo(arr) {
    arr = arr || []
    const topLeft = []
    const bottomLeft = []
    const topRight = []
    const bottomRight = []
    arr.forEach((it) => {
        if (ToothQuadrant.TOP_LEFT.indexOf(it) > -1) {
            topLeft.push(it % 10)
        } else if (ToothQuadrant.TOP_RIGHT.indexOf(it) > -1) {
            topRight.push(it % 10)
        } else if (ToothQuadrant.BOTTOM_LEFT.indexOf(it) > -1) {
            bottomLeft.push(it % 10)
        } else if (ToothQuadrant.BOTTOM_RIGHT.indexOf(it) > -1) {
            bottomRight.push(it % 10)
        }
    })
    let isChildTooth = false
    // FDI牙位表示法，大于48代表乳牙
    if (Math.max(...arr) > 48) {
        isChildTooth = true
    }
    const topLength = topLeft.length + topRight.length
    const bottomLength = bottomLeft.length + bottomRight.length
    const isTopAll = bottomLength === 0 && (isChildTooth ? topLength === 10 : topLength === 16)
    const isBottomAll =
        topLength === 0 && (isChildTooth ? bottomLength === 10 : bottomLength === 16)

    const isAll = isChildTooth ? arr.length === 20 : arr.length === 32

    let maxQuadrant = topLeft
    if (topRight.length > maxQuadrant.length) {
        maxQuadrant = topRight
    }
    if (bottomLeft.length > maxQuadrant.length) {
        maxQuadrant = bottomLeft
    }
    if (bottomRight.length > maxQuadrant.length) {
        maxQuadrant = bottomRight
    }

    // 医保局规定: 全口、上半口、下半口都要展示出全部牙位
    // let maxQuadrantWidth = 42;
    // if(!isAll && !isTopAll && !isBottomAll) {
    //     const length = maxQuadrant.length > 2 ? maxQuadrant.length : 2;
    //     // 单边单个字宽度8.5；整个上下半区*2
    //     let numWidth = 8;
    //     if(isChildTooth) {
    //         numWidth = 10;
    //     }
    //     maxQuadrantWidth = length * numWidth * 2 + numWidth;
    // }

    const length = maxQuadrant.length > 2 ? maxQuadrant.length : 2
    // 单边单个字宽度8.5；整个上下半区*2
    let numWidth = 8
    if (isChildTooth) {
        numWidth = 10
    }
    const maxQuadrantWidth = length * numWidth * 2 + numWidth

    return {
        isAll, // 全口
        isTopAll, // 上半口
        isBottomAll, // 下半口
        isChildTooth,
        maxQuadrant,
        topLeft,
        bottomLeft,
        topRight,
        bottomRight,
        maxQuadrantWidth, // 最长牙位宽度
    }
}
// 诊断
export function formatDentistry2Html(arr) {
    if (!arr) return ''
    let str = ''
    arr.forEach((item) => {
        let toothNosHtml = ''
        if (item.toothNos && item.toothNos.length) {
            toothNosHtml += formatToothNos2Html(item.toothNos)
        }

        if (Array.isArray(item.value)) {
            if (item.value.length) {
                str += `<div>${toothNosHtml}`
                str += item.value.map((it) => it.name).join('，')
                str += '</div>'
            }
        } else {
            if (item.value) {
                str += `<div>${toothNosHtml}${item.value}</div>`
            }
        }
    })
    return str
}

/**
 * 获取全部/西药/中药诊断
 * @param {Object[]} arr
 * @param {number} type 0:全部诊断 1:西药诊断 2:中药诊断
 * @return {string}
 */
export function formatDentistry2HtmlByWesternOrChinese(arr, type = 0) {
    if (!arr) return '';
    let str = '';
    arr.forEach((item) => {
        let toothNosHtml = '';
        if (item.toothNos && item.toothNos.length) {
            toothNosHtml += formatToothNos2Html(item.toothNos);
        }

        if (Array.isArray(item.value)) {
            if (item.value.length) {
                str += `<div>${toothNosHtml}`;
                str += item.value.filter((it) => {
                    if (type === 1) return !it.hint?.includes?.('中医');
                    if (type === 2) return !!it.hint?.includes?.('中医');
                    return true;
                }).map((it) => it.name).join('，');
                str += '</div>';
            }
        } else {
            if (item.value) {
                str += `<div>${toothNosHtml}${item.value}</div>`;
            }
        }
    })
    return str;
}
// 眼部检查
export function formatEyeExamination2Html(arr) {
    if (!arr) return ''
    let str = ''
    arr.forEach((item) => {
        if (item.name && (item.rightEyeValue || item.leftEyeValue)) {
            str += `<div>${item.name}：${item.rightEyeValue ? '右眼（OD）' : ''}${item.rightEyeValue}${item.rightEyeValue ? '；' : ''}${item.leftEyeValue ? '左眼（OS）' : ''}${item.leftEyeValue}</div>`
        }
    })
    return str
}

export function formatToothNos2Html(toothNos, customWidth) {
    if (!toothNos) return ''
    const {
        // isAll,
        // isTopAll,
        // isBottomAll,
        isChildTooth,
        topLeft,
        bottomLeft,
        topRight,
        bottomRight,
        maxQuadrantWidth,
    } = getToothNosInfo(toothNos)
    // 医保局规定: 全口、上半口、下半口都要展示出全部牙位
    // if(isAll || isTopAll || isBottomAll) {
    //     let str = '';
    //     if(isAll) {
    //         str = '全口';
    //     } else {
    //         if(isTopAll) {
    //             str = '上半口';
    //         }
    //         if(isBottomAll) {
    //             str = '下半口';
    //         }
    //     }
    //     return `
    //     <div class="global-tooth-selected-quadrant all-tooth" style="width: ${customWidth ? `${customWidth}px` : `${maxQuadrantWidth}px`}">
    //         <div class="all-tooth" >${str}</div>
    //     </div>
    // `;
    // }

    let _topLeft = topLeft.sort((a, b) => b - a).slice()
    let _bottomLeft = bottomLeft.sort((a, b) => b - a).slice()
    let _topRight = topRight.sort((a, b) => a - b).slice()
    let _bottomRight = bottomRight.sort((a, b) => a - b).slice()

    if (isChildTooth) {
        _topLeft = _topLeft.map((it) => ToothChildMap[it])
        _bottomLeft = _bottomLeft.map((it) => ToothChildMap[it])
        _topRight = _topRight.map((it) => ToothChildMap[it])
        _bottomRight = _bottomRight.map((it) => ToothChildMap[it])
    }
    return `
        <div class="global-tooth-selected-quadrant">
            <div class="top-tooth" style="width: ${customWidth ? `${customWidth}px` : `${maxQuadrantWidth}px`}">
                <div class="left-tooth">${_topLeft.join('')}</div>
                <div class="right-tooth">${_topRight.join('')}</div>
            </div>
            <div class="bottom-tooth" style="width: ${customWidth ? `${customWidth}px` : `${maxQuadrantWidth}px`}">
                <div class="left-tooth">${_bottomLeft.join('')}</div>
                <div class="right-tooth">${_bottomRight.join('')}</div>
            </div>
        </div>
    `
}

export function isJSON(str) {
    if (typeof str == 'string') {
        try {
            const obj = JSON.parse(str)
            return typeof obj == 'object' && obj
        } catch (e) {
            return false
        }
    }
}

export function formatRangeStr(range) {
    if (range[0] === range[1]) return range[0]
    return range.join('~')
}

/**
 * @desc 格式化流行病史
 * <AUTHOR> Yang
 * @date 2020-12-09 17:16:32
 */
export function formatEpidemiologicalHistory2Str(epidemiologicalHistory) {
    if (!epidemiologicalHistory) return ''

    if (isJSON(epidemiologicalHistory)) {
        let currentObj = {
            patientChecked: false,
            attendantChecked: false,
            suspiciousList: [],
            symptomList: [],
        }
        const valObj = JSON.parse(epidemiologicalHistory)
        if (Array.isArray(valObj)) {
            currentObj.symptomList = valObj
        } else {
            currentObj = valObj
        }
        return getEpidemiologicalHistoryStr(currentObj)
    }

    return epidemiologicalHistory
}

export function getEpidemiologicalHistoryStr(currentObj) {
    let str = ''
    const { suspiciousList = [], symptomList } = currentObj

    symptomList.forEach((item) => {
        str += item.label
        if (item.isSuspicious) {
            if (suspiciousList && suspiciousList.length) {
                str += `${suspiciousList
                    .map((it) => {
                        return `${it.label}（${it.value}）`
                    })
                    .join('，')}`
                const hasActive = suspiciousList.some((it) => it.value === '有')
                if (hasActive) {
                    str += '，需密切关注和引导新冠肺炎排查'
                }
                str += '。'
            } else {
                str += item.label ? '：' : ''
            }
        } else {
            // 兼容老数据
            if (item.value === 1) {
                str += '（是）；'
            } else if (item.value === 0) {
                str += '（否）；'
            } else if (item.value) {
                str += `（${item.value}）；`
            } else if (str && !item.value) {
                str += str.match(/[;；]$/) ? '' : '；'
            }
        }
    })
    return str
}

/**
 * @desc 格式化口腔检查
 * <AUTHOR> Yang
 * @date 2020-10-15 08:42:35
 * @params for2String: true 是否转成 string 格式 类似#11, 默认转换成html
 */
export function formatOralExamination2Html(oralExamination, for2String = false) {
    if (!oralExamination) return ''
    if (isJSON(oralExamination)) {
        const _list = JSON.parse(oralExamination)
        if (for2String) {
            return _trans2Str(_list)
        }
        return _trans2Html(_list)
    }
    return oralExamination
}

export function _trans2Str(list) {
    const _list = list.map((item) => {
        let str = item
        if (typeof item === 'object') {
            str = ''
            item.positions.forEach((pos) => {
                pos.dataNo.forEach((no) => {
                    str += `${str ? ' ' : ''}#${positionsStrObj[pos.position]}${no}`
                })
            })
            str += ` ${item.describes.join(' ')}`
        }
        return str.trim()
    })
    return _list.join('，')
}

export function _trans2Html(list) {
    const _list = list.map((item) => {
        let str = item
        if (typeof item === 'object') {
            str = ''
            item.positions.forEach((pos) => {
                str += `<span class="${pos.position}">${pos.dataNo.join(' ')} </span>`
            })
            str += ` ${item.describes.join(' ')}`
        }
        return str.trim()
    })
    return _list.join('，')
}

// 格式化穴位
export function formatAcupoints(acupoints) {
    if (!acupoints || !acupoints.length) {
        return ''
    }
    let res = []
    acupoints.forEach((item) => {
        res.push(`[${item.position}]${item.name}`)
    })
    return res.join('、')
}

// 检查西药处方中是否存在组号
export function checkExistedGroupId(form) {
    if (
        form.prescriptionFormItems &&
        form.prescriptionFormItems.length &&
        Array.isArray(form.prescriptionFormItems[0])
    ) {
        return form.prescriptionFormItems.some((item) =>
            item.some((it) => it.groupId !== null && it.groupId !== undefined),
        )
    }
    const index = form.prescriptionFormItems.findIndex((item) => {
        return item.groupId !== null && item.groupId !== undefined
    })
    return index > -1
}

/**
 * @desc 皮试文案转换
 * <AUTHOR>
 * @date 2022-04-20 15:13:02
 */
export function formatAst(ast) {
    let str = ''
    switch (ast) {
    case AstEnum.PI_SHI:
        str = '皮试'
        break
    case AstEnum.XU_YONG:
        str = '续用'
        break
    case AstEnum.MIAN_SHI:
        str = '免试'
        break
    default:
        str = ''
        break
    }
    return str
}

/**
 * @desc 转换皮试结果
 * <AUTHOR>
 * @date 2022-11-10 14:10:15
 * @params
 * @return
 */
export function formatAstResult(astResult) {
    if (!astResult || !astResult.result) {
        return ' '
    }
    if (astResult.result === '阴性') {
        return '-'
    } else {
        return '+'
    }
}

export function transformProductUnit(form, formItem) {
    return form.sourceFormType === SourceFormTypeEnum.TREATMENT ||
        form.sourceFormType === SourceFormTypeEnum.EXAMINATION ||
        form.sourceFormType === SourceFormTypeEnum.OTHER
        ? formItem.unit
            ? formatTreatmentUnit(formItem.unit, '*')
            : '次'
        : formItem.unit
}

export function formatToothNos2Text(toothNos) {
    if (!toothNos) return ''
    const { isAll, isTopAll, isBottomAll } = getToothNosInfo(toothNos)
    if (isAll || isTopAll || isBottomAll) {
        let str = ''
        if (isAll) {
            str = '全口'
        } else {
            if (isTopAll) {
                str = '上半口'
            }
            if (isBottomAll) {
                str = '下半口'
            }
        }
        return str
    }
    return toothNos
        .slice()
        .sort((a, b) => a - b)
        .join('、')
}
export function formatDentistry2Text(arr) {
    if (!arr) return ''
    const result = []
    arr.forEach((item) => {
        if (item.value) {
            let str = ''
            if (item.toothNos && item.toothNos.length) {
                str += `${formatToothNos2Text(item.toothNos)} `
            }
            if (Array.isArray(item.value)) {
                str += item.value.map((it) => it.name).join('，')
            } else {
                str += item.value
            }
            result.push(str)
        }
    })
    return result.join('，')
}

export function getUsageTypeStr(externalForm) {
    const { usageType, usageSubType } = externalForm;
    const { label: typeStr } = UsageTypeOptions.find(it => it.value === usageType) || {};
    let usageSubTypeOptions = [];
    switch (usageType) {
    case ExternalPRUsageTypeEnum.tieFu:
        usageSubTypeOptions = TieFuSubOptions;
        break;
    case ExternalPRUsageTypeEnum.zhenCi:
        usageSubTypeOptions = ZhenCiSubOptions;
        break;
    case ExternalPRUsageTypeEnum.aiJiu:
        usageSubTypeOptions = AiJiuSubOptions;
        break;
    case ExternalPRUsageTypeEnum.baGuan:
        usageSubTypeOptions = BaGuanSubOptions;
        break;
    case ExternalPRUsageTypeEnum.tuiNa:
        usageSubTypeOptions = TuiNaSubOptions;
        break;
    }
    const { label: subTypeStr } = usageSubTypeOptions.find(it => it.value === usageSubType) || {};
    if (subTypeStr) {
        return subTypeStr;
    }
    return typeStr;
}

export function isBasedOnAcupoint(form, item) {
    return [
        ExternalPRUsageTypeEnum.tieFu,
        ExternalPRUsageTypeEnum.zhenCi,
        ExternalPRUsageTypeEnum.aiJiu,
    ].includes(item.usageType ?? form.usageType);
}

export function getExternalTypeStr(from, item) {
    const isAllAcupoint = item.acupoints.every((it) => it.acupointType === ExternalAcupointTypeEnum.ACUPOINT);
    if(isAllAcupoint) {
        return '穴位：';
    }
    const isAllBodyPosition = item.acupoints.every((it) => it.acupointType === ExternalAcupointTypeEnum.BODY_POSITION);
    if(isAllBodyPosition) {
        return '部位：';
    }
    return isBasedOnAcupoint(from, item) ? '穴位：' : '部位：'
}

export function calcDosageCount(item) {
    if (item.acupointUnitCount && item.acupointUnit) {
        return item.acupointUnitCount;
    }
    let count = 0;
    (item.acupoints || []).forEach((it) => {
        if (it.name) {
            if (isNumber(it.position)) {
                count += parseFloat(it.position);
            } else if (it.position === '双') {
                count += 2;
            } else if (it.position !== '-') {
                count++;
            }
        }
    });
    return count || 1;
}

/**
 * 姓名脱敏
 * @param name {string}
 * @param formatType {number} 1:脱敏 2:不脱敏
 * @return {string}
 */
export function filterName(name, formatType = 1) {
    const value = name.trim()
    if (!value) return ''

    if (formatType === 1) {
        if(value.length === 2) {
            return '*' + value.slice(1)
        }
        return value.slice(0, 1) + '*' + value.slice(-1);
    }
    return value
}
