<template>
  <div>
    <div data-type="header" data-pendants-index="1">
      header1
    </div>
    <div data-type="mix-box">
      <ul class="body" data-type="group">
        <li class="title">
          页面头部1
        </li>
        <li data-type="item">内容开始-------------</li>
        <li data-type="item">内容1</li>
        <li data-type="item">内容2</li>
        <li data-type="item">内容3</li>
        <li data-type="item">内容4</li>
        <li data-type="item">内容5</li>
        <li data-type="item">内容6</li>
        <li data-type="item">内容7</li>
        <li data-type="item">内容8</li>
        <li data-type="item">内容9</li>
        <li data-type="item">内容10</li>
        <li data-type="item">内容11</li>
        <li data-type="item">内容12</li>
        <li data-type="item">内容13</li>
        <li data-type="item">内容结束------------</li>
      </ul>
    </div>

    <div data-type="footer" data-pendants-index="1">
      footer1
    </div>

    <div data-type="header" class="header2" data-pendants-index="2">
      header2
    </div>

    <div data-type="mix-box">
      <ul class="body" data-type="group">
        <li class="title">
          页面头部2
        </li>
        <li data-type="item">内容开始-------------</li>
        <li data-type="item">内容1</li>
        <li data-type="item">内容2</li>
        <li data-type="item">内容3</li>
        <li data-type="item">内容4</li>
        <li data-type="item">内容5</li>
        <li data-type="item">内容6</li>
        <li data-type="item">内容7</li>
        <li data-type="item">内容8</li>
        <li data-type="item">内容9</li>
        <li data-type="item">内容10</li>
        <li data-type="item">内容11</li>
        <li data-type="item">内容12</li>
        <li data-type="item">内容结束------------</li>
      </ul>
    </div>
    <div data-type="footer" data-pendants-index="2">
      footer2
    </div>


    <div data-type="header" class="header2" data-pendants-index="3">
      header3
    </div>

    <div data-type="mix-box">
      <ul class="body" data-type="group">
        <li class="title">
          页面头部3
        </li>
        <li data-type="item">内容开始-------------</li>
        <li data-type="item">内容1</li>
        <li data-type="item">内容2</li>
        <li data-type="item">内容3</li>
        <li data-type="item">内容4</li>
        <li data-type="item">内容5</li>
        <li data-type="item">内容6</li>
        <li data-type="item">内容7</li>
        <li data-type="item">内容8</li>
        <li data-type="item">内容9</li>
        <li data-type="item">内容10</li>
        <li data-type="item">内容11</li>
        <li data-type="item">内容12</li>
        <li data-type="item">内容结束------------</li>
      </ul>
    </div>
    <div data-type="footer" data-pendants-index="3">
      footer3
    </div>

    <div data-type="fixed-bottom-box" style="height: 100px;background: #eee;">
        底部固定区域
    </div>
  </div>
</template>

<script>
import CKHandler from '../data-handler/CK-handler.js'
import PageSizeMap from "../../share/page-size.js";
import {PrintBusinessKeyEnum} from "../constant/print-constant.js";

export default {
  name: "MultiHeaderPage",
  DataHandler: CKHandler,
  pageConfig: {
    name: 'MultiHeaderPage',
    size: '*'
  },
  pages: [
    {
      paper: PageSizeMap.A5,
      isRecommend: false,
    },
  ],
  props: {
    renderData: {
      type: Object,
      default() {
        return {}
      }
    },
  },
}
</script>
<style lang="scss">
@import "../style/inventory-common";

.body > li {
  line-height: 55px;
}

</style>
