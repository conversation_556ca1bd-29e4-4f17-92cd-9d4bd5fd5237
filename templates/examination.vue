<template>
    <div>
        <outpatient-header
            :print-data="printData"
            :print-title="printTitle"
            :config="config"
            :organ-title="organTitle"
            data-type="header"
            :source-type="ExecuteHeaderSourceType.examination"
        ></outpatient-header>

        <template v-if="!printData.title && examinationInspectFormItems.length">
            <table
                data-type="mix-box"
                class="print-examination-table"
            >
                <thead>
                    <tr>
                        <td
                            colspan="4"
                            class="item-name"
                        >
                            项目
                        </td>
                        <td
                            colspan="1"
                            class="right-text"
                            :class="{ hidden: !contentConfig.productUnitPrice}"
                            style="padding-right: 16pt"
                        >
                            单价
                        </td>
                        <td
                            style="padding-right: 8pt;"
                            colspan="2"
                            :class="{ hidden: !isPrintExaminationSheetNo}"
                        >
                            检验单号
                        </td>
                        <td
                            style="padding-right: 8pt;"
                            colspan="2"
                            :class="{ hidden: !isPrintExaminationApplySheetNo}"
                        >
                            申请单号
                        </td>
                        <td
                            colspan="1"
                            class="right-text"
                            style="padding-right: 16pt"
                        >
                            数量
                        </td>
                        <td
                            colspan="1"
                            overflow
                        >
                            单位
                        </td>
                    </tr>
                </thead>
                <tbody data-type="group">
                    <template v-for="(item, index) in examinationInspectFormItems">
                        <tr
                            :key="`${item.name }inspect-item`"
                            :class="{'has-remark-tr': item.remark}"
                            data-type="item"
                        >
                            <td colspan="4">
                                <div
                                    class="item-name"
                                    overflow
                                >
                                    <span v-if="item.isChild && contentConfig.composeChildren === 2">*</span>
                                    <span
                                        v-else
                                        class="item-number"
                                    >{{ index + 1 }}.</span>
                                    <span
                                        v-if="item.toothNos && item.toothNos.length"
                                        v-html="formatToothNos2Html(item.toothNos)"
                                    ></span>
                                    {{ item.name }}
                                    <span
                                        v-if="item.remark"
                                        class="item-remark"
                                    >
                                        {{ item.remark }}
                                    </span>
                                </div>
                            </td>
                            <td
                                :class="{ hidden: !contentConfig.productUnitPrice}"
                                colspan="1"
                                class="right-text"
                                style="padding-right: 16pt"
                            >
                                {{ item.unitPrice | formatMoney }}
                            </td>
                            <td
                                colspan="2"
                                style="padding-right: 8pt;word-break: break-all;"
                                :class="{ hidden: !isPrintExaminationSheetNo}"
                            >
                                {{ item.orderNo }}
                            </td>
                            <td
                                colspan="2"
                                style="padding-right: 8pt;word-break: break-all;"
                                :class="{ hidden: !isPrintExaminationApplySheetNo}"
                            >
                                {{ item.examinationApplySheetNo }}
                            </td>
                            <td
                                colspan="1"
                                class="right-text"
                                style="padding-right: 16pt;"
                            >
                                {{ item.unitCount }}
                            </td>
                            <td
                                colspan="1"
                                overflow
                            >
                                {{ item.unit }}
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
            <div data-type="new-page"></div>
        </template>

        <template v-if="!extra.isPreview && printData.title && examinationTestFormItems.length">
            <table
                data-type="mix-box"
                class="print-examination-table"
            >
                <thead>
                    <tr>
                        <td
                            colspan="4"
                            class="item-name"
                        >
                            项目
                        </td>
                        <td
                            :class="{ hidden: !contentConfig.productUnitPrice}"
                            colspan="1"
                        >
                            单价
                        </td>
                        <td
                            colspan="2"
                            style="padding-right: 8pt"
                            :class="{ hidden: !isPrintExaminationSheetNo}"
                        >
                            检查单号
                        </td>
                        <td
                            colspan="2"
                            style="padding-right: 8pt"
                            :class="{ hidden: !isPrintExaminationApplySheetNo}"
                        >
                            申请单号
                        </td>
                        <td colspan="1">
                            数量
                        </td>
                        <td colspan="1">
                            单位
                        </td>
                    </tr>
                </thead>
                <tbody data-type="group">
                    <tr
                        v-for="(item, index) in examinationTestFormItems"
                        :key="`${item.name }test-item`"
                        data-type="item"
                    >
                        <td colspan="4">
                            <div
                                class="item-name"
                                overflow
                            >
                                <span v-if="item.isChild && contentConfig.composeChildren === 2">*</span>
                                <span
                                    v-else
                                    class="item-number"
                                >{{ index + 1 }}.</span>
                                <span
                                    v-if="item.toothNos && item.toothNos.length"
                                    v-html="formatToothNos2Html(item.toothNos)"
                                ></span>
                                {{ item.name }}
                            </div>
                            <div
                                class="item-name"
                                overflow
                            >
                                {{ item.remark }}
                            </div>
                        </td>
                        <td
                            :class="{ hidden: !contentConfig.productUnitPrice}"
                            colspan="1"
                        >
                            {{ item.unitPrice | formatMoney }}
                        </td>
                        <td
                            colspan="2"
                            style="padding-right: 8pt;word-break: break-all;"
                            :class="{ hidden: !isPrintExaminationSheetNo}"
                        >
                            {{ item.orderNo }}
                        </td>
                        <td
                            colspan="2"
                            style="padding-right: 8pt;word-break: break-all;"
                            :class="{ hidden: !isPrintExaminationApplySheetNo}"
                        >
                            {{ item.examinationApplySheetNo }}
                        </td>
                        <td colspan="1">
                            {{ item.unitCount }}
                        </td>
                        <td colspan="1">
                            {{ item.unit }}
                        </td>
                    </tr>
                </tbody>
            </table>
        </template>

        <outpatient-footer
            :print-data="printData"
            data-type="footer"
            print-type="examination"
            :config="config"
        ></outpatient-footer>
    </div>
</template>

<script>
    import OutpatientHeader from './components/medical-document-header/execute-header.vue';
    import OutpatientFooter from './components/medical-document-footer/index.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { GoodsSubTypeEnum, GoodsTypeEnum } from "./common/constants.js";
    import { clone, formatMoney } from "./common/utils.js";
    import { formatToothNos2Html } from "./common/medical-transformat.js";
    import examinationHandler from "./data-handler/examination-handler";
    import { ExecuteHeaderSourceType } from './components/medical-document-header/constant'

    const SHEET_NO_PRINT_ENUM = {
        notPrint: 0,
        examSheetNo: 1,
        examApplySheetNo: 2,
    }

    export default {
        DataHandler: examinationHandler,
        name: "Examination",
        components: {
            OutpatientHeader,
            OutpatientFooter,
        },
        filters: {
            formatMoney,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
            extra: {
                type: Object,
                default() {
                    return {}
                },
            },
        },
        businessKey: PrintBusinessKeyEnum.EXAMINATION,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM95_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分', // 默认选择的等分纸
            },
        ],
        computed: {
            ExecuteHeaderSourceType() {
                return ExecuteHeaderSourceType
            },
            printData() {
                return this.renderData.printData || {};
            },
            config() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.examination) {
                    return this.renderData.config.medicalDocuments.examination;
                }
                return {};
            },
            headerConfig() {
                return this.config.header || {};
            },
            contentConfig() {
                return this.config && this.config.content || {};
            },
            examinationFormItems() {
                const formItems = clone(this.printData && this.printData.examinationFormItems || []);

                if(this.isPrintExaminationSheetNo || this.isPrintExaminationApplySheetNo) {
                    const examinationTestFormItems = formItems.filter(
                        (item) => item.type === GoodsTypeEnum.EXAMINATION &&
                            [GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect, GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test].includes(item.subType),
                    );
                    const composeFormItems = formItems.filter((item) => item.type === GoodsTypeEnum.COMPOSE);
                    let resultFormItems = [];

                    resultFormItems = this.covertFormItems(examinationTestFormItems);

                    composeFormItems.forEach((item) => {
                        const { composeChildren } = item;
                        item.composeChildren = this.covertFormItems(composeChildren || []);
                    });

                    resultFormItems = resultFormItems.concat(composeFormItems);
                    return resultFormItems;
                }

                return formItems;
            },
            printTitle() {
                return this.printData.title ? '检查单' : '检验单'
            },
            // 检查项目
            examinationTestFormItems() {
                const cacheExaminationFormItems = this.examinationFormItems.filter( item => {
                    return item.type === GoodsTypeEnum.EXAMINATION &&
                        item.subType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test || item.type === GoodsTypeEnum.COMPOSE;
                })
                const res = [];

                cacheExaminationFormItems.forEach((item) => {
                    if (item.type === GoodsTypeEnum.COMPOSE) {
                        // 要展示套餐母项
                        if (this.contentConfig.composeChildren !== 1) {
                            res.push({
                                ...item,
                                isCompose: true,
                            });
                        }
                        // 要展示套餐子项
                        if (this.contentConfig.composeChildren) {
                            const childJoinResultList = item.composeChildren || [];
                            childJoinResultList.forEach((item) => item.isChild = true)
                            res.push(...childJoinResultList);
                        }
                    } else {
                        res.push(item);
                    }
                });
                return res;
            },
            // 检验项目
            examinationInspectFormItems() {
                const cacheExaminationFormItems = this.examinationFormItems.filter( item => {
                    return item.type === GoodsTypeEnum.EXAMINATION && item.subType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect || item.type === GoodsTypeEnum.COMPOSE;
                })
                const res = [];
                cacheExaminationFormItems.forEach((item) => {
                    if (item.type === GoodsTypeEnum.COMPOSE) {
                        // 要展示套餐母项
                        if (this.contentConfig.composeChildren !== 1) {
                            res.push({
                                ...item,
                                isCompose: true,
                            });
                        }
                        // 要展示套餐子项
                        if (this.contentConfig.composeChildren) {
                            const childJoinResultList = item.composeChildren || [];
                            childJoinResultList.forEach((item) => item.isChild = true)
                            res.push(...childJoinResultList);
                        }
                    } else {
                        res.push(item);
                    }
                });
                return res;
            },
            organ() {
                return this.printData.organ;
            },
            organTitle() {
                return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.examination || '';
            },

            isPrintExaminationSheetNo() {
                return this.contentConfig.examineNo === SHEET_NO_PRINT_ENUM.examSheetNo
            },

            isPrintExaminationApplySheetNo() {
                return this.contentConfig.examineNo === SHEET_NO_PRINT_ENUM.examApplySheetNo
            },
        },
        methods: {
            formatToothNos2Html,

            /**
             * @return {*[]}
             */
            covertFormItems(formItems = []) {
                let result = formItems.reduce((res, formItem) => {
                    // 如果设置开单后才可执行检验，examSheetSimpleViews 在门诊为空，单号没有但是需要项目信息
                    const examSheetSimpleViews = formItem.examSheetSimpleViews || [{}];
                    const joinResultList = examSheetSimpleViews.map(e => ({
                        ...formItem,
                        orderNo: e.orderNo || '',
                        examinationApplySheetNo: e.examinationApplySheetNo || '',
                        unitCount: e.orderNo ? 1 : formItem.unitCount,
                        name: e.name || formItem.name,
                    }));
                    res.push(...joinResultList);
                    return res;
                }, []);

                console.debug(result, '拼接的结果');

                return result;
            },
        },
    }
</script>

<style lang="scss">
@import "./style/reset.scss";
@import "./components/layout/print-layout.scss";

.abc-page-content{
    padding: 8pt;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "微软雅黑";
}

.print-examination-table {
    width: 100%;
    font-size: 10pt;
    line-height: 12pt;
    table-layout: fixed;
    margin-top: 6pt;

    .has-remark-tr {
        td{
            padding-bottom: 2pt;
        }
    }
    td {
        padding-bottom: 6pt;
        // word-break: keep-all;
        // white-space: nowrap;
        // overflow: hidden;
        &.remark-td{
            padding-top: 0;
        }
    }


    .item-name {
        position: relative;
        padding-left: 18pt;

        .item-number {
            position: absolute;
            top: 0;
            left: 0;
        }
    }
  .item-remark{
    padding-left: 6pt;
  }
    .right-text{
        padding-right: 4pt;
        text-align: right;
    }
    .hidden {
      display: none;
    }
}


.global-tooth-selected-quadrant {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    width: auto;
    min-width: 32pt;
    height: 20pt;
    vertical-align: middle;

    .top-tooth,
    .bottom-tooth {
        display: flex;
        align-items: center;
        width: 100%;
        min-width: 32pt;
    }

    .left-tooth,
    .right-tooth {
        display: flex;
        align-items: center;
        width: 50%;
        height: 9pt;
        padding: 0 1pt;
        font-family: 'MyKarlaRegular';
        font-size: 9pt;
        letter-spacing: 1px;
        user-select: none;
    }

    .left-tooth {
        justify-content: flex-end;
        border-right: 1pt solid #7a8794;
    }

    .top-tooth {
        min-width: 32pt;
        border-bottom: 1pt solid #7a8794;

        > div {
            padding-bottom: 1px;
        }
    }

    .bottom-tooth {
        > div {
            padding-top: 1px;
        }
    }

    &.all-tooth {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }

    .all-tooth {
        min-width: 18pt;
        height: 11pt;
        font-size: 9pt;
        line-height: 11pt;
    }

    &.no-data {
        .left-tooth {
            border-right: 1px dashed #000000;
        }

        .top-tooth {
            border-bottom: 1px dashed #000000;
        }
    }
}

</style>
