<!--exampleData
{
        "id": "*********",
        "type": 2,
        "orderNo": "BS2024112000007",
        "settlementOrderId": null,
        "settlementStatus": null,
        "settlementOrder": null,
        "applyClinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "toUser": null,
        "outDate": null,
        "status": 1,
        "isConfirm": 1,
        "kindCount": 1,
        "comment": [],
        "externalFlag": 0,
        "list": [
            {
                "batchId": null,
                "goods": {
                    "goodsVersion": 0,
                    "id": "122afd48191f6ff99c9bb7879acf3a42",
                    "goodsId": "122afd48191f6ff99c9bb7879acf3a42",
                    "shortId": "1269407161",
                    "status": 1,
                    "py": "0.9%LHNZSY|0.9%LHNZYY|0.9%lvhuanazhusheye|0.9%lvhuanazhuyeye|0.9%lv<PERSON><PERSON><PERSON><PERSON>yiye",
                    "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
                    "type": 1,
                    "subType": 1,
                    "barCode": "1",
                    "manufacturer": "四川科伦",
                    "manufacturerFull": "四川科伦药业股份有限公司",
                    "medicineCadn": "0.9%氯化钠注射液",
                    "medicineNmpn": "国药准字H51021156",
                    "medicineDosageNum": 2.00000,
                    "medicineDosageUnit": "mg",
                    "specType": 0,
                    "isSell": 1,
                    "pieceUnit": "支",
                    "packageUnit": "瓶",
                    "dismounting": 1,
                    "pieceNum": 10,
                    "inTaxRat": 16.0,
                    "outTaxRat": 16.0,
                    "createdUserId": "6e45706922a74966ab51e4ed1e604641",
                    "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
                    "lastModifiedDate": "2023-12-28T10:42:23Z",
                    "disable": 0,
                    "needExecutive": 0,
                    "typeId": 12,
                    "customTypeId": "0",
                    "chainPiecePrice": 0.3500,
                    "chainPackagePrice": 3.5000,
                    "chainPackageCostPrice": 15.63636,
                    "piecePrice": 0.3500,
                    "packagePrice": 3.5000,
                    "packageCostPrice": 15.63636,
                    "v2DisableStatus": 0,
                    "displayName": "0.9%氯化钠注射液",
                    "displaySpec": "2mg*10支/瓶",
                    "combineType": 0,
                    "chainDisable": 0,
                    "chainV2DisableStatus": 0,
                    "disableSell": 0,
                    "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
                    "defaultInOutTax": 0,
                    "shebaoPayMode": 2,
                    "inspectionSite": 0,
                    "priceType": 1,
                    "expiredWarnMonths": 30
                },
                "goodsId": "122afd48191f6ff99c9bb7879acf3a42",
                "id": "*********",
                "orderId": "*********",
                "packagePrice": 3.5000,
                "piecePrice": 0.3500,
                "packageCount": 0.0000,
                "pieceCount": 1.0000,
                "pieceNum": 10,
                "type": 2,
                "externalFlag": 0,
                "stock": null,
                "stockInId": null,
                "totalCost": 1.5637,
                "totalCostE": 1.35,
                "traceableCodeList": null,
                "outReason": "过期失效，过期失效，过期失效，过期失效，过期失效",
                "packageCostPrice": 15.63636,
                "inItem": null
            }
        ],
        "pharmacy": {
            "no": 31,
            "name": "药店合作药房",
            "type": 0
        },
        "logs": [
            {
                "id": "3791",
                "action": "创建出库单",
                "detail": null,
                "comment": "",
                "creator": {
                    "id": "6e45706922a74966ab51e4ed1e604641",
                    "name": "丁柱11"
                },
                "createdDate": "2024-11-20T02:54:23Z"
            }
        ],
        "stockInOrder": null,
        "gspStatus": null,
        "gsp": null,
        "typeName": "报损出库",
        "inOrderId": null,
        "fromOrgan": {
            "id": "fff730ccc5ee45d783d82a85b8a0e52d",
            "parentId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "name": "四川省成都市高新区交子大道高新大源店",
            "shortName": "高新大原店",
            "nodeType": 2,
            "viewMode": 0,
            "hisType": 0,
            "shortNameFirst": "高新大原店"
        },
        "fromOrganId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "toOrgan": null,
        "toOrganId": null,
        "count": 0.1000,
        "amount": 1.5637,
        "amountExcludingTax": 1.3500,
        "createdUser": {
            "id": "6e45706922a74966ab51e4ed1e604641",
            "name": "丁柱11"
        },
        "createdDate": "2024-11-20T02:54:23Z",
        "lastModifiedDate": "2024-11-20T02:54:23Z"
    }
-->

<template>
    <div>
        <div data-type="header">
            <div class="order-title">
                {{ tableTitle }}
            </div>
            <print-row class="print-stock-header">
                <!--<print-col :span="8">-->
                <!--    出库门店：{{ order && order.fromOrgan.shortName || order.fromOrgan.name || '' }}-->
                <!--</print-col>-->
                <print-col
                    v-if="multiPharmacyCanUse"
                    :span="8"
                >
                    出库库房：{{ order && order.pharmacy && order.pharmacy.name || '' }}
                </print-col>
                <print-col
                    v-if="order.type === 5 || order.type === 6"
                    :span="8"
                >
                    科室：{{ order.toOrgan && order.toOrgan.name || '' }}
                </print-col>
                <print-col
                    v-if="order.type === 5 || order.type === 6"
                    :span="8"
                >
                    出库人：{{ order.toUser && order.toUser.name || '' }}
                </print-col>
                <print-col :span="8">
                    出库日期：{{ (order.typeName === '退货出库' ? order.inDate : order.outDate) | parseTime('y-m-d') }}
                </print-col>
                <print-col :span="8">
                    出库单号：{{ order && order.orderNo || '' }}
                </print-col>

                <print-col
                    v-if="order.type === 1"
                    :span="8"
                >
                    领用人：{{ order.toOrgan && order.toOrgan.name || '' }}{{ order.toUser && order.toUser.name || '' }}
                </print-col>
                <print-col
                    v-if="order.type === 10 && order.typeName === '退货出库' && order.stockInOrder && order.stockInOrder.supplier"
                    :span="8"
                >
                    供应商：{{ order.stockInOrder.supplier || '' }}
                </print-col>
                <print-col
                    v-if="order.type === 10 && order.typeName === '退货出库' && order.stockInOrder && order.stockInOrder.stockInOrderNo"
                    :span="8"
                >
                    对应系统入库单号：{{ order.stockInOrder.stockInOrderNo || ' ' }}
                </print-col>
            </print-row>
        </div>

        <div data-type="big-table">
            <table
                class="print-stock-table-wrapper"
            >
                <thead>
                    <tr class="table-title">
                        <th :colspan="2">
                            药品编码
                        </th>
                        <th :colspan="4">
                            药品名称
                        </th>
                        <th :colspan="2">
                            规格
                        </th>
                        <th :colspan="2">
                            厂家
                        </th>
                        <th :colspan="2">
                            柜号
                        </th>
                        <th :colspan="2">
                            生产批号
                        </th>
                        <th :colspan="2">
                            效期
                        </th>
                        <th
                            :colspan="2"
                            class="number-right"
                        >
                            数量
                        </th>
                        <th
                            :colspan="2"
                            class="number-right"
                        >
                            进价
                        </th>
                        <th
                            :colspan="2"
                            class="number-right"
                        >
                            无税金额
                        </th>
                        <th
                            :colspan="2"
                            class="number-right"
                        >
                            含税金额
                        </th>
                        <th
                            :colspan="2"
                            class="number-right no-right-border"
                        >
                            税率
                        </th>
                        <th 
                            v-if="showOutReason"
                            :colspan="2"
                        >
                            报损原因
                        </th>
                    </tr>
                </thead>

                <tbody>
                    <tr
                        v-for="(item) in order.list"
                        :key="item.id"
                        class="table-tr"
                    >
                        <!--            药品编码-->
                        <td :colspan="2">
                            {{ item.goods.shortId || '' }}
                        </td>

                        <!--            药品名称-->
                        <td :colspan="4">
                            <div>{{ item.goods | goodsFullName }}</div>
                        </td>

                        <!--            规格-->
                        <td :colspan="2">
                            {{ item.goods | goodsSpec }}
                        </td>

                        <!--            厂家-->
                        <td :colspan="2">
                            {{ item.goods && item.goods.manufacturer || '' }}
                        </td>

                        <!--            柜号-->
                        <td :colspan="2">
                            {{ item.goods && item.goods.position || '' }}
                        </td>

                        <!--            批号-->
                        <td :colspan="2">
                            {{ (item.stock && item.stock.batchNo) || item.batchNo || '' }}
                        </td>

                        <!--            效期-->
                        <td :colspan="2">
                            {{ formatExpiryDate((item.stock && item.stock.expiryDate) || item.expiryDate ) }}
                        </td>

                        <!--            数量-->
                        <td
                            :colspan="2"
                            class="number-right"
                        >
                            {{ item|complexCount }}
                        </td>

                        <!--            进价-->
                        <td
                            :colspan="2"
                            class="number-right"
                        >
                            <span v-if="item.inItem">
                                <data-permission-control :value="item.inItem.useUnitCostPrice">
                                    {{ moneyDigit(item.inItem.useUnitCostPrice, 5) }}/{{ item.inItem.useUnit }}
                                </data-permission-control>
                            </span>
                            <span v-else>
                                <data-permission-control :value="item.stock && item.stock.packageCostPrice || item.packageCostPrice">
                                    {{ moneyDigit(item.stock && item.stock.packageCostPrice || item.packageCostPrice, 5) }} / {{ item.goods.packageUnit || item.goods.pieceUnit }}
                                </data-permission-control>
                            </span>
                        </td>

                        <!--无税金额-->
                        <td
                            :colspan="2"
                            class="number-right"
                        >
                            <data-permission-control :value="(item.totalCostE || item.useTotalCostPriceE)">
                                {{ formatMoney(item.totalCostE || item.useTotalCostPriceE, false) }}
                            </data-permission-control>
                        </td>

                        <!--含税金额-->
                        <td
                            :colspan="2"
                            class="number-right"
                        >
                            <data-permission-control :value="(item.totalCost || item.useTotalCostPrice)">
                                {{ formatMoney(item.totalCost || item.useTotalCostPrice, false) }}
                            </data-permission-control>
                        </td>

                        <!--税率-->
                        <td
                            :colspan="2"
                            class="number-right no-right-border"
                        >
                            {{ item.goods.inTaxRat }}%
                        </td>

                        <!--报损原因-->
                        <td
                            v-if="showOutReason"
                            :colspan="2"
                        >
                            {{ item.outReason || '' }}
                        </td>
                    </tr>
                </tbody>
            </table>

            <print-row>
                <print-row data-last-page="LastPage">
                    <print-col :span="24">
                        备注：{{ order && order.comment || '' }}
                    </print-col>
                </print-row>
                <print-row>
                    <print-col :span="8">
                        合计金额(大写)：
                        <data-permission-control :value="order.amount">
                            {{ digitUppercase(order.amount) }}
                        </data-permission-control>
                    </print-col>
                    <print-col :span="5">
                        合计金额：
                        <data-permission-control :value="order.amount">
                            {{ formatMoney(order.amount, false) }}
                        </data-permission-control>
                    </print-col>
                    <print-col :span="4">
                        无税合计：
                        <data-permission-control :value="order.amountExcludingTax">
                            {{ formatMoney(order.amountExcludingTax, false) }}
                        </data-permission-control>
                    </print-col>
                    <print-col :span="4">
                        合计品种：{{ order && order.kindCount || '' }}
                    </print-col>
                    <print-col :span="3">
                        第 <span
                            data-page-no="PageNo"
                        >##</span>页/共 <span data-page-count="PageCount">##</span>页
                    </print-col>
                </print-row>
                <print-row data-last-page="LastPage">
                    <print-col :span="4">
                        收货人：
                    </print-col>
                    <print-col :span="5">
                        质量情况：
                    </print-col>
                    <print-col :span="5">
                        保管人：
                    </print-col>
                    <print-col :span="5">
                        验收人：
                    </print-col>
                    <print-col :span="5">
                        制单人：{{ order && order.createdUser.name }}
                    </print-col>
                </print-row>
            </print-row>
        </div>
    </div>
</template>

<script>
    import CKHandler from './data-handler/CK-handler.js'
    import {
        goodsSpec,
        digitUppercase,
        complexCount,
        parseTime,
        moneyDigit,
        formatMoney,
        goodsFullName
    } from "./common/utils.js";
    import PrintRow from './components/layout/print-row.vue';
    import PrintCol from './components/layout/print-col.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import DataPermissionControl from './components/inventory/data-permission-control.js';

    export default {
        name: "CK",
        DataHandler: CKHandler,
        components: {
            PrintCol,
            PrintRow,
            DataPermissionControl,
        },
        businessKey: PrintBusinessKeyEnum.GOODS_OUT,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分' // 默认选择的等分纸
            },
        ],
        filters: {
            goodsSpec,
            complexCount,
            parseTime,
            goodsFullName
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        computed: {
            order() {
                return this.renderData.printData;
            },
            config() {
                return this.renderData.config;
            },
            tableTitle() {
                if(this.order) {
                    let str = '';
                    switch (this.order.type) {
                        case 1:
                            str = '领料出库单';
                            break
                        case 2:
                            str = '报损出库单';
                            break
                        case 10:
                            str = '退货出库单';
                            break
                        case 5:
                            str = '科室消耗单';
                            break
                        case 6:
                            str = '其他出库单';
                            break
                        case 8:
                            str = '生产出库单';
                            break
                    }
                    if(this.order.type===10 && this.order.typeName==='退货出库'){
                        return `${this.order.toOrgan && this.order.toOrgan.name ||'' } ${str}`
                    }
                    return `${this.order.fromOrgan && this.order.fromOrgan.name ||'' } ${str}`
                } else {
                    return '出库单'
                }
            },
            multiPharmacyCanUse() {
                return this.order.multiPharmacyCanUse;
            },
            showOutReason() {
                // 报损出库单才显示报损原因
                return this.order.type === 2
            }
        },
        methods: {
            formatMoney,
            moneyDigit,
            digitUppercase,
            formatExpiryDate(expiryDate) {
                if(expiryDate) {
                    return expiryDate.replace(/-/g,'/')
                }
                return ''
            },
        }
    }
</script>
<style lang="scss">
@import "./style/inventory-common.scss";
</style>
