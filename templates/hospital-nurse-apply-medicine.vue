<template>
    <div class="abc-hospital-dispensing hospital-nurse-apply-medicine-wrapper">
        <div data-type="header">
            <div class="clinic-name">
                <template v-if="headerConfig.title">
                    {{ headerConfig.title }}
                    <template v-if="headerConfig.subtitle">
                        <br />{{ headerConfig.subtitle }}
                    </template>
                </template>
                <template v-else>
                    {{ clinicName }}
                </template>
            </div>
            <div class="prescription-type">
                {{ medicalPrescriptionTitle }}
            </div>

            <print-row class="table-header">
                <print-col
                    :span="8"
                    class="table-header-left"
                >
                    申领病区: {{ printData.wardAreaViewName }}
                </print-col>
                <print-col
                    class="table-header-left"
                    :span="8"
                >
                    发药药房: {{ pharmacyName }}
                </print-col>
                <print-col
                    class="table-header-right"
                    :span="8"
                >
                    {{ printData.isReturnOrder ? '退领时间' : '申领时间' }}:
                    {{ printData.created | parseTime('y-m-d h:i', true) }}
                </print-col>
            </print-row>
        </div>

        <table
            data-type="complex-table"
            style="word-break: break-all; word-wrap: break-word;"
        >
            <thead>
                <tr>
                    <th class="patient">
                        患者
                    </th>
                    <th
                        class="medicine-name"
                        colspan="2"
                    >
                        药品名称
                    </th>
                    <th
                        v-if="contentConfig.manufacturer"
                        class="manufacturer"
                    >
                        厂家
                    </th>
                    <th
                        v-if="contentConfig.displaySpec"
                        class="displaySpec"
                    >
                        规格
                    </th>
                    <th
                        v-if="contentConfig.usage"
                        class="usage"
                    >
                        用法
                    </th>
                    <th
                        v-if="contentConfig.freq"
                        class="freq"
                    >
                        频率
                    </th>
                    <th
                        v-if="contentConfig.singleDosage"
                        class="single-dosage"
                    >
                        单次剂量
                    </th>
                    <th
                        v-if="contentConfig.days"
                        class="days"
                    >
                        天数
                    </th>
                    <th
                        v-if="contentConfig.commitCount"
                        class="medicine-count"
                    >
                        {{ printData.isReturnOrder ? '应退' : '应发' }}
                    </th>
                    <th
                        v-if="contentConfig.pushCount"
                        class="medicine-count"
                    >
                        {{ printData.isReturnOrder ? '实退' : '实发' }}
                    </th>
                    <th
                        v-if="contentConfig.dispenseTime"
                        class="dispense-time"
                    >
                        {{ printData.isReturnOrder ? '退药时间' : '发药时间' }}
                    </th>
                    <th
                        v-if="contentConfig.remark"
                        class="remark"
                    >
                        备注
                    </th>
                </tr>
            </thead>

            <tbody>
                <template v-for="(patient, patientIndex) in tableDataGroup">
                    <template v-for="(dispensingItem, index) in groupDispensingList(patient.dispensingList)">
                        <template v-for="(item, formItemIndex) in dispensingItem.dispensingFormItems">
                            <tr :key="`${patientIndex}-${index}-${formItemIndex}`">
                                <!-- 患者信息 -->
                                <td
                                    v-if="!index && !formItemIndex"
                                    :rowspan="getRowSpan(patient.dispensingList)"
                                >
                                    {{ patient.bedNo.padStart(2, '0') }}
                                    <br />
                                    {{ patient.patient.name }}
                                </td>
                                <!-- 药名 -->
                                <td
                                    colspan="2"
                                    class="align-left"
                                    :class="dispensingItem.dispensingFormItems.length > 1 ? {
                                        'is-group-start': !formItemIndex,
                                        'is-group-end': formItemIndex === dispensingItem.dispensingFormItems.length - 1,
                                        'is-group-process': formItemIndex !== dispensingItem.dispensingFormItems.length - 1 && formItemIndex !== 0
                                    } : {
                                        'is-group-start': dispensingItem.isGroupStart,
                                        'is-group-end': dispensingItem.isGroupEnd,
                                        'is-group-process': dispensingItem.isGroupProcess
                                    }"
                                >
                                    <span>{{ item.name }}</span>
                                    <!-- 中药展示煎法 -->
                                    <template v-if="item.productType === 1 && item.productSubType === 2">
                                        <span style=" margin-left: 8px; font-size: 10px; color: #7a8794;">{{ item.usageInfo && item.usageInfo.specialRequirement }}</span>
                                    </template>
                                </td>
                                <!-- 厂家 -->
                                <td v-if="contentConfig.manufacturer">
                                    {{ item.productInfo && item.productInfo.manufacturer }}
                                </td>
                                <!-- 规格 -->
                                <td v-if="contentConfig.displaySpec">
                                    {{ item.productInfo && item.productInfo.displaySpec }}
                                </td>
                                <!-- 用法 -->
                                <td v-if="contentConfig.usage">
                                    {{ dispensingItem.usageInfo && dispensingItem.usageInfo.usage }}
                                </td>
                                <!-- 频率 -->
                                <td v-if="contentConfig.freq">
                                    {{ dispensingItem.usageInfo && dispensingItem.usageInfo.freq }}
                                </td>
                                <!-- 单次剂量 -->
                                <td v-if="contentConfig.singleDosage">
                                    <template v-if="isChineseGoods(dispensingItem)">
                                        {{ item.unitCount }} {{ item.unit }}
                                    </template>
                                    <template v-else-if="isMaterials(dispensingItem)"></template>
                                    <template v-else>
                                        <template v-if="dispensingItem.usageInfo">
                                            {{ dispensingItem.usageInfo.doseCount }}{{ dispensingItem.usageInfo.dosageUnit }}
                                        </template>
                                    </template>
                                </td>
                                <!-- 天数 -->
                                <td v-if="contentConfig.days">
                                    <template v-if="dispensingItem.usageInfo">
                                        {{ dispensingItem.usageInfo.days }}
                                    </template>
                                </td>
                                <!-- 应发 -->
                                <td v-if="contentConfig.commitCount">
                                    {{ getCount(item) }}
                                </td>
                                <!-- 实发 -->
                                <td v-if="contentConfig.pushCount">
                                    {{ getTotalNumber(item).totalNumber }}
                                </td>
                                <!-- 发药时间 -->
                                <td v-if="contentConfig.dispenseTime">
                                    <div
                                        v-for="time in splitDispenseTime(item, printData.isReturnOrder)"
                                        :key="time"
                                    >
                                        {{ time }}
                                    </div>
                                </td>
                                <!-- 备注 -->
                                <td v-if="contentConfig.remark">
                                    {{ item.usageInfo && item.usageInfo.requirement }}
                                </td>
                            </tr>
                        </template>
                        <tr
                            v-if="isChineseGoods(dispensingItem)"
                            :key="`${patientIndex}-${index}`"
                        >
                            <!-- 加工 -->
                            <td
                                :colspan="chineseProcessRowSpan"
                                style="text-align: left;"
                            >
                                {{ getProcessUsageInfo(dispensingItem) }}{{ getRequirement(dispensingItem) }}。{{ getDispensingFormItemInfo(dispensingItem) }}
                            </td>
                        </tr>
                    </template>
                </template>
            </tbody>
        </table>

        <div
            class="footer"
            data-type="footer"
            style="padding-top: 8px;"
        >
            <div class="table-footer">
                <print-row>
                    <print-col
                        v-if="footerConfig.deployer"
                        :span="6"
                    >
                        调配人：
                    </print-col>
                    <print-col
                        v-if="footerConfig.reviewer"
                        :span="6"
                    >
                        复核人：
                    </print-col>
                    <print-col
                        v-if="footerConfig.medicineReceive"
                        :span="6"
                    >
                        取药人：
                    </print-col>
                    <print-col
                        v-if="footerConfig.printDate"
                        :span="6"
                        :class="{ 'align-right': footerConfig.deployer || footerConfig.reviewer || footerConfig.medicineReceive }"
                    >
                        打印时间：{{ new Date() | parseTime('y-m-d h:i', true) }}
                    </print-col>
                </print-row>
            </div>
            <div class="pagination">
                <template v-if="extra.isPreview">
                    第1页/共1页
                </template>
                <template v-else>
                    第<span data-page-no="PageNo"></span>页/共<span data-page-count="PageCount"></span>页
                </template>
            </div>
        </div>
    </div>
</template>

<script>
    import {
        getDispensingFormItemInfo,
        getProcessUsageInfo,
        getRequirement,
        groupDispensingList,
        parseTime, splitDispenseTime
    } from './common/utils.js';
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import PrintCol from "./components/layout/print-col.vue";
    import PrintRow from "./components/layout/print-row.vue";
    import {DispenseOrderFormItemTypeEnum, PharmacyTypeEnum,} from "./common/constants.js";
    import Clone from "./common/clone";

    const initPrintConfig = {
        // 领药退药单前记
        header: {
            'title': '', // 抬头名称 字符串必填 String
            'subtitle': '', // 副抬头名称 可选值 String
        },
        // 领药退药单正文
        content: {
            'manufacturer': 1, // 厂家, 可选值 0 1, 默认 1
            'displaySpec': 1, // 规格, 可选值 0 1, 默认 1
            'usage': 0, // 用法, 可选值 0 1, 默认 0
            'freq': 0, // 频率, 可选值 0 1, 默认 0
            'remark': 1, // 备注, 可选值 0 1, 默认 1
            "singleDosage": 0, // 单次剂量, 可选值 0 1, 默认 0
            "days": 0, // 天数, 可选值 0 1, 默认 0
            'commitCount': 1, // 应发/应退, 可选值 0 1, 默认 1
            'pushCount': 1, // 实发/实退, 可选值 0 1, 默认 1
            'isMerge': 0, // 合并相同药品, 可选值 0 1, 默认 0
        },
        // 领药退药单后记
        footer: {
            'deployer': 0, // 调配人, 可选值 0 1, 默认 0
            'reviewer': 0, // 复核人, 可选值 0 1, 默认 0
            'medicineReceive': 0, // 取药人, 可选值 0 1, 默认 0
            'printDate': 1, // 打印时间, 可选值 0 1, 默认 1
        },
    };

    export default {
        name: "HospitalNursePatientDispensing",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.HOSPITAL_NURSE_APPLY_MEDICINE,
        components: {
            PrintRow,
            PrintCol
        },
        filters: {
            parseTime
        },
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
            extra: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        computed: {
            printData() {
                console.log('%cprintData\n', 'background: green; padding: 0 5px', Clone(this.renderData.printData));
                return this.renderData.printData;
            },
            config() {
                if (this.renderData.config && this.renderData.config.hospitalMedicalDocuments && this.renderData.config.hospitalMedicalDocuments.dispenseAndUnDispenseOrder) {
                    return this.renderData.config.hospitalMedicalDocuments.dispenseAndUnDispenseOrder;
                }
                return initPrintConfig;
            },
            headerConfig() {
                return this.config.header || {};
            },
            contentConfig() {
                return this.config.content || {};
            },
            footerConfig() {
                return this.config.footer || {};
            },
            medicalPrescriptionTitle() {
                return this.printData.medicalPrescriptionTitle;
            },
            clinicName() {
                return this.printData.clinicName;
            },
            tableDataGroup() {
                return this.printData.dispensingData || [];
            },
            pharmacyName() {
                const {pharmacyType} = this.printData;
                if (pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                    return '本地药房';
                }
                if (pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                    return '空中药房';
                }
                return '虚拟药房';
            },
            chineseProcessRowSpan() {
                let count = 2;
                if (this.contentConfig.manufacturer) {
                    count++;
                }
                if (this.contentConfig.displaySpec) {
                    count++;
                }
                if (this.contentConfig.usage) {
                    count++;
                }
                if (this.contentConfig.freq) {
                    count++;
                }
                if (this.contentConfig.singleDosage) {
                    count++;
                }
                if (this.contentConfig.remark) {
                    count++;
                }
                if (this.contentConfig.days) {
                    count++;
                }
                if (this.contentConfig.commitCount) {
                    count++;
                }
                if (this.contentConfig.pushCount) {
                    count++;
                }
                if (this.contentConfig.dispenseTime) {
                    count++;
                }
                return count;
            },
        },
        methods: {
            getProcessUsageInfo,
            getRequirement,
            getDispensingFormItemInfo,
            groupDispensingList,
            splitDispenseTime,
            getCount(item) {
                const {
                    productSubType,
                    productType,
                    unitCount,
                    totalCount,
                    doseCount,
                    unit
                } = item;

                let totalNumber = `${totalCount}${unit}`;
                if (productSubType === 2 && productType === 1) {
                    totalNumber = `${unitCount}${unit}*${doseCount}剂`;
                }
                return totalNumber;
            },
            getTotalNumber(item) {
                const needShowTotal = (
                    item.status !== DispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE &&
                    item.status !== DispenseOrderFormItemTypeEnum.APPLY_DISPENSE_REJECT &&
                    item.status !== DispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT);
                const {
                    productSubType, productType, unitCount, totalCount, doseCount, unit
                } = item;
                let totalNumber = `${needShowTotal ? totalCount : 0}${unit}`;
                if (productSubType === 2 && productType === 1) {
                    totalNumber = `${unitCount}${unit}*${needShowTotal ? doseCount : 0}剂`;
                }
                return {
                    needShowTotal,
                    totalNumber,
                };
            },
            getRowSpan(dispensingList) {
                let rowSpan = 0;
                dispensingList.forEach((item) => {
                    rowSpan += (item.dispensingFormItems && item.dispensingFormItems.length || 1);
                    // 中药需要额外一行展示加工信息
                    if (this.isChineseGoods(item)) {
                        rowSpan += 1;
                    }
                });
                return rowSpan;
            },
            isChineseGoods(dispensingItem) {
                const item = dispensingItem.dispensingFormItems?.[0];
                if (!item) {
                    return false;
                }
                const {
                    productSubType, productType,
                } = item;
                return productSubType === 2 && productType === 1;
            },
            /**
             * 耗材(物资、商品)
             * @param dispensingItem
             * @return {boolean}
             */
            isMaterials(dispensingItem) {
                const item = dispensingItem.dispensingFormItems?.[0];
                if (!item) {
                    return false;
                }
                const {
                    productType,
                } = item;
                return productType === 7 || productType === 2;
            },
        }
    }
</script>
<style lang="scss">
@import "style/hospital-dispensing.scss";

.hospital-nurse-apply-medicine-white-space {
    word-break: break-all;
    word-wrap: break-word;
    white-space: normal;
}

.hospital-nurse-apply-medicine-wrapper {
    box-sizing: content-box !important;

    .patient {
        width: 50px;
        text-align: center;
    }

    .medicine-name {
        text-align: left;
    }

    .manufacturer {
        text-align: center;
    }

    .displaySpec {
        text-align: center;
    }

    .usage {
        width: 50px;
        text-align: center;
    }

    .freq {
        width: 40px;
        text-align: center;
    }

    .single-dosage {
        width: 54px;
        text-align: center;
    }

    .days {
        width: 28px;
        text-align: center;
    }

    .medicine-count {
        width: 42px;
        text-align: center;
    }

    .dispense-time {
        width: 56px;
        text-align: center;
    }

    .remark {
        width: 50px;
        text-align: center;
    }
}
</style>
<!--exampleData
{
	"dispensingData": [{
		"patient": {
			"name": "傻鸟患"
		},
		"dispensingList": [{
				"id": "ffffffff0000000034a39c403bee4016",
				"associateFormItemId": null,
				"status": 0,
				"undispenseType": 0,
				"unitCount": 2,
				"remainingUnitCount": 2,
				"doseCount": 1,
				"remainingDoseCount": 1,
				"totalCount": 2,
				"dispensingFormId": "ffffffff0000000034a39c403bee4015",
				"sourceItemType": 0,
				"productId": "ffffffff000000003487afaec7a48000",
				"productType": 1,
				"productSubType": 1,
				"name": "阿莫君阿莫西林胶囊(阿莫西林)",
				"sourceFormItemId": "3793047110413467665",
				"unit": "片",
				"totalCostPrice": 0,
				"unitPrice": 4.1667,
				"useDismounting": 1,
				"groupId": null,
				"sort": 0,
				"usageInfo": null,
				"productInfo": {
					"goodsVersion": 1,
					"sourceFlag": 1,
					"id": "ffffffff000000003487afaec7a48000",
					"goodsId": null,
					"status": 1,
					"name": "阿莫西林",
					"displayName": "阿莫君阿莫西林胶囊(阿莫西林)",
					"displaySpec": "12片/盒",
					"organId": "ffffffff00000000146808c695534000",
					"typeId": 12,
					"type": 1,
					"subType": 1,
					"manufacturer": "江西汇仁",
					"pieceNum": 12,
					"pieceUnit": "片",
					"packageUnit": "盒",
					"dismounting": 0,
					"medicineCadn": "阿莫君阿莫西林胶囊",
					"specType": 0,
					"position": null,
					"piecePrice": 4.1667,
					"packagePrice": 50,
					"packageCostPrice": 50,
					"inTaxRat": 0,
					"outTaxRat": 0,
					"pieceCount": 8,
					"packageCount": 988,
					"dispGoodsCount": "988盒8片",
					"stockPieceCount": 0,
					"stockPackageCount": 987,
					"dispStockGoodsCount": "987盒",
					"availablePackageCount": 988,
					"availablePieceCount": 8,
					"outPieceCount": 0,
					"outPackageCount": 987,
					"dispOutGoodsCount": "0盒",
					"dispProhibitGoodsCount": "0盒",
					"lockingPieceCount": 8,
					"lockingPackageCount": 1,
					"dispLockingGoodsCount": "1盒8片",
					"lastPackageCostPrice": 50,
					"needExecutive": 0,
					"shortId": "128482165909",
					"composeUseDismounting": 0,
					"composeSort": 0,
					"createdUserId": "6e45706922a74966ab51e4ed1e604641",
					"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
					"lastModifiedDate": "2023-10-26T06:34:38Z",
					"combineType": 0,
					"bizRelevantId": null,
					"extendSpec": "",
					"medicalFeeGrade": 0,
					"shebaoNationalCode": "XJ01CAA040E001010502825",
					"disable": 0,
					"chainDisable": 0,
					"v2DisableStatus": 0,
					"chainV2DisableStatus": 0,
					"disableSell": 0,
					"isSell": 1,
					"customTypeId": 0,
					"chainPackagePrice": 50,
					"chainPiecePrice": 4.1667,
					"chainPackageCostPrice": 50,
					"manufacturerFull": "江西汇仁药业有限公司",
					"chainId": "ffffffff00000000146808c695534000",
					"supplier": null,
					"shebao": {
						"goodsId": "ffffffff000000003487afaec7a48000",
						"goodsType": 1,
						"payMode": 0,
						"isDummy": 0,
						"medicineNum": "128482165909",
						"medicalFeeGrade": 0,
						"nationalCode": "XJ01CAA040E001010502825",
						"nationalCodeId": "2376750067640467758"
					},
					"recentAvgSell": 0,
					"profitRat": 0,
					"lastStockInId": 100018000,
					"lastStockInOrderSupplier": "科伦医药有限公司",
					"pharmacyType": 0,
					"pharmacyNo": 15,
					"pharmacyName": "中药库房1",
					"pharmacyGoodsStockList": [{
							"pharmacyName": "西药库",
							"pharmacyNo": 0,
							"lastPackageCostPrice": 50,
							"stockPieceCount": 0,
							"stockPackageCount": 100,
							"availablePackageCount": 100,
							"availablePieceCount": 0
						},
						{
							"pharmacyName": "门诊中药房",
							"pharmacyNo": 1,
							"lastPackageCostPrice": 1,
							"stockPieceCount": 0.5,
							"stockPackageCount": 98,
							"availablePackageCount": 99,
							"availablePieceCount": 9.5
						},
						{
							"pharmacyName": "中西药库",
							"pharmacyNo": 18,
							"lastPackageCostPrice": 10,
							"stockPieceCount": 0,
							"stockPackageCount": 10095,
							"availablePackageCount": 10097,
							"availablePieceCount": 0
						},
						{
							"pharmacyName": "药库2",
							"pharmacyNo": 7,
							"lastPackageCostPrice": 3,
							"stockPieceCount": 3,
							"stockPackageCount": 944,
							"availablePackageCount": 986,
							"availablePieceCount": 1
						},
						{
							"pharmacyName": "中药房",
							"pharmacyNo": 9,
							"lastPackageCostPrice": 1,
							"stockPieceCount": 2,
							"stockPackageCount": 1002,
							"availablePackageCount": 1002,
							"availablePieceCount": 4
						},
						{
							"pharmacyName": "b库房",
							"pharmacyNo": 10,
							"lastPackageCostPrice": 10,
							"stockPieceCount": 0,
							"stockPackageCount": 0,
							"availablePackageCount": 1,
							"availablePieceCount": 0
						},
						{
							"pharmacyName": "中药库房1",
							"pharmacyNo": 15,
							"lastPackageCostPrice": 50,
							"stockPieceCount": 0,
							"stockPackageCount": 987,
							"availablePackageCount": 988,
							"availablePieceCount": 8
						}
					],
					"defaultInOutTax": 1,
					"dispenseAveragePackageCostPrice": 50,
					"shebaoPayMode": 0,
					"innerFlag": 0,
					"feeComposeType": 0,
					"feeTypeId": "3784125800409153536",
					"feeTypeName": "加班费1234",
					"usePieceUnitFlag": 1,
					"nationalCode": "XJ01CAA040E001010502825",
					"nationalCodeId": "2376750067640467758",
					"feeCategoryId": 2,
					"cMSpec": ""
				},
				"composeType": 0,
				"composeChildren": null,
				"stockPieceCount": 0,
				"stockPackageCount": 0,
				"thisTimeDispensed": 0,
				"pharmacyNo": 15,
				"pharmacyType": 0,
				"pharmacyName": null,
				"pharmacyExtendInfo": null,
				"pharmacyDispenseFlag": 0,
				"created": "2023-11-19T16:00:01Z",
				"lastModified": "2023-11-19T16:00:01Z",
				"statusName": "待发",
				"useCount": null,
				"supportReDispense": 0,
				"adviceRuleItemId": "3792903840505020418",
				"adviceExecuteItemId": "3793047110413467665",
				"operationLogs": null,
				"remainingTotalPrice": 8.33,
				"doctorId": null,
				"departmentId": null,
				"nurseId": null,
				"remark": null,
				"toothNos": null,
				"isHistoryItem": 0,
				"totalPrice": 8.33
			},
			{
				"id": "ffffffff0000000034a39c403bee4020",
				"associateFormItemId": null,
				"status": 0,
				"undispenseType": 0,
				"unitCount": 2,
				"remainingUnitCount": 2,
				"doseCount": 1,
				"remainingDoseCount": 1,
				"totalCount": 2,
				"dispensingFormId": "ffffffff0000000034a39c403bee401f",
				"sourceItemType": 0,
				"productId": "ffffffff000000003487afaec7a48000",
				"productType": 1,
				"productSubType": 1,
				"name": "阿莫君阿莫西林胶囊(阿莫西林)",
				"sourceFormItemId": "3793047110413467667",
				"unit": "片",
				"totalCostPrice": 0,
				"unitPrice": 4.1667,
				"useDismounting": 1,
				"groupId": null,
				"sort": 0,
				"usageInfo": null,
				"productInfo": {
					"goodsVersion": 1,
					"sourceFlag": 1,
					"id": "ffffffff000000003487afaec7a48000",
					"goodsId": null,
					"status": 1,
					"name": "阿莫西林",
					"displayName": "阿莫君阿莫西林胶囊(阿莫西林)",
					"displaySpec": "12片/盒",
					"organId": "ffffffff00000000146808c695534000",
					"typeId": 12,
					"type": 1,
					"subType": 1,
					"manufacturer": "江西汇仁",
					"pieceNum": 12,
					"pieceUnit": "片",
					"packageUnit": "盒",
					"dismounting": 0,
					"medicineCadn": "阿莫君阿莫西林胶囊",
					"specType": 0,
					"position": null,
					"piecePrice": 4.1667,
					"packagePrice": 50,
					"packageCostPrice": 50,
					"inTaxRat": 0,
					"outTaxRat": 0,
					"pieceCount": 8,
					"packageCount": 988,
					"dispGoodsCount": "988盒8片",
					"stockPieceCount": 0,
					"stockPackageCount": 987,
					"dispStockGoodsCount": "987盒",
					"availablePackageCount": 988,
					"availablePieceCount": 8,
					"outPieceCount": 0,
					"outPackageCount": 987,
					"dispOutGoodsCount": "0盒",
					"dispProhibitGoodsCount": "0盒",
					"lockingPieceCount": 8,
					"lockingPackageCount": 1,
					"dispLockingGoodsCount": "1盒8片",
					"lastPackageCostPrice": 50,
					"needExecutive": 0,
					"shortId": "128482165909",
					"composeUseDismounting": 0,
					"composeSort": 0,
					"createdUserId": "6e45706922a74966ab51e4ed1e604641",
					"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
					"lastModifiedDate": "2023-10-26T06:34:38Z",
					"combineType": 0,
					"bizRelevantId": null,
					"extendSpec": "",
					"medicalFeeGrade": 0,
					"shebaoNationalCode": "XJ01CAA040E001010502825",
					"disable": 0,
					"chainDisable": 0,
					"v2DisableStatus": 0,
					"chainV2DisableStatus": 0,
					"disableSell": 0,
					"isSell": 1,
					"customTypeId": 0,
					"chainPackagePrice": 50,
					"chainPiecePrice": 4.1667,
					"chainPackageCostPrice": 50,
					"manufacturerFull": "江西汇仁药业有限公司",
					"chainId": "ffffffff00000000146808c695534000",
					"supplier": null,
					"shebao": {
						"goodsId": "ffffffff000000003487afaec7a48000",
						"goodsType": 1,
						"payMode": 0,
						"isDummy": 0,
						"medicineNum": "128482165909",
						"medicalFeeGrade": 0,
						"nationalCode": "XJ01CAA040E001010502825",
						"nationalCodeId": "2376750067640467758"
					},
					"recentAvgSell": 0,
					"profitRat": 0,
					"lastStockInId": 100018000,
					"lastStockInOrderSupplier": "科伦医药有限公司",
					"pharmacyType": 0,
					"pharmacyNo": 15,
					"pharmacyName": "中药库房1",
					"pharmacyGoodsStockList": [{
							"pharmacyName": "西药库",
							"pharmacyNo": 0,
							"lastPackageCostPrice": 50,
							"stockPieceCount": 0,
							"stockPackageCount": 100,
							"availablePackageCount": 100,
							"availablePieceCount": 0
						},
						{
							"pharmacyName": "门诊中药房",
							"pharmacyNo": 1,
							"lastPackageCostPrice": 1,
							"stockPieceCount": 0.5,
							"stockPackageCount": 98,
							"availablePackageCount": 99,
							"availablePieceCount": 9.5
						},
						{
							"pharmacyName": "中西药库",
							"pharmacyNo": 18,
							"lastPackageCostPrice": 10,
							"stockPieceCount": 0,
							"stockPackageCount": 10095,
							"availablePackageCount": 10097,
							"availablePieceCount": 0
						},
						{
							"pharmacyName": "药库2",
							"pharmacyNo": 7,
							"lastPackageCostPrice": 3,
							"stockPieceCount": 3,
							"stockPackageCount": 944,
							"availablePackageCount": 986,
							"availablePieceCount": 1
						},
						{
							"pharmacyName": "中药房",
							"pharmacyNo": 9,
							"lastPackageCostPrice": 1,
							"stockPieceCount": 2,
							"stockPackageCount": 1002,
							"availablePackageCount": 1002,
							"availablePieceCount": 4
						},
						{
							"pharmacyName": "b库房",
							"pharmacyNo": 10,
							"lastPackageCostPrice": 10,
							"stockPieceCount": 0,
							"stockPackageCount": 0,
							"availablePackageCount": 1,
							"availablePieceCount": 0
						},
						{
							"pharmacyName": "中药库房1",
							"pharmacyNo": 15,
							"lastPackageCostPrice": 50,
							"stockPieceCount": 0,
							"stockPackageCount": 987,
							"availablePackageCount": 988,
							"availablePieceCount": 8
						}
					],
					"defaultInOutTax": 1,
					"dispenseAveragePackageCostPrice": 50,
					"shebaoPayMode": 0,
					"innerFlag": 0,
					"feeComposeType": 0,
					"feeTypeId": "3784125800409153536",
					"feeTypeName": "加班费1234",
					"usePieceUnitFlag": 1,
					"nationalCode": "XJ01CAA040E001010502825",
					"nationalCodeId": "2376750067640467758",
					"feeCategoryId": 2,
					"cMSpec": ""
				},
				"composeType": 0,
				"composeChildren": null,
				"stockPieceCount": 0,
				"stockPackageCount": 0,
				"thisTimeDispensed": 0,
				"pharmacyNo": 15,
				"pharmacyType": 0,
				"pharmacyName": null,
				"pharmacyExtendInfo": null,
				"pharmacyDispenseFlag": 0,
				"created": "2023-11-19T16:00:02Z",
				"lastModified": "2023-11-19T16:00:02Z",
				"statusName": "待发",
				"useCount": null,
				"supportReDispense": 0,
				"adviceRuleItemId": "3792903840505020418",
				"adviceExecuteItemId": "3793047110413467667",
				"operationLogs": null,
				"remainingTotalPrice": 8.33,
				"doctorId": null,
				"departmentId": null,
				"nurseId": null,
				"remark": null,
				"toothNos": null,
				"isHistoryItem": 0,
				"totalPrice": 8.33
			}
		],
		"usageInfo": {
			"freq": "bid",
			"usage": "口服",
			"checked": true,
			"decoction": false,
			"doseCount": 2,
			"adviceTags": [{
				"name": "补开",
				"type": 0
			}],
			"dosageUnit": "片",
			"requirement": "首次加倍，不知道些什",
			"processUsage": "口服",
			"processBagUnitCount": 0
		},
		"bedNo": "05"
	}],
	"wardAreaViewName": "住院1",
	"pharmacyName": "",
	"pharmacyType": 0,
	"created": "2023-11-19T16:00:01Z",
	"medicalPrescriptionTitle": "领药单",
	"isReturnOrder": false,
	"clinicName": "ABC医院",
	"IS_HOSPITAL": true,
	"IS_FEE_COMPOSE": true,
	"IS_GLASSES": true
}
-->