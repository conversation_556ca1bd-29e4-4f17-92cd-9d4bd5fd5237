<template>
    <div class="examination-cloud-report-wrapper">
        <div data-type="header">
            <div class="cloud-report-title">
                检验报告单
            </div>

            <div class="cloud-report-header">
                <div class="cloud-report-header-row">
                    <div class="cloud-report-header-item">
                        <span>姓名：</span>
                        <span style="font-weight: 400;">{{ patient.name }}</span>
                    </div>

                    <div class="cloud-report-header-item">
                        <span>性别：</span>
                        <span>{{ patient.sex }}</span>
                    </div>

                    <div class="cloud-report-header-item">
                        <span>年龄：</span>
                        <span>{{ patient.age }}</span>
                    </div>

                    <div class="cloud-report-header-item">
                        <span>联系方式：</span>
                        <span>{{ patient.mobile }}</span>
                    </div>
                </div>

                <div class="cloud-report-header-row">
                    <div class="cloud-report-header-item">
                        <span>检测编号：</span>
                        <span>{{ examInfo.no }}</span>
                    </div>

                    <div class="cloud-report-header-item">
                        <span>检测项目：</span>
                        <span>{{ examInfo.name }}</span>
                    </div>

                    <div class="cloud-report-header-item">
                        <span>检测时间：</span>
                        <span>{{ examInfo.time }}</span>
                    </div>

                    <div class="cloud-report-header-item">
                        <span>检测机构：</span>
                        <span>{{ organName }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="cloud-report-result">
            <div class="cloud-report-result-header row">
                <div class="name">
                    检验项目
                </div>

                <div class="value">
                    T/C
                </div>

                <div class="result">
                    结果
                </div>

                <div class="range">
                    参考范围
                </div>
            </div>

            <div class="cloud-report-result-row">
                <div
                    v-for="(row, key) in itemsValue"
                    :key="key"
                    class="cloud-report-result-item row"
                >
                    <div class="name">
                        {{ row.name }}{{ row.enName ? `(${row.enName})` : '' }}
                    </div>

                    <div class="value">
                        {{ handleValue(row) }}
                    </div>

                    <div class="result">
                        {{ row.value }}
                    </div>

                    <div class="range">
                        {{ row.ref }}
                    </div>
                </div>
            </div>
        </div>

        <div
            v-if="itemsValueForChart.length > 0"
            class="cloud-report-chart"
        >
            <div class="cloud-report-chart-header">
                对标值
            </div>

            <div
                v-for="(o, key) in itemsValueForChart"
                :key="handleKey(o, key)"
                class="cloud-report-chart-item"
            >
                <exam-cloud-chart
                    :value="handleValue(o)"
                    :label="handleLabel(o)"
                    :left="additionalData[key] ? additionalData[key].left : 0"
                ></exam-cloud-chart>
            </div>
        </div>

        <div
            class="cloud-report-footer"
            data-type="footer"
        >
            <div class="cloud-report-footer-info">
                <div class="cloud-report-footer-info-item">
                    <span>报告时间：</span>
                    <span>{{ reportTime || '' }}</span>
                </div>
            </div>

            <div class="cloud-report-footer-statement">
                声明：本报告依据POCT即时检验设备检测结果，并经异常值核验后出具
            </div>
        </div>
    </div>
</template>

<script>
    import PrintCommonHandler from './data-handler/examination-report-handler.js'

    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import {formatDate} from '@tool/date';
    import ExamCloudChart from "./components/exam-cloud-chart/index.vue";


    export default {
        DataHandler: PrintCommonHandler,
        name: "ExaminationCloudReport",
        components: {
            ExamCloudChart
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        businessKey: PrintBusinessKeyEnum.EXAMINATION_CLOUD_REPORT,
        imageTransformSetting: 'x-oss-process=image/resize,p_90',
        pages: [
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        computed: {
            printData() {
                return this.renderData.printData || null;
            },

            patient() {
                if (!this.printData || !this.printData.patient) {
                    return {
                        name: '',
                        sex: '',
                        age: '',
                        mobile: '',
                    };
                }

                const { name, sex, age, mobile } = this.printData.patient;

                return {
                    name: name || '',
                    sex: sex || '',
                    age: this.formatAge(age),
                    mobile: mobile || '',
                };
            },

            examInfo() {
                if (!this.printData) {
                    return {
                        name: '',
                        no: '',
                        time: '',
                        organ: '',
                    };
                }

                return {
                    name: this.printData.name || '',
                    no: this.printData.examinationOrder?.no || '',
                    time: this.printData.testTime ? formatDate(this.printData.testTime, 'YYYY-MM-DD HH:mm') : '',
                    organ: this.organName,
                };
            },

            itemsValue() {
                if (!this.printData) return [];

                return (this.printData.itemsValue || []).filter((item) => item.valueType !== 'IMAGE' && !item.isDeleted);
            },

            reportTime() {
                if (!this.printData || !this.printData.reportTime) return '';

                return formatDate(this.printData.reportTime, 'YYYY-MM-DD HH:mm')
            },

            organName() {
                if (!this.printData || !this.printData.additionalClinic) return '';

                return this.printData.additionalClinic.shortName || this.printData.additionalClinic.name;
            },

            additionalData() {
                if (!this.printData) return [];

                return this.printData.additionalData || [];
            },

            itemsValueForChart() {
                if (!this.itemsValue) return [];

                return this.itemsValue.filter((item) => {
                    return item.value.includes('阴性') || item.value.includes('阳性');
                });
            },
        },

        methods: {
            formatAge(age) {
                if (!age) return '';

                const {
                    year, month, day,
                } = age;

                if (year) return `${year}岁`;

                if (month) return `${month}月`;

                if (day) return `${day}天`;

                return '';
            },
          
            handleValue(item) {
                if (!item) return 0;
                const { extendData } = item;

                return extendData?.tcRatio ? parseFloat(extendData.tcRatio) : 0;
            },

            handleLabel(item) {
                const enName = item.enName ? `(${item.enName})` : '';

                return `${item.name}${enName}`;
            },

            handleKey(item, key) {
                return `${item.examinationSheetId || ''}${item.goodsId || ''}${key}`;
            },
        }
    }
</script>

<style lang="scss">
@import "./style/reset.scss";
@import "./components/layout/print-layout.scss";

.examination-cloud-report-wrapper {
  .cloud-report-title {
    font-family: SimSun, STSong, Serif;
    font-size: 19px;
    font-weight: bold;
    height: 27px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cloud-report-header {
    font-size: 11px;
    font-weight: 300;
    margin-top: 16px;

    .cloud-report-header-row {
      display: flex;
      gap: 6px;

      .cloud-report-header-item {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;

        > span {
          &:first-child {
            flex-shrink: 0;
          }

          &:last-child {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
          }
        }
      }
    }

    .cloud-report-header-row + .cloud-report-header-row {
      margin-top: 12px;
    }
  }

  .cloud-report-result {
    margin: 24px 0 16px;

    .row {
      display: flex;
      align-items: center;
      gap: 4px;

      .name {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
      }

      .value {
        width: 130px;
        flex-shrink: 0;
      }

      .result {
        width: 90px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
      }

      .range {
        width: 130px;
        text-align: center;
        flex-shrink: 0;
      }
    }

    .cloud-report-result-header {
      height: 24px;
      font-size: 11px;
      font-weight: 300;
      border-top: 1px solid #000;
      border-bottom: 1px solid #000;
    }

    .cloud-report-result-row {
      display: flex;
      flex-direction: column;
      padding: 6px 0 14px;

      .cloud-report-result-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 13px;
        font-weight: 400;
      }

      .cloud-report-result-item + .cloud-report-result-item {
        margin-top: 6px;
      }
    }
  }

  .cloud-report-chart {
    .cloud-report-chart-header {
      font-size: 11px;
      font-weight: 300;
      padding-bottom: 10px;
      border-bottom: 1px solid #000;
    }

    .cloud-report-chart-item + .cloud-report-chart-item {
      margin-top: 12px;
    }
  }

  .cloud-report-footer {
    border-top: 1px solid #000;
    padding-top: 6px;
    font-size: 11px;

    .cloud-report-footer-info {
      display: flex;
      gap: 6px;
      font-weight: 300;

      .cloud-report-footer-info-item {
        display: flex;
        align-items: center;
        flex: 1;
      }
    }

    .cloud-report-footer-statement {
      font-weight: 400;
      margin-top: 2px;
    }
  }
}
</style>

<!--exampleData
{
  "additionalClinic": {
    "name": "四川省成都市高新区交子大道高新大源店",
    "shortName": "高新大原店"
  },
  "additionalData": [
    {
      "left": 17.01891472868217
    }
  ],
  "id": "ffffffff0000000034e457d7e5048002",
  "goodsType": 3,
  "type": 1,
  "subType": 0,
  "patientId": "82d6754a0ce048899b5fefd0d73eeeb3",
  "patientOrderId": "ffffffff0000000034e457d7a22c8000",
  "examinationApplySheetId": "3811267769434144772",
  "wardAreaId": null,
  "relationPatientOrderId": null,
  "chargeSheetId": "ffffffff0000000034e457d7a86ac004",
  "chargeFormItemId": "ffffffff0000000034e457d7a86ac007",
  "chargeSheetType": 2,
  "outpatientFormItemId": "ffffffff0000000034e457d7a4588002",
  "adviceExecuteItemId": null,
  "peFormItemId": null,
  "peFormItemIds": null,
  "organPrintView": {
    "id": "fff730ccc5ee45d783d82a85b8a0e52d",
    "name": "高新大原店",
    "contactPhone": "0851-8511132",
    "addressProvinceName": "四川",
    "addressCityName": "成都市",
    "addressDistrictName": "高新区",
    "addressDetail": "四川省成都市成华区保和街道邛崃山路333号",
    "logo": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/basic/%E8%AF%8A%E6%89%80Logo%E6%B2%99%E5%9D%AA%E5%9D%9D_ah8qQyhlUdmr.jpg",
    "qrUrl": "http://weixin.qq.com/q/02GrXTEhJyeUD10000007g",
    "category": "中西医结合医院",
    "addressProvinceId": "510000",
    "addressCityId": "510100",
    "addressDistrictId": "510109",
    "hisType": 0,
    "medicalDocumentsTitle": {
      "prescription": "高新大原店",
      "medical": "高新大原店",
      "infusion": "高新大原店",
      "treatment": "高新大原店",
      "examination": "高新大原店",
      "illnessCert": "高新大原店",
      "inspection": "高新大原店"
    }
  },
  "examinationId": "ffffffff0000000034e457c3158cc000",
  "supplierId": 1,
  "barCode": 29045,
  "businessType": 10,
  "orderNo": "JY202412160003",
  "sampleNoHistories": [
    {
      "deviceModelId": "3808806073960333312",
      "deviceId": "1689091309109336",
      "sampleNo": "JY202412160003",
      "displaySampleNo": "JY202412160003"
    }
  ],
  "status": 1,
  "sampleStatus": 20,
  "doctorId": "6e45706922a74966ab51e4ed1e604641",
  "doctor": {
    "id": "6e45706922a74966ab51e4ed1e604641",
    "name": "丁柱112",
    "mobile": "***********",
    "countryCode": "86",
    "status": 1,
    "created": "2018-01-09 10:11:38",
    "shortId": "939523477594734595",
    "namePy": "dingzhu112|zhengzhu112",
    "namePyFirst": "DZ112|ZZ112",
    "headImgUrl": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png",
    "handSign": "https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/ZeEg9sFSOQA1UPYjGaMADMCyEk2aLpJd_1733724525248",
    "wechatOpenIdMp": "1",
    "wechatNickName": "刘喜",
    "wechatUnionId": "o2VGt0-_RMKgynfy3e6aipz3G268"
  },
  "sellerId": "6e45706922a74966ab51e4ed1e604641",
  "seller": {
    "id": "6e45706922a74966ab51e4ed1e604641",
    "name": "丁柱112",
    "mobile": "***********",
    "countryCode": "86",
    "status": 1,
    "created": "2018-01-09 10:11:38",
    "shortId": "939523477594734595",
    "namePy": "dingzhu112|zhengzhu112",
    "namePyFirst": "DZ112|ZZ112",
    "headImgUrl": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png",
    "handSign": "https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/ZeEg9sFSOQA1UPYjGaMADMCyEk2aLpJd_1733724525248",
    "wechatOpenIdMp": "1",
    "wechatNickName": "刘喜",
    "wechatUnionId": "o2VGt0-_RMKgynfy3e6aipz3G268"
  },
  "doctorDepartmentId": "59140f8ecdeb4553ab570f710274e0ab",
  "doctorDepartmentName": "小儿外科诊室",
  "sellerDepartmentId": "59140f8ecdeb4553ab570f710274e0ab",
  "sellerDepartmentName": "小儿外科诊室",
  "executeDepartmentId": "",
  "executeDepartmentName": null,
  "attachments": [],
  "remark": null,
  "itemsValue": [
    {
      "id": "ffffffff0000000034e457c0e5048004",
      "goodsId": "ffffffff0000000034e457c0f58cc048",
      "goodsName": "肺炎支原体",
      "type": 3,
      "name": "肺炎支原体",
      "enName": "MP IgM",
      "ref": "阴性",
      "refDetails": [
        {
          "itemId": "ffffffff0000000034e457c0e5048004",
          "ageUnit": "岁",
          "ref": "阴性"
        }
      ],
      "itemCode": "HAJY5000035",
      "itemType": 1,
      "resultDisplayScale": 2,
      "value": "阳性",
      "extendData": {
        "tcRatio": "0.142"
      },
      "valueType": "STRING",
      "sort": 0,
      "groupBy": "肺炎支原体IgM抗体检测",
      "groupById": 0,
      "examinationSheetId": "ffffffff0000000034e457d7e5048002",
      "deviceModelId": "3808806073960333312",
      "deviceModelIds": [
        "3808806073960333312"
      ],
      "updateItemsValueFlag": 1
    },
    {
      "id": "00000000000000000000000000000001",
      "goodsId": "00000000000000000000000000000000",
      "type": null,
      "name": "WBC DIFF Scattergram. BMP",
      "enName": "WBC DIFF Scattergram. BMP",
      "itemCode": null,
      "itemType": null,
      "value": "https://cd-cis-static-assets-test.oss-cn-chengdu.aliyuncs.com/clinic-usage/3214bf41b9e7466eaf4e425ec066cddb/medical-record/llCeJS7XPMoMWssGVGpkxEGVSegukJsv_1666598848227.jpeg",
      "valueType": "IMAGE",
      "sort": 0,
      "groupBy": "肺炎支原体IgM抗体检测",
      "groupById": 0,
      "examinationSheetId": "ffffffff0000000034e457d7e5048002",
      "deviceModelId": "3808806073960333312",
      "deviceModelIds": [
        "3808806073960333312"
      ],
      "updateItemsValueFlag": 1
    },
    {
      "id": "00000000000000000000000000000002",
      "goodsId": "00000000000000000000000000000000",
      "type": null,
      "name": "WBC Histogram. BMP",
      "enName": "WBC Histogram. BMP",
      "itemCode": null,
      "itemType": null,
      "value": "https://cd-cis-static-assets-test.oss-cn-chengdu.aliyuncs.com/clinic-usage/9daeca0eab074c639892433ffef36f94/medical-record/EwRDmHDOPW3zZY4HXs8vE7EZxDq8kGW4_1666599180285.jpeg",
      "valueType": "IMAGE",
      "sort": 0,
      "groupBy": "肺炎支原体IgM抗体检测",
      "groupById": 0,
      "examinationSheetId": "ffffffff0000000034e457d7e5048002",
      "deviceModelId": "3808806073960333312",
      "deviceModelIds": [
        "3808806073960333312"
      ],
      "updateItemsValueFlag": 1
    }
  ],
  "deviceData": null,
  "preItemsValue": [
    {
      "id": null,
      "goodsId": null,
      "type": null,
      "name": null,
      "ref": "{\"min\":\"\",\"max\":\"\"}",
      "itemCode": null,
      "itemType": null,
      "value": null,
      "valueType": "STRING",
      "sort": 0,
      "updateItemsValueFlag": 0
    },
    {
      "id": null,
      "goodsId": null,
      "type": null,
      "name": null,
      "ref": "{\"min\":\"\",\"max\":\"\"}",
      "itemCode": null,
      "itemType": null,
      "value": null,
      "valueType": "STRING",
      "sort": 0,
      "updateItemsValueFlag": 0
    },
    {
      "id": null,
      "goodsId": null,
      "type": null,
      "name": null,
      "ref": "{\"min\":\"\",\"max\":\"\"}",
      "itemCode": null,
      "itemType": null,
      "value": null,
      "valueType": "STRING",
      "sort": 0,
      "updateItemsValueFlag": 0
    }
  ],
  "examinationHistories": [],
  "created": "2024-12-16T11:23:43Z",
  "orderByDate": "2024-12-16T11:23:43Z",
  "lastModifiedBy": "6e45706922a74966ab51e4ed1e604641",
  "patientOrderNumber": "29406",
  "wardAreaName": null,
  "bedNumber": null,
  "testerId": "6e45706922a74966ab51e4ed1e604641",
  "testerName": "丁柱112",
  "tester": {
    "id": "6e45706922a74966ab51e4ed1e604641",
    "name": "丁柱112",
    "mobile": "***********",
    "countryCode": "86",
    "status": 1,
    "created": "2018-01-09 10:11:38",
    "shortId": "939523477594734595",
    "namePy": "dingzhu112|zhengzhu112",
    "namePyFirst": "DZ112|ZZ112",
    "headImgUrl": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png",
    "handSign": "https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/ZeEg9sFSOQA1UPYjGaMADMCyEk2aLpJd_1733724525248",
    "wechatOpenIdMp": "1",
    "wechatNickName": "刘喜",
    "wechatUnionId": "o2VGt0-_RMKgynfy3e6aipz3G268"
  },
  "testTime": "2023-04-12T19:28:00Z",
  "sampleType": "全血",
  "checkerId": null,
  "checkerName": null,
  "checkTime": null,
  "reportTime": null,
  "chargeFormItemStatus": 0,
  "chargeFormItemOnceFee": 0,
  "examinationChargeFormItems": [
    {
      "examinationSheetId": "ffffffff0000000034e457d7e5048002",
      "chargeFormItemSourceId": "ffffffff0000000034e457d7a4588002",
      "chargeFormItemId": "ffffffff0000000034e457d7a86ac007",
      "chargeFormItemStatus": 0,
      "receivedUnitCount": 1,
      "receivedTotalPrice": 0,
      "receivedUnitPrice": 0,
      "chargeFormItemStatusName": "未收费"
    }
  ],
  "canExecute": 1,
  "diagnosis": null,
  "patient": {
    "id": "82d6754a0ce048899b5fefd0d73eeeb3",
    "name": "张",
    "namePy": "zhang",
    "namePyFirst": "Z",
    "mobile": "13881889623",
    "countryCode": null,
    "sex": "男",
    "birthday": "1990-01-01",
    "age": {
      "year": 34,
      "month": 11,
      "day": 16
    },
    "isMember": 1,
    "idCard": "000000001652500000",
    "marital": null,
    "weight": null,
    "importFlag": 0,
    "ethnicity": null,
    "nationality": null,
    "contactName": null,
    "contactRelation": null,
    "contactMobile": null,
    "sn": "271506",
    "remark": null,
    "profession": "",
    "company": null,
    "companyMobile": null,
    "blockFlag": 0,
    "address": {
      "addressCityId": null,
      "addressCityName": null,
      "addressDetail": null,
      "addressDistrictId": null,
      "addressDistrictName": null,
      "addressGeo": null,
      "addressProvinceId": null,
      "addressProvinceName": null,
      "addressPostcode": null,
      "fullAddress": ""
    },
    "familyMobile": null,
    "tags": null,
    "activeDate": "2024-12-16T11:23:43.000+00:00",
    "activeClinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "lastOutpatientDate": "2024-12-16T11:23:41.000+00:00",
    "lastOutpatientClinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "lastOutpatientDoctorId": "6e45706922a74966ab51e4ed1e604641",
    "wxOpenId": null,
    "unionId": null,
    "wxUserId": null,
    "wxNickName": null,
    "wxHeadImgUrl": null,
    "isWxMainPatient": 0,
    "wxBindStatus": 0
  },
  "registrationFormItem": null,
  "examinationSheetReport": {
    "id": "ffffffff0000000034e457d7e5048003",
    "examinationSheetId": "ffffffff0000000034e457d7e5048002",
    "mergeSheetId": "3811267769434144773",
    "goodsType": 3,
    "type": 1,
    "subType": 0,
    "deviceType": 4,
    "imageFiles": [],
    "method": null,
    "videoDescription": null,
    "resultInfo": null,
    "advice": null,
    "suggestion": null,
    "diagnosisFlag": null,
    "versionFlag": 0,
    "recordDoctorId": null,
    "consultationDoctorId": null,
    "principalDoctorId": null,
    "examinationName": "肺炎支原体IgM抗体检测",
    "diagnosisEntryItems": [
      {
        "id": "3811274484078755840",
        "type": 1,
        "deviceType": 4,
        "name": "肺炎支原体",
        "abnormalFlag": 10,
        "tag": 30
      }
    ],
    "recordDoctorName": null,
    "consultationDoctorName": null,
    "principalDoctorName": null,
    "principalDoctorMobile": null
  },
  "samplePipe": null,
  "deviceModelId": "3808806073960333312",
  "deviceRoomId": null,
  "deviceView": {
    "id": "3808806073960333312",
    "deviceModelId": "3808806073960333312",
    "name": "血常规51",
    "model": "51",
    "deviceUuid": "红岸基元·51",
    "manufacture": "红岸基元",
    "iconUrl": "https://cd-cis-static-assets-test.oss-cn-chengdu.aliyuncs.com/oa/lis-device/image/FqIFUfQZsrv6BsPn93tD2NLLd2mYuzzI_1730705126515.png",
    "remarks": "123",
    "goodsType": 3,
    "goodsSubType": 1,
    "goodsExtendSpec": null,
    "supplierId": 1,
    "deviceType": 1,
    "deviceTypeName": "临床检验",
    "usageType": 1,
    "usageTypeName": "血液分析",
    "innerFlag": 0,
    "sampleNoRule": {
      "prefix": null,
      "length": 1,
      "format": null
    },
    "connectStatus": 20,
    "deviceModelStatus": 0,
    "lastModified": "2024-11-04T07:25:27Z",
    "lastModifiedBy": "00000000000000000000000000000000",
    "deviceStatus": 0,
    "clinicInfos": [
      {
        "organ": {
          "id": "fff730ccc5ee45d783d82a85b8a0e52d",
          "parentId": "6a869c22abee4ffbaef3e527bbb70aeb",
          "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
          "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
          "name": "四川省成都市高新区交子大道高新大源店",
          "shortName": "高新大原店",
          "nodeType": 2,
          "viewMode": 0,
          "hisType": 0,
          "shortNameFirst": "高新大原店"
        },
        "deviceId": "1689091309109336",
        "shortId": "AABCCF0001",
        "deviceStatus": 10,
        "deviceStatusName": "使用中",
        "created": "2024-12-16T11:20:30Z"
      }
    ],
    "extendInfos": {
      "develops": [
        {
          "id": "JiangXiaoFeng",
          "name": "蒋晓风"
        }
      ]
    }
  },
  "examinationApplySheetView": {
    "id": "3811267769434144772",
    "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
    "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "doctorId": "6e45706922a74966ab51e4ed1e604641",
    "departmentId": "59140f8ecdeb4553ab570f710274e0ab",
    "patientId": "82d6754a0ce048899b5fefd0d73eeeb3",
    "patientOrderId": "ffffffff0000000034e457d7a22c8000",
    "registrationFormItemId": null,
    "deviceId": null,
    "deviceRoomId": null,
    "no": "JY2412160002",
    "businessType": 20,
    "goodsType": 3,
    "type": 1,
    "subType": 0,
    "deviceType": null,
    "chiefComplaint": "咳嗽",
    "presentHistory": null,
    "physicalExamination": null,
    "diagnosisInfos": [
      {
        "id": null,
        "diseaseName": "急性上呼吸道感染",
        "diseaseCode": "J06.900"
      }
    ],
    "purpose": null,
    "planExecuteDate": "2024-12-16",
    "created": "2024-12-16T11:23:43Z",
    "status": 20,
    "patient": null,
    "dcm4cheeView": null,
    "pharmacyType": 0,
    "pharmacyNo": 0,
    "coChainId": null,
    "coClinicId": null,
    "doctorName": null
  },
  "location": null,
  "peSheetSimpleView": null,
  "examinationOrder": {
    "id": "3811267769434144777",
    "patientOrderId": "ffffffff0000000034e457d7a22c8000",
    "mergeSheetId": "3811267769434144773",
    "type": 1,
    "no": "Y2024121600001",
    "status": 20,
    "receivableFee": 74.5,
    "depositDeductionFee": 0,
    "receivedFee": 74.5,
    "depositBusinessId": null,
    "payOrderId": "3811267769725386753",
    "payMode": 1,
    "payTime": "2024-12-16T11:23:44Z",
    "created": "2024-12-16T11:23:44Z"
  },
  "examinationGoodsItems": [
    {
      "goodsVersion": 0,
      "id": "ffffffff0000000034e457c3158cc000",
      "goodsId": "ffffffff0000000034e457c3158cc000",
      "status": 1,
      "name": "肺炎支原体IgM抗体检测",
      "displayName": "肺炎支原体IgM抗体检测",
      "displaySpec": "次",
      "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
      "typeId": 20,
      "type": 3,
      "subType": 1,
      "pieceNum": 1,
      "pieceUnit": null,
      "packageUnit": "次",
      "dismounting": 0,
      "medicineCadn": "",
      "position": null,
      "chainPackagePrice": 49,
      "piecePrice": null,
      "packagePrice": 49,
      "packageCostPrice": 0,
      "fixedPackagePrice": 49,
      "priceType": 1,
      "inTaxRat": 0,
      "outTaxRat": 0,
      "needExecutive": 0,
      "hospitalNeedExecutive": 0,
      "shortId": "HAJYABC007",
      "composePackageCount": 1,
      "composePrice": 49,
      "composePackagePrice": 49,
      "chainComposePackagePrice": 49,
      "composeUseDismounting": 0,
      "composeSort": 0,
      "disableComposePrint": 0,
      "children": [
        {
          "goodsVersion": 0,
          "id": "ffffffff0000000034e457c0f58cc048",
          "goodsId": "ffffffff0000000034e457c0f58cc048",
          "status": 1,
          "name": "肺炎支原体",
          "displayName": "肺炎支原体",
          "displaySpec": "次",
          "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
          "typeId": 20,
          "type": 3,
          "subType": 1,
          "pieceNum": 1,
          "pieceUnit": null,
          "packageUnit": "次",
          "dismounting": 0,
          "medicineCadn": "",
          "position": null,
          "chainPackagePrice": 49,
          "piecePrice": null,
          "packagePrice": 49,
          "packageCostPrice": 0,
          "fixedPackagePrice": 49,
          "priceType": 1,
          "inTaxRat": 0,
          "outTaxRat": 0,
          "needExecutive": 0,
          "hospitalNeedExecutive": 0,
          "shortId": "300000905086698",
          "composePackageCount": 1,
          "composePrice": 49,
          "composePackagePrice": 49,
          "chainComposePackagePrice": 49,
          "composeUseDismounting": 0,
          "composeSort": 0,
          "disableComposePrint": 0,
          "createdUserId": "566fdc8b20c64d6d953e9c36991c4830",
          "lastModifiedUserId": "566fdc8b20c64d6d953e9c36991c4830",
          "lastModifiedDate": "2024-12-16T11:20:39Z",
          "combineType": 0,
          "bizExtensions": {
            "bizRefId": "ffffffff0000000034e1d5ff1af58000",
            "providerId": 1,
            "sampleType": "全血",
            "itemCategory": "1"
          },
          "enName": "MP IgM",
          "bizRelevantId": "3808806073960333312",
          "extendSpec": "",
          "deviceInfo": {
            "model": "51",
            "name": "血常规51",
            "deviceModeId": "3808806073960333312",
            "deviceUuid": "红岸基元·51",
            "manufacture": "红岸基元",
            "iconUrl": "https://cd-cis-static-assets-test.oss-cn-chengdu.aliyuncs.com/oa/lis-device/image/FqIFUfQZsrv6BsPn93tD2NLLd2mYuzzI_1730705126515.png",
            "deviceType": 1,
            "deviceTypeName": "临床检验",
            "usageType": 1,
            "usageTypeName": "血液分析",
            "innerFlag": 0
          },
          "deviceType": 1,
          "medicalFeeGrade": 0,
          "disable": 1,
          "chainDisable": 0,
          "v2DisableStatus": 0,
          "chainV2DisableStatus": 0,
          "disableSell": 1,
          "isSell": 0,
          "customTypeId": "0",
          "chainPackageCostPrice": 0,
          "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
          "shebao": {
            "goodsId": "ffffffff0000000034e457c0f58cc048",
            "goodsType": 3,
            "isDummy": 0,
            "medicineNum": "300000905086698",
            "medicalFeeGrade": 0
          },
          "pharmacyType": 0,
          "pharmacyNo": 0,
          "defaultInOutTax": 0,
          "dispenseAveragePackageCostPrice": 0,
          "innerFlag": 0,
          "deviceInnerFlag": 0,
          "feeComposeType": 0,
          "feeTypeId": "20",
          "usePieceUnitFlag": 0,
          "copiedFlag": 0,
          "coopFlag": 0,
          "cloudSupplierFlag": 1,
          "expiredWarnMonths": 30,
          "dangerIngredient": 0,
          "isPreciousDevice": 0,
          "cMSpec": ""
        }
      ],
      "createdUserId": "566fdc8b20c64d6d953e9c36991c4830",
      "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
      "lastModifiedDate": "2024-12-16T11:22:21Z",
      "combineType": 1,
      "bizExtensions": {
        "bizRefId": "ffffffff0000000034e1d62c7af58000",
        "providerId": 1,
        "sampleType": "全血",
        "itemCategory": "4"
      },
      "bizRelevantId": null,
      "extendSpec": "",
      "deviceType": 4,
      "medicalFeeGrade": 0,
      "disable": 0,
      "chainDisable": 0,
      "v2DisableStatus": 0,
      "chainV2DisableStatus": 0,
      "disableSell": 0,
      "isSell": 1,
      "customTypeId": "0",
      "chainPackageCostPrice": 0,
      "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
      "shebao": {
        "goodsId": "ffffffff0000000034e457c3158cc000",
        "goodsType": 3,
        "isDummy": 0,
        "medicineNum": "HAJYABC007",
        "medicalFeeGrade": 0
      },
      "pharmacyType": 0,
      "pharmacyNo": 0,
      "defaultInOutTax": 0,
      "dispenseAveragePackageCostPrice": 0,
      "innerFlag": 0,
      "deviceInnerFlag": 0,
      "feeComposeType": 0,
      "feeTypeId": "20",
      "usePieceUnitFlag": 0,
      "copiedFlag": 0,
      "coopFlag": 0,
      "cloudSupplierFlag": 1,
      "expiredWarnMonths": 30,
      "dangerIngredient": 0,
      "isPreciousDevice": 0,
      "cMSpec": ""
    }
  ],
  "deviceType": 4,
  "isMerge": 0,
  "importFlag": 0,
  "updateItemsValueFlag": 1,
  "reportInvalidFlag": 1,
  "isMutualRecognition": 0,
  "deviceStatus": 0,
  "goodsSubType": 1,
  "innerFlag": 0,
  "departmentName": "小儿外科诊室",
  "departmentId": "59140f8ecdeb4553ab570f710274e0ab",
  "doctorName": "丁柱112",
  "examinationApplySheetNo": "JY2412160002",
  "sellerName": "丁柱112",
  "clinicPrintName": "高新大原店",
  "deviceName": "血常规51",
  "lastModifiedMillsTime": "1734360731000",
  "lastModifiedTime": "2024-12-16T14:52:11Z",
  "name": "肺炎支原体IgM抗体检测",
  "modifier": {
    "id": "6e45706922a74966ab51e4ed1e604641",
    "name": "丁柱112",
    "mobile": "***********",
    "countryCode": "86",
    "status": 1,
    "created": "2018-01-09 10:11:38",
    "shortId": "939523477594734595",
    "namePy": "dingzhu112|zhengzhu112",
    "namePyFirst": "DZ112|ZZ112",
    "headImgUrl": "https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001ea0b8f80078c000/doctor/2_D4FOMhXaVkeq.png",
    "handSign": "https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/ZeEg9sFSOQA1UPYjGaMADMCyEk2aLpJd_1733724525248",
    "wechatOpenIdMp": "1",
    "wechatNickName": "刘喜",
    "wechatUnionId": "o2VGt0-_RMKgynfy3e6aipz3G268"
  },
  "extendDiagnosisInfos": [
    {
      "toothNos": null,
      "value": [
        {
          "code": "J06.900",
          "name": "急性上呼吸道感染",
          "diseaseType": null,
          "hint": "医保ICD10"
        }
      ]
    }
  ],
  "modifierName": "丁柱112"
}
-->