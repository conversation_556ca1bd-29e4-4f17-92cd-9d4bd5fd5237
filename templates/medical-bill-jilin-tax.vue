<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    socialName: '诊费',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 100.11,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    socialName: 'HPV基因全套',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 320,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    socialName: '甲胎蛋白（AFP）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 40,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '针灸理疗套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
        },

        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    socialName: '四环素软膏（三益）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '支',
                    discountedPrice: 36.0,
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    socialName: '法莫替丁片（迪诺洛克）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '片',
                    discountedPrice: 6.0,
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    socialName: '胸腺肽肠溶片（奇莫欣）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '盒',
                    discountedPrice: 20.0,
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            curYearBalance: 8, // 当年账户余额
            allYearBalance: 10, // 历年账户余额
            curYearAccountPaymentFee: 7, // 本年账户支付
            allYearAccountPaymentFee: 9, // 历年账户支付
            fundPayment: 0, // 规定病种
            cashPayment: 0, // 规定病种
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 1, // 自负金额
            allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 3, // 自理金额
            allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 5, // 自费金额
            allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            sbzzPaymentFee: 0, // 商保赔付
        },
    },
}
-->

<template>
    <div>
        <div class="jilin-medical-bill-content">
            <div
                v-if="blueInvoiceData"
                style="position: absolute; top: 0.4cm; left: 1.4cm;"
            >
                销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }}
                号码：{{ blueInvoiceData.invoiceNumbers[ 0 ] }}
            </div>
            <refund-icon
                v-if="isRefundBill"
                top="0.7cm"
                left="1.4cm"
            ></refund-icon>
            <template v-for="item in [0,1]">
                <div :style="{position: 'absolute',left: `${item * 67 }mm`}">
                    <div class="organ">
                        {{ jilin.institutionName }}
                    </div>
                    <span style="left: 60mm;top: 15.5mm;position: absolute">{{ shebaoPayment.cardId }}</span>

                    <div class="patient-name">
                        {{ patient.name }}
                        <!--            <template v-if="shebaoPayment.cardId">-->
                        <!--                {{ shebaoPayment.cardId }}-->
                        <!--            </template>-->
                    </div>


                    <div class="owner-type">
                        {{ shebaoPayment.cardOwnerType }}
                    </div>


                    <span class="row-1 col-1">{{ westernMedicineFee|formatMoney }}</span>
                    <span class="row-2 col-1">{{ chineseComposeMedicineFee|formatMoney }}</span>
                    <span class="row-3 col-1">{{ chineseMedicineDrinksPieceFee|formatMoney }}</span>
                    <span class="row-4 col-1">{{ examinationExaminationFee|formatMoney }}</span>

                    <span class="row-1 col-2">{{ examinationInspectionFee|formatMoney }}</span>
                    <span class="row-5 col-2">{{ treatmentFee|formatMoney }}</span>
                    <template v-if="isFeeCompose">
                        <span class="row-8 col-2">{{ nursingFee|formatMoney }}</span>
                        <span class="row-9 col-2">{{ operationFee|formatMoney }}</span>
                    </template>
                    <span
                        class="row-6"
                        style="left: 48mm; width: 20mm"
                    >其他</span>
                    <span class="row-6 col-2">{{ otherFeeTotal|formatMoney }}</span>


                    <div
                        class="fee-item"
                        style="left: 48mm;top: 85mm;position: absolute"
                    >
                        {{ shebaoPayment.accountPaymentFee | formatMoney }}
                    </div>
                    <div
                        class="fee-item"
                        style="left: 48mm;top: 91mm;position: absolute"
                    >
                        {{ printData.receivedFee | formatMoney }}
                    </div>

                    <div
                        class="fee-item"
                        style="left: 48mm;top: 97mm;position: absolute"
                    >
                        {{ printData.personalPaymentFee | formatMoney }}
                    </div>

                    <div
                        class="fee-item"
                        style="left: 48mm;top: 120mm;position: absolute"
                    >
                        {{ digitUppercase(finalFee) }}
                    </div>

                    <div
                        class="fee-item"
                        style="left: 48mm;top: 125mm;position: absolute;"
                    >
                        {{ shebaoPayment.cardBalance | formatMoney }}
                    </div>

                    <div class="charger">
                        {{ printData.chargedByName }}
                    </div>
                    <span class="date">
                        <span class="year">{{ year }}</span>
                        <span class="month">{{ month }}</span>
                        <span class="day">{{ day }}</span>
                    </span>
                </div>
            </template>

            <template v-for="itemNo in [0,1,2]">
                <div
                    :key="`${itemNo}-item-no`"
                    class="vice-union"
                    :style="{position: 'absolute', top: `${itemNo * 47 }mm`}"
                >
                    <span style="left: 14mm;top: 14mm;position: absolute">{{ shebaoPayment.cardId }}</span>
                    <span style="left: 53mm;top: 14mm;position: absolute">{{ patient.name }}</span>
                    <span style="left: 15mm;top: 19mm;position: absolute">
                        {{ year }}-{{ month }}-{{ day }}
                    </span>
                    <span style="left: 53mm;top: 19mm;position: absolute">{{ printData.departmentName }}</span>

                    <div
                        v-for="(item, index) in limitMedicalBill"
                        :key="`${index }` + `fee`"
                        class="fee-item"
                        :style="{ 'left': index % 2 === 0 ? '8mm' : '42mm', 'top': `${Math.floor(index / 2) * 4 + 24 }mm` }"
                    >
                        <span class="fee-name">{{ item.name }}</span>
                        <span class="fee-amount">{{ item.totalFee | formatMoney }}</span>
                    </div>
                    <span
                        class="fee-item"
                        :style="{ 'left': 6 % 2 === 0 ? '8mm' : '42mm', 'top': `${Math.ceil(limitMedicalBill.length / 2) * 4 + 24}mm` }"
                    >其他费用{{ otherFeeTotal|formatMoney }}</span>
                    <div
                        class="fee-item"
                        :style="{ 'left': 8 % 2 === 0 ? '8mm' : '42mm', 'top': `${(Math.ceil((limitMedicalBill.length) / 2) + 1) * 4 + 24}mm` }"
                    >
                        合计：{{ finalFee | formatMoney }}
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { MM242_151_JILIN_TAX, Orientation } from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import {PRINT_BILL_AMOUNT_TYPE} from "./common/constants";

    export default {
        name: "MedicalBillJilin",
        components: {
            RefundIcon
        },
        filters: {
            filterIdCard( idCard ) {
                if (!idCard) return '';
                if (typeof idCard === 'string') {
                    return `${idCard.substr( 0, 2 )}********${idCard.substr( 11, 4 )}****`;
                }
                return idCard;
            },
        },
        mixins: [ BillDataMixins ],
        data() {
            return {
                registrationFee: 0, // 挂号费
                westernMedicineFee: 0, // 西药费
                chineseMedicineFee: 0,
                chineseMedicineDrinksPieceFee: 0, // 中药饮片费用
                chineseComposeMedicineFee: 0, // 中成药费用 + 中药颗粒
                treatmentFee: 0, // 治疗理疗费
                examinationInspectionFee: 0, // 检查费
                examinationExaminationFee: 0, // 化验费
                materialFee: 0, // 材料费
                examinationFee: 0, // 诊察费
                nursingFee: 0, // 护理费
                physiotherapyFee: 0, // 理疗费
                bedFee: 0, // 床位费
                operationFee: 0, // 手术费
                generalDiagnosisAndTreatmentFee: 0, // 一般诊疗费
                pharmacyServiceFee: 0, // 药事服务费
                otherFee: 0, // 一般诊疗费( 其他费用 )
                outerFlagFee: 0, // 非内置费用
            }
        },
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_JILIN,
        pages: [
            {
                paper: PageSizeMap.MM242_151_JILIN_TAX,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            jilin() {
                return this.config.jilin || {};
            },
            totalFee() {
                return this.printData && this.printData.receivableFee;
            },
            netIncomeFee() {
                return this.printData && this.printData.netIncomeFee;
            },

            otherFeeTotal() {
                if (this.isFeeCompose) {
                    return this.registrationFee + this.materialFee + this.examinationFee + this.physiotherapyFee + this.bedFee + this.generalDiagnosisAndTreatmentFee + this.pharmacyServiceFee + this.outerFlagFee;
                }
                return this.otherFee + this.materialFee + this.registrationFee
            },
            limitMedicalBill() {
                return this.medicalBills.filter( item => {
                    return item.printType !== PRINT_BILL_AMOUNT_TYPE.registrationFeeType && item.printType !== PRINT_BILL_AMOUNT_TYPE.materialFeeType && item.printType !== PRINT_BILL_AMOUNT_TYPE.otherFeeType
                } )
            },
            allLimitMedicalBill() {
                const res = [[], [], []];
                const cacheMedicalBills = JSON.parse(JSON.stringify((this.medicalBills)));
                cacheMedicalBills.forEach((item, index) => {
                    if (index % 3 === 0) {
                        res[0].push(item);
                    } else if (index % 3 === 1) {
                        res[1].push(item);
                    } else {
                        res[2].push(item);
                    }
                });
                // 计算合计
                res.forEach((item) => {
                    let totalFee = 0;
                    item.forEach((ele) => {
                        totalFee += ele.totalFee;
                    });
                    item[0].allTotalFee = totalFee;
                });
                return res;
            },
        },
        created() {
            this.initFee();
        },
        methods: {
            initFee() {
                this.medicalBills.forEach( ( item ) => {
                    switch (item.printType) {
                        case PRINT_BILL_AMOUNT_TYPE.westernMedicineFeeType:
                            this.westernMedicineFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.chineseMedicineDrinksPieceFeeType:
                            this.chineseMedicineDrinksPieceFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.chineseComposeMedicineFeeType:
                            this.chineseComposeMedicineFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.examinationInspectionFeeType:
                            this.examinationInspectionFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.examinationExaminationFeeType:
                            this.examinationExaminationFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.treatmentFeeType:
                            this.treatmentFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.registrationFeeType:
                            this.registrationFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.materialFeeType:
                            this.materialFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.otherFeeType:
                            this.otherFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.examinationType:
                            this.examinationFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.nursingType:
                            this.nursingFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.physiotherapyType:
                            this.physiotherapyFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.bedType:
                            this.bedFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.operationType:
                            this.operationFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.generalDiagnosisAndTreatmentType:
                            this.generalDiagnosisAndTreatmentFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.pharmacyServiceType:
                            this.pharmacyServiceFee = item.totalFee;
                            break;
                        default:
                            break;
                    }
                    // 将自定义的费用类型合并到outerFlagFee中方便后续计算
                    // 0 自定义  1 内置
                    if (item.innerFlag === 0) {
                        this.outerFlagFee += item.totalFee;
                    }
                } );
            },
            transAmount( amount ) {
                const output = [];
                for (let i = 0; i < 8; i++) {
                    output.push( '零' );
                }
                amount += '';
                amount = amount.replace( /\./g, '' );
                const digit = [ '零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖' ];
                let digitIndex = 7;
                for (let i = amount.length - 1; i >= 0; i--) {
                    output[ digitIndex ] = digit[ amount[ i ] ];
                    digitIndex--;
                }
                return output;
            },
            getLeft( index ) {
                if (index % 4 === 0) {
                    return '15mm';
                }
                if (index % 4 === 1) {
                    return '46mm';
                }
                if (index % 4 === 2) {
                    return '76mm';
                }
                if (index % 4 === 3) {
                    return '109mm';
                }
            },
        },

    }
</script>
<style lang="scss">
* {
    padding: 0;
    margin: 0;
}

.jilin-medical-bill-content {
    @import './components/refund-icon/refund-icon.scss';

    position: absolute;
    top: 0;
    left: 0;
    font-size: 9pt;
    line-height: 10pt;
    height: 140mm;
    width: 240mm;

    .id-number,
    .organ,
    .patient-name,
    .created-date,
    .charge-item,
    .total-price,
    .outpatient-no,
    .row-1,
    .row-2,
    .row-3,
    .row-4,
    .row-5,
    .row-6,
    .row-7,
    .row-8,
    .row-9,
    .settle-no,
    .record-no,
    .charger,
    .owner-type,
    .vice-union {
        position: absolute;
    }

    .owner-type {
        top: 25mm;
        left: 33mm;
        width: 20mm;
    }

    .fee-item {
        position: absolute;
        width: 50mm;
        top: 20mm;
    }

    .created-date {
        top: 21.5mm;
        left: 31mm;
    }

    .organ {
        top: 15.5mm;
        width: 95mm;
        left: 34mm;
    }

    .patient-name,
    .outpatient-no {
        width: 95mm;
        top: 20mm;
    }

    .patient-name {
        left: 33mm;
    }

    .outpatient-no {
        left: 60mm;
    }

    .settle-no {
        top: 25mm;
        left: 116mm;
    }

    .record-no {
        top: 30mm;
        left: 116mm;
    }

    .row-1 {
        top: 39.5mm;
    }

    .row-2 {
        top: 46mm;
    }

    .row-3 {
        top: 52mm;
    }

    .row-4 {
        top: 58mm;
    }

    .row-5 {
        top: 73mm;
    }

    .row-6 {
        top: 79mm;
    }

    .row-7 {
        top: 95mm;
    }

    .row-8 {
        top: 62mm;
    }

    .row-9 {
        top: 45.5mm;
    }

    .col-1 {
        left: 31mm;
    }

    .col-2 {
        left: 62mm;
    }

    .col-3 {
        left: 82mm;
    }

    .col-4 {
        left: 109mm;
    }

    .charger {
        width: 95mm;
        top: 131mm;
        left: 31mm;
    }

    .vice-union {
        top: 0;
        left: 151mm;
        width: 110mm;
    }

    .date {
        left: 45mm;
        top: 131mm;
        position: absolute;
        width: 95mm;

        .year, .month, .day {
            position: absolute;
        }

        .year {
            left: 0;
        }

        .month {
            left: 13mm;
        }

        .day {
            left: 20mm;
        }
    }

}

.abc-page_preview {
    background: url("/static/assets/print/jilin-tax.jpg");
    background-size: 242mm 151mm;
    color: #2a82e4;
}
</style>
