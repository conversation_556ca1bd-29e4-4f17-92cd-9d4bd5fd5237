<template>
    <div class="hospital-cashier-wrapper">
        <!-- 门店信息 -->
        <div class="hospital-cashier-header-wrapper">
            <!-- 门店 logo -->
            <div
                v-if="clinicInfo.titleStyle === 1"
                class="hospital-cashier-logo-wrapper"
            >
                <img
                    v-if="logo"
                    :src="logo"
                    class="hospital-cashier-logo-image"
                    alt=""
                />
                <div
                    v-else-if="extra.isPreview"
                    class="hospital-cashier-no-logo"
                >
                    未上传Logo
                </div>
            </div>

            <!-- 门店名称 -->
            <div class="hospital-cashier-organ-title">
                {{ organTitle }}
            </div>

            <!-- 门店地址 -->
            <div
                v-if="clinicInfo.address && clinicBasicConfig.addressDetail"
                class="hospital-cashier-header-text-info"
            >
                {{ clinicBasicConfig.addressDetail }}
            </div>

            <!-- 门店电话 -->
            <div
                v-if="clinicInfo.mobile && clinicBasicConfig.contactPhone"
                class="hospital-cashier-header-text-info"
            >
                {{ clinicBasicConfig.contactPhone }}
            </div>

            <!-- 标题 -->
            <div class="hospital-cashier-header-type">
                收费单
            </div>
        </div>

        <!-- 患者信息 -->
        <div class="hospital-cashier-patient-info-wrapper">
            <!-- 姓名 -->
            <div class="hospital-cashier-patient-name">
                <span>姓名：</span>
                <span>{{ patient.name || '匿名患者' }}</span>
                <span
                    v-if="patientInfo.sex"
                    class="hospital-cashier-patient-item"
                >{{ patient.sex }}</span>
                <span
                    v-if="patientInfo.age"
                    class="hospital-cashier-patient-item"
                >{{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}</span>
            </div>

            <!-- 手机 -->
            <div
                v-if="patientInfo.mobile && patient.mobile"
                class="hospital-cashier-patient-name"
            >
                <span>手机：</span>
                <span>{{ patient.mobile | filterMobileV2(patientInfo.mobileType === 0 ? 2 : 1) }}</span>
            </div>

            <!-- 科室 -->
            <div
                v-if="patientInfo.department && patientOrderInfo.departmentName"
                class="hospital-cashier-patient-name"
            >
                <span>科室：</span>
                <span>{{ patientOrderInfo.departmentName }}</span>
            </div>

            <!-- 住院号 -->
            <div
                v-if="patientInfo.inPatientNo && patientOrderInfo.no"
                class="hospital-cashier-patient-name"
            >
                <span>住院号：</span>
                <span>{{ patientOrderInfo.no }}</span>
            </div>

            <!-- 入院日期 -->
            <div
                v-if="patientInfo.inpatientTime && (patientOrderInfo.inpatientTimeRequest || patientOrderInfo.inpatientTime)"
                class="hospital-cashier-patient-name"
            >
                <span>入院日期：</span>
                <span>{{ (patientOrderInfo.inpatientTimeRequest || patientOrderInfo.inpatientTime) | formatDate }}</span>
            </div>

            <!-- 出院日期 -->
            <div
                v-if="patientInfo.dischargeTime && patientOrderInfo.dischargeTime"
                class="hospital-cashier-patient-name"
            >
                <span>出院日期：</span>
                <span>{{ patientOrderInfo.dischargeTime | formatDate }}</span>
            </div>
        </div>
        
        <spacing-line></spacing-line>

        <!-- 费用类别明细 -->
        <div
            v-if="config.feeType"
            class="hospital-cashier-fee-type-wrapper"
        >
            <div
                v-for="(item, idx) in itemGroupByGoodsTypePrintInfos"
                :key="`hospital-cashier-fee-type-key-${idx}`"
                class="hospital-cashier-fee-type-item"
            >
                <div>{{ item.goodsTypeName }}</div>
                <div>{{ item.totalPrice | formatMoney }}</div>
            </div>

            <spacing-line></spacing-line>
        </div>
        
        <!-- 收银信息 -->
        <div
            v-if="cashierInfo.receivableFee || cashierInfo.depositFee || cashierInfo.netIncomeFee"
            class="hospital-cashier-pay-wrapper"
        >
            <div
                v-if="cashierInfo.receivableFee"
                class="hospital-cashier-pay-item"
            >
                <span>应付：</span>
                <span>{{ printData.totalFee | formatMoney }}</span>
            </div>
            <div
                v-if="cashierInfo.depositFee"
                class="hospital-cashier-pay-item"
            >
                <span>预缴金：</span>
                <span>{{ printData.depositReceivedFee | formatMoney }}</span>
            </div>
            <div
                v-if="cashierInfo.netIncomeFee"
                class="hospital-cashier-pay-item"
            >
                <span>
                    实付：
                    <template v-if="printData.refundFee">(退押金){{ printData.refundFee | formatMoney }}&nbsp;</template>
                    <template v-if="settleTransactionPrintInfos.length">
                        <template
                            v-for="it in settleTransactionPrintInfos"
                        >({{ it.payModeDisPlayName }}){{ it.amount | formatMoney }}&nbsp;</template>
                    </template>
                    <template v-if="shebaoPayment.receivedFee">(医保){{ shebaoPayment.receivedFee | formatMoney }}&nbsp;</template>
                </span>
            </div>

            <spacing-line></spacing-line>
        </div>
        
        <!-- 医保信息 -->
        <div
            v-if="hasShebaoPayment && (healthCardInfo.balanceInfo || healthCardInfo.settlementInfo || healthCardInfo.cardInfo)"
            class="hospital-cashier-shebao-wrapper"
        >
            <!-- 医保卡信息 -->
            <div
                v-if="healthCardInfo.cardInfo"
                class="hospital-cashier-shebao-card-info-wrapper"
            >
                <div class="hospital-cashier-shebao-card-info-item">
                    <span>医保号：</span>
                    <span>{{ shebaoPayment.cardId }}</span>
                </div>
                <div class="hospital-cashier-shebao-card-info-item">
                    <span>人员编号：</span>
                    <span>{{ extraInfo.psnNo }}</span>
                </div>
                <div class="hospital-cashier-shebao-card-info-item">
                    <div class="hospital-cashier-shebao-card-info-card-owner">
                        <span>持卡人：</span>
                        <span>{{ shebaoPayment.cardOwner }}</span>
                    </div>
                    <div class="hospital-cashier-shebao-card-info-card-owner">
                        <span>关系：</span>
                        <span>{{ shebaoPayment.relationToPatient }}</span>
                    </div>
                </div>
            </div>

            <!-- 结算信息 -->
            <div
                v-if="healthCardInfo.settlementInfo"
                class="hospital-cashier-shebao-card-info-wrapper"
            >
                <div class="hospital-cashier-shebao-card-info-item">
                    <span>医疗类别：</span>
                    <span>{{ shebaoPayment.medType }}</span>
                </div>
                <div
                    v-if="isJinan"
                    class="hospital-cashier-shebao-card-info-item"
                >
                    <span>医保支付方式：</span>
                    <span>{{ extraInfo.sfrzfs }}</span>
                </div>
                <div
                    v-if="!isEmpty(shebaoPayment.fundPaymentFee) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item"
                >
                    <span>基金支付：</span>
                    <span>{{ shebaoPayment.fundPaymentFee | formatMoney }}</span>
                </div>
                <div
                    v-if="!isEmpty(extraInfo.hifpPay) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item hospital-cashier-left-13px"
                >
                    <span>统筹支出：</span>
                    <span>{{ extraInfo.hifpPay | formatMoney }}</span>
                </div>
                <div
                    v-if="!isEmpty(extraInfo.cvlservPay) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item hospital-cashier-left-13px"
                >
                    <span>公务员补助：</span>
                    <span>{{ extraInfo.cvlservPay | formatMoney }}</span>
                </div>
                <div
                    v-if="!isEmpty(extraInfo.hifmiPay) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item hospital-cashier-left-13px"
                >
                    <span>大病保险：</span>
                    <span>{{ extraInfo.hifmiPay | formatMoney }}</span>
                </div>
                <div
                    v-if="!isEmpty(extraInfo.hifobPay) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item hospital-cashier-left-13px"
                >
                    <span>大额补助：</span>
                    <span>{{ extraInfo.hifobPay | formatMoney }}</span>
                </div>
                <div
                    v-if="!isEmpty(extraInfo.mafPay) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item hospital-cashier-left-13px"
                >
                    <span>医疗救助：</span>
                    <span>{{ extraInfo.mafPay | formatMoney }}</span>
                </div>
                <div
                    v-if="!isEmpty(extraInfo.hifesPay) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item hospital-cashier-left-13px"
                >
                    <span>企业补充医疗保险：</span>
                    <span>{{ extraInfo.hifesPay | formatMoney }}</span>
                </div>
                <div
                    v-if="!isEmpty(extraInfo.othPay) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item hospital-cashier-left-13px"
                >
                    <span>其他支出：</span>
                    <span>{{ extraInfo.othPay | formatMoney }}</span>
                </div>
                <div
                    v-if="!isEmpty(shebaoPayment.accountPaymentFee) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item"
                >
                    <span>账户支付：</span>
                    <span>{{ shebaoPayment.accountPaymentFee | formatMoney }}</span>
                    <span v-if="!isEmpty(extraInfo.acctMulaidPay) || isShowZeroSettlementInfo">（共济支付: {{ extraInfo.acctMulaidPay | formatMoney }}）</span>
                </div>
                <div
                    v-if="!isEmpty(shebaoPayment.personalPaymentFee) || isShowZeroSettlementInfo"
                    class="hospital-cashier-shebao-card-info-item"
                >
                    <span>现金支付：</span>
                    <span>{{ shebaoPayment.personalPaymentFee | formatMoney }}</span>
                </div>
                <div class="hospital-cashier-shebao-card-info-item">
                    <span>起付线：</span>
                    <span>{{ extraInfo.actPayDedc | formatMoney }}</span>
                </div>
            </div>

            <!-- 余额信息 -->
            <div
                v-if="healthCardInfo.balanceInfo"
                class="hospital-cashier-shebao-card-info-wrapper"
            >
                <div class="hospital-cashier-shebao-card-info-item">
                    <span>医保余额：</span>
                    <span>{{ shebaoPayment.cardBalance | formatMoney }}</span>
                </div>
                <div class="hospital-cashier-shebao-card-info-item">
                    <span>原有余额：</span>
                    <span>{{ shebaoPayment.beforeCardBalance | formatMoney }}</span>
                </div>
            </div>

            <spacing-line></spacing-line>
        </div>

        <!-- 收费信息 -->
        <div
            v-if="isShowChargeInfo"
            class="hospital-cashier-charge-info-wrapper"
        >
            <div
                v-if="clinicInfo.chargeOperator"
                class="hospital-cashier-charge-by"
            >
                <span>收费员：</span>
                <span>{{ printData.sellerName }}</span>
            </div>
            <div
                v-if="clinicInfo.chargeDate"
                class="hospital-cashier-charge-by"
            >
                <span>收费时间：</span>
                <span>{{ printData.chargeTime | formatDate('YYYY-MM-DD HH:mm:ss') }}</span>
            </div>
            <div
                v-if="clinicInfo.printDate"
                class="hospital-cashier-charge-by"
            >
                <span>打印时间：</span>
                <span>{{ new Date() | formatDate('YYYY-MM-DD HH:mm:ss') }}</span>
            </div>
        </div>

        <!-- 公告提示 -->
        <div
            v-if="config.remark"
            class="hospital-cashier-remark-wrapper"
        >
            <spacing-line v-if="isShowChargeInfo"></spacing-line>

            <div
                style="white-space: pre-wrap;"
                v-html="config.remark"
            >
            </div>
        </div>

        <!-- 微医院二维码 -->
        <div
            v-if="clinicInfo.qrCode && qrCode"
            class="hospital-cashier-qrcode-wrapper"
        >
            <img
                class="hospital-cashier-qrcode-img"
                :src="qrCode"
                alt=""
            />
            <div class="hospital-cashier-qrcode-text-wrapper">
                <span>微信就诊</span>
                <span>挂号预约</span>
                <span>就诊报告</span>
                <span>用药医嘱</span>
            </div>
        </div>

        <!-- 电子发票二维码 -->
        <div
            v-if="invoiceQrcode && config.invoiceCode"
            class="hospital-cashier-qrcode-wrapper"
        >
            <img
                class="hospital-cashier-qrcode-img"
                :src="invoiceQrcode"
                alt=""
            />
            <div class="hospital-cashier-qrcode-text-wrapper">
                <span>微信扫一</span>
                <span>扫查看发</span>
                <span>票</span>
            </div>
        </div>

        <!-- 追溯码二维码 -->
        <div
            v-if="traceCodeQrCodeUrl && clinicInfo.traceCodeQrCode"
            class="hospital-cashier-qrcode-wrapper"
        >
            <img
                class="hospital-cashier-qrcode-img"
                :src="traceCodeQrCodeUrl"
                alt=""
            />
            <div class="hospital-cashier-qrcode-text-wrapper">
                <span>扫码查看</span>
                <span>药品追溯</span>
                <span>码</span>
            </div>
        </div>
    </div>
</template>

<script>
    import PrintCommonDataHandler from './data-handler/common-handler';
    import { PrintBusinessKeyEnum } from './constant/print-constant';
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { formatAge, formatMoney } from './common/utils';
    import { filterMobileV2 } from './common/medical-transformat';
    import SpacingLine from './components/medical-document-header/spacing-line.vue';
    import { formatDate } from '@tool/date';

    const initPrintConfig = {
        title: '', // 抬头名称
        // 抬头信息
        clinicInfo: {
            titleStyle: 0, // 抬头样式
            address: 1, // 地址
            mobile: 1, // 电话
            chargeOperator: 1, // 收费员
            chargeDate: 1, // 收费时间
            printDate: 1, // 打印时间
            remark: 0, // 收费备注
            qrCode: 1, // 微诊所二维码
            traceCodeQrCode: 0, // 追溯码
        },
        // 患者信息
        patientInfo: {
            sex: 1, // 性别
            age: 1, // 年龄
            mobile: 1, // 手机
            department: 1, // 科室
            inPatientNo: 1, // 住院号
            medicalRecordNo: 0, // 病历号
            inpatientTime: 1, // 住院日期
            dischargeTime: 1, // 出院日期
            mobileType: 1, // 手机隐私保护, 0:完整展示, 1:隐藏中间4位
        },
        feeType: 1, // 打印费用类型信息
        // 收银信息
        cashierInfo: {
            receivableFee: 1, // 应付
            depositFee: 1, // 预缴金
            netIncomeFee: 1, // 实付
        },
        // 医保结算
        healthCardInfo: {
            cardInfo: 0, // 医保卡信息
            settlementInfo: 1, // 结算信息
            balanceInfo: 0, // 余额信息
            zeroSettlementInfo: 0, // 医保结算信息, 0:不打印金额为0的医保结算信息, 1:打印金额为0的医保结算信息
        },
        remark: '', // 备注
        invoiceCode: 0, // 电子发票二维码 取值 0 1 默认值 0
    };
	
    export default {
        name: 'HospitalCashier',
        components: { SpacingLine },
        DataHandler: PrintCommonDataHandler,
        businessKey: PrintBusinessKeyEnum.HOSPITAL_CASHIER,
        pages: [
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM58,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM70_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80_100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_140,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_93_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM148_118_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM200_1397,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM175_94,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_200,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM90_120_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM241_279,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分', // 默认选择的等分纸
            },
        ],
        filters: {
            filterMobileV2,
            formatDate,
            formatMoney,
        },
        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
            extra: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            config() {
                if (this.renderData.config?.hospitalFeeBills?.hospitalCashier) {
                    return this.renderData.config.hospitalFeeBills.hospitalCashier;
                }
                return initPrintConfig;
            },
            clinicBasicConfig() {
                return this.printData.clinicBasicConfig || {};
            },
            logo() {
                return this.clinicBasicConfig.logo || '';
            },
            organTitle() {
                return this.config.title ? this.config.title : this.printData.clinicName ? this.printData.clinicName : '';
            },
            clinicInfo() {
                return this.config.clinicInfo || {};
            },
            cashierInfo() {
                return this.config.cashierInfo || {};
            },
            patientInfo() {
                return this.config.patientInfo || {};
            },
            healthCardInfo() {
                return this.config.healthCardInfo || {};
            },
            patientOrderInfo() {
                return this.printData.patientOrderInfo || {};
            },
            patient() {
                return this.patientOrderInfo.patient || {};
            },
            itemGroupByGoodsTypePrintInfos() {
                return this.printData.itemGroupByGoodsTypePrintInfos || [];
            },
            shebaoPayment() {
                return this.printData.shebaoPayment || {};
            },
            hasShebaoPayment() {
                return !!this.printData.shebaoPayment;
            },
            extraInfo() {
                return this.shebaoPayment.extraInfo || {};
            },
            isShowZeroSettlementInfo(){
                return this.healthCardInfo.zeroSettlementInfo;
            },
            isShowChargeInfo() {
                return this.clinicInfo.chargeOperator || this.clinicInfo.chargeDate || this.clinicInfo.printDate || this.clinicInfo.remark;
            },
            qrCode() {
                return this.printData.qrCode;
            },
            region() {
                return (this.shebaoPayment && this.shebaoPayment.region) || '';
            },
            isJinan() {
                return this.region === 'shandong_jinan';
            },
            settleTransactionPrintInfos() {
                return (this.printData.settleTransactionPrintInfos || []).filter((x) => x.payMode !== 5);
            },
            invoiceQrcode() {
                return this.printData.invoiceQrcode;
            },
            traceCodeQrCodeUrl() {
                return this.printData.traceCodeQrCodeUrl || '';
            },
        },
        methods: {
            formatAge,
            isEmpty(value) {
                return value === undefined || value === null || value === '' || value === '0' || value === 0
            },
        },
    }
</script>

<style lang="scss">
.hospital-cashier-wrapper {
    font-size: 13px;

    .hospital-cashier-header-wrapper {
        width: 100%;
    }

    .hospital-cashier-logo-wrapper {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    .hospital-cashier-logo-image {
        width: auto;
        height: 44px;
        border: 0;
    }

    .hospital-cashier-no-logo {
        width: 100%;
        text-align: center;
    }

    .hospital-cashier-organ-title {
        width: 100%;
        font-size: 17px;
        text-align: center;
    }

    .hospital-cashier-header-text-info {
        width: 100%;
        line-height: 18px;
        text-align: center;
    }

    .hospital-cashier-header-type {
        width: 100%;
        margin: 3px 0;
        font-size: 17px;
        text-align: center;
    }

    .hospital-cashier-patient-info-wrapper {
        width: 100%;
    }

    .hospital-cashier-patient-name {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .hospital-cashier-patient-item {
        margin-left: 6px;
    }

    .hospital-cashier-fee-type-wrapper {
        width: 100%;
    }

    .hospital-cashier-fee-type-item {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    .hospital-cashier-pay-wrapper {
        width: 100%;
    }

    .hospital-cashier-pay-item {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .hospital-cashier-shebao-wrapper {
        width: 100%;
    }

    .hospital-cashier-shebao-card-info-wrapper {
        width: 100%;
    }

    .hospital-cashier-shebao-card-info-item {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .hospital-cashier-shebao-card-info-card-owner {
        display: flex;
        align-items: center;
        width: 50%;
    }

    .hospital-cashier-left-13px {
        margin-left: 13px;
    }

    .hospital-cashier-charge-info-wrapper {
        width: 100%;
    }

    .hospital-cashier-charge-by {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .hospital-cashier-remark-wrapper {
        width: 100%;
    }

    .hospital-cashier-qrcode-wrapper {
        display: flex;
        gap: 6px;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-top: 8px;
    }

    .hospital-cashier-qrcode-img {
        width: 81px;
        height: 81px;
        border: none;
    }

    .hospital-cashier-qrcode-text-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 81px;
        font-weight: bold;
        line-height: 18px;
    }
}

[data-size=page_热敏小票（58mm）] {
    .hospital-cashier-logo-image {
        height: 35px !important;
    }
}
</style>
