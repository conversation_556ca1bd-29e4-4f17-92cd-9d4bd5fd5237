<template>
    <div>
        <div
            data-type="mix-box"
            class="print-medical-fee-list-wrapper"
        >
            <div
                v-if="showTraceCodeQrCode && printData.traceCodeQrCodeUrl"
                style="position: absolute; top: 4mm; right: 4mm; text-align: center;"
            >
                <img
                    :src="printData.traceCodeQrCodeUrl"
                    alt=""
                    class="print-medical-fee-list-trace-code-img"
                    style="width: 38pt;"
                />
                <div style="font-size: 9pt;">
                    扫码查看追溯码
                </div>
            </div>

            <table
                class="print-medical-fee-list"
                style="table-layout: fixed;"
            >
                <thead>
                    <tr>
                        <th
                            class="item-title"
                            :colspan="tableColspan"
                            style="padding-bottom: 14px;"
                        >
                            <template v-if="$abcSocialSecurity && !$abcSocialSecurity.config.isHeilongjiangHarbin">
                                {{ (curMedicalConfig && curMedicalConfig.title ) || organ.name }}医药费收据及收费项目清单
                            </template>
                            <template v-else>
                                黑龙江省省本级医疗保障基金结算清单
                            </template>
                        </th>
                    </tr>

                    <tr class="item-head">
                        <th :colspan="tableColspan">
                            <div class="medical-fee-list-normal-header-item">
                                姓名：{{ patient.name }} {{ patient.sex }} {{ formatAge(patient.age) }}
                            </div>
                            <div class="medical-fee-list-normal-header-item">
                                医生代码：{{ printData.doctorName }}
                                <template v-if="printData.nationalDoctorCode">
                                    ({{ printData.nationalDoctorCode }})
                                </template>
                            </div>
                            <div class="medical-fee-list-normal-header-item">
                                机构代码：{{ printData.hospitalCode }}
                            </div>
                        </th>
                    </tr>
                    <tr
                        v-if="!isHospital"
                        class="item-head"
                    >
                        <th :colspan="tableColspan">
                            <div class="medical-fee-list-normal-header-item">
                                科别：{{ printData.departmentName }}
                                <template v-if="printData.departmentCaty">
                                    ({{ printData.departmentCaty }})
                                </template>
                            </div>
                            <div class="medical-fee-list-normal-header-item">
                                门诊流水号：{{ printData.serialNo }}
                            </div>
                            <div
                                class="medical-fee-list-normal-header-item"
                                style=" white-space: nowrap;"
                            >
                                处方单据号：{{ printData.patientOrderNo | formatPatientOrder }}
                            </div>
                        </th>
                    </tr>
                    <tr class="item-head">
                        <th :colspan="tableColspan">
                            <div class="medical-fee-list-normal-header-item">
                                诊断：
                                <template v-if="diagnosisInfos">
                                    {{ diagnosisInfos }}
                                </template>
                            </div>
                            <div class="medical-fee-list-normal-header-item">
                                <span v-if="isHospital">人员编号：{{ shebaoPayment.cardNo }}</span>
                                <span v-else>人员编号：{{ extraInfo.psnNo }}</span>
                            </div>
                            <div class="medical-fee-list-normal-header-item">
                                门诊类别：{{ outpatientCategory }}
                            </div>
                        </th>
                    </tr>
                    <tr
                        v-if="curMedicalConfig.idCardShowType !== 2 || curMedicalConfig.patientAddress"
                        class="item-head"
                    >
                        <th :colspan="tableColspan">
                            <div
                                v-if="curMedicalConfig.idCardShowType !== 2"
                                class="medical-fee-list-normal-header-item"
                            >
                                {{ getIdCardTypeStr(patient.idCardType) }}：{{ filterIdCard(patient.idCard, curMedicalConfig.idCardShowType) }}
                            </div>
                            <div
                                v-if="curMedicalConfig.patientAddress"
                                class="medical-fee-list-normal-header-item address-th"
                                :style="{ width: curMedicalConfig.idCardNum ? '64%' : '100%' }"
                                overflow
                            >
                                地址: {{ formatAddress(patient.address) }}
                            </div>
                        </th>
                    </tr>
                    <tr class="border-line">
                        <th
                            :colspan="nameColspan"
                        >
                            项目名称
                        </th>
                        <th
                            colspan="2"
                            style="padding: 4pt 0;"
                        >
                            医保等级
                        </th>
                        <th
                            :colspan="socialColspan"
                        >
                            国家代码<template v-if="showChargeItemTraceCode">
                                / 追溯码
                            </template>
                        </th>
                        <th
                            colspan="1"
                            class="text-right"
                        >
                            单位
                        </th>
                        <th
                            :colspan="2"
                            class="text-right"
                        >
                            单价
                        </th>
                        <th
                            :colspan="countColspan"
                            class="text-center"
                        >
                            数量
                        </th>
                        <th
                            :colspan="2"
                            class="text-right"
                        >
                            金额
                        </th>
                        <th
                            v-if="curMedicalConfig.selfPaymentFee"
                            colspan="2"
                            class="text-center"
                        >
                            自付比例
                        </th>
                        <th
                            v-if="curMedicalConfig.noAffordMoney"
                            colspan="3"
                            class="text-right"
                        >
                            不可报销金额
                        </th>
                        <th
                            v-if="curMedicalConfig.canAffordRange"
                            colspan="2"
                            class="text-right"
                        >
                            可报销范围
                        </th>
                    </tr>
                </thead>
                <tbody data-type="group">
                    <template v-for="(item, index) in productItems">
                        <tr
                            v-if="item.chargeItem && item.chargeItem.isTraceCode"
                            :key="`medical-fee-list-normal-item-trace-code-${index}`"
                            data-type="item"
                        >
                            <td :colspan="nameColspan"></td>
                            <td colspan="2"></td>
                            <td :colspan="tableColspan - nameColspan - 2">
                                {{ item.chargeItem.traceCode }}
                            </td>
                        </tr>
                        
                        <tr
                            v-else-if="item.chargeItem"
                            :key="`medical-fee-list-normal-item-${index}`"
                            data-type="item"
                        >
                            <td
                                :colspan="nameColspan"
                                :class="{ 'is-child': item.chargeItem && item.chargeItem.composeType >= 2 && composeChildrenConfig === 2 }"
                            >
                                <span
                                    v-if="item.chargeItem && item.chargeItem.toothNos && item.chargeItem.toothNos.length"
                                    v-html="formatToothNos2Html(item.chargeItem.toothNos)"
                                ></span>
                                <template v-if="item.chargeItem && item.chargeItem.sourceItemType === 0">
                                    {{ item.chargeItem && item.chargeItem.name }}
                                </template>
                            </td>
                            <td
                                colspan="2"
                            >
                                <template v-if="item.chargeItem && item.chargeItem.sourceItemType === 0">
                                    <template v-if=" (item.chargeItem.medicalFeeGrade) && isDisplaySocialCode(item.chargeItem, isFeeCompose, productInfoRuleConfig)">
                                        {{ medicalFeeGrade2PrintStr(item.chargeItem.medicalFeeGrade) }}
                                    </template>
                                </template>
                            </td>
                            <td
                                :colspan="socialColspan"
                                style="word-break: break-all;"
                            >
                                <template v-if="item.chargeItem && item.chargeItem.sourceItemType === 0">
                                    <template v-if="isDisplaySocialCode(item.chargeItem, isFeeCompose, productInfoRuleConfig)">
                                        {{ item.chargeItem && item.chargeItem.socialCode ? item.chargeItem.socialCode : '-' }}
                                    </template>
                                    <template v-else>
                                        -
                                    </template>
                                </template>
                                <template v-else>
                                    -
                                </template>
                            </td>
                            <td
                                colspan="1"
                                class="text-right"
                                style="padding: 0;"
                            >
                                <template v-if="item.chargeItem && item.chargeItem.sourceItemType === 0">
                                    {{ item.chargeItem && (item.chargeItem.socialUnit || '次') }}
                                </template>
                            </td>
                            <td
                                :colspan="2"
                                class="text-right"
                            >
                                <template v-if="item.chargeItem && item.chargeItem.sourceItemType === 0">
                                    <template
                                        v-if="item.chargeItem && item.chargeItem.discountedPrice && item.chargeItem && item.chargeItem.count"
                                    >
                                        {{ item.chargeItem.discountedUnitPrice | formatMoney(false) }}
                                    </template>
                                </template>
                            </td>
                            <td
                                :colspan="countColspan"
                                class="text-center"
                            >
                                <template v-if="item.chargeItem && item.chargeItem.sourceItemType === 0">
                                    {{ item.chargeItem && item.chargeItem.count }}
                                </template>
                            </td>
                            <td
                                :colspan="2"
                                class="text-right"
                            >
                                <template v-if="item.chargeItem && item.chargeItem.sourceItemType === 0">
                                    <template v-if="item.chargeItem">
                                        {{ item.chargeItem.discountedPrice | formatMoney }}
                                    </template>
                                </template>
                            </td>
                            <td
                                v-if="curMedicalConfig.selfPaymentFee"
                                colspan="2"
                                class="text-center"
                            >
                                <template v-if="item.chargeItem">
                                    {{
                                        filterOwnExpenseRatio(item.chargeItem.ownExpenseRatio)
                                    }}
                                </template>
                            </td>
                            <td
                                v-if="curMedicalConfig.noAffordMoney"
                                colspan="3"
                                class="text-right"
                            >
                                <template v-if="item.chargeItem">
                                    {{ item.chargeItem.overlmtAmt | formatMoney }}
                                </template>
                            </td>
                            <td
                                v-if="curMedicalConfig.canAffordRange"
                                colspan="2"
                                class="text-right"
                            >
                                <template v-if="item.chargeItem">
                                    {{ item.chargeItem.inscpScpAmt | formatMoney }}
                                </template>
                            </td>
                        </tr>
                    </template>
                </tbody>

                <tfoot>
                    <tr>
                        <td :colspan="tableColspan">
                            <div class="tfoot-item">
                                <div
                                    v-for="(item, idx) in chargeInfo"
                                    :key="`medical-fee-list-footer-charge-info-${idx}`"
                                >
                                    <span>{{ item.title }}：{{ item.amount }}</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td :colspan="tableColspan">
                            <div class="tfoot-item">
                                <div
                                    v-for="(item, idx) in chargePersonInfo"
                                    :key="`medical-fee-list-footer-charge-person-info-${idx}`"
                                    :style="item.customStyle ? item.customStyle : {}"
                                >
                                    <span>{{ item.title }}：{{ item.amount }}</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import {
        filterOwnExpenseRatio,
        medicalFeeGrade2PrintStr,
        formatAge,
        formatAddress,
        isDisplaySocialCode,
        parseTime, getIdCardTypeStr, deepClone,
    } from "./common/utils.js";
    import NationalBillData from "./mixins/national-bill-data.js";
    import { formatToothNos2Html, filterIdCard } from "./common/medical-transformat";
    import { resetChargeItemComposeSocialFee } from "./mixins/reset-compose-social-fee";
    import { InsutypeCodeLabelEnum } from './common/constants';

    export default {
        name: "MedicalFeeListNormal",
        mixins: [BillDataMixins, NationalBillData],
        props: {
            extra: {
                type: Object,
                default() {
                    return {}
                },
            },
        },
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_FEE_LIST_NORMAL,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6_LANSCAPE,
                isRecommend: false,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分', // 默认选择的等分纸
            },
        ],
        computed: {
            medicalListConfig() {
                return this.renderData.config.medicalListConfig || {}
            },
            // 是否有社保卡支付
            hasHealthCardPay() {
                return (
                    this.printData.healthCardBalance !== null &&
                    this.printData.healthCardBalance !== '' &&
                    this.printData.healthCardBalance !== undefined
                );
            },
            organ() {
                return this.printData.organ || {};
            },
            diagnosisInfos() {
                if (this.printData.diagnosisInfos && this.printData.diagnosisInfos.length) {
                    const res = [];
                    this.printData.diagnosisInfos.forEach(item => {
                        res.push(`${item.name}${item.code ? '(' + item.code + ')' : ''}`);
                    })
                    return res.join('，')
                }
                return null;
            },
            doctorName() {
                return `${this.printData.doctorName || ''}`;
            },
            departmentName() {
                return this.printData.departmentName;
            },
            patient() {
                return this.printData.patient || {};
            },
            institutionName() {
                return this.curMedicalConfig.institutionName || ''
            },
            curMedicalConfig() {
                return this.medicalListConfig.normal || {};
            },
            // 医疗清单 0 只展示套餐  2 只展示子项  1 展示套餐加子项 转换成同 医疗票据 0 只展示套餐1 只展示子项  2 展示套餐名加子项
            composeChildrenConfig() {
                switch (this.curMedicalConfig.composeChildren || 0) {
                    case 1:
                        return 2;
                    case 2:
                        return 1;
                    default:
                        return 0;
                }
            },
            // 项目信息规则配置
            productInfoRuleConfig() {
                return this.curMedicalConfig.productInfoRule || 0;
            },
            traceCodeConfig() {
                return this.curMedicalConfig.traceCode ?? 0;
            },
            showTraceCodeQrCode() {
                return this.traceCodeConfig === 2;
            },
            showChargeItemTraceCode() {
                return this.traceCodeConfig === 1;
            },
            //是否为住院发票
            isHospital() {
                return this.printData.feeListType === 'hospital'
            },
            // 表格宽度
            tableColspan() {
                let colspan = 20
                if (this.curMedicalConfig.selfPaymentFee) {
                    colspan += 2
                }
                if (this.curMedicalConfig.noAffordMoney) {
                    colspan += 3
                }
                if (this.curMedicalConfig.canAffordRange) {
                    colspan += 2
                }
                if (this.curMedicalConfig.selfPaymentFee && this.curMedicalConfig.noAffordMoney && this.curMedicalConfig.canAffordRange) {
                    colspan -=2
                }
                return colspan
            },
            // 表格头部宽度
            tableHeadColspan() {
                let tableHeadColspan = {
                    colspan1: 6,
                    colspan2: 9,
                    colspan3: 5,
                }
                if (this.curMedicalConfig.selfPaymentFee) {
                    tableHeadColspan.colspan2++
                    tableHeadColspan.colspan3++
                }
                if (this.curMedicalConfig.noAffordMoney) {
                    tableHeadColspan.colspan1++
                    tableHeadColspan.colspan2++
                    tableHeadColspan.colspan3++
                }
                if (this.curMedicalConfig.canAffordRange) {
                    tableHeadColspan.colspan2++
                    tableHeadColspan.colspan3++
                }
                if (this.curMedicalConfig.selfPaymentFee && this.curMedicalConfig.noAffordMoney && this.curMedicalConfig.canAffordRange) {
                    tableHeadColspan.colspan2--
                    tableHeadColspan.colspan3--
                }
                return tableHeadColspan
            },
            // 项目名称宽度
            nameColspan() {
                let nameColspan = 4
                if (this.curMedicalConfig.selfPaymentFee && this.curMedicalConfig.noAffordMoney && this.curMedicalConfig.canAffordRange) {
                    nameColspan--
                }
                return nameColspan
            },
            socialColspan() {
                let nameColspan = 6;
                let count = 0;
                if (this.curMedicalConfig.selfPaymentFee) {
                    count++;
                }
                if (this.curMedicalConfig.noAffordMoney) {
                    count += 2;
                }
                if (this.curMedicalConfig.canAffordRange) {
                    count++;
                }
                if (count > 1) {
                    nameColspan++;
                }
                return nameColspan;
            },
            // 数量宽度
            countColspan() {
                let countColspan = 2
                if (this.curMedicalConfig.selfPaymentFee && this.curMedicalConfig.noAffordMoney && this.curMedicalConfig.canAffordRange) {
                    countColspan--
                }
                return countColspan
            },
            chargeInfo() {
                if (this.isHospital) {
                    return [
                        {
                            title: '费用合计',
                            amount: this.printData.totalPrice,
                        },
                        {
                            title: '统筹基金支付',
                            amount: this.printData.shebaoPayment && this.printData.shebaoPayment.fundAmount,
                        },
                        {
                            title: '离休统筹金',
                            amount: this.printData.shebaoPayment && this.printData.shebaoPayment.otherPaymentFee,
                        },
                        {
                            title: '个人账户支付',
                            amount: this.printData.shebaoPayment && this.printData.shebaoPayment.acctPay,
                        },
                        {
                            title: '个人现金支付',
                            amount: this.printData.personalPaymentFee,
                        },
                    ]
                }

                let res = [
                    {
                        title: '费用合计',
                        amount: this.printData.netIncomeFee,
                    },
                    {
                        title: '基金支付',
                        amount: this.shebaoPayment.fundPaymentFee,
                    },
                    {
                        title: '个人账户支付',
                        amount: this.shebaoPayment.accountPaymentFee,
                    },
                    {
                        title: '支付前账户余额',
                        amount: this.shebaoPayment.beforeCardBalance,
                    },
                    {
                        title: '起付线',
                        amount: this.extraInfo.actPayDedc,
                    },
                    {
                        title: '统筹基金支付',
                        amount: this.extraInfo.hifpPay,
                    },
                    {
                        title: '统筹支付比例',
                        amount: this.extraInfo.poolPropSelfpay ? this.extraInfo.poolPropSelfpay : '0',
                    },
                    {
                        title: '支付后账户余额',
                        amount: this.shebaoPayment.cardBalance,
                        show: this.isShowCardBalance,
                    },
                    {
                        title: '起付线累计',
                        amount: this.extraInfo.beforeDedcCumulative,
                        show: !!this.$abcSocialSecurity?.config?.isNeimenggu,
                    },
                    {
                        title: '医保区划',
                        amount: this.$abcSocialSecurity?.$national && this.$abcSocialSecurity.$national.tools.getCityAreaCodeWording(this.extraInfo.insuplcAdmdvs),
                        show: !this.isShowGeneralFundpayBalc,
                    },
                    {
                        title: '公务员补助',
                        amount: this.extraInfo.cvlservPay,
                    },
                    {
                        title: '个人现金支付',  
                        amount: this.printData.personalPaymentFee,
                    },
                    {
                        title: '共济账户支付',
                        amount: this.extraInfo.acctMulaidPay,
                    },
                    {
                        title: '医保钱包支付',
                        amount: this.extraInfo.wltpayAmt,
                        show: !!this.$abcSocialSecurity?.config?.isShowWltAcctFlagSwitch,
                    },
                    {
                        title: '公补支付',
                        amount: null,
                        show: !!this.$abcSocialSecurity?.config?.isHeilongjiangHarbin,
                    },
                    {
                        title: '大病救助支付',
                        amount: null,
                        show: !!this.$abcSocialSecurity?.config?.isHeilongjiangHarbin,
                    },
                    {
                        title: '统筹余额',
                        amount: this.extraInfo.generalFundpayBalc,
                        show: this.isShowGeneralFundpayBalc,
                    },
                    {
                        title: "大病余额",
                        amount: this.extraInfo.chronicDiseaseDBalc,
                        show: !!this.extraInfo.chronicDiseaseDBalc,
                    },
                    {
                        title: "慢病余额",
                        amount: this.extraInfo.chronicDiseaseMBalc,
                        show: !!this.extraInfo.chronicDiseaseMBalc,
                    },
                    {
                        title: '医保区划',
                        amount: this.$abcSocialSecurity?.$national && this.$abcSocialSecurity.$national.tools.getCityAreaCodeWording(this.extraInfo.insuplcAdmdvs),
                        show: this.isShowGeneralFundpayBalc,
                    },
                    {
                        title: '累计统筹支付金额',
                        amount: this.extraInfo.ljtczf,
                        show: this.isShowCumInfo,
                    },
                    {
                        title: '累计大额支付金额',
                        amount: this.extraInfo.ljdezf,
                        show: this.isShowCumInfo,
                    },
                    {
                        title: '累计个人负担金额',
                        amount: this.extraInfo.ljgrzf,
                        show: this.isShowCumInfo,
                    },
                    {
                        title: '累计起付线',
                        amount: this.extraInfo.ljqfx,
                        show: this.isShowCumInfo,
                    },
                    {
                        title: '门诊统筹额度累计',
                        amount: this.extraInfo.bnptmzljbxje,
                        show: this.isShowCumInfo,
                    },
                ].filter(item => item.show !== false)
                const nanjingRes = [
                    {
                        title: '费用合计',
                        amount: this.printData.netIncomeFee,
                    },
                    {
                        title: '医保范围内费用',
                        amount: this.extraInfo.inscpScpAmt,
                    },
                    {
                        title: '统筹基金支付',
                        amount: this.shebaoPayment.fundPaymentFee,
                    },
                    {
                        title: '医疗救助',
                        amount: this.extraInfo.mafPay,
                    },
                    {
                        title: '大病保险',
                        amount: this.extraInfo.seriousDiseaseFundPaymentFee,
                    },
                    {
                        title: '个人账户支付',
                        amount: this.shebaoPayment.accountPaymentFee,
                    },
                    {
                        title: '个人现金支付',
                        amount: this.printData.personalPaymentFee,
                    },
                    {
                        title: '个人自付',
                        amount: this.extraInfo.psnSelfpay,
                    },
                    {
                        title: '个人自费',
                        amount: this.extraInfo.fulamtOwnpayAmt,
                    },
                    {
                        title: '账户余额',
                        amount: this.shebaoPayment.cardBalance,
                    },
                ];
                return this.$abcSocialSecurity && this.$abcSocialSecurity.config.isJiangsuNanjing ? nanjingRes : res;
            },
            // 收费人员等信息
            chargePersonInfo() {
                return [
                    {
                        title: "收费员",
                        amount: this.printData.chargedByName,
                        visible: this.curMedicalConfig.chargedByName,
                    },
                    {
                        title: "收费时间",
                        amount: parseTime(
                            this.printData.chargedTime,
                            'y-m-d h:i:s',
                            true,
                        ),
                        visible: this.curMedicalConfig.chargedTime,
                    },
                    {
                        title: "收费单位",
                        amount: this.institutionName.slice(0, 20),
                        visible: this.curMedicalConfig.institution,
                        customStyle: 'width: 50%',
                    },
                    {
                        title: '患者签字',
                        amount: '',
                        visible: this.curMedicalConfig.patientSignature,
                    },
                ].filter((x) => !!x.visible);
            },
            // 是否展示支付后账户余额
            isShowCardBalance() {
                // 默认都展示
                let isShowCardBalance = true
                if (!this.curMedicalConfig.isShowCardBalance && this.extraInfo.insuplcAdmdvs?.includes('3799')) {
                    // 济南门店配置了不展示且患者是省医保时不展示
                    isShowCardBalance = false
                }
                return isShowCardBalance
            },
            // 是否展示统筹余额
            isShowGeneralFundpayBalc() {
                return !!this.$abcSocialSecurity?.config?.isNeedQueryFundQuota
            },
            // 是否展示累计支付信息
            isShowCumInfo() {
                return this.curMedicalConfig.isShowCumInfo == 1
            },
            chargeForms() {
                return this.printData.chargeForms || [];
            },
            productItems() {
                const chargeItemsLength = this.renderChargeFormItems.length;
                const chargeInfoLength = this.chargeInfo.length;
                const res = [];
                if (chargeItemsLength >= chargeInfoLength) {
                    this.chargeInfo.forEach((item, index) => {
                        const tempItem = Object.assign({}, item, {
                            chargeItem: this.renderChargeFormItems[index],
                        });
                        res.push(tempItem);
                    });

                    for (let i = chargeInfoLength; i < chargeItemsLength; i++) {
                        const tempItem = Object.assign(
                            {},
                            {
                                title: null,
                                amount: null,
                                chargeItem: this.renderChargeFormItems[i],
                            },
                        );
                        res.push(tempItem);
                    }
                } else {
                    this.chargeInfo.forEach((item, index) => {
                        let tempItem = null;
                        if (index < chargeItemsLength) {
                            tempItem = Object.assign({}, item, {
                                chargeItem: this.renderChargeFormItems[index],
                            });
                        } else {
                            tempItem = Object.assign({}, item);
                            tempItem.chargeItem = null;
                        }

                        res.push(tempItem);
                    });
                }
                return res;
            },
            isFeeCompose() {
                return !!this.printData.IS_FEE_COMPOSE;
            },
            renderChargeFormItems() {
                let cacheChargeFormItems = deepClone(this.chargeFormItems);
                if (this.curMedicalConfig.isPrintNonZeroItem) {
                    cacheChargeFormItems = cacheChargeFormItems.filter((item) => {
                        return !!item.discountedPrice;
                    })
                }
                if (this.curMedicalConfig.registerDisplayName) {
                    cacheChargeFormItems.forEach((item) => {
                        if (item.name === '诊费') {
                            item.name = '挂号费';
                        }
                    })
                }
                let cacheChargeFormItemsWithTraceCode = cacheChargeFormItems;
                if (this.showChargeItemTraceCode) {
                    cacheChargeFormItemsWithTraceCode = [];
                    cacheChargeFormItems.forEach((item) => {
                        cacheChargeFormItemsWithTraceCode.push(item);
                        if (Array.isArray(item.traceableCodeList) && item.traceableCodeList.length) {
                            cacheChargeFormItemsWithTraceCode.push({
                                traceCode: item.traceableCodeList.map((it) => it.no).join('、'),
                                isTraceCode: true,
                            })
                        }
                    })
                }
                return resetChargeItemComposeSocialFee(cacheChargeFormItemsWithTraceCode);
            },
            outpatientCategory() {
                let str = '';
                if (this.extraInfo.insureType) {
                    str += this.extraInfo.insureType;
                }
                if (this.shebaoPayment.medType) {
                    str += `(${this.shebaoPayment.medType})`;
                }
                if (this.extraInfo.insutypeCode) {
                    str += ` ${InsutypeCodeLabelEnum[this.extraInfo.insutypeCode] || ''}`;
                }
                if (!this.$abcSocialSecurity?.$national?.config?.isJiangsuXuzhou && this.curMedicalConfig.diseaseName && this.extraInfo.diseaseName) {
                    // 江苏徐州不打印病种
                    str += ` ${this.extraInfo.diseaseName}`;
                }
                return str;
            },
        },
        methods: {
            getIdCardTypeStr,
            formatToothNos2Html,
            filterIdCard,
            formatAddress,
            medicalFeeGrade2PrintStr,
            filterOwnExpenseRatio,
            formatAge,
            isDisplaySocialCode,
        },
    }
</script>
<style lang="scss">
@import "./style/medical-fee-list/national/index";

.abc-page_preview {
    color: #000000;
    background: #ffffff;
}

// 由于样式被其他 css 覆盖，所以这里强制复原
.print-medical-fee-list-wrapper {
    .print-medical-fee-list-trace-code-img {
        margin: unset !important;
        position: unset !important;
        top: unset !important;
        bottom: unset !important;
        left: unset !important;
        right: unset !important;
        overflow: unset !important;
    }
}

.address-th {
    overflow: hidden;
    word-break: keep-all;
    white-space: nowrap;
}

.medical-fee-list-normal-header-item {
    display: inline-block;
    width: 32%;
    vertical-align: top;
}
</style>
