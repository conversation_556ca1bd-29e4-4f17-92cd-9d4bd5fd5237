<template>
    <div>
        <hospitalization-certificate-header
            :clinic-info="clinicInfo"
            :header-config="headerConfig"
        >
        </hospitalization-certificate-header>
        <hospitalization-certificate-content
            :print-data="printData"
            :content-config="contentConfig"
        >
        </hospitalization-certificate-content>
        <hospitalization-certificate-footer
            :print-data="printData"
            :footer-config="footerConfig"
        ></hospitalization-certificate-footer>
    </div>
</template>

<script>
    import HospitalizationCertificateHeader from "./components/hospitalization-certificate/header.vue";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import CommonHandler from "./data-handler/common-handler";
    import HospitalizationCertificateContent from "./components/hospitalization-certificate/content.vue";
    import HospitalizationCertificateFooter from "./components/hospitalization-certificate/footer.vue";

    export  default {
        name:'HospitalizationCertificate',
        DataHandler: <PERSON>Handler,
        businessKey: PrintBusinessKeyEnum.HOSPITALIZATION_CERTIFICATE,
        pages: [
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
        ],
        components: {
            HospitalizationCertificateFooter,
            HospitalizationCertificateContent,
            HospitalizationCertificateHeader},
        props:{
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        computed:{
            printData() {
                return this.renderData.printData || {};
            },
            config() {
                if (this.renderData.config && this.renderData.config.hospitalMedicalDocuments && this.renderData.config.hospitalMedicalDocuments.hospitalizationCertificate) {
                    return this.renderData.config.hospitalMedicalDocuments.hospitalizationCertificate;
                }
                return {};
            },
            headerConfig() {
                return this.config.header || {};
            },
            contentConfig(){
                return this.config.content ||  {};
            },
            footerConfig(){
                return this.config.footer || {};
            },
            clinicInfo() {
                return this.printData.clinicInfo || {};
            },
        }
    }
</script>