<!--exampleData
{}
-->

<template>
    <div></div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";

    export default {
        name: "HospitalMedicalRecord",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.HOSPITAL_MEDICAL_RECORD,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
    }
</script>
<style lang="scss">

</style>
