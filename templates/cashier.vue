<!--exampleData
{
  "id": "ffffffff0000000034793835e4250000",
  "patient": {
    "id": "ffffffff0000000027d094280f91a000",
    "chainId": "ffffffff000000000c5a1308069aa000",
    "name": "唐启涛",
    "namePy": "tangqitao",
    "namePyFirst": "TQT",
    "mobile": "18*********",
    "sex": "男",
    "birthday": "1992-08-12",
    "idCard": "510781199408121154",
    "isMember": 1,
    "age": {
      "year": 30,
      "month": 7,
      "day": 10
    },
    "address": {
      "addressCityId": "",
      "addressCityName": "",
      "addressDetail": "",
      "addressDistrictId": "",
      "addressDistrictName": "",
      "addressGeo": null,
      "addressProvinceId": "",
      "addressProvinceName": ""
    },
    "sn": "000823",
    "remark": "",
    "profession": "",
    "company": "",
    "memberInfo": null,
    "patientSource": null,
    "tags": [],
    "marital": null,
    "weight": null,
    "ethnicity": "",
    "wxBindStatus": 0,
    "isAttention": 0,
    "externalCodeId": "",
    "externalCodeRemark": null,
    "shebaoCardInfo": null,
    "chronicArchivesInfo": null,
    "childCareInfo": null,
    "patientPoints": null
  },
  "organ": {
    "id": "ffffffff0000000019ba2aa81002a000",
    "name": "四川-成都",
    "shortName": "四川-成都",
    "addressDetail": "",
    "contactPhone": null,
    "logo": null,
    "category": "",
    "hisType": 0,
    "medicalDocumentsTitle": null
  },
  "chargeForms": [
    {
      "id": "ffffffff0000000034793835e4250001",
      "chargeFormItems": [
        {
          "id": "ffffffff0000000034793835e4250002",
          "name": "阿莫西林胶囊(阿莫仙/联邦/联邦制药)",
          "unit": "盒",
          "count": 1,
          "unitCount": 1,
          "doseCount": 1,
          "totalPrice": 500,
          "discountedPrice": 500,
          "discountedUnitPrice": 500,
          "unitPrice": 500,
          "composeType": 0,
          "composeChildren": null,
          "position": null,
          "displaySpec": "0.5mg*24粒/盒",
          "sourceItemType": 0,
          "socialCode": "ZA10DAJ0112010104123",
          "hisCode": "001516",
          "socialUnit": "盒",
          "socialName": "阿莫西林胶囊(阿莫仙/联邦/联邦制药)",
          "medicalFeeGrade": 3,
          "ownExpenseRatio": 1,
          "ownExpenseFee": 500,
          "inscpScpAmt": 0,
          "overlmtAmt": 0,
          "productType": 1,
          "productSubType": 1
        }
      ],
      "sourceFormType": 4,
      "printFormType": 4,
      "totalPrice": 500,
      "processUsageInfo": null
    }
  ],
  "chargeTransactions": [
    {
      "payMode": 5,
      "paySubMode": 0,
      "payModeName": "医保卡",
      "paySubModeName": null,
      "payModeDisplayName": "医保卡",
      "amount": 500,
      "thirdPartyPayCardId": ""
    }
  ],
  "totalFee": 500,
  "discountFee": 0,
  "receivableFee": 500,
  "netIncomeFee": 500,
  "refundFee": null,
  "diagnosedDate": null,
  "diagnosis": "奥尼昂-尼昂热",
  "diagnosisInfos": [
    {
      "code": "A92.100",
      "name": "奥尼昂-尼昂热",
      "diseaseType": null
    }
  ],
  "extendDiagnosisInfos": [
    {
      "toothNos": null,
      "value": [
        {
          "code": "A92.100",
          "name": "奥尼昂-尼昂热",
          "diseaseType": null
        }
      ]
    }
  ],
  "healthCardBeginningBalance": 500,
  "healthCardBalance": 0,
  "healthCardOwner": "唐启涛",
  "healthCardOwnerRelationToPatient": "本人",
  "healthCardId": null,
  "healthCardAccountPaymentFee": 462.57,
  "healthCardFundPaymentFee": 0,
  "healthCardOtherPaymentFee": 37.43,
  "healthCardCardOwnerType": null,
  "healthCardSelfConceitFee": 0,
  "healthCardSelfPayFee": 0,
  "personalPaymentFee": 0,
  "chargedByName": "祁烁",
  "chargedTime": "2023-03-07T10:27:07Z",
  "sellerName": "",
  "doctorName": "唐启涛",
  "nationalDoctorCode": "D510107067517",
  "patientOrderNo": "",
  "departmentName": null,
  "departmentCaty": null,
  "hospitalCode": "H51019903663",
  "doctorWorkNo": null,
  "refundByName": null,
  "refundTime": null,
  "latestOwePaidTime": null,
  "shebaoPayment": {
    "cardId": "",
    "cardOwner": "唐启涛",
    "cardOwnerType": "在职",
    "idCardNum": "510781199408121154",
    "beforeCardBalance": 462.57,
    "cardBalance": 0,
    "relationToPatient": "本人",
    "receivedFee": 462.57,
    "accountPaymentFee": 462.57,
    "fundPaymentFee": 0,
    "otherPaymentFee": 0,
    "medType": "普通门诊",
    "selfPaymentFee": null,
    "selfHandledPaymentFee": null,
    "chargeNumber": "**************",
    "extraInfo": {
      "insutype": "职工基本医疗保险",
      "cvlservPay": 0,
      "hifesPay": 0,
      "hifmiPay": 0,
      "hifobPay": 0,
      "mafPay": 0,
      "ljtczf": 22.22,
      "ljdezf": 22.22,
      "ljgrzf": 22.22,
      "ljqfx": 22.22,
      "bnptmzljbxje": 22.22,
      "psnCashPay": 37.43,
      "hifpPay": 0,
      "actPayDedc": 0,
      "othPay": 0,
      "fulamtOwnpayAmt": 500,
      "overlmtSelfpay": 0,
      "preselfpayAmt": 0,
      "inscpScpAmt": 0,
      "acctMulaidPay": 37.43,
      "hospPartAmt": 0,
      "psnNo": "**********",
      "iptOtpNo": "abc-34793849df428000",
      "medfeeSumamt": null,
      "psnPartAmt": 500,
      "selfPaymentFeeYi": 0,
      "selfPaymentFeeBing": 500,
      "gongjiAccountPaymentFee": 37.43,
      "gongjiFundPaymentFee": 0,
      "gongjiOtherPaymentFee": 0,
      "gongjiPsnCashPay": 0,
      "gongjiBalc": 4873.28,
      "gongjiAuthorName": "唐威",
      "gongjiRelation": "父母"
    },
    "region": "sichuan_chengdu",
    "personalPaymentFee": 0
  },
  "subTotals": {
    "registrationFee": 0,
    "westernMedicineFee": 500,
    "chineseMedicineFee": 0,
    "examinationFee": 0,
    "treatmentFee": 0,
    "materialFee": 0,
    "onlineConsultationFee": 0,
    "expressDeliveryFee": 0,
    "decoctionFee": 0,
    "otherFee": 0,
    "composeProductFee": 0
  },
  "medicalBills": [
    {
      "name": "西药费",
      "totalFee": 500,
      "totalCount": 1,
      "unit": "项",
      "printType": 1
    }
  ],
  "giftCoupons": [],
  "promotionBalances": [],
  "healthCardPaymentFee": 500,
  "serialNo": "3781115166266294272"
};
-->

<template>
    <cashier-template
        :print-data="printData"
        :config="config"
        :extra="extra"
        :view-distribute-print-config="viewDistributePrintConfig"
        :is-optimization="isOptimization"
    ></cashier-template>
</template>

<script>
    import CashierTemplate from './components/cashier/index.vue'

    import PrintCommonDataHandler from "./data-handler/common-handler.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";

    export default {
        DataHandler: PrintCommonDataHandler,
        components: {
            CashierTemplate,
        },
        businessKey: PrintBusinessKeyEnum.CASHIER,
        pages: [
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM58,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM70_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80_100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_140,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_93_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM148_118_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM200_1397,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM175_94,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_200,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM90_120_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM241_279,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分', // 默认选择的等分纸
            },
        ],

        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                },
            },
            extra: {
                type: Object,
                default() {
                    return {}
                },
            },
            options: {
                type: Object,
                default() {
                    return {}
                },
            },
            viewDistributePrintConfig: {
                type: Object,
                default() {
                    return {}
                },
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            config() {
                if(this.renderData.config && this.renderData.config.cashier) {
                    return this.renderData.config.cashier;
                }
                return {};
            },
            // 58mm 小票需要优化排版
            isOptimization() {
                return this.options.page?.size === '热敏小票（58mm）';
            },
        },
    };
</script>
