<template>
    <div class="pe-charge-fee-list-wrapper">
        <div data-type="header">
            <div class="pe-charge-fee-list__title">
                {{ clinicName }}体检收费清单
            </div>

            <div :class="headerClass">
                <div
                    v-for="(o, key) in headers"
                    :key="key"
                    class="list__header-item"
                >
                    <span class="header-item__label">{{ o.label }}：</span>
                    <span class="header-item__value">{{ o.value }}</span>
                </div>
            </div>
        </div>

        <div
            class="pe-charge-fee-list__content"
            data-type="block"
        >
            <div class="pe-charge-fee-list__content-header">
                <div
                    v-for="(o, key) in contentHeader"
                    :key="key"
                    :style="o.style"
                    class="content-header-item"
                >
                    {{ o.label }}
                </div>
            </div>

            <div
                v-for="(o, key) in list"
                :key="key"
                class="pe-charge-fee-list__content-section"
            >
                <div
                    v-for="(m, n) in contentHeader"
                    :key="n"
                    :style="m.style"
                    class="content-section-item"
                >
                    {{ o[m.key] }}
                </div>
            </div>
        </div>


        <div
            class="pe-charge-fee-list__footer"
            data-type="footer"
        >
            <div class="pe-charge-fee-list__footer-item">
                <div class="footer-item__label">
                    收费员：
                </div>
                <div class="footer-item__value">
                    {{ chargeEmployeeName }}
                </div>
            </div>

            <div class="pe-charge-fee-list__footer-item">
                <div class="footer-item__label">
                    收费时间：
                </div>
                <div class="footer-item__value">
                    {{ chargeTime }}
                </div>
            </div>

            <div class="pe-charge-fee-list__footer-item">
                <div class="footer-item__label">
                    收费单位：
                </div>
                <div class="footer-item__value">
                    {{ clinicName }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import PrintHandler from "./data-handler/print-handler.js";

    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";

    export default {
        name: "PEChargeFeeList",
        DataHandler: PrintHandler,
        businessKey: PrintBusinessKeyEnum.PE_CHARGE_FEE_LIST,
        pages: [
            {
                paper: PageSizeMap.B5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: Object,
        },
        computed: {
            printData() {
                console.log(this.renderData.printData, 'printData')
                return this.renderData.printData;
            },

            clinicName() {
                return this.printData.clinicName
            },

            chargeEmployeeName() {
                return this.printData.chargeEmployeeName
            },

            chargeTime() {
                return this.printData.chargeTime
            },

            headers() {
                return this.printData.headers
            },

            list() {
                return this.printData.list
            },

            contentHeader() {
                if (this.printData.type === 'personal') {
                    return [
                        { label: '项目名称', key: 'name', style: { flex: 1 } },
                        { label: '类型', key: 'type', style: { width: '94px' } },
                        { label: '金额', key: 'price', style: { width: '72px', textAlign: 'right' } },
                    ]
                }

                return [
                    { label: '项目名称', key: 'name', style: { flex: 1 } },
                    { label: '人数', key: 'count', style: { width: '56px' } },
                    { label: '类型', key: 'type', style: { width: '94px' } },
                    { label: '金额', key: 'price', style: { width: '72px', textAlign: 'right' } },
                ]
            },


            headerClass() {
                return `pe-charge-fee-list-${this.printData.type}__header`
            },
        },
    }
</script>

<style lang="scss">
@import "./style/reset.scss";

.pe-charge-fee-list-wrapper {
  .pe-charge-fee-list__title {
    margin-bottom: 24px;
    font-size: 21px;
    line-height: 22px;
    text-align: center;
    font-weight: 700;
  }

  .pe-charge-fee-list-personal__header {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    font-size: 11px;
    font-weight: 300;
    line-height: 16px;

    .list__header-item {
      display: flex;
      align-items: flex-start;
      width: calc((100% - 8px) / 3);

      .header-item__label {
        flex-shrink: 0;
      }

      .header-item__value {
        flex: 1;
        margin-left: 4px;
      }
    }
  }

  .pe-charge-fee-list-group__header {
    display: flex;
    gap: 4px;
    font-size: 11px;
    font-weight: 300;
    line-height: 16px;

    .list__header-item {
      display: flex;
      align-items: flex-start;
      flex: 2;

      &:first-child {
        flex: 3;
      }

      .header-item__label {
        flex-shrink: 0;
      }

      .header-item__value {
        flex: 1;
        word-break: break-all;
        overflow: hidden;
        margin-left: 4px;
      }
    }
  }

  .pe-charge-fee-list__content {
    border: 1px solid #000;
    margin-top: 8px;

    .pe-charge-fee-list__content-header {
      font-size: 13px;
      font-weight: 400;
      line-height: 16px;
      border-bottom: 1px solid #000;
      display: flex;

      .content-header-item {
        height: 32px;
        padding: 0 8px;
        line-height: 32px;
      }

      .content-header-item + .content-header-item {
        border-left: 1px solid #000;
      }
    }

    .pe-charge-fee-list__content-section {
      display: flex;
      font-size: 13px;
      font-style: normal;
      font-weight: 300;

      .content-section-item {
        height: 32px;
        padding: 0 8px;
        line-height: 32px;
      }

      .content-section-item +.content-section-item {
        border-left: 1px solid #000;
      }
    }

    .pe-charge-fee-list__content-section + .pe-charge-fee-list__content-section {
      border-top: 1px solid #000;
    }
  }

  .pe-charge-fee-list__footer {
    display: flex;
    font-size: 13px;
    font-weight: 400;
    line-height: 16px;
    border-top: 1px solid #000;
    padding-top: 8px;

    .pe-charge-fee-list__footer-item {
      display: flex;

      .footer-item__label {
        flex-shrink: 0;
      }

      .footer-item__value {
        flex: 1;
        word-break: break-all;
        overflow: hidden;
      }

        &:nth-child(1) {
            flex: 6;
        }

        &:nth-child(2) {
            flex: 8;
        }

      &:last-child {
        flex: 10;
      }
    }

    .pe-charge-fee-list__footer-item + .pe-charge-fee-list__footer-item {
      margin-left: 8px;
    }
  }
}

</style>