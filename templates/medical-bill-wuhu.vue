<template>
    <div>
        <template v-for="(page, pageIndex) in currentRenderPage">
            <div
                :key="pageIndex"
                class="wuhu-medical-bill-content"
            >
                <div
                    class="wuhu-medical-bill-page"
                >
                    <refund-icon
                        v-if="isRefundBill"
                        top="2mm"
                        left="15mm"
                    ></refund-icon>

                    <block-box
                        top="19"
                        left="26"
                    >
                        {{ patient.name }}
                    </block-box>

                    <!-- 西药费 -->
                    <block-box
                        top="33"
                        left="32"
                    >
                        {{ westernMedicineCalcFee | formatMoney }}
                    </block-box>

                    <!-- 中成药费 -->
                    <block-box
                        top="38"
                        left="32"
                    >
                        {{ chineseComposeMedicineCalcFee | formatMoney }}
                    </block-box>

                    <!-- 中草药费 -->
                    <block-box
                        top="43.5"
                        left="32"
                    >
                        {{ chineseMedicineDrinksPieceCalcFee | formatMoney }}
                    </block-box>

                    <!-- 诊察费 -->
                    <block-box
                        top="49"
                        left="32"
                    >
                        {{ examinationCalcFee | formatMoney }}
                    </block-box>

                    <!-- 检查费 -->
                    <block-box
                        top="54"
                        left="32"
                    >
                        {{ examinationInspectionCalcFee | formatMoney }}
                    </block-box>

                    <!-- 治疗费 -->
                    <block-box
                        top="59"
                        left="32"
                    >
                        {{ treatmentCalcFee | formatMoney }}
                    </block-box>

                    <!-- 手术费 -->
                    <block-box
                        top="33"
                        left="62"
                    >
                        {{ operationCalcFee | formatMoney }}
                    </block-box>

                    <!-- 化验费 -->
                    <block-box
                        top="38"
                        left="62"
                    >
                        {{ examinationExaminationCalcFee | formatMoney }}
                    </block-box>

                    <!-- 其他费 -->
                    <block-box
                        top="43.5"
                        left="62"
                    >
                        {{ otherCalcFee | formatMoney }}
                    </block-box>

                    <block-box
                        top="48"
                        left="47"
                    >
                        材料费
                    </block-box>
                    <block-box
                        top="49"
                        left="62"
                    >
                        {{ materialCalcFee | formatMoney }}
                    </block-box>

                    <block-box
                        top="64.5"
                        left="33"
                    >
                        {{ finalFee | formatMoney }}
                    </block-box>

                    <block-box
                        top="69"
                        left="33"
                    >
                        {{ digitUppercase(finalFee) }}
                    </block-box>

                    <!-- 医保支付 -->
                    <block-box
                        top="75"
                        left="31"
                    >
                        {{ shebaoPayment.receivedFee | formatMoney }}
                    </block-box>

                    <!-- 个人现金 -->
                    <block-box
                        top="75"
                        left="62"
                    >
                        {{ printData.personalPaymentFee | formatMoney }}
                    </block-box>

                    <!-- 账户余额 -->
                    <block-box
                        top="81"
                        left="39"
                    >
                        {{ shebaoPayment.cardBalance | formatMoney }}
                    </block-box>

                    <block-box
                        top="86"
                        left="63"
                    >
                        {{ printData.chargedByName }}
                    </block-box>

                    <block-box
                        top="86"
                        left="25"
                    >
                        {{ printData.chargedTime | parseTime('y-m-d') }}
                    </block-box>

                    <block-box
                        top="86"
                        left="91"
                        style="width: 47mm;"
                    >
                        {{ wuhu.institutionName }}
                    </block-box>

                    <div
                        class="form-items-wrapper"
                    >
                        <div
                            v-for="(item, index) in page.formItems"
                            :key="index + pageIndex"
                            class="form-item-tr"
                        >
                            <span class="item-name">
                                <template v-if="isShowSocialCode(item)">[{{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}]</template>
                                {{ item.name }}
                            </span>
                            <span class="item-price">{{ item.count }}{{ item.unit }}</span>
                            <span class="item-price">{{ item.discountedUnitPrice | formatMoney }}</span>
                            <span class="total-price">{{ item.discountedPrice | formatMoney }}</span>
                        </div>

                        <div
                            v-if="hasOverPageTip"
                            class="only-one-page"
                        >
                            *** 因纸张限制，部分项目未打印 ***
                        </div>
                        <template v-else>
                            <div
                                v-if="pageIndex !== renderPage.length - 1"
                                class="next-page"
                            >
                                *** 接下页 ***
                            </div>
                        </template>
                    </div>

                    <div
                        v-for="(feeItem, feeItemIndex) in rightItems"
                        :key="feeItemIndex"
                        :class="`right-items-${feeItemIndex}`"
                    >
                        <div class="right-items-patient-name">
                            {{ patient.name }}
                        </div>
                        <div
                            v-for="item in feeItem.items"
                            :key="item"
                        >
                            {{ item }}
                        </div>
                        <div class="right-items-total-price">
                            {{ feeItem.totalPrice | formatMoney }}
                        </div>
                        <div class="right-items-charge-name">
                            {{ printData.chargedByName }}
                        </div>
                        <div class="right-items-charge-time">
                            {{ printData.chargedTime | parseTime('y-m-d') }}
                        </div>
                    </div>
                </div>
            </div>
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import NationalBillData from "./mixins/national-bill-data";
    import clone from "./common/clone";

    export default {
        name: "MedicalBillWuhu",
        components: {
            RefundIcon,
            BlockBox
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_WUHU,
        pages: [
            {
                paper: PageSizeMap.MM241_94_WUHU,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        data() {
            return {
                westernMedicineCalcFee: 0, // 西药费
                chineseComposeMedicineCalcFee: 0, // 中成药费
                chineseMedicineDrinksPieceCalcFee: 0, // 中草药费
                examinationCalcFee: 0, // 诊察费
                examinationInspectionCalcFee: 0, // 检查费
                treatmentCalcFee: 0, // 治疗费
                operationCalcFee: 0, // 手术费
                examinationExaminationCalcFee: 0, // 化验费
                materialCalcFee: 0, // 材料费
                otherCalcFee: 0, // 其他费用
                rightItems: [{
                    items: [],
                    totalPrice: 0,
                }, {
                    items: [],
                    totalPrice: 0,
                }, {
                    items: [],
                    totalPrice: 0,
                }],
            }
        },
        computed: {
            wuhu() {
                return this.config.wuhu || {}
            },
            splitType() {
                return this.wuhu.splitType;
            },
            isOnlyOnePage() {
                if (this.splitType === 1) {
                    return this.renderPage.length > 1;
                }
                return false;
            },
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                if (this.isOnlyOnePage || this.extra.isPreview) {
                    return this.renderPage.slice(0, 1);
                }
                return this.renderPage;
            },
            medicalBills() {
                return this.printData.medicalBills && this.printData.medicalBills.filter(billItem => {
                    if (billItem.name === this.$t('registrationFeeName')) {
                        return !!billItem.totalFee
                    }
                    return true
                }) || [];
            },
            renderPage() {
                return this.spliceFormItems(this.chargeFormItems, 12);
            },
            otherFeeTotal() {
                if (this.isFeeCompose) {
                    return this.registrationFee + this.examinationFee + this.nursingFee + this.physiotherapyFee + this.bedFee + this.generalDiagnosisAndTreatmentFee + this.pharmacyServiceFee + this.outerFlagFee;
                }
                return this.registrationFee + this.otherFee
            },
            // 是否医保支付
            isShebaoPayment() {
                return Object.keys(this.shebaoPayment).length
            },
        },
        created() {
            this.initFee();

            this.westernMedicineCalcFee = this.westernMedicineFee;
            this.chineseComposeMedicineCalcFee = this.chineseComposeMedicineFee;
            this.chineseMedicineDrinksPieceCalcFee = this.chineseMedicineDrinksPieceFee;
            this.examinationCalcFee = this.examinationFee + this.registrationFee + this.generalDiagnosisAndTreatmentFee;
            this.examinationInspectionCalcFee = this.examinationInspectionFee;
            this.treatmentCalcFee = this.treatmentFee;
            this.operationCalcFee = this.operationFee;
            this.examinationExaminationCalcFee = this.examinationExaminationFee;
            this.materialCalcFee = this.materialFee;
            this.otherCalcFee = this.outerFlagFee + this.otherFee + this.pharmacyServiceFee + this.bedFee + this.nursingFee + this.physiotherapyFee;
            const feeList = [
                {
                    name: '西药费',
                    price: this.westernMedicineCalcFee,
                },
                {
                    name: '中成药费',
                    price: this.chineseComposeMedicineCalcFee,
                },
                {
                    name: '中草药费',
                    price: this.chineseMedicineDrinksPieceCalcFee,
                },
                {
                    name: '诊察费',
                    price: this.examinationCalcFee,
                },
                {
                    name: '检查费',
                    price: this.examinationInspectionCalcFee,
                },
                {
                    name: '治疗费',
                    price: this.treatmentCalcFee,
                },
                {
                    name: '手术费',
                    price: this.operationCalcFee,
                },
                {
                    name: '化验费',
                    price: this.examinationExaminationCalcFee,
                },
                {
                    name: '材料费',
                    price: this.materialCalcFee,
                },
                {
                    name: '其他费用',
                    price: this.otherCalcFee,
                },
            ];
            let index = 0;
            feeList.forEach((feeItem) => {
                if (+feeItem.price !== 0) {
                    this.rightItems[index].items.push(feeItem.name);
                    this.rightItems[index].totalPrice += feeItem.price;
                    index++;
                    if (index % 3 === 0) {
                        index = 0;
                    }
                }
            })
        },
    }
</script>
<style lang="scss">
* {
    padding: 0;
    margin: 0;
}

.wuhu-medical-bill-content {
    @import "./components/refund-icon/refund-icon.scss";

    font-size: 9pt;

    .wuhu-medical-bill-page {
        position: absolute;
        width: 241mm;
        height: 94mm;
    }

    .trans-number,
    .patient,
    .form-items-wrapper,
    .medical-bill-wrapper {
        position: absolute;
        line-height: 11pt;
    }

    .trans-number {
        top: 47.5mm;
        left: 30mm;
    }

    .patient {
        top: 24mm;
    }

    .form-items-wrapper {
        top: 32mm;
        left: 76mm;
        width: 60mm;
        height: 47mm;
    }

    .next-page {
        position: absolute;
        bottom: -6mm;
        left: 0;
        width: 100%;
        text-align: center;
    }

    .form-item-tr {
        width: 100%;
        height: 4mm;
        font-size: 0;
        line-height: 11pt;
    }

    .item-name,
    .item-count,
    .item-price,
    .item-spec,
    .item-proportion,
    .total-price {
        display: inline-block;
        *display: inline;
        overflow: hidden;
        font-size: 9pt;
        word-break: keep-all;
        white-space: nowrap;
        *zoom: 1;
    }

    .shebao-wrapper {
        position: absolute;
        top: 60mm;
        left: 112mm;
    }

    .item-name {
        width: 27mm;
    }

    .item-count {
        width: 36mm;
        text-align: center;
    }

    .item-proportion {
        width: 6mm;
        padding-left: 1mm;
        text-align: right;
    }

    .item-spec {
        padding-left: 2mm;
    }

    .item-price,
    .item-spec {
        width: 10mm;
        *width: 10mm;
        text-align: right;
    }

    .total-price {
        width: 13mm;
        *width: 13mm;
        text-align: right;
    }

    .medical-bill-wrapper {
        top: 37mm;
        left: 23mm;
        width: 62mm;
        height: 31mm;
    }

    .medical-bill-item {
        position: absolute;
        width: 31mm;
    }

    .upper-money {
        position: absolute;
        top: 96mm;
    }

    .upper-money {
        left: 36mm;
    }

    .only-one-page {
        text-align: center;
    }

    .right-items-0 {
        position: absolute;
        top: 26mm;
        left: 156mm;
        width: 20mm;
        height: 66mm;
    }

    .right-items-1 {
        position: absolute;
        top: 26mm;
        left: 185mm;
        width: 20mm;
        height: 66mm;
    }

    .right-items-2 {
        position: absolute;
        top: 26mm;
        left: 214mm;
        width: 20mm;
        height: 66mm;
    }

    .right-items-patient-name {
        margin-bottom: 2mm;
    }

    .right-items-total-price {
        position: absolute;
        bottom: 9.5mm;
    }

    .right-items-charge-name {
        position: absolute;
        bottom: 5mm;
    }

    .right-items-charge-time {
        position: absolute;
        bottom: 1mm;
    }
}

.abc-page_preview {
    color: #2a82e4;
    background: url("/static/assets/print/wuhu.jpg");
    background-size: 241mm 94mm;

    .wuhu-medical-bill-page {
        top: -1mm;
    }
}
</style>
