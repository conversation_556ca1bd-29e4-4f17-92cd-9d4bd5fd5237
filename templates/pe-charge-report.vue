<!--exampleData

{
    PRINT_REGION: 'hangzhou'
}

-->
<template>
    <div>
        <div class="operate-cashier-report">
            <div class="report-title">
                {{ reportTitle }}
            </div>

            <table class="report-table no-border-table">
                <tbody>
                    <tr>
                        <td colspan="2">
                            时间：{{ summaryData.dateRange }}
                        </td>
                    </tr>
                </tbody>
            </table>

            <table class="report-table">
                <tbody>
                    <tr>
                        <td colspan="1">
                            现金结算合计
                        </td>
                        <td colspan="1">
                            {{ summaryData.cashSettleTotalAmount || 0 }}
                        </td>
                        <td colspan="1">
                            实收合计
                        </td>
                        <td colspan="1">
                            {{ summaryData.receivedTotalAmount || 0 }}
                        </td>
                    </tr>
                </tbody>
            </table>

            <table class="report-table">
                <tbody>
                    <tr>
                        <td
                            colspan="6"
                            class="title-td"
                        >
                            收费方式
                        </td>
                    </tr>
                    <tr
                        v-for="(tr, trIndex) in payModes"
                        :key="trIndex"
                    >
                        <template v-for="(td, tdIndex) in tr">
                            <td
                                :key="tdIndex + td.name"
                                colspan="1"
                            >
                                {{ td.name }}
                            </td>
                            <td
                                :key="tdIndex + td.name + td.value + trIndex"
                                :colspan="tdIndex === tr.length - 1 && trIndex === payModes.length - 1 ? lastTdColdSpan : 1"
                                :title="td.value || 0"
                            >
                                {{ td.value || 0 }}
                            </td>
                        </template>
                    </tr>
                </tbody>
            </table>
            <table class="report-table no-border-table">
                <tbody>
                    <tr>
                        <td
                            colspan="1"
                        >
                            收费员：{{ summaryData.employeeName }}
                        </td>
                        <td
                            colspan="1"
                        >
                            制表人：
                        </td>
                        <td
                            colspan="1"
                            style="width: 210px"
                        >
                            制表时间：{{ summaryData.tabulationTime }}
                        </td>
                        <td
                            colspan="1"
                        >
                            审核人：
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>

    import PrintHandler from "./data-handler/print-handler.js";

    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    export default {
        name: "PEChargeReport",
        DataHandler: PrintHandler,
        businessKey: PrintBusinessKeyEnum.STAT_PE_CHARGE_REPORT,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: Object,
        },
        data() {
            return {
                lastTdColdSpan: 1,
            }
        },
        computed: {
            payModes() {
                return this.divideBy(this.printData.payModes, 3)
            },
            printData() {
                return this.renderData.printData;
            },
            summaryData() {
                return this.printData.summaryData
            },

            reportTitle() {
                return this.printData.currentClinicName
            },
        },
        created() {
            const lastColSpan = this.printData.payModes.length % 3;
            if (lastColSpan === 1) {
                this.lastTdColdSpan = 5;
            } else if (lastColSpan === 2) {
                this.lastTdColdSpan = 3;
            } else {
                this.lastTdColdSpan = 1;
            }
        },
        methods: {
            divideBy(arr, count) {
                const result = [];
                let tmp = [];
                for (let i = 0, ii = arr.length; i < ii; i++) {
                    if (tmp.length === count) {
                        result.push(tmp);
                        tmp = [];
                    }
                    tmp.push(arr[i]);

                    if (i === ii - 1 && tmp.length > 0) {
                        result.push(tmp);
                    }
                }
                return result;
            },
        }
    }
</script>

<style lang="scss">
@import "./style/reset.scss";
.operate-cashier-report {
  margin: 0 auto;
  font-family: "Microsoft YaHei", "微软雅黑";

  .report-title {
    margin-bottom: 24pt;
    font-size: 16pt;
    line-height: 21pt;
    text-align: center;
    font-weight: 600;
  }

  .report-table {
    margin-top: 20px;
    width: 100%;
    table-layout: fixed;

    &.no-border-table {
      td {
        border: none;
      }
    }

    td {
      padding: 7pt 6pt;
      font-size: 10pt;
      line-height: 11pt;
      border: 1px solid #000000;
    }

    .title-td {
      font-size: 10pt;
      font-weight: bold;
      line-height: 11pt;
      background-color: #f5f7fb;
    }
  }
}


</style>