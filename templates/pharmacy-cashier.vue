<template>
    <pharmacy-cashier-template
        :config="config"
        :print-data="printData"
        :is-refund="isRefund"
        :is-optimization="isOptimization"
    ></pharmacy-cashier-template>
</template>

<script>
    import Pharmacy<PERSON>ashierTemplate from "./components/pharmacy-cashier/index.vue";

    import PrintCommonDataHandler from './data-handler/common-handler';
    import { PrintBusinessKeyEnum } from './constant/print-constant';
    import PageSizeMap, { Orientation } from "../share/page-size.js";
	
    export default {
        DataHandler: PrintCommonDataHandler,
        components: {
            PharmacyCashierTemplate
        },
        businessKey: PrintBusinessKeyEnum.PHARMACY_CASHIER,
        pages: [
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM58,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM70_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80_100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_140,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_93_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM148_118_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM200_1397,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM175_94,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_200,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM90_120_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM241_279,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分' // 默认选择的等分纸
            },
        ],
        props: {
            renderData: {
                type: Object,
                default: () => ({})
            },
            options: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        computed: {
            printData() {
                return this.renderData.printData
            },
            config() {
                if(this.renderData.config && this.renderData.config.cashier) {
                    return this.renderData.config.cashier;
                }
                return {};
            },
            isRefund() {
                return this.printData?.isRefund
            },
            // 58mm 小票需要优化排版
            isOptimization() {
                return this.options.page?.size === '热敏小票（58mm）';
            },
        },
    }
</script>
