<!--exampleData
{
	"patient": {
		"id": "ffffffff000000001bc17db00f42e000",
		"name": "打印重构",
		"namePy": null,
		"namePyFirst": null,
		"birthday": "1999-11-04",
		"mobile": "18628245639",
		"sex": "男",
		"idCard": null,
		"isMember": 1,
		"age": {
			"year": 22,
			"month": 1,
			"day": 30
		},
		"address": null,
		"sn": null,
		"remark": null,
		"profession": null,
		"company": null,
		"patientSource": null,
		"tags": null,
		"marital": null,
		"weight": null,
		"wxOpenId": null,
		"wxHeadImgUrl": null,
		"wxNickName": null,
		"wxBindStatus": null,
		"isAttention": null,
		"shebaoCardInfo": null,
		"childCareInfo": null,
		"chronicArchivesInfo": null
	},
	"organ": {
		"id": "fff730ccc5ee45d783d82a85b8a0e52d",
		"name": "高新大源店",
		"shortName": "高新大源店",
		"addressDetail": "趵突泉1",
		"contactPhone": "18910121191",
		"logo": null,
		"category": null
	},
	"dispensingForms": [{
		"dispensingFormItems": [{
			"name": "酚咖片(芬必得)",
			"unit": "片",
			"count": 1.000000,
			"specialRequirement": "首次加倍",
			"formatSpec": "20片/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "制霉素片(a)",
			"unit": "袋",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "50IU*231袋/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "龙泽熊胆胶囊(熊胆丸)",
			"unit": "盒",
			"count": 1.000000,
			"specialRequirement": null,
			"formatSpec": "0.25g*20粒/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "阿莫西林克拉维酸钾片西林克拉维酸钾片西林克拉维酸钾片(中诺艾林)",
			"unit": "片",
			"count": 1.000000,
			"specialRequirement": null,
			"formatSpec": "375mg*12片/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "注射用硫酸奈替米星",
			"unit": "ml",
			"count": 9.999000,
			"specialRequirement": null,
			"formatSpec": "10IU*2ml/支",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}],
		"sourceFormType": 4,
		"processUsageInfo": null,
		"dispensingFormId": "ffffffff000000001e3994d00d52e01a",
		"processBagUnitCount": 0,
		"auditName": null,
		"auditId": null,
		"isCombineForm": 0,
		"cMSpec": null
	}, {
		"dispensingFormItems": [{
			"name": "龙泽熊胆胶囊(熊胆丸)",
			"unit": "盒",
			"count": 1.000000,
			"specialRequirement": "续用(皮试阴性)",
			"formatSpec": "0.25g*20粒/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "十滴水胶丸(拾)",
			"unit": "盒",
			"count": 1.000000,
			"specialRequirement": null,
			"formatSpec": "2片/盒",
			"position": "",
			"productType": 1,
			"productSubType": 3
		}, {
			"name": "布洛芬缓释胶囊(芬必得)",
			"unit": "粒",
			"count": 4.000000,
			"specialRequirement": "与抗生素间隔2小时",
			"formatSpec": "300mg*20粒/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "氢溴酸右美沙芬分散片",
			"unit": "片",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "15mg*24片/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "口炎颗粒",
			"unit": "袋",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "3g*10袋/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "阿莫西林克拉维酸钾片西林克拉维酸钾片西林克拉维酸钾片(中诺艾林)",
			"unit": "片",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "375mg*11片/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "酚咖片(芬必得)",
			"unit": "片",
			"count": 2.000000,
			"specialRequirement": "续用(皮试阴性)",
			"formatSpec": "20片/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "龙泽熊胆胶囊(熊胆丸)",
			"unit": "盒",
			"count": 1.000000,
			"specialRequirement": null,
			"formatSpec": "0.25g*20粒/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "布洛芬缓释胶囊(芬必得)",
			"unit": "粒",
			"count": 4.000000,
			"specialRequirement": null,
			"formatSpec": "300mg*20粒/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "小儿麻甘颗粒",
			"unit": "袋",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "2.5g*12袋/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "胞磷胆碱钠注射液",
			"unit": "支",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "0.25g*2ml/支",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "注射用硫酸奈替米星",
			"unit": "ml",
			"count": 3.000000,
			"specialRequirement": null,
			"formatSpec": "10IU*2ml/支",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "酚咖片(芬必得)",
			"unit": "片",
			"count": 2.000000,
			"specialRequirement": "续用(皮试阴性)",
			"formatSpec": "20片/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}],
		"sourceFormType": 5,
		"processUsageInfo": null,
		"dispensingFormId": "ffffffff000000001e3994d00d52e020",
		"processBagUnitCount": 0,
		"auditName": null,
		"auditId": null,
		"isCombineForm": 0,
		"cMSpec": null
	}, {
		"dispensingFormItems": [{
			"name": "酚咖片(芬必得)",
			"unit": "片",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "20片/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "氢溴酸右美沙芬分散片",
			"unit": "片",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "15mg*24片/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "注射用硫酸奈替米星",
			"unit": "ml",
			"count": 3.000000,
			"specialRequirement": null,
			"formatSpec": "10IU*2ml/支",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}, {
			"name": "注射用阿魏酸钠",
			"unit": "盒",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "11IU/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}],
		"sourceFormType": 5,
		"processUsageInfo": null,
		"dispensingFormId": "ffffffff000000001e3994d00d52e02e",
		"processBagUnitCount": 0,
		"auditName": null,
		"auditId": null,
		"isCombineForm": 0,
		"cMSpec": null
	}, {
		"dispensingFormItems": [{
			"name": "地骨皮",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "款冬花",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "白芍",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "三七药酒",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "泽泻",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "桉油x桉油x桉油x桉油x",
			"unit": "罐",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "222",
			"position": "B-3-6",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "厚朴",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "番泻叶",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "白前",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "浙贝母",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "矮地茶1",
			"unit": "罐",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "艾叶2",
			"unit": "罐",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "山药",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "高丽参",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "川楝子",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "天麻(野生一级)",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "白花蛇舌草颗粒三三三",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}, {
			"name": "白花蛇舌草颗粒壹4",
			"unit": "g",
			"count": 2.000,
			"doseCount": 3,
			"specialRequirement": null,
			"formatSpec": "",
			"position": "",
			"productType": 1,
			"productSubType": 2
		}],
		"sourceFormType": 6,
		"specification": "中药饮片",
		"doseCount": 3,
		"dailyDosage": "1日半1剂",
		"usage": "煎服",
		"freq": "1日3次",
		"usageLevel": "每次250ml",
		"processUsageInfo": "",
		"dispensingFormId": "ffffffff000000001e3994d00d52e007",
		"processBagUnitCount": 0,
		"auditName": null,
		"auditId": null,
		"isCombineForm": 0,
		"cMSpec": "中药饮片"
	}, {
		"dispensingFormItems": [{
			"name": "医用外科口罩",
			"unit": "袋",
			"count": 2.000000,
			"specialRequirement": null,
			"formatSpec": "N独立包装*1袋/袋*1袋/袋",
			"position": "",
			"productType": 2,
			"productSubType": 1
		}, {
			"name": "盐酸左氧氟沙星滴眼液",
			"unit": "盒",
			"count": 10.000000,
			"specialRequirement": null,
			"formatSpec": "5ml*1揿/盒",
			"position": "",
			"productType": 1,
			"productSubType": 1
		}],
		"sourceFormType": 10,
		"processUsageInfo": null,
		"dispensingFormId": "ffffffff000000001e3994d00d52e033",
		"processBagUnitCount": 0,
		"auditName": null,
		"auditId": null,
		"isCombineForm": 0,
		"cMSpec": null
	}, {
		"dispensingFormItems": [{
			"name": "三爪型异物钳-1",
			"unit": "颗",
			"count": 1.000000,
			"specialRequirement": null,
			"formatSpec": "11支/颗",
			"position": "",
			"productType": 2,
			"productSubType": 1
		}, {
			"name": "心愿(枣花蜜)",
			"unit": "瓶",
			"count": 1.000000,
			"specialRequirement": null,
			"formatSpec": "500g/瓶*500g/瓶",
			"position": "",
			"productType": 7,
			"productSubType": 4
		}],
		"sourceFormType": 0,
		"processUsageInfo": null,
		"dispensingFormId": null,
		"processBagUnitCount": 0,
		"auditName": null,
		"auditId": null,
		"isCombineForm": 1,
		"cMSpec": null
	}],
	"chargedByName": "刘喜",
	"chargedTime": "2022-01-03T07:17:44Z",
	"sellerName": "",
	"dispensedByName": "",
	"dispensedTime": null,
	"auditName": null,
	"auditId": null,
	"isDecoction": false,
	"contactMobile": null,
	"doctorName": "刘喜",
	"patientOrderNo": "00010675",
	"netIncomeFee": 1574.3500,
	"deliveryInfo": null,
	"departmentName": "www",
	"diagnose": "暂无现病史暂无现病史，感冒病",
	"doctorAdvice": "1.服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。服头孢前3天和后7天禁止饮酒，忌用外用酒精。<br>3.多喝热水<br>4.谨遵医嘱<br>5.饮食清淡",
	"barcode": "00010675-01",
	"pharmacyNo": 0,
	"pharmacyType": 0
}

-->
<template>
    <dispensing-component-ticket
        :render-data="renderData"
        :is-undispense="isUndispense"
    >
    </dispensing-component-ticket>
</template>

<script>
    import DispensingComponentTicket from './components/dispensing/dispensing-component-ticket.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import DispensingHandler from './data-handler/dispensing-handler';

    export default {
        name: 'PrintDispensingTemplate',
        components: {
            DispensingComponentTicket,
        },
        businessKey: PrintBusinessKeyEnum.DISPENSE,
        DataHandler: DispensingHandler,
        pages: [
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM58,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM70_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80_100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_140,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_93_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM148_118_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM200_1397,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM175_94,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM175_94_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_200,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM90_120_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分' // 默认选择的等分纸
            },
        ],
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
            extra: {
                type: Object,
                default() {
                    return {}
                }
            },
        },

        computed: {
            isUndispense() {
                return !!this.extra.isUndispense;
            }
        },
    };
</script>