<template>
    <div class="hospital-advice-shandong-wrapper">
        <template v-for="(page, pageIndex) in printPages">
            <div
                class="hospital-advice-shandong-header-wrapper"
                :class="`${page.isPageUpdated ? 'is-not-update-advice-color' : 'is-new-advice-color'}`"
            >
                <div class="hospital-advice-shandong-title">
                    <div class="hospital-advice-shandong-custom-title">
                        <template v-if="page.printConfig.title">
                            <span>{{ page.printConfig.title }}</span>
                            <!--<span v-if="page.printConfig.subtitle">{{ page.printConfig.subtitle }}</span>-->
                        </template>
                        <template v-else>
                            <span>{{ clinicName }}</span>
                        </template>
                    </div>
                    <div class="hospital-advice-shandong-type">
                        {{ typeStr }}
                    </div>
                </div>
                <div class="hospital-advice-shandong-patient-info">
                    <div style="width: 16%;">
                        姓名：{{ patient.name }}
                    </div>
                    <div style="width: 10%;">
                        性别：{{ patient.sex }}
                    </div>
                    <div style="width: 14%;">
                        年龄：{{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}
                    </div>
                    <div>科别：{{ patientHospitalInfo.departmentName }}</div>
                    <div>病区：{{ patientHospitalInfo.wardName }} {{ patientHospitalInfo.bedNo }}床</div>
                    <div style=" width: 16%; text-align: right;">
                        住院号：{{ patientHospitalInfo.no }}
                    </div>
                </div>
            </div>

            <table
                v-if="isLongTimeAdvice"
                data-type="block"
                class="hospital-advice-shandong-table"
                :class="page.isPageUpdated ? 'table-is-update' : 'table-is-not-update'"
            >
                <tbody>
                    <tr
                        class="hospital-advice-shandong-header-th"
                        :class="`${page.isPageUpdated ? 'is-not-update-advice-color' : 'is-new-advice-color'}`"
                    >
                        <td :colspan="4">
                            <div class="hospital-advice-shandong-header-td-content">
                                起始
                            </div>
                        </td>
                        <td :colspan="3">
                            <div class="hospital-advice-shandong-header-td-content">
                                停止
                            </div>
                        </td>
                    </tr>

                    <tr
                        class="hospital-advice-shandong-header-th"
                        :class="`${page.isPageUpdated ? 'is-not-update-advice-color' : 'is-new-advice-color'}`"
                    >
                        <td class="hospital-advice-shandong-start-time">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                年月日时分
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-content">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                医嘱内容
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                医师签名
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                护士签名
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-stop-time">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                月日时分
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                医师签名
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                护士签名
                            </div>
                        </td>
                    </tr>

                    <tr
                        v-for="item in tableContentTrList"
                        :key="`hospital-advice-shandong-table-advice-tr-${item}`"
                        class="hospital-advice-shandong-content-tr"
                        :class="`${page.list[item] && page.list[item].isAdviceNew ? 'is-update-advice-color' : page.list[item] && page.list[item].isContinueAdvice ? 'is-not-update-advice-color' : 'is-new-advice-color'}`"
                    >
                        <td class="hospital-advice-shandong-start-time">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item]">
                                    {{ page.list[item].startTime | parseTime('y-m-d') }}
                                    <br />
                                    {{ page.list[item].startTime | parseTime('h:i') }}
                                </template>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-content">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item]">
                                    <div
                                        v-if="isNurseOrTreatment(page.list[item].adviceRuleType)"
                                        class="hospital-advice-shandong-nurse-treatment-content"
                                    >
                                        <div class="hospital-advice-shandong-western-medicine-name">
                                            {{ page.list[item].name }} {{ page.list[item].remark }}
                                        </div>

                                        <template
                                            v-if="showSingleDosage(page.list[item]) && page.list[item].singleDosageCount && parseInt(page.list[item].singleDosageCount) > 1 && page.list[item].singleDosageUnit"
                                        >
                                            <div class="hospital-advice-shandong-western-medicine-single-dosage">
                                                {{ page.list[item].singleDosageCount }}{{ transformAdviceUnit(page.list[item]) }}
                                            </div>

                                            <div
                                                class="hospital-advice-shandong-group-start"
                                                style="visibility: hidden;"
                                            ></div>

                                            <div
                                                class="hospital-advice-shandong-usage"
                                                style="visibility: hidden;"
                                            >
                                                占位符
                                            </div>
                                        </template>

                                        <div class="hospital-advice-shandong-freq">
                                            {{ page.list[item].freq }}
                                        </div>
                                    </div>

                                    <div
                                        v-else-if="isChineseMedicine(page.list[item].adviceRuleType)"
                                        class="hospital-advice-shandong-nurse-treatment-content"
                                    >
                                        <div class="hospital-advice-shandong-western-medicine-name">
                                            中药饮片 {{ page.list[item].dosageCount }}{{ page.list[item].dosageUnit }} {{ page.list[item].usage }} {{ page.list[item].remark }}
                                        </div>
                                        <div>
                                            {{ page.list[item].dailyDosage }}
                                        </div>
                                    </div>

                                    <div
                                        v-else-if="isWesternMedicine(page.list[item].adviceRuleType)"
                                        class="hospital-advice-shandong-western-medicine-content"
                                    >
                                        <div class="hospital-advice-shandong-western-medicine-name">
                                            {{ page.list[item].name }} {{ getAstInfo(page.list[item]) }} {{ page.list[item].remark }}
                                        </div>

                                        <div class="hospital-advice-shandong-western-medicine-single-dosage">
                                            {{ page.list[item].singleDosageCount }}{{ transformAdviceUnit(page.list[item]) }}
                                        </div>

                                        <div
                                            v-if="page.list[item].isStart"
                                            class="hospital-advice-shandong-group-start"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-group-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-group-color' : 'is-new-advice-group-color'}`"
                                        ></div>
                                        <div
                                            v-else-if="page.list[item].isMiddle"
                                            class="hospital-advice-shandong-group-middle"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-group-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-group-color' : 'is-new-advice-group-color'}`"
                                        ></div>
                                        <div
                                            v-else-if="page.list[item].isEnd"
                                            class="hospital-advice-shandong-group-end"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-group-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-group-color' : 'is-new-advice-group-color'}`"
                                        ></div>
                                        <div
                                            v-else
                                            class="hospital-advice-shandong-group-position"
                                        ></div>

                                        <div
                                            class="hospital-advice-shandong-usage"
                                            :style="page.list[item].isMiddle || page.list[item].isStart ? { color: 'transparent !important' } : {}"
                                        >
                                            {{ page.list[item].usage }}
                                        </div>

                                        <div
                                            class="hospital-advice-shandong-freq"
                                            :style="page.list[item].isMiddle || page.list[item].isStart ? { color: 'transparent !important' } : {}"
                                        >
                                            {{ page.list[item].freq }}
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item]">
                                    <template v-if="page.printConfig.startDoctor === 2">
                                        {{ page.list[item].createdByName }}
                                    </template>
                                    <template v-else-if="page.printConfig.startDoctor === 3">
                                        <img
                                            v-if="judgeImgUrl(page.list[item].createdByHandSign)"
                                            :src="page.list[item].createdByHandSign"
                                            alt=""
                                            class="hospital-advice-shandong-content-td-sign-img"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-img-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-img-color' : 'is-new-advice-img-color'}`"
                                        />
                                        <template v-else>
                                            {{ page.list[item].createdByName }}
                                        </template>
                                    </template>
                                </template>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item] && page.list[item].status >= MedicalAdviceStatusEnum.CHECKED">
                                    <span
                                        v-if="page.printConfig.startNurse === 2"
                                        :class="`${page.list[item].adviceGroupInfo.newCheckedOperatorName ? 'is-update-advice-color' : ''}`"
                                    >
                                        {{
                                            page.list[item].adviceGroupInfo.newCheckedOperatorName ?
                                                page.list[item].adviceGroupInfo.newCheckedOperatorName :
                                                page.list[item].adviceGroupInfo.checkedOperatorName
                                        }}
                                    </span>
                                    <template v-else-if="page.printConfig.startNurse === 3">
                                        <img
                                            v-if="judgeImgUrl(page.list[item].adviceGroupInfo.newCheckedOperatorHandSign) || judgeImgUrl(page.list[item].adviceGroupInfo.checkedOperatorHandSign)"
                                            :src="judgeImgUrl(page.list[item].adviceGroupInfo.newCheckedOperatorHandSign) ? page.list[item].adviceGroupInfo.newCheckedOperatorHandSign : page.list[item].adviceGroupInfo.checkedOperatorHandSign"
                                            alt=""
                                            class="hospital-advice-shandong-content-td-sign-img"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-img-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-img-color' : 'is-new-advice-img-color'} ${page.list[item].adviceGroupInfo.newCheckedOperatorHandSign ? 'is-update-advice-img-color' : ''}`"
                                        />
                                        <span
                                            v-else
                                            :class="`${page.list[item].adviceGroupInfo.newCheckedOperatorName ? 'is-update-advice-color' : ''}`"
                                        >
                                            {{
                                                page.list[item].adviceGroupInfo.newCheckedOperatorName ?
                                                    page.list[item].adviceGroupInfo.newCheckedOperatorName :
                                                    page.list[item].adviceGroupInfo.checkedOperatorName
                                            }}
                                        </span>
                                    </template>
                                </template>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-stop-time">
                            <div class="hospital-advice-shandong-content-td-content">
                                <span
                                    v-if="page.printConfig.longTimeStopTime !== 0 && page.list[item] && page.list[item].status >= MedicalAdviceStatusEnum.STOPPED"
                                    :class="`${page.list[item].adviceGroupInfo.newStopTime ? 'is-update-advice-color' : ''}`"
                                >
                                    {{ (page.list[item].adviceGroupInfo.newStopTime ? page.list[item].adviceGroupInfo.newStopTime : page.list[item].adviceGroupInfo.stopTime) | parseTime('m-d h:i') }}
                                </span>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item] && page.list[item].status >= MedicalAdviceStatusEnum.STOPPED">
                                    <span
                                        v-if="page.printConfig.stopDoctor === 2"
                                        :class="`${page.list[item].adviceGroupInfo.newStopDoctorName ? 'is-update-advice-color' : ''}`"
                                    >
                                        {{
                                            page.list[item].adviceGroupInfo.newStopDoctorName ?
                                                page.list[item].adviceGroupInfo.newStopDoctorName :
                                                page.list[item].adviceGroupInfo.stopDoctorName
                                        }}
                                    </span>
                                    <template v-else-if="page.printConfig.stopDoctor === 3">
                                        <img
                                            v-if="judgeImgUrl(page.list[item].adviceGroupInfo.newStopDoctorHandSign) || judgeImgUrl(page.list[item].adviceGroupInfo.stopDoctorHandSign)"
                                            :src="judgeImgUrl(page.list[item].adviceGroupInfo.newStopDoctorHandSign) ? page.list[item].adviceGroupInfo.newStopDoctorHandSign : page.list[item].adviceGroupInfo.stopDoctorHandSign"
                                            alt=""
                                            class="hospital-advice-shandong-content-td-sign-img"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-img-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-img-color' : 'is-new-advice-img-color'} ${page.list[item].adviceGroupInfo.newStopDoctorHandSign ? 'is-update-advice-img-color' : ''}`"
                                        />
                                        <span
                                            v-else
                                            :class="`${page.list[item].adviceGroupInfo.newStopDoctorName ? 'is-update-advice-color' : ''}`"
                                        >
                                            {{
                                                page.list[item].adviceGroupInfo.newStopDoctorName ?
                                                    page.list[item].adviceGroupInfo.newStopDoctorName :
                                                    page.list[item].adviceGroupInfo.stopDoctorName
                                            }}
                                        </span>
                                    </template>
                                </template>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item]">
                                    <span
                                        v-if="page.printConfig.stopNurse === 2"
                                        :class="`${page.list[item].adviceGroupInfo.newStopConfirmOperatorName ? 'is-update-advice-color' : ''}`"
                                    >
                                        {{
                                            page.list[item].adviceGroupInfo.newStopConfirmOperatorName ?
                                                page.list[item].adviceGroupInfo.newStopConfirmOperatorName :
                                                page.list[item].adviceGroupInfo.stopConfirmOperatorName
                                        }}
                                    </span>
                                    <template v-else-if="page.printConfig.stopNurse === 3">
                                        <img
                                            v-if="judgeImgUrl(page.list[item].adviceGroupInfo.newStopConfirmOperatorHandSign) || judgeImgUrl(page.list[item].adviceGroupInfo.stopConfirmOperatorHandSign)"
                                            :src="judgeImgUrl(page.list[item].adviceGroupInfo.newStopConfirmOperatorHandSign) ? page.list[item].adviceGroupInfo.newStopConfirmOperatorHandSign : page.list[item].adviceGroupInfo.stopConfirmOperatorHandSign"
                                            alt=""
                                            class="hospital-advice-shandong-content-td-sign-img"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-img-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-img-color' : 'is-new-advice-img-color'} ${page.list[item].adviceGroupInfo.newStopConfirmOperatorHandSign ? 'is-update-advice-img-color' : ''}`"
                                        />
                                        <span
                                            v-else
                                            :class="`${page.list[item].adviceGroupInfo.newStopConfirmOperatorName ? 'is-update-advice-color' : ''}`"
                                        >
                                            {{
                                                page.list[item].adviceGroupInfo.newStopConfirmOperatorName ?
                                                    page.list[item].adviceGroupInfo.newStopConfirmOperatorName :
                                                    page.list[item].adviceGroupInfo.stopConfirmOperatorName
                                            }}
                                        </span>
                                    </template>
                                </template>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
			
            <table
                v-if="!isLongTimeAdvice"
                data-type="block"
                class="hospital-advice-shandong-table"
                :class="page.isPageUpdated ? 'table-is-update' : 'table-is-not-update'"
            >
                <tbody>
                    <tr
                        class="hospital-advice-shandong-header-th"
                        :class="`${page.isPageUpdated ? 'is-not-update-advice-color' : 'is-new-advice-color'}`"
                    >
                        <td class="hospital-advice-shandong-one-time-start-time">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                年月日时分
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-content">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                医嘱内容
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                医师签名
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-stop-time">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                执行时间
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-sub-header-td-content">
                                护士签名
                            </div>
                        </td>
                    </tr>

                    <tr
                        v-for="item in tableContentTrList"
                        :key="`hospital-advice-shandong-table-advice-tr-${item}`"
                        class="hospital-advice-shandong-content-tr"
                        :class="`${page.list[item] && page.list[item].isAdviceNew ? 'is-update-advice-color' : page.list[item] && page.list[item].isContinueAdvice ? 'is-not-update-advice-color' : 'is-new-advice-color'}`"
                    >
                        <td class="hospital-advice-shandong-one-time-start-time">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item]">
                                    {{ page.list[item].startTime | parseTime('y-m-d h:i') }}
                                </template>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-content">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item]">
                                    <div
                                        v-if="isOnlyDisplayName(page.list[item].adviceRuleType)"
                                        class="hospital-advice-shandong-nurse-treatment-content"
                                    >
                                        <div class="hospital-advice-shandong-western-medicine-name">
                                            {{ page.list[item].name }} {{ getDischargeDateInfo(page.list[item]) }} {{ page.list[item].remark }}
                                        </div>

                                        <template
                                            v-if="showSingleDosage(page.list[item]) && page.list[item].singleDosageCount && parseInt(page.list[item].singleDosageCount) > 1 && page.list[item].singleDosageUnit"
                                        >
                                            <div class="hospital-advice-shandong-western-medicine-single-dosage">
                                                {{ page.list[item].singleDosageCount }}{{ transformAdviceUnit(page.list[item]) }}
                                            </div>

                                            <div
                                                class="hospital-advice-shandong-group-start"
                                                style="visibility: hidden;"
                                            ></div>

                                            <div
                                                class="hospital-advice-shandong-usage"
                                                style="visibility: hidden;"
                                            >
                                                占位符
                                            </div>

                                            <div
                                                class="hospital-advice-shandong-freq"
                                                style="visibility: hidden;"
                                            >
                                                占位符
                                            </div>
                                        </template>
                                    </div>

                                    <div
                                        v-else-if="isChineseMedicine(page.list[item].adviceRuleType)"
                                        class="hospital-advice-shandong-nurse-treatment-content"
                                    >
                                        <div class="hospital-advice-shandong-western-medicine-name">
                                            中药饮片 {{ page.list[item].dosageCount }}{{ page.list[item].dosageUnit }} {{ page.list[item].usage }} {{ page.list[item].remark }}
                                        </div>
                                        <div>
                                            {{ page.list[item].dailyDosage }}
                                        </div>
                                    </div>

                                    <div
                                        v-else-if="isWesternMedicine(page.list[item].adviceRuleType)"
                                        class="hospital-advice-shandong-western-medicine-content"
                                    >
                                        <div class="hospital-advice-shandong-western-medicine-name">
                                            {{ page.list[item].name }} {{ getAstInfo(page.list[item]) }} {{ page.list[item].remark }}
                                        </div>

                                        <div
                                            v-if="isDischargeMedicine(page.list[item].type, page.list[item].adviceRuleType)"
                                            class="hospital-advice-shandong-western-medicine-spec"
                                        >
                                            {{ getSpecAndDispenseCount(page.list[item]) }}
                                        </div>

                                        <div class="hospital-advice-shandong-western-medicine-single-dosage">
                                            {{ page.list[item].singleDosageCount }}{{ transformAdviceUnit(page.list[item]) }}
                                        </div>

                                        <div
                                            v-if="page.list[item].isStart"
                                            class="hospital-advice-shandong-group-start"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-group-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-group-color' : 'is-new-advice-group-color'}`"
                                        ></div>
                                        <div
                                            v-else-if="page.list[item].isMiddle"
                                            class="hospital-advice-shandong-group-middle"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-group-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-group-color' : 'is-new-advice-group-color'}`"
                                        ></div>
                                        <div
                                            v-else-if="page.list[item].isEnd"
                                            class="hospital-advice-shandong-group-end"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-group-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-group-color' : 'is-new-advice-group-color'}`"
                                        ></div>
                                        <div
                                            v-else
                                            class="hospital-advice-shandong-group-position"
                                        ></div>

                                        <div
                                            class="hospital-advice-shandong-usage"
                                            :style="page.list[item].isMiddle || page.list[item].isStart ? { color: 'transparent !important' } : {}"
                                        >
                                            {{ page.list[item].usage }}
                                        </div>

                                        <div
                                            class="hospital-advice-shandong-freq"
                                            :style="page.list[item].isMiddle || page.list[item].isStart ? { color: 'transparent !important' } : {}"
                                        >
                                            {{ page.list[item].freq }}
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item]">
                                    <template v-if="page.printConfig.startDoctor === 2">
                                        {{ page.list[item].createdByName }}
                                    </template>
                                    <template v-else-if="page.printConfig.startDoctor === 3">
                                        <img
                                            v-if="judgeImgUrl(page.list[item].createdByHandSign)"
                                            :src="page.list[item].createdByHandSign"
                                            alt=""
                                            class="hospital-advice-shandong-content-td-sign-img"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-img-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-img-color' : 'is-new-advice-img-color'}`"
                                        />
                                        <template v-else>
                                            {{ page.list[item].createdByName }}
                                        </template>
                                    </template>
                                </template>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-stop-time">
                            <div class="hospital-advice-shandong-content-td-content">
                                <span
                                    v-if="page.printConfig.oneTimeExecuteTime !== 0 && page.list[item] && page.list[item].status >= MedicalAdviceStatusEnum.EXECUTED"
                                    :class="`${page.list[item].adviceGroupInfo.newExecuteTime ? 'is-update-advice-color' : ''}`"
                                >
                                    {{ (page.list[item].adviceGroupInfo.newExecuteTime ? page.list[item].adviceGroupInfo.newExecuteTime : page.list[item].adviceGroupInfo.executeTime) | parseTime('m-d h:i') }}
                                </span>
                            </div>
                        </td>

                        <td class="hospital-advice-shandong-advice-sign">
                            <div class="hospital-advice-shandong-content-td-content">
                                <template v-if="page.list[item]">
                                    <span
                                        v-if="page.printConfig.executeNurse === 2"
                                        :class="`${page.list[item].adviceGroupInfo.newExecuteOperatorName ? 'is-update-advice-color' : ''}`"
                                    >
                                        {{
                                            page.list[item].adviceGroupInfo.newExecuteOperatorName ?
                                                page.list[item].adviceGroupInfo.newExecuteOperatorName :
                                                page.list[item].adviceGroupInfo.executeOperatorName
                                        }}
                                    </span>
                                    <template v-else-if="page.printConfig.executeNurse === 3">
                                        <img
                                            v-if="judgeImgUrl(page.list[item].adviceGroupInfo.newExecuteOperatorHandSign) || judgeImgUrl(page.list[item].adviceGroupInfo.executeOperatorHandSign)"
                                            :src="judgeImgUrl(page.list[item].adviceGroupInfo.newExecuteOperatorHandSign) ? page.list[item].adviceGroupInfo.newExecuteOperatorHandSign : page.list[item].adviceGroupInfo.executeOperatorHandSign"
                                            alt=""
                                            class="hospital-advice-shandong-content-td-sign-img"
                                            :class="`${page.list[item].isAdviceNew ? 'is-update-advice-img-color' : page.list[item].isContinueAdvice ? 'is-not-update-advice-img-color' : 'is-new-advice-img-color'} ${page.list[item].adviceGroupInfo.newExecuteOperatorHandSign ? 'is-update-advice-img-color' : ''}`"
                                        />
                                        <span
                                            v-else
                                            :class="`${page.list[item].adviceGroupInfo.newExecuteOperatorName ? 'is-update-advice-color' : ''}`"
                                        >
                                            {{
                                                page.list[item].adviceGroupInfo.newExecuteOperatorName ?
                                                    page.list[item].adviceGroupInfo.newExecuteOperatorName :
                                                    page.list[item].adviceGroupInfo.executeOperatorName
                                            }}
                                        </span>
                                    </template>
                                </template>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
			
            <div
                class="hospital-advice-shandong-footer"
                :class="`${page.isPageUpdated ? 'is-not-update-advice-color' : 'is-new-advice-color'}`"
            >
                第 {{ page.page + 1 }} 页
            </div>

            <div
                v-if="pageIndex < printPages.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import { PrintBusinessKeyEnum } from './constant/print-constant';
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import {
        AdviceRuleType,
        hospitalAdviceShandongPageLimit,
        MedicalAdviceStatusEnum,
        MedicalAdviceTypeEnum, TreatmentTypeEnum,
    } from './common/constants';
    import {deepClone, formatAge, formatTreatmentUnit, isImgUrl, parseTime} from './common/utils';
    import HospitalAdviceShandongHandler from './data-handler/hospital-advice-shandong-handler';

    export default {
        name: 'HospitalAdviceShandong',
        DataHandler: HospitalAdviceShandongHandler,
        businessKey: PrintBusinessKeyEnum.HOSPITAL_ADVICE_SHANDONG,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            }
        ],
        filters: {
            parseTime,
        },
        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
            extra: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                MedicalAdviceTypeEnum,
                hospitalAdviceShandongPageLimit,
                MedicalAdviceStatusEnum,
            };
        },
        computed: {
            tableContentTrList() {
                return Array(hospitalAdviceShandongPageLimit).fill().map((_, index) => index);
            },
            printData() {
                return this.renderData.printData || {};
            },
            printPages() {
                console.log('%c renderData\n', 'background: green; padding: 0 5px', deepClone(this.renderData));
                return this.renderData.printPages || {};
            },
            clinicName() {
                return this.printData.clinicName || '';
            },
            isLongTimeAdvice() {
                return this.printData.adviceType === MedicalAdviceTypeEnum.LONG_TIME;
            },
            typeStr() {
                return this.isLongTimeAdvice ? '长期医嘱单' : '临时医嘱单';
            },
            patientHospitalInfo() {
                return this.printData.patientHospitalInfo || {};
            },
            patient() {
                return this.patientHospitalInfo.patient || {};
            },
        },
        methods: {
            formatAge,
            isImgUrl,
            transformAdviceUnit(advice) {
                const { adviceRuleType, singleDosageUnit } = advice;
                return [
                    AdviceRuleType.TREATMENT,
                    AdviceRuleType.NURSE,
                    AdviceRuleType.INSPECTION,
                    AdviceRuleType.ASSAY,
                    AdviceRuleType.PHYSIOTHERAPY,
                    AdviceRuleType.SURGERY,
                ].includes(adviceRuleType)
                    ? singleDosageUnit ? formatTreatmentUnit(singleDosageUnit, '*') : '次'
                    : singleDosageUnit
            },
            judgeImgUrl(url) {
                if (!url) return false;
                return isImgUrl(url);
            },
            isNurseOrTreatment(adviceRuleType) {
                return [AdviceRuleType.NURSE, AdviceRuleType.NURSE_LEVEL, AdviceRuleType.TREATMENT].includes(adviceRuleType);
            },
            isWesternMedicine(adviceRuleType) {
                return adviceRuleType === AdviceRuleType.WESTERN_MEDICINE;
            },
            isChineseMedicine(adviceRuleType) {
                return [AdviceRuleType.CHINESE_MEDICINE_GRANULES, AdviceRuleType.CHINESE_MEDICINE_TABLETS].includes(adviceRuleType);
            },
            isOnlyDisplayName(adviceRuleType) {
                return [
                    AdviceRuleType.MEDICINE_MATERIAL,
                    AdviceRuleType.SELF_PRODUCT,
                    AdviceRuleType.HEALTH_MEDICINE,
                    AdviceRuleType.HEALTH_FOOD,
                    AdviceRuleType.OTHER_PRODUCT,
                    AdviceRuleType.INSPECTION,
                    AdviceRuleType.ASSAY,
                    AdviceRuleType.NURSE,
                    AdviceRuleType.NURSE_LEVEL,
                    AdviceRuleType.TREATMENT,
                    AdviceRuleType.PHYSIOTHERAPY,
                    AdviceRuleType.CONSULTATION,
                    AdviceRuleType.SURGERY,
                    AdviceRuleType.POSTOPERATIVE,
                    AdviceRuleType.TRANSFER_DEPARTMENT,
                    AdviceRuleType.DISCHARGE_WITH_MEDICINE,
                    AdviceRuleType.TRANSFER_WITH_MEDICINE,
                ].includes(adviceRuleType);
            },
            showSingleDosage(advice) {
                const { adviceRuleType, isNeedHospitalExecute } = advice;
                return [
                    AdviceRuleType.TREATMENT,
                    AdviceRuleType.NURSE,
                ].includes(adviceRuleType) && isNeedHospitalExecute;
            },
            // 出院带药 + 西成药
            isDischargeMedicine(type, adviceRuleType) {
                return type === MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE && adviceRuleType === AdviceRuleType.WESTERN_MEDICINE;
            },
            getSpecAndDispenseCount(advice) {
                let info = '';
                if (advice.dosageCount && advice.dosageUnit) {
                    info = `${advice.dosageCount}${advice.dosageUnit}`;
                }
                const { adviceGoodsItems } = advice;
                const item = (adviceGoodsItems || []).find((it) => it.type === 0);
                if (item) {
                    const { goodsSpec } = item;
                    if (goodsSpec) {
                        info = `${goodsSpec}*${info}`;
                    }
                }
                return info;
            },
            getAstInfo(advice) {
                let info = '';
                if (advice.astFlag === 1) {
                    info += '皮试';

                    if (advice.astResult) {
                        const { result } = advice.astResult;
                        info = `${info}(${result === '阳性' ? '+' : '-'})`;
                    } else {
                        info += '()';
                    }
                }
                return info;
            },
            getDischargeDateInfo(advice) {
                if (advice.adviceGroupInfo && advice.adviceGroupInfo.diagnosisTreatmentType === TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE) {
                    if (advice.name === '死亡出院' && advice.deathTime) {
                        return `死亡时间：${parseTime(advice.deathTime, 'y-m-d', true)}`;
                    }
                    if (advice.dischargeHospitalTime) {
                        return `出院时间：${parseTime(advice.dischargeHospitalTime, 'y-m-d', true)} 出院原因：${advice.dischargeHospitalReason}`;
                    }
                }
                return '';
            },
        },
    };
</script>

<style lang="scss">
.hospital-advice-shandong-wrapper {
    box-sizing: border-box;
    font-family: SimSun, "宋体", STSong, "华文宋体", serif !important;

    * {
        box-sizing: border-box;
        font-family: SimSun, "宋体", STSong, "华文宋体", serif !important;
    }

    .is-not-update-advice-color {
        color: transparent;
    }

    .is-not-update-advice-img-color {
        border-bottom: 2px solid transparent !important;
        opacity: 0;
    }

    .is-not-update-advice-group-color {
        border-color: transparent;
    }

    .is-update-advice-color {
        color: black;
    }

    .is-update-advice-img-color {
        border-bottom: 2px solid transparent !important;
        opacity: 1;
    }

    .is-update-advice-group-color {
        border-color: black;
    }

    .is-new-advice-color {
        color: black;
    }

    .is-new-advice-img-color {
        border-bottom: 2px solid transparent !important;
        opacity: 1;
    }

    .is-new-advice-group-color {
        border-color: black;
    }

    .hospital-advice-shandong-header-wrapper {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding-bottom: 4px;
    }

    .hospital-advice-shandong-title {
        display: flex;
        flex-direction: column;
        gap: 2px;
        align-items: center;
        width: 100%;
        font-family: 'SimSun', 'NSimSun', 'FangSong', 'STSong', 'STFangsong', sans-serif;
    }

    .hospital-advice-shandong-custom-title {
        display: flex;
        flex-direction: column;
        gap: 2px;
        align-items: center;
        width: 100%;
        font-size: 19px;
        line-height: 27px;
    }

    .hospital-advice-shandong-type {
        width: 100%;
        font-size: 21px;
        font-weight: bold;
        line-height: 29px;
        text-align: center;
    }

    .hospital-advice-shandong-patient-info {
        display: flex;
        align-items: flex-start;
        width: 100%;
        height: 26px;
        font-size: 13px;
        line-height: 13px;

        div {
            width: 22%;
        }
    }

    .hospital-advice-shandong-table {
        width: 100%;
        font-size: 13px;
        line-height: 17px;
        border-collapse: collapse;

        td {
            height: inherit;
            padding: 0 6px;
        }

        &.table-is-not-update {
            border: 1px solid #000000;

            td,
            th {
                border: 1px solid #000000;
            }
        }

        &.table-is-update {
            border: 1px solid transparent;

            td,
            th {
                border: 1px solid transparent;
            }
        }

        .hospital-advice-shandong-header-th {
            height: 24px;
            max-height: 24px;
        }

        .hospital-advice-shandong-header-tr {
            height: 34px;
            max-height: 34px;
        }

        .hospital-advice-shandong-header-td-content {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            font-weight: bold;
        }

        .hospital-advice-shandong-sub-header-td-content {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            font-weight: bold;
        }

        .hospital-advice-shandong-content-tr {
            height: 34px;
            max-height: 34px;
        }

        .hospital-advice-shandong-content-td-content {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
            word-break: break-all;
            word-wrap: break-word;
            white-space: wrap;
        }

        .hospital-advice-shandong-content-td-sign-img {
            width: 100%;
            height: calc(100% - 8px);
            object-fit: contain;
        }

        .hospital-advice-shandong-start-time {
            width: 92px;
        }

        .hospital-advice-shandong-one-time-start-time {
            width: 130px;
        }

        .hospital-advice-shandong-advice-sign {
            width: 70px;
        }

        .hospital-advice-shandong-stop-time {
            width: 90px;
        }

        .hospital-advice-shandong-nurse-treatment-content {
            position: relative;
            display: flex;
            gap: 6px;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .hospital-advice-shandong-western-medicine-content {
            position: relative;
            display: flex;
            gap: 6px;
            align-items: center;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .hospital-advice-shandong-western-medicine-name {
            flex: 1;
            width: 0;
            max-height: 34px;
            overflow: hidden;
            word-break: break-all;
            word-wrap: break-word;
            white-space: wrap;
        }

        .hospital-advice-shandong-western-medicine-spec {
            width: 62px;
            min-width: 62px;
            max-width: 62px;
            max-height: 34px;
            overflow: hidden;
            text-align: right;
            word-break: break-all;
            word-wrap: break-word;
            white-space: wrap;
        }

        .hospital-advice-shandong-western-medicine-single-dosage {
            width: 54px;
            min-width: 54px;
            max-width: 54px;
            max-height: 34px;
            overflow: hidden;
            text-align: right;
            word-break: break-all;
            word-wrap: break-word;
            white-space: wrap;
        }

        .hospital-advice-shandong-group-start {
            width: 4px;
            height: 17px;
            margin-top: 17px;
            border: 1px solid #000000;
            border-bottom: none;
            border-left: none;
            border-top-right-radius: 4px;
        }

        .hospital-advice-shandong-group-middle {
            width: 4px;
            height: 34px;
            border-right: 1px solid #000000;
        }

        .hospital-advice-shandong-group-end {
            width: 4px;
            height: 17px;
            margin-bottom: 17px;
            border: 1px solid #000000;
            border-top: none;
            border-left: none;
            border-bottom-right-radius: 4px;
        }

        .hospital-advice-shandong-group-position {
            width: 4px;
            height: 34px;
        }

        .hospital-advice-shandong-usage {
            width: 28px;
            min-width: 28px;
            max-width: 28px;
            max-height: 34px;
            overflow: hidden;
            text-align: right;
            word-break: break-all;
            word-wrap: break-word;
            white-space: wrap;
        }

        .hospital-advice-shandong-freq {
            width: 32px;
            min-width: 32px;
            max-width: 32px;
            max-height: 34px;
            overflow: hidden;
            text-align: right;
            word-break: break-all;
            word-wrap: break-word;
            white-space: wrap;
        }
    }

    .hospital-advice-shandong-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        font-family: 'SimSun', 'NSimSun', 'FangSong', 'STSong', 'STFangsong', sans-serif;
        font-size: 13px;
        line-height: 13px;
        text-align: center;
    }
}

.abc-page_print {
    .hospital-advice-shandong-wrapper {
        .is-not-update-advice-color {
            color: transparent !important;
        }

        .is-not-update-advice-img-color {
            border-bottom: 2px solid transparent !important;
            opacity: 0 !important;
        }

        .is-not-update-advice-group-color {
            border-color: transparent !important;
        }

        .is-update-advice-color {
            color: black !important;
        }

        .is-update-advice-img-color {
            border-bottom: 2px solid transparent !important;
            opacity: 1 !important;
        }

        .is-update-advice-group-color {
            border-color: black !important;
        }

        .is-new-advice-color {
            color: black !important;
        }

        .is-new-advice-img-color {
            border-bottom: 2px solid transparent !important;
            opacity: 1 !important;
        }

        .is-new-advice-group-color {
            border-color: black !important;
        }
    }
}

.abc-page_preview {
    .hospital-advice-shandong-wrapper {
        .is-not-update-advice-color {
            color: black !important;
        }

        .is-not-update-advice-img-color {
            border-bottom: 2px solid transparent !important;
            opacity: 1 !important;
        }

        .is-not-update-advice-group-color {
            border-color: black !important;
        }

        .is-update-advice-color {
            color: red !important;
        }

        .is-update-advice-img-color {
            border-bottom: 2px solid red !important;
            opacity: 1 !important;
        }

        .is-update-advice-group-color {
            border-color: red !important;
        }
    }

    .hospital-advice-shandong-table {
        &.table-is-not-update {
            border: 1px solid #000000 !important;

            td,
            th {
                border: 1px solid #000000 !important;
            }
        }

        &.table-is-update {
            border: 1px solid #000000 !important;

            td,
            th {
                border: 1px solid #000000 !important;
            }
        }
    }
}
</style>