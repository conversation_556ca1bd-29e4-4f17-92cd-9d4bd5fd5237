<template>
    <div>
        <template v-for="(page, pageIndex) in renderPage">
            <div
                :key="`medical-bill-taiyuan-page-${pageIndex}`"
                class="medical-bill-taiyuan-wrapper"
            >
                <block-box
                    v-if="blueInvoiceData"
                    :left="23"
                    :top="9"
                >
                    销项负数 对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                </block-box>

                <!-- 业务流水号 -->
                <block-box
                    :left="31"
                    :top="21"
                >
                    {{ printData.serialNo }}
                </block-box>

                <!-- 医疗类别 -->
                <block-box
                    :left="59"
                    :top="21"
                >
                    {{ shebaoPayment.medType }}
                </block-box>

                <!-- 姓名 -->
                <block-box
                    :left="24"
                    :top="26.5"
                >
                    {{ patient.name }}
                </block-box>

                <!-- 性别 -->
                <block-box
                    :left="52"
                    :top="26.5"
                >
                    {{ patient.sex }}
                </block-box>

                <!-- 医保类型-->
                <block-box
                    :left="77"
                    :top="26.5"
                >
                    {{ shebaoPayment.cardOwnerType }}
                </block-box>

                <!-- 社会保障号码-->
                <block-box
                    :left="36"
                    :top="32.5"
                >
                    {{ printData.healthCardNo }}
                </block-box>

                <block-box
                    :left="16"
                    :top="44"
                    :font="8"
                    class="medical-bill-taiyuan-item-wrapper"
                >
                    <div
                        v-for="(item, idx) in page.formItems"
                        :key="`medical-bill-taiyuan-item-${pageIndex}-${idx}`"
                    >
                        <div
                            class="medical-bill-taiyuan-item"
                            style="width: 28%; overflow: hidden; white-space: nowrap;"
                            overflow
                        >
                            {{ item.name }}
                        </div>
                        <div
                            class="medical-bill-taiyuan-item medical-bill-taiyuan-item-right"
                            style="width: 11%;"
                        >
                            {{ item.count }}
                        </div>
                        <div
                            class="medical-bill-taiyuan-item medical-bill-taiyuan-item-right"
                            style="width: 19%;"
                        >
                            {{ item.discountedPrice | formatMoney }}
                        </div>
                        <div
                            class="medical-bill-taiyuan-item medical-bill-taiyuan-item-right"
                            style="width: 28%;"
                        >
                            {{ handleOwnExpensePrice(item) | formatMoney }}
                        </div>
                    </div>
                </block-box>

                <!-- 合计(大写) -->
                <block-box
                    :left="32"
                    :top="118.5"
                >
                    {{ digitUppercase(finalFee) }}
                </block-box>

                <!-- 合计 -->
                <block-box
                    :left="72"
                    :top="118.5"
                >
                    {{ finalFee | formatMoney }}
                </block-box>

                <!-- 支付方式 -->
                <block-box
                    :left="100"
                    :top="118.5"
                >
                    {{ payModeType }}
                </block-box>

                <!-- 医保统筹支付 -->
                <block-box
                    :left="35"
                    :top="125"
                >
                    {{ shebaoPayment.fundPaymentFee | formatMoney }}
                </block-box>

                <!-- 个人账户支付 -->
                <block-box
                    :left="68"
                    :top="125"
                >
                    {{ shebaoPayment.accountPaymentFee | formatMoney }}
                </block-box>

                <!-- 其他医保支付 -->
                <block-box
                    :left="100"
                    :top="125"
                >
                    {{ shebaoPayment.otherPaymentFee | formatMoney }}
                </block-box>

                <!-- 个人支付金额 -->
                <block-box
                    :left="35"
                    :top="130.5"
                >
                    {{ printData.personalPaymentFee | formatMoney }}
                </block-box>

                <!-- 收款单位 -->
                <block-box
                    :left="33"
                    :top="135.5"
                    style="max-width: 19mm; word-wrap: break-word; word-break: break-all;"
                >
                    {{ currentConfig.institutionName }}
                </block-box>

                <!-- 收款人 -->
                <block-box
                    :left="68"
                    :top="135.5"
                >
                    {{ printData.chargedByName }}
                </block-box>

                <!-- 收款年份 -->
                <block-box
                    :left="82"
                    :top="136"
                >
                    {{ year }}
                </block-box>

                <!-- 收款月份 -->
                <block-box
                    :left="95"
                    :top="136"
                >
                    {{ month }}
                </block-box>

                <!-- 收款日期 -->
                <block-box
                    :left="104"
                    :top="136"
                >
                    {{ day }}
                </block-box>

                <div
                    v-if="hasOverPageTip"
                    class="medical-bill-taiyuan-next-page-tips"
                >
                    *** 因纸张限制，部分项目未打印 ***
                </div>
                <div
                    v-else-if="pageIndex < pagesData.length - 1"
                    class="medical-bill-taiyuan-next-page-tips"
                >
                    *** 接下页 ***
                </div>
            </div>

            <div
                v-if="pageIndex < renderPage.length - 1"
                :key="`medical-bill-taiyuan-new-page-${pageIndex}`"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import BillDataMixins from './mixins/bill-data';
    import NationalBillData from './mixins/national-bill-data';
    import CommonHandler from './data-handler/common-handler';
    import { PrintBusinessKeyEnum } from './constant/print-constant';
    import PageSizeMap, { Orientation } from '../share/page-size.js';
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';

    export default {
        name: 'MedicalBillTaiyuan',
        components: { BlockBox },
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_TAIYUAN,
        pages: [
            {
                paper: PageSizeMap.MM129_200_TAIYUAN,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        mixins: [BillDataMixins, NationalBillData],
        computed: {
        },
    }
</script>

<style lang="scss">
.medical-bill-taiyuan-wrapper {
    width: 129mm;
    height: 200mm;
    position: relative;
    font-size: 9pt !important;

    * {
        font-size: 9pt !important;
    }

    .medical-bill-taiyuan-item-wrapper {
        width: 96mm;
        height: 74mm;
    }

    .medical-bill-taiyuan-item {
        display: inline-block;
        vertical-align: top;
    }

    .medical-bill-taiyuan-item-right {
        text-align: right;
    }

    .medical-bill-taiyuan-next-page-tips {
        position: absolute;
        width: 100%;
        top: 114mm;
        text-align: center;
    }
}

.abc-page_preview {
    background-image: url("/static/assets/print/taiyuan.png");
    background-size: 100% 100%;
    color: #2a82e4;
}
</style>
