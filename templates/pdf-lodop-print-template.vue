<template>
    <div class="pdf-lodop-print-template-wrapper">
    </div>
</template>

<script>
    import { PrintBusinessKeyEnum } from "./constant/print-constant";
    import CommonHandler from "./data-handler/common-handler";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { canvasToImage } from './common/utils';

    export default {
        name: "PdfLodopPrintTemplate",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.PDF_LODOP_PRINT_TEMPLATE,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            pdfPageList() {
                return this.printData.pdfPageList || [];
            },
        },
        mounted() {
            const pageInfo = this.$options.propsData.options.page;
            let { width, height } = pageInfo;
            const { orientation, size } = pageInfo;
            // 如果内置的 A4、A5 未获取到长宽, 则特殊处理
            if (!width && !height) {
                if (size === 'A4') {
                    width = 210;
                    height = 297;
                } else if (size === 'A5') {
                    width = 148;
                    height = 210;
                }
            }
            const imageWidth = orientation === 1 ? width : height
            const wrapperEl = this.$el;
            this.pdfPageList.forEach((pdfPage, index) => {
                const canvasImg = canvasToImage(pdfPage.canvas, imageWidth);
                wrapperEl.appendChild(canvasImg);
                if (index < this.pdfPageList.length - 1) {
                    const newPageDivEl = document.createElement("div");
                    newPageDivEl.setAttribute("data-type", 'new-page');
                    wrapperEl.appendChild(newPageDivEl);
                }
            });
        },
    }
</script>
