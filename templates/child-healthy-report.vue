<!--exampleData

{
    "patientId": null,
    "outpatientSheetId": "ffffffff000000001cdd596010bac000",
    "departmentName": "内科",
    "doctorName": "谭海灯",
    "patient": {
      "id": "ffffffff000000001cdd59180f4e2000",
      "name": "几个月",
      "namePy": null,
      "namePyFirst": null,
      "birthday": "2021-10-27",
      "mobile": "",
      "sex": "男",
      "idCard": null,
      "isMember": 0,
      "age": {
        "year": 0,
        "month": 1,
        "day": 4
      },
      "address": null,
      "sn": null,
      "remark": null,
      "profession": null,
      "company": null,
      "patientSource": null,
      "tags": null,
      "marital": null,
      "weight": null,
      "wxOpenId": null,
      "wxHeadImgUrl": null,
      "wxNickName": null,
      "wxBindStatus": null,
      "isAttention": null,
      "shebaoCardInfo": null,
      "childCareInfo": null,
      "chronicArchivesInfo": null
    },
    "healthReportRecord": null,
    "bodyGrowthRecordViews": {
      "heightAndWeightViews": [],
      "heightAndAgeViews": [],
      "weightAndAgeViews": [],
      "headSizeAndAgeViews": [],
      "bmiViews": []
    },
    "physicalExamination": "体格检查正常",
    "suggestion": {
      "inoculation": "0-1岁的小朋友需要按时注射以下疫苗，按时接种，有效防御 0月：乙肝疫苗；卡介苗；<br>1月：乙肝疫苗；<br>2月：脊灰疫苗；<br>3月：脊灰疫苗；百白破疫苗；<br>4月：脊灰疫苗；百白破疫苗；<br>5月：百白破疫苗；<br>6月：乙肝疫苗；流脑疫苗；<br>8月：麻疹疫苗；<br>9月：流脑疫苗",
      "diet": "以母乳为主的乳制品摄入，可食用母乳，牛乳，羊乳，以及安全合规的奶粉进行喂养，根据情况可进行混合喂养。",
      "sleep": "正常情况下，1-3个月的宝宝睡眠时间约为16个小时。白天，宝宝的每次睡眠时长约1-2小时，白天约需要睡4次；夜晚，宝宝已经可以坚持睡10-11个小时了。",
      "mouth": "出牙前（6个月前）快要出牙的宝宝大多会流口水，清清黏黏的口水擦了又流，该阶段可通过：牙胶、护齿湿巾进行护理"
    },
    "evaluationResults": null,
    "created": "2021-12-01T06:53:08Z"

  }

-->

<template>
    <div class="print-child-health-wrapper">
        <h6 class="title">
            儿童健康管理测评报告
        </h6>
        <div class="patient-wrapper">
            <div class="patient-item">
                <div>姓名：{{ patient.name }}</div>
                <div>年龄：{{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}</div>
                <div>手机：{{ patient.mobile }}</div>
            </div>
            <div class="patient-item">
                <div>体检日期：{{ printData.created | parseTime('y-m-d h:i:s') }}</div>
            </div>
        </div>

        <table
            style="margin-top: 24px;"
            class="care-table"
            data-type="mix-box"
        >
            <tbody data-type="group">
                <tr data-type="item">
                    <td colspan="1">
                        身体概况
                    </td>
                    <td colspan="6">
                        <span
                            v-if="printData.healthReportRecord && printData.healthReportRecord.conclusion"
                            v-html="printData.healthReportRecord.conclusion"
                        ></span>
                    </td>
                </tr>
            </tbody>
        </table>

        <table
            class="care-table"
            data-type="mix-box"
        >
            <tbody data-type="group">
              <tr>
                <td colspan="1">
                  健康建议
                </td>
                <td
                    colspan="6"
                >
                  <span data-type="item" v-for="item in healthReportRecordAdvice">
                    {{ item }}
                    <br>
                  </span>
                </td>
            </tr>
            </tbody>
        </table>

        <section
            class="print-result-item"
        >
            <h3>体格检查</h3>
            <table class="care-table">
                <tbody>
                    <tr>
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            体格检查
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.physicalExamination"
                        >
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>

        <section
            class="print-result-item print-care-record"
        >
            <h3>看护情况</h3>
        </section>

        <section
            class="print-result-item print-care-record"
            data-type="mix-box"
        >
            <table class="care-table">
                <tbody data-type="group">
                    <tr v-if="printData.suggestion && printData.suggestion.inoculation" data-type="item">
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            接种
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.inoculation"
                        >
                        </td>
                    </tr>
                    <tr v-if="printData.suggestion && printData.suggestion.diet" data-type="item">
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            饮食
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.diet"
                        >
                        </td>
                    </tr>
                    <tr v-if="printData.suggestion && printData.suggestion.sleep" data-type="item">
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            睡眠
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.sleep"
                        >
                        </td>
                    </tr>
                    <tr v-if="printData.suggestion && printData.suggestion.mouth" data-type="item">
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            口腔护理
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.mouth"
                        >
                        </td>
                    </tr>
                    <tr v-if="printData.suggestion && printData.suggestion.growthPromotion" data-type="item">
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            发育促进
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.growthPromotion"
                        >
                        </td>
                    </tr>
                    <tr
                        v-if="printData.suggestion && printData.suggestion.diseasePrevention"
                        data-type="item"
                    >
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            疾病预防
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.diseasePrevention"
                        >
                        </td>
                    </tr>
                    <tr
                        v-if="printData.suggestion && printData.suggestion.accidentalDamage"
                        data-type="item"
                    >
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            预防意外伤害
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.accidentalDamage"
                        >
                        </td>
                    </tr>
                    <tr
                        v-if="printData.suggestion && printData.suggestion.eye"
                        data-type="item"
                    >
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            眼和视力保健
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.eye"
                        >
                        </td>
                    </tr>
                    <tr
                        v-if="printData.suggestion && printData.suggestion.ear"
                        data-type="item"
                    >
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            听力保健
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.ear"
                        >
                        </td>
                    </tr>
                    <tr
                        v-if="printData.suggestion && printData.suggestion.otherAdvice"
                        data-type="item"
                    >
                        <td
                            colspan="1"
                            class="label-td"
                        >
                            其他补充建议
                        </td>
                        <td
                            colspan="6"
                            v-html="printData.suggestion.otherAdvice"
                        >
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>

        <div
            v-if="printData.evaluationResults && printData.evaluationResults.length"
            data-type="new-page"
        ></div>

        <template v-for="result in printData.evaluationResults || []">
            <section
                :key="result.id"
                class="print-result-item"
            >
                <h3 v-if="result.formAbstract && result.formAbstract.name">
                    {{ result.formAbstract.name }}
                </h3>
            </section>
            <section
                :key="result.id"
                data-type="mix-box"
                class="print-result-item"
            >
                <question-result :data="result"></question-result>
            </section>
        </template>

        <section
            v-if="isChild"
            class="print-result-item"
        >
            <template>
                <abc-growth-chart
                    style="text-align: center;"
                    :type="`bmi-${echartSexType}`"
                    :series="BMISeries"
                    :width="svgWidth"
                    :height="svgHeight"
                ></abc-growth-chart>
            </template>
        </section>

        <section class="print-result-item">
            <template>
                <abc-growth-chart
                    style="text-align: center;"
                    :type="`lw-${echartAgeType}-${echartSexType}-percent`"
                    :series="HWSeries"
                    :width="svgWidth"
                    :height="svgHeight"
                ></abc-growth-chart>
            </template>
        </section>

        <section
            v-if="!isChild"
            class="print-result-item"
        >
            <template>
                <abc-growth-chart
                    style="text-align: center;"
                    :type="`hlw-${echartSexType}`"
                    :series="HLWSeries"
                    :width="svgWidth"
                    :height="svgHeight"
                ></abc-growth-chart>
            </template>
        </section>

        <section class="print-result-item">
            <template>
                <abc-growth-chart
                    style="text-align: center;"
                    :type="`lw-${echartAgeType}-${echartSexType}-std`"
                    :series="HWSeries"
                    :width="svgWidth"
                    :height="svgHeight"
                ></abc-growth-chart>
            </template>
        </section>
    </div>
</template>

<script>
    import QuestionResult from './components/question-result/index.vue';
    import {formatAge, parseTime} from "./common/utils.js";
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import PrintHandler from './data-handler/common-handler.js';

    export default {
        name: 'PrintHealthReport',
        DataHandler: PrintHandler,
        businessKey: PrintBusinessKeyEnum.CHILD_HEALTHY_REPORT,
        filters: {
            parseTime
        },
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        components: {
            QuestionResult
        },
        props: {
            renderData: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                formatAge,
                svgWidth: 520,
                svgHeight: 720
            };
        },
        computed: {
            healthReportRecordAdvice() {
                return this.printData.healthReportRecord?.advice?.split('<br>') || [];
            },
            printData() {
                return this.renderData.printData || {};
            },
            patient() {
                return this.printData.patient || {};
            },
            echartSexType() {
                return this.patient.sex === '男' ? 'boy' : 'girl';
            },
            echartAgeType() {
                return this.isChild ? '2-18' : '0-3';
            },
            isChild() {
                return this.patient.age && this.patient.age.year >= 3;
            },

            /**
             * @desc 身长体重百分位曲线图
             * <AUTHOR>
             * @date 2020-06-11 15:02:48
             */
            HWSeries() {
                const age = this.isChild ? 12 : 1;

                const heightAgeSeriesData = [];
                const weightAgeSeriesData = [];

                this.printData.bodyGrowthRecordViews.heightAndAgeViews.forEach((item) => {
                    if (!this.isChild && item.ageMonths < 36) {
                        heightAgeSeriesData.push(item);
                    }
                    if (this.isChild && item.ageMonths >= 24) {
                        heightAgeSeriesData.push(item);
                    }
                });
                this.printData.bodyGrowthRecordViews.weightAndAgeViews.forEach((item) => {
                    if (!this.isChild && item.ageMonths < 36) {
                        weightAgeSeriesData.push(item);
                    }
                    if (this.isChild && item.ageMonths >= 24) {
                        weightAgeSeriesData.push(item);
                    }
                });

                const heightAges = heightAgeSeriesData.map((item) => {
                    return [item.ageMonths / age, item.height];
                });

                const weightAges = weightAgeSeriesData.map((item) => {
                    return [item.ageMonths / age, item.weight];
                });

                return [
                    {
                        name: '年龄-体重',
                        color: '#0EBA52',
                        width: 2,
                        xLabel: '年龄',
                        xUnit: '月',
                        yLabel: '体重',
                        yUnit: 'kg',
                        showPoint: true,
                        pointColor: '#0EBA52',
                        pointHoverColor: '#ff9933',
                        data: weightAges,
                    },
                    {
                        name: '年龄-身长',
                        color: '#0EBA52',
                        width: 2,
                        xLabel: '年龄',
                        xUnit: '月',
                        yLabel: '身长',
                        yUnit: 'cm',
                        showPoint: true,
                        data: heightAges,
                    },
                ];
            },
            /**
             * @desc 身长体重百分位曲线图
             * <AUTHOR>
             * @date 2020-06-11 15:02:48
             */
            HLWSeries() {
                const headSizeAges = this.printData.bodyGrowthRecordViews.headSizeAndAgeViews
                    .filter((item) => {
                        return item.ageMonths < 36 && item.headSize > 0;
                    })
                    .map((item) => {
                        return [item.ageMonths, item.headSize];
                    });
                const heightWeight = this.printData.bodyGrowthRecordViews.heightAndWeightViews.map((item) => {
                    return [item.height, item.weight];
                });
                return [
                    {
                        name: '身长-体重',
                        color: '#0EBA52',
                        width: 2,
                        xLabel: '身长',
                        xUnit: 'cm',
                        yLabel: '体重',
                        yUnit: 'kg',
                        showPoint: true,
                        data: heightWeight,
                    },
                    {
                        name: '头围-年龄',
                        color: '#0EBA52',
                        width: 2,
                        xLabel: '年龄',
                        xUnit: '月',
                        yLabel: '头围',
                        yUnit: 'cm',
                        showPoint: true,
                        pointColor: '#0EBA52',
                        pointHoverColor: '#ff9933',
                        data: headSizeAges,
                    },
                ];
            },

            BMISeries() {
                const BMISeries = this.printData.bodyGrowthRecordViews.bmiViews
                    .filter((item) => {
                        return item.ageMonths >= 24;
                    })
                    .map((item) => {
                        return [item.ageMonths / 12, item.bmi];
                    });
                return [
                    {
                        name: 'BMI-年龄',
                        color: '#0EBA52',
                        width: 2,
                        xLabel: '年龄',
                        xUnit: '岁',
                        yLabel: 'BMI',
                        yUnit: 'cm',
                        showPoint: true,
                        pointColor: '#0EBA52',
                        pointHoverColor: '#ff9933',
                        data: BMISeries,
                    },
                ];
            },
        },
    };
</script>

<style lang="scss">

@import "./style/reset.scss";

.abc-page-content {
  box-sizing: border-box;
  padding: 8pt;
  font-family: "Microsoft YaHei", "微软雅黑";
}

.title {
  margin-bottom: 12pt;
  font-size: 16pt;
  font-weight: bolder;
  text-align: center;
}

.patient-wrapper {
  padding: 4pt;
  margin-top: 8pt;
  border-bottom: 1px solid #000000;

  * {
    font-size: 10pt;
  }

  .patient-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6pt 0;

    & > div {
      flex: 1;
    }
  }
}

.print-result-item {
  padding: 12pt 0 0;

  > h3 {
    margin-bottom: 12pt;
    font-size: 13pt;
    font-weight: bolder;
  }

  .chart-wrapper {
    display: flex;
    align-items: center;
  }
}

.card-quota {
  margin-bottom: 14pt;
  border-bottom: 1px dashed #000000;

  &:last-child {
    border-bottom: none;
  }

  &-name {
    height: 14pt;
    margin-bottom: 8pt;
    font-size: 10pt;
    font-weight: bold;
    line-height: 14pt;
  }

  &-level {
    height: 14pt;
    margin-bottom: 2pt;
    font-size: 10pt;
    line-height: 14pt;
  }

  &-description {
    margin-top: 4px;
    font-size: 14px;
    line-height: 20px;
  }
}

.care-table {
  width: 100%;
  table-layout: fixed;

  .label-td {
    text-align: center;
    vertical-align: middle;
  }

  td,
  th {
    padding: 4pt;
    font-size: 10pt;
    border: 1px solid #000000;
  }
}

</style>
