<!--exampleData
{

}
-->
<template>
    <div class="hospital-deposit-receipt">
        <div
            data-type="header"
            class="header-wrapper"
        >
            <div
                v-if="config.logo && printData.logo"
                class="hospital-deposit-receipt-logo-wrapper"
            >
                <img
                    :src="printData.logo"
                    alt=""
                    class="hospital-deposit-receipt-header-logo"
                />
            </div>
            <div class="clinic-name">
                <template v-if="config.title">
                    {{ config.title }}
                </template>
                <template v-else>
                    {{ printData.clinicName || '' }}
                </template>
            </div>
            <div class="form-name">
                住院预交款收据
            </div>
            <div
                v-if="config.qrCode && printData.qrCode"
                class="hospital-deposit-receipt-qr-code-wrapper"
            >
                <img
                    :src="printData.qrCode"
                    alt=""
                    class="hospital-deposit-receipt-qr-code-img"
                />
            </div>
        </div>
        <div class="deposit-table">
            <table>
                <tbody>
                    <tr>
                        <td class="spread-td">
                            姓名
                        </td>
                        <td class="spread-td">
                            {{ printData.patientName }}
                        </td>
                        <td class="spread-td">
                            住院号
                        </td>
                        <td class="spread-td">
                            {{ printData.hospitalNo }}
                        </td>
                        <td class="spread-td">
                            科室
                        </td>
                        <td class="spread-td">
                            {{ printData.departmentName }}
                        </td>
                    </tr>
                    <tr>
                        <td colspan="6">
                            <span class="flex-item">
                                <span>本次缴费： <template v-if="isRefund">(退费)</template>{{ digitUppercase(amount) }}</span><span v-if="amount"><template v-if="isRefund">退</template>{{ $t('currencySymbol') }}{{ amount | formatMoney }} <template v-if="payModeDisplayName">（{{ payModePrepDisplayName }}：{{ payModeDisplayName }}）</template></span>
                            </span>
                            <span class="flex-item">
                                <span>累计缴费： {{ digitUppercase(totalAmount) }} </span><span v-if="totalAmount">{{ $t('currencySymbol') }}{{ totalAmount | formatMoney }} </span>
                            </span>
                            <span class="flex-item">
                                <span>缴费后余额： {{ digitUppercase(availableAmount) }} </span>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="6">
                            {{ config.remark }}
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="table-footer">
                <span v-if="config.chargeName">收费员：{{ createdByName }}</span>
                <span v-if="config.chargeTime">日期：{{ new Date() | parseTime('y-m-d h:i', true) }}</span>
                <span :class="config.institution ? '' : 'charge-clinic'">收费单位：{{ config.institution }}</span>
            </div>
        </div>
    </div>
</template>

<script>
    import CommonHandler from "./data-handler/common-handler.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import {digitUppercase, formatMoney, parseTime} from "./common/utils.js";
    import {HospitalDepositReceiptTypeEnum} from "./common/constants.js";

    const initPrintConfig = {
        'title': '', // 抬头名称 字符串必填 String
        'subtitle': '', // 副抬头名称 可选值 String
        'logo': 0, // logo, 可选值 0 1, 默认 0
        'qrCode': 0, // 二维码, 可选值 0 1, 默认 0
        'chargeName': 1, // 收费员, 可选值 0 1, 默认 1
        'chargeTime': 1, // 收费日期, 可选值 0 1, 默认 1
        'remark': '此为临时收据，不作报销凭证，盖章有效。请妥善保存，出院结算时交回。', // 备注内容, String, 默认值: 此为临时收据，不作报销凭证，盖章有效。请妥善保存，出院结算时交回。
        'institution': '', // 收费单位 String
    };

    export default {
        name: "HospitalDepositReceipt",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.HOSPITAL_DEPOSIT_RECEIPT,
        filters: {
            parseTime,
            formatMoney
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        pages: [
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分'
            },
        ],
        computed: {
            printData() {
                return this.renderData.printData;
            },
            config() {
                if (this.renderData.config?.hospitalFeeBills?.depositReceipt) {
                    return this.renderData.config.hospitalFeeBills.depositReceipt;
                }
                return initPrintConfig;
            },
            amount() {
                return this.printData.lastTransaction?.amount ?? 0;
            },
            payModeDisplayName() {
                return this.printData.lastTransaction?.payModeDisplayName ?? '';
            },
            payModePrepDisplayName() {
                return this.isRefund ? '退费方式' : '支付方式';
            },
            totalAmount() {
                return this.printData.totalAmount ?? 0;
            },
            createdByName() {
                return this.printData.lastTransaction?.createdByName ?? '';
            },
            availableAmount() {
                return this.printData.availableAmount ?? 0;
            },
            type() {
                return  this.printData.lastTransaction?.type ?? '';
            },
            isRefund() {
                return this.type === HospitalDepositReceiptTypeEnum.REFUND;
            }
        },
        methods: {
            digitUppercase(amount) {
                if(digitUppercase(amount)) {
                    return digitUppercase(amount);
                } else {
                    return '零元整';
                }
            }
        },
    }
</script>

<style lang="scss">
.hospital-deposit-receipt {
    .header-wrapper {
        position: relative;
    }

    .hospital-deposit-receipt-logo-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 134pt;
        height: 40pt;
    }

    .hospital-deposit-receipt-header-logo {
        width: auto;
        max-width: 100%;
        height: 100%;
    }

    .hospital-deposit-receipt-qr-code-wrapper {
        position: absolute;
        top: 0;
        right: 0;
        width: 56px;
        height: 56px;
    }

    .hospital-deposit-receipt-qr-code-img {
        width: 100%;
        height: 100%;
    }

    .clinic-name,
    .form-name {
        font-size: 12pt;
        font-weight: bold;
        line-height: 2;
        text-align: center;
    }

    .form-name {
        font-weight: normal;
    }

    .deposit-table {
        margin-top: 20px;
        font-size: 10pt;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #000000;
    }

    td {
        padding: 5px;
        border: 1px solid #000000;
    }

    .table-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 4pt;
    }

    .charge-clinic {
        padding-right: 100pt;
    }

    .flex-item {
        display: block;

        span {
            display: inline-block;
            min-width: 240px;
        }

        line-height: 1.5;
    }

    .spread-td {
        width: 16.66%;
    }
}
</style>
