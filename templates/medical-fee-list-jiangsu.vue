<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    unitPrice: 2,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    unit: '次',
                    unitPrice: 2,
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305903',
            chargeFormItems: [
                {
                    id: 'ffebc4a7da95425489aeeb456b0c43ec',
                    name: '推拿',
                    unit: '次',
                    unitPrice: 50,
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 50.0,
                },
                {
                    id: 'eb2a534087c34b18934c84f5af292fd6',
                    name: '肩周炎针灸治疗',
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    unitPrice: 90,
                    doseCount: 1.0,
                    totalPrice: 90.0,
                },
            ],
            sourceFormType: 3,
        },
        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    unitPrice: 36,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    unitPrice: 6.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    unitPrice: 20,
                    totalPrice: 20.0,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f0200',
                    name: '复方丹参片（罗浮山）',
                    unit: '瓶',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    unitPrice: 20,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
        {
            id: '9410ffd3ece8439e9e12c8f3df396bc8',
            chargeFormItems: [
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    unitPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                    name: '盐知母',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                    unitPrice: 36.0,
                    specialRequirement: '包煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9072',
                    name: '山药YG',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    unitPrice: 16.5,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9073',
                    name: '牡丹皮YG',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9076',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                },
            ],
            sourceFormType: 6,
            specification: '中药饮片',
            doseCount: 1,
            dailyDosage: '1日1剂',
            usage: '煎服',
            freq: '1日3次',
            usageLevel: '每次150ml',
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            curYearBalance: 8, // 当年账户余额
            allYearBalance: 10, // 历年账户余额
            curYearAccountPaymentFee: 7, // 本年账户支付
            allYearAccountPaymentFee: 9, // 历年账户支付
            fundPayment: 0, // 规定病种
            cashPayment: 0, // 规定病种
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 1, // 自负金额
            allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 3, // 自理金额
            allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 5, // 自费金额
            allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            sbzzPaymentFee: 0, // 商保赔付
        },
    },
};
-->

<template>
    <div>
        <template v-for="(page, pageIndex) in currentRenderPage">
            <div class="zhejiang-medical-fee-content">
                <div
                    v-if="blueInvoiceData"
                    style="position: absolute; top: 0.2cm; left: 1.8cm;"
                >
                    销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                </div>
                <refund-icon
                    v-if="isRefundBill"
                    top="1cm"
                    left="1.8cm"
                ></refund-icon>
                <div class="stub-form">
                    <span class="medical-fee-list-jiangsu-title">
                        {{ jiangsu.institutionName }}
                    </span>
                    <span class="card-id">
                        <span>{{ extraInfo.psnNo ? extraInfo.psnNo.slice(0,16) : '' }}</span>
                        <span>{{ extraInfo.psnNo ? extraInfo.psnNo.slice(-10) : '' }}</span>
                    </span>
                    <span class="name">
                        {{ patient.name }}
                    </span>
                    <span
                        class="card-type"
                        style="position: absolute; top: 21.5mm; left: 90mm;"
                    >
                        {{ shebaoPayment.cardOwnerType || '自费' }}
                    </span>
                    <span class="date">
                        <span class="year">{{ year }}</span>
                        <span class="month">{{ month }}</span>
                        <span class="day">{{ day }}</span>
                    </span>

                    <span
                        class="organ"
                        style="left: 15mm; width: 61mm; white-space: normal;"
                    >{{ jiangsu.institutionName }}</span>
                    <span
                        class="organ"
                        style="left: 88mm;"
                    >{{ printData.chargedByName }}</span>
                </div>
                <div
                    v-if="!extra.isPreview && currentRenderPage.length > 1"
                    class="page-number"
                >
                    <span data-page-no="PageNo">##</span>/<span data-page-count="PageCount">##</span>
                </div>
                <div class="invoice">
                    <div class="form-items-wrapper">
                        <template v-if="page.formItems.length <= 15">
                            <div
                                v-for="(item, index) in page.formItems"
                                :key="index + pageIndex"
                                class="form-item-tr "
                            >
                                <span
                                    overflow
                                    class="item-name"
                                >{{ item.name }}
                                    <template v-if="item.displaySpec">/{{ item.displaySpec }}</template>
                                </span>
                                <span class="item-count">{{ item.discountedUnitPrice | formatMoney(false) }}*{{ item.count }}{{ item.unit }}</span>
                                <span class="item-price">{{ item.discountedPrice | formatMoney }}</span>
                                <span class="item-price">{{ item.ownExpenseFee || "0" | formatMoney }}</span>
                            </div>
                        </template>
                        <template v-else>
                            <div
                                v-for="(item, index) in page.formItems"
                                :key="index + pageIndex"
                                class="form-item-tr form-item-two-col"
                            >
                                <span
                                    overflow
                                    class="item-name"
                                ><template v-if="item.medicalFeeGrade">[{{
                                     item.medicalFeeGrade | medicalFeeGrade2PrintStr
                                 }}]</template>{{ item.name }}
                                    <template v-if="item.displaySpec">/{{ item.displaySpec }}</template>
                                </span>
                                <span class="item-count">{{ item.unitPrice | formatMoney }} * {{ item.count }}{{ item.unit }}</span>
                                <span class="item-price">{{ item.discountedPrice | formatMoney }}</span>
                            </div>
                        </template>
                        <div
                            v-if="hasOverPageTip"
                            class="only-one-page form-item-tr"
                        >
                            *** 因纸张限制，部分项目未打印 ***
                        </div>
                    </div>
                    <div style=" position: absolute; top: 76mm; left: 45mm;">
                        {{ digitUppercase(finalFee) }}
                    </div>
                    <div style=" position: absolute; top: 76mm; left: 85mm;">
                        {{ finalFee|formatMoney }}
                    </div>
                    <div style=" position: absolute; top: 80mm; left: 33mm;">
                        {{ shebaoPayment.personalPaymentFee | formatMoney }}
                    </div>
                    <div style=" position: absolute; top: 80mm; left: 68mm;">
                        {{ extraInfo.ownpayAmt | formatMoney }}
                    </div>
                    <div style=" position: absolute; top: 80mm; left: 92mm;">
                        {{ extraInfo.psnSelfpay | formatMoney }}
                    </div>
                    <div
                        class="shebao-wrapper"
                    >
                        <div class="shebao-info">
                            <div>
                                费用总额： {{ extraInfo.medfeeSumamt | formatMoney }} <span style="display: inline-block; width: 15mm;"></span>
                            </div>
                            <div>
                                大病支付： {{ extraInfo.seriInsFund | formatMoney }}<span style="display: inline-block; width: 15mm;"></span>
                            </div>
                            <div>
                                救助支付： {{ extraInfo.mafPay | formatMoney }}<span style="display: inline-block; width: 15mm;"></span>
                            </div>
                            <div>
                                家庭账户支付： {{ extraInfo.fmAcctPay | formatMoney }}<span style="display: inline-block; width: 15mm;"></span>
                            </div>
                            <div>
                                个人自付： {{ extraInfo.psnSelfpay | formatMoney }}<span style="display: inline-block; width: 15mm;"></span>
                            </div>
                            <div>
                                其中合规自费： {{ extraInfo.complianceOwnPayAmt | formatMoney }}<span style="display: inline-block; width: 15mm;"></span>
                            </div>
                        </div>
                        <div style="position: absolute; top: 1mm; left: 43mm; width: 180mm;">
                            <div>统筹支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}</div>
                            <div>其他支付：{{ shebaoPayment.otherPaymentFee | formatMoney }}</div>
                            <div>个人账户支付： {{ shebaoPayment.accountPaymentFee | formatMoney }}</div>
                            <div>个人账户余额： {{ shebaoPayment.cardBalance | formatMoney }}</div>
                            <div>个人自费： {{ extraInfo.ownpayAmt| formatMoney }}</div>
                            <div>现金支付： {{ printData.personalPaymentFee | formatMoney }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import {PRINT_BILL_AMOUNT_TYPE} from "./common/constants.js";
    import NationalBillData from "./mixins/national-bill-data";

    export default {
        name: "MedicalFeeListJiangsu",
        components: {
            RefundIcon
        },
        mixins: [BillDataMixins, NationalBillData],
        props: {
            extra: {
                type: Object,
                default() {
                    return {}
                }
            }
        },
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_FEE_LIST_JIANGSU,
        pages: [
            {
                paper: PageSizeMap.MM120_114,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        data() {
            return {
                isFeeList: true,
                registrationFee: '', // 挂号费
                westernMedicineFee: '', // 西药费
                chineseMedicineFee: '',
                chineseMedicineDrinksPieceFee: '', // 中药饮片费用
                chineseComposeMedicineFee: '', // 中成药费用 + 中药颗粒
                treatmentFee: '', // 治疗理疗费
                examinationInspectionFee: '', // 检查费
                examinationExaminationFee: '', // 化验费
                materialFee: '', // 材料费
                otherFee: '', // 一般诊疗费( 其他费用 )
            };
        },
        computed: {
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                return this.isOnlyOnePage ? this.renderPage.slice(0, 1) : this.renderPage
            },
            splitType() {
                return this.jiangsu.splitType;
            },
            isOnlyOnePage() {
                return this.splitType === 1 && (this.renderPage.length > 1 || this.extra.isPreview);
            },
            jiangsu() {
                return this.config.jiangsu || {};
            },
            // 医疗清单 0 只展示套餐  2 只展示子项  1 展示套餐加子项 转换成同 医疗票据 0 只展示套餐1 只展示子项  2 展示套餐名加子项
            composeChildrenConfig() {
                const composeChildren = this.jiangsu.composeChildren || 0;
                switch (composeChildren) {
                    case 1:
                        return 2;
                    case 2:
                        return 1;
                    default:
                        return 0;
                }
            },
            config() {
                return this.renderData.config.medicalListConfig || {}
            },
            isNeedResetComposeSocialFee() {
                return true
            },
            renderPage() {
                return this.spliceFormItems(this.chargeFormItems, 10);
            },
        },
        created() {
            this.initFee();
        },
        methods: {
            initFee() {
                this.medicalBills.forEach((item) => {
                    switch (item.printType) {
                        case PRINT_BILL_AMOUNT_TYPE.westernMedicineFeeType:
                            this.westernMedicineFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.chineseMedicineDrinksPieceFeeType:
                            this.chineseMedicineDrinksPieceFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.chineseComposeMedicineFeeType:
                            this.chineseComposeMedicineFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.examinationInspectionFeeType:
                            this.examinationInspectionFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.examinationExaminationFeeType:
                            this.examinationExaminationFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.treatmentFeeType:
                            this.treatmentFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.registrationFeeType:
                            this.registrationFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.materialFeeType:
                            this.materialFee = item.totalFee;
                            break;
                        case PRINT_BILL_AMOUNT_TYPE.otherFeeType:
                            this.otherFee = item.totalFee;
                            break;
                        default:
                            break;
                    }
                });
            },
        },
    }
</script>
<style lang="scss">
* {
    padding: 0;
    margin: 0;
}

.zhejiang-medical-fee-content {
    @import "./components/refund-icon/refund-icon.scss";

    position: absolute;
    top: 1mm;
    width: 120mm;
    height: 114mm;
    font-size: 9pt;

    .stub-form {
        position: relative;
        width: 120mm;
        height: 114mm;
        //border: 1pt solid #00aca0;

        .medical-fee-list-jiangsu-title {
            position: absolute;
            top: 11mm;
            left: 0;
            box-sizing: border-box;
            width: 100%;
            padding: 0 14mm;
            text-align: center;
        }

        .card-id,
        .name,
        .date,
        .fee-item,
        .total-fee,
        .organ {
            position: absolute;
        }

        .card-id {
            left: 58mm;
            display: inline-block;
            width: 30mm;
            word-wrap: break-word;
        }

        .name,
        .card-id, {
            top: 21.5mm;
        }

        .name {
            left: 27mm;
        }

        .date {
            top: 15mm;
            left: 37mm;

            .year,
            .month,
            .day {
                position: absolute;
            }

            .year {
                left: 0;
            }

            .month {
                left: 10mm;
            }

            .day {
                left: 18mm;
            }
        }

        .fee-item {
            position: absolute;
        }

        .col-1 {
            left: 35mm;
        }

        .col-2 {
            left: 68mm;
        }

        .row-1 {
            top: 47mm;
        }

        .row-2 {
            top: 53mm;
        }

        .row-3 {
            top: 60mm;
        }

        .row-4 {
            top: 66mm;
        }

        .row-5 {
            top: 71mm;
        }

        .row-6 {
            top: 79mm;
        }

        .row-7 {
            top: 86mm;
        }

        .row-8 {
            top: 92.5mm;
        }

        .total-fee {
            top: 99.5mm;
            left: 35mm;
        }

        .organ {
            top: 100.5mm;
            left: 52mm;
            white-space: nowrap;
        }
    }

    .invoice {
        position: absolute;
        top: -3.5mm;
        width: 115mm;
        height: 127mm;
        //border: 1pt solid #00aca0;

        .form-items-wrapper {
            position: absolute;
            top: 35mm;
            left: 15mm;
            width: 94mm;
            height: 50mm;
            font-size: 0;

            .form-item-tr {
                width: 100%;
                font-size: 0;

                &.form-item-two-col {
                    display: inline-block;
                    width: 49.9%;

                    .item-name {
                        width: 42%;
                        min-width: 42%;
                        max-width: 42%;
                    }

                    .item-count {
                        width: 21%;
                        min-width: 21%;
                        max-width: 21%;
                    }

                    .item-price {
                        width: 19%;
                        min-width: 19%;
                        max-width: 19%;
                    }
                }

                &.only-one-page {
                    font-size: 9pt;
                    text-align: center;
                }
            }

            .item-name,
            .item-count,
            .item-price {
                display: inline-block;
                padding-right: 0.1mm;
                font-size: 9pt;
                line-height: 12pt;
                vertical-align: text-top;
            }

            .item-name {
                width: 42%;
                min-width: 42%;
                max-width: 42%;
                margin-right: 2pt;
                overflow: hidden;
                word-break: keep-all;
                white-space: nowrap;
            }

            .item-count {
                width: 21%;
                min-width: 21%;
                max-width: 21%;
                overflow: hidden;
                word-break: keep-all;
                white-space: nowrap;
            }

            .item-price {
                width: 15%;
                min-width: 15%;
                max-width: 15%;
            }
        }

        .shebao-wrapper {
            position: absolute;
            top: 82mm;
            left: 25mm;
            padding-top: 1mm;
            padding-right: 4mm;
            font-size: 9pt;
            line-height: 10pt;
        }

        .med-type {
            position: absolute;
            top: 0;
            right: 8mm;
        }

        .year-amount {
            > span {
                display: inline-block;
                width: 42mm;
            }
        }
    }

    .page-number {
        position: absolute;
        top: 109mm;
        left: 105mm;
    }
}

.abc-page_preview {
    color: #2a82e4;
    background: url("/static/assets/print/jiangsu-fee.jpg");
    background-size: 120mm 114mm;
}
</style>
