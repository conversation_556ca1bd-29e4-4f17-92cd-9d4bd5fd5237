<template>
    <div>
        <template v-for="(applySheet, i) in renderApplySheets">
            <component
                :is="curHeaderComponent"
                :print-data="getApplySheetHeaderInfo(i)"
                :print-title="printTitle"
                :config="config"
                :organ-title="organTitle"
                data-type="header"
                :data-pendants-index="`${i}applySheet`"
                :is-hospital="isInHospital"
            ></component>

            <template v-if="applySheet.renderExamItems.length">
                <table
                    data-type="mix-box"
                    class="print-examination-table"
                >
                    <thead>
                        <tr>
                            <td
                                colspan="4"
                                class="item-name"
                            >
                                项目
                            </td>
                            <td
                                colspan="1"
                                class="right-text"
                                :class="{ hidden: !contentConfig.productUnitPrice}"
                            >
                                单价
                            </td>
                            <td
                                colspan="1"
                                class="right-text"
                                style="padding-right: 16pt;"
                            >
                                数量
                            </td>
                            <td
                                colspan="1"
                                overflow
                            >
                                单位
                            </td>
                        </tr>
                    </thead>
                    <tbody data-type="group">
                        <template v-for="(item, index) in applySheet.renderExamItems">
                            <tr
                                :key="`${item.name }inspect-item`"
                                :class="{'has-remark-tr': item.remark}"
                                data-type="item"
                            >
                                <td colspan="4">
                                    <div
                                        class="item-name"
                                        overflow
                                    >
                                        <span class="item-number">{{ index + 1 }}.</span>
                                        <span
                                            v-if="item.toothNos && item.toothNos.length"
                                            v-html="formatToothNos2Html(item.toothNos)"
                                        ></span>
                                        {{ item.name }}
                                        <span
                                            v-if="item.remark"
                                            class="item-remark"
                                        >
                                            {{ item.remark }}
                                        </span>
                                    </div>
                                </td>
                                <td
                                    :class="{ hidden: !contentConfig.productUnitPrice}"
                                    colspan="1"
                                    class="right-text"
                                >
                                    {{ item.unitPrice | formatMoney }}
                                </td>
                                <td
                                    colspan="1"
                                    class="right-text"
                                    style="padding-right: 16pt;"
                                >
                                    {{ item.unitCount }}
                                </td>
                                <td
                                    colspan="1"
                                    overflow
                                >
                                    {{ item.unit }}
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </template>

            <template v-if="contentConfig.medicalRecord">
                <div class="examination_medical-record">
                    <div
                        v-for="(item,i) in applySheet.medicalRecordItems"
                        :key="i"
                        class="medical-record-item"
                    >
                        <span>
                            <span
                                v-for="(c,j) in item.label.split('')"
                                :key="j"
                            >
                                {{ c }}
                            </span>
                            <span>
                                ：
                            </span>
                        </span>
                        <span>
                            {{ item.value }}
                        </span>
                    </div>
                </div>
            </template>

            <template v-if="contentConfig.inspectPurpose">
                <div class="examination_medical-record">
                    <div class="medical-record-item">
                        <span>
                            <span>检</span>
                            <span>查</span>
                            <span>目</span>
                            <span>的</span>
                            <span>：</span>
                        </span>
                        <span>
                            {{ applySheet.purposeOfExamination }}
                        </span>
                    </div>
                </div>
            </template>

            <!-- <div
                v-if="i < renderApplySheets.length - 1"
                data-type="new-page"
            >
            </div> -->

            <div
                data-type="footer"
                class="examination_footer"
                :data-pendants-index="`${i}applySheet`"
            >
                <div class="row-1">
                    <span>
                        <span
                            v-if="footerConfig.doctorSignature"
                            style="display: inline-flex; align-items: center;"
                        >
                            <span>
                                医生：
                            </span>
                            
                            <hand-sign :value="getApplySheetFooterInfo(i).doctorHandSign"></hand-sign>
                        </span>
                        <span v-else>
                            医生： {{ getApplySheetFooterInfo(i).doctorName }}
                        </span>

                        <span v-if="footerConfig.amount && !isInHospital">
                            金额：{{ getApplySheetFooterInfo(i).totalPrice | formatMoney }}
                        </span>
                    </span>
                    <span v-if="footerConfig.printDate">
                        打印日期：{{ new Date() | parseTime('y-m-d h:i:s') }}
                    </span>
                </div>
                <div class="row-2">
                    {{ footerConfig.remark }}
                </div>
            </div>
        </template>
    </div>
</template>

<script>
    import ExaminationApplySheetHeaderOutpatient from './components/examination-apply-sheet/header-outpatient.vue';
    import ExaminationApplySheetHeaderHospital from './components/examination-apply-sheet/header-hospital.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { GoodsSubTypeEnum, GoodsTypeEnum } from "./common/constants.js";
    import { formatMoney, parseTime } from "./common/utils.js";
    import { formatToothNos2Html } from "./common/medical-transformat.js";
    import examinationHandler from "./data-handler/examination-apply-sheet-handler";
    import HandSign from "./components/hand-sign/index.vue";
    export default {
        DataHandler: examinationHandler,
        name: "ExaminationApplySheet",
        components: {
            HandSign,
            ExaminationApplySheetHeaderOutpatient,
            ExaminationApplySheetHeaderHospital,
        },
        filters: {
            formatMoney,
            parseTime,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
            extra: {
                type: Object,
                default() {
                    return {}
                },
            },
        },
        businessKey: PrintBusinessKeyEnum.EXAMINATION_APPLY_SHEET,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM95_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分', // 默认选择的等分纸
            },
        ],
        computed: {
            printData() {
                console.log(this.renderData, 'renderData');
                return this.renderData.printData || {};
            },
            config() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.examination) {
                    console.log(this.renderData.config.medicalDocuments.examination, 'config-----');
                    return this.renderData.config.medicalDocuments.examination;
                }
                return {};
            },
            headerConfig() {
                return this.config?.header || {};
            },
            contentConfig() {
                return this.config?.content || {};
            },
            footerConfig() {
                return this.config?.footer || {};
            },
            renderApplySheets() {
                console.log(this.printData.rows, 'this.printData.rows');
                const applySheets = this.printData.rows.map(item => ({
                    ...item,
                    renderExamItems: item.businessFormItems.filter(item => !item.noNeedPrint),
                    medicalRecordItems: [
                        {
                            label: '主诉',
                            value: item.chiefComplaint,
                        },
                        {
                            label: '现病史',
                            value: item.presentHistory,
                        },
                        {
                            label: '体征',
                            value: item.physicalExamination,
                        },
                    ],
                    purposeOfExamination: item.purpose || '',
                })).filter(item => !item.noNeedPrint) || [];
                console.log(applySheets, 'applySheets');
                return applySheets;
            },

            printTitle() {
                const applySheetItem = this.renderApplySheets[0] || {};

                return applySheetItem.type === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test ? '检查申请单' : '检验申请单'
            },
            organTitle() {
                const applySheetItem = this.renderApplySheets[0] || {};

                return applySheetItem?.organPrintView?.medicalDocumentsTitle?.examination || '';
            },
            // 是否为住院
            isInHospital() {
                return !!this.printData.isInHospital;
            },
            curHeaderComponent() {
                return this.isInHospital ? ExaminationApplySheetHeaderHospital : ExaminationApplySheetHeaderOutpatient;
            },
        },
        methods: {
            getApplySheetHeaderInfo(i) {
                const applySheetItem = this.renderApplySheets[i] || {};
                const {
                    patient,
                    no,
                    organPrintView,
                    shebaoCardInfo,
                    patientOrderNumber,
                    created,
                    departmentName,
                    diagnosisInfos,
                    bedNo,
                    wardName,
                    inpatientNo,
                    healthCardPayLevel,
                    healthCardNo,
                } = applySheetItem;

                const diagnosis = (diagnosisInfos || []).map(d => d.diseaseName).join('、');

                const _shebaoCardInfo = shebaoCardInfo || {};

                return {
                    patient,
                    barcode: no,
                    qrCode: this.printData.qrCode || '',
                    organ: organPrintView || {},
                    shebaoCardInfo: _shebaoCardInfo,
                    patientOrderNo: patientOrderNumber,
                    diagnosedDate: created || '',
                    departmentName,
                    diagnosis,
                    healthCardNo,
                    healthCardPayLevel,
                    bedNo,
                    wardName,
                    inpatientNo,
                }
            },
            getApplySheetFooterInfo(i) {
                const applySheetItem = this.renderApplySheets[i] || {};

                return {
                    doctorName: applySheetItem.doctorName,
                    totalPrice: applySheetItem.amount || 0,
                    doctorHandSign: applySheetItem.doctor?.handSign,
                }
            },
            formatToothNos2Html,
        },
    }
</script>

<style lang="scss">
@import "./style/reset.scss";
@import "./components/layout/print-layout.scss";

.abc-page-content {
    box-sizing: border-box;
    padding: 8pt;
    font-family: "Microsoft YaHei", "微软雅黑";
}

.print-examination-table {
    width: 100%;
    margin-top: 6pt;
    font-size: 10pt;
    line-height: 12pt;
    table-layout: fixed;

    .has-remark-tr {
        td {
            padding-bottom: 2pt;
        }
    }

    td {
        padding-bottom: 6pt;
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;

        &.remark-td {
            padding-top: 0;
        }
    }

    .item-name {
        position: relative;
        // padding-left: 18pt;

        // .item-number {
        //     position: absolute;
        //     top: 0;
        //     left: 0;
        // }
    }

    .item-remark {
        padding-left: 6pt;
    }

    .right-text {
        padding-right: 4pt;
        text-align: right;
    }

    .hidden {
        visibility: hidden;
    }
}

.global-tooth-selected-quadrant {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    width: auto;
    min-width: 32pt;
    height: 20pt;
    vertical-align: middle;

    .top-tooth,
    .bottom-tooth {
        display: flex;
        align-items: center;
        width: 100%;
        min-width: 32pt;
    }

    .left-tooth,
    .right-tooth {
        display: flex;
        align-items: center;
        width: 50%;
        height: 9pt;
        padding: 0 1pt;
        font-family: 'MyKarlaRegular';
        font-size: 9pt;
        letter-spacing: 1px;
        user-select: none;
    }

    .left-tooth {
        justify-content: flex-end;
        border-right: 1pt solid #7a8794;
    }

    .top-tooth {
        min-width: 32pt;
        border-bottom: 1pt solid #7a8794;

        > div {
            padding-bottom: 1px;
        }
    }

    .bottom-tooth {
        > div {
            padding-top: 1px;
        }
    }

    &.all-tooth {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }

    .all-tooth {
        min-width: 18pt;
        height: 11pt;
        font-size: 9pt;
        line-height: 11pt;
    }

    &.no-data {
        .left-tooth {
            border-right: 1px dashed #000000;
        }

        .top-tooth {
            border-bottom: 1px dashed #000000;
        }
    }
}

.examination_medical-record {
    padding: 7.5pt 0;
    border-top: 1pt dashed #000000;

    .medical-record-item {
        display: flex;
        margin-bottom: 6pt;
        font-size: 10pt;
        line-height: 12pt;
        color: #000000;

        &:last-child {
            margin-bottom: 0;
        }

        >span:first-child {
            display: inline-flex;
            align-items: center;
            justify-content: space-between;
            width: 50pt;
        }

        >span:last-child {
            flex: 1;
        }
    }
}

.examination_footer {
    position: absolute;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    width: 100%;
    padding: 6.5pt 8pt 0;
    font-family: Microsoft YaHei, 微软雅黑;
    border-top: 1px solid #000000;

    .row-1 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12pt;
        font-size: 10pt;
        line-height: 12pt;

        >span:first-child {
            >span:first-child {
                margin-right: 15pt;
            }
        }
    }

    .row-2 {
        font-size: 8pt;
        line-height: 10pt;

        >span:first-child {
            margin-right: 6pt;
        }
    }
}

</style>
