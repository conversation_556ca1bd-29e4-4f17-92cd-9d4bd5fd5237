<template>
    <div>
        <div data-type="header">
            <outpatient-header
                :print-data="printData"
                :config="config"
                :organ-title="organTitle"
                print-title="门诊病历"
                class="medical-header"
                :show-rp="false"
            >
            </outpatient-header>
        </div>
        <template v-for="item in sortedMrStruct">
            <div
                v-if="item.key === 'chiefComplaint' && medicalRecord.chiefComplaint && chiefComplaintList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">主 诉</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(chiefComplaintText, idx) in chiefComplaintList"
                        :key="`chief-complaint-${idx}`"
                        data-type="item"
                    >
                        {{ chiefComplaintText }}
                        <template v-if="medicalRecord.symptomTime && idx === chiefComplaintList.length - 1">
                            ；发病日期：{{ medicalRecord.symptomTime | parseTime('y-m-d') }}
                        </template>
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'presentHistory' && medicalRecord.presentHistory && presentHistoryList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">现 病 史</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(presentHistoryText, idx) in presentHistoryList"
                        :key="`present-history-${idx}`"
                        data-type="item"
                    >
                        {{ presentHistoryText }}
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'pastHistory' && medicalRecord.pastHistory && pastHistoryList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">既 往 史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(pastHistoryText, idx) in pastHistoryList"
                        :key="`past-history-${idx}`"
                        data-type="item"
                    >
                        {{ pastHistoryText }}
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'wearGlassesHistory' && medicalRecord.wearGlassesHistory && wearGlassesHistoryList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">戴 镜 史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(wearGlassesHistoryText, idx) in wearGlassesHistoryList"
                        :key="`wear-glasses-history-${idx}`"
                        data-type="item"
                    >
                        {{ wearGlassesHistoryText }}
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'personalHistory' && medicalRecord.personalHistory && personalHistoryList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">个 人 史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(personalHistoryText, idx) in personalHistoryList"
                        :key="`personal-history-${idx}`"
                        data-type="item"
                    >
                        {{ personalHistoryText }}
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'allergicHistory' && medicalRecord.allergicHistory && allergicHistoryList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">过 敏 史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(allergicHistoryText, idx) in allergicHistoryList"
                        :key="`allergic-history-${idx}`"
                        data-type="item"
                    >
                        {{ allergicHistoryText }}
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'familyHistory' && medicalRecord.familyHistory && familyHistoryList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">家 族 史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(familyHistoryText, idx) in familyHistoryList"
                        :key="`family-history-${idx}`"
                        data-type="item"
                    >
                        {{ familyHistoryText }}
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'obstetricalHistory' && medicalRecord.obstetricalHistory && obstetricalHistory && obstetricalHistoryList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">月经婚育史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos obstetrical-history"
                    data-type="group"
                >
                    <div
                        v-for="(obstetricalHistoryText, idx) in obstetricalHistoryList"
                        :key="`obstetrical-history-${idx}`"
                        data-type="item"
                        v-html="obstetricalHistoryText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'epidemiologicalHistory' && medicalRecord.epidemiologicalHistory && formatEpidemiologicalHistory && epidemiologicalHistoryList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">流 行 病 史</span>
                    <span>：</span>
                </div>

                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(epidemiologicalHistoryText, idx) in epidemiologicalHistoryList"
                        :key="`epidemiological-history-${idx}`"
                        data-type="item"
                        v-html="epidemiologicalHistoryText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'physicalExamination' && medicalRecord.physicalExamination && physicalExaminationList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">体 格 检 查</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(physicalExaminationText, idx) in physicalExaminationList"
                        :key="`physical-examination-${idx}`"
                        data-type="item"
                        v-html="physicalExaminationText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'chineseExamination' && medicalRecord.chineseExamination && chineseExaminationList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">望 闻 切 诊</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(chineseExaminationText, idx) in chineseExaminationList"
                        :key="`chinese-examination-${idx}`"
                        data-type="item"
                        v-html="chineseExaminationText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'tongue' && medicalRecord.tongue && tongueList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">舌 象</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(tongueText, idx) in tongueList"
                        :key="`tongue-${idx}`"
                        data-type="item"
                        v-html="tongueText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'pulse' && medicalRecord.pulse && pulseList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">脉 象</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(pulseText, idx) in pulseList"
                        :key="`pulse-${idx}`"
                        data-type="item"
                        v-html="pulseText"
                    >
                    </div>
                </div>
            </div>
            <template v-if="isDentistryExaminationsLabel">
                <div
                    v-if="item.key === 'dentistryExaminations' && medicalRecord.dentistryExaminations && formatDentistryExaminations && formatDentistryExaminationsList.length"
                    :key="item.key"
                    class="medical-item"
                    data-type="mix-box"
                >
                    <div class="label-title">
                        <span class="label">口 腔 检 查</span>
                        <span>：</span>
                    </div>
                    <div
                        class="infos oral-examination-wrapper"
                        data-type="group"
                    >
                        <div
                            v-for="(formatDentistryExaminationsText, idx) in formatDentistryExaminationsList"
                            :key="`format-dentistry-examinations-${idx}`"
                            data-type="item"
                            v-html="formatDentistryExaminationsText"
                        >
                        </div>
                    </div>
                </div>
            </template>
            <div
                v-else-if="item.key === 'oralExamination' && medicalRecord.oralExamination && formatOralExamination && formatOralExaminationList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">口 腔 检 查</span>
                    <span>：</span>
                </div>
                <div
                    class="infos oral-examination-wrapper"
                    data-type="group"
                >
                    <div
                        v-for="(formatOralExaminationText, idx) in formatOralExaminationList"
                        :key="`format-oral-examination-${idx}`"
                        data-type="item"
                        v-html="formatOralExaminationText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'auxiliaryExaminations' && medicalRecord.auxiliaryExaminations && medicalRecord.auxiliaryExaminations.length && formatAuxiliaryExaminations && formatAuxiliaryExaminationsList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">辅 助 检 查</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(auxiliaryExaminationsText, idx) in formatAuxiliaryExaminationsList"
                        :key="`auxiliary-examinations-${idx}`"
                        data-type="item"
                        v-html="auxiliaryExaminationsText"
                    >
                    </div>
                </div>
            </div>
            <!--眼部检查-->
            <template
                v-if="item.key === 'eyeExamination'"
            >
                <template
                    v-if="medicalRecord.eyeExamination && medicalRecord.eyeExamination.items && medicalRecord.eyeExamination.items.length"
                >
                    <div
                        :key="item.key"
                        class="medical-item"
                    >
                        <div class="label-title">
                            <span class="label">眼 部 检 查</span>
                            <span>：</span>
                        </div>
                    </div>

                    <eye-inspect-table
                        :key="`${item.key}-table`"
                        :list="medicalRecord.eyeExamination.items"
                        :style-config="styleConfig"
                        current-page-is-a4
                    ></eye-inspect-table>

                    <abc-print-space
                        :key="`${item.key}-space`"
                        :value="6"
                    ></abc-print-space>
                </template>

                <!--眼科检查报告-->
                <template v-if="contentConfig.eyeInspectReport && !!eyeInspectTableLength">
                    <div
                        :key="`${item.key}-item`"
                        class="medical-item"
                    >
                        <div class="label-title">
                            <span class="label">检 查 报 告</span>
                            <span>：</span>
                        </div>
                    </div>

                    <eye-inspect-merge-item-table
                        :key="`${item.key}-merge-table`"
                        :items-value="getItemsValue()"
                        :style-config="styleConfig"
                        current-page-is-a4
                        class="medical-record-eye-inspect-table"
                        @get-table-length="v => eyeInspectTableLength = v"
                    ></eye-inspect-merge-item-table>

                    <abc-print-space
                        :key="`${item.key}-space2`"
                        :value="6"
                    ></abc-print-space>
                </template>
            </template>
            <div
                v-if="item.key === 'syndromeTreatment' && medicalRecord.syndromeTreatment && syndromeTreatmentList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">辨 证 论 治</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(syndromeTreatmentText, idx) in syndromeTreatmentList"
                        :key="`syndrome-treatment-${idx}`"
                        data-type="item"
                        v-html="syndromeTreatmentText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'diagnosis' && medicalRecord.extendDiagnosisInfos && extendDiagnosisInfos && extendDiagnosisInfosList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">诊 断</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(extendDiagnosisInfosText, idx) in extendDiagnosisInfosList"
                        :key="`extend-diagnosis-infos-${idx}`"
                        style="display: inline-block;"
                        data-type="item"
                        v-html="extendDiagnosisInfosText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'syndrome' && medicalRecord.syndrome && syndromeList && syndromeList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">辨 证</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(syndromeText, idx) in syndromeList"
                        :key="`syndrome-${idx}`"
                        data-type="item"
                        v-html="syndromeText"
                    >
                    </div>
                </div>
            </div>
            <div
                v-if="item.key === 'treatmentPlans' && medicalRecord.treatmentPlans && medicalRecord.treatmentPlans.length && formatTreatmentPlans && formatTreatmentPlansList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">治 疗 计 划</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(treatmentPlansText, idx) in formatTreatmentPlansList"
                        :key="`treatment-plans-${idx}`"
                        data-type="item"
                        v-html="treatmentPlansText"
                    ></div>
                </div>
            </div>
            <template v-else>
                <div
                    v-if="item.key === 'therapy' && medicalRecord.therapy && therapyList.length"
                    :key="item.key"
                    class="medical-item"
                    data-type="mix-box"
                >
                    <div class="label-title">
                        <span class="label">治 法</span>
                        <span>：</span>
                    </div>
                    <div
                        class="infos"
                        data-type="group"
                    >
                        <div
                            v-for="(therapyText, idx) in therapyList"
                            :key="`therapy-${idx}`"
                            data-type="item"
                            v-html="therapyText"
                        ></div>
                    </div>
                </div>
            </template>
            <div
                v-if="item.key === 'chinesePrescription' && medicalRecord.chinesePrescription && chinesePrescriptionList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">方 药</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(chinesePrescriptionText, idx) in chinesePrescriptionList"
                        :key="`chinese-prescription-${idx}`"
                        data-type="item"
                        v-html="chinesePrescriptionText"
                    ></div>
                </div>
            </div>
            <div
                v-if="
                    item.key === 'disposals' &&
                        (contentConfig.medicalItem ||
                            (medicalRecord.disposals && medicalRecord.disposals.length && formatDisposals && formatDisposalsList.length))
                "
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">处 置</span>
                    <span>：</span>
                </div>
                <div
                    v-if="medicalRecord.disposals && medicalRecord.disposals.length && formatDisposals && formatDisposalsList.length"
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(disposalsText, idx) in formatDisposalsList"
                        :key="`disposals-${idx}`"
                        data-type="item"
                        v-html="disposalsText"
                    ></div>
                </div>
            </div>
            <div
                v-if="item.key === 'target' && medicalRecord.target && targetList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">目 标</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(targetText, idx) in targetList"
                        :key="`target-${idx}`"
                        data-type="item"
                        v-html="targetText"
                    ></div>
                </div>
            </div>
            <div
                v-if="item.key === 'prognosis' && medicalRecord.prognosis && prognosisList.length"
                :key="item.key"
                class="medical-item"
                data-type="mix-box"
            >
                <div class="label-title">
                    <span class="label">预 后</span>
                    <span>：</span>
                </div>
                <div
                    class="infos"
                    data-type="group"
                >
                    <div
                        v-for="(prognosisText, idx) in prognosisList"
                        :key="`prognosis-${idx}`"
                        data-type="item"
                        v-html="prognosisText"
                    ></div>
                </div>
            </div>
        </template>

        <template v-if="contentConfig.medicalItem">
            <!--项目-->
            <template v-for="(form, fIndex) in printData.productForms">
                <print-row
                    :key="`product-form-${fIndex}`"
                    data-type="mix-box"
                >
                    <div data-type="group">
                        <print-col
                            v-for="(formItem, pIndex) in form.productFormItems"
                            :key="`product-form-item-${pIndex}`"
                            class="product-form-item"
                            :class="[productItemClass(formItem, pIndex)]"
                            :span="(isLandscape && formItem.composeType !== 1) ? 12 : 24"
                        >
                            <template v-if="formItem.composeType === 1">
                                <template v-if="contentConfig.compose !== 1">
                                    <print-col
                                        :span="19"
                                        data-type="item"
                                    >
                                        <template
                                            v-if="
                                                !isSelfPay &&
                                                    contentConfig.treatmentMedicalFeeGrade &&
                                                    formItem.productInfo &&
                                                    formItem.productInfo.medicalFeeGrade
                                            "
                                        >
                                            [
                                            <span>{{ formItem.productInfo.medicalFeeGrade | medicalFeeGrade2PrintStr }}</span>
                                            <span v-if="contentConfig.treatmentOwnExpenseRatio">
                                                {{ selfPayPropNumber(formItem.productInfo) }}
                                            </span>
                                            ]
                                        </template>
                                        <span
                                            v-if="formItem.toothNos && formItem.toothNos.length"
                                            v-html="formatToothNos2Html(formItem.toothNos)"
                                        ></span>
                                        【套】{{ formItem.name }}
                                    </print-col>
                                    <print-col
                                        :span="5"
                                        style="text-align: right;"
                                        class="medical-record-product-compose-item-text-right"
                                        data-type="item"
                                    >
                                        {{ formItem.unitCount }}{{ transformProductUnit(form , formItem) }}
                                    </print-col>
                                    <print-col
                                        :span="24"
                                        data-type="item"
                                    >
                                        {{ formItem.remark && formItem.remark }}
                                    </print-col>
                                </template>
                                <template v-if="contentConfig.compose !== 0">
                                    <template v-for="(groupItem, gIndex) in formItem.composeChildren">
                                        <template
                                            :class="{'has-remark-tr': contentConfig.compose === 1 && formItem.remark}"
                                            data-type="item"
                                        >
                                            <print-col
                                                v-if="contentConfig.compose === 2"
                                                :key="`product-compose-form-num-${gIndex}`"
                                                :span="2"
                                                class="no-right-border medical-record-product-compose-item-text-right"
                                                style="text-align: right;"
                                                overflow
                                                data-type="item"
                                            >
                                                {{ gIndex + 1 }}.
                                            </print-col>
                                            <print-col
                                                :key="`product-compose-form-name-${gIndex}`"
                                                :span="contentConfig.compose === 2 ? 18 : 20"
                                                class="no-right-border"
                                                overflow
                                                data-type="item"
                                            >
                                                <template
                                                    v-if="
                                                        !isSelfPay &&
                                                            contentConfig.treatmentMedicalFeeGrade &&
                                                            groupItem.productInfo &&
                                                            groupItem.productInfo.medicalFeeGrade
                                                    "
                                                >
                                                    [
                                                    <span>{{ groupItem.productInfo.medicalFeeGrade | medicalFeeGrade2PrintStr }}</span>
                                                    <span v-if="contentConfig.treatmentOwnExpenseRatio">
                                                        {{ selfPayPropNumber(groupItem.productInfo) }}
                                                    </span>
                                                    ]
                                                </template>
                                                {{ groupItem.name }}
                                            </print-col>

                                            <print-col
                                                :key="`product-compose-form-unit-${gIndex}`"
                                                :span="4"
                                                class="no-right-border no-left-border text-right medical-record-product-compose-item-text-right"
                                                style="text-align: right;"
                                                data-type="item"
                                            >
                                                <span v-if="groupItem.productInfo && !groupItem.productInfo.needExecutive">{{ groupItem.unitCount
                                                }}{{ formatTreatmentUnit(groupItem.unit, ' ') }}</span>
                                                <span v-else> {{ groupItem.unitCount
                                                }}{{ formatTreatmentUnit(groupItem.unit, ' ') }}</span>
                                            </print-col>
                                        </template>
                                        <!--                                <tr-->
                                        <!--                                    v-if="contentConfig.compose === 1 && formItem.remark"-->
                                        <!--                                    data-type="item"-->
                                        <!--                                >-->
                                        <!--                                    <td-->
                                        <!--                                        overflow-->
                                        <!--                                        class="remark-text no-right-border remark-td"-->
                                        <!--                                        colspan="12"-->
                                        <!--                                    >-->
                                        <!--                                        {{ formItem.remark }}-->
                                        <!--                                    </td>-->
                                        <!--                                </tr>-->
                                    </template>
                                </template>
                            </template>
                            <template v-else>
                                <print-row data-type="item">
                                    <print-col :span="24">
                                        <div style="display: flex; flex-wrap: nowrap;">
                                            <div style="flex: 1; margin-right: 8px; overflow: hidden; white-space: nowrap;">
                                                <template
                                                    v-if="
                                                        !isSelfPay &&
                                                            contentConfig.treatmentMedicalFeeGrade &&
                                                            formItem.productInfo &&
                                                            formItem.productInfo.medicalFeeGrade
                                                    "
                                                >
                                                    [
                                                    <span>{{ formItem.productInfo.medicalFeeGrade | medicalFeeGrade2PrintStr }}</span>
                                                    <span v-if="contentConfig.treatmentOwnExpenseRatio">
                                                        {{ selfPayPropNumber(formItem.productInfo) }}
                                                    </span>
                                                    ]
                                                </template>
                                                <span
                                                    v-if="formItem.toothNos && formItem.toothNos.length"
                                                    v-html="formatToothNos2Html(formItem.toothNos)"
                                                ></span>
                                                {{ formItem.name }}
                                            </div>

                                            <div
                                                overflow
                                                class="medical-record-product-compose-item-text-right"
                                            >
                                                {{ generateDiagnosisUsageInfo(form, formItem) }}
                                            </div>
                                        </div>
                                    </print-col>

                                    <print-col :span="24">
                                        {{ formItem.remark && formItem.remark }}
                                    </print-col>
                                </print-row>
                            </template>
                        </print-col>
                    </div>
                </print-row>

                <print-row
                    v-if="fIndex < printData.productForms.length - 1 || showProductFormLine"
                    :key="`medical-record-split-line-${fIndex}`"
                    class="split-form-line"
                ></print-row>
            </template>

            <!-- 西药 -->
            <template
                v-for="(form, fIndex) in printData.prescriptionWesternForms"
            >
                <print-row
                    v-for="(formItem, pIndex) in form.prescriptionFormItems"
                    :key="`western-form-${pIndex}`"
                    class="western-form-item"
                    data-type="mix-box"
                >
                    <print-col
                        :span="15"
                    >
                        <div
                            class="western-item"
                            style=" display: flex; gap: 8px; align-items: flex-start; width: 100%; padding-right: 8px;"
                        >
                            <div
                                class="western-item-wrapper"
                                style="flex: 1; width: unset;"
                                overflow
                            >
                                <div
                                    class="western-item-name"
                                    :style="{ 'max-width': contentConfig.westernMedicineSpec ? '80%' : '100%' }"
                                    overflow
                                >
                                    <template
                                        v-if="
                                            !isSelfPay &&
                                                contentConfig.westernMedicalFeeGrade &&
                                                formItem.productInfo &&
                                                formItem.productInfo.medicalFeeGrade
                                        "
                                    >
                                        [
                                        <span>{{ formItem.productInfo.medicalFeeGrade | medicalFeeGrade2PrintStr }}</span>
                                        <span v-if="contentConfig.westernOwnExpenseRatio">
                                            {{ selfPayPropNumber(formItem.productInfo) }}
                                        </span>
                                        ]
                                    </template>
                                    <span v-if="contentConfig.medicineTradeName">
                                        {{ formItem.name }}
                                    </span>
                                    <span v-else>
                                        {{ formItem.medicineCadn || formItem.name }}
                                    </span>
                                </div>
                                <div
                                    v-if="contentConfig.westernMedicineSpec && formItem.productInfo"
                                    class="western-item-spec"
                                >
                                    ({{ formItem.productInfo | goodsSpec }})
                                </div>
                            </div>

                            <div
                                v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE"
                                class="western-item-text"
                            >
                                [自备]
                            </div>

                            <div class="western-item-text">
                                × {{ formItem.unitCount }}{{ formItem.unit }}
                            </div>

                            <div
                                v-if="displayAst(formItem)"
                                class="western-item-text western-item-ast"
                            >
                                {{ displayAst(formItem) }}
                            </div>
                        </div>
                        <div
                            v-if="formItem.specialRequirement && formItem.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE"
                            class="western-item-remark"
                        >
                            备注：{{ formItem.specialRequirement }}
                        </div>
                    </print-col>

                    <print-col
                        :span="9"
                        class="western-item-usage"
                        overflow
                    >
                        {{ contentConfig.medicalLatin ? 'Sig：' : '每次' }}{{ formItem.dosage }}{{ formItem.dosageUnit }}
                        {{ freqFormat(formItem.freq, contentConfig.medicalLatin) }}&nbsp;
                        {{ usageFormat(formItem.usage, contentConfig.medicalLatin) }}&nbsp;
                        {{ formItem.ivgtt ? formItem.ivgtt : '' }}{{
                            transIvgttUnit(formItem.ivgttUnit, contentConfig.medicalLatin)
                        }}
                        <template v-if="contentConfig.westernMedicineDays">
                            {{ formItem.days }}天
                        </template>
                    </print-col>
                </print-row>
                <print-row
                    v-if="(fIndex < printData.prescriptionWesternForms.length - 1) || showWesternFormLine"
                    :key="`western-form-split-line-${fIndex}`"
                    class="split-form-line"
                ></print-row>
            </template>

            <!-- 输液 需要分组展示 -->
            <template
                v-for="(form,fIndex) in printData.prescriptionInfusionForms"
            >
                <div
                    v-for="(groupFormItems, groupIndex) in getInfusionGroupItems(form.prescriptionFormItems)"
                    :key="`${groupIndex }InForm`"
                    data-type="mix-box"
                >
                    <div
                        :key="`${groupIndex }InFormItem`"
                        class="infusion-group-row"
                        data-type="group"
                    >
                        <div
                            class="infusion-item-left"
                        >
                            <div
                                v-for="item in groupFormItems"
                                :key="`inFormItem${item.id}`"
                                class="infusion-form-item"
                                data-type="item"
                            >
                                <div
                                    style="width: 100%; font-size: 0;"
                                    overflow
                                >
                                    <div
                                        class="product-item-wrapper"
                                        style=" display: inline-flex; gap: 8px; align-items: flex-start; width: 85%; padding-right: 8px;"
                                    >
                                        <div
                                            class="western-item-wrapper"
                                            style=" flex: 1; width: unset;"
                                        >
                                            <div
                                                class="western-item-name"
                                                :style="{ 'max-width': contentConfig.westernMedicineSpec ? '80%' : '100%' }"
                                                overflow
                                            >
                                                <template
                                                    v-if="
                                                        !isSelfPay &&
                                                            contentConfig.westernMedicalFeeGrade &&
                                                            item.productInfo &&
                                                            item.productInfo.medicalFeeGrade
                                                    "
                                                >
                                                    [
                                                    <span>{{ item.productInfo.medicalFeeGrade | medicalFeeGrade2PrintStr }}</span>
                                                    <span v-if="contentConfig.westernOwnExpenseRatio">
                                                        {{ selfPayPropNumber(item.productInfo) }}
                                                    </span>
                                                    ]
                                                </template>
                                                {{ item.name }}
                                            </div>
                                            <div
                                                v-if="contentConfig.westernMedicineSpec"
                                                class="western-item-spec"
                                                overflow
                                            >
                                                {{ item.productInfo | goodsSpec(1) }}
                                            </div>
                                        </div>

                                        <div
                                            v-if="item.chargeType === OutpatientChargeTypeEnum.NO_CHARGE"
                                            class="western-item-text"
                                        >
                                            [自备]
                                        </div>

                                        <div class="western-item-text">
                                            × {{ item.unitCount }}{{ item.unit }}
                                        </div>

                                        <div
                                            v-if="displayAst(item)"
                                            class="western-item-text western-item-ast"
                                        >
                                            {{ displayAst(item) }}
                                        </div>
                                    </div>


                                    <div class="western-item-dosage">
                                        {{ item.dosage }}{{ item.dosageUnit }}
                                    </div>
                                </div>
                                <div
                                    v-if="item.specialRequirement && item.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE"
                                    class="western-item-remark"
                                >
                                    备注：{{ item.specialRequirement }}
                                </div>
                            </div>
                            <div
                                class="infusion-split-line"
                                :style="{'height': `calc(100% - 16pt)`, minHeight: '13pt'}"
                            ></div>
                        </div>
                        <div
                            class="infusion-item-right"
                        >
                            <div
                                class="western-item-usage"
                                overflow
                            >
                                {{ freqFormat(groupFormItems[0].freq, contentConfig.medicalLatin) }} {{ groupFormItems[0].days }}天
                            </div>
                            <div
                                class="western-item-usage"
                                overflow
                            >
                                {{ usageFormat(groupFormItems[0].usage, contentConfig.medicalLatin) }}
                                {{
                                    groupFormItems[0].ivgtt ? groupFormItems[0].ivgtt : ''
                                }}{{ transIvgttUnit(groupFormItems[0].ivgttUnit, contentConfig.medicalLatin) }}
                            </div>
                        </div>
                    </div>
                </div>
                <print-row
                    v-if="fIndex < printData.prescriptionInfusionForms.length - 1 || showInfusionFormLine"
                    :key="`${fIndex}InSplitLine`"
                    class="split-form-line"
                ></print-row>
            </template>

            <!--药品-->
            <div
                v-for="(form, fIndex) in printData.prescriptionChineseForms"
                :key="`${fIndex }CNForm`"
                data-type="mix-box"
                style="margin-bottom: 12pt;"
            >
                <print-row data-type="group">
                    <print-col
                        v-for="(formItem, rowIndex) in form.prescriptionFormItems"
                        :key="`CNFormItem${rowIndex}`"
                        :span="6"
                        class="chinese-form-item"
                        data-type="item"
                    >
                        <div
                            class="chinese-form-name"
                            :class="{'no-requirement': !formItem.specialRequirement}"
                            overflow
                        >
                            {{ formItem.name }}
                        </div>
                        <div
                            class="chinese-form-count"
                            overflow
                        >
                            {{ formItem.unitCount }}{{ formItem.unit || 'g' }}
                        </div>
                        <div
                            v-if="formItem.specialRequirement"
                            class="requirement"
                            overflow
                        >
                            {{ formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE ? '自备' : formItem.specialRequirement }}
                        </div>
                    </print-col>
                    <print-col
                        :span="24"
                        data-type="item"
                        class="chinese-usage"
                    >
                        <span v-if="form.doseCount">共 {{ form.doseCount }} 剂</span>
                        <span>，{{ form.usage }}</span>
                        <span>，{{ form.dailyDosage }}</span>
                        <span>{{ form.freq }}</span>
                        <span>{{ form.usageLevel }}</span>
                        <span v-if="form.usageDays">{{ form.usageDays }}</span>
                        <span v-if="form.requirement && form.requirement.length"> ，{{ form.requirement }} </span>
                    </print-col>
                </print-row>
                <div
                    v-if="fIndex < printData.prescriptionChineseForms.length - 1 || showExternalFormLine"
                    :key="`${fIndex}CnSplitLine`"
                    class="split-form-line"
                ></div>
            </div>

            <!-- 外治处方 -->
            <template
                v-for="form in printData.prescriptionExternalForms"
            >
                <template
                    v-for="(formItem, fIndex) in form.prescriptionFormItems"
                >
                    <print-row
                        :key="`externalFormItem${fIndex}`"
                        class="external-item-name"
                    >
                        {{ fIndex + 1 }}. {{ formItem.name }}
                    </print-row>
                    <print-row
                        :key="`externalFormItemInfo${fIndex}`"
                    >
                        <print-col
                            v-for="(acupoint, rowIndex) in formItem.externalGoodsItems"
                            :key="`externalFormItemName${rowIndex}`"
                            :span="6"
                            overflow
                            class="external-goods"
                        >
                            <span
                                class="external-goods-name"
                                overflow
                                :class="{ 'has-long-name': acupoint.name.length > 6 }"
                            >{{ acupoint.name }}</span>
                            <span
                                class="external-goods-count"
                                style="margin-left: 4pt; font-size: 10pt;"
                            >{{ acupoint.unitCount }}{{ acupoint.unit }}</span>
                        </print-col>
                    </print-row>
                    <print-row
                        v-if="formatAcupoints(formItem.acupoints)"
                        :key="`externalFormItemAcuPoints${fIndex}`"
                        data-type="mix-box"
                        style="margin-bottom: 6pt;"
                    >
                        <print-col
                            :span="24"
                            class="label-common"
                        >
                            <span class="label">
                                {{ getExternalTypeStr(form, formItem) }}
                            </span>
                            {{ formatAcupoints(formItem.acupoints) }}
                        </print-col>
                    </print-row>
                    <print-row
                        :key="`externalFormItemUsage${fIndex}`"
                        data-type="mix-box"
                        class="external-item-usage"
                    >
                        <print-col :span="24">
                            <span class="label">用法：</span>
                            <template v-if="formItem.unitCount">
                                共 {{ formItem.dosage }} 次
                            </template>
                            <template v-if="formItem.freq">
                                ，{{ formItem.freq }}
                            </template>
                            <template
                                v-if="formItem.specialRequirement"
                            >
                                ，{{ formItem.specialRequirement }}
                            </template>
                        </print-col>
                    </print-row>
                </template>
            </template>
        </template>

        <template v-if="contentConfig.doctorAdvice && medicalRecord.doctorAdvice">
            <doctor-advice
                is-medical-record
                :doctor-advice="doctorAdvice"
            ></doctor-advice>
        </template>

        <next-is-blank></next-is-blank>

        <div data-type="footer">
            <outpatient-footer
                :print-data="printData"
                :config="config"
                print-type="medical"
            ></outpatient-footer>
        </div>
    </div>
</template>


<script>
    import { formatAst, formatAstResult, getExternalTypeStr } from "./common/medical-transformat.js";
    import {
        formatAge,
        formatMoney,
        formatTreatmentUnit,
        getSelfPayPropNumber,
        goodsSpec,
        medicalFeeGrade2PrintStr,
        splitByBr,
        updateEyeInspectItemsPrintable
    } from "./common/utils.js";
    import PrintCol from "./components/layout/print-col.vue";
    import PrintRow from "./components/layout/print-row.vue";
    import OutpatientFooter from './components/medical-document-footer/index.vue';
    import OutpatientHeader from './components/medical-record/header.vue';
    import MedicalRecordDataHandler from "./data-handler/medical-record-handler.js";

    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { CLINIC_TYPE, MedicalRecordTypeEnum, SourceFormTypeEnum } from "./common/constants.js";
    import {
        formatAcupoints,
        formatDentistry2Html,
        formatEpidemiologicalHistory2Str,
        formatEyeExamination2Html,
        formatObstetricalHistory2Str,
        formatOralExamination2Html,
        freqFormat,
        getDoctorAdviceArray,
        getInfusionGroupItems,
        transformProductUnit,
        transIvgttUnit,
        usageFormat
    } from "./common/medical-transformat.js";
    import DoctorAdvice from './components/prescription/doctor-advice/index.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";

    import { formatToothNos2Html } from "./common/medical-transformat.js";
    import EyeInspectMergeItemTable from "./components/eye-inspect-report/merge-item-table.vue";
    import AbcPrintSpace from "./components/layout/space.vue";
    import EyeInspectTable from "./components/medical-record/eye-inspect-table.vue";
    import { ExternalPRUsageTypeEnum } from "./constant/external-constants";
    import { OutpatientChargeTypeEnum } from "./constant/print-constant.js";
    import NextIsBlank from './components/next-is-blank/index.vue';

    export default {
        DataHandler: MedicalRecordDataHandler,
        components: {
            NextIsBlank,
            EyeInspectMergeItemTable,
            AbcPrintSpace,
            EyeInspectTable,
            OutpatientHeader,
            OutpatientFooter,
            PrintCol,
            PrintRow,
            DoctorAdvice,
        },
        filters: {
            goodsSpec,
            formatAst,
            formatAstResult,
            medicalFeeGrade2PrintStr,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
            options: {
                type: Object,
                default() {
                    return {}
                }
            },
            injectConfig: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        data() {
            return {
                OutpatientChargeTypeEnum,
                SourceFormTypeEnum,
                eyeInspectTableLength: 1,
                getExternalTypeStr
            }
        },
        businessKey: PrintBusinessKeyEnum.MEDICAL_RECORD,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM95_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分' // 默认选择的等分纸
            },
        ],
        computed: {
            clinicMrConfig() {
                return this.injectConfig && this.injectConfig.clinicMrConfig || {};
            },
            getMrTypePropertyKey() {
                return this.injectConfig && this.injectConfig.getMrTypePropertyKey;
            },
            getOralExamKey() {
                return this.injectConfig && this.injectConfig.getOralExamKey;
            },
            viewDistributeOutpatientConfig() {
                return this.injectConfig && this.injectConfig.viewDistributeOutpatientConfig;
            },
            sortedMrStruct() {
                const {
                    multiMedicalRecord, defaultMedicalRecordType,
                } = this.viewDistributeOutpatientConfig || {};
                if (multiMedicalRecord === false) {
                    return this.clinicMrConfig[this.getMrTypePropertyKey()[defaultMedicalRecordType]];
                }
                return this.clinicMrConfig[this.getMrTypePropertyKey()[this.medicalRecord.type ?? 0]]
            },
            printData() {
                return this.renderData.printData || {};
            },
            isSelfPay() {
                return this.printData.healthCardPayLevel === '自费';
            },
            isLandscape() {
                return this.options.page?.orientation  === Orientation.landscape;
            },
            config() {
                if (this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.medical) {
                    return this.renderData.config.medicalDocuments.medical;
                }
                return {};
            },
            headerConfig() {
                return this.config && this.config.header || {};
            },
            contentConfig() {
                return this.config && this.config.content || {}
            },
            styleConfig() {
                return this.config && this.config.style || {}
            },
            organ() {
                return this.printData && this.printData.organ;
            },
            organTitle() {
                if (!this.headerConfig.title) {
                    return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.medical || '';
                }
                return this.headerConfig.title;
            },
            clinicName() {
                return this.organ.name;
            },
            patient() {
                return this.printData.patient;
            },
            medicalRecord() {
                return this.printData.medicalRecord;
            },
            chiefComplaintList() {
                return splitByBr(this.medicalRecord.chiefComplaint);
            },
            presentHistoryList() {
                return splitByBr(this.medicalRecord.presentHistory);
            },
            pastHistoryList() {
                return splitByBr(this.medicalRecord.pastHistory);
            },
            wearGlassesHistoryList() {
                return splitByBr(this.medicalRecord.wearGlassesHistory);
            },
            personalHistoryList() {
                return splitByBr(this.medicalRecord.personalHistory);
            },
            allergicHistoryList() {
                return splitByBr(this.medicalRecord.allergicHistory);
            },
            familyHistoryList() {
                return splitByBr(this.medicalRecord.familyHistory);
            },
            obstetricalHistory() {
                return formatObstetricalHistory2Str(this.medicalRecord.obstetricalHistory);
            },
            obstetricalHistoryList() {
                return splitByBr(this.obstetricalHistory);
            },
            formatEpidemiologicalHistory() {
                return formatEpidemiologicalHistory2Str(this.medicalRecord.epidemiologicalHistory);
            },
            epidemiologicalHistoryList() {
                return splitByBr(this.formatEpidemiologicalHistory);
            },
            physicalExaminationList() {
                return splitByBr(this.medicalRecord.physicalExamination);
            },
            chineseExaminationList() {
                return splitByBr(this.medicalRecord.chineseExamination);
            },
            tongueList() {
                return splitByBr(this.medicalRecord.tongue);
            },
            pulseList() {
                return splitByBr(this.medicalRecord.pulse);
            },
            formatDentistryExaminations() {
                return formatDentistry2Html(this.medicalRecord.dentistryExaminations);
            },
            formatDentistryExaminationsList() {
                return splitByBr(this.formatDentistryExaminations);
            },
            formatOralExamination() {
                return formatOralExamination2Html(this.medicalRecord.oralExamination);
            },
            formatOralExaminationList() {
                return splitByBr(this.formatOralExamination);
            },
            formatAuxiliaryExaminations() {
                return formatDentistry2Html(this.medicalRecord.auxiliaryExaminations);
            },
            formatAuxiliaryExaminationsList() {
                return splitByBr(this.formatAuxiliaryExaminations);
            },
            syndromeTreatmentList() {
                return splitByBr(this.medicalRecord.syndromeTreatment);
            },
            extendDiagnosisInfos() {
                return formatDentistry2Html(this.medicalRecord.extendDiagnosisInfos);
            },
            extendDiagnosisInfosList() {
                return splitByBr(this.extendDiagnosisInfos);
            },
            syndromeList() {
                const syndrome = this.medicalRecord.syndrome;
                return splitByBr(syndrome);
            },
            formatTreatmentPlans() {
                return formatDentistry2Html(this.medicalRecord.treatmentPlans);
            },
            formatTreatmentPlansList() {
                return splitByBr(this.formatTreatmentPlans);
            },
            therapyList() {
                return splitByBr(this.medicalRecord.therapy);
            },
            chinesePrescriptionList() {
                return splitByBr(this.medicalRecord.chinesePrescription);
            },
            formatDisposals() {
                return formatDentistry2Html(this.medicalRecord.disposals);
            },
            formatDisposalsList() {
                return splitByBr(this.formatDisposals);
            },
            targetList() {
                return splitByBr(this.medicalRecord.target);
            },
            prognosisList() {
                return splitByBr(this.medicalRecord.prognosis);
            },
            doctorAdvice() {
                return getDoctorAdviceArray(this.printData.medicalRecord.doctorAdvice);
            },
            showProductFormLine() {
                return this.printData.prescriptionWesternForms.length
                    || this.printData.prescriptionInfusionForms.length
                    || this.printData.prescriptionChineseForms.length
                    || this.printData.prescriptionExternalForms.length;
            },
            showWesternFormLine() {
                return this.printData.prescriptionInfusionForms.length
                    || this.printData.prescriptionChineseForms.length
                    || this.printData.prescriptionExternalForms.length;
            },
            showInfusionFormLine() {
                return this.printData.prescriptionChineseForms.length
                    || this.printData.prescriptionExternalForms.length;
            },
            showExternalFormLine() {
                return this.printData.prescriptionExternalForms.length;
            },
            isDentistry() {
                return this.organ &&
                    ( this.organ.hisType === CLINIC_TYPE.DENTISTRY
                        || (this.organ.hisType === CLINIC_TYPE.HOSPITAL && this.medicalRecordType === MedicalRecordTypeEnum.ORAL));
            },
            isDentistryExaminationsLabel() {
                if (this.getOralExamKey) {
                    return this.getOralExamKey(this.medicalRecordType) === 'dentistryExaminations';
                }
                return this.isDentistry;
            },
            medicalRecordType() {
                return this.medicalRecord.type;
            },
            currentPageIsA5() {
                return this.options.page.size === 'A5';
            },
            currentPageIsA4() {
                return this.options.page.size === 'A4';
            },
        },
        mounted() {
            console.log(this.options, 'this.options');
        },
        methods: {
            formatAge,
            freqFormat,
            usageFormat,
            formatTreatmentUnit,
            transIvgttUnit,
            formatMoney,
            getInfusionGroupItems,
            formatObstetricalHistory2Str,
            formatEpidemiologicalHistory2Str,
            formatOralExamination2Html,
            formatDentistry2Html,
            formatEyeExamination2Html,
            formatAcupoints,
            formatToothNos2Html,
            transformProductUnit,
            formatPatientOrderNo(value = '') {
                let srcStr = '00000000';
                if (!value)
                    return srcStr;
                return (srcStr + ('' + value)).slice(-8);
            },
            selfPayPropNumber(goods) {
                return getSelfPayPropNumber(goods, this.printData.shebaoCardInfo);
            },
            isBasedOnAcupoint(form) {
                return [
                    ExternalPRUsageTypeEnum.tieFu,
                    ExternalPRUsageTypeEnum.zhenCi,
                    ExternalPRUsageTypeEnum.aiJiu,
                ].includes(form.usageType);
            },
            getItemsValue() {
                return updateEyeInspectItemsPrintable(this);
            },
            displayAst(item) {
                let str = '';
                const ast = formatAst(item.ast);
                if (ast) {
                    str += `${ast}`;
                }
                if (item.ast === 1) {
                    str += '(';
                    const astResult = formatAstResult(item.astResult);
                    if (astResult) {
                        str += `${astResult}`;
                    }
                    str += ')';
                }
                return str;
            },
            productItemClass(formItem, index) {
                if (this.isLandscape && formItem.composeType !== 1) {
                    if (index % 2 === 0) {
                        return 'pd-r-12'
                    } else {
                        return 'pd-l-12'
                    }
                }
            },
            /**
             * 生成诊疗项目用法信息
             */
            generateDiagnosisUsageInfo(form, formItem) {
                const renderUnit = transformProductUnit(form , formItem);
                // 治疗理疗
                if(!this.isDentistry && form.sourceFormType === SourceFormTypeEnum.TREATMENT) {
                    return [
                        formItem.dailyDosage ? `${formItem.freq ? '每次' : '每天'}${ formItem.dailyDosage }${ renderUnit }` : '',
                        `${ formItem.freq || '' }`,
                        formItem.days ? `${ formItem.days }天` : '',
                        `共${ formItem.unitCount }${ renderUnit }`
                    ].filter(item => !!item).join('，');
                }

                return `共${ formItem.unitCount }${ renderUnit }`
            }
        }
    };
</script>


<style lang="scss">
@import "./components/layout/print-layout.scss";
@import "./style/reset.scss";

.abc-page-content {
    box-sizing: border-box;
    padding: 8pt;
    overflow: hidden;
    font-family: "Microsoft YaHei", 微软雅黑;

    div {
        box-sizing: border-box;
    }
}

.medical-header {
    margin-bottom: 6pt;
}

.print-col {
    font-size: 10pt;
    line-height: 12pt;
}

.no-right-border {
    padding: 1pt 0;
}

.medical-item {
    position: relative;
    min-height: 12pt;
    padding-left: 60pt;
    margin-bottom: 6pt;
    font-size: 10pt;
    line-height: 12pt;

    .label-title {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        box-sizing: border-box;
        width: 60pt;
        font-size: 0;

        .label {
            position: relative;
            display: inline-block;
            width: 50pt;
            font-size: 10pt;
            font-weight: bold;
            line-height: 12pt;
            text-align: justify;
            text-align-last: justify;
            text-justify: distribute;
            word-break: break-all;

            &::after {
                display: inline-block;
                width: 100%;
                content: '';
            }

            & + span {
                position: absolute;
                top: 0;
                right: 0;
                font-size: 10pt;
                line-height: 12pt;
                vertical-align: inherit;
            }
        }
    }

    .infos {
        font-size: 10pt;
        font-weight: 300;
        line-height: 12pt;

        &.oral-examination-wrapper {
            span {
                display: inline-block;
                padding: 1pt;
                font-family: Roboto;
                font-size: 8pt;
                line-height: 8pt;
            }

            span.top-left {
                border-right: 1pt solid #878c92;
                border-bottom: 1pt solid #878c92;
            }

            span.top-right {
                border-bottom: 1pt solid #878c92;
                border-left: 1pt solid #878c92;
            }

            span.bottom-left {
                border-top: 1pt solid #878c92;
                border-right: 1pt solid #878c92;
            }

            span.bottom-right {
                border-top: 1pt solid #878c92;
                border-left: 1pt solid #878c92;
            }
        }

        &.obstetrical-history {
            .menstruation {
                display: inline-flex;
                align-items: center;

                > span {
                    display: inline-flex;
                    flex-direction: column;
                    margin: 0 4pt;
                    text-align: center;

                    span {
                        height: 9pt;
                        font-size: 9pt;
                        line-height: 9pt;
                    }

                    .frasl {
                        width: 100%;
                        height: 0;
                        border-bottom: 1pt solid rgba(0, 0, 0, 0.5);
                    }
                }
            }
        }
    }
}

.product-form-item {
    margin-bottom: 6pt;
    font-weight: 300;

    &.pd-r-12 {
        padding-right: 12pt;
    }

    &.pd-l-12 {
        padding-left: 12pt;
    }
}

.split-form-line {
    margin-bottom: 6pt;
    border-bottom: 1px dashed #000000;
}

.western-form-item {
    margin-bottom: 6pt;
    font-weight: 300;
}

.infusion-form-item:not(:last-child) {
    margin-bottom: 6pt;
}

.infusion-item-left,
.infusion-item-right {
    position: relative;
    display: inline-block;
    font-size: 10pt;
    font-weight: 300;
    line-height: 12pt;
    vertical-align: middle;
}

.infusion-group-row {
    padding-bottom: 6pt;
    font-size: 0;
}

.infusion-item-left {
    position: relative;
    width: 75%;

    .infusion-split-line {
        position: absolute;
        top: 6pt;
        right: 0;
        width: 2pt;
        border-top: 1px solid #000000;
        border-right: 1px solid #000000;
        border-bottom: 1px solid #000000;
    }
}

.infusion-item-right {
    width: 24%;
    padding-left: 4pt;
}

.western-item,
.chinese-form-item {
    font-size: 0;
    font-weight: 300;
}

.chinese-form-item {
    position: relative;
    margin-bottom: 6pt;
    overflow: hidden;
    line-height: initial;
    background-color: #ffffff;

    .chinese-form-name,
    .chinese-form-count,
    .requirement {
        display: inline-block;
        *display: inline;
        overflow: hidden;
        font-size: 10pt;
        line-height: 12pt;
        word-break: break-all;
        white-space: nowrap;
    }

    .chinese-form-name {
        width: auto;
        max-width: 48%;

        &.no-requirement {
            max-width: 69%;
        }
    }

    .chinese-form-count {
        width: 30%;
        max-height: 12pt;
        padding-left: 2pt;
        overflow: hidden;
        word-break: break-all;
    }

    .requirement {
        width: 22%;
    }
}

.external-goods {
    margin-bottom: 6pt;
    font-size: 0;
    font-weight: 300;

    .external-goods-name,
    .external-goods-count {
        display: inline-block;
        *display: inline;
        overflow: hidden;
        font-size: 10pt;
        line-height: 12pt;
        word-break: keep-all;
        white-space: nowrap;
        vertical-align: top;
    }

    .external-goods-name {
        width: auto;
        max-width: 75%;
    }

    .external-goods-count {
        max-width: 24%;
        margin-left: 4pt;
    }
}

.western-item-name,
.product-item-wrapper,
.western-item-wrapper,
.western-item-spec,
.western-item-count,
.western-item-dosage,
.western-item-text,
.external-item-name {
    display: inline-block;
    *display: inline;
    overflow: hidden;
    font-size: 10pt;
    font-weight: 300;
    line-height: 12pt;
    word-break: keep-all;
    white-space: nowrap;
    vertical-align: top;
    *zoom: 1;
}

.western-item-ast {
    width: 44px;
    min-width: 44px;
    max-width: 44px;
    text-align: right;
}

.western-item-wrapper {
    width: 74%;
    font-size: 0;
}

.external-item-acupoints {
    font-size: 10pt;
    font-weight: 300;
    line-height: 14pt;
}

.label-common {
    position: relative;
    padding-left: 36pt;
    margin-top: 6pt;
    font-size: 10pt;
    font-weight: 300;
    line-height: 12pt;

    .label {
        position: absolute;
        top: 0;
        left: 0;
        width: 36pt;
    }
}

.western-item-usage,
.external-item-usage,
.chinese-usage {
    overflow: hidden;
    font-size: 10pt;
    font-weight: 300;
    line-height: 12pt;
    word-break: keep-all;
    white-space: nowrap;
}

.external-item-usage {
    margin-bottom: 12pt;
}

.chinese-usage {
    padding-bottom: 6pt;
    overflow: visible !important;
    word-break: unset !important;
    white-space: normal !important;
}

.western-item-name {
    max-width: 80%;
}

.western-item-spec {
    min-width: 20%;
}

.western-item-count {
    width: 26%;
}

.western-item-dosage {
    width: 13%;
    text-align: right;
}

.western-item-remark {
    margin-top: 2pt;
    font-size: 9pt;
    font-weight: 300;
}

.next-printData {
    margin: 2pt 0;
    font-size: 9pt;
    text-align: center;
}

.__is-split_end__ {
    .split-form-line {
        display: none;
    }
}

.global-tooth-selected-quadrant {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    width: auto;
    min-width: 32pt;
    height: 17pt;
    padding-top: 2px;
    vertical-align: middle;

    .top-tooth,
    .bottom-tooth {
        display: flex;
        align-items: center;
        width: 100%;
        min-width: 32pt;
    }

    .left-tooth,
    .right-tooth {
        display: flex;
        align-items: center;
        width: 50%;
        height: 7pt;
        padding: 0 1pt;
        font-family: 'MyKarlaRegular';
        font-size: 9pt;
        letter-spacing: 1px;
        user-select: none;
    }

    .left-tooth {
        justify-content: flex-end;
        border-right: 1pt solid #7a8794;
    }

    .top-tooth {
        min-width: 32pt;
        border-bottom: 1pt solid #7a8794;

        > div {
            padding-bottom: 1px;
        }
    }

    .bottom-tooth {
        > div {
            padding-top: 1px;
        }
    }

    &.all-tooth {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }

    .all-tooth {
        min-width: 18pt;
        height: 11pt;
        font-size: 9pt;
        line-height: 11pt;
    }

    &.no-data {
        .left-tooth {
            border-right: 1px dashed #000000;
        }

        .top-tooth {
            border-bottom: 1px dashed #000000;
        }
    }
}

.medical-record-eye-inspect-table {
    &.page-is-a4 {
        tbody {
            .table-cell {
                font-weight: 300;
            }
        }
    }
}

.medical-record-product-compose-item-text-right {
    text-align: right;
}

</style>
