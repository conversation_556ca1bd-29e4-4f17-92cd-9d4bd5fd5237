<template>
    <div>
        <template 
            v-for="(report,idx) in examinationReportList"
        >
            <examination-report-header
                :key="`examination-report-header-${idx}`"
                :print-data="report"
                :header-config="headerConfig"
                :organ-title="organTitle"
                data-type="header"
                :data-pendants-index="`${idx}examinationReport`"
            ></examination-report-header>

            <examination-table
                :key="`examination-table-${idx}`"
                :list="report.items"
                is-single-column
                :content-config="contentConfig"
            ></examination-table>

            <div
                v-if="contentConfig.comment"
                :key="`examination-report-remark-${idx}`"
                style=" margin: 10pt 4pt 20pt; font-size: 9pt;"
                data-type="mix-box"
            >
                备注：{{ report.remark }}
            </div>
            
            <!-- 图片 -->
            <template v-if="contentConfig.bloodHistogram">
                <examination-images
                    :key="`examination-report-images-${idx}`"
                    data-type="fixed-bottom-box"
                    size="medium"
                    :list="report.attachments"
                ></examination-images>
            </template>

            <examination-report-footer
                :key="`examination-report-footer-${idx}`"
                :print-data="report"
                data-type="footer"
                print-type="examination"
                :config="config"
                :data-pendants-index="`${idx}examinationReport`"
            ></examination-report-footer>
        </template>
    </div>
</template>

<script>
    import ExaminationReportHeader from './components/examination-report-header/index.vue';
    import ExaminationReportFooter from './components/examination-report-footer/index.vue';
    import ExaminationTable from "./components/examination-report-landscape/examination-landscape-table.vue";
    import ExaminationImages from "./components/examination-report-landscape/examination-landscape-images.vue";

    import PrintCommonHandler from './data-handler/examination-report-handler.js'

    import { calRange, calValue2 } from "./common/medical-transformat.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import clone from './common/clone.js';

    export default {
        DataHandler: PrintCommonHandler,
        name: "ExaminationReport",
        components: {
            ExaminationReportHeader,
            ExaminationReportFooter,
            ExaminationTable,
            ExaminationImages,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        businessKey: PrintBusinessKeyEnum.EXAMINATION_REPORT,
        imageTransformSetting: 'x-oss-process=image/resize,p_90',
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM95_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            printData() {
                console.log(this.renderData, 'this.renderData');

                return this.renderData.printData || null;
            },

            config() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.examineReport) {
                    return this.renderData.config.medicalDocuments.examineReport;
                }
                return {};
            },

            headerConfig() {
                const originConfig = this.config.header || {};
                // 自定义logo的门店列表
                const customLogoClinicIdList = [
                    'ffffffff0000000034a7aff0c70f8002', // 包头稀土高新区稀土路街道办事处社区卫生服务中心
                ]
                
                return {
                    ...originConfig,
                    isCustomLogo: customLogoClinicIdList.includes(this.printData?.organPrintView?.id),
                };
            },

            contentConfig() {
                console.log(this.config);
                return this.config && this.config.content || {};
            },
           
            organ() {
                return this.printData.organPrintView;
            },

            organTitle() {
                return this.organ && this.organ.name;
            },

            // 如果合并的检验项目需要拆分打印，拆分合并的项目，单独生成一张报告
            examinationReportList() {
                const {
                    isMerge,printProjectIdListOnMergeMode,
                } = this.renderData.printOptions;

                const createExaminationPrintReportData = (items, name) => {
                    const data = clone(this.printData);
                    name && (data.name = name);
                    data.attachments = items.filter(item => item.valueType === 'IMAGE') || [];
                    data.items = items.filter(item => item.valueType !== 'IMAGE') || [];

                    return data;
                }


                if(isMerge) {
                    const items = (this.printData.itemsValue || []).filter((item) =>
                        printProjectIdListOnMergeMode.includes(item.groupById),
                    )
                    return [
                        createExaminationPrintReportData(items),
                    ];
                } else {
    
                    // 所有指标
                    const items = this.printData.itemsValue || [];
    
                    // 根据 groupById 拆分
                    const res = items.reduce((res, item) => {
                        const groupItem = res.find(r => r.groupById === item.groupById);
    
                        if(groupItem !== undefined) {
                            groupItem.items.push(item);
                        } else {
                            const groupItem = {
                                groupBy: item.groupBy,
                                groupById: item.groupById,
                                items: [ item ],
                            }
    
                            res.push(groupItem);
                        }
    
                        return res;
                    }, [])
    
                    return res.map(groupItem => {
                        return createExaminationPrintReportData(
                            groupItem.items,
                            groupItem.groupBy,
                        );
                    })
                }
            },
        },
        methods: {
            calValue2,
            calRange,
        },
    }
</script>

<style lang="scss">
@import "./style/reset.scss";
@import "./components/layout/print-layout.scss";

.abc-page-content {
    box-sizing: border-box;
    padding: 8pt;
    font-family: "Microsoft YaHei", "微软雅黑";
}
</style>

<!--exampleData
{
  "id": "3795524689198792705",
  "goodsType": 3,
  "type": 1,
  "subType": 0,
  "patientId": "672543ad35c511e998496c92bf5d5878",
  "patientOrderId": "ffffffff0000000034ac6998220dc000",
  "examinationApplySheetId": "3795524688661921794",
  "wardAreaId": null,
  "relationPatientOrderId": null,
  "chargeSheetId": "ffffffff0000000034ac699846120003",
  "chargeFormItemId": "ffffffff0000000034ac699846120006",
  "chargeSheetType": 2,
  "outpatientFormItemId": "ffffffff0000000034ac69982372c002",
  "organPrintView": null,
  "barCode": 17619,
  "businessType": 10,
  "orderNo": "JY202401120004",
  "sampleNo": "JY202401120004",
  "status": 0,
  "sampleStatus": 0,
  "doctorId": "6e45706922a74966ab51e4ed1e604641",
  "doctorName": "宁铁桥",
  "sellerId": "6e45706922a74966ab51e4ed1e604641",
  "sellerName": "宁铁桥",
  "doctorDepartmentId": "2d2d0cb8f61f4301afaaf689d62b51c4",
  "doctorDepartmentName": "内科急诊",
  "sellerDepartmentId": "2d2d0cb8f61f4301afaaf689d62b51c4",
  "sellerDepartmentName": "内科急诊",
  "executeDepartmentId": "",
  "executeDepartmentName": null,
  "attachments": [],
  "remark": null,
  "itemsValue": [
    {
      "id": "ffffffff0000000022e9f460106ac000",
      "goodsId": "ffffffff00000000229680d0115c2000",
      "goodsName": "单项模板219",
      "type": 1,
      "name": "单项模板219单项模板219单项模板219单项模板219",
      "ref": "{\"min\":\"1\",\"max\":\"10\"}",
      "refDetails": [
        {
          "itemId": "ffffffff0000000022e9f460106ac000",
          "ageUnit": "岁",
          "ref": "{\"min\":\"1\",\"max\":\"10\"}"
        }
      ],
      "itemCode": "000034",
      "itemType": 1,
      "enName": "aaa",
      "resultDisplayScale": 2,
      "value": "1222222222222212222222222222",
      "valueType": "STRING",
      "groupBy": "检验1017",
      "groupById": 0,
      "examinationSheetId": "ffffffff0000000034ac699843eb4001"
    },
    {
      "id": "ffffffff0000000022f658f0106d2000",
      "goodsId": "ffffffff000000002296e058115d4000",
      "goodsName": "jxf新建模版单项",
      "type": 1,
      "name": "jxf新建模版单项",
      "ref": "{\"min\":\"\",\"max\":\"\"}",
      "refDetails": [
        {
          "itemId": "ffffffff0000000022f658f0106d2000",
          "ageUnit": "岁",
          "ref": "{\"min\":\"\",\"max\":\"\"}"
        }
      ],
      "enName": "bbb",
      "itemCode": "000036",
      "itemType": 1,
      "resultDisplayScale": 2,
      "value": null,
      "valueType": "STRING",
      "groupBy": "检验1017",
      "groupById": 0,
      "examinationSheetId": "ffffffff0000000034ac699843eb4001"
    },
    {
      "id": "ffffffff00000000349acec8e36c4000",
      "goodsId": "ffffffff00000000349acec8ca21c000",
      "goodsName": "检验单项0927",
      "type": 3,
      "name": "检验单项0927",
      "ref": "阴性",
      "refDetails": [
        {
          "itemId": "ffffffff00000000349acec8e36c4000",
          "ageUnit": "岁",
          "ref": "阴性"
        }
      ],
      "itemCode": "000199",
      "itemType": 1,
      "resultDisplayScale": 2,
      "value": null,
      "valueType": "STRING",
      "groupBy": "检验组合0927",
      "groupById": 1,
      "examinationSheetId": "ffffffff0000000034ac69a763eb4000"
    }
  ],
  "deviceData": null,
  "preItemsValue": [
    {
      "id": null,
      "goodsId": null,
      "type": null,
      "name": null,
      "ref": "{\"min\":\"\",\"max\":\"\"}",
      "itemCode": null,
      "itemType": null,
      "value": null,
      "valueType": "STRING"
    },
    {
      "id": null,
      "goodsId": null,
      "type": null,
      "name": null,
      "ref": "{\"min\":\"\",\"max\":\"\"}",
      "itemCode": null,
      "itemType": null,
      "value": null,
      "valueType": "STRING"
    },
    {
      "id": null,
      "goodsId": null,
      "type": null,
      "name": null,
      "ref": "{\"min\":\"\",\"max\":\"\"}",
      "itemCode": null,
      "itemType": null,
      "value": null,
      "valueType": "STRING"
    }
  ],
  "examinationHistories": [],
  "created": "2024-01-12T01:54:11Z",
  "orderByDate": "2024-01-12T01:54:11Z",
  "lastModifiedBy": "6e45706922a74966ab51e4ed1e604641",
  "patientOrderNumber": "24583",
  "wardAreaName": null,
  "bedNumber": null,
  "testerId": null,
  "testerName": null,
    "tester": {
        'id': 'ffffffff0000000034909cbbff6c0000',
        'name': '曾龙',
        'mobile': '17628286728',
        'countryCode': null,
        'status': 1,
        'shortId': '3787699617876672512',
        'namePy': 'zenglong|cenglong',
        'namePyFirst': 'ZL|CL',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001db97db800450000/doctor/WX20231103-210520%402x_RkTr8vXKTyyH.png',
        'handSign': '曾龙',
        'wechatOpenIdMp': 'o_yu51AgPtvd_uAoQ9nnxkHhhOmw',
        'wechatNickName': '',
    },
  "testTime": null,
  "sampleType": "血清",
  "checkerId": null,
  "checkerName": null,
  "checker": {
        'id': 'ffffffff0000000034909cbbff6c0000',
        'name': '曾龙',
        'mobile': '17628286728',
        'countryCode': null,
        'status': 1,
        'shortId': '3787699617876672512',
        'namePy': 'zenglong|cenglong',
        'namePyFirst': 'ZL|CL',
        'headImgUrl': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff000000001db97db800450000/doctor/WX20231103-210520%402x_RkTr8vXKTyyH.png',
        'handSign': '曾龙',
        'wechatOpenIdMp': 'o_yu51AgPtvd_uAoQ9nnxkHhhOmw',
        'wechatNickName': '',
    },
  "checkTime": null,
  "reportTime": null,
  "chargeFormItemStatus": 0,
  "canExecute": 1,
  "diagnosis": null,
  "patient": {
    "id": "672543ad35c511e998496c92bf5d5878",
    "name": "徐俊芳",
    "namePy": "xujunfang",
    "namePyFirst": "XJF",
    "mobile": "13880799689",
    "countryCode": null,
    "sex": "女",
    "birthday": "1990-01-01",
    "age": {
      "year": 34,
      "month": 0,
      "day": 11
    },
    "isMember": 0,
    "idCard": null,
    "marital": null,
    "weight": null,
    "importFlag": 0,
    "ethnicity": null,
    "nationality": null,
    "contactName": null,
    "contactRelation": null,
    "contactMobile": null,
    "sn": "162532",
    "remark": null,
    "profession": "",
    "company": null,
    "companyMobile": null,
    "blockFlag": 0,
    "address": {
      "addressCityId": null,
      "addressCityName": null,
      "addressDetail": "13",
      "addressDistrictId": null,
      "addressDistrictName": null,
      "addressGeo": null,
      "addressProvinceId": null,
      "addressProvinceName": null,
      "addressPostcode": null
    },
    "familyMobile": null,
    "tags": null,
    "activeDate": "2024-01-09T07:15:00.000+00:00",
    "activeClinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "lastOutpatientDate": "2024-01-09T07:06:48.000+00:00",
    "lastOutpatientClinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "wxOpenId": null,
    "unionId": null,
    "wxUserId": null,
    "wxNickName": null,
    "wxHeadImgUrl": null,
    "isWxMainPatient": 0,
    "wxBindStatus": 0
  },
  "registrationFormItem": null,
  "examinationSheetReport": {
    "id": "ffffffff0000000034ac699843eb4001",
    "goodsType": 3,
    "type": 1,
    "subType": 0,
    "deviceType": 1,
    "imageFiles": [],
    "method": null,
    "videoDescription": null,
    "resultInfo": null,
    "advice": null,
    "suggestion": null,
    "diagnosisFlag": null,
    "recordDoctorId": null,
    "consultationDoctorId": null,
    "principalDoctorId": null,
    "recordDoctorName": null,
    "consultationDoctorName": null,
    "principalDoctorName": null
  },
  "samplePipe": {
    "id": "3790615264659128321",
    "code": "#12",
    "name": "无聊组",
    "pipeName": "红色管",
    "sampleType": "血清",
    "color": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAiZJREFUSEvdlk9r03AYx7+//GvWOZK1CC3dKPaiJ8Ely9hF0ZMvQhFBvO+6F+Bh78CDMNAX4c32JA2/bnjTk2MptFkCSw+i/ZNOnsxIFqYZLKlgICTkz+/D9/l+nydhyNjODENDqXQAVW1haQnRXi4DjQawugocHwOeB/j+EcLwHmu3g78tyTKBm5v3IUmdCKKq5zA6rq2lgcBo9JBx3r4+UJY7kTICpRU6DnByQgqBIHjEer0P+QMJepnC3IDJksY+rq8Dup70MGeFyXLSOQE1Dej3z0tKwRmNCirp5SnNUWG6pGkgBYYU5uZhOqWFhmZr6y4E4dPvpv9z45OHG4zzw2u1Bb3s7uy8lhqNlzdWVhACOAMglMsoLS/jexDg2+kpmOu+ubm39yJrkGROGlqg2+0+q1Qq+/V6HdPpFJPJBLIsQ9M0+L6P4XCI8Xj83LKs/VyBtVoNs9ksgoqiCF3X4XkeXNctBkgKSR0BF6LwnwBjDyVJikpauIcEJB8JWHhoFp7S2ENSGKf0/y1pHJpC+5Aaf+GhocZPeliIQtu2n+i6/pZCQ7D0LB0MBnTtqWVZ73KZpZzzDUVReq1WKyppcpZSSvv0mwGYhmH08gLKYRh+aTabtxRFuTBLHceh4f1VFMXbpmlOcwHSIrZtP2CMva9Wq6qqqtGkCcMQjuP8APDYsqxOFozuX+l7GC/EOb8zn89fAdj+de2jIAi7pml+vgqMnvkJYv3OLM20NdYAAAAASUVORK5CYII=",
    "status": 0,
    "goodsId": "ffffffff00000000349af87fea2e4000",
    "goodsSnap": {},
    "institutionName": "2艾迪康1223"
  },
  "deviceModelId": "1",
  "deviceRoomId": null,
  "deviceView": {
    "id": "1",
    "deviceModelId": "1",
    "name": "迈瑞全自动血细胞分析仪BC-21",
    "model": "BC-21",
    "deviceUuid": "迈瑞·BC-21",
    "manufacture": "迈瑞",
    "iconUrl": "https://cd-cis-static-common.oss-cn-chengdu.aliyuncs.com/img/lis/instrument-image/bc-21.jpg",
    "buyUrl": "https://abcyun.cn/mall/goods/2584122392920760320?goodsSpuId=2584110057774686208&skuId=2584122392920760320&categoryId=10005",
    "remarks": "诊所必备检验仪器，对观察治疗效果，用药或停药、停止治疗的常用参考指标",
    "goodsType": 3,
    "goodsSubType": 1,
    "goodsExtendSpec": null,
    "deviceType": 1,
    "deviceTypeName": "临床检验",
    "usageType": 1,
    "usageTypeName": "血液分析",
    "salePrice": 19500,
    "innerFlag": 0,
    "sampleNoRule": {
      "prefix": "NO",
      "length": 6,
      "format": ""
    },
    "connectStatus": 0,
    "deviceModelStatus": 0,
    "deviceStatus": 0,
    "clinicInfos": [
      {
        "organ": {
          "id": "fff730ccc5ee45d783d82a85b8a0e52d",
          "parentId": "6a869c22abee4ffbaef3e527bbb70aeb",
          "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
          "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
          "name": "四川省成都市高新区交子大道高新大源店",
          "shortName": "高新大原店",
          "nodeType": 2,
          "viewMode": 0,
          "hisType": 0,
          "shortNameFirst": "高新大原店"
        },
        "deviceId": "3781376743759101952",
        "shortId": "000049",
        "deviceStatus": 10,
        "deviceStatusName": "使用中",
        "created": "2023-03-13T01:44:09Z"
      }
    ],
    "extendInfos": {
      "screen": "10寸彩色液晶触摸屏",
      "sampleCount": "200000",
      "sample": [
        "末梢全血",
        "静脉全血",
        "预稀释血"
      ],
      "reagent": "封闭试剂",
      "testItem": "20项参数三个直方图，白细胞、红细胞、血小板",
      "examSpeed": "40样本/小时",
      "operatingPrinciple": "电阻法计数",
      "measurementPattern": "全血模式9μl和预稀释模式20μl",
      "advantages": "国际一线品牌、设备采购成本低、试剂成本低至0.8元、利润高达95%、门诊开单率15%、1.5分钟出结果精准稳定，基层医疗机构推荐使用。"
    }
  },
  "examinationApplySheetView": {
    "id": "3795524688661921794",
    "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
    "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "doctorId": "6e45706922a74966ab51e4ed1e604641",
    "departmentId": "2d2d0cb8f61f4301afaaf689d62b51c4",
    "patientId": "672543ad35c511e998496c92bf5d5878",
    "patientOrderId": "ffffffff0000000034ac6998220dc000",
    "registrationFormItemId": null,
    "deviceId": null,
    "deviceRoomId": null,
    "no": "JY2401120002",
    "businessType": 20,
    "goodsType": 3,
    "type": 1,
    "subType": 0,
    "deviceType": null,
    "chiefComplaint": null,
    "presentHistory": null,
    "physicalExamination": null,
    "diagnosisInfos": [
      {
        "id": null,
        "diseaseName": "急性上呼吸道感染",
        "diseaseCode": "J06.900"
      }
    ],
    "purpose": null,
    "planExecuteDate": "2024-01-12",
    "created": "2024-01-12T01:54:11Z",
    "status": 10,
    "patient": null,
    "dcm4cheeView": null
  },
  "location": null,
  "peSheetSimpleView": null,
  "deviceType": 1,
  "isMerge": 1,
  "importFlag": 0,
  "departmentName": "内科急诊",
  "clinicPrintName": null,
  "examinationApplySheetNo": "JY2401120002",
  "deviceName": "迈瑞全自动血细胞分析仪BC-21",
  "lastModifiedMillsTime": "1705024571000",
  "goodsSubType": 1,
  "departmentId": "2d2d0cb8f61f4301afaaf689d62b51c4",
  "innerFlag": 0,
  "deviceStatus": 0,
  "lastModifiedTime": "2024-01-12T01:56:11Z",
  "name": "检验1017、检验组合0927",
  "modifierName": "宁铁桥",
  "extendDiagnosisInfos": [
    {
      "toothNos": null,
      "value": [
        {
          "code": "J06.900",
          "name": "急性上呼吸道感染",
          "diseaseType": null
        }
      ]
    }
  ]
}
-->