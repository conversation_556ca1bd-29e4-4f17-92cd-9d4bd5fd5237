<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    prepaidSettleSummary: {
      refundFee: -102,
    },
    chargeForms: [
        {
            id: '9410ffd3ece8439e9e12c8f3df396bc8',
            chargeFormItems: [
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                    name: '盐知母',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                    specialRequirement: '包煎',
                    ownExpenseFee: 36,
                },
                {
                    id: '7ef3ac794a034b4e952031d4b14b18c1',
                    name: '盐黄柏',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 0.03,
                    specialRequirement: '先煎',
                    ownExpenseFee: 0.63,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9071',
                    name: '白花蛇舌',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16.2,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9072',
                    name: '山药YG',
                    unit: 'g',
                    count: 5.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9073',
                    name: '牡丹皮YG',
                    unit: 'g',
                    count: 10.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                {
                    id: 'c7d9841903db47a3a1943d6f9d3f9076',
                    name: '白花蛇舌草颗粒1/15（4-9）',
                    unit: 'g',
                    count: 30.0,
                    unitCount: 12.0,
                    doseCount: 1.0,
                    totalPrice: 16.2,
                    specialRequirement: '先煎',
                    ownExpenseFee: 16,
                },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                //     name: '盐知母',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 36.0,
                //     specialRequirement: '包煎',
                // },
                // {
                //     id: '7ef3ac794a034b4e952031d4b14b18c1',
                //     name: '盐黄柏',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 1.0,
                //     doseCount: 1.0,
                //     totalPrice: 0.03,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9072',
                //     name: '山药YG',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9073',
                //     name: '牡丹皮YG',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9076',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },{
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: '8393dc7a61c4468f9e82b4ac27eb3b6c',
                //     name: '盐知母',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 36.0,
                //     specialRequirement: '包煎',
                // },
                // {
                //     id: '7ef3ac794a034b4e952031d4b14b18c1',
                //     name: '盐黄柏',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 1.0,
                //     doseCount: 1.0,
                //     totalPrice: 0.03,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9071',
                //     name: '白花蛇舌',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9072',
                //     name: '山药YG',
                //     unit: 'g',
                //     count: 5.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9073',
                //     name: '牡丹皮YG',
                //     unit: 'g',
                //     count: 10.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
                // {
                //     id: 'c7d9841903db47a3a1943d6f9d3f9076',
                //     name: '白花蛇舌草颗粒1/15（4-9）',
                //     unit: 'g',
                //     count: 30.0,
                //     unitCount: 12.0,
                //     doseCount: 1.0,
                //     totalPrice: 16.2,
                //     specialRequirement: '先煎',
                // },
            ],
            sourceFormType: 6,
            specification: '中药饮片',
            doseCount: 1,
            dailyDosage: '1日1剂',
            usage: '煎服',
            freq: '1日3次',
            usageLevel: '每次150ml',
        },

        // {
        //     id: '338adf3126c141e0ab38d5de35e9305902',
        //     chargeFormItems: [
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //         {
        //             id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
        //             name: 'HPV基因全套',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 320.0,
        //         },
        //         {
        //             id: '7d546ba7fd4d472db0aedc21d544ad9f',
        //             name: '甲胎蛋白（AFP）',
        //             unit: '次',
        //             count: 1.0,
        //             unitCount: 1.0,
        //             doseCount: 1.0,
        //             totalPrice: 40.0,
        //         },
        //     ],
        //     sourceFormType: 2,
        // },

    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号
    diagnosisInfos: [
        {
            code: 'L55.900',
            diseaseType: null,
            name: '晒斑[晒伤]',
        },
    ],

    patientOrderNo: '**********',
    memberCardBalance: null,
    memberCardMobile: '',
    memberCardBeginningBalance: '', // 会员卡原有余额
    healthCardBeginningBalance: '567.68', // 社保卡原有余额
    healthCardOwnerRelationToPatient: '父女', // 持卡人关系
    healthCardBalance: '0.00', // 社保卡余额
    healthCardNo: '********', // 社保卡卡号
    healthCardOwner: '任我行', // 持卡人姓名"
    serialNo: '********', // 门诊流水号"

    healthCardId: '********', // 医保编号
    healthCardAccountPaymentFee: '19.99', // 帐户支付金额
    healthCardFundPaymentFee: 20, // 统筹支付金额
    healthCardOtherPaymentFee: '10', // 其它支付金额
    healthCardCardOwnerType: '职工', // 医保类型 职工 居民 离休干部
    healthCardSelfConceitFee: '11', // 自负金额
    healthCardSelfPayFee: '22', // 自费金额
    personalPaymentFee: '33', // 个人现金支付

    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
        },
    ],

    shebaoPayment: {
        cardId: '********', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        extraInfo: {
            // 青岛数据
            individualAffordabilityLine: '9.9', //个人负担起付线
        },
    },
}
-->

<template>
    <div>
        <div class="medical-hospital-bill-hebei">
            <!-- 患者医保划分 -->
            <block-box
                :font="8"
                :top="18"
                :left="74"
            >
                {{ extraInfo.insuplcAdmdvsName }}
                <template v-if="extraInfo.insuplcAdmdvs">
                    （{{ extraInfo.insuplcAdmdvs }}）
                </template>
            </block-box>

            <!-- 机构名称 -->
            <block-box
                :font="8"
                :top="23"
                :left="39"
            >
                {{ organ.name }}
            </block-box>
            <!--         机构类型-->
            <block-box
                :font="8"
                :top="23"
                :left="86"
            >
                {{ organ.category }}
            </block-box>
            <!--          科室-->
            <block-box
                :font="8"
                :top="23"
                :left="107"
            >
                {{ printData.departmentName }}
            </block-box>
            <!--          流水号-->
            <block-box
                :font="8"
                :top="23"
                :left="141"
            >
                {{ printData.hospitalNo }}
            </block-box>

            <!-- 住院号 -->
            <block-box
                :font="8"
                :top="28"
                :left="35"
            >
                {{ printData.caseNumber }}
            </block-box>

            <!--          住院时间-->
            <block-box
                :font="8"
                :top="28"
                :left="67"
            >
                {{ getYMD(printData.inHospitalDate).year }}
            </block-box>
            <block-box
                :font="8"
                :top="28"
                :left="80"
            >
                {{ getYMD(printData.inHospitalDate).month }}
            </block-box>
            <block-box
                :font="8"
                :top="28"
                :left="89"
            >
                {{ getYMD(printData.inHospitalDate).day }}
            </block-box>
            <block-box
                :font="8"
                :top="28"
                :left="99"
            >
                {{ getYMD(printData.outHospitalDate).year }}
            </block-box>
            <block-box
                :font="8"
                :top="28"
                :left="114"
            >
                {{ getYMD(printData.outHospitalDate).month }}
            </block-box>
            <block-box
                :font="8"
                :top="28"
                :left="123"
            >
                {{ getYMD(printData.outHospitalDate).day }}
            </block-box>
            <!--          住院天数-->
            <block-box
                :font="8"
                :top="28"
                :left="144"
            >
                {{ printData.inpatientDays }}
            </block-box>

            <!--          开票人-->
            <!-- 第三行 -->
            <block-box
                :font="8"
                :top="33"
                :left="32"
            >
                {{ printData.buyerName }}
            </block-box>
            <block-box
                :font="8"
                :top="33"
                :left="58"
            >
                {{ printData.buyerSex }}
            </block-box>
            <!--          医保类型-->
            <block-box
                :font="8"
                :top="33"
                :left="91"
            >
                {{ shebaoPayment.cardOwnerType }}
            </block-box>
            <!--          税号-->
            <block-box
                :font="8"
                :top="33"
                :left="146"
            >
                {{ printData.buyerTaxNum }}
            </block-box>

            <!-- 费用明细内容 -->
            <block-box
                :font="8"
                :top="43"
                :left="26"
                class="table-content"
            >
                <div class="items-wrapper">
                    <div
                        v-for="(item, index) in medicalBills"
                        :key="index"
                        class="charge-item"
                    >
                        <span class="item-name">{{ item.name }}</span>
                        <span class="item-amount">{{ item.totalFee | formatMoney }}</span>
                        <span class="item-fee-type">{{ item.payTypeName }}</span>
                    </div>
                </div>
            </block-box>

            <!-- 费用合计 -->
            <block-box
                :font="8"
                :top="74"
                :left="41"
            >
                {{ digitUppercase(printData.invoiceFee) }}
            </block-box>
            <block-box
                :font="8"
                :top="74"
                :left="114"
            >
                {{ printData.invoiceFee | formatMoney }}
            </block-box>

            <!-- 预缴金额 -->
            <block-box
                :font="8"
                :top="80"
                :left="37"
            >
                {{ prepaidSettleSummary.prepaidFee | formatMoney }}
            </block-box>
            <!--          补缴金额-->
            <block-box
                :font="8"
                :top="80"
                :left="76"
            >
                {{ prepaidSettleSummary.repaidFee | formatMoney }}
            </block-box>
            <!--          退费 金额-->
            <block-box
                :font="8"
                :top="80"
                :left="116"
            >
                <template v-if="prepaidSettleSummary.refundedFee">
                    {{ Math.abs(prepaidSettleSummary.refundedFee) | formatMoney }}
                </template>
                <template v-else>
                    0.00
                </template>
            </block-box>
            <!--          医院垫支-->
            <block-box
                :font="8"
                :top="80"
                :left="153"
            >
                {{ prepaidSettleSummary.hospitalAdvancePayment | formatMoney }}
            </block-box>



            <!--          医保统筹支付-->
            <block-box
                :font="8"
                :top="84"
                :left="42"
            >
                {{ shebaoPayment.fundPaymentFee | formatMoney }}
            </block-box>
            <!--个人账户支付-->
            <block-box
                :font="8"
                :top="84"
                :left="81"
            >
                {{ shebaoPayment.accountPaymentFee | formatMoney }}
            </block-box>
            <!--个人自付-->
            <block-box
                :font="8"
                :top="84"
                :left="116"
            >
                {{ shebaoPayment.selfPaymentFee | formatMoney }}
            </block-box>
            <!--          个人自费-->
            <block-box
                :font="8"
                :top="84"
                :left="153"
            >
                {{ shebaoPayment.selfHandledPaymentFee | formatMoney }}
            </block-box>

            <!--个人账户余额-->
            <block-box
                :font="8"
                :top="89"
                :left="42"
            >
                {{ shebaoPayment.cardBalance | formatMoney }}
            </block-box>
            <!--          统筹累计支付-->
            <block-box
                :font="8"
                :top="89"
                :left="81"
            >
                {{ extraInfo.optFundPayCum | formatMoney }}
            </block-box>

            <block-box
                :font="8"
                :top="93"
                :left="25"
            >
                基本统筹支付： {{ extraInfo.hifpPay | formatMoney }}
            </block-box>
            <block-box
                :font="8"
                :top="93"
                :left="64"
            >
                医疗救助支付：{{ extraInfo.mafPay | formatMoney }}
            </block-box>
            <block-box
                :font="8"
                :top="93"
                :left="116"
            >
                大病保险支付：{{ extraInfo.hifmiPay | formatMoney }}
            </block-box>


            <!-- 收费单位 -->
            <block-box
                :font="8"
                :top="103"
                :left="46"
            >
                {{ currentConfig.institutionName }}
            </block-box>
            <!--          开票员-->
            <block-box
                :font="8"
                :top="103"
                :left="110"
            >
                {{ printData.payee }}
            </block-box>
            <!--          开票日期-->
            <block-box
                :font="8"
                :top="103"
                :left="152"
            >
                {{ getYMD(printData.invoiceDate).year }}
            </block-box>
            <block-box
                :font="8"
                :top="103"
                :left="169"
            >
                {{ getYMD(printData.invoiceDate).month }}
            </block-box>
            <block-box
                :font="8"
                :top="103"
                :left="179"
            >
                {{ getYMD(printData.invoiceDate).day }}
            </block-box>
        </div>
    </div>
</template>

<script>
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import clone from "./common/clone.js";
    import { SourceFormTypeEnum } from "./common/constants.js";
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import CommonHandler from './data-handler/common-handler.js';
    import BillDataMixins from './mixins/bill-data';
    import NationalBillData from "./mixins/national-bill-data.js";

    export default {
        name: "MedicalHospitalHebei",
        components: {
            BlockBox
        },
        mixins: [BillDataMixins, NationalBillData],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_HOSPITAL_BILL_HEBEI,
        pages: [
            {
                paper: PageSizeMap.MM120_212_HEBEI,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        // 社保总支付的应该是 shebaoPayment.receivedFee
        computed: {
            chargeTransactions() {
                return this.printData && this.printData.chargeTransactions;
            },
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                return this.isOnlyOnePage ? this.renderPage.slice(0, 1) : this.renderPage
            },

            isOnlyOnePage() {
                return this.splitType === 1 && (this.renderPage.length > 1 || this.extra.isPreview);
            },
            format() {
                return this.config.format;
            },
            // 是否分页打印
            splitType() {
                return this.config[this.format].splitType;
            },
            config() {
                return this.renderData.config.hospitalBillConfig || { hubei: {} };
            },
            currentConfig() {
                return this.config[this.format] || {};
            },
            chineseForms() {
                return this.chargeForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                });
            },
            chineseFormItems() {
                return this.getFormItems(this.chineseForms)
            },
            notChineseForms() {
                return this.chargeForms.filter((form) => {
                    return form.sourceFormType !== SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                });
            },

            notChineseFormItems() {
                return this.getFormItems(this.notChineseForms)
            },
            renderChineseFormItems() {
                return this.spliceFormItems(this.chineseFormItems, 13, 9, true);
            },
            renderNotChineseFormItems() {
                return this.spliceFormItems(this.notChineseFormItems, 13, 9, false);
            },
            renderPage() {
                return this.renderChineseFormItems.concat(this.renderNotChineseFormItems);
            },
        },
        methods: {
            getFormItems(form) {
                let res = [];
                form.forEach((form) => {
                    form.chargeFormItems && form.chargeFormItems.forEach((formItem) => {
                        if(this.composeChildrenConfig === 2) {
                            if ((formItem.productType === 3 || formItem.composeType > 0) && formItem.composeChildren && formItem.composeChildren.length) {
                                res = res.concat(formItem.composeChildren);
                            } else {
                                res.push(formItem);
                            }
                        } else if(this.composeChildrenConfig === 1) {
                            res.push(formItem);
                            if ((formItem.productType === 3 || formItem.composeType > 0) && formItem.composeChildren && formItem.composeChildren.length) {
                                res = res.concat(formItem.composeChildren);
                            }
                        } else {
                            res.push(formItem);
                        }
                    });
                });
                return res;
            },
            spliceFormItems(formItems, pageCount, firstPageCount, isChineseForm) {
                const page = [];
                const cacheFormItems = clone(formItems);
                let pageIndex = 0;
                while(cacheFormItems && cacheFormItems.length) {
                    pageIndex++;
                    let endIndex = 0;
                    if(pageIndex === 1) {
                        endIndex = cacheFormItems.length > firstPageCount ? firstPageCount : cacheFormItems.length;
                    } else {
                        endIndex = cacheFormItems.length > pageCount ? pageCount : cacheFormItems.length;
                    }
                    page.push({
                        formItems: cacheFormItems.splice(0, endIndex),
                        pageIndex,
                        isChineseForm,
                    });
                }
                return page;
            },
            getYMD(dataStr) {
                if (dataStr) {
                    const year = new Date(dataStr).getFullYear();
                    const month = new Date(dataStr).getMonth() + 1;
                    const day = new Date(dataStr).getDate();
                    return {
                        year,
                        month,
                        day
                    }
                }
                return {
                    year: '',
                    month: '',
                    day: '',
                }
            }
        },
    }
</script>
<style lang="scss">
.medical-hospital-bill-hebei {
    position: relative;
    width: 212mm;
    height: 120mm;
    font-size: 9pt;

    .table-header {
        .header-row {
            display: flex;
            border-bottom: 1px solid #000;
            padding-bottom: 2mm;
            
            .col-item {
                flex: 2;
                padding-right: 5mm;
            }
            
            .col-amount {
                flex: 1;
                text-align: right;
            }
        }
    }

    .table-content {
        width: 166mm;
        height: 30mm;
        .items-wrapper {
            .charge-item {
                display: inline-block;
                width: 49.9%;
                > span {
                  display: inline-block;
                }

                .item-name {
                    width: 40mm;
                }
                
                .item-amount {
                  width: 16mm;
                  text-align: right;
                }

                .item-fee-type {
                  width: 20mm;
                  text-align: right;
                }
            }
        }
    }
}

.abc-page_preview {
    background: url("/static/assets/print/hospital-bill/medical-hospital-bill-hebei.png");
  background-size: 212mm 120mm;
    color: #2a82e4;
}
</style>
