<!--exampleData

{
  "kindCount": 3,
  "comment": [],
  "receiveDepartment": {
    "id": "ffffffff0000000034692fa655df0000",
    "name": "神经内科",
    "chainId": "ffffffff00000000146808c695534000",
    "clinicId": "ffffffff00000000146808c695534004",
    "tagId": "ffffffff0000000034692fb435df0000",
    "type": 1,
    "isDefault": 0
  },
  "receiveDepartmentId": "ffffffff0000000034692fa655df0000",
  "id": "100001132",
  "orderNo": "LY2023060600001",
  "type": 1,
  "status": 2,
  "statusName": "完成",
  "createdBy": {
    "id": "6e45706922a74966ab51e4ed1e604641",
    "name": "刘喜"
  },
  "created": "2023-06-06T06:19:45Z",
  "lastModifiedBy": {
    "id": "6e45706922a74966ab51e4ed1e604641",
    "name": "刘喜"
  },
  "reviewed": null,
  "reviewBy": null,
  "inConfirmed": "2023-06-06T06:20:07Z",
  "inConfirmBy": {
    "id": "6e45706922a74966ab51e4ed1e604641",
    "name": "刘喜"
  },
  "outConfirmed": null,
  "outConfirmBy": null,
  "organ": {
    "id": "ffffffff00000000146808c695534004",
    "parentId": "ffffffff00000000146808c695534000",
    "clinicId": "ffffffff00000000146808c695534004",
    "chainId": "ffffffff00000000146808c695534000",
    "name": "ABC医院",
    "nodeType": 2,
    "viewMode": 0,
    "hisType": 100,
    "shortNameFirst": "ABC医院"
  },
  "inPharmacy": {
    "no": 9,
    "name": "库房aaaa",
    "type": 0
  },
  "outPharmacy": {
    "no": 7,
    "name": "药库2",
    "type": 0
  },
  "lastModified": "2023-06-06T06:20:07Z",
  "recepted": "2023-06-06T06:20:07Z",
  "receptedBy": {
    "id": "6e45706922a74966ab51e4ed1e604641",
    "name": "刘喜"
  },
  "list": [
    {
      "applicationPackageCount": 2,
      "applicationPieceCount": 2,
      "packageCount": 2,
      "pieceCount": 2,
      "goods": {
        "goodsVersion": 0,
        "id": "ffffffff000000003482b83346940000",
        "goodsId": "ffffffff000000003482b83346940000",
        "shortId": "000938",
        "status": 1,
        "name": "测试西药",
        "py": "CSXY|CSXY|CESHIXIYAO|CESHIXIYAO",
        "organId": "ffffffff00000000146808c695534000",
        "type": 1,
        "subType": 1,
        "barCode": "12321412",
        "manufacturer": "上海上药中西",
        "manufacturerFull": "上海上药中西制药有限公司",
        "medicineCadn": "测试西药",
        "medicineNmpn": "adfasaf",
        "medicineDosageNum": 12,
        "medicineDosageUnit": "片",
        "specType": 0,
        "isSell": 1,
        "pieceUnit": "板",
        "packageUnit": "盒",
        "dismounting": 0,
        "pieceNum": 3,
        "inTaxRat": 0,
        "outTaxRat": 0,
        "createdUserId": "6e45706922a74966ab51e4ed1e604641",
        "createdDate": "2023-05-04T01:55:38.856Z",
        "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
        "lastModifiedDate": "2023-05-04T01:55:38.856Z",
        "disable": 0,
        "needExecutive": 0,
        "typeId": 12,
        "customTypeId": "0",
        "chainPiecePrice": 16.6667,
        "chainPackagePrice": 50,
        "piecePrice": 16.6667,
        "packagePrice": 50,
        "packageCostPrice": 0,
        "v2DisableStatus": 0,
        "displayName": "测试西药(测试西药)",
        "displaySpec": "12片*3板/盒",
        "combineType": 0,
        "chainDisable": 0,
        "chainV2DisableStatus": 0,
        "disableSell": 0,
        "chainId": "ffffffff00000000146808c695534000",
        "defaultInOutTax": 1,
        "shebaoPayMode": 0
      },
      "goodsId": "ffffffff000000003482b83346940000",
      "totalCostPrice": 5.3334,
      "packageCostPrice": 2,
      "lastModified": "2023-06-06T06:19:45Z",
      "batchs": [
        {
          "id": "*********",
          "receptSourceId": null,
          "batchId": "*********",
          "expiryDate": "2024-12-31",
          "batchNo": "ff0001",
          "packageCount": 2,
          "packageCostPrice": 2,
          "totalCostPrice": 5.3334,
          "pieceCount": 2,
          "applicationPackageCount": 2,
          "applicationPieceCount": 2,
          "returnLeftPackageCount": 2,
          "returnLeftPieceCount": 2,
          "returnPackageCount": 0,
          "returnPieceCount": 0
        }
      ]
    },
    {
      "applicationPackageCount": 0,
      "applicationPieceCount": 22,
      "packageCount": 0,
      "pieceCount": 22,
      "goods": {
        "goodsVersion": 0,
        "id": "ffffffff000000003484099606cd8000",
        "goodsId": "ffffffff000000003484099606cd8000",
        "shortId": "128482165785",
        "status": 1,
        "py": "ASAMLZ|AISIAOMEILAZUO",
        "organId": "ffffffff00000000146808c695534000",
        "type": 1,
        "subType": 2,
        "medicineCadn": "艾司奥美拉唑",
        "specType": null,
        "materialSpec": "中药饮片",
        "isSell": 1,
        "pieceUnit": "g",
        "dismounting": 1,
        "pieceNum": 1,
        "inTaxRat": 0,
        "outTaxRat": 0,
        "createdUserId": "ffffffff000000001775fa080df64000",
        "createdDate": "2023-05-12T01:51:45.006Z",
        "lastModifiedUserId": "ffffffff000000001775fa080df64000",
        "lastModifiedDate": "2023-05-12T01:51:45.006Z",
        "disable": 0,
        "needExecutive": 0,
        "typeId": 14,
        "customTypeId": "0",
        "chainPiecePrice": 2,
        "chainPackagePrice": 2,
        "piecePrice": 2,
        "packagePrice": 2,
        "packageCostPrice": 1,
        "v2DisableStatus": 0,
        "displayName": "艾司奥美拉唑",
        "combineType": 0,
        "chainDisable": 0,
        "chainV2DisableStatus": 0,
        "disableSell": 0,
        "chainId": "ffffffff00000000146808c695534000",
        "defaultInOutTax": 1,
        "cMSpec": "中药饮片"
      },
      "goodsId": "ffffffff000000003484099606cd8000",
      "totalCostPrice": 44,
      "packageCostPrice": 2,
      "lastModified": "2023-06-06T06:19:45Z",
      "batchs": [
        {
          "id": "*********",
          "receptSourceId": null,
          "batchId": "*********",
          "expiryDate": "2023-12-31",
          "batchNo": "ff0002",
          "packageCount": 0,
          "packageCostPrice": 2,
          "totalCostPrice": 44,
          "pieceCount": 22,
          "applicationPackageCount": 0,
          "applicationPieceCount": 22,
          "returnLeftPackageCount": 22,
          "returnLeftPieceCount": 0,
          "returnPackageCount": 0,
          "returnPieceCount": 0
        }
      ]
    },
    {
      "applicationPackageCount": 2,
      "applicationPieceCount": 2,
      "packageCount": 2,
      "pieceCount": 2,
      "goods": {
        "goodsVersion": 0,
        "id": "ffffffff000000003487afaec7a48000",
        "goodsId": "ffffffff000000003487afaec7a48000",
        "shortId": "128482165909",
        "status": 1,
        "name": "阿莫西林",
        "py": "AMXL|AMJAMXLJN|AMOXILIN|AMOJUNAMOXILINJIAONANG",
        "organId": "ffffffff00000000146808c695534000",
        "type": 1,
        "subType": 1,
        "manufacturer": "江西汇仁",
        "manufacturerFull": "江西汇仁药业有限公司",
        "medicineCadn": "阿莫君阿莫西林胶囊",
        "specType": 0,
        "isSell": 1,
        "pieceUnit": "片",
        "packageUnit": "盒",
        "dismounting": 0,
        "pieceNum": 12,
        "inTaxRat": 0,
        "outTaxRat": 0,
        "createdUserId": "6e45706922a74966ab51e4ed1e604641",
        "createdDate": "2023-06-03T05:15:34.513Z",
        "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
        "lastModifiedDate": "2023-06-03T05:15:34.513Z",
        "disable": 0,
        "needExecutive": 0,
        "typeId": 12,
        "customTypeId": "0",
        "chainPiecePrice": 4.1667,
        "chainPackagePrice": 50,
        "piecePrice": 4.1667,
        "packagePrice": 50,
        "packageCostPrice": 0,
        "v2DisableStatus": 0,
        "displayName": "阿莫君阿莫西林胶囊(阿莫西林)",
        "displaySpec": "12片/盒",
        "combineType": 0,
        "chainDisable": 0,
        "chainV2DisableStatus": 0,
        "disableSell": 0,
        "chainId": "ffffffff00000000146808c695534000",
        "defaultInOutTax": 1,
        "shebaoPayMode": 0
      },
      "goodsId": "ffffffff000000003487afaec7a48000",
      "totalCostPrice": 6.5001,
      "packageCostPrice": 3,
      "lastModified": "2023-06-06T06:19:45Z",
      "batchs": [
        {
          "id": "*********",
          "receptSourceId": null,
          "batchId": "*********",
          "expiryDate": "2025-12-31",
          "batchNo": "ff0003",
          "packageCount": 2,
          "packageCostPrice": 3,
          "totalCostPrice": 6.5001,
          "pieceCount": 2,
          "applicationPackageCount": 2,
          "applicationPieceCount": 2,
          "returnLeftPackageCount": 1,
          "returnLeftPieceCount": 2,
          "returnPackageCount": 0,
          "returnPieceCount": 0
        }
      ]
    }
  ],
  "logs": [
    {
      "id": "2508",
      "action": "入库方确认",
      "detail": null,
      "comment": "",
      "creator": {
        "id": "6e45706922a74966ab51e4ed1e604641",
        "name": "刘喜"
      },
      "createdDate": "2023-06-06T06:20:07Z"
    },
    {
      "id": "2507",
      "action": "创建出库单",
      "detail": null,
      "comment": "",
      "creator": {
        "id": "6e45706922a74966ab51e4ed1e604641",
        "name": "刘喜"
      },
      "createdDate": "2023-06-06T06:19:45Z"
    }
  ],
  "amount": 55.8335,
  "count": 26.8333,
  "toUserId": "",
  "toUser": null
}
-->

<template>
    <div>
        <div data-type="header">
            <div class="order-title">
                {{ tableTitle }}
            </div>
            <print-row
                v-if="isReturn"
                class="print-stock-header"
            >
                <print-col
                    :span="8"
                >
                    入库库房：{{ inPharmacy.name || '' }}
                </print-col>
                <print-col
                    :span="8"
                >
                    出库库房：{{ outPharmacy.name || '' }}
                </print-col>
                <print-col :span="8">
                    退回日期：{{ (order.created) | parseTime('y-m-d') }}
                </print-col>
                <print-col :span="8">
                    领用退回单号：{{ order && order.orderNo || '' }}
                </print-col>
                <print-col :span="8">
                    原领用单号：{{ receptSourceOrder && receptSourceOrder.orderNo || '' }}
                </print-col>
                <print-col
                    :span="8"
                >
                    领用人：{{ receiveDepartment.name ? `${receiveDepartment.name}/` : '' }}{{ toUser.name || '' }}
                </print-col>
            </print-row>
            <print-row
                v-else
                class="print-stock-header"
            >
                <print-col
                    :span="8"
                >
                    领用人：{{ receiveDepartment.name ? `${receiveDepartment.name}/` : '' }}{{ toUser.name || '' }}
                </print-col>
                <print-col
                    :span="8"
                >
                    入库库房：{{ inPharmacy.name || '' }}
                </print-col>
                <print-col
                    :span="8"
                >
                    出库库房：{{ outPharmacy.name || '' }}
                </print-col>
                <print-col :span="8">
                    领用日期：{{ (order.created) | parseTime('y-m-d') }}
                </print-col>
                <print-col :span="8">
                    领用单号：{{ order && order.orderNo || '' }}
                </print-col>
            </print-row>
        </div>

        <div data-type="big-table">
            <table
                class="print-stock-table-wrapper"
            >
                <thead>
                    <tr class="table-title">
                        <th :colspan="2">
                            药品编码
                        </th>
                        <th :colspan="4">
                            药品名称
                        </th>
                        <th :colspan="2">
                            规格
                        </th>
                        <th :colspan="2">
                            厂家
                        </th>
                        <th
                            v-if="order.isPrintPosition"
                            :colspan="2"
                        >
                            柜号
                        </th>
                        <th :colspan="2">
                            生产批号
                        </th>
                        <th :colspan="2">
                            效期
                        </th>
                        <th
                            :colspan="2"
                            class="number-right"
                        >
                            数量
                        </th>
                        <th
                            :colspan="2"
                            class="number-right"
                        >
                            进价
                        </th>
                        <th
                            :colspan="2"
                            class="number-right"
                        >
                            无税金额
                        </th>
                        <th
                            :colspan="2"
                            class="number-right"
                        >
                            含税金额
                        </th>
                        <th
                            :colspan="2"
                            class="number-right no-right-border"
                        >
                            税率
                        </th>
                    </tr>
                </thead>

                <tbody>
                    <tr
                        v-for="(item) in orderList"
                        :key="item.id"
                        class="table-tr"
                    >
                        <!--            药品编码-->
                        <td :colspan="2">
                            {{ item.goods.shortId || '' }}
                        </td>

                        <!--            药品名称-->
                        <td :colspan="4">
                            <div>{{ item.goods | goodsFullName }}</div>
                        </td>

                        <!--            规格-->
                        <td :colspan="2">
                            {{ item.goods | goodsSpec }}
                        </td>

                        <!--            厂家-->
                        <td :colspan="2">
                            {{ item.goods && item.goods.manufacturer || '' }}
                        </td>

                        <!--            柜号-->
                        <td
                            v-if="order.isPrintPosition"
                            :colspan="2"
                        >
                            {{ item.goods && item.goods.position || '' }}
                        </td>

                        <!--            批号-->
                        <td :colspan="2">
                            {{ item.batchNo || '' }}
                        </td>

                        <!--            效期-->
                        <td :colspan="2">
                            {{ formatExpiryDate(item.expiryDate ) }}
                        </td>

                        <!--            数量-->
                        <td
                            :colspan="2"
                            class="number-right"
                        >
                            {{ item|complexCount }}
                        </td>

                        <!--            进价-->
                        <td
                            :colspan="2"
                            class="number-right"
                        >
                            <data-permission-control :value="item.packageCostPrice">
                                <span>{{ item.packageCostPrice | moneyDigit(5) }} / {{ item.goods.packageUnit || item.goods.pieceUnit }}</span>
                            </data-permission-control>
                        </td>

                        <!--无税金额-->
                        <td
                            :colspan="2"
                            class="number-right"
                        >
                            <data-permission-control :value="item.totalCostPriceE">
                                {{ (item.totalCostPriceE) | formatMoney(false) }}
                            </data-permission-control>
                        </td>

                        <!--含税金额-->
                        <td
                            :colspan="2"
                            class="number-right"
                        >
                            <data-permission-control :value="item.totalCostPrice">
                                {{ ( item.totalCostPrice) | formatMoney(false) }}
                            </data-permission-control>
                        </td>

                        <!--税率-->
                        <td
                            :colspan="2"
                            class="number-right no-right-border"
                        >
                            {{ item.goods.inTaxRat }}%
                        </td>
                    </tr>
                </tbody>
            </table>

            <print-row>
                <print-row data-last-page="LastPage">
                    <print-col :span="24">
                        备注：{{ order && order.comment || '' }}
                    </print-col>
                </print-row>
                <print-row>
                    <print-col :span="8">
                        合计金额(大写)：
                        <data-permission-control :value="order.amount">
                            {{ digitUppercase(order.amount) }}
                        </data-permission-control>
                    </print-col>
                    <print-col :span="5">
                        合计金额：
                        <data-permission-control :value="order.amount">
                            {{ order.amount | formatMoney(false) }}
                        </data-permission-control>
                    </print-col>
                    <print-col :span="4">
                        无税合计：
                        <data-permission-control :value="order.amountExcludingTax">
                            {{ order.amountExcludingTax | formatMoney(false) }}
                        </data-permission-control>
                    </print-col>
                    <print-col :span="4">
                        合计品种：{{ order && order.kindCount || '' }}
                    </print-col>
                    <print-col :span="3">
                        第 <span
                            data-page-no="PageNo"
                        >##</span>页/共 <span data-page-count="PageCount">##</span>页
                    </print-col>
                </print-row>
                <print-row data-last-page="LastPage">
                    <print-col :span="4">
                        收货人：
                    </print-col>
                    <print-col :span="5">
                        质量情况：
                    </print-col>
                    <print-col :span="5">
                        保管人：
                    </print-col>
                    <print-col :span="5">
                        验收人：
                    </print-col>
                    <print-col :span="5">
                        制单人：{{ order && order.createdBy && order.createdBy.name }}
                    </print-col>
                </print-row>
            </print-row>
        </div>
    </div>
</template>

<script>
    import CKHandler from './data-handler/CK-handler.js'
    import {
        goodsSpec,
        digitUppercase,
        complexCount,
        parseTime,
        moneyDigit,
        formatMoney,
        goodsFullName,
    } from "./common/utils.js";
    import PrintRow from './components/layout/print-row.vue';
    import PrintCol from './components/layout/print-col.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import DataPermissionControl from './components/inventory/data-permission-control.js';

    export default {
        name: "LY", // 领用单
        DataHandler: CKHandler,
        components: {
            PrintCol,
            PrintRow,
            DataPermissionControl,
        },
        businessKey: PrintBusinessKeyEnum.GOODS_APPLY,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分' // 默认选择的等分纸
            },
        ],
        filters: {
            goodsSpec,
            complexCount,
            parseTime,
            formatMoney,
            goodsFullName
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        computed: {
            order() {
                return this.renderData.printData;
            },
            config() {
                return this.renderData.config;
            },
            isReturn() {
                if(!this.order) return false;
                return this.order.type === 10 || this.order.type === 30;
            },
            tableTitle() {
                const {organ} = this.order;
                let str = `${organ && organ.name || ''}`;
                switch (this.order.type) {
                    case 1:
                    case 20:
                        str += ' 领用单';
                        break
                    case 10:
                    case 30:
                        str += ' 领用退回单';
                        break
                    default:
                        str += ' 领用单';
                        break
                }
                return str;
            },
            inPharmacy() {
                return this.order.inPharmacy || {};
            },
            outPharmacy() {
                return this.order.outPharmacy || {};
            },
            receptSourceOrder() {
                return this.order.receptSourceOrder || {};
            },
            receiveDepartment() {
                return this.isReturn ? this.receptSourceOrder.receiveDepartment || {} : this.order.receiveDepartment || {};
            },
            toUser() {
                return this.isReturn ? this.receptSourceOrder.toUser || {}: this.order.toUser || {};
            },
            orderList() {
                return this.flatBatchInfo( this.order.list );
            }
        },
        methods: {
            formatMoney,
            moneyDigit,
            digitUppercase,
            formatExpiryDate( expiryDate ) {
                if (expiryDate) {
                    return expiryDate.replace( /-/g, '/' )
                }
                return ''
            },
            /**
             * @desc 将批次信息拍平
             * <AUTHOR>
             * @date 2023-06-08 21:44:10
             * @params
             * @return
             */
            flatBatchInfo( list ) {
                let arr = [];
                if (list) {
                    list.forEach( item => {
                        item.batchs.forEach( batch => {
                            arr.push( {
                                goods: item.goods,
                                ...batch,
                                packageCount: batch.applicationPackageCount || batch.packageCount,
                                pieceCount: batch.applicationPieceCount || batch.pieceCount,
                            } )
                        } )
                    } )
                }
                return arr;
            }
        }
    }
</script>
<style lang="scss">
@import "./style/inventory-common.scss";
</style>
