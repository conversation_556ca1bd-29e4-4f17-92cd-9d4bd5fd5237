<template>
    <div class="print-stat-wrapper">
        <div class="print-stat-content">
            <div class="print-charge-notification-wrapper">
                <div class="header">
                    医疗费用知情告知书
                </div>
                <div class="content">
                    <div>
                        <span class="patient-name">患者姓名:{{ patient.name }}</span><span class="sex">性别:{{ patient.sex }}</span><span class="age">年龄:{{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}</span> <span class="mobile">电话：{{ patient.mobile }}</span>
                    </div>
                    <div>根据《深圳经济特区医疗条例》第四十一条的规定，医疗机构应当及时告知患者病情、诊疗措施、医疗风险以及医疗费用等医疗服务信息。不宜或者无法告知患者的，应当告知其近亲属或者陪同人员。现就有关事项告知如下：</div>
                    <div class="content">
                        一、经本医疗机构医师<span class="doctor-name">{{ printData.doctorName }} </span>接诊，初步诊断为:<span class="diagnosis">
                            {{ printData.diagnosis }}
                        </span>
                        <space-line
                            v-if="!printData.diagnosis"
                            :count="30"
                        ></space-line>
                        ，诊疗措施包括：<span class="disposals">{{ printData.disposals }}</span>
                        <space-line
                            v-if="!printData.disposals"
                            :count="30"
                        ></space-line>
                    </div>
                    <div class="content">
                        二、您本次就医产生的医疗费用共计<span class="total-money">{{ summary.netIncomeIgnoreOweFee | formatMoney }}</span>元，包含的检查/治疗项目及其单价为: <span class="product">{{ printData.chargeFormItemText }}</span>
                        <space-line
                            v-if="!printData.chargeFormItemText"
                            :count="30"
                        ></space-line>
                    </div>
                    <div class="content">
                        三、治疗过程中因合并症，并发症等原因，导致实际进行的临床治疗偏离临床路径，需要采取适宜的方式继续治疗的，按项目另行收费。
                    </div>
                    <div class="content">
                        四、在治疗过程中仍有可能出现不可抗拒的医疗风险、并发症及其他意外情况，因此请您认真阅读相关治疗同意书，并了解疾病的医疗风险。
                    </div>
                    <div class="content">
                        五、上述内容请您认真阅读，如有不理解之处，请咨询您的接诊医师。如果您已经理解并同意以上检查/治疗项目及费用，请您签署意见。
                    </div>
                </div>
                <div>
                    患者（或者近亲属、陪同人员）意见：
                    <div>（同意/不同意）______________本次检查/治疗项目及费用</div>
                    <div>患者签字：______________</div>
                </div>
                <div>
                    <span>
                        委托代理人、亲属签字：_______________
                    </span>
                    <span>
                        与患者的关系：________________
                    </span>
                </div>
                <div class="doctor-sign-wrapper">
                    <div class="doctor-sign">
                        医务人员签字:
                        <div class="doctor">
                            <img
                                v-if="printData.handSignPicUrl"
                                width="90"
                                height="26"
                                :src="printData.handSignPicUrl"
                                alt=""
                            />
                            <span
                                v-else
                                class="text"
                            >
                                {{ printData.doctorName }} 
                            </span>
                        </div>
                    </div>
                    <span>日期：</span>
                    <span class="date">{{ year }}</span>年
                    <span class="date">{{ month }}</span>月
                    <span class="date">{{ day }}</span>日
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import PrintCommonDataHandler from "./data-handler/common-handler.js";
    import { formatAge } from "./common/utils.js";
    import SpaceLine from './components/space-line/space-line.vue'

    export default {
        name: "ChargeNotification",
        components: {
            SpaceLine
        },
        DataHandler: PrintCommonDataHandler,
        businessKey: PrintBusinessKeyEnum.CHARGE_NOTIFICATION,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                }
            }
        },
        computed: {
            printData() {
                return this.renderData.printData;
            },
            summary() {
                return this.printData && this.printData.chargeSummary || {};
            },
            patient() {
                return this.printData && this.printData.patient || {};
            },
            currentClinic() {
                return this.printData && this.printData.currentClinic || {};
            },
            chargeNoticeInfo() {
                return this.printData && this.printData.chargeNoticeInfo || {};
            },
            chargedTime() {
                return this.summary.chargedTime;
            },
            year() {
                if (this.chargedTime) {
                    return new Date(this.chargedTime).getFullYear();
                }
                return '';
            },
            month() {
                if (this.chargedTime) {
                    return new Date(this.chargedTime).getMonth() + 1;
                }
                return '';
            },
            day() {
                if (this.chargedTime) {
                    return new Date(this.chargedTime).getDate();
                }
                return '';
            },
        },
        methods: {
            formatAge,
        }
    }
</script>
<style lang="scss">
.print-charge-notification-wrapper {
    padding: 18pt 10pt 0;
    font-size: 10pt;
    line-height: 18pt;

    .header {
        font-size: 16pt;
        font-weight: 500;
        line-height: 20pt;
        text-align: center;
    }

    .content {
        line-break: anywhere;
        margin-top: 10pt;
    }

    .patient-name {
        display: inline-block;
        min-width: 120pt;
        text-indent: 0;
    }
    .sex,
    .age {
        display: inline-block;
        min-width: 80pt;
    }

    .doctor-name,
    .total-money {
        display: inline-block;
        min-width: 30pt;
        padding: 0 4pt;
        text-indent: 0;
          text-decoration: underline;
    }
    
    .diagnosis{
        min-width: 232pt;
        padding: 0 4pt;
        text-indent: 0;
        text-decoration: underline;
    }

    .disposals {
        min-width: 410pt;
        padding: 0pt 4pt;
        text-indent: 0;
        text-decoration: underline;
    }

    .product {
        min-width: 93pt;
        padding: 0 4pt;
        text-indent: 0;
        text-decoration: underline;
    }

    .agreemeet,
    .sign {
        display: inline-block;
        min-width: 100pt;
         text-decoration: underline;
    } 

    .doctor-sign-wrapper {
        .doctor-sign {
            display: inline-block;
            margin-right: 40pt;
            .doctor {
                display: inline-block;
                padding: 0 4pt;
                text-indent: 0;
                min-width: 30pt;
                 text-decoration: underline;

                img {
                    display: inline-block;
                    width: 90pt;
                    height: 22pt;
                    vertical-align: middle;
                }
            }
        }

        .date {
            display: inline-block;
            min-width: 20pt;
            font-size: 10pt;
            vertical-align: middle;
        }
    }

    .patient-footer {
        padding-right: 80pt;
        margin-top: 30pt;
        text-align: right;
    }
}
</style>
