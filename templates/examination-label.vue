<!--exampleData
{
    patient: {
        name: '满岛光',
        age: {
            year: 30,
            month: 4,
            day: 5
        },
        sex: '女',
        birthday: '1993-07-01',
        sn: '121221123121221123',
    },
    doctor<PERSON>ame: '刘喜',
    created: '2022-08-08 12:01',
    clinicPrintName: '东京国立医院11111111111111111111111111111111111111111111111111111111111'
}
-->

<template>
    <div class="examination-label-wrapper">
        <div
            data-type="header"
            class="examination-label-header"
        >
            {{ printData.clinicPrintName }}
        </div>

        <div class="examination-label-center">
            <div class="examination-label-patient">
                <div class="patient-name">
                    {{ printData.patient.name }}
                </div>

                <div>
                    <span>
                        {{ printData.patient.sex }}
                    </span>
                    <span>
                        {{ formatAge(printData.patient.age) }}
                    </span>

                    <span>
                        {{ printData.patient.birthday }}
                    </span>
                </div>

                <div
                    overflow
                    style="width: 100%;overflow: hidden;white-space:nowrap"
                >
                    档案号：{{ printData.patient.sn }}
                </div>
            </div>
        </div>

        <div
            data-type="footer"
            class="examination-label-bottom"
        >
            <div>
                送检医生：{{ printData.doctor }}
            </div>
            <div>
                送检时间：{{ printData.created | parseTime('y-m-d') }}
            </div>
        </div>
    </div>
</template>

<script>
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { formatAge,parseTime } from "./common/utils.js";

    export default {
        name: "ExaminationLabel",
        businessKey: PrintBusinessKeyEnum.EXAMINATION_LABEL,
        pages: [
            {
                paper: PageSizeMap.TAG50_40,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        filters: {
            parseTime
        },
        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            printData() {
                const {
                    patient,
                    doctorName,
                    created,
                } = this.renderData;

                const _patient = {
                    ...(patient || {}),
                    age: patient.age || {
                        year: 0,
                        month: 0,
                        day: 0,
                    },
                };

                return {
                    clinicPrintName: this.renderData.clinicPrintName,
                    patient: {
                        name: _patient.name,
                        age: _patient.age,
                        sex: _patient.sex,
                        birthday: _patient.birthday,
                        sn: _patient.sn,
                    },
                    doctor: doctorName,
                    created,
                };
            },
        },
        mounted() {
            console.debug(this.renderData, '--------renderData---------')
        },
        methods: {
            formatAge,
        }
    }
</script>

<style lang='scss'>
// .abc-page_preview {
//   width: 189px !important;
//   height: 152px !important;
//   padding: 0 !important; 
// }

.examination-label-wrapper {

    .examination-label-header {
        font-size: 9pt;
        line-height: 20pt;
        padding: 0 6pt;
        border-bottom: 0.5pt solid #000000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .examination-label-center {
        padding: 7pt 6pt;

        .examination-label-patient {
            font-size: 10pt;

            div + div {
                margin-top: 4pt;
            }

            .patient-name {
                font-size: 12pt;
                color: #000;
                font-weight: bold;
            }
        }

    }
    .examination-label-bottom {
        font-size: 9pt;
        padding: 3.5pt 6pt;
        border-top: 0.5pt solid #000000;
    }
}
</style>