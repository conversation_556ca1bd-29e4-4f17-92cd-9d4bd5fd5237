<!--exampleData

{
"doctorName": "胡青牛",
     "patient": {
    "id": "ffffffff000000001d6f9c200605e000",
    "name": "任盈盈",
    "namePy": "renyingying",
    "namePyFirst": "RYY",
    "birthday": "1999-12-15",
    "mobile": "13900000000",
    "sex": "女",
    "idCard": null,
    "isMember": 1,
    "age": {
      "year": 22,
      "month": 0,
      "day": 6
    },
    "address": null,
    "sn": "000881",
    "remark": null,
    "profession": null,
    "company": null,
    "patientSource": null,
    "tags": [],
    "marital": null,
    "weight": null,
    "wxOpenId": null,
    "wxHeadImgUrl": null,
    "wxNickName": null,
    "wxBindStatus": 0,
    "isAttention": 0,
    "shebaoCardInfo": null,
    "childCareInfo": null,
    "chronicArchivesInfo": null
  },
    forms: [{
    "id": "ffffffff000000001f2d5418113d800d",
    "patientOrderId": "ffffffff000000001f2d541810b82000",
    "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
    "chargeSheetId": "ffffffff000000001f2d5418113d8003",
    "sourceFormId": "ffffffff000000001f2d541810c44009",
    "sourceFormType": 4,
    "status": 0,
    "usageScopeId": null,
    "medicineStateScopeId": null,
    "medicineStateScopeName": null,
    "vendorId": null,
    "vendorName": null,
    "expectedTotalPrice": null,
    "expectedPriceFlag": 0,
    "specification": null,
    "medicalRecord": null,
    "deliveryInfo": null,
    "processRule": null,
    "sort": 0,
    "processInfo": null,
    "usageInfo": null,
    "totalPrice": 447.66,
    "totalDiscountPrice": -326.12,
    "discountedTotalPrice": 121.54,
    "sourceTotalPrice": null,
    "isCanBeRefund": 0,
    "dispensingStatus": null,
    "isAirPharmacyNeedPaid": 0,
    "pharmacyNo": 0,
    "pharmacyType": 0,
    "isCanRefundDoseCount": 0,
    "isCanRefundSingle": 1,
    "doseCount": 1,
    "refundDoseCount": null,
    "leftDoseCount": 1,
    "keyId": "56d4d29f8f3746a4a1d2a5c3e49d32ba",
    "isFullChecked": true,
    "formItems": [{
        "id": "ffffffff000000001f2d5418113d800e",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d800d",
        "sourceFormItemId": "ffffffff000000001f2d541810c4400a",
        "status": 0,
        "unit": "片",
        "name": "酚咖片(芬必得)",
        "unitCostPrice": 1.15,
        "unitCount": 4,
        "doseCount": 1,
        "unitPrice": 0.64,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.64,
        "discountPrice": -2.04,
        "totalPrice": 2.56,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 2.56,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.52,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 1,
        "productId": "c74745c1cf364c78bcdbec7dcc33619a",
        "groupId": 1,
        "sort": 0,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "c74745c1cf364c78bcdbec7dcc33619a",
            "goodsId": null,
            "status": 1,
            "name": "芬必得",
            "displayName": "酚咖片(芬必得)",
            "displaySpec": "20片/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "中美天津史克",
            "pieceNum": 20,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "酚咖片",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0.64,
            "packagePrice": 12.8,
            "packageCostPrice": 23,
            "inTaxRat": 16,
            "outTaxRat": 16,
            "stockPieceCount": 11,
            "stockPackageCount": 116,
            "lastPackageCostPrice": 23,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2018-08-09T04:00:16Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 12.8,
            "chainPiecePrice": 0.64,
            "chainPackageCostPrice": 23,
            "pieceCount": 11,
            "packageCount": 116,
            "manufacturerFull": "中美天津史克制药有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 23,
            "limitPricePiecePrice": 0.64,
            "limitPricePackagePrice": 12.8,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": null
        },
        "usageInfo": {
            "ast": 0,
            "days": 1,
            "freq": "q6h",
            "ivgtt": 0,
            "usage": "口服",
            "dosage": "1",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "片",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -2.04,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 11,
        "stockPackageCount": 116,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "122c606ec9f84f70b656b3924fd1fbf4",
        "checked": true,
        "index": 2,
        "sortIndex": 1
    }, {
        "id": "ffffffff000000001f2d5418113d800f",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d800d",
        "sourceFormItemId": "ffffffff000000001f2d541810c4400b",
        "status": 0,
        "unit": "盒",
        "name": "十滴水胶丸(拾)",
        "unitCostPrice": 2,
        "unitCount": 2,
        "doseCount": 1,
        "unitPrice": 3,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 3,
        "discountPrice": -4.8,
        "totalPrice": 6,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 6,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 1.2,
        "useDismounting": 0,
        "productType": 1,
        "productSubType": 3,
        "productId": "e6a3d335cc1eabefd39d87a097602041",
        "groupId": 1,
        "sort": 1,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "e6a3d335cc1eabefd39d87a097602041",
            "goodsId": null,
            "status": 1,
            "name": "拾",
            "displayName": "十滴水胶丸(拾)",
            "displaySpec": "2片/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 16,
            "type": 1,
            "subType": 3,
            "pieceNum": 2,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "十滴水胶丸",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0,
            "packagePrice": 3,
            "packageCostPrice": 2,
            "inTaxRat": 0,
            "outTaxRat": 0,
            "stockPieceCount": 0,
            "stockPackageCount": 45,
            "lastPackageCostPrice": 2,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-07-04T11:55:06Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 3,
            "chainPiecePrice": 0,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 45,
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 2,
            "limitPricePiecePrice": 0,
            "limitPricePackagePrice": 3,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": ""
        },
        "usageInfo": {
            "ast": 0,
            "days": 1,
            "freq": "q6h",
            "ivgtt": 0,
            "usage": "口服",
            "dosage": "1",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "片",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -4.8,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 0,
        "stockPackageCount": 45,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "51131792ae4e45c2852d22e2e466386d",
        "checked": true,
        "index": 3,
        "sortIndex": 2
    }, {
        "id": "ffffffff000000001f2d5418113d8010",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d800d",
        "sourceFormItemId": "ffffffff000000001f2d541810c4400c",
        "status": 0,
        "unit": "袋",
        "name": "消旋卡多曲颗粒(杜拉宝)",
        "unitCostPrice": 0.33333,
        "unitCount": 12,
        "doseCount": 1,
        "unitPrice": 2,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 2,
        "discountPrice": -19.2,
        "totalPrice": 24,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 24,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 4.8,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 1,
        "productId": "368ac1011ddf7c7782e93e1ba06073e1",
        "groupId": 1,
        "sort": 2,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "368ac1011ddf7c7782e93e1ba06073e1",
            "goodsId": null,
            "status": 1,
            "name": "杜拉宝",
            "displayName": "消旋卡多曲颗粒(杜拉宝)",
            "displaySpec": "10mg*9袋/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "四川百利",
            "pieceNum": 9,
            "pieceUnit": "袋",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "消旋卡多曲颗粒",
            "medicineNmpn": "国药准字H20050411",
            "medicineDosageNum": 10,
            "medicineDosageUnit": "mg",
            "extendSpec": null,
            "position": "",
            "piecePrice": 2,
            "packagePrice": 18,
            "packageCostPrice": 3,
            "inTaxRat": 16,
            "outTaxRat": 16,
            "stockPieceCount": 0,
            "stockPackageCount": 10,
            "lastPackageCostPrice": 3,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2018-06-15T06:33:07Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 18,
            "chainPiecePrice": 2,
            "chainPackageCostPrice": 3,
            "pieceCount": 0,
            "packageCount": 10,
            "manufacturerFull": "四川百利药业有限责任公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 3,
            "limitPricePiecePrice": 2,
            "limitPricePackagePrice": 18,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": null
        },
        "usageInfo": {
            "ast": 0,
            "days": 1,
            "freq": "q6h",
            "ivgtt": 0,
            "usage": "口服",
            "dosage": "3",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "袋",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -19.2,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 0,
        "stockPackageCount": 10,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "e549b263b3424998b2ae1e79c30abc1e",
        "checked": true,
        "index": 4,
        "sortIndex": 3
    }, {
        "id": "ffffffff000000001f2d5418113d8011",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d800d",
        "sourceFormItemId": "ffffffff000000001f2d541810c4400d",
        "status": 0,
        "unit": "只",
        "name": "robins测试入库1",
        "unitCostPrice": 1,
        "unitCount": 4,
        "doseCount": 1,
        "unitPrice": 20,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 20,
        "discountPrice": -64,
        "totalPrice": 80,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 80,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 16,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 1,
        "productId": "ffffffff000000001aba7fc8107fc000",
        "groupId": 1,
        "sort": 3,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "ffffffff000000001aba7fc8107fc000",
            "goodsId": null,
            "status": 1,
            "name": "",
            "displayName": "robins测试入库1",
            "displaySpec": "10片*5只/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 5,
            "pieceUnit": "只",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "robins测试入库1",
            "medicineDosageNum": 10,
            "medicineDosageUnit": "片",
            "extendSpec": "",
            "position": "",
            "piecePrice": 20,
            "packagePrice": 100,
            "packageCostPrice": 5,
            "inTaxRat": 11,
            "outTaxRat": 11,
            "stockPieceCount": 0,
            "stockPackageCount": 7,
            "lastPackageCostPrice": 5,
            "needExecutive": 0,
            "shortId": "60100001227",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2021-10-10T10:29:14Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 14128,
            "chainPackagePrice": 100,
            "chainPiecePrice": 20,
            "chainPackageCostPrice": 5,
            "pieceCount": 0,
            "packageCount": 7,
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 1,
            "dispenseAveragePackageCostPrice": 5,
            "limitPricePiecePrice": 20,
            "limitPricePackagePrice": 100,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": ""
        },
        "usageInfo": {
            "ast": 0,
            "days": 1,
            "freq": "q6h",
            "ivgtt": 0,
            "usage": "口服",
            "dosage": "1",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "只",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -64,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 0,
        "stockPackageCount": 7,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "557205bb2edd496091a0a98921757d64",
        "checked": true,
        "index": 5,
        "sortIndex": 4
    }, {
        "id": "ffffffff000000001f2d5418113d8012",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d800d",
        "sourceFormItemId": "ffffffff000000001f2d541810c4400e",
        "status": 0,
        "unit": "盒",
        "name": "龙泽熊胆胶囊(熊胆丸)",
        "unitCostPrice": 25,
        "unitCount": 1,
        "doseCount": 1,
        "unitPrice": 15,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 15,
        "discountPrice": -12,
        "totalPrice": 15,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 15,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 3,
        "useDismounting": 0,
        "productType": 1,
        "productSubType": 1,
        "productId": "c2a338c6ed68a9d5bbefdbea31cd5c66",
        "groupId": 1,
        "sort": 4,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "c2a338c6ed68a9d5bbefdbea31cd5c66",
            "goodsId": null,
            "status": 1,
            "name": "熊胆丸",
            "displayName": "龙泽熊胆胶囊(熊胆丸)",
            "displaySpec": "0.25g*20粒/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "长春普制药股份有限公司",
            "pieceNum": 20,
            "pieceUnit": "粒",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "龙泽熊胆胶囊",
            "medicineNmpn": "国药准字Z22",
            "medicineDosageNum": 0.25,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0,
            "packagePrice": 15,
            "packageCostPrice": 25,
            "inTaxRat": 2,
            "outTaxRat": 0,
            "stockPieceCount": 0,
            "stockPackageCount": 9,
            "lastPackageCostPrice": 25,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-07-16T12:22:10Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 15,
            "chainPiecePrice": 0,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 9,
            "manufacturerFull": "长春普制药股份有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 25,
            "limitPricePiecePrice": 0,
            "limitPricePackagePrice": 15,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": null
        },
        "usageInfo": {
            "ast": 0,
            "days": 1,
            "freq": "q6h",
            "ivgtt": 0,
            "usage": "口服",
            "dosage": "2",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "粒",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -12,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 0,
        "stockPackageCount": 9,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "4e9bee450adf414e99ed01de7269bd5f",
        "checked": true,
        "index": 6,
        "sortIndex": 5
    }, {
        "id": "ffffffff000000001f2d5418113d8013",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d800d",
        "sourceFormItemId": "ffffffff000000001f2d541810c4400f",
        "status": 0,
        "unit": "片",
        "name": "robins_shortId测试",
        "unitCostPrice": 123,
        "unitCount": 1,
        "doseCount": 1,
        "unitPrice": 0.1,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.1,
        "discountPrice": -0.08,
        "totalPrice": 0.1,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 0.1,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.02,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 1,
        "productId": "ffffffff000000000fdb800009e34000",
        "groupId": null,
        "sort": 5,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "ffffffff000000000fdb800009e34000",
            "goodsId": null,
            "status": 1,
            "name": "",
            "displayName": "robins_shortId测试",
            "displaySpec": "10片/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "pieceNum": 10,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "robins_shortId测试",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0.1,
            "packagePrice": 1,
            "packageCostPrice": 1230,
            "inTaxRat": 10,
            "outTaxRat": 10,
            "stockPieceCount": 3.8,
            "stockPackageCount": 374,
            "lastPackageCostPrice": 1230,
            "needExecutive": 0,
            "shortId": "a",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2021-09-17T10:20:48Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 14128,
            "chainPackagePrice": 1,
            "chainPiecePrice": 0.1,
            "chainPackageCostPrice": 1230,
            "pieceCount": 3.8,
            "packageCount": 374,
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 1230,
            "limitPricePiecePrice": 0.1,
            "limitPricePackagePrice": 1,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": ""
        },
        "usageInfo": {
            "ast": 0,
            "days": 1,
            "freq": "qd",
            "ivgtt": 0,
            "usage": "口服",
            "dosage": "1",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "片",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -0.08,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 3.8,
        "stockPackageCount": 374,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "2d321989e1b840ae92295d288ee9d986",
        "checked": true,
        "index": 7,
        "sortIndex": 6
    }, {
        "id": "ffffffff000000001f2d5418113d8014",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d800d",
        "sourceFormItemId": "ffffffff000000001f2d541810c44010",
        "status": 0,
        "unit": "盒",
        "name": "抗HPV生物蛋白敷料",
        "unitCostPrice": 281.6,
        "unitCount": 1,
        "doseCount": 1,
        "unitPrice": 320,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 320,
        "discountPrice": -224,
        "totalPrice": 320,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 320,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 96,
        "useDismounting": 0,
        "productType": 2,
        "productSubType": 1,
        "productId": "ffffffff0000000012448c480b342004",
        "groupId": null,
        "sort": 6,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "ffffffff0000000012448c480b342004",
            "goodsId": null,
            "status": 1,
            "name": "抗HPV生物蛋白敷料",
            "displayName": "抗HPV生物蛋白敷料",
            "displaySpec": "TYC1:3g/支，1支/盒*3g/盒*3g/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 17,
            "type": 2,
            "subType": 1,
            "manufacturer": "山西锦波生物",
            "pieceNum": 3,
            "pieceUnit": "g",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "",
            "materialSpec": "TYC1:3g/支，1支/盒*3g/盒",
            "extendSpec": null,
            "position": "",
            "piecePrice": null,
            "packagePrice": 320,
            "packageCostPrice": 281.6,
            "inTaxRat": 0,
            "outTaxRat": 0,
            "stockPieceCount": 0,
            "stockPackageCount": 5000,
            "lastPackageCostPrice": 281.6,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2021-03-19T01:44:40Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 320,
            "chainPackageCostPrice": 281.6,
            "pieceCount": 0,
            "packageCount": 5000,
            "manufacturerFull": "山西锦波生物医药股份有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 281.6,
            "limitPricePiecePrice": null,
            "limitPricePackagePrice": 320,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": "TYC1:3g/支，1支/盒*3g/盒"
        },
        "usageInfo": {
            "ast": 0,
            "days": 1,
            "freq": "qd",
            "ivgtt": 0,
            "usage": "外用",
            "dosage": "1",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "g",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "TYC1:3g/支，1支/盒*3g/盒",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -224,
                "type": 1
            }],
            "giftRulePromotionInfos": [{
                "id": "2159614491261616128",
                "discountPrice": 0,
                "type": 2
            }],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 0,
        "stockPackageCount": 5000,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "65c512e38e4841889c2fc1d396f3c04b",
        "checked": true,
        "index": 8,
        "sortIndex": 7
    }],
}, {
    "id": "ffffffff000000001f2d5418113d8015",
    "patientOrderId": "ffffffff000000001f2d541810b82000",
    "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
    "chargeSheetId": "ffffffff000000001f2d5418113d8003",
    "sourceFormId": "ffffffff000000001f2d541810c44011",
    "sourceFormType": 4,
    "status": 0,
    "usageScopeId": null,
    "medicineStateScopeId": null,
    "medicineStateScopeName": null,
    "vendorId": null,
    "vendorName": null,
    "expectedTotalPrice": null,
    "expectedPriceFlag": 0,
    "specification": null,
    "medicalRecord": null,
    "deliveryInfo": null,
    "processRule": null,
    "sort": 1,
    "processInfo": null,
    "usageInfo": null,
    "totalPrice": 3.8,
    "totalDiscountPrice": -3.04,
    "discountedTotalPrice": 0.76,
    "sourceTotalPrice": null,
    "isCanBeRefund": 0,
    "dispensingStatus": null,
    "isAirPharmacyNeedPaid": 0,
    "pharmacyNo": 0,
    "pharmacyType": 0,
    "isCanRefundDoseCount": 0,
    "isCanRefundSingle": 1,
    "doseCount": 1,
    "refundDoseCount": null,
    "leftDoseCount": 1,
    "keyId": "96268765b2e844c8bcae4d0accde52ac",
    "isFullChecked": true,
    "formItems": [{
        "id": "ffffffff000000001f2d5418113d8016",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d8015",
        "sourceFormItemId": "ffffffff000000001f2d541810c44012",
        "status": 0,
        "unit": "粒",
        "name": "布洛芬缓释胶囊(芬必得)",
        "unitCostPrice": 1,
        "unitCount": 4,
        "doseCount": 1,
        "unitPrice": 0.95,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.95,
        "discountPrice": -3.04,
        "totalPrice": 3.8,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 3.8,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.76,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 1,
        "productId": "64a3acba15da4c34b8c352fe43319c55",
        "groupId": null,
        "sort": 0,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "64a3acba15da4c34b8c352fe43319c55",
            "goodsId": null,
            "status": 1,
            "name": "芬必得",
            "displayName": "布洛芬缓释胶囊(芬必得)",
            "displaySpec": "300mg*20粒/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "中美天津史克",
            "pieceNum": 20,
            "pieceUnit": "粒",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "布洛芬缓释胶囊",
            "medicineDosageNum": 300,
            "medicineDosageUnit": "mg",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0.95,
            "packagePrice": 19,
            "packageCostPrice": 20,
            "inTaxRat": 16,
            "outTaxRat": 16,
            "stockPieceCount": 7,
            "stockPackageCount": 4,
            "lastPackageCostPrice": 20,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-04-18T04:57:40Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 19,
            "chainPiecePrice": 0.95,
            "chainPackageCostPrice": 22,
            "pieceCount": 7,
            "packageCount": 4,
            "manufacturerFull": "中美天津史克制药有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 20,
            "limitPricePiecePrice": 0.95,
            "limitPricePackagePrice": 19,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": null
        },
        "usageInfo": {
            "ast": 0,
            "days": 2,
            "freq": "qd",
            "ivgtt": 0,
            "usage": "口服",
            "dosage": "2",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "粒",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -3.04,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 7,
        "stockPackageCount": 4,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "4f778a6e548f425282b940acbd426adc",
        "checked": true,
        "index": 9,
        "sortIndex": 8
    }],
}, {
    "id": "ffffffff000000001f2d5418113d8017",
    "patientOrderId": "ffffffff000000001f2d541810b82000",
    "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
    "chargeSheetId": "ffffffff000000001f2d5418113d8003",
    "sourceFormId": "ffffffff000000001f2d541810c44013",
    "sourceFormType": 5,
    "status": 0,
    "usageScopeId": null,
    "medicineStateScopeId": null,
    "medicineStateScopeName": null,
    "vendorId": null,
    "vendorName": null,
    "expectedTotalPrice": null,
    "expectedPriceFlag": 0,
    "specification": null,
    "medicalRecord": null,
    "deliveryInfo": null,
    "processRule": null,
    "sort": 0,
    "processInfo": null,
    "usageInfo": null,
    "totalPrice": 5.64,
    "totalDiscountPrice": -4.5,
    "discountedTotalPrice": 1.14,
    "sourceTotalPrice": null,
    "isCanBeRefund": 0,
    "dispensingStatus": null,
    "isAirPharmacyNeedPaid": 0,
    "pharmacyNo": 0,
    "pharmacyType": 0,
    "isCanRefundDoseCount": 0,
    "isCanRefundSingle": 1,
    "doseCount": 1,
    "refundDoseCount": null,
    "leftDoseCount": 1,
    "keyId": "90b7e3bc0de14a9eb5d3f61f549b29ef",
    "isFullChecked": true,
    "formItems": [{
        "id": "ffffffff000000001f2d5418113d8018",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d8017",
        "sourceFormItemId": "ffffffff000000001f2d541810c44014",
        "status": 0,
        "unit": "支",
        "name": "卡介菌多糖核酸注射液(斯奇康)",
        "unitCostPrice": 11,
        "unitCount": 4,
        "doseCount": 1,
        "unitPrice": 1,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 1,
        "discountPrice": -3.2,
        "totalPrice": 4,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 4,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.8,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 1,
        "productId": "e7f750e1ef9a497c878afa1521987780",
        "groupId": 1,
        "sort": 0,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "e7f750e1ef9a497c878afa1521987780",
            "goodsId": null,
            "status": 1,
            "name": "斯奇康",
            "displayName": "卡介菌多糖核酸注射液(斯奇康)",
            "displaySpec": "1ml*10支/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "湖南斯奇生物",
            "pieceNum": 10,
            "pieceUnit": "支",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "卡介菌多糖核酸注射液",
            "medicineNmpn": "国药准字S20020019",
            "medicineDosageNum": 1,
            "medicineDosageUnit": "ml",
            "extendSpec": null,
            "position": null,
            "piecePrice": 1,
            "packagePrice": 10,
            "packageCostPrice": 110,
            "inTaxRat": 33,
            "outTaxRat": 33,
            "stockPieceCount": 0,
            "stockPackageCount": 1,
            "lastPackageCostPrice": 110,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2021-04-01T08:21:15Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "shebaoCityCode": "XL03AXK015B002020104944",
            "shebaoNationalCode": "XL03AXK015B002020104944",
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 10,
            "chainV2DisableStatus": 10,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 10,
            "chainPiecePrice": 1,
            "chainPackageCostPrice": 20,
            "pieceCount": 0,
            "packageCount": 1,
            "manufacturerFull": "湖南斯奇生物制药有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "shebao": {
                "goodsId": "e7f750e1ef9a497c878afa1521987780",
                "goodsType": 1,
                "medicineNum": "**********",
                "name": "卡介菌多糖核酸注射液(卡介菌多糖核酸注射液(斯奇康))",
                "medicalFeeGrade": 0,
                "nationalCode": "XL03AXK015B002020104944"
            },
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 110,
            "limitPricePiecePrice": 1,
            "limitPricePackagePrice": 10,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": ""
        },
        "usageInfo": {
            "ast": 1,
            "days": 2,
            "freq": "qd",
            "ivgtt": 60,
            "usage": "静滴",
            "dosage": "2",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "滴/分钟",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "支",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -3.2,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 0,
        "stockPackageCount": 1,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "b7f43b8adefa46299adbc825d1bd1068",
        "checked": true,
        "index": 13,
        "sortIndex": 12
    }, {
        "id": "ffffffff000000001f2d5418113d8019",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d8017",
        "sourceFormItemId": "ffffffff000000001f2d541810c44015",
        "status": 0,
        "unit": "片",
        "name": "蛇伤散(11)",
        "unitCostPrice": 0.00813,
        "unitCount": 2,
        "doseCount": 1,
        "unitPrice": 0.18,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.18,
        "discountPrice": -0.28,
        "totalPrice": 0.36,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 0.36,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.08,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 1,
        "productId": "267d0ca64815c480760fb1bd9e81a0fd",
        "groupId": 1,
        "sort": 1,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "267d0ca64815c480760fb1bd9e81a0fd",
            "goodsId": null,
            "status": 1,
            "name": "11",
            "displayName": "蛇伤散(11)",
            "displaySpec": "1g*123片/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "barCode": "12344444",
            "manufacturer": "山东凤凰",
            "pieceNum": 123,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "蛇伤散",
            "medicineNmpn": "123345",
            "medicineDosageNum": 1,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0.18,
            "packagePrice": 22,
            "packageCostPrice": 1,
            "inTaxRat": 3,
            "outTaxRat": 3,
            "stockPieceCount": 0,
            "stockPackageCount": 100,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-03-24T06:25:03Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 22,
            "chainPiecePrice": 0.18,
            "chainPackageCostPrice": 1,
            "pieceCount": 0,
            "packageCount": 100,
            "manufacturerFull": "山东凤凰制药股份有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 1,
            "limitPricePiecePrice": 0.18,
            "limitPricePackagePrice": 22,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": ""
        },
        "usageInfo": {
            "ast": 0,
            "days": 2,
            "freq": "qd",
            "ivgtt": 60,
            "usage": "静滴",
            "dosage": "1",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "滴/分钟",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "片",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -0.28,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 0,
        "stockPackageCount": 100,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "29e4cf6e4d2f4100b7465330bd856166",
        "checked": true,
        "index": 14,
        "sortIndex": 13
    }, {
        "id": "ffffffff000000001f2d5418113d801a",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d8017",
        "sourceFormItemId": "ffffffff000000001f2d541810c44016",
        "status": 0,
        "unit": "片",
        "name": "酚咖片(芬必得)",
        "unitCostPrice": 1.15,
        "unitCount": 2,
        "doseCount": 1,
        "unitPrice": 0.64,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.64,
        "discountPrice": -1.02,
        "totalPrice": 1.28,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 1.28,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.26,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 1,
        "productId": "c74745c1cf364c78bcdbec7dcc33619a",
        "groupId": 1,
        "sort": 2,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "c74745c1cf364c78bcdbec7dcc33619a",
            "goodsId": null,
            "status": 1,
            "name": "芬必得",
            "displayName": "酚咖片(芬必得)",
            "displaySpec": "20片/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "中美天津史克",
            "pieceNum": 20,
            "pieceUnit": "片",
            "packageUnit": "盒",
            "dismounting": 1,
            "medicineCadn": "酚咖片",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0.64,
            "packagePrice": 12.8,
            "packageCostPrice": 23,
            "inTaxRat": 16,
            "outTaxRat": 16,
            "stockPieceCount": 11,
            "stockPackageCount": 116,
            "lastPackageCostPrice": 23,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "7ddc235c14274ef0b422bb9d13fe784d",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2018-08-09T04:00:16Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 12.8,
            "chainPiecePrice": 0.64,
            "chainPackageCostPrice": 23,
            "pieceCount": 11,
            "packageCount": 116,
            "manufacturerFull": "中美天津史克制药有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 23,
            "limitPricePiecePrice": 0.64,
            "limitPricePackagePrice": 12.8,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": null
        },
        "usageInfo": {
            "ast": 0,
            "days": 2,
            "freq": "qd",
            "ivgtt": 60,
            "usage": "静滴",
            "dosage": "1",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "滴/分钟",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "片",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -1.02,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 11,
        "stockPackageCount": 116,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "bd8c6e919cea442ea6736b78da01a97c",
        "checked": true,
        "index": 15,
        "sortIndex": 14
    }],
}, {
    "id": "ffffffff000000001f2d5418113d801b",
    "patientOrderId": "ffffffff000000001f2d541810b82000",
    "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
    "chargeSheetId": "ffffffff000000001f2d5418113d8003",
    "sourceFormId": "ffffffff000000001f2d541810c44017",
    "sourceFormType": 5,
    "status": 0,
    "usageScopeId": null,
    "medicineStateScopeId": null,
    "medicineStateScopeName": null,
    "vendorId": null,
    "vendorName": null,
    "expectedTotalPrice": null,
    "expectedPriceFlag": 0,
    "specification": null,
    "medicalRecord": null,
    "deliveryInfo": null,
    "processRule": null,
    "sort": 1,
    "processInfo": null,
    "usageInfo": null,
    "totalPrice": 48,
    "totalDiscountPrice": -38.4,
    "discountedTotalPrice": 9.6,
    "sourceTotalPrice": null,
    "isCanBeRefund": 0,
    "dispensingStatus": null,
    "isAirPharmacyNeedPaid": 0,
    "pharmacyNo": 0,
    "pharmacyType": 0,
    "isCanRefundDoseCount": 0,
    "isCanRefundSingle": 1,
    "doseCount": 1,
    "refundDoseCount": null,
    "leftDoseCount": 1,
    "keyId": "e4afa28728774303afa173a99530d2cb",
    "isFullChecked": true,
    "formItems": [{
        "id": "ffffffff000000001f2d5418113d801c",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d801b",
        "sourceFormItemId": "ffffffff000000001f2d541810c44018",
        "status": 0,
        "unit": "盒",
        "name": "复方太子参颗粒",
        "unitCostPrice": 24,
        "unitCount": 1,
        "doseCount": 1,
        "unitPrice": 48,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 48,
        "discountPrice": -38.4,
        "totalPrice": 48,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 48,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 9.6,
        "useDismounting": 0,
        "productType": 1,
        "productSubType": 1,
        "productId": "52fdf90e477540a1bb1c5e5712eeca5f",
        "groupId": 1,
        "sort": 0,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "52fdf90e477540a1bb1c5e5712eeca5f",
            "goodsId": null,
            "status": 1,
            "name": "",
            "displayName": "复方太子参颗粒",
            "displaySpec": "5g*6袋/盒",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 12,
            "type": 1,
            "subType": 1,
            "manufacturer": "闽东力捷迅药业",
            "pieceNum": 6,
            "pieceUnit": "袋",
            "packageUnit": "盒",
            "dismounting": 0,
            "medicineCadn": "复方太子参颗粒",
            "medicineNmpn": "国药准字B20020899",
            "medicineDosageNum": 5,
            "medicineDosageUnit": "g",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0,
            "packagePrice": 48,
            "packageCostPrice": 24,
            "inTaxRat": 3,
            "outTaxRat": 3,
            "stockPieceCount": 0,
            "stockPackageCount": 42,
            "lastPackageCostPrice": 24,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "34a01bccf80a4d4294a77b95d3d73ace",
            "lastModifiedUserId": "34a01bccf80a4d4294a77b95d3d73ace",
            "lastModifiedDate": "2018-09-05T06:29:09Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 48,
            "chainPiecePrice": 0,
            "chainPackageCostPrice": 24,
            "pieceCount": 0,
            "packageCount": 42,
            "manufacturerFull": "福建省闽东力捷迅药业有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 24,
            "limitPricePiecePrice": 0,
            "limitPricePackagePrice": 48,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": null
        },
        "usageInfo": {
            "ast": 1,
            "days": 2,
            "freq": "qd",
            "ivgtt": 60,
            "usage": "静滴",
            "dosage": "2",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "滴/分钟",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "袋",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -38.4,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 0,
        "stockPackageCount": 42,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "9af3c1ea932949199b25252821485e8f",
        "checked": true,
        "index": 10,
        "sortIndex": 9
    }],
}, {
    "id": "ffffffff000000001f2d5418113d8004",
    "patientOrderId": "ffffffff000000001f2d541810b82000",
    "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
    "chargeSheetId": "ffffffff000000001f2d5418113d8003",
    "sourceFormId": "ffffffff000000001f2d541810c44000",
    "sourceFormType": 6,
    "status": 0,
    "usageScopeId": null,
    "medicineStateScopeId": null,
    "medicineStateScopeName": null,
    "vendorId": null,
    "vendorName": null,
    "expectedTotalPrice": null,
    "expectedPriceFlag": 0,
    "specification": "中药饮片",
    "medicalRecord": null,
    "deliveryInfo": null,
    "processRule": null,
    "sort": 0,
    "processInfo": null,
    "usageInfo": {
        "ast": null,
        "days": null,
        "freq": "每天3次",
        "ivgtt": null,
        "usage": "煎服",
        "dosage": null,
        "checked": false,
        "payType": null,
        "doseCount": 1,
        "ivgttUnit": null,
        "usageDays": null,
        "usageType": null,
        "dosageUnit": null,
        "usageLevel": "每次200ml",
        "dailyDosage": "每天1剂",
        "isDecoction": false,
        "requirement": "",
        "processUsage": null,
        "usageSubType": null,
        "contactMobile": null,
        "specification": "中药饮片",
        "specialRequirement": null,
        "processBagUnitCount": 0
    },
    "totalPrice": 0.64,
    "totalDiscountPrice": -0.5,
    "discountedTotalPrice": 0.14,
    "sourceTotalPrice": null,
    "isCanBeRefund": 0,
    "dispensingStatus": null,
    "isAirPharmacyNeedPaid": 0,
    "pharmacyNo": 0,
    "pharmacyType": 0,
    "isCanRefundDoseCount": 0,
    "isCanRefundSingle": 0,
    "doseCount": 1,
    "refundDoseCount": 0,
    "leftDoseCount": 1,
    "keyId": "39c862154ebe4adabe63c0e472882fe2",
    "isFullChecked": true,
    "formItems": [{
        "id": "ffffffff000000001f2d5418113d8005",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d8004",
        "sourceFormItemId": "ffffffff000000001f2d541810c44001",
        "status": 0,
        "unit": "g",
        "name": "白花蛇舌草",
        "unitCostPrice": 0.0268,
        "unitCount": 1,
        "doseCount": 1,
        "unitPrice": 0.054,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.054,
        "discountPrice": -0.04,
        "totalPrice": 0.05,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 0.05,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.01,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 2,
        "productId": "b63f177287c74161967f09e55af0085d",
        "groupId": null,
        "sort": 0,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "b63f177287c74161967f09e55af0085d",
            "goodsId": null,
            "status": 1,
            "name": "",
            "displayName": "白花蛇舌草",
            "displaySpec": "",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "manufacturer": "四川中庸药业有限公司",
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "白花蛇舌草",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.054,
            "packagePrice": 0.054,
            "packageCostPrice": 0.0268,
            "inTaxRat": 16,
            "outTaxRat": 16,
            "stockPieceCount": 199000,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.0268,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "367218bb13e44dc0ba16fd5b5df803e5",
            "lastModifiedDate": "2018-07-12T09:28:03Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "shebaoCityCode": "T640200931",
            "shebaoNationalCode": "T640200931",
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.054,
            "chainPiecePrice": 0.054,
            "chainPackageCostPrice": 0.0268,
            "pieceCount": 199000,
            "packageCount": 0,
            "manufacturerFull": "四川中庸药业有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "shebao": {
                "goodsId": "b63f177287c74161967f09e55af0085d",
                "goodsType": 1,
                "medicineNum": "**********",
                "name": "白花蛇舌草(白花蛇舌草)",
                "medicalFeeGrade": 0,
                "nationalCode": "T640200931"
            },
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 0.0268,
            "limitPricePiecePrice": 0.054,
            "limitPricePackagePrice": 0.054,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": "中药饮片"
        },
        "usageInfo": {
            "ast": null,
            "days": null,
            "freq": "",
            "ivgtt": 0,
            "usage": "",
            "dosage": "",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -0.04,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 199000,
        "stockPackageCount": 0,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "cf579755291349e78652e8c67c7771ab",
        "checked": true,
        "index": 16,
        "sortIndex": 15
    }, {
        "id": "ffffffff000000001f2d5418113d8006",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d8004",
        "sourceFormItemId": "ffffffff000000001f2d541810c44002",
        "status": 0,
        "unit": "g",
        "name": "甘松",
        "unitCostPrice": 11,
        "unitCount": 2,
        "doseCount": 1,
        "unitPrice": 0.244,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.244,
        "discountPrice": -0.39,
        "totalPrice": 0.49,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 0.49,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.1,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 2,
        "productId": "3172cd5ce6eb4c00a01288be622d3b85",
        "groupId": null,
        "sort": 1,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "3172cd5ce6eb4c00a01288be622d3b85",
            "goodsId": null,
            "status": 1,
            "name": "",
            "displayName": "甘松",
            "displaySpec": "",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "manufacturer": "四川中庸药业有限公司",
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "甘松",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.244,
            "packagePrice": 0.244,
            "packageCostPrice": 11,
            "inTaxRat": 16,
            "outTaxRat": 16,
            "stockPieceCount": 6896,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 11,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "367218bb13e44dc0ba16fd5b5df803e5",
            "lastModifiedDate": "2018-07-18T08:12:53Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "shebaoCityCode": "T000800281",
            "shebaoNationalCode": "T000800281",
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.244,
            "chainPiecePrice": 0.244,
            "chainPackageCostPrice": 11,
            "pieceCount": 6896,
            "packageCount": 0,
            "manufacturerFull": "四川中庸药业有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "shebao": {
                "goodsId": "3172cd5ce6eb4c00a01288be622d3b85",
                "goodsType": 1,
                "medicineNum": "*********",
                "name": "甘松(甘松)",
                "medicalFeeGrade": 0,
                "nationalCode": "T000800281"
            },
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 11,
            "limitPricePiecePrice": 0.244,
            "limitPricePackagePrice": 0.244,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": "中药饮片"
        },
        "usageInfo": {
            "ast": null,
            "days": null,
            "freq": "",
            "ivgtt": 0,
            "usage": "",
            "dosage": "",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -0.39,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 6896,
        "stockPackageCount": 0,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "019421a94b8c42f09ed11ad3e39c9bf3",
        "checked": true,
        "index": 17,
        "sortIndex": 16
    }, {
        "id": "ffffffff000000001f2d5418113d8007",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d8004",
        "sourceFormItemId": "ffffffff000000001f2d541810c44003",
        "status": 0,
        "unit": "g",
        "name": "三七药酒",
        "unitCostPrice": 0.3333,
        "unitCount": 2,
        "doseCount": 1,
        "unitPrice": 0.005,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.005,
        "discountPrice": 0,
        "totalPrice": 0.01,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 0.01,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.01,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 2,
        "productId": "62ffb8d77ebf4a01a3d66a9f61c7b0b7",
        "groupId": null,
        "sort": 2,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "62ffb8d77ebf4a01a3d66a9f61c7b0b7",
            "goodsId": null,
            "status": 1,
            "name": "",
            "displayName": "三七药酒",
            "displaySpec": "",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "三七药酒",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": "",
            "piecePrice": 0.005,
            "packagePrice": 0.005,
            "packageCostPrice": 0.3333,
            "inTaxRat": 3,
            "outTaxRat": 3,
            "stockPieceCount": 76816,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.3333,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedDate": "2019-04-28T06:26:22Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.005,
            "chainPiecePrice": 0.005,
            "chainPackageCostPrice": 0.3333,
            "pieceCount": 76816,
            "packageCount": 0,
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 0.3333,
            "limitPricePiecePrice": 0.005,
            "limitPricePackagePrice": 0.005,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": "中药饮片"
        },
        "usageInfo": {
            "ast": null,
            "days": null,
            "freq": "",
            "ivgtt": 0,
            "usage": "",
            "dosage": "",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": null,
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 76816,
        "stockPackageCount": 0,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "ef12d3517b734ec8ab478c2108b5fe9f",
        "checked": true,
        "index": 18,
        "sortIndex": 17
    }, {
        "id": "ffffffff000000001f2d5418113d8008",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d8004",
        "sourceFormItemId": "ffffffff000000001f2d541810c44004",
        "status": 0,
        "unit": "g",
        "name": "泽泻",
        "unitCostPrice": 1,
        "unitCount": 1,
        "doseCount": 1,
        "unitPrice": 0.051,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.051,
        "discountPrice": -0.04,
        "totalPrice": 0.05,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 0.05,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.01,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 2,
        "productId": "5321c750be9546e1ad9f9604ff2c9c0b",
        "groupId": null,
        "sort": 3,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "5321c750be9546e1ad9f9604ff2c9c0b",
            "goodsId": null,
            "status": 1,
            "name": "盐炙",
            "displayName": "泽泻(盐炙)",
            "displaySpec": "",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "manufacturer": "四川中庸药业有限公司",
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "泽泻",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.051,
            "packagePrice": 0.051,
            "packageCostPrice": 1,
            "inTaxRat": 16,
            "outTaxRat": 16,
            "stockPieceCount": 46668,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 1,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "367218bb13e44dc0ba16fd5b5df803e5",
            "lastModifiedDate": "2018-07-18T07:16:45Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "shebaoCityCode": "T000600873",
            "shebaoNationalCode": "T000600873",
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.051,
            "chainPiecePrice": 0.051,
            "chainPackageCostPrice": 1,
            "pieceCount": 46668,
            "packageCount": 0,
            "manufacturerFull": "四川中庸药业有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "shebao": {
                "goodsId": "5321c750be9546e1ad9f9604ff2c9c0b",
                "goodsType": 1,
                "medicineNum": "**********",
                "name": "泽泻(泽泻(盐炙))",
                "medicalFeeGrade": 0,
                "nationalCode": "T000600873"
            },
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 1,
            "limitPricePiecePrice": 0.051,
            "limitPricePackagePrice": 0.051,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": "中药饮片"
        },
        "usageInfo": {
            "ast": null,
            "days": null,
            "freq": "",
            "ivgtt": 0,
            "usage": "",
            "dosage": "",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -0.04,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 46668,
        "stockPackageCount": 0,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "e2b3629642b94fccb8e6ae65da5891e2",
        "checked": true,
        "index": 19,
        "sortIndex": 18
    }, {
        "id": "ffffffff000000001f2d5418113d8009",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d8004",
        "sourceFormItemId": "ffffffff000000001f2d541810c44005",
        "status": 0,
        "unit": "g",
        "name": "小茴香",
        "unitCostPrice": 0.3,
        "unitCount": 1,
        "doseCount": 1,
        "unitPrice": 0.04,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.04,
        "discountPrice": -0.03,
        "totalPrice": 0.04,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 0.04,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.01,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 2,
        "productId": "34fa4a8004cd4a17b4a2e8185357ea5b",
        "groupId": null,
        "sort": 4,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "34fa4a8004cd4a17b4a2e8185357ea5b",
            "goodsId": null,
            "status": 1,
            "name": "",
            "displayName": "小茴香",
            "displaySpec": "",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "manufacturer": "四川中庸药业有限公司",
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "小茴香",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.04,
            "packagePrice": 0.04,
            "packageCostPrice": 0.3,
            "inTaxRat": 16,
            "outTaxRat": 16,
            "stockPieceCount": 14638,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.3,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "7ddc235c14274ef0b422bb9d13fe784d",
            "lastModifiedDate": "2018-07-19T00:44:55Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "shebaoCityCode": "T000700788",
            "shebaoNationalCode": "T000700788",
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.04,
            "chainPiecePrice": 0.04,
            "chainPackageCostPrice": 0.3,
            "pieceCount": 14638,
            "packageCount": 0,
            "manufacturerFull": "四川中庸药业有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "shebao": {
                "goodsId": "34fa4a8004cd4a17b4a2e8185357ea5b",
                "goodsType": 1,
                "medicineNum": "*********",
                "name": "小茴香(小茴香)",
                "medicalFeeGrade": 0,
                "nationalCode": "T000700788"
            },
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 0.3,
            "limitPricePiecePrice": 0.04,
            "limitPricePackagePrice": 0.04,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": "中药饮片"
        },
        "usageInfo": {
            "ast": null,
            "days": null,
            "freq": "",
            "ivgtt": 0,
            "usage": "",
            "dosage": "",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -0.03,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 14638,
        "stockPackageCount": 0,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "5f6da3f1609b4eae82928ccd5df16fc7",
        "checked": true,
        "index": 20,
        "sortIndex": 19
    }],
}, {
    "id": "ffffffff000000001f2d5418113d800a",
    "patientOrderId": "ffffffff000000001f2d541810b82000",
    "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
    "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
    "chargeSheetId": "ffffffff000000001f2d5418113d8003",
    "sourceFormId": "ffffffff000000001f2d541810c44006",
    "sourceFormType": 6,
    "status": 0,
    "usageScopeId": null,
    "medicineStateScopeId": null,
    "medicineStateScopeName": null,
    "vendorId": null,
    "vendorName": null,
    "expectedTotalPrice": null,
    "expectedPriceFlag": 0,
    "specification": "中药饮片",
    "medicalRecord": null,
    "deliveryInfo": null,
    "processRule": null,
    "sort": 1,
    "processInfo": null,
    "usageInfo": {
        "ast": null,
        "days": null,
        "freq": "1日3次",
        "ivgtt": null,
        "usage": "冲服",
        "dosage": null,
        "checked": false,
        "payType": null,
        "doseCount": 2,
        "ivgttUnit": null,
        "usageDays": "",
        "usageType": null,
        "dosageUnit": null,
        "usageLevel": "每次1袋",
        "dailyDosage": "1日1剂",
        "isDecoction": false,
        "requirement": "饭后服用",
        "processUsage": null,
        "usageSubType": null,
        "contactMobile": null,
        "specification": "中药饮片",
        "specialRequirement": null,
        "processBagUnitCount": 0
    },
    "totalPrice": 1.14,
    "totalDiscountPrice": -0.9,
    "discountedTotalPrice": 0.24,
    "sourceTotalPrice": null,
    "isCanBeRefund": 0,
    "dispensingStatus": null,
    "isAirPharmacyNeedPaid": 0,
    "pharmacyNo": 0,
    "pharmacyType": 0,
    "isCanRefundDoseCount": 0,
    "isCanRefundSingle": 0,
    "doseCount": 2,
    "refundDoseCount": 0,
    "leftDoseCount": 2,
    "keyId": "45e3fbfd9a72456f8917b4727fc90c62",
    "isFullChecked": true,
    "formItems": [{
        "id": "ffffffff000000001f2d5418113d800b",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d800a",
        "sourceFormItemId": "ffffffff000000001f2d541810c44007",
        "status": 0,
        "unit": "g",
        "name": "山药",
        "unitCostPrice": 0.039,
        "unitCount": 1,
        "doseCount": 2,
        "unitPrice": 0.08,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.08,
        "discountPrice": -0.12,
        "totalPrice": 0.16,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 0.16,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.04,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 2,
        "productId": "87c0473cc0a74f70b0acdb8d728391d3",
        "groupId": null,
        "sort": 0,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "先煎一小时",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "87c0473cc0a74f70b0acdb8d728391d3",
            "goodsId": null,
            "status": 1,
            "name": "",
            "displayName": "山药",
            "displaySpec": "",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "manufacturer": "四川中庸药业有限公司",
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "山药",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.08,
            "packagePrice": 0.08,
            "packageCostPrice": 0.039,
            "inTaxRat": 3,
            "outTaxRat": 3,
            "stockPieceCount": 120659,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 0.039,
            "needExecutive": 0,
            "shortId": "**********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "6bffb2108d9c4170884d91d69270cdb0",
            "lastModifiedDate": "2018-09-01T03:27:20Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "shebaoCityCode": "T001700667",
            "shebaoNationalCode": "T001700667",
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.08,
            "chainPiecePrice": 0.08,
            "chainPackageCostPrice": 0.039,
            "pieceCount": 120659,
            "packageCount": 0,
            "manufacturerFull": "四川中庸药业有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "shebao": {
                "goodsId": "87c0473cc0a74f70b0acdb8d728391d3",
                "goodsType": 1,
                "medicineNum": "**********",
                "name": "山药(山药)",
                "medicalFeeGrade": 0,
                "nationalCode": "T001700667"
            },
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 0.039,
            "limitPricePiecePrice": 0.08,
            "limitPricePackagePrice": 0.08,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": "中药饮片"
        },
        "usageInfo": {
            "ast": null,
            "days": null,
            "freq": "",
            "ivgtt": 0,
            "usage": "",
            "dosage": "",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "先煎一小时",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -0.12,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 120659,
        "stockPackageCount": 0,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "d0c365e983dd49b0a936486f0803a275",
        "checked": true,
        "index": 11,
        "sortIndex": 10
    }, {
        "id": "ffffffff000000001f2d5418113d800c",
        "clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
        "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
        "patientOrderId": "ffffffff000000001f2d541810b82000",
        "chargeSheetId": "ffffffff000000001f2d5418113d8003",
        "chargeFormId": "ffffffff000000001f2d5418113d800a",
        "sourceFormItemId": "ffffffff000000001f2d541810c44008",
        "status": 0,
        "unit": "g",
        "name": "甘松",
        "unitCostPrice": 11,
        "unitCount": 2,
        "doseCount": 2,
        "unitPrice": 0.244,
        "expectedUnitPrice": null,
        "sourceUnitPrice": 0.244,
        "discountPrice": -0.78,
        "totalPrice": 0.98,
        "expectedTotalPrice": null,
        "sourceTotalPrice": 0.98,
        "adjustmentPrice": 0,
        "discountedTotalPrice": 0.2,
        "useDismounting": 1,
        "productType": 1,
        "productSubType": 2,
        "productId": "3172cd5ce6eb4c00a01288be622d3b85",
        "groupId": null,
        "sort": 1,
        "composeType": 0,
        "composeChildren": [],
        "paySource": 0,
        "isUseLimitPrice": 0,
        "isProductDeleted": 0,
        "isGift": 0,
        "isAirPharmacy": 0,
        "specialRequirement": "",
        "productInfo": {
            "goodsVersion": 0,
            "sourceFlag": 1,
            "id": "3172cd5ce6eb4c00a01288be622d3b85",
            "goodsId": null,
            "status": 1,
            "name": "",
            "displayName": "甘松",
            "displaySpec": "",
            "organId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "typeId": 14,
            "type": 1,
            "subType": 2,
            "manufacturer": "四川中庸药业有限公司",
            "pieceNum": 1,
            "pieceUnit": "g",
            "packageUnit": null,
            "dismounting": 1,
            "medicineCadn": "甘松",
            "materialSpec": "中药饮片",
            "extendSpec": null,
            "position": null,
            "piecePrice": 0.244,
            "packagePrice": 0.244,
            "packageCostPrice": 11,
            "inTaxRat": 16,
            "outTaxRat": 16,
            "stockPieceCount": 6896,
            "stockPackageCount": 0,
            "lastPackageCostPrice": 11,
            "needExecutive": 0,
            "shortId": "*********",
            "composeUseDismounting": 0,
            "composeSort": 0,
            "createdUserId": "6e45706922a74966ab51e4ed1e604641",
            "lastModifiedUserId": "367218bb13e44dc0ba16fd5b5df803e5",
            "lastModifiedDate": "2018-07-18T08:12:53Z",
            "combineType": 0,
            "medicalFeeGrade": 0,
            "shebaoCityCode": "T000800281",
            "shebaoNationalCode": "T000800281",
            "disable": 0,
            "chainDisable": 0,
            "v2DisableStatus": 0,
            "chainV2DisableStatus": 0,
            "disableSell": 0,
            "isSell": 1,
            "customTypeId": 0,
            "chainPackagePrice": 0.244,
            "chainPiecePrice": 0.244,
            "chainPackageCostPrice": 11,
            "pieceCount": 6896,
            "packageCount": 0,
            "manufacturerFull": "四川中庸药业有限公司",
            "chainId": "6a869c22abee4ffbaef3e527bbb70aeb",
            "supplier": null,
            "shebao": {
                "goodsId": "3172cd5ce6eb4c00a01288be622d3b85",
                "goodsType": 1,
                "medicineNum": "*********",
                "name": "甘松(甘松)",
                "medicalFeeGrade": 0,
                "nationalCode": "T000800281"
            },
            "pharmacyNo": 0,
            "defaultInOutTax": 0,
            "dispenseAveragePackageCostPrice": 11,
            "limitPricePiecePrice": 0.244,
            "limitPricePackagePrice": 0.244,
            "composeLimitPricePiecePrice": null,
            "composeLimitPricePackagePrice": null,
            "limitPriceChildren": [],
            "limitPriceEffect": false,
            "useLimitPrice": false,
            "cMSpec": "中药饮片"
        },
        "usageInfo": {
            "ast": null,
            "days": null,
            "freq": "",
            "ivgtt": 0,
            "usage": "",
            "dosage": "",
            "checked": true,
            "payType": null,
            "doseCount": 0,
            "ivgttUnit": "",
            "usageDays": null,
            "usageType": null,
            "dosageUnit": "",
            "usageLevel": null,
            "dailyDosage": null,
            "isDecoction": false,
            "requirement": null,
            "processUsage": null,
            "usageSubType": null,
            "contactMobile": null,
            "specification": "",
            "specialRequirement": "",
            "processBagUnitCount": 0
        },
        "promotionInfo": {
            "adjustmentFee": null,
            "deductDiscountInfos": [],
            "discountPromotionInfos": [{
                "id": "2098116709975924736",
                "discountPrice": -0.78,
                "type": 1
            }],
            "giftRulePromotionInfos": [],
            "couponInfos": [],
            "patientPointPromotionFee": 0,
            "ids": []
        },
        "isOutOfStock": 0,
        "isCanNotDelete": 0,
        "stockPieceCount": 6896,
        "stockPackageCount": 0,
        "canRefundUnitCount": 0,
        "canRefundDoseCount": 0,
        "canRefundDeductCount": 0,
        "deductedTotalCount": 0,
        "keyId": "bc895204e4e94dd981e0218add46f371",
        "checked": true,
        "index": 12,
        "sortIndex": 11
    }],
}]
}

-->

<template>
    <div>
        <template v-for="(form, formIndex) in forms">
            <template v-if="form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE">
                <div
                    data-type="header"
                    :data-count="form.countData.value"
                    :data-prescription-group="formIndex"
                    :data-pendants-index="`${formIndex}normalGroup`"
                    class="medicine-tag-header medicine-tag-header-patient-container"
                >
                    <div
                        class="medicine-tag-patient-info"
                        overflow
                    >
                        <span class="font-bold medicine-tag-patient-name">{{ patient.name }}</span>
                        {{ patient.sex }}
                        {{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}
                        <template v-if="config.mobile">
                            <span class="size_40_30">{{ patient.mobile | filterMobile40(config.mobileType) }}</span>
                            <span class="not_size_40_30">{{ patient.mobile | filterMobileV2(config.mobileType) }}</span>
                        </template>
                    </div>
                    <div
                        v-if="!isSettingPreview"
                        class="count-data-container"
                    >
                        <span style="line-height: 18px;">{{ NUMBER_ICONS[form.countData.countId] }}</span>
                        <span>&nbsp;× {{ form.countData.value }}份</span>
                    </div>
                </div>
                <div class="medicine-tag-chinese-content">
                    <print-row
                        v-if="form.usageInfo"
                        class="medicine-tag-chinese-usage flex-wrapper"
                    >
                        <print-col
                            :span="isShowDepartment && departmentName ? 17 : 24"
                            overflow
                        >
                            {{ form.usageInfo.specification }} 共{{ getDoseCount(form) }}剂
                            <template v-if="form.usageInfo && form.usageInfo.isDecoction && form.usageInfo.totalProcessCount">
                                共{{ form.usageInfo.totalProcessCount }}袋
                            </template>
                        </print-col>


                        <print-col
                            v-if="isShowDepartment"
                            :span="7"
                            overflow
                            class="tag-department-name"
                        >
                            {{ departmentName }}
                        </print-col>
                    </print-row>
                    <div
                        v-if="form.usageInfo"
                        class="medicine-tag-chinese-usage  flex-wrapper"
                        overflow
                    >
                        <span class="item-label">用法：</span><span class="font-bold item-usage">
                            {{ form.usageInfo.usage }}
                            {{ form.usageInfo.usageDays }}
                            {{ form.usageInfo.usageLevel }}
                            <br />
                            {{ form.usageInfo.dailyDosage }}
                            {{ form.usageInfo.freq }}
                        </span>
                    </div>
                    <div
                        class="medicine-tag-chinese-usage"
                        overflow
                    >
                        备注：{{ form.usageInfo && form.usageInfo.requirement }}
                    </div>
                </div>
                <div

                    class="medicine-tag-footer"
                    data-type="footer"
                    :data-pendants-index="`${formIndex}normalGroup`"
                >
                    <div
                        v-if="config.chineseRemark"
                        overflow
                        class="medicine-tag-footer-info"
                    >
                        {{ config.chineseRemark }}
                    </div>
                    <div
                        v-if="config.printDate"
                        class="medicine-tag-footer-time"
                    >
                        {{ new Date() | parseTime('y-m-d h:i:s') }}
                    </div>
                </div>
            </template>

            <template v-else>
                <template v-if="getWesternMedicine(form).groupItems.length">
                    <template v-for="(formItems, formItemIndex) in getWesternMedicine(form).groupItems">
                        <div
                            data-type="header"
                            :data-count="formItems[0].countData.value"
                            :data-prescription-group="formIndex"
                            :data-pendants-index="`${formIndex}${formItemIndex}hasGroupId`"
                            class="medicine-tag-header medicine-tag-header-patient-container medicine-tag-header-group"
                        >
                            <div
                                class="medicine-tag-patient-info"
                                overflow
                            >
                                {{ NUMBER_ICONS[formItems[0].groupId] }}
                                <span class="font-bold">{{ patient.name }}</span>
                                {{ patient.sex }}
                                {{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}
                                <template v-if="config.mobile">
                                    <span class="size_40_30">{{ patient.mobile | filterMobile40(config.mobileType) }}</span>
                                    <span class="not_size_40_30">{{ patient.mobile | filterMobileV2(config.mobileType) }}</span>
                                </template>
                            </div>
                            <div
                                v-if="!isSettingPreview"
                                class="count-data-container"
                            >
                                <span style="line-height: 18px;">{{ NUMBER_ICONS[formItems[0].countData.countId] }}</span>
                                <span>&nbsp;× {{ formItems[0].countData.value }}份</span>
                            </div>
                        </div>

                        <div data-type="mix-box">
                            <div
                                data-type="group"
                                class="medicine-tag-western-content medicine-tag-content-group"
                            >
                                <template v-for="items in splitGroupItems(formItems)">
                                    <print-row
                                        v-for="item in items"
                                        data-type="item"
                                        class="western-group-item medicine-tag-item-group"
                                    >
                                        <print-col
                                            :span="17"
                                            overflow
                                            class="western-group-name"
                                        >
                                            {{ item.name }}
                                        </print-col>
                                        <print-col
                                            v-if="item.usageInfo"
                                            :span="7"
                                            overflow
                                            class="text-right western-group-dosage"
                                        >
                                            {{ item.usageInfo.dosage }}{{ item.usageInfo.dosageUnit }}/次
                                        </print-col>
                                        <print-col
                                            :span="24"
                                            class="western-group-spec"
                                            overflow
                                        >
                                            {{ goodsSpec(item.productInfo) }}
                                            <template v-if="config.westernTotalAmount">
                                                ×{{ item.unitCount }}{{ item.unit }}
                                            </template>
                                            <template v-if="config.westernSkinTest && item.ast === AstEnum.PI_SHI">
                                                {{ item.astResult | filterSkinTest }}
                                            </template>
                                        </print-col>
                                    </print-row>
                                </template>
                            </div>
                        </div>
                        <div
                            v-if="formItems[0].usageInfo"
                            class="medicine-tag-group-footer"
                            data-type="footer"
                            :data-pendants-index="`${formIndex}${formItemIndex}hasGroupId`"
                        >
                            <div class="western-group-usage-footer font-bold">
                                {{ formItems[0].usageInfo.usage }}
                                {{ freqFormat(formItems[0].usageInfo.freq) }}
                                {{ formItems[0].usageInfo.days }}天
                                {{ formItems[0].usageInfo.ivgtt || '' }}{{ formItems[0].usageInfo.ivgttUnit || '' }}
                            </div>
                            <print-row
                                class="western-group-usage-footer"
                            >
                                <print-col
                                    v-if="config.printDate"
                                    :span="isShowDepartment && departmentName ? 12 : 24"
                                    overflow
                                >
                                    {{ new Date() | parseTime('y-m-d h:i:s') }}
                                </print-col>
                                <print-col
                                    v-if="isShowDepartment"
                                    :span="12"
                                    class="tag-department-name"
                                    :class="{'is-text-left': !config.printDate}"
                                    overflow
                                >
                                    {{ departmentName }}
                                </print-col>
                            </print-row>
                        </div>
                    </template>
                </template>


                <template v-if="getWesternMedicine(form).formItems.length">
                    <template v-for="(item, itemIndex) in getWesternMedicine(form).formItems">
                        <div
                            data-type="header"
                            :data-count="item.countData.value"
                            :data-prescription-group="formIndex"
                            :data-pendants-index="`${formIndex}${itemIndex}noGroupId`"
                            class="medicine-tag-header medicine-tag-header-patient-container"
                        >
                            <div
                                class="medicine-tag-patient-info"
                                overflow
                            >
                                <span class="medicine-tag-patient-name font-bold">{{ patient.name }}</span>
                                {{ patient.sex }}
                                {{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}
                                <template v-if="config.mobile">
                                    <span class="size_40_30">{{ patient.mobile | filterMobile40(config.mobileType) }}</span>
                                    <span class="not_size_40_30">{{ patient.mobile | filterMobileV2(config.mobileType) }}</span>
                                </template>
                            </div>
                            <div
                                v-if="!isSettingPreview"
                                class="count-data-container"
                            >
                                <span style="line-height: 18px;">{{ NUMBER_ICONS[item.countData.countId] }}</span>
                                <span>&nbsp;× {{ item.countData.value }}份</span>
                            </div>
                        </div>

                        <div class="medicine-tag-western-content western-tag-content">
                            <print-row
                                class="western-no-group-item"
                            >
                                <print-col
                                    :span="isShowDepartment && departmentName ? 17 : 24"
                                    overflow
                                >
                                    {{ item.name }}
                                </print-col>

                                <print-col
                                    v-if="isShowDepartment"
                                    :span="7"
                                    overflow
                                    class="tag-department-name"
                                >
                                    {{ departmentName }}
                                </print-col>
                            </print-row>
                            <div
                                class="western-no-group-item"
                                overflow
                            >
                                <span class="item-label">规格：</span>{{ goodsSpec(item.productInfo) }}
                                <template v-if="config.westernTotalAmount">
                                    ×{{ item.unitCount }}{{ item.unit }}
                                </template>
                            </div>
                            <div
                                v-if="item.usageInfo"
                                class="western-no-group-item overflow-unset flex-wrapper"
                            >
                                <span class="item-label">用法：</span>
                                <span class="font-bold item-usage">
                                    {{ item.usageInfo.usage }}
                                    每次{{ item.usageInfo.dosage }}{{ item.usageInfo.dosageUnit }}
                                    <br />
                                    {{ freqFormat(item.usageInfo.freq) }}
                                    <template v-if="config.westernDays">共{{ item.usageInfo.days }}天</template>
                                    <template v-if="config.westernSkinTest && item.ast === AstEnum.PI_SHI">{{ item.astResult | filterSkinTest }}</template>
                                </span>
                            </div>
                        </div>
                        <div
                            class="medicine-tag-footer"
                            data-type="footer"
                            :data-pendants-index="`${formIndex}${itemIndex}noGroupId`"
                        >
                            <div class="medicine-tag-footer-info">
                                备注：{{ item.specialRequirement || (item.usageInfo && item.usageInfo.specialRequirement) }}
                            </div>
                            <div
                                v-if="config.printDate"
                                class="medicine-tag-footer-time"
                            >
                                {{ new Date() | parseTime('y-m-d h:i:s') }}
                            </div>
                        </div>
                    </template>
                </template>
            </template>
        </template>
    </div>
</template>

<script>
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";

    import { formatAge, goodsSpec, obj2arr, parseTime } from "./common/utils.js";
    import { AstEnum, NUMBER_ICONS, SourceFormTypeEnum } from "./common/constants.js";
    import { filterMobileV2, freqFormat } from "./common/medical-transformat.js";

    import PageSizeMap, { Orientation, TAG60_60 } from "../share/page-size.js";

    import PrintRow from "./components/layout/print-row.vue";
    import PrintCol from "./components/layout/print-col.vue";
    import clone from "./common/clone.js";
    import MedicineTagHandler from "./data-handler/medicine-tag-handler";

    export default {
        name: "MedicineTag",
        DataHandler: MedicineTagHandler,
        businessKey: PrintBusinessKeyEnum.MEDICINE_TAG,
        components: {
            PrintCol,
            PrintRow,
        },
        pages: [
            {
                paper: PageSizeMap.TAG40_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG40_60,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG50_70,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_30,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_40,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG60_60,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG70_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG70_50,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG80_40,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.TAG80_50,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },

        ],
        filters: {
            filterMobileV2,
            parseTime,
            filterMobile40(mobile, formatType) {
                if(!mobile) {
                    return "";
                }
                if(formatType === 2) {
                    return mobile;
                }
                if(formatType === 1) {
                    return mobile.substr(0,3) + "**" + mobile.substr(9, 11);
                }
                if(formatType === 0) {
                    return '';
                }
                return mobile
            },
            filterSkinTest(value) {
                if (!value || !value.result) return '皮试()';
                if (value.result === '阳性') return '皮试(+)';
                return '皮试(-)';
            },
        },
        props: {
            renderData: {
                type: Object,
            },
            extra: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                SourceFormTypeEnum,
                NUMBER_ICONS,
                AstEnum,
            }
        },
        computed: {
            printData() {
                return this.renderData.printData;
            },
            config() {
                if (this.renderData.config.medicineTag?.common) {
                    return this.renderData.config.medicineTag.common;
                }
                return this.renderData.config.medicineTag || {};
            },
            forms() {
                return clone(this.printData.forms).filter((item) => {
                    item.formItems = item.formItems.filter((item) => item.sourceItemType !== 1);
                    return item.formItems.length > 0
                }) || []
            },
            patient() {
                return this.printData.patient || {};
            },
            /**
             * 是否是打印设置页面的预览
             * 如果是的话需要隐藏份数标签
             * @returns {boolean}
             */
            isSettingPreview() {
                return this.extra?.isPreview === true;
            },
            // 是否打印开单科室
            isShowDepartment() {
                return this.config.billDepartment;
            },
            departmentName() {
                return this.printData.departmentName;
            },
        },
        methods: {
            formatAge,
            freqFormat,
            goodsSpec,
            /**
             * @desc 对西药和输液处方 根据 groupId 拆分
             * <AUTHOR>
             * @date 2022-01-26 19:32:55
             * @params
             * @return
             */
            getWesternMedicine(form) {
                let formItemsIncludeIdObj = {};
                let formItemsNoId = [];
                form.formItems.forEach(item => {
                    if (item.groupId) {
                        if (!formItemsIncludeIdObj[item.groupId]) {
                            formItemsIncludeIdObj[item.groupId] = [];
                        }
                        formItemsIncludeIdObj[item.groupId].push(item);
                    } else {
                        formItemsNoId.push(item);
                    }
                })
                return {
                    groupItems: obj2arr(formItemsIncludeIdObj),
                    formItems: formItemsNoId,
                }
            },
            splitGroupItems(formItems) {
                let res = [];
                const LEN = 3;
                let items = clone(formItems);
                while (items.length) {
                    if (items.length >= 3) {
                        res.push(items.splice(0, LEN));
                    } else {
                        res.push(items.splice(0, items.length));
                    }
                }
                return res;
            },
            getDoseCount(form) {
                return form.leftDoseCount ? form.leftDoseCount : form.usageInfo.doseCount;
            },
        },
    }
</script>

<style lang="scss">
@import "./templates/components/layout/_print-layout";

* {
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "微软雅黑";
}

.flex-wrapper {
    display: flex;
}

.abc-page_preview {
    &.abc-page {
        overflow: visible;
    }

    .count-data-container {
        display: flex !important;
    }
}

.font-bold {
    font-weight: bold;
}

.text-right {
    text-align: right;
}

.medicine-tag-header {
    padding: 3pt 0 3pt 4pt;
    border-bottom: 1pt solid #000000;
}

.medicine-tag-header-patient-container {
    position: relative;

    .count-data-container {
        position: absolute;
        top: 5px;
        right: -82px;
        display: none;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 34px;
        font-size: 14px;
        font-weight: 500;
        color: #88a6cb;
        vertical-align: middle;
        background-color: #ffffff;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }
}

.medicine-tag-footer {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    //height: 25px;
    padding: 3pt 4pt;
    border-top: 1pt solid #000000;
}

.medicine-tag-group-footer {
    padding: 0 4pt 3pt 4pt;
}

.medicine-tag-patient-info,
.medicine-tag-footer-info {
    flex: 1;
    overflow: hidden;
    font-size: 8pt;
    line-height: 12pt;
    word-break: break-all;
    white-space: nowrap;

    .size_40_30 {
        display: none;
    }
}

.medicine-tag-patient-info {
    font-size: 10pt;
    line-height: 12pt;
}

.medicine-tag-footer-info {
    font-size: 9pt;
    line-height: 12pt;
}

.medicine-tag-footer-time {
    font-size: 9pt;
    line-height: 12pt;
}

.western-group-usage-footer {
    font-size: 8pt;
    line-height: 10pt;
}

.medicine-tag-chinese-content {
    padding: 3pt 0 0 4pt;

    .medicine-tag-chinese-usage {
        margin-bottom: 4pt;
        overflow: hidden;
        font-size: 10pt;
        line-height: 14pt;
        word-break: break-all;
        white-space: nowrap;

        .item-usage {
            font-size: 11pt;
            line-height: 15pt;
        }
    }
}

.tag-department-name {
    text-align: right;
  .is-text-left {
    text-align: left;
  }
}

.medicine-tag-content-group {
    padding: 3pt 4pt 0 4pt !important;
}

.medicine-tag-western-content {
    padding: 3pt 4pt 0 4pt;

    .western-group-item {
        .western-group-name {
            max-height: 11pt;
            font-size: 9pt;
            line-height: 11pt;
        }

        .western-group-dosage,
        .western-group-spec {
            max-height: 10pt;
            font-size: 8pt;
            line-height: 10pt;
        }

        .western-group-name,
        .western-group-dosage,
        .western-group-spec {
            overflow: hidden;
            word-break: break-all;
            white-space: nowrap;
        }
    }

    .medicine-tag-item-group {
        &:not(:last-of-type) {
            margin-bottom: 1pt !important;
        }
    }

    .western-no-group-item {
        margin-bottom: 4pt;
        overflow: hidden;
        font-size: 10pt;
        line-height: 14pt;
        word-break: break-all;
        white-space: nowrap;

        .item-usage {
            font-size: 11pt;
            line-height: 15pt;
        }

        &.overflow-unset {
            overflow: unset;
        }
    }
}

[data-size=page_40mm×30mm] {
    .size_40_30 {
        display: inline-block;
    }

    .not_size_40_30 {
        display: none;
    }

    .medicine-tag-header {
        padding: 0 0 0 4pt;
    }

    .medicine-tag-header-group {
        padding-top: 0;
        padding-bottom: 0;
    }

    .medicine-tag-footer {
        padding: 1pt 4pt 0 4pt;
    }

    .medicine-tag-group-footer {
        padding: 0 4pt 1pt 4pt;
    }

    .medicine-tag-western-content {
        padding: 1pt 0 0 4pt;
    }

    .medicine-tag-content-group {
        padding: 2pt 4pt 0 4pt !important;
    }

    .medicine-tag-chinese-content {
        padding: 1pt 0 0 4pt;
    }

    .medicine-tag-patient-info {
        font-size: 8pt;
        line-height: 11pt;

        .medicine-tag-patient-name {
            font-size: 8pt;
            line-height: 11pt;
        }
    }

    .medicine-tag-footer-info {
        font-size: 8pt;
        line-height: 12pt;
    }

    .medicine-tag-footer-time {
        font-size: 8pt;
        line-height: 12pt;
    }

    .western-group-usage-footer {
        font-size: 6pt;
        line-height: 7pt;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
    }

    .medicine-tag-chinese-content {
        .medicine-tag-chinese-usage {
            margin-bottom: 1pt;
            font-size: 8pt;
            line-height: 13pt;

            .item-label {
                display: none;
            }

            .item-usage {
                font-size: 10pt;
                line-height: 14pt;
            }

            .department-name {
               max-width: 32pt;
            }
        }
    }

    .medicine-tag-western-content {
        .western-group-item {
            .western-group-name {
                max-height: 10pt;
                font-size: 7pt;
                line-height: 10pt;
            }

            .western-group-dosage,
            .western-group-spec {
                max-height: 8pt;
                font-size: 6pt;
                line-height: 8pt;
            }
        }

        .medicine-tag-item-group {
            margin-bottom: 0 !important;
        }

        .western-no-group-item {
            margin-bottom: 1pt;
            font-size: 8pt;
            line-height: 13pt;

            .item-label {
                display: none;
            }

            .item-usage {
                font-size: 10pt;
                line-height: 14pt;
            }
        }
    }
}

[data-size=page_50mm×30mm],
[data-size=page_60mm×30mm] {
    .medicine-tag-header {
        padding: 0 0 0 4pt;
    }

    .medicine-tag-header-group {
        padding-top: 0;
        padding-bottom: 0;
    }

    .medicine-tag-footer {
        padding: 0 4pt 0 4pt;
    }

    .medicine-tag-group-footer {
        padding: 0 0 0 4pt;
    }

    .medicine-tag-patient-info {
        font-size: 8pt;
        line-height: 12pt;
    }

    .medicine-tag-footer-info {
        font-size: 8pt;
        line-height: 12pt;
    }

    .medicine-tag-footer-time {
        font-size: 8pt;
        line-height: 12pt;
    }

    .western-group-usage-footer {
        font-size: 8pt;
        line-height: 9pt;
    }

    .medicine-tag-chinese-content {
        padding: 2pt 4pt 0 4pt !important;

        .medicine-tag-chinese-usage {
            margin-bottom: 1pt;
            font-size: 10pt;
            line-height: 13pt;

            .item-usage {
                font-size: 9pt;
                line-height: 13pt;
            }
        }
    }

    .medicine-tag-western-content {
        padding: 2pt 4pt 0 4pt !important;

        .western-group-item {
            .western-group-name {
                max-height: 9pt;
                font-size: 8pt;
                line-height: 9pt;
            }

            .western-group-dosage,
            .western-group-spec {
                max-height: 8pt;
                font-size: 7pt;
                line-height: 8pt;
            }
        }

        .medicine-tag-item-group {
            margin-bottom: 0 !important;
        }

        .western-no-group-item {
            margin-bottom: 1pt;
            font-size: 10pt;
            line-height: 13pt;

            .item-usage {
                font-size: 9pt;
                line-height: 13pt;
            }
        }
    }

    .medicine-tag-content-group {
        padding: 2pt 4pt 0 4pt !important;
    }
}

[data-size=page_70mm×50mm],
[data-size=page_50mm×70mm],
[data-size=page_80mm×50mm] {
    .medicine-tag-header {
        padding: 3pt 0 3pt 4pt;
    }

    .medicine-tag-patient-info {
        font-size: 12pt;
        line-height: 14pt;
    }

    .medicine-tag-footer-info {
        font-size: 10pt;
        line-height: 14pt;
    }

    .medicine-tag-footer-time {
        font-size: 10pt;
        line-height: 14pt;
    }

    .western-group-usage-footer {
        font-size: 10pt;
        line-height: 12pt;
    }

    .medicine-tag-chinese-content {
        .medicine-tag-chinese-usage {
            font-size: 12pt;
            line-height: 18pt;

            .item-usage {
                font-size: 13pt;
                line-height: 17pt;
            }
        }
    }

    .medicine-tag-western-content {
        .medicine-tag-item-group {
            &:not(:last-of-type) {
                margin-bottom: 2pt !important;
            }
        }

        .western-group-item {
            .western-group-name {
                max-height: 14pt;
                font-size: 12pt;
                line-height: 14pt;
            }

            .western-group-dosage,
            .western-group-spec {
                max-height: 13pt;
                font-size: 11pt;
                line-height: 13pt;
            }
        }

        .western-no-group-item {
            font-size: 12pt;
            line-height: 18pt;

            .item-usage {
                font-size: 13pt;
                line-height: 17pt;
            }
        }
    }

    .medicine-tag-content-group {
        padding: 3pt 8pt 0 4pt !important;
    }
}

[data-size=page_50mm×40mm] {
    .western-no-group-item {
        .item-usage {
            font-size: 10pt;
            line-height: 14pt;
        }
    }
}
</style>
