<!--exampleData
{
  address: "杭州西湖x2",
  bankAccount: "1212143412312124124123412",
  bankAgent: "工商银行",
  invoiceChecker: "Bubblex",
  invoiceFee: 185.8,
  invoiceOpener: "刘喜",
  invoiceRemark: "2021-09-15就诊",
  patientId: "ffffffff0000000019b298100f340000",
  patientName: "电子发票",
  payee: "刘喜",
  sendRemarkType: 0,
  status: 2,
  taxName: "杭州店",
  taxNum: "***************",
  telephone: "***********",
  type: 1,
  caseNumber: 'xx',
  IS_HOSPITAL: true,

}
-->

<template>
    <div>
        <div class="print-e-invoice-preview">
            <div class="patient">
                {{ printData.patientName }}
            </div>
            <div class="product-items">
                <div
                    v-for="(item, index) in chargeDetails"
                    :key="index"
                    class="item__content"
                >
                    <span class="name">{{ item.chargeName }}</span>
                    <span class="std">{{ item.amt }}</span>
                </div>
            </div>

            <span class="upper-price">{{ digitUppercase(printData.invoiceFee) }}</span>
            <span class="lower-price">{{ printData.invoiceFee }}</span>

            <div class="invoice-info">
                <p>{{ printData.taxNum }}</p>
                <p>{{ printData.address }} {{ printData.telephone }}</p>
                <p>{{ printData.bankAgent }}</p>
            </div>
            <div class="tax-name">
                {{ printData.taxName }}
            </div>

            <template v-if="isHospital">
                <block-box
                    :top="19"
                    :left="24"
                    :font="8"
                >
                    {{ printData.invoiceCode }}
                </block-box>
                <block-box
                    :top="19"
                    :left="134"
                    :font="8"
                >
                    {{ printData.invoiceNumber }}
                </block-box>

                <block-box
                    :top="68"
                    :left="17"
                    :font="8"
                >
                    业务流水号：{{ printData.busNo }}
                </block-box>
                <block-box
                    :top="68"
                    :left="63"
                    :font="8"
                >
                    病历号：{{ printData.caseNumber }}
                </block-box>
                <block-box
                    :top="68"
                    :left="94"
                    :font="8"
                >
                    住院号：{{ printData.hospitalNo }}
                </block-box>
                <block-box
                    :top="68"
                    :left="133"
                    :font="8"
                >
                    住院科别：{{ printData.departmentName }}
                </block-box>
                <block-box
                    :top="73"
                    :left="17"
                    :font="8"
                >
                    住院时间：{{ printData.inHospitalDate }} - {{ printData.outHospitalDate }}
                </block-box>
                <block-box
                    :top="73"
                    :left="63"
                    :font="8"
                >
                    预缴金额：{{ printData.cashPay }}
                </block-box>
                <block-box
                    :top="73"
                    :left="94"
                    :font="8"
                >
                    补缴金额：{{ printData.cashRecharge }}
                </block-box>
                <block-box
                    :top="73"
                    :left="133"
                    :font="8"
                >
                    退费金额：{{ printData.cashRefund }}
                </block-box>
                <block-box
                    :top="78"
                    :left="17"
                    :font="8"
                >
                    机构类型：{{ printData.medicalInstitution }}
                </block-box>
                <block-box
                    :top="78"
                    :left="63"
                    :font="8"
                >
                    医保类型：{{ printData.medicalCareType }}
                </block-box>
                <block-box
                    :top="78"
                    :left="94"
                    :font="8"
                >
                    医保编号：{{ printData.medicalInsuranceID }}
                </block-box>
                <block-box
                    v-if="printData.sex"
                    :top="78"
                    :left="133"
                    :font="8"
                >
                    性别：{{ printData.sex }}
                </block-box>
                <block-box
                    :top="83"
                    :left="17"
                    :font="8"
                >
                    统筹基金支付：{{ printData.fundPay }}
                </block-box>
                <block-box
                    :top="83"
                    :left="63"
                    :font="8"
                >
                    其他支付：{{ printData.otherfundPay }}
                </block-box>
                <block-box
                    :top="83"
                    :left="94"
                    :font="8"
                >
                    个人账户支付：{{ printData.accountPay }}
                </block-box>
                <block-box
                    :top="83"
                    :left="133"
                    :font="8"
                >
                    个人现金支付：{{ printData.selfCashPay }}
                </block-box>
                <block-box
                    :top="88"
                    :left="17"
                    :font="8"
                >
                    个人自负：{{ printData.selfPayAmt }}
                </block-box>
                <block-box
                    :top="88"
                    :left="63"
                    :font="8"
                >
                    个人自费：{{ printData.ownPay }}
                </block-box>
                <block-box
                    :top="88"
                    :left="94"
                    :font="8"
                >
                    交费日期：<template v-if="printData.chargeDate">
                        {{ printData.chargeDate | parseTime('y-m-d h:i:s', true) }}
                    </template>
                </block-box>
            </template>

            <!--            收款人-->
            <div class="cashier">
                {{ printData.payee }}
            </div>
            <!--            复核人-->
            <div class="review">
                {{ printData.invoiceOpener }}
            </div>
        </div>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import { digitUppercase, formatMoney } from './common/utils.js';
    import BlockBox from "./components/medical-bill/national-medical-bill/block-box.vue";
    import {parseTime} from "./common/utils.js";
    import {MM210_127_JIANGSU} from "../share/page-size";

    export default {
        name: "MedicalEInvoiceChongqing",
        DataHandler: CommonHandler,
        components: {
            BlockBox
        },
        businessKey: PrintBusinessKeyEnum.MEDICAL_E_INVOICE_CHONGQING,
        filters: {
            formatMoney,
            parseTime,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        pages: [
            {
                paper: PageSizeMap.MM210_127_JIANGSU,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            printData() {
                console.log(this.renderData.printData)
                return this.renderData.printData || null;
            },
            chargeDetails() {
                return this.printData && this.printData.chargeDetails;
            },
            isHospital() {
                return this.printData.IS_HOSPITAL;
            },
        },
        methods: {
            digitUppercase
        }
    }
</script>
<style lang="scss">
.abc-page_preview {
    background: url("/static/assets/print/e-invoice-chongqing.png");
    background-size: 210mm 127mm;
    color: #2a82e4;
}

.print-e-invoice-preview {
  position: relative;
  top: 0;
  bottom: 0;
  left: 0;
  width: 210mm;
  height: 127mm;
  font-size: 12px ;
  line-height: 14px;
  .patient {
    position: absolute;
    top: 28mm;
    left: 26mm;
  }

  .product-items {
    position: absolute;
    width: 160mm;
    height:28mm;
    max-height: 28mm;
    overflow: hidden;
    top: 39mm;
    left: 18mm;
    .item__content {
      width: 50%;
      max-width: 50%;
      min-width: 50%;
      display: inline-flex;
      height: 4.5mm;
      max-height: 4.5mm;
      overflow: hidden;
      line-height: 5mm;
      flex: 1;
      .name {
        width: 54mm;
      }
      .std {
        width: 12mm
      }

    }
  }
  .tax-name {
    position: absolute;
    top: 115mm;
    left: 36mm;
    width: 72mm;
  }

  .price {
    position: absolute;
    top: 65mm;
    left: 132mm;
  }

  .upper-price,
  .lower-price {
    position: absolute;
    top: 78.5mm;
  }

  .upper-price {
    left: 38mm;
  }

  .lower-price {
    left: 129mm;
  }

  .invoice-info {
    position: absolute;
    top: 76mm;
    left: 32mm;
  }

  .invoice-remark {
    position: absolute;
    top: 76mm;
    left: 112mm;
  }

  .cashier,
  .review,
  .open {
    position: absolute;
    top: 115mm;
  }

  .cashier {
    left: 168mm;
  }

  .review {
    left: 129mm;
  }
}
</style>
