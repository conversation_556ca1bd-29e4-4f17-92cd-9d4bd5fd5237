<template>
    <div>
        <template v-for="(prescriptionByPatient, prescriptionByPatientIndex) in prescriptionList">
            <!-- 西药 -->
            <template v-if="prescriptionByPatient.western && prescriptionByPatient.western.length">
                <template v-for="(westernPrescription, westernPrescriptionIndex) in prescriptionByPatient.western">
                    <!-- 头部 -->
                    <hospital-prescription-header
                        :key="`${prescriptionByPatientIndex}-${westernPrescriptionIndex}-WP-header`"
                        data-type="header"
                        :data-pendants-index="`${prescriptionByPatientIndex}-${westernPrescriptionIndex}-WP`"
                        :patient-info="prescriptionByPatient.patientInfo"
                        :diagnoses="prescriptionByPatient.diagnoses"
                        :clinic-info="clinicInfo"
                        :header-config="headerConfig"
                        :tag="westernPrescription.tag"
                        :type="1"
                        :shebao-payment="prescriptionByPatient.shebaoPayment"
                        :barcode-src="qrCode"
                        :print-date="westernPrescription.startTime"
                    ></hospital-prescription-header>
                    <!-- 内容 -->
                    <hospital-prescription-western
                        :key="`${prescriptionByPatientIndex}-${westernPrescriptionIndex}-WP-content`"
                        :western="westernPrescription"
                        :header-config="headerConfig"
                        :content-config="contentConfig"
                    ></hospital-prescription-western>
                    <next-is-blank></next-is-blank>
                    <!-- 底部 -->
                    <hospital-prescription-footer
                        :key="`${prescriptionByPatientIndex}-${westernPrescriptionIndex}-WP-footer`"
                        data-type="footer"
                        :data-pendants-index="`${prescriptionByPatientIndex}-${westernPrescriptionIndex}-WP`"
                        :footer-config="footerConfig"
                        :doctor-info="westernPrescription.doctorInfo"
                    ></hospital-prescription-footer>
                </template>
            </template>

            <!-- 输注 -->
            <template v-if="prescriptionByPatient.infusion && prescriptionByPatient.infusion.length">
                <template v-for="(infusionPrescription, infusionPrescriptionIndex) in prescriptionByPatient.infusion">
                    <!-- 头部 -->
                    <hospital-prescription-header
                        :key="`${prescriptionByPatientIndex}-${infusionPrescriptionIndex}-IP-header`"
                        data-type="header"
                        :data-pendants-index="`${prescriptionByPatientIndex}-${infusionPrescriptionIndex}-IP`"
                        :patient-info="prescriptionByPatient.patientInfo"
                        :diagnoses="prescriptionByPatient.diagnoses"
                        :clinic-info="clinicInfo"
                        :header-config="headerConfig"
                        :tag="infusionPrescription.tag"
                        :type="2"
                        :shebao-payment="prescriptionByPatient.shebaoPayment"
                        :barcode-src="qrCode"
                        :print-date="infusionPrescription.startTime"
                    ></hospital-prescription-header>
                    <!-- 内容 -->
                    <div
                        v-for="(goods, goodIndex) in getInfusionGroupHospitalItems(infusionPrescription)"
                        :key="`${prescriptionByPatientIndex}-${infusionPrescriptionIndex}-${goodIndex}-IP-content`"
                        data-type="mix-box"
                    >
                        <hospital-prescription-infusion
                            :key="`${prescriptionByPatientIndex}-${infusionPrescriptionIndex}-${goodIndex}-IP-item`"
                            show-days
                            :config="config"
                            :other-info="infusionPrescription.otherInfo"
                            :goods="goods"
                            :group-id="goodIndex + 1"
                        ></hospital-prescription-infusion>
                    </div>
                    <next-is-blank></next-is-blank>
                    <!-- 底部 -->
                    <hospital-prescription-footer
                        :key="`${prescriptionByPatientIndex}-${infusionPrescriptionIndex}-IP-footer`"
                        data-type="footer"
                        :data-pendants-index="`${prescriptionByPatientIndex}-${infusionPrescriptionIndex}-IP`"
                        :footer-config="footerConfig"
                        :doctor-info="infusionPrescription.doctorInfo"
                    ></hospital-prescription-footer>
                </template>
            </template>

            <!-- 中药 -->
            <template v-if="prescriptionByPatient.chinese && prescriptionByPatient.chinese.length">
                <template v-for="(chinesePrescription, chinesePrescriptionIndex) in prescriptionByPatient.chinese">
                    <!-- 头部 -->
                    <hospital-prescription-header
                        :key="`${prescriptionByPatientIndex}-${chinesePrescriptionIndex}-CP-header`"
                        data-type="header"
                        :data-pendants-index="`${prescriptionByPatientIndex}-${chinesePrescriptionIndex}-CP`"
                        :patient-info="prescriptionByPatient.patientInfo"
                        :diagnoses="prescriptionByPatient.diagnoses"
                        :clinic-info="clinicInfo"
                        :header-config="headerConfig"
                        :tag="chinesePrescription.tag"
                        :type="3"
                        :shebao-payment="prescriptionByPatient.shebaoPayment"
                        :barcode-src="qrCode"
                        :print-date="chinesePrescription.startTime"
                    ></hospital-prescription-header>
                    <!-- 内容 -->
                    <div
                        :key="`${prescriptionByPatientIndex}-${chinesePrescriptionIndex}-CP-pr`"
                        data-type="mix-box"
                    >
                        <!-- 中药详情 -->
                        <hospital-prescription-chinese
                            :config="config"
                            :goods="chinesePrescription.goods"
                            :chinese-other-info="chinesePrescription.otherInfo"
                        ></hospital-prescription-chinese>

                        <!--用法-->
                        <print-row
                            :key="`${prescriptionByPatientIndex}-${chinesePrescriptionIndex}-CP-use-way`"
                            data-type="group"
                            class="remark-row"
                        >
                            <print-col
                                data-type="item"
                                class="remark-col"
                                :span="24"
                            >
                                <span class="label">用法：</span>
                                <span v-html="getChineseFormUsage(chinesePrescription.otherInfo, chinesePrescription.goods, contentConfig).totalInfo"></span>
                            </print-col>
                        </print-row>

                        <print-row
                            v-if="getChineseFormUsage(chinesePrescription.otherInfo, chinesePrescription.goods, contentConfig).usageInfo"
                            :key="`${prescriptionByPatientIndex}-${chinesePrescriptionIndex}-CP-use-box`"
                            data-type="group"
                            class="remark-row"
                        >
                            <print-col
                                data-type="item"
                                class="remark-col"
                                :span="24"
                            >
                                <span v-html="getChineseFormUsage(chinesePrescription.otherInfo, chinesePrescription.goods, contentConfig).usageInfo"></span>
                            </print-col>
                        </print-row>

                        <!-- 加工-->
                        <print-row
                            v-if="contentConfig.processInfo && (getProcessInfoStr(chinesePrescription.otherInfo))"
                            :key="`${prescriptionByPatientIndex}-${chinesePrescriptionIndex}-CP-process`"
                            data-type="group"
                            class="remark-row first-remark-row"
                        >
                            <print-col
                                data-type="item"
                                class="remark-col"
                                :span="24"
                            >
                                <span class="label">加工：</span>
                                {{ getProcessInfoStr(chinesePrescription.otherInfo) }}
                                <template v-if="chinesePrescription.otherInfo.processInfo && chinesePrescription.otherInfo.processInfo.remark">
                                    ，{{ chinesePrescription.otherInfo.processInfo.remark }}
                                </template>
                                <template v-else-if="chinesePrescription.otherInfo.processRemark">
                                    ，{{ chinesePrescription.otherInfo.processRemark }}
                                </template>
                            </print-col>
                        </print-row>
                    </div>

                    <next-is-blank></next-is-blank>
                    <!-- 底部 -->
                    <hospital-prescription-footer
                        :key="`${prescriptionByPatientIndex}-${chinesePrescriptionIndex}-CP-footer`"
                        data-type="footer"
                        :data-pendants-index="`${prescriptionByPatientIndex}-${chinesePrescriptionIndex}-CP`"
                        :footer-config="footerConfig"
                        :doctor-info="chinesePrescription.doctorInfo"
                    ></hospital-prescription-footer>
                </template>
            </template>
        </template>
    </div>
</template>

<script>
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import HospitalPrescriptionHandler from "./data-handler/hospital-prescription-handler.js";
    import HospitalPrescriptionHeader from "./components/hospital-prescrition/header.vue";
    import HospitalPrescriptionWestern from "./components/hospital-prescrition/western.vue";
    import HospitalPrescriptionFooter from "./components/hospital-prescrition/footer.vue";
    import HospitalPrescriptionInfusion from "./components/hospital-prescrition/infusion.vue";
    import HospitalPrescriptionChinese from "./components/hospital-prescrition/chinese.vue";
    import NextIsBlank from './components/next-is-blank/index.vue';
    import PrintCol from './components/layout/print-col.vue';
    import PrintRow from './components/layout/print-row.vue';
    import {doseTotal, getInfusionGroupHospitalItems} from "./common/medical-transformat";

    const ResetData = {
        // 处方前记
        'header': {
            'title': '', // 抬头名称 字符串 必填
            'subtitle': '', // 副抬头名称 字符串 非必填
            'logo': 0, // logo 可选值：0 1
            'qrcode': 0, // 二维码 可选值：0 1
            'mobile': 0, // 手机号 可选值：0 1
            'idCard': 0, // 身份证 可选值：0 1
            'socialCode': 0, // 医保号 可选值：0 1
            'computerCode': 0, // 电脑号/个人编号 可选值：0 1
            'address': 0, // 住址 可选值：0 1
            'mobileType': 1, // 手机号类型 可选值：0 1 2  0:不展示 1:隐藏中间4位 2:完整展示
        },
        // 处方正文
        'content': {
            'medicineTradeName': 1, // 中西成药 商品名 可选值：0 1
            'westernMedicineSpec': 1, // 中西成药 规格 可选值：0 1
            'chineseMedicineTotalCount': 0, // 中药 总重 可选值：0 1
            'processInfo': 0, // 加工信息 可选值：0 1
            'standardKindCount': 0, // 中西成药超过5种自动分页 可选值：0 1
            'medicalLatin': 0, // 中西成药用法使用拉丁文 可选值：0 1
            'westernMedicineDays': 0, // 中西成药天数, 可选值0 1, 默认0
            'westernPosition': 0, // 中西成药柜号, 可选值0 1, 默认0
            'westernManufacturer': 0, // 中西成药厂家, 可选值0 1, 默认0
            'chineseSingleDoseEachItemCount': 0, // 中药单剂克数, 可选值0 1, 默认0
            'chineseTotalDoseEachItemCount': 0, // 中药总剂克数, 可选值0 1, 默认0
            'chinesePosition': 0, // 中药柜号, 可选值0 1, 默认0
        },
        // 处方后记
        'footer': {
            'billSign': 0, // 大额处方患者签字, 可选值：0 1
            'check': 1, // 审核 可选值：0 1
            'assinger': 1, // 调配 可选值：0 1
            'dispense': 1, // 核发 可选值：0 1
            'printDate': 0, // 打印时间 可选值：0 1
            'remark': '除主诊医师特别注明外，处方仅当日有效。按卫生部规定，药房药品一经发出，不得退换。', // 备注 字符串
        },
    };

    const specialUsages = ['制膏', '制丸', '打粉']

    export default {
        name: 'HospitalPrescription',
        DataHandler: HospitalPrescriptionHandler,
        businessKey: PrintBusinessKeyEnum.HOSPITAL_PRESCRIPTION,
        pages: [
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分',
            },
        ],
        components: {
            HospitalPrescriptionChinese,
            HospitalPrescriptionInfusion,
            HospitalPrescriptionFooter,
            HospitalPrescriptionWestern,
            HospitalPrescriptionHeader,
            NextIsBlank,
            PrintCol,
            PrintRow,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            config() {
                if (this.renderData.config && this.renderData.config.hospitalMedicalDocuments && this.renderData.config.hospitalMedicalDocuments.prescription) {
                    return this.renderData.config.hospitalMedicalDocuments.prescription;
                }
                return ResetData;
            },
            headerConfig() {
                return this.config.header || {};
            },
            contentConfig() {
                return this.config.content || {};
            },
            footerConfig() {
                return this.config.footer || {};
            },
            prescriptionList() {
                return this.printData.prescriptionList || [];
            },
            clinicInfo() {
                return this.printData.clinicInfo || {};
            },
            qrCode() {
                return this.printData.qrCode || '';
            },
        },
        watch: {
            printData: {
                handler(v) {
                    console.log('%c printData\n', 'background: green; padding: 0 5px', JSON.parse(JSON.stringify(v)));
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            getInfusionGroupHospitalItems,
            // 获取中药处方用法
            getChineseFormUsage(otherInfo, goods, prContentConfig = {}) {
                let usageStr = '';
                let usageArray = [];
                if(otherInfo.usage) {
                    usageArray.push(otherInfo.usage);
                }
                if(otherInfo.dailyDosage) {
                    usageArray.push(otherInfo.dailyDosage);
                }
                if(otherInfo.freq) {
                    usageArray.push(otherInfo.freq)
                }
                if(otherInfo.usageLevel) {
                    usageArray.push(otherInfo.usageLevel)
                }
                if(otherInfo.usageDays && specialUsages.includes(otherInfo.usage)) {
                    usageArray.push(otherInfo.usageDays);
                }
                if(otherInfo.requirement) {
                    usageArray.push(otherInfo.requirement);
                }
                if(otherInfo.remark) {
                    usageArray.push(otherInfo.remark);
                }
                usageStr = usageArray.join('，');

                let _str = '';
                const { chineseMedicineTotalCount } = prContentConfig;
                _str += `共 <span class="bold-text">${otherInfo.doseCount || otherInfo.dosageCount || ''}</span> ${otherInfo.dosageUnit || '剂'}`;
                if(chineseMedicineTotalCount) {
                    _str += `，${doseTotal(goods).kind} 味`;
                    if (Number(doseTotal(goods).count)) {
                        _str += `，单剂 ${
                            doseTotal(goods).count
                        } g，总重 ${
                            (doseTotal(goods).count * (otherInfo.doseCount || otherInfo.dosageCount)).toFixed(2)
                        } g`
                    } else {
                        _str += `，${usageStr}`;
                        usageStr = '';
                    }
                } else {
                    _str += `，${usageStr}`;
                    usageStr = '';
                }

                return {
                    totalInfo: _str,
                    usageInfo: usageStr ,
                }
            },
            getProcessInfoStr(otherInfo) {
                const { processInfo } = otherInfo;

                // 库房
                if (!processInfo) {
                    const { processUsage, usageType, processBagUnitCountDecimal, totalProcessCount } = otherInfo;

                    if (!processUsage) return '';
                    const _arr = [];
                    _arr.push(processUsage);
                    // 加工方式为煎药
                    if (usageType === 1) {
                        if (processBagUnitCountDecimal) {
                            _arr.push(`1剂煎 ${processBagUnitCountDecimal} 袋`);
                        }
                        if (totalProcessCount) {
                            _arr.push(`共 ${totalProcessCount} 袋`);
                        }
                    }

                    return _arr.join('，');
                }

                // 医嘱
                const { displayName, usageType, perDosageBagCount, totalBagCount } = processInfo;

                if (!displayName) return '';
                const _arr = [];
                _arr.push(displayName);
                // 加工方式为煎药
                if (usageType === 1) {
                    if (perDosageBagCount) {
                        _arr.push(`1剂煎 ${perDosageBagCount} 袋`);
                    }
                    if (totalBagCount) {
                        _arr.push(`共 ${totalBagCount} 袋`);
                    }
                }

                return _arr.join('，');
            },
        }
    };
</script>

<style lang="scss">
@import "./components/layout/_print-layout";
@import "./style/reset.scss";

.remark-col {
    position: relative;
    padding-top: 6pt;
    padding-left: 30pt;
    font-size: 10pt;
    font-weight: 300;
    line-height: 12pt;

    .label {
        position: absolute;
        top: 6pt;
        left: 0;
        width: 30pt;
    }
}

.remark-row {
    margin-bottom: 6pt;
}

.bold-text {
    font-weight: bold;
}

.label {
    font-weight: normal;
}

.first-remark-row {
    .remark-col {
        padding-top: 6pt;
    }

    &.external-usage-row {
        .remark-col {
            padding-top: 0;

            .label {
                top: 0;
            }
        }
    }
}

</style>
