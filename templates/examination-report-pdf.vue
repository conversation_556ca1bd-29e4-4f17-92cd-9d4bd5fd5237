<template>
    <div class="examination-report-pdf-wrapper">
        <template v-for="(pdfPage, idx) in pdfPageList">
            <abc-print-image
                :key="idx"
                :value="pdfPage"
            ></abc-print-image>

            <div
                v-if="idx !== pdfPageList.length - 1"
                :key="idx"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import PrintCommonHandler from "./data-handler/examination-report-handler";
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import AbcPrintImage from "./components/layout/abc-print-image.vue";

    export default {
        name: "ExaminationReportPdf",
        components: {AbcPrintImage},

        DataHandler: PrintCommonHandler,

        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },

        businessKey: PrintBusinessKeyEnum.EXAMINATION_REPORT_PDF,

        pages: [
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],

        computed: {
            printData() {
                return this.renderData.printData || {};
            },

            pdfPageList() {
                return this.printData.pdfPageList || [];
            }
        },
    }
</script>