<!--exampleData
{
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: null,
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
    },
    'chargeForms': [
        {
            'id': 'ffffffff00000000168591800dc0e000',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca002',
                    'name': '诊费',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 0.1,
                    'discountedPrice': 0.01,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '诊费',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 1,
            'printFormType': 1,
            'processUsageInfo': null,
            'totalPrice': 0.1,
        },
        {
            'id': 'ffffffff00000000168591800dc0e006',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca009',
                    'name': '推拿',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 5,
                    'discountedPrice': 0.5,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '推拿',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
                {
                    'id': 'ffffffff00000000167b12480dbca00a',
                    'name': '肩周炎针灸治疗',
                    'unit': '单颌',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 12,
                    'discountedPrice': 6,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '单颌',
                    'socialName': '肩周炎针灸治疗',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 3,
            'printFormType': 3,
            'processUsageInfo': null,
            'totalPrice': 17,
        },
        {
            'id': 'ffffffff00000000168591800dc0e007',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca00b',
                    'name': 'HPV基因全套',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 24,
                    'discountedPrice': 12,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': 'HPV基因全套',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
                {
                    'id': 'ffffffff00000000167b12480dbca00c',
                    'name': '甲胎蛋白（AFP）',
                    'unit': '真大杀四方',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 22,
                    'discountedPrice': 11,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '真大杀四方',
                    'socialName': '甲胎蛋白（AFP）',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 2,
            'printFormType': 2,
            'processUsageInfo': null,
            'totalPrice': 66,
        },
        {
            'id': 'ffffffff00000000168591800dc0e008',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca00f',
                    'name': '热敏灸盒',
                    'unit': '支',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 0.5,
                    'discountedPrice': 0.2,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '',
                    'displaySpec': 'KC0.55号*号/支',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '支',
                    'socialName': '一次性使用静脉输液针',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '100004783',
                            'batchNo': null,
                            'expiryDate': '',
                            'manufacturer': '江西洪达医疗器械',
                            'manufacturerFull': '江西洪达医疗器械集团有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca010',
                    'name': '一次性针灸针',
                    'unit': '盒',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 269,
                    'discountedPrice': 107.58,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '',
                    'displaySpec': 'C50片*50片/盒',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '盒',
                    'socialName': '罗康全（卓越金锐/血糖试纸）',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '100004778',
                            'batchNo': null,
                            'expiryDate': '',
                            'manufacturer': '德国罗氏诊断',
                            'manufacturerFull': '德国罗氏诊断有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                },
            ],
            'sourceFormType': 9,
            'printFormType': 9,
            'processUsageInfo': null,
            'totalPrice': 274,
        },
        {
            'id': 'ffffffff00000000168591800dc0e008',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca00f',
                    'name': '输氧费',
                    'unit': '支',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 0.5,
                    'discountedPrice': 0.2,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '',
                    'displaySpec': 'KC0.55号*号/支',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '支',
                    'socialName': '输氧费',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca00f',
                    'name': '注射费',
                    'unit': '支',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 0.5,
                    'discountedPrice': 0.2,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '',
                    'displaySpec': 'KC0.55号*号/支',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '支',
                    'socialName': '注射费',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [],
                },
            ],
            'sourceFormType': 999,
            'printFormType': 999,
            'processUsageInfo': null,
            'totalPrice': 99,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '埋线减脂套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '埋线',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '卡介菌多糖核酸注射液(斯奇康)',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '减脂',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '尿常规',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '有检查检验',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '有检查检验',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
        },
        {
            'id': 'ffffffff00000000167b12480dbca022',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca023',
                    'name': '四环素软膏（三益）',
                    'unit': '片',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 0.1,
                    'discountedPrice': 0.01,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '',
                    'displaySpec': '0g*123片/盒',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '片',
                    'socialName': '维C银翘片(巍)',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '51723203',
                            'batchNo': '2021070120210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '大理白族中药',
                            'manufacturerFull': '大理白族自治州中药制药有限公司',
                            'supplierName': '超级玛丽亚',
                        },
                    ],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca024',
                    'name': '法莫替丁片（迪诺洛克）',
                    'unit': '片',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 9.52,
                    'discountedPrice': 0.96,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '',
                    'displaySpec': '1*21片/盒',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '片',
                    'socialName': 'robins测试发药1',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '100009993',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '云南白药',
                            'manufacturerFull': '李龙彬有限公司',
                            'supplierName': '长长长长长长长长长长长长长长长长',
                        },
                    ],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca025',
                    'name': '胸腺肽肠溶片（奇莫欣）',
                    'unit': '片',
                    'count': 3,
                    'unitCount': 3,
                    'doseCount': 1,
                    'totalPrice': 0.03,
                    'discountedPrice': 0.03,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '',
                    'displaySpec': '375mg*11片/盒',
                    'socialCode': 'YP10030577',
                    'hisCode': '**********',
                    'socialUnit': '片',
                    'socialName': '阿莫西林克拉维酸钾片西林克拉维酸钾片西林克拉维酸钾片(中诺艾林)',
                    'medicalFeeGrade': 1,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '100000138',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-31',
                            'manufacturer': '石药集团中诺药业（石家庄）有限公司',
                            'manufacturerFull': '石药集团中诺药业（石家庄）有限公司',
                            'supplierName': '盘点入库2',
                        },
                    ],
                }, {
                    'id': 'ffffffff00000000167b12480dbca027',
                    'name': '复方丹参片（罗浮山）',
                    'unit': '片',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 0.2,
                    'discountedPrice': 0.02,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '',
                    'displaySpec': '0g*123片/盒',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '片',
                    'socialName': '维C银翘片(巍)',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '51723203',
                            'batchNo': '20210701',
                            'expiryDate': '',
                            'manufacturer': '大理白族中药',
                            'manufacturerFull': '大理白族自治州中药制药有限公司',
                            'supplierName': '超级玛丽亚',
                        },
                    ],
                },
            ],
            'sourceFormType': 4,
            'printFormType': 4,
            'processUsageInfo': null,
            'totalPrice': 9.65,
        },
        {
            'id': 'ffffffff00000000167b12480dbca01c',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca01d',
                    'name': '白花蛇舌草颗粒1/15（4-9）',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 0.22,
                    'discountedPrice': 0.03,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '3-99',
                    'displaySpec': '05：10',
                    'socialCode': 'YP10785716',
                    'hisCode': '**********',
                    'socialUnit': 'g',
                    'socialName': '白花蛇舌草颗粒1/15（4-9）',
                    'medicalFeeGrade': 1,
                    specialRequirement: '先煎',
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '159815',
                            'batchNo': '180603',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸药业有限公司',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '四川省恒世康医药有限责任公司',
                        },
                    ],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca01e',
                    'name': '盐知母',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 0.5,
                    'discountedPrice': 0.05,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': 'B-9',
                    'displaySpec': '',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': 'g',
                    'socialName': '盐知母',
                    specialRequirement: '先煎',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '175568',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '四川省恒世康医药有限责任公司',
                        },
                    ],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca01f',
                    'name': '盐黄柏',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 6,
                    'discountedPrice': 0.6,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '19-8',
                    'displaySpec': '规格',
                    'socialCode': 'YP10788727',
                    'hisCode': '**********',
                    'socialUnit': 'g',
                    specialRequirement: '先煎',
                    'socialName': '盐黄柏',
                    'medicalFeeGrade': 1,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '100000740',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '江西天之海',
                            'manufacturerFull': '江西天之海药业股份有限公司',
                            'supplierName': '123123123131313',
                        },
                    ],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca020',
                    'name': '白花蛇舌',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 4,
                    'discountedPrice': 0.4,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '10-2',
                    'displaySpec': '',
                    'socialCode': 'YP10062583',
                    'hisCode': '700821738',
                    'socialUnit': 'g',
                    'socialName': '白花蛇舌',
                    specialRequirement: '包煎',
                    'medicalFeeGrade': 1,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '177217',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸',
                            'manufacturerFull': '',
                            'supplierName': '共因伤2',
                        },
                    ],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca021',
                    'name': '山药YG',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 0.08,
                    'discountedPrice': 0.01,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '10-6',
                    'displaySpec': '',
                    'socialCode': 'YP10789472',
                    'hisCode': '**********',
                    'socialUnit': 'g',
                    'socialName': '山药YG',
                    'medicalFeeGrade': 1,
                    'ownExpenseRatio': null,
                    specialRequirement: '先煎',
                    'goodsStockInfos': [
                        {
                            'stockId': '140643',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸药业有限公司',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                },
                {
                    'id': 'ffffffff0000000016712480dbca021',
                    'name': '牡丹皮YG',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 0.08,
                    'discountedPrice': 0.01,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '10-7',
                    'displaySpec': '',
                    'socialCode': 'YP10789472',
                    'hisCode': '**********',
                    'socialUnit': 'g',
                    'socialName': '牡丹皮YG',
                    specialRequirement: '先煎',
                    'medicalFeeGrade': 1,
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '140643',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸药业有限公司',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                },
                {
                    'id': 'ffffffff0000000016712480dbca021',
                    'name': '白花蛇舌草颗粒1/15（4-9）',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 0.08,
                    'discountedPrice': 0.01,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '10-8',
                    'displaySpec': '',
                    'socialCode': 'YP10789472',
                    'hisCode': '**********',
                    'socialUnit': 'g',
                    'socialName': '白花蛇舌草颗粒1/15（4-9）',
                    'medicalFeeGrade': 1,
                    specialRequirement: '先煎',
                    'ownExpenseRatio': null,
                    'goodsStockInfos': [
                        {
                            'stockId': '140643',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸药业有限公司',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                },
            ],
            'sourceFormType': 6,
            'printFormType': 6,
            'specification': '中药饮片',
            'doseCount': 1,
            'dailyDosage': '1日1剂',
            'usage': '煎服',
            'freq': '1日3次',
            'usageLevel': '每次150ml',
            'processUsageInfo': null,
            'totalPrice': 10.8,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    patientOrderNo: '零售开单',
    memberCardBalance: null,
    memberCardMobile: '',
    memberCardBeginningBalance: '', // 会员卡原有余额
    healthCardBeginningBalance: '567.68', // 社保卡原有余额
    healthCardOwnerRelationToPatient: '父女', // 持卡人关系
    healthCardBalance: '0.00', // 社保卡余额
    healthCardNo: '01000520', // 社保卡卡号
    healthCardOwner: '任我行', // 持卡人姓名"

    'subTotals': {
        'registrationFee': 0.1000,
        'westernMedicineFee': 66.3500,
        'chineseMedicineFee': 10.8000,
        'examinationFee': 57.0000,
        'treatmentFee': 2291.0000,
        'materialFee': 274.0000,
        'onlineConsultationFee': 0,
        'expressDeliveryFee': 0,
        'decoctionFee': 0,
        'otherFee': 66.0000,
    },
    medicalBill: {
        registrationFee: 0,
        westernMedicineFee: 1.9,
        chineseMedicineFee: 0,
        chineseComposeMedicineFee: 0,
        examinationFee: 0,
        treatmentFee: 0,
        materialFee: 0,
        otherFee: 0,
    },
    healthCardPaymentFee: 0.0,
    personalPaymentFee: 1.99,

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: '父女', // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        // region: 'hangzhou',
        extraInfo: {
            // curYearBalance: 8, // 当年账户余额
            // allYearBalance: 10, // 历年账户余额
            // curYearAccountPaymentFee: 7, // 本年账户支付
            // allYearAccountPaymentFee: 9, // 历年账户支付
            // cashPaymentFee: 0, // 医保现金支付
            // selfConceitFee: 1, // 自负金额
            // allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            // personalHandledAmount: 3, // 自理金额
            // allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            // personalPaymentAmount: 5, // 自费金额
            // allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            // curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
        },
    },
};
-->

<template>
    <div>
        <cashier-template
            :config="config"
            :view-distribute-print-config="viewDistributePrintConfig"
            is-refund
            :print-data="printData"
            :is-optimization="isOptimization"
        ></cashier-template>
    </div>
</template>

<script>
    import CashierTemplate from './components/cashier/index.vue'

    import { formatMoney, parseTime} from './common/utils.js';
    import PrintCommonDataHandler from "./data-handler/common-handler.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";

    // 费用定义
    // totalFee 加上议价加价  总计费用
    // discountFee 优惠  折扣
    // receivealbeFee 应收
    // netIncomeFee 实收
    export default {
        DataHandler: PrintCommonDataHandler,
        pageConfig: {
            name: 'Cashier',
            size: 'MM80',
        },
        components: {
            CashierTemplate
        },
        businessKey: PrintBusinessKeyEnum.REFUND_CASHIER,
        pages: [
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM58,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80_100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_140,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM200_1397,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            }, {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            }, {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        filters: {
            formatMoney,
            parseTime
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
            options: {
                type: Object,
                default() {
                    return {}
                }
            },
            viewDistributePrintConfig: {
                type: Object,
                default() {
                    return {}
                }
            }
        },
        computed: {
            printData() {
                return this.renderData.printData
            },
            config() {
                if(this.renderData.config && this.renderData.config.cashier) {
                    return this.renderData.config.cashier;
                }
                return {};
            },
            // 58mm 小票需要优化排版
            isOptimization() {
                return this.options.page?.size === '热敏小票（58mm）';
            },
        },
    };
</script>

