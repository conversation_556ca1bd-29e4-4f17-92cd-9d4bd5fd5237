<template>
    <div class="content-item overflow-hidden-item">
        <print-row
            v-for="formItem in form.dispensingFormItems"
            :key="formItem.id"
            class="item-row row"
        >
            <print-col
                :span="20"
                overflow
                class="text-info"
            >
                {{ formItem.name }}
                <span v-if="westernMedicine.spec">
                    {{ formItem.formatSpec }}
                </span>
            </print-col>
            <print-col
                v-if="formItem.sourceItemType === 1"
                :span="4"
                class="text-right text-info"
                style="text-align: right;"
            >
                自备
            </print-col>
            <print-col
                v-else
                :span="4"
                class="text-right text-info"
            >
                {{ formItem.count }}{{ formItem.unit }}
            </print-col>
            <print-row>
                <print-col
                    v-if="westernMedicine.position && formItem.position"
                    :span="westernMedicine.usage ? 8 : 24"
                    class="small-font text-info"
                    style="min-height: 0;"
                >
                    {{ formItem.position }}
                </print-col>
                <print-col
                    v-if="westernMedicine.usage"
                    :span="westernMedicine.position && formItem.position ? 16 : 24"
                    class="small-font text-info"
                    style="min-height: 0;"
                >
                    <template v-if="formItem.usageInfo && formItem.usageInfo.dosage">
                        Sig：{{ form.sourceFormType === 16 ? '共' : '每次' }}{{ formItem.usageInfo.dosage }}{{ formItem.usageInfo.dosageUnit || '次' }}
                    </template>
                    {{ freqFormat(formItem.usageInfo && formItem.usageInfo.freq, 1) }}
                    {{ usageFormat(formItem.usageInfo && formItem.usageInfo.usage, 1) }}
                    {{ formItem.usageInfo && formItem.usageInfo.specialRequirement }}
                </print-col>
            </print-row>

            <!-- 厂家 -->
            <print-row> 
                <print-col
                    v-if="westernMedicine.manufacturer"
                    :span="24"
                    class="small-font text-info"
                    overflow
                >
                    厂家：{{ formItem.manufacturer }}
                </print-col>
            </print-row>
            <!-- 批号 -->
            <print-row>
                <print-col
                    v-if="westernMedicine.batchNo"
                    :span="24"
                    class="small-font text-info"
                    overflow
                >
                    批号：{{ formItem.batchNo }}
                </print-col>
            </print-row>
        </print-row>
    </div>
</template>

<script>
    import PrintRow from "../layout/print-row.vue";
    import PrintCol from "../layout/print-col.vue";

    import { freqFormat, usageFormat } from "../../common/medical-transformat.js";

    export default {
        name: "WesternForm",
        components: {
            PrintRow,
            PrintCol,
        },
        props: {
            form: {
                type: Object,
            },
            westernMedicine: {
                type: Object,
                default: ()=> {
                    return {
                        spec: 0,
                        usage: 0,
                        position: 0,
                        manufacturer: 0,
                    }
                },
            },
        },
        methods: {
            freqFormat,
            usageFormat,
        },
    }
</script>

<style scoped>

</style>