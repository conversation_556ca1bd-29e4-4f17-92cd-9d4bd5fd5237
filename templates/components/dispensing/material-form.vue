<template>
    <div class="content-item overflow-hidden-item">
        <print-row
            v-for="formItem in form.dispensingFormItems"
            :key="formItem.id"
            class="item-row row"
        >
            <print-col
                :span="(() => {
                    if (!materialGoods) return 20;
                    if (materialGoods.spec) return 20;
                    if (materialGoods.position && materialGoods.batchNo) return 10;
                    if (materialGoods.position || materialGoods.batchNo) return 15;
                    return 20;
                })()"
                class="text-info"
            >
                {{ formItem.name }}
                <span v-if="materialGoods.spec">
                    {{ formItem.formatSpec }}
                </span>
            </print-col>
            <print-col
                v-if="materialGoods.position && !materialGoods.spec"
                :span="5"
                class="text-info"
                :customStyle="{ paddingLeft: '7pt', fontSize: '8pt' }"
            >
                {{ formItem.position }}
            </print-col>
            <print-col
                v-if="materialGoods.batchNo && !materialGoods.spec"
                :span="5"
                class="text-info"
                :customStyle="{ paddingLeft: '7pt', fontSize: '8pt' }"
            >
                {{ formItem.batchNo }}
            </print-col>
            <print-col
                :span="4"
                class="text-right text-info"
            >
                {{ formItem.count }}{{ formItem.unit }}
            </print-col>
            <print-col
                v-if="materialGoods.spec && materialGoods.position"
                :span="10"
                class="text-info"
                :customStyle="{ fontSize: '8pt' }"
            >
                {{ formItem.position }}
            </print-col>
            <print-col
                v-if="materialGoods.spec && materialGoods.batchNo"
                :span="5"
                class="text-info"
                :customStyle="{ paddingLeft: materialGoods.position ? '7pt' : '0', fontSize: '8pt' }"
            >
                {{ formItem.batchNo }}
            </print-col>
            <print-col
                v-if="materialGoods.manufacturer"
                :span="20"
                class="text-info"
            >
                {{ formItem.manufacturer }}
            </print-col>
        </print-row>
    </div>
</template>

<script>
    import PrintRow from "../layout/print-row.vue";
    import PrintCol from "../layout/print-col.vue";

    import {freqFormat, usageFormat} from "../../common/medical-transformat.js";

    export default {
        name: "MaterialForm",
        components: {
            PrintRow,
            PrintCol,
        },
        props: {
            form: {
                type: Object
            },
            materialGoods: {
                type: Object,
                default: ()=> {
                    return {
                        spec: 0,
                        batchNo: 0,
                        position: 0,
                        manufacturer: 0,
                    }
                }
            }
        },
        methods: {
            freqFormat,
            usageFormat,
        }
    }
</script>