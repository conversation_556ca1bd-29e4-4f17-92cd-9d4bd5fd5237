<template>
    <print-row class="dispensing-chinese-form-items">
        <print-col
            v-for="(formItem, index) in formItems"
            :key="index"
            :span="6"
            class="chinese-item"
        >
            <div
                class="position-wrapper"
                overflow
                :class="{ 'has-position': chineseMedicine.position && formItem.position }"
            >
                <span
                    v-if="chineseMedicine.position && formItem.position"
                    class="item-position"
                    overflow
                >
                    {{ formItem.position }}
                </span>
                <span
                    v-if="formItem.specialRequirement && chineseMedicine.specialRequirement"
                    class="special"
                    overflow
                >
                    {{ formItem.specialRequirement === "【自备】" ? "" : formItem.specialRequirement }}
                </span>
            </div>
            <div class="goods">
                <span
                    class="name"
                    overflow
                >{{ formItem.name }}</span>
                <span
                    v-if="formItem.sourceItemType === 1"
                    style="margin-left: 2pt;"
                    class="count"
                >
                    自备
                </span>
                <span
                    v-if="formItem.sourceItemType !== 1"
                    style="margin-left: 2pt;"
                    class="count"
                >
                    {{ formItem.count }}{{ formItem.unit }}
                </span>
            </div>
        </print-col>
    </print-row>
</template>

<script>
    import PrintRow from "../layout/print-row.vue";
    import PrintCol from "../layout/print-col.vue";
    export default {
        name: 'ChineseItems',
        components: {
            PrintCol,
            PrintRow,
        },
        props: {
            formItems: {
                type: Array,
            },
            config: {
                type: Object,
            },
        },
        computed: {
            chineseMedicine() {
                return this.config && this.config.chineseMedicine || {};
            },
        },
    };
</script>
<style lang="scss">
.dispensing-chinese-form-items {
    padding-top: 6pt;
    padding-bottom: 2pt;
    font-size: 0;

    .chinese-item {
        position: relative;
        box-sizing: border-box;
        height: 31pt;
        padding: 10pt 4pt 8pt 4pt;
        overflow: hidden;
        font-size: 10pt;
        line-height: 14pt;

        .goods {
            font-size: 0;

            .name,
            .count {
                display: inline-block;
                *display: inline;
                font-size: 11pt;
                line-height: 14pt;
                vertical-align: top;
                *zoom: 1;
            }

            .name {
                *width: 68%;
                max-width: 70%;
                font-weight: normal;
                overflow: hidden;
                word-break: keep-all;
                white-space: nowrap;
            }

            .count {
                min-width: 12pt;
                max-width: 40pt;
            }
        }

        &.long-chinese-item {
            .goods .name {
                font-size: 9pt;
            }
        }

        .position-wrapper {
            position: absolute;
            top: -1pt;
            max-width: 100%;
            padding-left: 20pt;
            overflow: hidden;
            font-size: 9pt;
            word-break: keep-all;
            white-space: nowrap;

            &.has-position {
                padding-left: 0;
            }

            .special {
                margin-left: 2pt;
                vertical-align: middle;
                max-width: 46pt;
                overflow: hidden;
                word-break: keep-all;
                white-space: nowrap;
            }

            .item-position{
                display: inline-block;
                max-width: 64pt;
                vertical-align: middle;
                overflow: hidden;
                word-break: keep-all;
                white-space: nowrap;
            }
        }
    }
}

</style>