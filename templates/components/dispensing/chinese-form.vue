<template>
    <div>
        <div class="content-item">
            <print-row
                v-for="(row, index) in splitForm(form)"
                class="overflow-hidden-item"
                style="margin-bottom: 4pt"
            >
                <print-col
                    v-if="row[0]"
                    :key="`dispensing-chinese-form-name-${index}`"
                    :span="12"
                    style="padding-right: 4%;"
                >
                    <print-row :gutter="2">
                        <print-col
                            :span="14"
                            class="text-info"
                            overflow
                        >
                            {{ row[0].name }}
                        </print-col>
                      
                        <print-col
                            v-if="row[0].sourceItemType === 1"
                            :span="8"
                            class="text-right text-info"
                        >
                            自备
                        </print-col>
                        <print-col
                            v-else
                            :span="8"
                            class="text-right text-info"
                        >
                            {{ row[0].count }}{{ row[0].unit }}
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            v-if="chineseMedicine.position && row[0].position"
                            class="text-info"
                            style="line-height: 11pt"
                            :span="12"
                            overflow
                        >
                            {{ row[0].position }}
                        </print-col>
                        <print-col
                            overflow
                            :span="chineseMedicine.position && row[0].position ? 12 : 24"
                            style="text-align: right;line-height: 11pt"
                        >
                            <template v-if="row[0].specialRequirement && chineseMedicine.specialRequirement">
                                [{{ row[0].specialRequirement }}]
                            </template>
                        </print-col>
                    </print-row>
                </print-col>
                <print-col
                    v-if="row[1]"
                    :key="`dispensing-chinese-form-item-${index}`"
                    :span="12"
                    style="padding-left: 4%;"
                >
                    <print-row :gutter="2">
                        <print-col
                            :span="16"
                            class="text-info"
                            overflow
                        >
                            {{ row[1].name }}
                        </print-col>
                        <print-col
                            v-if="row[1].sourceItemType === 1"
                            :span="8"
                            class="text-right text-info"
                        >
                            自备
                        </print-col>
                        <print-col
                            v-else
                            :span="8"
                            class="text-right text-info"
                        >
                            {{ row[1].count }}{{ row[1].unit }}
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            v-if="chineseMedicine.position && row[1].position"
                            class="text-info"
                            overflow
                            style="line-height: 11pt"
                            :span="12"
                        >
                            {{ row[1].position }}
                        </print-col>
                        <print-col
                            overflow
                            :span="chineseMedicine.position && row[1].position ? 12 : 24"
                            style="text-align: right;line-height: 11pt"
                        >
                            <template v-if="row[1].specialRequirement && chineseMedicine.specialRequirement">
                                [{{ row[1].specialRequirement }}]
                            </template>
                        </print-col>
                    </print-row>
                </print-col>
            </print-row>

            <print-row v-if="form.doseCount">
                <print-col
                    :span="24"
                >
                    共 <span style="font-size: 11pt">{{ form.doseCount }}</span> 剂 ({{ specification(form.cMSpec) }}{{ doseTotal(form).kind }}味 单剂{{
                        doseTotal(form).count
                    }}g 总重{{ parseFloat((form.doseCount * doseTotal(form).count).toFixed(2)) }}g)
                </print-col>
            </print-row>
        </div>
        <print-row v-if="showUsage && !isUndispense">
            <print-col
                :span="24"
                class="chinese-process"
            >
                <div class="label">
                    【用法】
                </div>
                <div class="text">
                    {{ form.usage }} {{ form.dailyDosage }} {{ form.freq }}
                    {{ form.usageLevel }} {{ form.usageDays }} {{ form.requirement }}
                </div>
            </print-col>
        </print-row>

        <print-row v-if="!isUndispense">
            <print-col
                v-if="otherConfig.process && form.processUsageInfo "
                :span="24"
                class="chinese-process"
            >
                <div class="label">
                    【加工】
                </div>
                <div class="text">
                    {{ processStr(form) }}
                </div>
            </print-col>
        </print-row>

        <template v-if="otherConfig.delivery && form.deliveryInfo">
            <print-row>
                <print-col :span="24">
                    【快递】{{ getDeliveryCompany(form.deliveryInfo) }}
                </print-col>
            </print-row>
            <print-row>
                <print-col :span="24">
                    {{ form.deliveryInfo.deliveryName }} {{ form.deliveryInfo.deliveryMobile }}
                </print-col>
            </print-row>
            <print-row>
                <print-col :span="24">
                    {{ getDeliverAddressStr(form.deliveryInfo) }}
                </print-col>
            </print-row>
        </template>
        <div class="print-split-line"></div>
    </div>
</template>

<script>
    import PrintRow from "../layout/print-row.vue";
    import PrintCol from "../layout/print-col.vue";
    import clone from "../../common/clone.js";
    import { formatMoney } from '../../common/utils.js';

    export default {
        name: "ChineseForm",
        components: {
            PrintCol,
            PrintRow,
        },
        filters: {
            formatMoney,
        },
        props: {
            form: {
                type: Object,
                required: true,
            },
            chineseMedicine: {
                type: Object,
                required: true,
            },
            otherConfig: {
                type: Object,
                required: true,
            },
            isUndispense:{
                type:Boolean,
                default:false,
            },
        },
        computed: {
            nameSpan() {
                let span = 21;
                if(this.chineseMedicine.position ){
                    span -= 4;
                }
                return span;
            },
            showUsage() {
                return this.form.usage|| this.form.dailyDosage ||  this.form.freq
                    || this.form.usageLevel || this.form.usageDays ||  this.form.requirement;
            },
        },
        methods: {
            specification(specification) {
                if (!specification) return '';
                if (specification === '中药饮片') {
                    return '饮片';
                }
                return '颗粒';

            },
            doseTotal(form) {
                let count = 0;
                const kinds = [];
                const NUM = 10000;
                form.dispensingFormItems.forEach((item) => {
                    if (item.unit === 'g') {
                        count += (item.count * NUM) || 0;
                    }
                    if (kinds.indexOf(item.name) === -1) {
                        kinds.push(item.name);
                    }
                });
                return {
                    kind: kinds.length,
                    count: count / NUM,
                };
            },
            getDeliveryCompany (deliveryInfo) {
                let str = '';
                if (deliveryInfo) {
                    str = deliveryInfo.deliveryCompany && deliveryInfo.deliveryCompany.name || '';
                    str += str ? '/' : '';
                    str += deliveryInfo.deliveryPayType ? '寄付' : '到付'; // deliveryPayType 0：到付，1：寄付
                }
                return str;
            },
            getDeliverAddressStr(deliveryInfo) {
                const {
                    addressCityName, addressDetail, addressDistrictName, addressProvinceName,
                } = deliveryInfo;
                let str = '';
                str += addressProvinceName || '';
                str += addressCityName || '';
                str += addressDistrictName || '';
                str += addressDetail || '';
                return str;
            },
            processStr(form) {
                let _str = '';
                if (form.processUsage) {
                    _str += `${form.processUsage} `;
                }
                if (form.processUsage && form.processUsage.indexOf('煎药') > -1) {
                    _str += `1剂煎${form.processBagUnitCountDecimal ? form.processBagUnitCountDecimal: form.processBagUnitCount}袋，共 ${form.doseCount * (form.processBagUnitCountDecimal ? form.processBagUnitCountDecimal: form.processBagUnitCount)} 袋`;
                }
                if (form.contactMobile) {
                    _str += `，联系人 ${form.contactMobile} `;
                }
                if(form.processRemark) {
                    _str += `，备注： ${form.processRemark}`;
                }
                return _str;
            },

            splitForm(form) {
                const items = clone(form.dispensingFormItems);
                const res = [];
                /*eslint-disable*/
                while (items && items.length) {
                    res.push(items.splice(0, 2));
                }
                return res;
            },
        }
    }
</script>
<style lang="scss">
.chinese-process {
    position: relative;
    .label {
        position: absolute;
        top: 0;
        left: 0;
    }
    .text {
        padding-left: 38pt;
    }
}
</style>