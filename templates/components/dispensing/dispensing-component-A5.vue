<template>
    <div>
        <template v-for="(sheet, sheetIndex) in dispensingSheet">
            <template v-if="getNormalForms(sheet).length || getMaterialForms(sheet).length || getGlassesForms(sheet).length || getOtherForms(sheet).length">
                <dispensing-header
                    :config="config"
                    :pharmacy-name="sheet.pharmacyName"
                    :pharmacy-address="sheet.pharmacyAddress"
                    :print-data="sheet"
                    :is-undispense="isUndispense"
                    data-type="header"
                    :data-pendants-index="`${sheetIndex}WP`"
                    :show-delivery="showDeliveryInfo(sheet)"
                >
                </dispensing-header>
                <template
                    v-for="form in getNormalForms(sheet)"
                >
                    <western-items
                        data-type="mix-box"
                        :form="form"
                        :form-items="form.dispensingFormItems"
                        :config="config"
                    ></western-items>
                    <div
                        v-if="other.delivery && sheet.deliveryInfo && !isUndispense"
                        class="print-dispensing-usage-wrapper"
                        style="border-top: none"
                    >
                        <div class="label">
                            快递：
                        </div>
                        <div class="content">
                            <p class="has-margin">
                                {{ company(sheet, form) }}
                            </p>
                            <p>{{ addressDetail(sheet, form) }}</p>
                        </div>
                    </div>
                    <doctor-advice
                        v-if="form.doctorAdvice && form.doctorAdvice.length"
                        :doctor-advice="form.doctorAdvice"
                    ></doctor-advice>
                </template>
                <!-- 商品材料 -->
                <template
                    v-for="form in getMaterialForms(sheet)"
                >
                    <western-items
                        data-type="mix-box"
                        :form="form"
                        :form-items="form.dispensingFormItems"
                        :config="config"
                        material-forms
                    ></western-items>
                    <div
                        v-if="other.delivery && sheet.deliveryInfo && !isUndispense"
                        class="print-dispensing-usage-wrapper"
                        style="border-top: none"
                    >
                        <div class="label">
                            快递：
                        </div>
                        <div class="content">
                            <p class="has-margin">
                                {{ company(sheet, form) }}
                            </p>
                            <p>{{ addressDetail(sheet, form) }}</p>
                        </div>
                    </div>
                    <doctor-advice
                        v-if="form.doctorAdvice && form.doctorAdvice.length"
                        :doctor-advice="form.doctorAdvice"
                    ></doctor-advice>
                </template>
                <!-- 眼镜 -->
                <template v-if="isSupportGlasses">
                    <template
                        v-for="form in getGlassesForms(sheet)"
                    >
                        <western-items
                            data-type="mix-box"
                            :form="form"
                            :form-items="form.dispensingFormItems"
                            :config="config"
                            glasses-forms
                        ></western-items>
                        <div
                            v-if="other.delivery && sheet.deliveryInfo && !isUndispense"
                            class="print-dispensing-usage-wrapper"
                            style="border-top: none"
                        >
                            <div class="label">
                                快递：
                            </div>
                            <div class="content">
                                <p class="has-margin">
                                    {{ company(sheet, form) }}
                                </p>
                                <p>{{ addressDetail(sheet, form) }}</p>
                            </div>
                        </div>
                        <doctor-advice
                            v-if="form.doctorAdvice && form.doctorAdvice.length"
                            :doctor-advice="form.doctorAdvice"
                        ></doctor-advice>
                    </template>
                </template>
                <!-- 其他 -->
                <template
                    v-for="form in getOtherForms(sheet)"
                >
                    <western-items
                        data-type="mix-box"
                        :form-items="form.dispensingFormItems"
                        :config="config"
                        other-forms
                    ></western-items>
                    <div
                        v-if="other.delivery && sheet.deliveryInfo && !isUndispense"
                        class="print-dispensing-usage-wrapper"
                        style="border-top: none"
                    >
                        <div class="label">
                            快递：
                        </div>
                        <div class="content">
                            <p class="has-margin">
                                {{ company(sheet, form) }}
                            </p>
                            <p>{{ addressDetail(sheet, form) }}</p>
                        </div>
                    </div>
                    <doctor-advice
                        v-if="form.doctorAdvice && form.doctorAdvice.length"
                        :doctor-advice="form.doctorAdvice"
                    ></doctor-advice>
                </template>
                <dispensing-footer
                    data-type="footer"
                    :print-data="sheet"
                    :config="config"
                    :data-pendants-index="`${sheetIndex}WP`"
                    :dispensing-form="normalAndOtherForm(sheet)"
                    :is-undispense="isUndispense"
                ></dispensing-footer>
            </template>
        </template>
        <template
            v-for="(sheet,index) in getChineseForms(dispensingSheet)"
        >
            <template
                v-for="(form, formIndex) in sheet.dispensingForms"
            >
                <dispensing-header
                    :config="config"
                    :print-data="sheet"
                    :c-m-spec="form.cMSpec"
                    :pharmacy-type="form.pharmacyType"
                    :show-process="showProcessRemark(form) && other.process"
                    :show-delivery="showDeliveryInfo(sheet)"
                    :pharmacy-name="sheet.pharmacyName"
                    :pharmacy-address="sheet.pharmacyAddress"
                    :take-medication-time="form.takeMedicationTime"
                    data-type="header"
                    :is-undispense="isUndispense"
                    :data-pendants-index="`${index}${formIndex}CP`"
                >
                </dispensing-header>
                <template v-for="items in splitArray(form.dispensingFormItems)">
                    <chinese-items
                        :form-items="items"
                        :config="config"
                        data-type="mix-box"
                    >
                    </chinese-items>
                </template>

                <div
                    data-type="mix-box"
                    class="print-dispensing-usage-wrapper"
                >
                    <div class="label">
                        用法：
                    </div>
                    <div class="content">
                        <p class="has-margin">
                            {{ totalInfo(form) }}
                        </p>
                        <p>{{ usageStr(form) }}</p>
                    </div>
                </div>
                <div
                    v-if="processStr(form) && other.process && !isUndispense"
                    class="print-dispensing-usage-wrapper"
                    data-type="mix-box"
                >
                    <div class="label">
                        加工：
                    </div>
                    <div class="content">
                        {{ processStr(form) }}
                    </div>
                </div>
                <div
                    v-if="other.delivery && sheet.deliveryInfo && !isUndispense"
                    class="print-dispensing-usage-wrapper"
                    data-type="mix-box"
                >
                    <div class="label">
                        快递：
                    </div>
                    <div class="content">
                        <p class="has-margin">
                            {{ company(sheet, form) }}
                        </p>
                        <p>{{ addressDetail(sheet, form) }}</p>
                    </div>
                </div>
                <doctor-advice
                    v-if="sheet.doctorAdvice && sheet.doctorAdvice.length && other.doctorAdvice"
                    :doctor-advice="doctorAdvice(sheet)"
                ></doctor-advice>
                <dispensing-footer
                    data-type="footer"
                    :print-data="sheet"
                    :data-pendants-index="`${index}${formIndex}CP`"
                    :config="config"
                    :dispensing-form="form"
                    :is-undispense="isUndispense"
                ></dispensing-footer>
            </template>
        </template>
    </div>
</template>

<script>
    import DispensingHeader from '../../components/medical-document-header/dispensing-header.vue';
    import DispensingFooter from '../../components/medical-document-footer/dispensing-footer.vue';
    import ChineseItems from '../../components/dispensing/chinese.vue';
    import WesternItems from '../../components/dispensing/western.vue';
    import DoctorAdvice from '../../components/prescription/doctor-advice/index.vue';
    import { SourceFormTypeEnum } from "../../common/constants.js";
    import { getDoctorAdviceArray } from "../../common/medical-transformat.js";

    export default {
        name: 'DispensingComponentA5',
        components: {
            DispensingHeader,
            DispensingFooter,
            ChineseItems,
            WesternItems,
            DoctorAdvice,
        },
        props: {
            renderData: {
                type: Object,
            },
            isUndispense:{
                type:Boolean,
                default:false,
            },
        },
        computed: {
            printData() {
                return this.renderData.printData;
            },
            isSupportGlasses() {
                return this.printData.IS_GLASSES;
            },
            dispensingSheet() {
                return this.printData.dispensingSheets || [this.printData];
            },
            config() {
                if(this.renderData.config && this.renderData.config.dispensing) {
                    return this.renderData.config.dispensing;
                }
                return {};
            },
            other() {
                return this.config.other || {};
            },
            showDeliveryRemark() {
                return this.other.delivery && this.deliveryInfo;
            },
            isTakeMedicationTime() {
                return this.printData._dispensingConfig?.isTakeMedicationTime;
            },
        },
        methods: {
            doctorAdvice(sheet){
                return getDoctorAdviceArray(sheet.doctorAdvice);
            },
            showProcessRemark(form) {
                return form && form.processUsageInfo;
            },
            showDeliveryInfo(sheet) {
                return sheet.deliveryInfo && this.other.delivery ;
            },

            getChineseForms(dispensingSheet) {
                const chinseSheet = []
                dispensingSheet.forEach(item => {

                    const chinseForms = item.dispensingForms.filter((form) => {
                        if(!this.isTakeMedicationTime) {
                            form.takeMedicationTime = '';
                        }
                        return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                    })
                    chinseSheet.push({
                        ...item,
                        dispensingForms: chinseForms,
                        
                    })
                })

                return chinseSheet
                
            },
            getNormalForms(dispensingSheet) {
                return dispensingSheet.dispensingForms.filter((form) => {
                    return form.sourceFormType !== SourceFormTypeEnum.PRESCRIPTION_CHINESE &&
                        form.sourceFormType !== SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM &&
                        form.sourceFormType !== SourceFormTypeEnum.MATERIAL &&
                        form.sourceFormType !== SourceFormTypeEnum.GLASSES &&
                        form.sourceFormType !== 0;
                });
            },
            getMaterialForms(dispensingSheet) {
                return dispensingSheet.dispensingForms.filter((form) => {
                    return (form.sourceFormType === SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM || form.sourceFormType === SourceFormTypeEnum.MATERIAL) && form.sourceFormType !== 0;
                });
            },
            getGlassesForms(dispensingSheet) {
                return dispensingSheet.dispensingForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.GLASSES && form.sourceFormType !== 0;
                });
            },
            getOtherForms(dispensingSheet) {
                return dispensingSheet.dispensingForms.filter((form) => {
                    return form.sourceFormType === 0;
                });
            },
            normalAndOtherForm(dispensingSheet) {
                return dispensingSheet.dispensingForms.filter((form) => {
                    return form.sourceFormType !== SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                });
            },
            totalInfo(form) {
                let _str = '';
                if (form.doseCount) {
                    _str += `共 ${form.doseCount} 剂，${this.doseTotal( form ).kind} 味，单剂 ${
                        this.doseTotal( form ).count
                    } g，总重 ${(this.doseTotal( form ).count * form.doseCount).toFixed( 2 )} g`;
                }

                return _str;
            },
            usageStr(form) {
                let _str = '';
                if (form.usage || form.dailyDosage || form.freq || form.usageLevel || form.usageDays) {
                    _str += `${form.usage ? `${form.usage}，` : ''}${
                        form.dailyDosage ? `${form.dailyDosage}，` : ''
                    }${form.freq ? `${form.freq}，` : ''}${form.usageLevel || ''}${form.usageDays ? `，${form.usageDays}` : ''}${form.requirement ? `，${form.requirement}` : ''}`;
                }

                return _str;
            },
            doseTotal(form) {
                let count = 0;
                const kinds = [];
                form.dispensingFormItems.forEach((item) => {
                    if (item.unit === 'g') {
                        count += item.count || 0;
                    }
                    if (kinds.indexOf(item.name) === -1) {
                        kinds.push(item.name);
                    }
                });
                return {
                    kind: kinds.length,
                    count: count.toFixed(2),
                };
            },
            processStr(form) {
                let _str = '';
                if (form.processUsage) {
                    _str += `${form.processUsage}`;
                }
                if (form.processUsage && form.processUsage.indexOf('煎药') > -1) {
                    _str += `，1剂煎${form.processBagUnitCountDecimal ? form.processBagUnitCountDecimal: form.processBagUnitCount}袋，共 ${
                        form.doseCount * (form.processBagUnitCountDecimal ? form.processBagUnitCountDecimal: form.processBagUnitCount)
                    } 袋`;
                }
                if (form.contactMobile) {
                    _str += `，联系人 ${form.contactMobile}`;
                }
                if(form.processRemark) {
                    _str += `，备注： ${form.processRemark}`;
                }
                return _str;
            },
            addressDetail(sheet, form) {
                const deliveryInfo = sheet.deliveryInfo || form.deliveryInfo;
                if (deliveryInfo) {
                    const {
                        addressCityName,
                        addressDetail,
                        addressDistrictName,
                        addressProvinceName,
                    } = deliveryInfo;
                    let str = '';
                    str += addressProvinceName || '';
                    str += addressCityName || '';
                    str += addressDistrictName || '';
                    str += addressDetail || '';
                    return `${str}`;
                }
                return '';
            },
            company(sheet, form) {
                const deliveryInfo = sheet.deliveryInfo || form.deliveryInfo;
                let _str = '';
                if (deliveryInfo) {
                    if (deliveryInfo.deliveryCompany) {
                        _str += `${
                            deliveryInfo.deliveryCompany.name ? deliveryInfo.deliveryCompany.name + ' - ' : ''
                        }`;
                    }
                    _str += `${deliveryInfo.deliveryPayType ? '寄付，' : '到付，'}`;
                    _str += `${deliveryInfo.deliveryName} ${deliveryInfo.deliveryMobile}`;
                }
                return _str;
            },
            // 将一个数组拆分成每组4个元素的二维数组
            splitArray(arr) {
                const result = [];
                for (let i = 0, len = arr.length; i < len; i += 4) {
                    result.push(arr.slice(i, i + 4));
                }
                return result;
            },

        },
    };
</script>
<style lang="scss">
@import '../../style/reset.scss';
@import "../../components/layout/print-layout";
* {
    font-family: "Microsoft YaHei", "微软雅黑";
}

.abc-page-content{
    padding: 8pt;
    box-sizing: border-box;
    overflow: hidden;
    font-family: "Microsoft YaHei", "微软雅黑";
}


.print-dispensing-usage-wrapper {
    display: flex;
    align-items: flex-start;
    padding: 6pt 0;
    font-size: 10pt;
    line-height: 14pt;
    border-top: 1pt dashed #000000;

    .label {
        width: 32pt;
        min-width: 32pt;
        max-width: 32pt;
        font-weight: 400;
    }

    .content {
        flex: 1;
        font-size: 10pt;

        .item {
            font-size: 9pt;
            line-height: 11pt;
        }
    }
    .has-margin {
        margin-bottom: 3pt;
    }
}
</style>
