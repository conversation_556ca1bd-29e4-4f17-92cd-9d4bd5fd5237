<template>
    <div class="dispensing-western-form-items">
        <div data-type="group">
            <print-row
                v-for="(formItem, formItemIndex) in formItems"
                :key="formItemIndex"
                data-type="item"
                class="western-item"
            >
                <print-col
                    v-if="glassesForms"
                    :span="glassesConfig.spec ? 10 : 21"
                    class="name"
                    :class="{ 'no-spec': !glassesConfig.spec }"
                    overflow
                >
                    {{ formItem.name }}
                </print-col>
                <print-col
                    v-if="!glassesForms"
                    :span="westernMedicine.spec ? 10 : 15"
                    class="name"
                    :class="{ 'no-spec': !westernMedicine.spec }"
                    overflow
                >
                    <template v-if="isGift">
                        【赠品】
                    </template>
                    {{ formItem.name }}
                </print-col>
                <print-col
                    v-if="otherForms && other.position"
                    :span="6"
                    class="spec"
                    overflow
                >
                    {{ formItem.position }}
                </print-col>
                <print-col
                    v-if="!otherForms && !glassesForms && westernMedicine.position"
                    :span="(westernMedicine && westernMedicine.batchNo) ? 3 : 6"
                    class="spec"
                    overflow
                >
                    {{ formItem.position }}
                </print-col>
                <print-col
                    v-if="!otherForms && !glassesForms && westernMedicine.batchNo"
                    :span="4"
                    class="spec"
                    overflow
                >
                    {{ formItem.batchNo }}
                </print-col>

                <print-col
                    v-if="glassesForms && glassesConfig.spec"
                    :span="11"
                    class="spec"
                    overflow
                >
                    {{ formItem.formatSpec }}
                </print-col>
                <print-col
                    v-if="!glassesForms && westernMedicine.spec"
                    :span="(westernMedicine && westernMedicine.batchNo) ? 4 : 5"
                    class="spec"
                    overflow
                >
                    {{ formItem.formatSpec }}
                </print-col>

                <print-col
                    v-if="formItem.sourceItemType === 1"
                    class="count"
                    :span="3"
                >
                    自备
                </print-col>
                <print-col
                    v-else
                    class="count"
                    :span="3"
                >
                    {{ formItem.count }}{{ formItem.unit }}
                </print-col>
                <print-col
                    :span="24"
                    overflow
                    style="white-space: nowrap"
                >
                    <template
                        v-if="!glassesForms && westernMedicine.usage && formItem.usageInfo"
                    >
                        <span
                            v-if="
                                formItem.usageInfo.dosage || 
                                    formItem.usageInfo.dosageUnit || 
                                    formItem.usageInfo.freq || 
                                    formItem.usageInfo.usage || 
                                    formItem.usageInfo.specialRequirement
                            "
                            class="remark"
                            :style="{ paddingRight: '7pt' }"
                        >
                            <template v-if="formItem.usageInfo && formItem.usageInfo.dosage">Sig：{{ form.sourceFormType === 16 ? '共' : '每次' }}{{
                                formItem.usageInfo.dosage
                            }}{{ formItem.usageInfo.dosageUnit || '次' }}</template>
                            {{ freqFormat(formItem.usageInfo && formItem.usageInfo.freq, 1) }}
                            {{ usageFormat(formItem.usageInfo && formItem.usageInfo.usage, 1) }}
                            {{ formItem.usageInfo && formItem.usageInfo.specialRequirement }}
                        </span>
                    </template>

                    <!-- 厂家 -->
                    <template v-if="westernMedicine.manufacturer">
                        <span class="remark">
                            厂家：{{ formItem.manufacturer }}
                        </span>
                    </template>
                </print-col>
            </print-row>
        </div>
    </div>
</template>

<script>
    import PrintRow from "../layout/print-row.vue";
    import PrintCol from "../layout/print-col.vue";

    import {freqFormat, usageFormat} from "../../common/medical-transformat.js";

    export default {
        name: 'Western',
        components: {
            PrintRow,
            PrintCol,
        },
        props: {
            formItems: {
                type: Array,
            },
            config: {
                type: Object,
            },
            isGift: {
                type: Boolean,
                default: false,
            },
            materialForms: {
                type: Boolean,
                default: false,
            },
            glassesForms: {
                type: Boolean,
                default: false,
            },
            otherForms: {
                type: Boolean,
                default: false,
            },
            form: {
                type: Object,
                default: ()=>{return {}}
            }
        },
        computed: {
            westernMedicine() {
                if (this.materialForms) return (this.config && this.config.materialGoods) || {};
                return (this.config && this.config.westernMedicine) || {};
            },
            glassesConfig() {
                return (this.config && this.config.glassesGoods) || {};
            },
            other() {
                return (this.config && this.config.materialGoods) || {position: '1'};
            },
        },
        methods: {
            freqFormat,
            usageFormat
        }
    };
</script>
<style lang="scss">
.dispensing-western-form-items {
  padding: 6pt 0;
  border-bottom: 1pt dashed #000000;

  .western-item {
    font-size: 0;
    line-height: 16pt;

    .name,
    .spec,
    .count,
    .remark {
      overflow: hidden;
      font-size: 10pt;
      line-height: 12pt;
      height: 12pt;
      word-break: break-all;
      white-space: nowrap;
    }

    .remark {
      min-width: 55pt;
      margin-left: 2pt;
      font-size: 9pt;
    }

    .spec,
    .count {
      text-align: right;
    }
  }
}

</style>
