<template>
    <div>
        <template v-for="sheet in dispensingSheet">
            <div
                :key="sheet.id"
                class="print-ticket-content"
            >
                <div class="content-item">
                    <print-row>
                        <print-col
                            :span="24"
                        >
                            <div
                                v-if="checkVirtualForm(sheet)"
                                style="display: inline-block; vertical-align: middle"
                            >
                                <div class="process-icon-text">
                                    代煎
                                </div>
                                <div class="process-icon-text">
                                    代配
                                </div>
                            </div>
                            <div
                                v-else
                                style="display: inline-block; vertical-align: middle"
                            >
                                <div
                                    v-if="showProcessRemark(sheet) && otherConfig.process"
                                    style="margin-right: 8pt;"
                                    class="process-icon-text"
                                >
                                    加工
                                </div>
                                <div
                                    v-if="showDeliveryRemark(sheet) && otherConfig.delivery"
                                    class="process-icon-text"
                                >
                                    快递
                                </div>
                                <div v-if="isTakeMedicationTime && getSheetTakeMedicationTime(sheet)">
                                    {{ isToday(getSheetTakeMedicationTime(sheet)) ? '(今)' : '' }} {{ getSheetTakeMedicationTime(sheet) |parseTime('m-d h:i') }} 取药
                                </div>
                            </div>

                            <img
                                v-if="config.barcode && barcodeSrc"
                                class="barcode-image"
                                :src="barcodeSrc"
                            />

                            <div
                                v-if="isUndispense"
                                class="undispense"
                            >
                                退药
                            </div>
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            :span="24"
                            class="organ-name"
                        >
                            {{ organTitle }}
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            :span="24"
                            class="type"
                        >
                            {{ isUndispense ? `退药单${ chargeTransactionTime }` : '调剂发药单' }}
                        </print-col>
                    </print-row>

                    <print-row>
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            姓名：{{ patient.name || '匿名患者' }}&nbsp;&nbsp;<span v-if="patientInfo.sex">{{ patient.sex }}</span>&nbsp;
                            <span v-if="patientInfo.age">{{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}</span>
                        </print-col>
                    </print-row>

                    <print-row v-if="patientInfo.mobile">
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            手机：{{ patient.mobile | filterMobileV2(patientInfo.mobileType) }}
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            v-if="patientInfo.patientOrderNo && sheet.patientOrderNo"
                            :span="24"
                            class="text-info"
                        >
                            诊号：{{ sheet.patientOrderNo }}
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            v-if="patientInfo.departmentName && sheet.departmentName"
                            class="text-info"
                            :span="24"
                        >
                            科室：{{ sheet.departmentName }}
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            v-if="patientInfo.doctor && sheet.doctorName"
                            class="text-info overflow-text"
                            :span="24"
                            overflow
                        >
                            医生：{{ sheet.doctorName }}
                        </print-col>
                        <print-col
                            v-if="patientInfo.doctor && !sheet.doctorName && sheet.sellerName"
                            class="text-info"
                            :span="24"
                        >
                            开单：{{ sheet.sellerName }}
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            v-if="patientInfo.diagnose && sheet.diagnose"
                            class="text-info"
                            :span="24"
                            overflow
                        >
                            诊断：{{ formatDiagnosis2Str(sheet.diagnose) }}
                        </print-col>
                    </print-row>
                </div>

                <div class="print-split-line"></div>

                <template v-if="sheet.openPharmacyFlag === 20 && otherConfig.takeMedicineLocation">
                    <print-row>
                        <print-col
                            class="get-medicine-place"
                            style="font-size: 9pt"
                            :span="24"
                            overflow
                        >
                            取药地点：{{ sheet.pharmacyName }} {{ sheet.pharmacyAddress }}
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            v-if="isTakeMedicationTime && getSheetTakeMedicationTime(sheet)"
                            style="font-size: 9pt"
                            class="get-medicine-time"
                            :span="24"
                            overflow
                        >
                            取药时间：预计 {{ getSheetTakeMedicationTime(sheet) | parseTime('m-d h:i') }}
                        </print-col>
                    </print-row>

                    <div class="print-split-line"></div>
                </template>

                <template v-if="filterWesternForms(sheet).length">
                    <western-form
                        v-for="form in filterWesternForms(sheet)"
                        :key="form.id"
                        :form="form"
                        :western-medicine="westernMedicine"
                    ></western-form>
                    <div
                        class="print-split-line"
                    ></div>
                </template>

                <!--套餐中的西药-->
                <div
                    v-if="filterOtherForms(sheet).length"
                    class="content-item"
                >
                    <western-form
                        v-for="form in filterOtherWMedicineForms(sheet)"
                        :key="form.id"
                        :form="form"
                        :western-medicine="westernMedicine"
                    ></western-form>

                    <div
                        v-if="filterOtherCMedicineForms(sheet).length"
                        class="print-split-line"
                    ></div>

                    <!--套餐中的中药-->
                    <chinese-form
                        v-for="(form, rIndex) in filterOtherCMedicineForms(sheet)"
                        :key="rIndex"
                        :form="form"
                        :other-config="otherConfig"
                        :chinese-medicine="chineseMedicine"
                    ></chinese-form>

                    <!--套餐中的其他-->
                    <material-form
                        v-for="(form, rIndex) in filterOtherFormProducts(sheet)"
                        :key="rIndex"
                        :form="form"
                        :material-goods="materialGoods"
                    ></material-form>

                    <div
                        v-if="filterOtherFormProducts(sheet).length"
                        class="print-split-line"
                    ></div>
                </div>

                <template v-if="filterMaterialForms(sheet).length">
                    <material-form
                        v-for="form in filterMaterialForms(sheet)"
                        :key="form.id"
                        :form="form"
                        :material-goods="materialGoods"
                    ></material-form>
                    <div
                        class="print-split-line"
                    ></div>
                </template>

                <template v-if="filterGiftForms(sheet).length">
                    <div
                        v-for="form in filterGiftForms(sheet)"
                        :key="form.id"
                        class="content-item"
                    >
                        <print-row
                            v-for="formItem in form.dispensingFormItems"
                            :key="formItem.id"
                            class="item-row row"
                        >
                            <print-col
                                :span="20"
                                class="text-info"
                            >
                                {{ formItem.name }}(赠)
                            </print-col>
                            <print-col
                                :span="4"
                                class="text-right text-info"
                            >
                                {{ formItem.count }}{{ formItem.unit }}
                            </print-col>
                        </print-row>
                    </div>
                    <div

                        class="print-split-line"
                    ></div>
                </template>

                <template v-if="filterChineseForms(sheet).length">
                    <chinese-form
                        v-for="form in filterChineseForms(sheet)"
                        :key="form.id"
                        :form="form"
                        :other-config="otherConfig"
                        :chinese-medicine="chineseMedicine"
                        :is-undispense="isUndispense"
                    >
                    </chinese-form>
                </template>

                <template v-if="isSupportGlasses && filterEyeglassesForms(sheet).length">
                    <western-form
                        v-for="form in filterEyeglassesForms(sheet)"
                        :key="form.id"
                        :form="form"
                        :western-medicine="{
                            spec: glassesGoods.spec,
                            usage: 0,
                            position: 0,
                        }"
                    ></western-form>
                    <div
                        class="print-split-line"
                    ></div>
                </template>

                <template v-if="otherConfig.delivery && sheet.deliveryInfo && !isUndispense">
                    <div class="content-item">
                        <print-row>
                            <print-col :span="24">
                                【快递】{{ getDeliverCompany(sheet) }}
                            </print-col>
                        </print-row>
                        <print-row>
                            <print-col :span="24">
                                {{ sheet.deliveryInfo.deliveryName }} {{ sheet.deliveryInfo.deliveryMobile }}
                            </print-col>
                        </print-row>
                        <print-row>
                            <print-col :span="24">
                                {{ getDeliverAddressStr(sheet) }}
                            </print-col>
                        </print-row>
                    </div>
                    <div
                        class="print-split-line"
                    ></div>
                </template>


                <template v-if="otherConfig.doctorAdvice && sheet.doctorAdvice && !isUndispense">
                    <div class="content-item">
                        <print-row>
                            <print-col :span="24">
                                【医嘱】
                                <span v-html="sheet.doctorAdvice"></span>
                            </print-col>
                        </print-row>
                    </div>
                    <div class="print-split-line"></div>
                </template>


                <div class="content-item">
                    <print-row>
                        <print-col
                            v-if="ticketFooter.chargedByName && sheet.chargedByName"
                            :span="24"
                        >
                            收费员：{{ sheet.chargedByName }}
                        </print-col>
                    </print-row>

                    <print-row>
                        <print-col
                            v-if="ticketFooter.amount"
                            :span="24"
                        >
                            {{ isUndispense ? '已退费' : '金额' }}：
                            <template v-if="isUndispense">
                                <span>{{ sheet.receivedPrice | formatMoney }}</span>
                            </template>
                            <template v-else>
                                <span v-if="ticketFooter.amount === 1">{{ sheet.receivedPrice | formatMoney }}</span>
                                <span v-if="ticketFooter.amount === 2"> {{ sheet.netIncomeFee | formatMoney }}</span>
                            </template>
                        </print-col>
                    </print-row>

                    <print-row v-if="ticketFooter.check">
                        <print-col :span="24">
                            审核：{{ sheet.auditName }}
                        </print-col>
                    </print-row>

                    <print-row v-if="ticketFooter.assigner">
                        <print-col :span="24">
                            调配：{{ sheet.compoundName }}
                        </print-col>
                    </print-row>

                    <print-row v-if="ticketFooter.dispense">
                        <print-col :span="24">
                            核发：{{ sheet.dispensedByName || '' }}
                        </print-col>
                    </print-row>

                    <print-row v-if="ticketFooter.printTime">
                        <print-col :span="24">
                            打印时间：{{ new Date() | parseTime('y-m-d h:i:s') }}
                        </print-col>
                    </print-row>

                    <print-row v-if="ticketFooter.address && getClinic(sheet).addressDetail">
                        <print-col :span="24">
                            地址：{{ getClinic(sheet).addressDetail }}
                        </print-col>
                    </print-row>
                    <print-row v-if="ticketFooter.mobile && getClinic(sheet).contactPhone">
                        <print-col :span="24">
                            电话：{{ getClinic(sheet).contactPhone }}
                        </print-col>
                    </print-row>
                </div>

                <template v-if="config.remark">
                    <div class="print-split-line"></div>
                    <div class="content-item">
                        <print-row>
                            <print-col
                                :span="24"
                                style="white-space: pre-wrap;"
                                v-html="config.remark"
                            ></print-col>
                        </print-row>
                    </div>
                    <div class="print-split-line"></div>
                </template>

                <print-row v-if="ticketFooter.replacementReminder && !isUndispense">
                    <print-col

                        :span="24"
                        class="print-cashier-title text-info"
                    >
                        药品离柜，概不退换
                    </print-col>
                </print-row>

                <print-row>
                    <div
                        v-if="qrCode && ticketFooter.qrCode"
                        :gutter="6"
                        class="qr-code-wrapper"
                    >
                        <img
                            class="qr-code"
                            borderthin="true"
                            :src="qrCode"
                            alt=""
                        />
                        <div
                            class="qr-code-tips"
                        >
                            <div>微信就诊</div>
                            <div>挂号预约</div>
                            <div>就诊报告</div>
                            <div>用药医嘱</div>
                        </div>
                    </div>
                </print-row>
            </div>
            <div
                :key="`${sheet.id}newpage`"
                data-type="new-page"
            >
            </div>
        </template>
    </div>
</template>

<script>
    import PrintRow from '../../components/layout/print-row.vue';
    import PrintCol from '../../components/layout/print-col.vue';
    import WesternForm from "../../components/dispensing/western-form.vue";
    import ChineseForm from "../../components/dispensing/chinese-form.vue";
    import MaterialForm from "../../components/dispensing/material-form.vue";
    import { formatAge, formatDiagnosis2Str, clone, parseTime, textToBase64BarCode, formatMoney,isToday } from '../../common/utils.js';
    import { PrintBusinessKeyEnum } from "../../constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../../../share/page-size.js";
    import PrintCommonDataHandler from "../../data-handler/common-handler.js";
    import { filterMobileV2 } from "../../common/medical-transformat.js";
    import { GoodsSubTypeEnum, SourceFormTypeEnum, GoodsTypeEnum, PharmacyTypeEnum } from "../../common/constants.js";

    export default {
        name: 'PrintDispensingComponentTemplate',
        components: {
            PrintRow,
            PrintCol,
            WesternForm,
            ChineseForm,
            MaterialForm,
        },
        filters: {
            filterMobileV2,
            parseTime,
            formatMoney,
        },
        businessKey: PrintBusinessKeyEnum.DISPENSE,
        DataHandler: PrintCommonDataHandler,
        pages: [
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM58,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_230,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_240,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM70_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80_100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_120,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100_140,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_93_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM148_118_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM140_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM200_1397,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM175_94,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM175_94_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM120_200,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM90_120_CASHIER,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分' // 默认选择的等分纸
            },
        ],
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
            isUndispense:{
                type:Boolean,
                default:false
            }
        },

        computed: {
            printData() {
                return this.renderData.printData
            },
            chargeTransactionTime() {
                let chargeTransactionTime = '(';
                if (this.printData?.chargeTransactionTime) {
                    return chargeTransactionTime += parseTime(this.printData.chargeTransactionTime,'m-d h:i') + ' 已退费)'
                }
                return ''
            },
            isSupportGlasses() {
                return this.printData.IS_GLASSES;
            },
            dispensingSheet() {
                return this.printData.dispensingSheets || [this.printData];
            },
            config() {
                if(this.renderData.config && this.renderData.config.dispensing) {
                    return this.renderData.config.dispensing;
                }
                return {};
            },
            organTitle() {
                if(!this.config.title) {
                    return this.dispensingSheet[0] && this.dispensingSheet[0].name || '';
                }
                return this.config.title || '';
            },
            qrCode() {
                return this.printData.qrCode || this.dispensingSheet[0].qrCode;
            },
            patient() {
                return this.dispensingSheet[0] && this.dispensingSheet[0].patient;
            },
            patientInfo() {
                return this.config && this.config.patientInfo || {};
            },
            otherConfig() {
                return this.config && this.config.other || {};
            },
            ticketFooter() {
                return this.config && this.config.ticketFooter || {};
            },
            // 中药配置项
            chineseMedicine() {
                return this.config && this.config.chineseMedicine || {};
            },
            // 西成药配置
            westernMedicine() {
                return this.config && this.config.westernMedicine || {};
            },
            // 眼镜配置
            glassesGoods() {
                return this.config && this.config.glassesGoods || {};
            },
            //材料配置
            materialGoods() {
                return this.config && this.config.materialGoods || {position: 1};
            },
            barcodeSrc() {
                const barcode = this.dispensingSheet[0]?.patientOrderNo ? `${this.dispensingSheet[0]?.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode);
            },
            isTakeMedicationTime() {
                return this.printData._dispensingConfig?.isTakeMedicationTime;
            },
        },
        methods: {
            isToday,
            formatDiagnosis2Str,
            formatAge,
            getClinic(sheet) {
                return sheet && sheet.organ;
            },
            filterWesternForms(sheet) {
                return sheet.dispensingForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN
                        || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION
                        || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL;
                });
            },
            filterChineseForms(sheet) {
                return sheet.dispensingForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE;
                });
            },
            filterMaterialForms(sheet) {
                return sheet.dispensingForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM || form.sourceFormType === SourceFormTypeEnum.MATERIAL;
                });
            },
            getSheetTakeMedicationTime(sheet){
                const chineseForms = this.filterChineseForms(sheet);
                let earliestTime = '';
                chineseForms.forEach(form => {
                    if(form.takeMedicationTime && !earliestTime) {
                        earliestTime = form.takeMedicationTime;
                    }
                    if(earliestTime && form.takeMedicationTime && new Date(earliestTime) > new Date(form.takeMedicationTime)) {
                        earliestTime = form.takeMedicationTime
                    }
                })
                return earliestTime;
            },
            filterEyeglassesForms(sheet) {
                return sheet.dispensingForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.GLASSES;
                });
            },
            /**
             * @desc 套餐 材料 商品form
             * <AUTHOR>
             * @date 2020-10-21 18:52:50
             * @params
             * @return
             */
            filterOtherForms(sheet) {
                return (
                    sheet.dispensingForms.filter((form) => {
                        return form.isCombineForm;
                    }) || []
                );
            },
            /**
             * @desc 套餐中的西药
             */
            filterOtherWMedicineForms(sheet) {
                const forms = clone(this.filterOtherForms(sheet)).filter((form) => {
                    if (form.dispensingFormItems && form.dispensingFormItems.length) {
                        form.dispensingFormItems =
                            form.dispensingFormItems.filter((item) => {
                                return item && item.productType === GoodsTypeEnum.MEDICINE && !this.isChineseMedicine(item);
                            }) || [];
                        return form.dispensingFormItems.length;
                    }
                });
                return forms;
            },
            isChineseMedicine(item) {
                if(!item) return false;
                return item.productType === GoodsTypeEnum.MEDICINE && item.productSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine;
            },
            /**
             * @desc 套餐中的中药
             */
            filterOtherCMedicineForms(sheet) {
                const forms = clone(this.filterOtherForms(sheet)).filter((form) => {
                    if (form.dispensingFormItems && form.dispensingFormItems.length) {
                        form.dispensingFormItems =
                            form.dispensingFormItems.filter((item) => {
                                return this.isChineseMedicine(item)
                            }) || [];
                        return form.dispensingFormItems.length;
                    }
                });
                return forms;
            },
            filterOtherFormProducts(sheet) {
                const forms = clone(this.filterOtherForms(sheet)).filter((form) => {
                    if (form.dispensingFormItems && form.dispensingFormItems.length) {
                        form.dispensingFormItems =
                            form.dispensingFormItems.filter((item) => {
                                if (!item) {
                                    return false;
                                }
                                return item.productType !== 1;
                            }) || [];
                        return form.dispensingFormItems.length;
                    }
                });
                return forms;
            },
            filterGiftForms(sheet) {
                return sheet.dispensingForms.filter((form) => {
                    return form.sourceFormType === 10;
                });
            },
            getDeliverCompany(sheet) {
                let str = '';
                const {deliveryInfo} = sheet;
                if (deliveryInfo) {
                    str = deliveryInfo.deliveryCompany && deliveryInfo.deliveryCompany.name;
                    str += str ? '/' : '';
                    str += deliveryInfo.deliveryPayType ? '寄付' : '到付'; // deliveryPayType 0：到付，1：寄付
                }
                return str;
            },
            getDeliverAddressStr(sheet) {
                if(!sheet) return '';
                const {deliveryInfo} = sheet;
                const {
                    addressCityName, addressDetail, addressDistrictName, addressProvinceName,
                } = deliveryInfo;
                let str = '';
                str += addressProvinceName || '';
                str += addressCityName || '';
                str += addressDistrictName || '';
                str += addressDetail || '';
                return str;
            },
            /**
             * @desc 快递费标记
             */
            showDeliveryRemark(sheet) {
                const hasDeliveryInfo =
                    this.filterChineseForms(sheet).findIndex((form) => {
                        return form.deliveryInfo;
                    }) > -1;
                return (hasDeliveryInfo || sheet.deliveryInfo) && this.otherConfig.delivery;
            },
            showProcessRemark(sheet) {
                const hasProcess =
                    this.filterChineseForms(sheet).findIndex((form) => {
                        return form.processUsageInfo;
                    }) > -1;
                return hasProcess;
            },
            checkVirtualForm(sheet) {
                const hasVirtualForm =
                    this.filterChineseForms(sheet).findIndex((form) => {
                        return form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
                    }) > -1;
                return hasVirtualForm;
            },
            getDeliveryCompany (deliveryInfo) {
                let str = '';
                if (deliveryInfo) {
                    str = deliveryInfo.deliveryCompany && deliveryInfo.deliveryCompany.name || '';
                    str += str ? '/' : '';
                    str += deliveryInfo.deliveryPayType ? '寄付' : '到付'; // deliveryPayType 0：到付，1：寄付
                }
                return str;
            },

            specification(specification) {
                if (!specification) return '';
                if (specification === '中药饮片') {
                    return '饮片';
                }
                return '颗粒';

            },
            doseTotal(form) {
                let count = 0;
                const kinds = [];
                form.dispensingFormItems.forEach((item) => {
                    if (item.unit === 'g') {
                        count += item.count || 0;
                    }
                    if (kinds.indexOf(item.name) === -1) {
                        kinds.push(item.name);
                    }
                });
                return {
                    kind: kinds.length,
                    count,
                };
            },
        },
    };
</script>

<style lang="scss">
@import '../../style/ticket-common.scss';

.barcode-image {
    width: 90pt;
    height: 24pt;
    vertical-align: middle;
    border: 0;
}

.process-icon-text {
    font-size: 12pt;
    font-weight: bold;
    line-height: 20pt;
}

.undispense {
  border: 2px solid black;
  display: inline-block;
  float: right;
  vertical-align: middle;
  border-radius: 8px;
  width: 70px;
  height: 26px;
  line-height: 26px;
  text-align: center;
}
</style>
