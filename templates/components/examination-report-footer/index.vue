<template>
    <div class="print-examination-footer">
        <abc-print-row>
            <abc-print-col :span="8">
                <span class="item-label">检验者:</span>

                <template v-if="footerConfig.testerSignature === 1">
                    <hand-sign :value="testerSignature"></hand-sign>
                </template>

                <template v-else-if="footerConfig.testerSignature === 0">
                    <span class="item-value">{{ printData.testerName }}</span>
                </template>
            </abc-print-col>

            <abc-print-col :span="8">
                <span class="item-label">
                    审核者:
                </span>

                <template v-if="footerConfig.checkerSignature === 1">
                    <hand-sign :value="checkerSignature"></hand-sign>
                </template>

                <template v-else-if="footerConfig.checkerSignature === 0">
                    <span class="item-value">{{ printData.checkerName }}</span>
                </template>
            </abc-print-col>

            <abc-print-col
                v-if="footerConfig.applyDate"
                :span="8"
                style="text-align: right;"
            >
                <span class="item-label">申请时间:</span>
                <span class="item-value">{{ printData.created | parseTime('y-m-d h:i:s') }}</span>
            </abc-print-col>
        </abc-print-row>

        <abc-print-row>
            <abc-print-col
                v-if="footerConfig.checkDate"
                :span="8"
            >
                <span class="item-label">
                    审核时间:
                </span>
                <span class="item-value">{{ printData.reportTime| parseTime('y-m-d h:i:s') }}</span>
            </abc-print-col>

            <abc-print-col
                v-if="footerConfig.printDate"
                :span="8"
            >
                <span class="item-label">
                    打印时间:
                </span>
                <span class="item-value">
                    {{ new Date() | parseTime('y-m-d h:i:s') }}
                </span>
            </abc-print-col>

            <abc-print-col
                v-if="footerConfig.examineDate"
                :span="8"
                style="text-align: right;"
            >
                <span class="item-label">检验时间:</span>
                <span class="item-value">{{ printData.testTime | parseTime('y-m-d h:i:s') }}</span>
            </abc-print-col>
        </abc-print-row>


        <abc-print-row class="tips">
            {{ footerConfig.remark }}
        </abc-print-row>

        <template v-if="footerConfig.mutualRecognitionDesc">
            <abc-print-row class="tips">
                <span>注：带</span>
                <examination-identification
                    type="1"
                ></examination-identification>
                <span>
                    标识项目为包头市互认项目，带
                </span>
                <examination-identification
                    type="2"
                ></examination-identification>
                <span>
                    为呼包互认HR项目！
                </span>
            </abc-print-row>
        </template>
    </div>
</template>

<script>

    import { parseTime } from "../../common/utils.js";
    import AbcPrintRow from "../layout/abc-layout/abc-row.vue";
    import AbcPrintCol from "../layout/abc-layout/abc-col.vue";
    import HandSign from "../hand-sign/index.vue";
    import ExaminationIdentification from "../examination-report-landscape/examination-identification.vue";

    export default {
        name: "ExaminationReportFooter",
        components: {
            ExaminationIdentification,
            AbcPrintRow,
            AbcPrintCol,
            HandSign,
        },
        filters: {
            parseTime,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
        },
        computed: {
            patient() {
                return this.printData && this.printData.patient || {};
            },
            footerConfig() {
                return this.config.footer || {};
            },
            testerSignature() {
                return this.printData.tester?.handSign;
            },

            checkerSignature() {
                return this.printData.checker?.handSign;
            },
        },
    }
</script>

<style lang="scss">
.abc-page-content__footer {
    z-index: 1;
    background: #ffffff;
}

.print-examination-footer {
    padding-top: 2pt;
    border-top: 1px solid #000000;

    .tips {
        font-size: 9pt;
        //font-family: "Microsoft YaHei", "微软雅黑";
        span {
            color: #0090ff;
        }
    }

    .item-label,
    .item-value {
        font-size: 9pt;
    }

    .item-label {
        //font-family: "Microsoft YaHei", "微软雅黑";
    }

    .item-value {
        font-weight: 300;
    }
}
</style>
