<template>
    <table
        class="intelligent-abnormal-table"
    >
        <colgroup>
            <col
                v-for="(h, i) in headers"
                :key="i"
                :width="h.width"
            />
        </colgroup>

        <tbody
            class="intelligent-abnormal-table-body"
        >
            <template v-if="dataList.length > 0">
                <tr
                    v-for="(item, i) in dataList"
                    :key="i"
                    class="intelligent-abnormal-table-tr"
                >
                    <td
                        class="intelligent-abnormal-table-cell"
                    >
                        {{ item.goodsName }}
                    </td>
                    <td
                        class="intelligent-abnormal-table-cell"
                        style="color: #f04a3e;"
                    >
                        {{ item.value }}
                    </td>
                    <td
                        class="intelligent-abnormal-table-cell"
                        style="text-align: center;"
                    >
                        <nature-display
                            :key="i"
                            :style="{
                                height: 'auto',
                            }"
                            :item="item"
                            :need-color="true"
                        ></nature-display>
                    </td>
                    <td
                        class="intelligent-abnormal-table-cell"
                        style="text-align: left;"
                    >
                        {{ calRange(item) }}&nbsp;&nbsp;{{ item.unit }}
                    </td>
                    <td
                        class="intelligent-abnormal-table-cell"
                        style="text-align: left;"
                    >
                        {{ item.abnormalTerm }}
                    </td>
                </tr>
            </template>
            <template v-else>
                <tr>
                    <td colspan="5">
                        <div class="intelligent-abnormal-table-empty">
                            未见明显异常
                        </div>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
</template>

<script>
    import NatureDisplay from "../nature-display/index.vue";
    import {
        calRange,
    } from "../../common/medical-transformat";

    export default {
        name: 'AbnormalTable',
        components: { NatureDisplay },
        props: {
            dataList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                headers: [
                    {
                        label: '异常项目',
                        style: {
                            textAlign: 'left',
                        },
                        width: 136,
                    },
                    {
                        label: '结果',
                        style: {
                            textAlign: 'left',
                        },
                        width: 48,
                    },
                    {
                        label: '提示',
                        style: {
                            textAlign: 'center',
                        },
                        width: 48,
                    },
                    {
                        label: '单位',
                        style: {
                            textAlign: 'left',
                        },
                        width: 92,
                    },
                    {
                        label: '参考结果',
                        style: {
                            textAlign: 'left',
                        },
                        width: 332,
                    },
                ],
            };
        },
        methods: {
            calRange,
        },
    };
</script>



<style scoped lang="scss">
.intelligent-abnormal-table {
  width: 100%;
  border-collapse: separate;
  font-size: 12px;
  padding-top: 4px;

  .intelligent-abnormal-table-cell {
    padding: 4px;

    &:first-child {
      padding-left: 0;
    }

    &.table-header-cell {
      span {
        font-weight: 400;
        color: var(--abc-color-T2, #7a8794);
      }
    }
  }

  .intelligent-abnormal-table-empty {
        padding: 24px 0;
        margin: 0 12px;
        margin-top: 6px;
        border-top: 1px solid #E0E2EB;
        font-size: 13px;
    }
}
</style>
