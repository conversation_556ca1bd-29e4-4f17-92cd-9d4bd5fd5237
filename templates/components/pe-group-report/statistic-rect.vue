<template>
    <div class="statistic-rect-wrapper">
        <div
            class="rect-list-wrapper"
        >
            <div class="rect-item-label-wrapper">
                <div
                    v-for="(item, idx) in renderOptions.yAxis.items"
                    :key="idx"
                    class="rect-item-label"
                    :style="{
                        ...renderOptions.yAxis.labelStyle,
                        paddingTop: renderOptions.yAxis.rectStyle.paddingTop || '16pt',
                        height: renderOptions.yAxis.rectStyle.height || '16pt',
                    }"
                >
                    <template v-if="!item.labelFixedWidth">
                        {{ item.label }}
                    </template>
                    <template v-else>
                        <div
                            class="rect-item-label-inner"
                            :style="{ top: renderOptions.yAxis.rectStyle.paddingTop || '16pt' }"
                        >
                            {{ item.label }}
                        </div>
                    </template>
                </div>
            </div>

            <div class="rect-item-value-wrapper">
                <div class="rect-item-value-top">
                    <div
                        v-for="(item, idx) in renderOptions.yAxis.items"
                        :key="idx"
                        class="rect-item-value-box"
                        :style="{
                            marginTop: item.placeholder ? 0 : renderOptions.yAxis.rectStyle.paddingTop,
                        }"
                    >
                        <div
                            class="rect-item-value-inner"
                            :style="{
                                ...renderOptions.yAxis.rectStyle,
                                paddingTop: 0,
                                width: item.width,
                            }"
                        ></div>

                        <div
                            :style="{
                                position: 'absolute',
                                left: item.width,
                                top: '50%',
                                transform: 'translateY(-50%)',
                                height: '100%',
                                display: 'flex',
                                'align-items': 'center',
                            }"
                        >
                            <div
                                v-if="item.value"
                                class="rect-item-value-text"
                                :style="renderOptions.yAxis.valueStyle"
                            >
                                {{ item.value }}
                            </div>

                            <div
                                v-if="item.tip"
                                class="rect-item-value-tip"
                            >
                                {{ item.tip }}
                            </div>
                        </div>
                    </div>
                    
                    <div
                        class="rect-item-value-box"
                        :style="`margin-top: 0;height: ${renderOptions.yAxis.rectStyle.paddingTop || '16pt'}`"
                    ></div>

                    <div
                        class="line-group-wrapper"
                        :style="{
                            height: `calc(100% + ${renderOptions.yAxis.rectStyle.paddingTop})`,
                            top: `-${renderOptions.yAxis.rectStyle.paddingTop}`,
                        }"
                    >
                        <div
                            v-for="(item, idx) in renderOptions.xAxis.items"
                            :key="idx"
                            class="line-item"
                        >
                        </div>
                    </div>
                </div>

                <div class="line-group-text">
                    <span
                        v-for="(item, idx) in renderOptions.xAxis.items"
                        :key="idx"
                        :style="renderOptions.xAxis.labelStyle"
                    >
                        {{ item.label }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'StatisticRect',

        props: {
            // 配置
            options: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            // 渲染配置
            renderOptions() {
                const {
                    xAxis: {
                        labelStyle: xAxisLabelStyle = {},
                        items: xAxisItems = [],
                    } = {},
                    yAxis: {
                        labelStyle: yAxisLabelStyle = {},
                        rectStyle = {},
                        valueStyle = {},
                        items: yAxisItems = [],
                        labelFixedWidth = false,
                    } = {}
                } = this.options;

                return {
                    // 坐标最大值
                    max: this.options.max || 100,
                    // 坐标最小值
                    min: this.options.min || 0,
                    // x 轴选项
                    xAxis: {
                        // 文字颜色
                        labelStyle: {
                            ...xAxisLabelStyle,
                            color: xAxisLabelStyle.color || '#7a8794',
                        },

                        items: xAxisItems,
                    },
                    // y 轴选项
                    yAxis: {
                        labelStyle: {
                            ...yAxisLabelStyle,
                            color: yAxisLabelStyle.color || '#7a8794',
                        },

                        labelFixedWidth,

                        // 矩形样式
                        rectStyle: {
                            ...rectStyle,
                            backgroundColor: rectStyle.backgroundColor || '#89cfa4',
                            height: rectStyle.height || '16pt',
                        },

                        // 值的样式
                        valueStyle: {
                            ...valueStyle,
                            color: valueStyle.color || '#3ccb75',
                        },

                        items: [
                            ...yAxisItems,
                        ].map((item) => {
                            item.width = (item.value / this.options.max) * 100 + '%';
                            return item;
                        }),
                    }
                }
            }
        },
    }
</script>

<style lang="scss">
.statistic-rect-wrapper {
    .rect-list-wrapper {
        display: flex;

        .rect-item-label-wrapper {
            flex-shrink: 0;

            .rect-item-label {
                padding-top: 16pt;
                text-align: right;
                color: #7A8794;
                font-size: 9pt;
                font-weight: 400;
                height: 16pt;
                //line-height: 16pt;
                padding-right: 4pt;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: flex-end;

                .rect-item-label-inner {
                    position: absolute;
                    width: 100%;
                    right: 4pt;
                    top: 0;
                    height: 100%;
                    line-height: inherit;
                    text-align: right;
                }
            }
        }

        .rect-item-value-wrapper {
            flex: 1;

            .rect-item-value-top {
                position: relative;

                .rect-item-value-box {
                    margin-top: 16pt;
                    position: relative;
                    z-index: 1;
                    display: flex;
                    align-items: center;
                    width: 100%;

                    .rect-item-value-inner {
                        height: 16pt;
                        background: #89CFA4;
                    }

                    .rect-item-value-text {
                        color: #3CCB75;
                        font-size: 10pt;
                        line-height: normal;
                        margin: 0 4pt;
                    }

                    .rect-item-value-tip {
                        color: #9E9EAE;
                        font-size: 9pt;
                        line-height: normal;
                        white-space: nowrap;
                    }
                }

                .line-group-wrapper {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: space-between;
                    top: 0;
                    left: 0;
                    z-index: 0;

                    .line-item {
                        width: 1pt;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.12);
                    }
                }
            }

            .line-group-text {
                padding-top: 8pt;
                width: 100%;
                display: flex;
                justify-content: space-between;

                >span {
                    text-align: center;
                    color: #7A8794;
                    font-size: 10pt;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 16pt; /* 160% */
                }
            }
        }

    }
}
</style>