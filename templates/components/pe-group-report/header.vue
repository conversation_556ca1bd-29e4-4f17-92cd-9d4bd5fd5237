<template>
    <div data-type="header">
        <div
            class="pe-group-header-wrapper"
        >
            <div class="header-left">
                <span class="organ-name">
                    {{ coverConfig.institutionName }}
                </span>
                <span class="header-title">
                    健康体检报告
                </span>
            </div>

            <div class="header-right">
                <span>
                    <span class="header-label">体检单号</span>
                    {{ orderNo }}
                </span>
            </div>
        </div>
        <abc-print-space :value="16"></abc-print-space>
    </div>
</template>

<script>
    import AbcPrintSpace from "../layout/space.vue";

    export default {
        name: 'PeGroupReportHeader',
        components: {AbcPrintSpace},

        props: {
            config: {
                type: Object,
                default: () => ({}),
            },
            printData: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            coverConfig() {
                return this.config.cover;
            },

            orderNo() {
                return this.printData.groupOrder?.orderNo || '';
            }
        }
    }
</script>

<style lang="scss">
.pe-group-header-wrapper {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding-bottom: 4pt;
    border-bottom: 0.5pt solid #D7D7D7;
    font-size: 10pt;
    font-weight: 400;

    .header-left {
        display: inline-flex;
        align-items: flex-end;

        .organ-name {
            font-size: 9pt;
            font-weight: 600;
            margin-right: 4pt;
            color: #005ED9;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            max-width: 180pt;
        }

        .header-title {
            font-size: 8pt;
            font-weight: 400;
            color: #000000;
        }
    }

    .header-right {
        flex: 1;
        justify-content: flex-end;
        font-weight: 400;
        display: flex;
        gap: 12pt;
        font-size: 8pt;

        .header-label {
            color: #8B8E98;
            margin-right: 4pt;
        }
    }
}
</style>