<template>
    <div
        ref="statistic-circle"
        class="statistic-circle-wrapper"
    >
    </div>
</template>

<script>
    export default {
        name: 'StatisticCircle',

        props: {
            radius: {
                type: Number,
                default: 60,
            },
            lineWidth: {
                type: Number,
                default: 30,
            },
            list: {
                type: Array,
                default: () => [],
            },
        },

        mounted() {
            this.draw()
        },

        methods: {
            draw() {
                if(!this.list.length) {
                    console.error('no data to render!')
                    return;
                }
                const canvas = document.createElement('canvas');
                // 放大 canvas 画布
                const dpi = window.devicePixelRatio;
                const displayWidth = this.radius * 2 + this.lineWidth * 2;
                canvas.width = dpi * displayWidth;
                canvas.height = dpi * displayWidth;

                const ctx = canvas.getContext('2d');
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                // 放大绘制的图像，确保清晰度
                const radius = this.radius * dpi;
                const lineWidth = this.lineWidth * dpi;

                const drawPercentageCircle = (startAngle = 0, percentage, strokeColor) => {
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, startAngle, startAngle + 2 * Math.PI * percentage);
                    ctx.lineWidth = lineWidth;
                    ctx.strokeStyle = strokeColor || '#ffffff';
                    ctx.stroke();
                    ctx.save();
                }

                // 绘制背景圆环
                drawPercentageCircle(0,1, '#f0f0f0');

                // 按比例绘制每个百分比圆
                let usePercentage = 0;
                for (const item of this.list) {
                    drawPercentageCircle(usePercentage * 2 * Math.PI ,item.percentage, item.color);
                    usePercentage += item.percentage;
                }

                // 放大 canvas 上下文
                ctx.scale(dpi, dpi);

                const image = document.createElement('img');
                image.src = canvas.toDataURL();
                image.style.width = `${displayWidth}px`;
                image.style.height = `${displayWidth}px`;
                this.$refs['statistic-circle'].appendChild(image);
            }
        }
    }
</script>

<style lang="scss">
.statistic-circle-wrapper {

}
</style>