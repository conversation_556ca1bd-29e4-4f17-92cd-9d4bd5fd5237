<template>
    <div
        id="cover-position-mark"
        class="individual-report-cover"
    >
        <div class="individual-report-cover-header">
            <div class="header-left">
                <div
                    v-if="coverConfig.institutionLogoUrl"
                    class="logo-img-wrapper"
                >
                    <img
                        :src="coverConfig.institutionLogoUrl"
                        alt=""
                    />
                </div>

                <span class="organ-name">
                    {{ coverConfig.institutionName || peOrganName }}
                </span>
            </div>

            <div class="secret-sign">
                保密
            </div>
        </div>

        <div class="individual-report-cover-body">
            <div class="individual-report-cover-title">
                <div class="title-chinese">
                    {{ groupReportName }}
                </div>
                <div class="title-chinese">
                    {{ coverConfig.subTitle }}
                </div>
            </div>

            <div
                class="individual-report-cover-info"
            >
                <div
                    v-if="coverConfig.organInfo.name"
                    class="user-info"
                >
                    {{ groupOrganName }}
                </div>

                <div
                    v-for="(item, k) in baseInfo"
                    :key="k"
                    class="info-item"
                >
                    <span>
                        <span
                            v-for="(v, i) in item.label"
                            :key="i"
                        >
                            {{ v }}
                        </span>
                    </span>

                    <span>
                        {{ item.value }}
                    </span>
                </div>
            </div>
        </div>

        <div
            v-if="coverConfig.qrCode && coverConfig.qrCodeUrl"
            class="individual-report-cover-footer-qr"
            data-pendants-index="1"
            data-type="footer"
            style="position: absolute"
        >
            <div class="qr-code-wrapper">
                <img
                    :src="coverConfig.qrCodeUrl"
                    alt=""
                />
            </div>

            <div class="extra-info">
                <div>
                    关注官方公众号
                </div>
                <div>
                    体检预约、查报告
                </div>
                <div>
                    健康咨询电话：
                    <span class="contact-phone-number">
                        {{ coverConfig.institutionContact }}
                    </span>
                </div>

                <div>
                    地址：{{ coverConfig.institutionAddress }}
                </div>
            </div>
        </div>

        <div
            v-else
            class="individual-report-cover-footer"
            data-pendants-index="1"
            data-type="footer"
        >
            <div>
                地址：{{ coverConfig.institutionAddress }}
            </div>

            <div>
                健康咨询电话：
                <span class="contact-phone-number">
                    {{ coverConfig.institutionContact }}
                </span>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'PeGroupReportCover',
        props: {
            config: {
                type: Object,
                default: () => ({}),
            },
            printData: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            coverConfig() {
                return this.config.cover;
            },
            groupOrganName() {
                return this.printData?.peOrgan?.name;
            },
            groupReportName() {
                return this.printData?.groupOrder?.orderName;
            },
            peOrganName() {
                return this.printData?.organPrintView?.name;
            },
            baseInfo() {
                const {
                    organInfo: {
                        contactPhone,
                        address,
                    },
                    institutionContact,
                    institutionAddress,
                } = this.coverConfig;

                const isShow = (v) => v === 1;

                return [
                    {
                        label: '联系电话',
                        value: institutionContact,
                        isShow: isShow(contactPhone),
                    },
                    {
                        label: '地址',
                        value: institutionAddress,
                        isShow: isShow(address),
                    },
                ].filter(v => v.isShow);
            },
        },
    };
</script>

<style lang='scss'>
.individual-report-cover {
    .individual-report-cover-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .header-left {
            display: flex;
            align-items: center;

            .logo-img-wrapper {
                width: 48px;
                font-size: 0;

                img {
                    width: 100%;
                    object-fit: contain;
                }
            }
            .organ-name {
                margin-left: 10pt;
                font-size: 14pt;
                font-weight: 400;
            }
        }

        .secret-sign {
            padding: 4px 8px;
            border-radius: 2px;
            border: 1px solid #FF5C5C;
            color: #FF5C5C;
            font-size: 14pt;
            font-weight: bold;
        }
    }

    .individual-report-cover-body {
        display: flex;
        flex-direction: column;
        align-items: center;

        .individual-report-cover-title {
            padding-top: 104pt;

            .title-chinese {
                text-align: center;
                margin-bottom: 4pt;
                font-weight: 600;
                font-size: 32pt;
                word-spacing: 12pt;
                line-height: 48pt;
            }
        }

        .individual-report-cover-info {
            padding-top: 95pt;
            font-size: 11pt;
            font-weight: 400;
            width: 276pt;

            .user-info {
                font-size: 20pt;
                font-weight: 600;
                padding-bottom: 6pt;
                border-bottom: 0.6px dashed #D7D7D7;
                margin-bottom: 2pt;
            }

            .info-item {
                padding-top: 10pt;
                display: flex;
                align-items: center;
                gap: 10pt;

                >span {
                    padding: 3pt 0;
                }

                >span:first-child {
                    width: 65pt;
                    color: #8B8E98;
                    display: inline-flex;
                    justify-content: space-between;
                    align-items: center;

                    >span:last-child::after {
                        content: '：';
                    }
                }

                >span:last-child {
                    padding-left: 3pt;
                    width: 214pt;
                    border-bottom: 0.6px solid #D7D7D7;
                }
            }
        }
    }

    .individual-report-cover-footer {
        box-sizing: border-box;
        width: 100%;
        display: flex;
        justify-content: center;
        gap: 8pt;
        border-top: 0.6px solid #D7D7D7;
        padding: 4pt 0 0;
        font-size: 9pt;
        font-weight: 400;
        line-height: 12pt;
        color: #8B8E98;
        position: absolute;
        bottom: 0;
        left: 0;

        .contact-phone-number {
            color: #000000;
            font-weight: 600;
        }
    }

    .individual-report-cover-footer-qr {
        display: flex;
        justify-content: center;
        gap: 16pt;
        align-items: center;
        width: 279pt;
        left: 50%;
        position: relative;
        transform: translateX(-50%);


        .qr-code-wrapper {
            width: 84pt;
            height: 84pt;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }

        .extra-info {
            color: #8B8E98;
            font-size: 9pt;
            flex: 1;

            >div:nth-child(1) {
                margin-bottom: 3pt;
            }

            >div:nth-child(2) {
                font-size: 12pt;
                color: #000000;
                padding-bottom: 3pt;
                border-bottom: 0.6pt solid #d7d7d7;
                margin-bottom: 6pt;
            }

            >div:nth-child(3) {
                .contact-phone-number {
                    color: #000000;
                    font-weight: 600;
                }
            }
        }
    }
}
</style>