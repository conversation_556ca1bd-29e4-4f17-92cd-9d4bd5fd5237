<template>
    <table class="pe-group-table">
        <tr
            v-for="(row, rowIndex) in list"
            :key="rowIndex"
        >
            <td
                v-for="(col, colIndex) in row"
                :key="colIndex"
                :style="col.style"
                :rowspan="col.rowSpan"
                :colspan="col.colSpan"
            >
                <div>
                    {{ col.value }}
                </div>
            </td>
        </tr>
    </table>
</template>

<script>
    export default {
        name: 'PeGroupTable',

        props: {
            list: {
                type: Array,
                default: () => ([ ]),
            },
        },
    }
</script>

<style lang="scss">
.pe-group-table {
    width: calc(100% + 8pt);
    border-spacing: 4pt 2pt;
    border-collapse: separate;
    margin-left: -4pt;
}
</style>