import {EXAM_TYPE, INSPECT_DEVICE_TYPE_TEXT} from "../../../constant/print-constant";
import {formatAge} from "../../../common/utils";
import {
    EXAMINATION_ABNORMAL_TAG_ENUM,
    EXAMINATION_ABNORMAL_TAG_ENUM_COLOR,
    EXAMINATION_ABNORMAL_TAG_ENUM_TEXT
} from "../constant";

export default {
    computed: {
        abnormalDetailList() {
            const {
                abnormalDetails = []
            } = this.printData;

            const getExamTypeName = (item) => {
                if(item.type === EXAM_TYPE.examination) {
                    return '检验异常明细'
                }
                const flag = INSPECT_DEVICE_TYPE_TEXT[item.deviceType].endsWith('检查');
                return INSPECT_DEVICE_TYPE_TEXT[item.deviceType] + (flag ? '异常明细' : '检查异常明细');
            }

            const generateTableList = (items, type, abnormalContent) => {
                return items.map((l) => {
                    const _item = {
                        name: l.patient?.name,
                        age: formatAge(l.patient?.age),
                        sex: l.patient?.sex,
                        value: abnormalContent,
                    };
                    if (type === EXAM_TYPE.inspect) {
                        return _item;
                    } else {
                        return {
                            ..._item,
                            ...(l.results?.[0] || {}),
                            name: l.patient?.name,
                        };
                    }
                });

            }

            return abnormalDetails.map((d) => ({
                title: getExamTypeName(d),
                groupList: (d.entryItems || []).map((item) => ({
                    name: item.name,
                    count: item.count,
                    percentage: `${item.ratio}%`,
                    type: d.type,
                    tagName: EXAMINATION_ABNORMAL_TAG_ENUM_TEXT[item.tag],
                    tagColor: EXAMINATION_ABNORMAL_TAG_ENUM_COLOR[item.tag],
                    tableList: generateTableList(item.peSheets || [], d.type, item.name),
                }))
            }));
        }
    }
}