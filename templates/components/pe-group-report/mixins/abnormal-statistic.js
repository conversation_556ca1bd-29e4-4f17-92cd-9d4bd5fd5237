import {EXAMINATION_ABNORMAL_TAG_ENUM, EXAMINATION_ABNORMAL_TAG_ENUM_TEXT} from "../constant";

export default {
    computed: {
        abnormalStatisticOptionList() {
            const {
                abnormalStats = []
            } = this.printData;

            let max = Math.max(...abnormalStats.map((a) => a.count));
            max = max % 5 === 0 ? max : max + 5 - max % 5;

            const generateXAxisItems = (max) => {
                const items = [];
                const step = max / 5;
                for(let i = 0; i <= 5; i++) {
                    items.push({ label: step * i });
                }
                return items;
            }

            const generateYAxisItems = (items) => {
                const getAbnormalText = (item) =>
                    item.tag === EXAMINATION_ABNORMAL_TAG_ENUM.textAbnormal ? (item.tagText || '') :  EXAMINATION_ABNORMAL_TAG_ENUM_TEXT[item.tag];

                const _items = items.map(item => ({
                    label: item.name + getAbnormalText(item),
                    value: item.count,
                    tip: `检出率${item.ratio}%`,
                }));

                const res = [];

                const itemPageSize = 24;
                while (_items.length > 0) {
                    res.push(_items.splice(0, itemPageSize));
                }

                return res;
            }

            const xAxisItems = generateXAxisItems(max);
            const yAxisItems = generateYAxisItems(abnormalStats);

            return yAxisItems.map(((items) => ({
                min: 0,
                max,
                xAxis: {
                    labelStyle: {
                        color: '#7A8794',
                        fontSize: '10pt',
                        fontWeight: 400,
                    },

                    items: xAxisItems,
                },
                yAxis: {
                    labelStyle: {
                        color: '#000000',
                        fontSize: '9pt',
                        fontWeight: 400,
                        width: '90pt',
                    },

                    labelFixedWidth: true,

                    rectStyle: {
                        backgroundColor: '#F93',
                        height: '20pt',
                        paddingTop: '6pt',
                    },

                    valueStyle: {
                        color: '#FF8B16',
                        fontSize: '10pt',
                        fontWeight: 600,
                    },

                    items,
                }
            })))
        },
    }
}