<template>
    <div class="pe-group-report-guide-wrapper">
        <p
            id="guide-position-mark"
            class="greet"
        >
            尊敬的
            <span class="patient-name">{{ groupOrganName }}</span>
        </p>

        <abc-print-space :value="12"></abc-print-space>

        <div class="text-1">
            贵单位于{{ date.start }}至{{ date.end }}在{{ coverConfig.institutionName || peOrganName }}进行健康体检，现将团体检查情况
        </div>
        <div class="text-1">
            报告如下：
        </div>

        <abc-print-space :value="12"></abc-print-space>
        <abc-html
            class="introduction-content"
            :value="introduction"
        >
        </abc-html>

        <p class="explain">
            报告说明：本报告仅作临床参考，不作任何证明依据
        </p>
    </div>
</template>

<script>
    import AbcHtml from "../layout/abc-html.vue";
    import AbcPrintSpace from "../layout/space.vue";

    export default {
        name: 'PeGroupReportGuide',

        components: {
            AbcPrintSpace,
            AbcHtml
        },

        props: {
            config: {
                type: Object,
                default: () => ({}),
            },
            printData: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            coverConfig() {
                return this.config.cover;
            },

            introduction() {
                return this.config?.introduction?.content || '';
            },

            groupOrganName() {
                return this.printData?.peOrgan?.name;
            },

            peOrganName() {
                return this.printData?.organPrintView?.name;
            },

            date() {
                const { beginDate, endDate } = this.printData.groupOrder;

                return {
                    start: this.formatDate(beginDate),
                    end: this.formatDate(endDate),
                }
            }
        },

        methods: {
            formatDate(date) {
                const _date = new Date(date);
                const y = _date.getFullYear();
                const m = _date.getMonth();
                const d = _date.getDate();
                return `${y}年${m + 1}月${d}日`
            }
        }
    }
</script>

<style lang="scss">
.pe-group-report-guide-wrapper {
    .text-1 {
        color: #37393F;
        font-size: 10pt;
        font-weight: 600;
        line-height: 18pt;
    }

    .greet {
        font-size: 12pt;
        font-weight: 600;

        .patient-name {
            margin: 0 2pt;
            color: #FF5C00;
        }
    }

    .introduction {
        color: #37393F;
        font-size: 10pt;
        font-style: normal;
        font-weight: 600;
        line-height: 18pt; /* 180% */
    }

    .introduction-content {
        font-size: 10pt;
        font-weight: 400;
        line-height: 18pt;
        color: #37393F;
    }

    .explain {
        padding-top: 12pt;
        font-size: 10pt;
        font-weight: 600;
        line-height: 18pt;
        padding-bottom: 12pt;
        border-bottom: 0.8pt dashed #D7D7D7;
    }
}
</style>