<template>
    <result-table
        :list="dataSource"
        :columns="renderColumns"
        class="pe-group-exam-table"
        fixed-height
    ></result-table>
</template>

<script>
    import ResultTable from "../pe-individual-report/inspect-report/result-table.vue";
    import {EXAM_TYPE} from "../../constant/print-constant";
    import NatureDisplay from "../nature-display/index.vue";
    import {calRange} from "../../common/medical-transformat";
    import {REFS_ENUM} from "../../common/constants";

    export default {
        name: 'ExamDetailTable',

        components: {
            ResultTable,
        },

        props: {
            type: {
                type: [Number, String],
                default: 2,
            },

            dataSource: {
                type: Array,
                default: () => ([]),
            }
        },

        computed: {
            inspectColumns() {
                return [
                    {
                        label: '姓名',
                        key: 'name',
                    },
                    {
                        label: '性别',
                        key: 'sex',
                    },
                    {
                        label: '年龄',
                        key: 'age',
                    },
                    {
                        label: '检查结果',
                        key: 'value',
                    },
                ];
            },

            examinationColumns() {
                const row = this.dataSource[0];
                const isYinYangReferenceType = row.type === REFS_ENUM.YIN_YANG;
                return [
                    {
                        label: '姓名',
                        key: 'name',
                        width: 2,
                    },
                    {
                        label: '性别',
                        key: 'sex',
                    },
                    {
                        label: '年龄',
                        key: 'age',
                    },
                    {
                        label: '检查结果',
                        key: 'value',
                        render: (createElement, row) => {
                            if(row.valueType !== 'IMAGE') {
                                const isYinYangReferenceType = row.type === REFS_ENUM.YIN_YANG;
                                return (
                                    createElement(
                                        'div',
                                        {
                                            style: {
                                                color: isYinYangReferenceType ? '#FF0000' : '#000000',
                                                fontWeight: isYinYangReferenceType ? '600' : 'normal',
                                            }
                                        },
                                        row.value,
                                    )
                                )
                            }

                            return '';
                        },
                    },
                    isYinYangReferenceType ? null : {
                        label: '单位',
                        key: 'unit',
                    },
                    {
                        label: '异常提示',
                        render: (createElement, row) => {

                            if(row.valueType !== 'IMAGE') {
                                const isYinYangReferenceType = row.type === REFS_ENUM.YIN_YANG;
                                if(isYinYangReferenceType) {
                                    return createElement(
                                        'div',
                                        {
                                            style: {
                                                color: '#FF0000',
                                                fontWeight: '600',
                                            }
                                        },
                                        row.value,
                                    )
                                }
                                return createElement(
                                    'div',
                                    null,
                                    [
                                        createElement(
                                            NatureDisplay,
                                            {
                                                props: {
                                                    item: row,
                                                    needColor: true,
                                                },
                                            }
                                        ),
                                    ],
                                )
                            }

                            return '';
                        },
                    },
                    {
                        label: '参考范围',
                        render: (createElement, row) => {
                            return createElement(
                                'div',
                                {
                                    style: {
                                        'word-break': 'break-word',
                                        'white-space': 'normal',
                                    }
                                },
                                calRange(row),
                            )
                        },
                        width: 2,
                    },
                ].filter(item => !!item);
            },

            renderColumns() {
                return this.type === EXAM_TYPE.inspect ? this.inspectColumns : this.examinationColumns;
            }
        }
    }
</script>

<style lang="scss">
.pe-group-exam-table {
    border-spacing: 4pt 2px;

    .report-result-table-cell{
        padding: 4pt 10pt 4pt 12pt !important;

        &.table-header {
            white-space: nowrap;
            height: auto !important;
            padding: 3pt 10pt 3pt 12pt !important;
        }
    }
}
</style>