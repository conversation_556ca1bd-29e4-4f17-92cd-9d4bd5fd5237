<template>
    <div class="pe-group-report-age-stage-wrapper">
        <template v-if="isShowAgeStage">
            <div
                id="age-stage-position-mark"
                class="age-title"
            >
                年龄阶段分布情况
            </div>

            <abc-print-space :value="16"></abc-print-space>

            <pe-group-table :list="ageStageTableList"></pe-group-table>

            <abc-print-space :value="40"></abc-print-space>

            <div class="histogram-wrapper">
                <div class="histogram-item">
                    <div class="title">
                        总分布情况
                    </div>

                    <abc-print-space :value="20"></abc-print-space>

                    <statistic-rect :options="totalDistributionOption"></statistic-rect>
                </div>

                <div class="histogram-item">
                    <div class="title">
                        男性分布情况
                    </div>

                    <abc-print-space :value="20"></abc-print-space>

                    <statistic-rect :options="maleDistributionOption"></statistic-rect>
                </div>

                <div class="histogram-item">
                    <div class="title">
                        女性分布情况
                    </div>

                    <abc-print-space :value="20"></abc-print-space>

                    <statistic-rect :options="femaleDistributionOption"></statistic-rect>
                </div>
            </div>

            <abc-print-space :value="40"></abc-print-space>
        </template>

        <template v-if="isShowSignSituation">
            <div
                id="exam-status-position-mark"
                class="age-title"
            >
                到检情况
            </div>

            <abc-print-space :value="16"></abc-print-space>

            <pe-group-table :list="signInTableList"></pe-group-table>
        </template>
    </div>
</template>

<script>
    import AbcPrintSpace from "../layout/space.vue";
    import PeGroupTable from "./components/table.vue";
    import StatisticRect from "./statistic-rect.vue";
    import clone from "../../common/clone";
    
    const originItems = [
        { min: 0, max: 17, label: '18岁以下', value: 0 },
        { min: 18, max: 29, label: '18～29岁', value: 0 },
        { min: 30, max: 39, label: '30～39岁', value: 0 },
        { min: 40, max: 49, label: '40～49岁', value: 0 },
        { min: 50, max: null, label: '50岁以上', value: 0 },
    ];

    export default {
        name: 'PeGroupAgeStage',

        components: {
            StatisticRect,
            PeGroupTable,
            AbcPrintSpace
        },

        props: {
            printData: {
                type: Object,
                default: () => ({}),
            },
            isShowAgeStage: Boolean,
            isShowSignSituation: Boolean,
        },

        computed: {
            ageStageTableList() {
                const {
                    statAbstract:{
                        ageStageStats = []
                    } = {}
                } = this.printData;

                const getAgeStageDisplayText = (min,max) => {
                    if(min && max) {
                        return `${min}~${max}岁`
                    }
                    if(min) {
                        return `${min}岁以上`
                    }
                    if(max) {
                        return  `${max + 1}岁以下`
                    }

                    return ''
                }

                const getPercentageDisplay = (num) => `${num}%`;

                const dataCellList = ageStageStats.map((item, idx) => {
                    const cellStyle = {
                        height: '20pt',
                        background: idx % 2 === 0 ? '#ffffff' : '#F0F0F0',
                        color: '#000000',
                        fontSize: '10pt',
                        fontWeight: 400,
                        textAlign: 'center'
                    };

                    return [
                        {
                            value: getAgeStageDisplayText(item.minAge, item.maxAge),
                            style: cellStyle,
                        },
                        {
                            value: item.total,
                            style: cellStyle,
                        },
                        {
                            value: getPercentageDisplay(item.ratio),
                            style: cellStyle,
                        },
                        {
                            value: item.maleCount,
                            style: cellStyle,
                        },
                        {
                            value: getPercentageDisplay(item.maleRatio),
                            style: cellStyle,
                        },
                        {
                            value: item.femaleCount,
                            style: cellStyle,
                        },
                        {
                            value: getPercentageDisplay(item.femaleRatio),
                            style: cellStyle,
                        },
                    ]
                });

                return [
                    [
                        {
                            value: '年龄段',
                            rowSpan: 2,
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 600,
                            }
                        },
                        {
                            value: '合计',
                            colSpan: 2,
                            style: {
                                textAlign: 'center',
                                background: '#89CFA4',
                                color: '#ffffff',
                                height: '20pt',
                                fontSize: '10pt',
                                fontWeight: 600,
                            }
                        },
                        {
                            value: '男性',
                            colSpan: 2,
                            style: {
                                textAlign: 'center',
                                background: '#7AC5FF',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 600,
                            }
                        },
                        {
                            value: '女性',
                            colSpan: 2,
                            style: {
                                textAlign: 'center',
                                background: '#FF95B0',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 600,
                            }
                        },
                    ],
                    [
                        {
                            value: '人数',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                height: '16pt',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '比例',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '人数',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '比例',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '人数',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '比例',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                    ],
                    ...dataCellList,
                ]
            },

            totalDistributionOption() {
                const {
                    statAbstract:{
                        ageStageStats = []
                    } = {}
                } = this.printData;

                return {
                    min: 0,
                    max: 50,
                    xAxis: {
                        labelStyle: {
                            color: '#7A8794',
                            fontSize: '10pt',
                            fontWeight: 400,
                        },

                        items: [
                            { label: 0 },
                            { label: 10 },
                            { label: 20 },
                            { label: 30 },
                            { label: 40 },
                            { label: 50 },
                        ],
                    },
                    yAxis: {
                        labelStyle: {
                            color: '#7A8794',
                            fontSize: '9pt',
                            fontWeight: 400,
                        },

                        rectStyle: {
                            backgroundColor: '#89CFA4',
                            height: '16pt',
                            paddingTop: '16pt',
                        },

                        valueStyle: {
                            color: '#3CCB75',
                            fontSize: '10pt',
                            fontWeight: 600,
                        },

                        items: clone(originItems).map((o) => {
                            o.value = ageStageStats.find((a) => {
                                return a.minAge === o.min && a.maxAge === o.max;
                            })?.total || 0;

                            return o;
                        }),
                    }
                }
            },

            maleDistributionOption() {
                const {
                    statAbstract:{
                        ageStageStats = []
                    } = {}
                } = this.printData;

                return {
                    min: 0,
                    max: 50,
                    xAxis: {
                        labelStyle: {
                            color: '#7A8794',
                            fontSize: '10pt',
                            fontWeight: 400,
                        },

                        items: [
                            { label: 0 },
                            { label: 10 },
                            { label: 20 },
                            { label: 30 },
                            { label: 40 },
                            { label: 50 },
                        ],
                    },
                    yAxis: {
                        labelStyle: {
                            color: '#7A8794',
                            fontSize: '9pt',
                            fontWeight: 400,
                        },

                        rectStyle: {
                            backgroundColor: '#7AC5FF',
                            height: '16pt',
                            paddingTop: '16pt',
                        },

                        valueStyle: {
                            color: '#7AC5FF',
                            fontSize: '10pt',
                            fontWeight: 600,
                        },

                        items: clone(originItems).map((o) => {
                            o.value = ageStageStats.find((a) => {
                                return a.minAge === o.min && a.maxAge === o.max;
                            })?.maleCount || 0;

                            return o;
                        }),
                    }
                }
            },

            femaleDistributionOption() {
                const {
                    statAbstract:{
                        ageStageStats = []
                    } = {}
                } = this.printData;

                return {
                    min: 0,
                    max: 50,
                    xAxis: {
                        labelStyle: {
                            color: '#7A8794',
                            fontSize: '10pt',
                            fontWeight: 400,
                        },

                        items: [
                            { label: 0 },
                            { label: 10 },
                            { label: 20 },
                            { label: 30 },
                            { label: 40 },
                            { label: 50 },
                        ],
                    },
                    yAxis: {
                        labelStyle: {
                            color: '#7A8794',
                            fontSize: '9pt',
                            fontWeight: 400,
                        },

                        rectStyle: {
                            backgroundColor: '#FF95B0',
                            height: '16pt',
                            paddingTop: '16pt',
                        },

                        valueStyle: {
                            color: '#FF95B0',
                            fontSize: '10pt',
                            fontWeight: 600,
                        },

                        items: clone(originItems).map((o) => {
                            o.value = ageStageStats.find((a) => {
                                return a.minAge === o.min && a.maxAge === o.max;
                            })?.femaleCount || 0;

                            return o;
                        }),
                    }
                }
            },

            signInTableList() {
                const {
                    statAbstract = {}
                } = this.printData;

                return [
                    [
                        {
                            value: '登记人数',
                            colSpan: 3,
                            style: {
                                textAlign: 'center',
                                background: '#69AFFD',
                                color: '#ffffff',
                                fontSize: '10pt',
                                height: '24pt',
                                fontWeight: 600,
                            }
                        },
                        {
                            value: '到检人数',
                            colSpan: 3,
                            style: {
                                textAlign: 'center',
                                background: '#63C78B',
                                color: '#ffffff',
                                height: '24pt',
                                fontSize: '10pt',
                                fontWeight: 600,
                            }
                        },
                        {
                            value: '到检率',
                            rowSpan: 2,
                            style: {
                                textAlign: 'center',
                                background: '#FF8F1F',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 600,
                            }
                        },
                    ],
                    [
                        {
                            value: '合计',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                height: '24pt',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '男性',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '女性',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '合计',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                height: '24pt',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '男性',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                        {
                            value: '女性',
                            style: {
                                textAlign: 'center',
                                background: '#A4A8B6',
                                color: '#ffffff',
                                fontSize: '10pt',
                                fontWeight: 400,
                                width: '12.5%',
                            }
                        },
                    ],
                    [
                        {
                            value: statAbstract.total,
                            style: {
                                textAlign: 'center',
                                background: '#F0F0F0',
                                color: '#000000',
                                fontSize: '10pt',
                                height: '24pt',
                                fontWeight: 400,
                            }
                        },
                        {
                            value: statAbstract.maleTotal,
                            style: {
                                textAlign: 'center',
                                background: '#F0F0F0',
                                color: '#000000',
                                fontSize: '10pt',
                                height: '24pt',
                                fontWeight: 400,
                            }
                        },
                        {
                            value: statAbstract.femaleTotal,
                            style: {
                                textAlign: 'center',
                                background: '#F0F0F0',
                                color: '#000000',
                                fontSize: '10pt',
                                height: '24pt',
                                fontWeight: 400,
                            }
                        },
                        {
                            value: statAbstract.examinedCount,
                            style: {
                                textAlign: 'center',
                                background: '#F0F0F0',
                                color: '#000000',
                                fontSize: '10pt',
                                height: '24pt',
                                fontWeight: 400,
                            }
                        },
                        {
                            value: statAbstract.maleExaminedCount,
                            style: {
                                textAlign: 'center',
                                background: '#F0F0F0',
                                color: '#000000',
                                fontSize: '10pt',
                                height: '24pt',
                                fontWeight: 400,
                            }
                        },
                        {
                            value: statAbstract.femaleExaminedCount,
                            style: {
                                textAlign: 'center',
                                background: '#F0F0F0',
                                color: '#000000',
                                fontSize: '10pt',
                                height: '24pt',
                                fontWeight: 400,
                            }
                        },
                        {
                            value: statAbstract.examinedRatio + '%',
                            style: {
                                textAlign: 'center',
                                background: '#F0F0F0',
                                color: '#000000',
                                fontSize: '10pt',
                                height: '24pt',
                                fontWeight: 400,
                            }
                        },
                    ]
                ]
            },
        },
    }
</script>

<style lang="scss">
.pe-group-report-age-stage-wrapper {
    .age-title {
        text-align: center;
        color: #37393F;
        font-size: 16pt;
        font-weight: 600;
        line-height: 18pt; /* 112.5% */
    }

    .histogram-wrapper {
        display: flex;
        gap: 22pt;
        width: 100%;

        .title {
            color: #37393F;
            text-align: center;
            font-size: 14pt;
            font-weight: 600;
            line-height: 18pt; /* 128.571% */
        }

        .histogram-item {
            text-align: center;
            flex: 1;
        }
    }
}
</style>