<template>
    <div data-type="footer">
        <abc-print-space :value="6"></abc-print-space>
        <div
            class="pe-group-report-footer-wrapper"
        >
            <div class="organ-info">
                <span>
                    地址：{{ coverConfig.institutionAddress }}
                </span>

                <span>
                    健康咨询电话：
                    <span style="color: #000000">
                        {{ coverConfig.institutionContact }}
                    </span>
                </span>
            </div>
            <div class="pagination">
                <span
                    data-page-no="PageNo"
                    style="color: #000000"
                ></span>
                /
                <span data-page-count="PageCount"></span>
            </div>
        </div>
    </div>
</template>

<script>
    import AbcPrintSpace from "../layout/space.vue";

    export default {
        name: 'PeGroupReportFooter',
        components: {AbcPrintSpace},

        props: {
            config: {
                type: Object,
                default: () => ({}),
            },
            printData: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            coverConfig() {
                return this.config.cover;
            }
        }
    }
</script>

<style lang="scss">
.pe-group-report-footer-wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border-top: 0.6px solid #D7D7D7;
    padding-top: 4pt;
    line-height: 11pt;
    font-weight: 400;

    .organ-info {
        color: #8B8E98;
        font-size: 8pt;

        span:first-child{
            margin-right: 8pt;
        }
    }

    .pagination {
        width: 56pt;
        font-size: 8pt;
        color: #8B8E98;
        text-align: right;
    }
}
</style>