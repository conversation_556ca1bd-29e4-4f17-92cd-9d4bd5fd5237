<template>
    <div class="pe-group-report-summary">
        <div
            id="summary-position-mark"
            class="title-1"
        >
            概述
        </div>

        <div class="summary-content-wrapper">
            <div
                v-for="(item, idx) in summaryList"
                :key="idx"
                class="summary-content-item"
            >
                <div>
                    {{ item.value }}
                </div>
                <div>
                    {{ item.label }}
                </div>
            </div>
        </div>

        <abc-print-space :value="40"></abc-print-space>

        <div class="abnormal-statistic-img">
            <div class="abnormal-statistic-img-item">
                <div class="left">
                    <div class="title">
                        异常占比
                    </div>

                    <abc-print-space :value="30"></abc-print-space>

                    <statistic-circle
                        :list="[
                            {
                                percentage: totalAbnormal.abnormalCount / totalAbnormal.total,
                                color: '#FF870F'
                            },
                            {
                                percentage: totalAbnormal.normalCount / totalAbnormal.total,
                                color: '#ABFCCB'
                            },
                        ]"
                    ></statistic-circle>

                    <abc-print-space :value="30"></abc-print-space>

                    <div class="desc-info">
                        <div>
                            <span style="font-weight: 400;color: #000000">总异常率：</span>
                            <span>
                                {{ totalAbnormal.abnormalRatio }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="right">
                    <div>
                        <span
                            class="tag"
                            style="background: #ABFCCB;"
                        >
                            正常
                        </span>
                        <span class="text">{{ totalAbnormal.normalCount }}人</span>
                    </div>
                    <abc-print-space :value="12"></abc-print-space>
                    <div>
                        <span
                            class="tag"
                            style="background: #FF870F"
                        >
                            异常
                        </span>
                        <span class="text">{{ totalAbnormal.abnormalCount }}人</span>
                    </div>
                    <abc-print-space :value="26"></abc-print-space>
                </div>
            </div>

            <div class="abnormal-statistic-img-item">
                <div class="left">
                    <div class="title">
                        男女异常率
                    </div>

                    <abc-print-space :value="30"></abc-print-space>

                    <statistic-circle
                        :list="[
                            {
                                percentage: abnormalByGender.maleCount / abnormalByGender.total,
                                color: '#58A0FF'
                            },
                            {
                                percentage: abnormalByGender.femaleCount / abnormalByGender.total,
                                color: '#F5B6C6'
                            },
                        ]"
                    ></statistic-circle>

                    <abc-print-space :value="30"></abc-print-space>

                    <div class="desc-info">
                        <div>
                            <div style="text-align: left">
                                <span style="font-weight: 400;color: #000000">男性异常率：</span>
                                <span class="number">{{ abnormalByGender.maleRatio }}</span>
                            </div>

                            <abc-print-space :value="6"></abc-print-space>

                            <div style="text-align: left">
                                <span style="font-weight: 400;color: #000000">女性异常率：</span>
                                <span class="number">{{ abnormalByGender.femaleRatio }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="right">
                    <div>
                        <span
                            class="tag"
                            style="background: #58A0FF"
                        >
                            男性
                        </span>
                        <span class="text">{{ abnormalByGender.maleCount }}人</span>
                    </div>
                    <abc-print-space :value="12"></abc-print-space>
                    <div>
                        <span
                            class="tag"
                            style="background: #F5B6C6;"
                        >
                            女性
                        </span>
                        <span class="text">{{ abnormalByGender.femaleCount }}人</span>
                    </div>
                    <abc-print-space :value="26"></abc-print-space>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import StatisticCircle from "./statistic-circle.vue";
    import AbcPrintSpace from "../layout/space.vue";

    export default {
        name: 'PeGroupSummary',

        components: {
            AbcPrintSpace,
            StatisticCircle
        },

        props: {
            printData: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            summaryList() {
                const {
                    statAbstract = {}
                } = this.printData;

                return [
                    {
                        label: '预计体检人数',
                        value: statAbstract.total || 0,
                    },
                    {
                        label: '实际体检人数',
                        value: statAbstract.examinedCount || 0,
                    },
                    {
                        label: '受检率',
                        value: statAbstract.examinedRatio ? `${statAbstract.examinedRatio}%` : 0,
                    },
                ]
            },

            totalAbnormal() {
                const {
                    statAbstract = {}
                } = this.printData;

                return {
                    normalCount: statAbstract.normalCount,
                    abnormalCount: statAbstract.abnormalCount,
                    abnormalRatio: `${statAbstract.abnormalRatio}%`,
                    total: ((statAbstract.normalCount || 0) + (statAbstract.abnormalCount || 0)) || 1,
                }
            },

            abnormalByGender() {
                const {
                    statAbstract = {}
                } = this.printData;

                return {
                    maleCount: statAbstract.maleAbnormalCount,
                    femaleCount: statAbstract.femaleAbnormalCount,
                    maleRatio: `${statAbstract.maleAbnormalRatio}%`,
                    femaleRatio: `${statAbstract.femaleAbnormalRatio}%`,
                    total: ((statAbstract.maleAbnormalCount || 0) + (statAbstract.femaleAbnormalCount || 0)) || 1,
                }
            }
        },
    }
</script>

<style lang="scss">
.pe-group-report-summary {
    .title-1 {
        color: #37393F;
        font-size: 16pt;
        font-weight: 600;
        line-height: 18pt;
        text-align: center;
    }

    .summary-content-wrapper {
        display: flex;

        .summary-content-item {
            width: 33.33%;
            height: 153pt;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            >div:first-child {
                color: #005CD7;
                font-size: 36pt;
                font-weight: 600;
                text-align: center;
            }

            >div:last-child {
                color: #8B8E98;
                font-size: 14pt;
                font-weight: 400;
                padding-top: 4pt;
                text-align: center;
            }
        }
    }

    .abnormal-statistic {
        color: #37393F;
        font-size: 14pt;
        line-height: 18pt;
        padding-bottom: 12pt;
        border-bottom: 1px dashed #D7D7D7;
    }

    .abnormal-statistic-img {
        display: flex;
        justify-content: space-between;

        .abnormal-statistic-img-item {
            display: flex;
            gap: 18pt;

            .left {
                text-align: center;

                .title {
                    color: #000000;
                    text-align: center;
                    font-size: 12pt;
                    font-weight: 400;
                    line-height: 16pt; /* 133.333% */
                }

                .desc-info {
                    color: #8B8E98;
                    font-size: 12pt;
                    font-weight: 600;
                    line-height: 16pt;
                    display: flex;
                    justify-content: center;
                }
            }

            .right {
                display: flex;
                justify-content: center;
                flex-direction: column;

                >div {
                    display: flex;
                    gap: 8pt;
                    align-items: center;

                    .tag {
                        padding: 3pt;
                        border-radius: 3pt;
                        color: #ffffff;
                        font-size: 10pt;
                        font-weight: 600;
                        line-height: 10pt;
                    }

                    .text {
                        color:#000000;
                        text-align: center;
                        font-size: 14pt;
                        font-weight: 600;
                        line-height: 22pt; /* 157.143% */
                    }
                }
            }
        }
    }
}
</style>