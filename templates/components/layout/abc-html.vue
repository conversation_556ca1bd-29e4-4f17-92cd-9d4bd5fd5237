<template>
    <div 
        class="abc-html-wrapper"
        v-html="transformValue(value)"
    >
    </div>
</template>

<script>
    export default {
        name: 'AbcHtml',

        props: {
            value: {
                type: String,
                default: '',
            },
        },

        methods: {
            transformValue(text) {
                if (!text) return '';
                // 将换行符替换为 HTML 的 `<br>` 标签
                return text.replace(/ /g, '&nbsp;').replace(/\n/g, '<br>');
            },
        },
    }
</script>

<style lang='scss'>
.abc-html-wrapper {
    white-space: pre-line;
    word-break: break-word;
}
</style>