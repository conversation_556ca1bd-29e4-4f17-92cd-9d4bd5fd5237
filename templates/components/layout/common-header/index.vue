<template>
    <div class="print-common-header">
        <div class="print-common-header-wrapper">
            <!--logo-->
            <div v-if="showLogo && logo" class="print-common-header-logo">
                <abc-print-image :value="logo"></abc-print-image>
            </div>

            <!--标题-->
            <div class="print-common-header-title">
                <div class="organ-name-wrapper">
                    <div class="organ-name-first-line">
                        {{ renderOrganName.renderFirstLineName }}
                    </div>
                    <div class="organ-name-second-line">
                        {{ renderOrganName.renderSecondLineName }}
                    </div>
                </div>

                <div v-if="showTitle" class="report-title">
                    {{ title }}
                </div>
            </div>

            <!--条码-->
            <div v-if="showBarcode && barcode" class="print-common-header-barcode">
                <abc-print-barcode :value="barcode" show-code></abc-print-barcode>
            </div>
        </div>

        <abc-print-space :value="16"></abc-print-space>

        <div class="print-common-header-field-list">
            <abc-print-row>
                <abc-print-col
                    v-for="(field, idx) in filedList"
                    :key="idx"
                    :style="field.style || {}"
                    :span="field.span"
                >
                    <abc-print-text size="normal" weight="normal">
                        {{ field.label }}：
                    </abc-print-text>

                    <abc-print-text size="normal" weight="normal" :style="field.valueStyle || {}">
                        {{ field.value }}
                    </abc-print-text>
                </abc-print-col>
            </abc-print-row>
        </div>

        <abc-print-space :value="8"></abc-print-space>
    </div>
</template>

<script>
    /**
     * 打印公用页头，包含logo、条码、标题、展示字段部分
     */
    import AbcPrintImage from '../abc-print-image.vue'
    import AbcPrintBarcode from '../abc-print-barcode.vue'
    import { getRenderOrganName } from '../../../common/utils'
    import AbcPrintRow from '../abc-layout/abc-row.vue'
    import AbcPrintCol from '../abc-layout/abc-col.vue'
    import AbcPrintSpace from '../space.vue'
    import AbcPrintText from '../abc-print-text/index.vue'
    import AbcPrintSplit from '../abc-print-split/index.vue'

    export default {
        name: 'PrintCommonHeader',

        components: {
            AbcPrintSplit,
            AbcPrintText,
            AbcPrintSpace,
            AbcPrintCol,
            AbcPrintRow,
            AbcPrintBarcode,
            AbcPrintImage
        },

        props: {
            // 头部配置，处理机构名称
            headerConfig: {
                type: Object,
                default: () => ({})
            },
            organName: {
                type: String,
                default: ''
            },
            logo: {
                type: String,
                default: ''
            },
            // 打印单子的标题，比如检查检验单
            title: {
                type: String,
                default: ''
            },
            barcode: {
                type: String,
                default: ''
            },
            // 页眉字段列表
            filedList: {
                type: Array,
                default: () => []
            },
            showLogo: Boolean,
            showTitle: Boolean,
            showBarcode: Boolean
        },

        computed: {
            renderOrganName() {
                return getRenderOrganName({
                    printOrganName: this.headerConfig.title,
                    printOrganSubName: this.headerConfig.subtitle,
                    organName: this.organName
                })
            }
        }
    }
</script>

<style lang="scss">
    .print-common-header {
        border-bottom: 1px solid #a6a6a6;

        .print-common-header-wrapper {
            padding: 0 90pt;

            .print-common-header-logo {
                position: absolute;
                left: 0;
                top: 0;
                width: 90pt;
                height: 40pt;
            }

            .print-common-header-title {
                width: 100%;
                min-height: 40pt;
                text-align: center;

                .organ-name-wrapper {
                    padding-bottom: 8pt;
                    color: #000000;
                    font-size: 20pt;
                    font-style: normal;
                    font-weight: 900;
                    line-height: 24pt; /* 120% */
                    font-family: SimSun;
                }

                .report-title {
                    font-size: 20px;
                    font-weight: 400;
                    font-family: SimSun;
                }
            }

            .print-common-header-barcode {
                position: absolute;
                right: 0;
                top: 0;
                width: 90pt;
                height: 40pt;
            }
        }
    }
</style>
