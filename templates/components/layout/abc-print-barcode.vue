<template>
    <div class="abc-print-barcode-wrapper">
        <abc-print-image
            :value="barcodeSrc()"
            :height="height"
            object-fit=""
        ></abc-print-image>

        <div
            v-if="showCode"
            class="barcode-numbers"
        >
            <span
                v-for="(c,idx) in barcodeArr"
                :key="idx"
            >
                {{ c }}
            </span>
        </div>
    </div>
</template>

<script>
    import {
        textToBase64BarCode
    } from "../../common/utils";
    import AbcPrintImage from "./abc-print-image.vue";

    export default {
        name: 'AbcPrintBarcode',

        components: {
            AbcPrintImage,
        },

        props: {
            value: {
                type: [String, Number],
                default: '',
            },

            showCode: Boolean,

            height: {
                type: Number,
                default: 40,
            }
        },

        computed: {
            barcodeArr() {
                return `${this.value}`.padStart(8,'0').split('') || [];
            }
        },

        methods: {
            barcodeSrc() {
                return textToBase64BarCode(this.value);
            },
        }
    }
</script>

<style lang="scss">
.abc-print-barcode-wrapper {
    .barcode-numbers {
        display: flex;
        justify-content: space-between;
        font-size: 9pt;
    }
}
</style>