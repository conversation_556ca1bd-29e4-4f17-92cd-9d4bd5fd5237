<template>
    <div
        class="print-row"
        :style="style"
    >
        <slot></slot>
        <div class="clear-float"></div>
    </div>
</template>

<script>
    export default {
        name: "PrintRow",
        componentName: 'PrintRow',
        props: {
            gutter: {
                type: Number,
                default: 0,
            },
        },
        computed: {
            style() {
                let style = {};
                if(this.gutter){
                    style.marginLeft = `-${this.gutter / 2}pt`;
                    style.marginRight = style.marginLeft;
                    style.width = 'auto';
                }
                return style;
            }
        }
    }
</script>
