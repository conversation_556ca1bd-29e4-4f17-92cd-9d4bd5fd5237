@use "sass:math";
.print-col {
    float: left;
    box-sizing: border-box;
}

.print-row {
    width: 100%;
    position: relative;

    & {
        .clear-float {
            display: block;
            clear: both;
        }
    }
}

@for $i from 1 through 24 {
    .print-col-#{$i} {
        width: (math.div(1, 24) * $i * 100 ) * 1%;
    }
}
.print-col-12 {
    *width: 49.9%;
}
.print-col-6 {
    *width: 22.4%;
}
.print-col-4 {
    *width: 16.4%;
}