<template>
    <component
        :is="tag"
        :style="style"
        :overflow="overflow"
    >
        <slot>
            {{ renderValue }}
        </slot>
    </component>
</template>

<script>
    import {getLengthWithFullCharacter} from "../../../common/utils";

    const COLOR_MAP = {
        dark: '#000000',
        gray: '#8B8E98',
    }

    const WEIGHT_MAP = {
        normal: '400',
        bold: '500',
        bolder: '600',
        boldest: '700',
    }

    const SIZE_MAP = {
        small: 8,
        normal: 10,
        large: 12
    }

    export default {
        name: 'AbcPrintText',

        props: {
            value: {
                type: String,
                default: '',
            },

            size: {
                type: [String, Number],
                default: 'normal' // small / normal / large 或者数字
            },

            theme: {
                type: String,
                default: 'dark',
                validate(value) {
                    return ['dark', 'gray'].includes(value);
                }
            },

            weight: {
                type: String,
                default: 'normal',
                validate(value) {
                    return ['normal', 'bold', 'bolder', 'boldest'].includes(value);
                }
            },

            align: {
                type: String,
                default: 'left',
                validate(value) {
                    return ['left', 'center', 'right'].includes(value);
                }
            },

            customStyle: {
                type: Object,
                default: () => ({})
            },

            tag: {
                type: String,
                default: 'span'
            },

            overflow: Boolean,

            maxLength: {
                type: Number,
                default: undefined
            }
        },

        computed: {
            style() {
                const fontSize = SIZE_MAP[this.size] || (Number.isNaN(Number(this.size)) ? 10 : this.size);

                const overflowStyle = {
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                }

                return {
                    color: COLOR_MAP[this.theme],
                    fontWeight: WEIGHT_MAP[this.weight],
                    fontSize: fontSize + 'pt',
                    lineHeight: fontSize + 2 + 'pt',
                    textAlign: this.align,
                    ...(this.overflow ? overflowStyle : {}),
                    ...this.customStyle
                }
            },

            renderValue() {
                const value = this.value;

                if(Number.isInteger(this.maxLength)) {
                    const splitLen = getLengthWithFullCharacter(value, this.maxLength).splitLength;
                    return value.slice(0, splitLen);
                }

                return value;
            }
        },
    }
</script>