<template>
    <div
        :class="['print-col', `print-col-${span}`]"
        :style="[style, customStyle]"
    >
        <slot></slot>
    </div>
</template>

<script>
    export default {
        name: "PrintCol",
        props: {
            span: {
                type: Number,
                default: 24,
            },
            customStyle: {
                type: Object,
                default() {
                    return {};
                },
            }
        },
        computed: {
            gutter() {
                let parent = this.$parent;
                while (parent && parent.$options.componentName !== 'PrintRow') {
                    parent = parent.$parent;
                }
                return parent ? parent.gutter : 0;
            },
            style() {
                let style = {};
                if (this.gutter) {
                    style.paddingLeft = this.gutter / 2 + 'pt';
                    style.paddingRight = style.paddingLeft;
                }
                return style;
            }
        }
    }
</script>
