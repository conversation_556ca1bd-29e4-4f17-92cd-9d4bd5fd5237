<template>
    <div
        class="abc-print-space"
        :style="style"
    >
    </div>
</template>

<script>
    export default {
        name: 'AbcPrintSpace',

        props: {
            value: {
                type: Number,
                default: 0,
            },

            direction: {
                type: String,
                default: 'v',
                validate(value) {
                    return ['v', 'h'].includes(value);
                }
            }
        },

        computed: {
            style() {
                if(this.direction === 'v') {
                    return {
                        height: `${this.value}pt`
                    }
                }
                return {
                    display: 'inline-block',
                    height: '100%',
                    width: `${this.value}pt`
                }
            }
        }
    }
</script>