<template>
    <div>
        <abc-print-space :value="2"></abc-print-space>
        <div :style="style">
        </div>
        <abc-print-space :value="2"></abc-print-space>
    </div>
</template>

<script>
    import AbcPrintSpace from "../space.vue";

    const COLOR_MAP = {
        dark: '#000000',
        gray: '#8B8E98',
    }

    export default {
        name: 'AbcPrintSplit',
        components: {AbcPrintSpace},

        props: {
            size: {
                type: Number,
                default: 0.5
            },

            color: {
                type: String,
                default: 'dark',
                validate(value) {
                    return ['dark', 'gray'].includes(value);
                }
            }
        },

        computed: {
            style() {
                return {
                    height: `${this.size}pt`,
                    backgroundColor: COLOR_MAP[this.color]
                }
            }
        }
    }
</script>