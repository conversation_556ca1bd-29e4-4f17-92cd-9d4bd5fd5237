<template>
    <div
        class="abc-print-row"
        :style="{
            '--gap': `${gap }px`,
            '--gapY': `${gapY }px`,
        }"
    >
        <slot></slot>
    </div>
</template>

<script>
    export default {
        name: 'AbcPrintRow',

        provide() {
            return {
                row: this,
            }
        },

        props: {
            gap: {
                type: Number,
                default: 0,
            },
            gapY: {
                type: Number,
                default: 0,
            },
        },
    }
</script>

<style lang="scss">
.abc-print-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    gap: var(--gapY) var(--gap);
}
</style>