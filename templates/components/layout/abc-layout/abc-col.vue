<template>
    <div
        class="abc-print-col"
        :style="{ width }"
    >
        <slot></slot>
    </div>
</template>

<script>
    const TotalCount = 24;

    export default {
        name: 'AbcPrintCol',

        inject: ['row'],

        props: {
            span: {
                type: Number,
                default: 1
            }
        },

        computed: {
            width() {
                return this.span / TotalCount * 100 + '%';
            }
        },
    }
</script>

<style lang="scss">
.abc-print-col {
    display: inline-flex;
    align-items: center;
}
</style>