<template>
    <div
        class="abc-print-image"
        :style="renderStyle"
    >
        <img
            :src="value"
            alt=""
            :style="{
                objectFit,
                width: '100%',
                height: '100%'
            }"
        />
    </div>
</template>

<script>
    export default {
        name: 'AbcPrintImage',

        props: {
            value: {
                type: String,
                default: '',
            },

            width: {
                type: [Number,String],
                default: '100%'
            },

            height: {
                type: [Number,String],
                default: '100%'
            },

            objectFit: {
                type: String,
                default: 'contain'
            }
        },

        computed: {
            renderStyle() {
                const isNumberStr = v => /^\d+$/.test(v);
                return {
                    width: this.width + (isNumberStr(this.width) ? 'pt' : '') ,
                    height: this.height + (isNumberStr(this.height) ? 'pt' : ''),
                }
            }
        }
    }
</script>