<template>
    <table
        class="abc-table"
        :class="{ 'has-border': border }"
        :style="tableStyle"
        data-type="complex-table"
    >
        <colgroup>
            <col
                v-for="(h, i) in renderColumns"
                :key="i"
                :width="h.percentWidth"
                :align="h.align"
            />
        </colgroup>

        <thead>
            <tr class="abc-table-tr">
                <th
                    v-for="(h, i) in renderColumns"
                    :key="i"
                    class="abc-table-cell"
                    :align="h.align"
                    :class="{ 'has-border': border }"
                    :style="h.thStyle || {}"
                >
                    {{ h.label }}
                </th>
            </tr>
        </thead>

        <tbody class="abc-table-body">
            <tr
                v-for="(item, i) in list"
                :key="i"
                class="abc-table-tr"
            >
                <td
                    v-for="column in columns"
                    :key="column.key"
                    class="abc-table-cell"
                    v-bind="column.customProps || {}"
                >
                    <template v-if="useSlot">
                        <slot
                            :name="column.key"
                            :item="item"
                            :index="i"
                        ></slot>
                    </template>

                    <template v-else>
                        <template v-if="column.render">
                            <render-props
                                :render="column.render"
                                :value="item"
                                :index="i"
                            ></render-props>
                        </template>

                        <template v-else>
                            {{ item[column.key] }}
                        </template>
                    </template>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
    import RenderProps from '../render-props/index.vue';

    /**
     * columns: {
     *  width: 1, // 比例
     *  align: 'left', // 对齐方式
     *  span: 2, // 合并列
     *  key: 'name', // 数据key
     *  label: '姓名', // 标题
     *  render: (h, row) => { return ( <div></div> ) }
     * }
     */

    export default {
        name: 'AbcTable',

        components: {
            RenderProps,
        },

        props: {
            columns: {
                type: Array,
                default: () => [],
            },

            list: {
                type: Array,
                default: () => [],
            },

            border: Boolean,

            useSlot: Boolean,
        },

        computed: {
            renderColumns() {
                const columns = this.columns || [];

                const total = this.columns.reduce((total, item) => {
                    total += item.width && typeof (item.width * 1) === 'number' ? item.width * 1 : 1;
                    return total;
                }, 0)

                columns.forEach(h => {
                    h.align = h.align || 'left';
                    h.width = h.width && typeof (h.width * 1) === 'number' ? h.width * 1 : 1;
                    h.percentWidth = `${((h.width * 1) / total) * 100}%`;
                })

                return columns;
            },

            tableStyle() {
                return {
                    // minHeight: '200pt'
                }
            },
        }
    }
</script>

<style lang='scss'>
.abc-table {
    width: 100%;
    border-collapse: unset;

    &.has-border {
        border-collapse: collapse;
        border-spacing: 0;
        border: 1pt solid #A6A6A6;

        .abc-table-tr {
            border-bottom: 1pt solid #A6A6A6;
        }

        .abc-table-cell {
            border-right: 1pt solid #A6A6A6;

            &:last-child {
                border-right: none;
            }
        }
    }


    .abc-table-cell {
        line-height: 30pt;
        padding: 0 10pt;
        font-size: 10pt;
        font-weight: 400;
        color: #000000;
    }

    .abc-table-body {
        .abc-table-tr:last-child {
            border-bottom: none;
        }
    }
}
</style>