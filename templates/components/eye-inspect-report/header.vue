<template>
    <div
        class="eye-inspect-header-wrapper"
        data-type="header"
    >
        <div class="eye-inspect-header-top">
            <template v-if="headerConfig.logo">
                <div
                    v-if="logo"
                    class="header-left"
                >
                    <abc-print-image :value="logo"></abc-print-image>
                </div>
            </template>

            <div class="header-center">
                <div class="organ-name-wrapper">
                    <div class="organ-name-first-line">
                        {{ renderOrganName.renderFirstLineName }}
                    </div>
                    <div class="organ-name-second-line">
                        {{ renderOrganName.renderSecondLineName }}
                    </div>
                </div>

                <div
                    v-if="headerConfig.examineName"
                    class="report-title"
                >
                    {{ reportTitle }}
                </div>
            </div>

            <div
                v-if="barcode && headerConfig.barcode"
                class="header-right"
            >
                <abc-print-barcode
                    :value="barcode"
                    show-code
                ></abc-print-barcode>
            </div>
        </div>

        <div class="header-base-field-list">
            <div
                class="header-base-field-item"
                style="width: 33%"
            >
                <span>检查人：</span>
                <span>{{ patientStr }}</span>
            </div>

            <div
                v-if="headerConfig.patientBirthDate"
                class="header-base-field-item"
                style="width: 33%;padding-left: 44pt;"
            >
                <span>出生日期：</span>
                <span>{{ birthday }}</span>
            </div>

            <div
                v-if="headerConfig.inspectNo"
                class="header-base-field-item"
                style="width: 33%;"
            >
                <span>检查编号：</span>
                <span>{{ inspectNo }}</span>
            </div>

            <div
                v-if="headerConfig.relationOutpatient"
                class="header-base-field-item"
                style="width: 33%"
            >
                <span>关联门诊：</span>
                <span>{{ formatReserveDate(printData.registrationFormItem) }}</span>
            </div>
        </div>

        <abc-print-space :value="14"></abc-print-space>
    </div>
</template>

<script>
    import AbcPrintImage from "../layout/abc-print-image.vue";
    import {formatAge, getRenderOrganName} from "../../common/utils";
    import AbcPrintBarcode from "../layout/abc-print-barcode.vue";
    import AbcPrintSpace from "../layout/space.vue";
    import {formatDate} from "@tool/date";

    export default {
        name: 'EyeInspectHeader',

        components: {
            AbcPrintSpace,
            AbcPrintBarcode,
            AbcPrintImage
        },

        props: {
            printData: {
                type: Object,
                default: () => ({}),
            },
            printConfig: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            logo() {
                return this.printData?.organPrintView?.logo
            },
            renderOrganName() {
                return getRenderOrganName({
                    printOrganName: this.headerConfig.title,
                    printOrganSubName: this.headerConfig.subtitle,
                    organName: this.printData?.organPrintView?.name,
                })
            },

            reportTitle() {
                return this.printData.name + '报告单'
            },

            barcode() {
                return this.printData.patientOrderNumber;
            },

            patientStr() {
                const {
                    patient = {}
                } = this.printData;

                return `${patient.name} ${patient.sex} ${formatAge(patient.age)}`
            },

            birthday() {
                return this.printData.patient?.birthday;
            },

            inspectNo() {
                return this.printData.orderNo;
            },

            headerConfig(){
                return this.printConfig.header || {};
            },
        },

        methods: {
            formatReserveDate(item) {
                const {
                    reserveDate,
                    reserveStart,
                    reserveEnd,
                    orderNoStr,
                } = item || {};

                if (!reserveDate) return '';

                const date = new Date(reserveDate.replace(/-/g, '/'));
                const todayDate = new Date(formatDate(new Date(), 'y-m-d', true).replace(/-/g, '/'));
                const diff = (todayDate.getTime() - date.getTime()) / 1000;

                let str = '';

                if (diff > 0 && diff <= 86400) {
                    str = '昨天';
                } else if (diff < 0 && diff >= -86400) {
                    str = '明天';
                } else if (diff === 0) {
                    str = '今天';
                } else {
                    str = reserveDate.substr(5, reserveDate.length);
                }

                return `${str} ${orderNoStr} ${reserveStart}~${reserveEnd}`;
            },
        }
    }
</script>

<style lang="scss">
.eye-inspect-header-wrapper {
    .eye-inspect-header-top {
        position: relative;
        padding: 8pt 90pt 0;

        .header-left {
            width: 90pt;
            height: 40pt;
            position: absolute;
            left: 0;
            top: 0;
        }

        .header-center {
            width: 100%;
            min-height: 40pt;
            text-align: center;

            .organ-name-wrapper {
                padding-bottom: 8pt;
                color: #000000;
                //font-family: "Songti SC";
                font-size: 20pt;
                font-style: normal;
                font-weight: 900;
                line-height: 24pt; /* 120% */
                font-family: SimSun;
            }

            .report-title {
                font-size: 20px;
                font-weight: 400;
                font-family: SimSun;
            }
        }

        .header-right {
            width: 90pt;
            height: 40pt;
            position: absolute;
            right: 0;
            top: 0;
        }
    }

    .header-base-field-list {
        padding-top: 16pt;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        color: #000000;
        font-family: "Microsoft YaHei UI";
        font-size: 10pt;
        font-style: normal;
        font-weight: 300;
        line-height: 12pt; /* 120% */

        .header-base-field-item {
            width: 25%;
            padding-top: 6pt;

            &:nth-child(3) {
                text-align: right;
            }
        }
    }
}
</style>