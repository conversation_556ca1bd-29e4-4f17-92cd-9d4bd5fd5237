<template>
    <table
        class="eye-inspect-report-table"
        data-type="complex-table"
    >
        <thead>
            <tr>
                <td
                    v-for="(headerItem, idx) in headerConfig"
                    :key="idx"
                    :colspan="headerItem.colSpan"
                    class="table-cell"
                    :style="{
                        ...headerItem.style,
                        padding: '4pt'
                    }"
                >
                    {{ headerItem.label }}
                </td>
            </tr>
        </thead>

        <tbody>
            <tr
                v-for="(row,i) in renderTableList"
                :key="i"
            >
                <td
                    v-for="(col,j) in row"
                    :key="j"
                    :rowspan="col.rowSpan"
                    :colspan="col.colSpan"
                    class="table-cell"
                >
                    <template v-if="col.type === 'item'">
                        <div class="item-list-wrapper">
                            <div
                                v-for="(item, k) in col.items"
                                :key="k"
                                class="item"
                            >
                                <template v-if="item.renderType === 'radio'">
                                    <div style="padding: 4pt;text-align: left">
                                        <span
                                            style="margin-right: 2pt;vertical-align: -2.5px"
                                        >
                                            <svg
                                                viewBox="0 0 24 24"
                                                width="15"
                                                height="15"
                                            >
                                                <rect
                                                    x="1"
                                                    y="1"
                                                    width="22"
                                                    height="22"
                                                    fill="none"
                                                    stroke="black"
                                                    stroke-width="2"
                                                    rx="5"
                                                    ry="5"
                                                />
                                                <template v-if="item.isChecked">
                                                    <path
                                                        d="M6,12 l4,4 l8,-8"
                                                        fill="none"
                                                        stroke="black"
                                                        stroke-width="2"
                                                    />
                                                </template>
                                            </svg>
                                        </span>

                                        <span style="font-size: 8pt">
                                            {{ item.displayName }}
                                        </span>
                                    </div>
                                </template>

                                <template v-else-if="item.renderType === 'textarea'">
                                    <div
                                        class="eye-result-cell"
                                        style="font-size: 8pt"
                                    >
                                        <abc-html :value="item.value"></abc-html>
                                    </div>
                                </template>

                                <template v-else>
                                    <div class="eye-result-cell">
                                        <span class="eye-result-cell-label">
                                            {{ item.displayName }}
                                        </span>

                                        <div
                                            class="eye-result-cell-content"
                                        >
                                            <span class="eye-result-cell-value">
                                                {{ item.useRenderValue ? item.renderValue : item.value }}
                                            </span>

                                            <span
                                                v-if="item.unit && !item.notDisplayUnit"
                                                class="eye-result-cell-unit"
                                            >
                                                {{ item.unit }}
                                            </span>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </template>

                    <template v-else>
                        <div
                            :style="{
                                padding: '4pt',
                                ...(col.style || {})
                            }"
                        >
                            {{ col.value }}
                        </div>
                    </template>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
    import AbcHtml from "../layout/abc-html.vue";
    import clone from "../../common/clone";
    import {formatDate} from "@tool/date";

    const CellType = {
        title: 'title',
        item: 'item',
    }

    export default {
        name: 'EyeInspectNoMergeItemTable',

        components: {AbcHtml},

        props: {
            itemsValue: {
                type: Array,
                default: () => ([]),
            },

            contentConfig: {
                type: Object,
                default: () => ({}),
            }
        },

        computed: {
            renderTableList() {
                const rows = clone(this.itemsValue)
                    .map(tree => this.clearTreeEmptyLevel(tree))
                    .reduce((res, node) => {
                        const _res = this.traverseTree(node);
                        res.push(..._res);
                        return res;
                    }, []);
                return this.createTableCellList(rows, this.contentConfig);
            },

            headerConfig() {
                const cellTypeCount = this.renderTableList.reduce((res, l) => {
                    const titleCells = l.filter(item => item.type === CellType.title).length;
                    res.push(titleCells);
                    return res;
                }, []);

                const maxTitleColNum = Math.max(...cellTypeCount);

                const res = [
                    {
                        label: '项目',
                        style: {
                            width: '14.4%',
                        },
                        colSpan: maxTitleColNum || 1,
                        type: CellType.title,
                    },
                    {
                        label: '右眼',
                        style: {
                            width: '32.4%',
                        },
                        colSpan: 1,
                    },
                    {
                        label: '左眼',
                        style: {
                            width: '32.4%',
                        },
                        colSpan: 1,
                    },
                ];
                if(this.contentConfig.tester) {
                    res.push({
                        label: '检查人',
                        style: {
                            width: '8.6%',
                        }
                    })
                }

                if(this.contentConfig.testTime) {
                    res.push({
                        label: '检查时间',
                        style: {
                            width: '12%',
                        }
                    })
                }
                return res;
            }
        },

        methods: {
            // 去掉空层级
            clearTreeEmptyLevel(tree) {
                const treeNode = tree.children?.[0];
                const len = tree.children?.length;
                if(len === 1) {
                    if(!treeNode.name && treeNode.type === CellType.title) {
                        tree.children = treeNode.children;
                        this.clearTreeEmptyLevel(tree);
                    }
                } else if(len) {
                    for (const child of tree.children) {
                        this.clearTreeEmptyLevel(child);
                    }
                }
                return tree;
            },

            // 获取叶子节点的指标
            getNodeItems(node) {
                const handleItem = item => {
                    // 特殊逻辑，两个输入框, 拼接结果
                    if(item.constraints?.length > 1) {
                        if(item.value) {
                            const valueList = item.value.split('@');
                            item.renderValue = valueList.reduce((res, v, idx) => {
                                if(v) {
                                    res.push(v + (item.constraints?.[idx]?.unit || ''));
                                }
                                return res;
                            }, []).join(' @ ');
                            item.notDisplayUnit = true;
                            item.useRenderValue = true;
                        }
                    }
                    // 文本域
                    if(item.componentType === 5) {
                        item.renderType = 'textarea';
                    }

                    return item;
                };

                return node.children.reduce((res, child) => {
                    let items = child.items || [];
                    // 特殊逻辑，主视眼只有一个指标，但是要拆分为左右眼
                    if(items[0].name === '主视眼') {
                        const item = items[0];
                        res.push(
                            {
                                displayName: item.options[1],
                                value: item.value,
                                renderType: 'radio',
                                isChecked: item.options[1] === item.value,
                            },
                            {
                                displayName: item.options[0],
                                value: item.value,
                                renderType: 'radio',
                                isChecked: item.options[0] === item.value,
                            },
                        )
                    } else {
                        items = items
                            .filter(item => !item.disablePrint)
                            .map(handleItem);

                        res.push(...items);
                    }
                    return res;
                }, []);
            },

            // 获取叶子节点的总数，inspectType 存在的节点视为叶子节点
            getNodeLeafCount(node) {
                if(node.inspectType) {
                    return 1;
                }

                let count = 0;

                if(node.children?.length) {
                    for (const child of node.children) {
                        count += this.getNodeLeafCount(child)
                    }
                }

                return count;
            },

            // 遍历树，获取叶子节点到根节点的路径
            traverseTree(node, path = [], res = []) {
                if(node.inspectType) {
                    const items = this.getNodeItems(node)
                    const _res = [
                        ...clone(path),
                        {
                            items: items || [],
                            colSpan: 2,
                            type: CellType.item,
                        }
                    ];
                    res.push(_res);
                    // 收集过的枝干节点不需要渲染
                    path.forEach(p => {
                        p.notNeedRender = true;
                    })
                    return;
                }

                path.push({
                    ...node,
                    rowSpan: this.getNodeLeafCount(node) || 1,
                    value: node.name,
                });

                if(node.children?.length) {
                    for(let i = 0; i < node.children.length; i++) {
                        this.traverseTree(node.children[i], path, res);
                    }
                }

                path.pop();

                return res;
            },

            // 计算单元格 colSpan
            computedCellColSpan(row, maxTitleColNum){
                const curTitleCellList = row.filter(l => l.type === CellType.title);
                const len = curTitleCellList.length;
                if(len && len < maxTitleColNum) {
                    curTitleCellList[len - 1].colSpan = maxTitleColNum - len + 1;
                }

                return row;
            },

            // 创建检查信息相关单元格
            createCheckInfoCell(originData, rows,config) {
                const noRepeatFirstColTitleList = Array.from(
                    new Set(
                        originData.map(item => item.name)
                    )
                );

                for (const title of noRepeatFirstColTitleList) {
                    const row = rows.find(row => row[0].name === title);
                    if(row) {
                        if(config.tester) {
                            row.push(
                                // 检查人
                                {
                                    value: row[0].checker?.name,
                                    rowSpan: row[0].rowSpan || 1,
                                    style: {
                                        fontSize: '8pt'
                                    }
                                }
                            )
                        }
                        if(config.testTime) {
                            row.push(
                                // 检查时间
                                {
                                    value: formatDate(row[0].checker?.date, 'MM-DD:HH:mm'),
                                    rowSpan: row[0].rowSpan || 1,
                                    style: {
                                        fontSize: '8pt'
                                    }
                                }
                            )
                        }

                    }
                }

                return rows;
            },

            // 清空不需要渲染的节点
            clearNotNeedRenderNode(rows) {
                return  rows.map(row => {
                    row = row.filter(cell => !cell.notNeedRender);
                    return row;
                });
            },

            // 生成表格的单元格数据
            createTableCellList(rows, contentConfig) {
                const cellTypeCount = rows.reduce((res, l) => {
                    const titleCells = l.filter(item => item.type === CellType.title).length;
                    res.push(titleCells);
                    return res;
                }, []);
                const maxTitleColNum = Math.max(...cellTypeCount);

                // 计算标题的合并单元格
                rows = rows.map(row => this.computedCellColSpan(row, maxTitleColNum));
                // 创建 检查人检查时间相关信息的单元格
                rows = this.createCheckInfoCell(this.itemsValue ,rows, contentConfig);
                // 清除不需要渲染的节点
                rows = this.clearNotNeedRenderNode(rows);

                return rows;
            }
        }
    }
</script>

<style lang="scss">
.eye-inspect-report-table{
    border-collapse: collapse;
    border: 1px solid #A6A6A6;
    width: 100%;

    .table-cell {
        border-right: 1px solid #A6A6A6;
        border-bottom: 1px solid #A6A6A6;
        font-size: 9pt;
        text-align: center;
        height: 1px;

        .item-list-wrapper {
            display: flex;
            justify-content: space-between;
            width: 100%;
            height: 100%;

            .item {
                flex: 1;
                width: 33.33%;
                padding: 4pt;
                border-right: 1px solid #A6A6A6;

                &:last-child {
                    border-right: none;
                }

                .eye-result-cell {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    height: 100%;

                    .eye-result-cell-label {
                        color: #7A8794;
                        max-width: 35pt;
                        font-size: 8pt;
                        padding-right: 2pt;
                        text-align: left;
                    }

                    .eye-result-cell-content {
                        .eye-result-cell-value {
                            font-size: 8pt;
                            padding-right: 2px;
                        }

                        .eye-result-cell-unit {
                            font-size: 8pt;
                            white-space: nowrap;
                        }
                    }

                }
            }
        }

    }
}
</style>