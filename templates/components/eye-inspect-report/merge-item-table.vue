<template>
    <table
        v-if="renderTableList.length"
        class="eye-inspect-report-merge-table"
        :class="{
            'page-is-a4': currentPageIsA4
        }"
        data-type="complex-table"
    >
        <thead>
            <tr :style="{ background: styleConfig.themeColor || '#8EAD24' }">
                <td
                    v-for="(headerItem, idx) in headerConfig"
                    :key="idx"
                    :colspan="headerItem.colSpan"
                    class="table-cell"
                    :style="{
                        ...headerItem.style,
                        padding: '6pt'
                    }"
                >
                    {{ headerItem.label }}
                </td>
            </tr>
        </thead>

        <tbody>
            <tr
                v-for="(row,i) in renderTableList"
                :key="i"
            >
                <td
                    v-for="(col,j) in row"
                    :key="j"
                    :rowspan="col.rowSpan"
                    :colspan="col.colSpan"
                    class="table-cell"
                    :style="col.style"
                >
                    <template v-if="col.type === 'item'">
                        <div class="item-list-wrapper">
                            <template v-for="(itemList, k) in col.items">
                                <!--模拟表格边框间隔-->
                                <div
                                    v-if="col.items.length > 1 && k === 1"
                                    :key="k"
                                    class="td-split-line"
                                ></div>

                                <div
                                    :key="k"
                                    class="item"
                                >
                                    <div class="eye-result-cell">
                                        <div
                                            v-if="getNormalItems(itemList).length"
                                            class="eye-result-cell-content text-content"
                                        >
                                            {{ getItemsText(itemList) }}
                                        </div>

                                        <div
                                            v-for="(item, l) in itemList.filter(it => (it.renderType && it.renderType === 'textarea'))"
                                            :key="l"
                                            class="eye-result-cell-content"
                                            style="width: 100%"
                                        >
                                            <abc-html :value="item.value"></abc-html>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>

                    <template v-else>
                        <div
                            :style="{
                                padding: '4pt',
                                ...(col.style || {})
                            }"
                        >
                            {{ col.value }}
                        </div>
                    </template>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
    import AbcHtml from "../layout/abc-html.vue";
    import clone from "../../common/clone";
    import {formatDate} from "@tool/date";

    const CellType = {
        title: 'title',
        item: 'item',
    }

    export default {
        name: 'EyeInspectMergeItemTable',

        components: {AbcHtml},

        props: {
            itemsValue: {
                type: Array,
                default: () => ([]),
            },

            contentConfig: {
                type: Object,
                default: () => ({}),
            },

            styleConfig: {
                type: Object,
                default: () => ({}),
            },

            currentPageIsA5: {
                type: Boolean,
                default: true,
            },

            currentPageIsA4: Boolean,
        },

        computed: {
            renderTableList() {
                let rows = clone(this.itemsValue)
                    .filter(this.filterGoodsDisableComposePrint)
                    .map(tree => this.clearTreeEmptyLevel(tree));
                rows = rows.reduce((res, node) => {
                    const _res = this.traverseTree(node);
                    res.push(..._res);
                    return res;
                }, []);
                return this.createTableCellList(rows, this.contentConfig);
            },

            headerConfig() {
                const cellTypeCount = this.renderTableList.reduce((res, l) => {
                    const titleCells = l.filter(item => item.type === CellType.title).length;
                    res.push(titleCells);
                    return res;
                }, []);

                const maxTitleColNum = Math.max(...cellTypeCount);

                const res = [
                    {
                        label: '项目',
                        style: {
                            width: '14.4%',
                        },
                        colSpan: maxTitleColNum || 1,
                        type: CellType.title,
                    },
                    {
                        label: '右眼（OD）',
                        style: {
                            width: '32.4%',
                        },
                        colSpan: 1,
                    },
                    {
                        label: '左眼（OS）',
                        style: {
                            width: '32.4%',
                        },
                        colSpan: 1,
                    },
                ];
                
                if(this.contentConfig.tester) {
                    res.push({
                        label: '检查人',
                        style: {
                            width: '8.6%',
                            minWidth: '30px',
                        }
                    })
                }

                if(this.contentConfig.testTime) {
                    res.push({
                        label: '检查时间',
                        style: {
                            width: '12%',
                            minWidth: '66px',
                        }
                    })
                }
                return res;
            }
        },

        mounted() {
            this.$emit('get-table-length', this.renderTableList.length);
        },

        methods: {
            // 去掉不打印的项目
            filterGoodsDisableComposePrint(item){
                return !item.goodsDisableComposePrint;
            },
            // 去掉空层级
            clearTreeEmptyLevel(tree) {
                const treeNode = tree.children?.[0];
                const len = tree.children?.length;
                if(len === 1) {
                    if(!treeNode.name && treeNode.type === CellType.title) {
                        tree.children = treeNode.children;
                        this.clearTreeEmptyLevel(tree);
                    }
                } else if(len) {
                    for (const child of tree.children) {
                        this.clearTreeEmptyLevel(child);
                    }
                }
                return tree;
            },

            handleItem (item) {
                // 特殊逻辑，两个输入框, 拼接结果
                if(item.constraints?.length > 1) {
                    if(item.value) {
                        const valueList = item.value.split('@');
                        item.renderValue = valueList.reduce((res, v, idx) => {
                            if(v) {
                                res.push(v + (item.constraints?.[idx]?.unit || ''));
                            }
                            return res;
                        }, []).join(' @ ');
                        item.notDisplayUnit = true;
                        item.useRenderValue = true;
                    }
                }
                // 文本域
                if(item.componentType === 5) {
                    item.renderType = 'textarea';
                }

                return item;
            },

            // 获取叶子节点的总数，node.type === title 的节点视为叶子节点
            getNodeLeafCount(node) {
                if(node.children?.[0]?.inspectType) {
                    return 1;
                }

                let count = 0;

                if(node.children?.length) {
                    for (const child of node.children) {
                        count += this.getNodeLeafCount(child)
                    }
                }

                return count;
            },

            getEyeItems(node) {
                let leftEyeItems = [], rightEyeItems = [], noTypeItems = [];
                const children = node.children;
                // 是否只有不区分眼别的指标
                const isOnlyNoEyeTypeItem = children.every(child => {
                    return child.children.length === 1;
                })
                // 每个 child 代表 pc 中渲染的表格中的一行
                for (const child of children) {
                    if(isOnlyNoEyeTypeItem) {
                        noTypeItems = noTypeItems.concat(child.children[0].items);
                    } else {
                        // 不区分眼别的指标
                        if(child.children.length === 1) {
                            leftEyeItems = leftEyeItems.concat(child.children[0].items);
                            rightEyeItems = rightEyeItems.concat(child.children[0].items);
                        } else {
                            leftEyeItems = leftEyeItems.concat(child.children[0].items);
                            rightEyeItems = rightEyeItems.concat(child.children[1].items);
                        }
                    }
                }

                if(isOnlyNoEyeTypeItem) {
                    return [ noTypeItems.map(this.handleItem) ];
                }

                const filterNoNeedPrint = item => !item.disablePrint

                return [
                    leftEyeItems.map(this.handleItem).filter(filterNoNeedPrint),
                    rightEyeItems.map(this.handleItem).filter(filterNoNeedPrint),
                ];
            },

            // 遍历树，获取叶子节点到根节点的路径
            traverseTree(node, path = [], res = []) {
                // 眼科单项或者单项中的单组视为叶子节点
                if(node.children?.[0]?.inspectType) {
                    const items = this.getEyeItems(node);
                    const _res = [
                        ...clone(path),
                        {
                            ...node,
                            rowSpan: this.getNodeLeafCount(node) || 1,
                            value: node.name,
                        },
                        {
                            items,
                            colSpan: 2,
                            type: CellType.item,
                        }
                    ];
                    // 对于单项，如果指标都不打印，则该单项也不打印
                    if(items[0]?.length || items[1]?.length) {
                        res.push(_res);
                    }
                    // 收集过的枝干节点不需要渲染
                    path.forEach(p => {
                        p.notNeedRender = true;
                    })
                    return res;
                }

                path.push({
                    ...node,
                    rowSpan: this.getNodeLeafCount(node) || 1,
                    value: node.name,
                });

                if(node.children?.length) {
                    for(let i = 0; i < node.children.length; i++) {
                        this.traverseTree(node.children[i], path, res);
                    }
                }

                path.pop();

                return res;
            },

            // 计算单元格 colSpan
            computedCellColSpan(row, maxTitleColNum){
                const curTitleCellList = row.filter(l => l.type === CellType.title);
                const len = curTitleCellList.length;
                if(len && len < maxTitleColNum) {
                    curTitleCellList[len - 1].colSpan = maxTitleColNum - len + 1;
                }

                return row;
            },

            // 创建检查信息相关单元格
            createCheckInfoCell(originData, rows,config) {
                const noRepeatFirstColTitleList = Array.from(
                    new Set(
                        originData.map(item => item.name)
                    )
                );

                for (const title of noRepeatFirstColTitleList) {
                    const row = rows.find(row => row[0].name === title);
                    if(row) {
                        if(config.tester) {
                            row.push(
                                // 检查人
                                {
                                    value: row[0].checker?.name,
                                    rowSpan: row[0].rowSpan || 1,
                                    style: {
                                        fontSize: this.currentPageIsA5 && !this.currentPageIsA4 ? '10px' : '10pt'
                                    }
                                }
                            )
                        }
                        if(config.testTime) {
                            row.push(
                                // 检查时间
                                {
                                    value: formatDate(row[0].checker?.date, 'MM-DD:HH:mm'),
                                    rowSpan: row[0].rowSpan || 1,
                                    style: {
                                        fontSize: this.currentPageIsA5 && !this.currentPageIsA4 ? '10px' : '10pt'
                                    }
                                }
                            )
                        }

                    }
                }

                return rows;
            },

            // 设置单元格的背景色
            setCellBackground(rows){
                const backgroundOptions = ['#EFEFEF', '#ffffff'];
                let curRowBg = backgroundOptions[1];

                for(let i = 0; i < rows.length; i++) {
                    const lastRowBgColor = rows[i - 1]?.[0]?.style?.background;
                    if(lastRowBgColor) {
                        curRowBg = lastRowBgColor === backgroundOptions[0] ? backgroundOptions[1] : backgroundOptions[0];
                    }

                    if(rows[i][0].rowSpan > 1) {
                        const len = rows[i][0].rowSpan;
                        for(let j = 0; j < len; j++) {
                            rows[i+j].forEach(cell => {
                                cell.style = {
                                    ...cell.style,
                                    background: curRowBg
                                }
                            })
                        }
                        i += len - 1;
                    } else {
                        rows[i].forEach(cell => {
                            cell.style = {
                                ...cell.style,
                                background: curRowBg
                            }
                        })
                    }
                }

                return rows;
            },

            // 清空不需要渲染的节点
            clearNotNeedRenderNode(rows) {
                return  rows.map(row => {
                    row = row.filter(cell => !cell.notNeedRender);
                    return row;
                });
            },

            // 生成表格的单元格数据
            createTableCellList(rows, contentConfig) {
                const cellTypeCount = rows.reduce((res, l) => {
                    const titleCells = l.filter(item => item.type === CellType.title && !item.notNeedRender).length;
                    res.push(titleCells);
                    return res;
                }, []);
                const maxTitleColNum = Math.max(...cellTypeCount);

                // 计算标题的合并单元格
                rows = rows.map(row => this.computedCellColSpan(row, maxTitleColNum));
                // 创建 检查人检查时间相关信息的单元格
                rows = this.createCheckInfoCell(this.itemsValue ,rows, contentConfig);
                // 设置背景色
                rows = this.setCellBackground(rows);
                // 清除不需要渲染的节点
                rows = this.clearNotNeedRenderNode(rows);
                console.log(rows)
                return rows;
            },

            getNormalItems(items) {
                return items.filter(it => !it.renderType || (it.renderType && it.renderType !== 'textarea'))
            },

            getItemsText(items) {
                return this.getNormalItems(items).map(item => {
                    if(!item.value) {
                        return '';
                    }
                    return `${(item.displayName || '')} ${(item.displayName || '') ? ':' : ''} ${item.useRenderValue ? item.renderValue : (item.value || '')} ${item.notDisplayUnit ? '' : (item.unit || '')}`
                }).filter(item => !!item).join('，');
            }
        }
    }
</script>

<style lang="scss">
* {
    box-sizing: border-box;
}
.eye-inspect-report-merge-table{
    //border-spacing: 4px 2px;
    //border-collapse: unset;
    //margin-left: -4px;
    //width: calc(100% + 8px);
    border-collapse: collapse;
    border: 1px solid #A6A6A6;
    width: 100%;

    //a4 下字体变大，默认 a5 的字体大小
    &.page-is-a4 {
        //border-spacing: 4pt 2pt;

        thead {
            .table-cell {
                font-size: 10pt;
                line-height: 16pt; /* 120% */
            }
        }

        .table-cell {
            font-size: 10pt;
            line-height: 12pt;
        }
    }

    thead {
        .table-cell {
            color: #FFFFFF;
            font-size: 10px;
            font-style: normal;
            font-weight: 700;
        }
    }

    .table-cell {
        border-right: 1px solid #A6A6A6;
        border-bottom: 1px solid #A6A6A6;
        font-size: 10px;
        font-weight: 400;
        color: #000000;
        text-align: center;
        height: 1px;

        .item-list-wrapper {
            display: flex;
            justify-content: space-between;
            width: 100%;
            height: 100%;

            .item {
                text-align: left;
                flex: 1;

                .eye-result-cell {
                    display: flex;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    height: 100%;
                    word-break: break-all;

                    .eye-result-cell-content {
                        padding: 6pt;

                        &.text-content {
                            line-height: 16pt;
                        }
                    }

                }
            }

            .td-split-line {
                width: 1px;
                height: 100%;
                background-color: #A6A6A6;
            ;
            }
        }

    }
}
</style>