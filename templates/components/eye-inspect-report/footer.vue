<template>
    <div data-type="footer">
        <abc-print-space :value="8"></abc-print-space>
        <div class="eye-inspect-footer-wrapper">
            <div
                v-if="footerConfig.optometrist || footerConfig.reporter || footerConfig.seller || footerConfig.createdTime || footerConfig.printTime"
                class="bottom-field-list-wrapper"
            >
                <div
                    v-if="footerConfig.optometrist"
                    class="bottom-field-item"
                    style="width: 39.33%"
                >
                    <span>视光师：</span>
                </div>
                <div
                    v-if="footerConfig.reporter"
                    class="bottom-field-item"
                    style="width: 40.66%"
                >
                    <span>报告人：</span>
                </div>
                <div
                    v-if="footerConfig.seller"
                    class="bottom-field-item"
                    style="width: 20%"
                >
                    <span>开单人：</span>
                    <span>{{ printData.sellerName }}</span>
                </div>

                <div
                    v-if="footerConfig.createdTime"
                    class="bottom-field-item"
                    style="width: 39.33%"
                >
                    <span>开单时间：</span>
                    <span>{{ formatDate(printData.created, 'YYYY-MM-DD HH:mm') }}</span>
                </div>

                <div
                    v-if="footerConfig.printTime"
                    class="bottom-field-item"
                    style="width: 40.66%"
                >
                    <span>打印时间：</span>
                    <span>{{ formatDate(Date.now(), 'YYYY-MM-DD HH:mm') }}</span>
                </div>
            </div>

            <div
                class="bottom-remark-box"
            >
                <div
                    v-if="footerConfig.remark"
                    class="remark-content"
                    :style="{
                        width: footerConfig.telephone || footerConfig.address ? '50%' : '100%'
                    }"
                >
                    {{ footerConfig.remark }}
                </div>

                <div
                    v-if="footerConfig.telephone || footerConfig.address"
                    class="address"
                >
                    <div v-if="footerConfig.telephone">
                        <span>电话：</span>
                        <span>
                            {{ organInfo.contactPhone }}
                        </span>
                    </div>
                    <div v-if="footerConfig.address">
                        <span>地址：</span>
                        <span>
                            {{ address }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {formatDate} from "@tool/date";
    import AbcPrintSpace from "../layout/space.vue";

    export default {
        name: 'EyeInspectFooter',
        components: {AbcPrintSpace},

        props: {
            printData: {
                type: Object,
                default: () => ({}),
            },
            printConfig: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            footerConfig(){
                return this.printConfig.footer || {};
            },

            organInfo() {
                return this.printData?.organPrintView || {};
            },

            address() {
                const {
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                    addressDetail,
                } = this.organInfo;
                return addressProvinceName + addressCityName + addressDistrictName + addressDetail;
            }
        },

        methods: {formatDate},
    }
</script>

<style lang="scss">
.eye-inspect-footer-wrapper {
    .bottom-field-list-wrapper {
        border-top: 0.5px solid #A6A6A6;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        color: #000000;
        font-family: "Microsoft YaHei UI";
        font-size: 10pt;
        font-style: normal;
        font-weight: 350;
        line-height: 12pt; /* 120% */

        .bottom-field-item {
            width: 33.33%;
           padding-top: 10pt;

            &:nth-child(2) {
                padding-left: 20pt;
            }
            &:nth-child(6) {
                padding-left: 20pt;
            }

            &:nth-child(3n) {
                text-align: right;
            }
        }
    }

    .bottom-remark-box {
        padding-top: 14pt;
        font-size: 8pt;
        font-weight: 300;
        display: flex;
        justify-content: space-between;

        .remark-content {
            padding-right: 20px;
            width: 50%;
        }

        .address {
            text-align: right;
            width: 50%;
        }
    }
}
</style>