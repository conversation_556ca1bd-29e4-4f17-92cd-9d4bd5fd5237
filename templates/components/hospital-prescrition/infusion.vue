<template>
    <div
        class="infusion-item-wrapper"
        data-type="group"
    >
        <div class="group-col left-col ie-group-col">
            <span
                v-if="groupId"
                class="group-number-icon"
            >
                {{ NUMBER_ICONS[groupId] }}
            </span>
            <div
                v-for="(formItem, index) in goods"
                :key="`${index}-item`"
                :class="{ 'first-item': !index}"
                class="infusion-group-item"
                data-type="item"
            >
                <print-row>
                    <print-col
                        :span="getOtherInfo(formItem.id).astFlag ? 17 : 20"
                        class="item-wrapper"
                        overflow
                    >
                        <div
                            class="item-base-info"
                            :class="{'has-group-id': !!groupId}"
                        >
                            <div
                                v-if="contentConfig.medicineTradeName"
                                class="group-name"
                                style="max-width: 52%;"
                                overflow
                            >
                                {{ formItem.name || formItem.goodsName }}
                            </div>
                            <div
                                v-else
                                class="group-name"
                                style="max-width: 52%;"
                                overflow
                            >
                                {{ formItem.name || formItem.goodsName }}<template v-if="formItem.medicineCadn">
                                    ({{ formItem.medicineCadn }})
                                </template>
                            </div>

                            <div
                                v-if="contentConfig.westernMedicineSpec"
                                class="group-spec"
                            >
                                <template v-if="contentConfig.westernMedicineSpec">
                                    ({{ handleSpec(formItem) }})
                                </template>
                            </div>
                        </div>
                    </print-col>

                    <print-col
                        v-if="getOtherInfo(formItem.id).astFlag"
                        :span="3"
                        class="group-name"
                        overflow
                    >
                        {{ getOtherInfo(formItem.id).astFlag | formatAst }}
                        <template v-if="getOtherInfo(formItem.id).astFlag === 1">
                            ({{ getOtherInfo(formItem.id).astResult | formatAstResult }})
                        </template>
                    </print-col>

                    <print-col
                        :span="4"
                        class="dosage-count"
                    >
                        <span :class="{'has-number-icon': !!groupId}">
                            {{ getOtherInfo(formItem.id).singleDosageCount || getOtherInfo(formItem.id).doseCount }}{{ getOtherInfo(formItem.id).singleDosageUnit || getOtherInfo(formItem.id).dosageUnit }}
                        </span>
                    </print-col>
                </print-row>
                <print-row
                    class="usage-info"
                    style=" box-sizing: border-box; padding-right: 12pt;"
                >
                    <print-col
                        :span="24"
                        :style="{
                            'padding-left': !!groupId ? '28pt' : '12pt'
                        }"
                        style=" overflow: hidden; white-space: nowrap;"
                    >
                        <span v-if="contentConfig.westernPosition && getCurrGoodsItem(formItem).position">
                            {{ getCurrGoodsItem(formItem).position }}
                        </span>
                        <span v-if="contentConfig.westernPosition && getCurrGoodsItem(formItem).position && contentConfig.westernManufacturer && getCurrGoodsItem(formItem).manufacturerFull">
                            /
                        </span>
                        <span v-if="contentConfig.westernManufacturer && getCurrGoodsItem(formItem).manufacturerFull">
                            {{ getCurrGoodsItem(formItem).manufacturerFull }}
                        </span>
                        <span
                            v-if="((contentConfig.westernPosition && getCurrGoodsItem(formItem).position) || (contentConfig.westernManufacturer && getCurrGoodsItem(formItem).manufacturerFull)) &&
                                (getOtherInfo(formItem.id).requirement || getOtherInfo(formItem.id).remark)"
                        >
                            /
                        </span>
                        <span v-if="getOtherInfo(formItem.id).requirement">备注：{{ getOtherInfo(formItem.id).requirement }}</span>
                        <span v-if="getOtherInfo(formItem.id).remark">备注：{{ getOtherInfo(formItem.id).remark }}</span>
                    </print-col>
                </print-row>
            </div>

            <div
                class="group-line"
                :style="getGroupStyle"
            ></div>
        </div>
        <div
            class="group-col right-col"
        >
            <template>
                <print-row>
                    <print-col
                        :span="24"
                        class="usage"
                    >
                        {{ getOtherInfo(goods[0].id).freq && freqFormat(getOtherInfo(goods[0].id).freq, contentConfig.medicalLatin) }}
                        <template v-if="contentConfig.westernMedicineDays && getDaysByAdvice(goods[0].id, otherInfo)">
                            {{ getDaysByAdvice(goods[0].id, otherInfo) }}天
                        </template>
                    </print-col>
                </print-row>
                <print-row>
                    <print-col
                        :span="24"
                        class="usage"
                    >
                        {{ getOtherInfo(goods[0].id).usage && usageFormat(getOtherInfo(goods[0].id).usage, contentConfig.medicalLatin) }}
                        <template v-if="getOtherInfo(goods[0].id).ivgtt">
                            {{ getOtherInfo(goods[0].id).ivgtt }}滴/分钟
                        </template>
                    </print-col>
                </print-row>
            </template>
        </div>
    </div>
</template>

<script>
    import PrintCol from '../layout/print-col.vue';
    import PrintRow from '../layout/print-row.vue';
    import {formatAst, formatAstResult, freqFormat, usageFormat} from "../../common/medical-transformat";
    import {formatGoodsDosageSpec, getDaysByAdvice, handleSpec} from "../../common/utils";
    import {NUMBER_ICONS} from "../../common/constants";
    import {OutpatientChargeTypeEnum} from "../../constant/print-constant";

    export default {
        name: 'HospitalPrescriptionInfusion',
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            formatAst,
            formatAstResult,
            formatGoodsDosageSpec
        },
        props: {
            goods: {
                type: Array,
                required: true,
            },
            groupId: {
                type: Number,
                default: 1,
            },
            config: Object,
            showDays: {
                type: Boolean,
                default: false,
            },
            otherInfo: {
                type: Object,
                default: ()=> {
                    return {}
                },
            },
            formType: {
                type: String,
                default: 'prescription'
            }
        },
        data() {
            return {
                NUMBER_ICONS,
                OutpatientChargeTypeEnum,
            }
        },
        computed: {
            getGroupStyle() {
                const len = this.goods.length;
                return {
                    top: len === 1 ? '0' : '6pt',
                    height: 'calc(100% - 16pt)'
                }
            },
            contentConfig() {
                return this.config && this.config.content || {};
            },
            isExecuteType() {
                return this.formType === 'infusionExecute';
            },
            showTotalCount() {
                return !this.isExecuteType || this.config.content.medicineTotalCount;
            },
        },
        methods: {
            getDaysByAdvice,
            handleSpec,
            freqFormat,
            usageFormat,
            getOtherInfo(id) {
                return this.otherInfo[id] || {};
            },
            showXIcon(formItem) {
                const spec = formatGoodsDosageSpec(formItem.productInfo);
                return spec && (this.contentConfig.westernMedicineSpec || this.contentConfig.medicineSpec)
            },
            getCurrGoodsItem(item) {
                return item.currGoodsItem || item.productInfo || {};
            },
        }
    }
</script>

<style lang="scss">
.infusion-item-wrapper {
    padding-bottom: 16pt;
    font-size: 0;
    font-weight: 300;
    vertical-align: middle;

    .number-icon {
        display: inline-block;
        width: 16pt;
        font-size: 10pt;
        line-height: 12pt;
        vertical-align: middle;
    }

    .group-number-icon {
        position: absolute;
        top: 0;
        z-index: 1;
        width: 16pt;
        font-size: 10pt;
        line-height: 12pt;
    }

    .item-wrapper,
    .item-base-info {
        font-size: 0;
    }

    .item-wrapper {
        white-space: nowrap;
    }

    .item-base-info {
        box-sizing: border-box;
        overflow: hidden;
        white-space: nowrap;

        &.has-group-id {
            padding-left: 16pt;
        }

        &.no-total-count {
            width: 100%;
            max-width: 100%;
        }
    }

    .infusion-remark {
        font-size: 9pt;

        &.has-number-icon {
            padding-left: 16pt;
        }
    }

    .good-name,
    .group-name,
    .group-spec,
    .usage-info,
    .dosage-count,
    .usage {
        display: inline-block;
        overflow: hidden;
        font-size: 10pt;
        line-height: 12pt;
        word-break: keep-all;
        white-space: nowrap;
        vertical-align: middle;
    }

    .infusion-group-item {
        padding-top: 6pt;

        &.first-item {
            padding-top: 0;
        }
    }

    .usage-info {
        padding-top: 2pt;
    }

    .dosage-count {
        padding-right: 12pt;
        text-align: right;
    }

    .group-col {
        display: inline-block;
        vertical-align: middle;
    }

    .left-col {
        position: relative;
        width: 75%;

        .group-line {
            position: absolute;
            top: 0;
            right: 8pt;
            width: 2pt;
            height: 100%;
            min-height: 12pt;
            content: '';
            border-top: 1px solid #000000;
            border-right: 1pt solid #000000;
            border-bottom: 1px solid #000000;
        }
    }

    .right-col {
        width: 25%;
    }

    .infusion-sign-name {
        top: -1pt;
        display: inline-block;
        width: auto;
        height: 14pt;
        margin-left: 4pt;
        font-size: 6pt;

        img {
            width: auto;
            height: 14pt;
            border: 0;
        }
    }
}

</style>
