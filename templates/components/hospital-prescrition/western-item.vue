<template>
    <div
        class="hospital-prescription-western-item-wrapper"
        data-type="group"
    >
        <print-row
            class="hospital-prescription-name"
            data-type="item"
        >
            <print-col
                :span="1"
                class="hospital-prescription-number-icon"
                :style="{ visibility: good.groupSort ? 'visible' : 'hidden' }"
            >
                <template v-if="good.groupSort">
                    {{ NUMBER_ICONS[good.groupSort] }}
                </template>
                <!-- print-col 内容为空会没有宽度, 导致列无法对齐, 这里给一个默认值同时设置 visibility: hidden 来撑开宽度 -->
                <template v-else>
                    {{ NUMBER_ICONS[1] }}
                </template>
            </print-col>
            <print-col
                :span="getGoodsNameSpan"
                class="hospital-prescription-spec"
                overflow
            >
                <span>
                    {{ good.goodsName || good.name }}
                    <template v-if="contentConfig.medicineTradeName && good.medicineCadn">
                        ({{ good.medicineCadn }})
                    </template>
                </span>
            </print-col>
            <print-col
                v-if="contentConfig.westernMedicineSpec"
                :span="6"
                class="hospital-prescription-spec"
                style="text-align: right;"
                overflow
            >
                {{ handleSpec(good) }}
            </print-col>
            <print-col
                :span="3"
                class="hospital-prescription-spec"
                style="text-align: right;"
                overflow
            >
                <template v-if="otherInfo[good.id] && otherInfo[good.id].chargeFlag === OutpatientChargeTypeEnum.NO_CHARGE">
                    [自备]
                </template><template v-else>
                    ×
                </template>{{ good.unitCount }}{{ good.unit }}
            </print-col>
            <print-col
                v-if="contentConfig.westernMedicineDays && getDaysByAdvice(good.id, otherInfo)"
                :span="2"
                class="hospital-prescription-spec"
                style="text-align: right;"
            >
                {{ getDaysByAdvice(good.id, otherInfo) }}天
            </print-col>
        </print-row>
        <print-row data-type="item">
            <print-col
                :span="24"
                class="hospital-prescription-usage-info"
                overflow
            >
                <!-- 医嘱和药房数据接口不一致, 需要分开处理 -->
                {{ contentConfig.medicalLatin ? 'Sig' : '用法' }}：<template v-if="otherInfo[good.id] && otherInfo[good.id].singleDosageCount && otherInfo[good.id].singleDosageUnit">
                    每次{{ otherInfo[good.id].singleDosageCount }}{{ otherInfo[good.id].singleDosageUnit }}
                </template><template v-else-if="good.otherInfo && good.otherInfo.doseCount && good.otherInfo.dosageUnit">
                    每次{{ good.otherInfo.doseCount }}{{ good.otherInfo.dosageUnit }}
                </template>
                {{ otherInfo[good.id] && freqFormat(otherInfo[good.id].freq, contentConfig.medicalLatin) }}
                {{ otherInfo[good.id] && usageFormat(otherInfo[good.id].usage, contentConfig.medicalLatin) }}
                <template v-if="otherInfo[good.id]">
                    <template v-if="otherInfo[good.id].remark">
                        备注：{{ otherInfo[good.id].remark }}
                    </template><template v-else-if="otherInfo[good.id].requirement">
                        备注：{{ otherInfo[good.id].requirement }}
                    </template>
                </template>
            </print-col>
        </print-row>
        <print-row data-type="item">
            <print-col
                :span="24"
                class="hospital-prescription-usage-info"
            >
                <template v-if="contentConfig.westernPosition && currGoodsItem.position">
                    {{ currGoodsItem.position }}
                </template>
                <template v-if="contentConfig.westernPosition && currGoodsItem.position && contentConfig.westernManufacturer && currGoodsItem.manufacturerFull">
                    /
                </template>
                <template v-if="contentConfig.westernManufacturer && currGoodsItem.manufacturerFull">
                    {{ currGoodsItem.manufacturerFull }}
                </template>
            </print-col>
        </print-row>
    </div>
</template>

<script>
    import PrintRow from "../layout/print-row.vue";
    import PrintCol from "../layout/print-col.vue";
    import {NUMBER_ICONS} from "../../common/constants";
    import {freqFormat, usageFormat} from "../../common/medical-transformat";
    import {OutpatientChargeTypeEnum} from "../../constant/print-constant";
    import {getDaysByAdvice, handleSpec} from "../../common/utils";

    export default {
        name: 'WesternItem',
        components: {PrintCol, PrintRow},
        props: {
            good: {
                type: Object,
                default: () => ({}),
            },
            headerConfig: {
                type: Object,
                default: () => ({}),
            },
            contentConfig: {
                type: Object,
                default: () => ({}),
            },
            otherInfo: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                NUMBER_ICONS,
                OutpatientChargeTypeEnum,
            }
        },
        computed: {
            getGoodsNameSpan() {
                if (this.contentConfig.westernMedicineSpec && this.contentConfig.westernMedicineDays) return 12;
                if (this.contentConfig.westernMedicineSpec) return 14;
                if (this.contentConfig.westernMedicineDays) return 18;
                return 20;
            },
            currGoodsItem() {
                return this.good.currGoodsItem || this.good.productInfo || {};
            },
        },
        methods: {
            getDaysByAdvice,
            freqFormat,
            usageFormat,
            handleSpec,
        },
    }
</script>

<style lang="scss">
.hospital-prescription-western-item-wrapper {
    padding-bottom: 12pt;

    .hospital-prescription-name {
        line-height: 12pt;
    }

    .hospital-prescription-number-icon {
        display: inline-block;
        font-size: 10pt;
        line-height: 12pt;
        vertical-align: top;
    }

    .hospital-prescription-spec {
        overflow: hidden;
        font-size: 10pt;
        line-height: 12pt;
        word-break: keep-all;
        white-space: nowrap;
    }

    .hospital-prescription-usage-info {
        padding: 2pt 0 0 36pt;
        overflow: hidden;
        font-size: 10pt;
        font-weight: 300;
        line-height: 12pt;
        white-space: nowrap;
    }
}
</style>
