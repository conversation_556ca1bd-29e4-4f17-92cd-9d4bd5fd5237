<template>
    <div class="hospital-prescription-footer">
        <!-- 大额处方签名 -->
        <div
            v-if="footerConfig.billSign"
            class="hospital-prescription-big-fee-sign"
        >
            <div style="display: inline-block;">
                大额处方意见：
            </div>
            <div class="hospital-prescription-sign-check-box"></div>
            <div class="hospital-prescription-sign-text">
                同意
            </div>
            <div class="hospital-prescription-sign-text">
                签名：
            </div>
        </div>
        
        <!-- 处方签名 -->
        <div class="hospital-prescription-footer-wrapper">
            <!-- 签名 -->
            <div class="hospital-prescription-sign-wrapper">
                <div class="hospital-prescription-check-sign-info">
                    医生：<img
                        v-if="doctorInfo.createdByHandSign && isImgUrl(doctorInfo.createdByHandSign)"
                        :src="doctorInfo.createdByHandSign"
                        :alt="doctorInfo.createdByName"
                        class="hospital-prescription-sign-img"
                    /><template v-else-if="doctorInfo.createdByName">
                        {{ doctorInfo.createdByName }}
                    </template><template v-else-if="doctorInfo.doctorName">
                        {{ doctorInfo.doctorName }}
                    </template>
                </div>
                <div
                    v-if="footerConfig.check"
                    class="hospital-prescription-check-sign-info"
                >
                    审核：
                </div>
                <div
                    v-if="footerConfig.assinger"
                    class="hospital-prescription-check-sign-info"
                >
                    调配：
                </div>
                <div
                    v-if="footerConfig.dispense"
                    class="hospital-prescription-check-sign-info"
                >
                    核发：
                </div>
            </div>
            <!-- 备注/打印时间 -->
            <div class="hospital-prescription-remark-info">
                <div
                    v-if="footerConfig.remark"
                    v-html="footerConfig.remark"
                ></div>
                <div v-if="footerConfig.printDate">
                    打印时间：{{ new Date() | parseTime('y-m-d h:i:s') }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { isImgUrl, parseTime } from "../../common/utils";

    export default {
        name: 'HospitalPrescriptionFooter',
        filters: {
            parseTime,
        },
        props: {
            footerConfig: {
                type: Object,
                default() {
                    return {};
                },
            },
            doctorInfo: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        methods: { isImgUrl },
    }
</script>

<style lang="scss">
.hospital-prescription-footer {
    .hospital-prescription-big-fee-sign {
        box-sizing: border-box;
        width: 100%;
        padding-right: 69pt;
        padding-bottom: 7pt;
        font-family: MicrosoftYaHei;
        font-size: 10pt;
        line-height: 12pt;
        color: #000000;
        text-align: right;
    }

    .hospital-prescription-sign-check-box {
        position: relative;
        top: 1pt;
        display: inline-block;
        width: 8pt;
        height: 8pt;
        border: 1px solid #000000;
        border-radius: 2pt;
    }

    .hospital-prescription-sign-text {
        display: inline-block;
        margin-left: 4pt;
    }

    .hospital-prescription-footer-wrapper {
        width: 100%;
        border-top: 1px solid #000000;
    }

    .hospital-prescription-sign-wrapper {
        display: flex;
        width: 100%;
        font-size: 0;
    }

    .hospital-prescription-check-sign-info {
        display: flex;
        width: 25%;
        font-family: MicrosoftYaHei;
        font-size: 10pt;
        line-height: 26pt;
        color: #000000;
    }

    .hospital-prescription-sign-img {
        width: 68pt;
        height: 26pt;
        object-fit: contain;
    }

    .hospital-prescription-remark-info {
        font-family: MicrosoftYaHei;
        font-size: 8pt;
        line-height: 10pt;
        color: #000000;
    }
}
</style>
