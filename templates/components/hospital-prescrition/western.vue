<template>
    <div
        class="hospital-prescription-western-wrapper"
        data-type="mix-box"
    >
        <template v-if="contentConfig.standardKindCount">
            <template v-for="(splitGoods, splitGoodsIndex) in handleStandardKindCountGoods">
                <template v-for="(good, goodIndex) in splitGoods">
                    <western-item
                        :key="`${splitGoodsIndex}-${goodIndex}-WP-standard-item`"
                        :good="good"
                        :header-config="headerConfig"
                        :content-config="contentConfig"
                        :other-info="otherInfo"
                    ></western-item>
                </template>
                <div
                    v-if="splitGoodsIndex === splitGoods.length - 1"
                    data-type="new-page"
                ></div>
            </template>
        </template>
        <template v-else>
            <template v-for="(good, goodIndex) in handleStandardKindCountGoods">
                <western-item
                    :key="`${goodIndex}-WP-item`"
                    :good="good"
                    :header-config="headerConfig"
                    :content-config="contentConfig"
                    :other-info="otherInfo"
                ></western-item>
            </template>
        </template>
    </div>
</template>

<script>
    import clone from "../../common/clone";
    import {NUMBER_ICONS} from "../../common/constants";
    import {freqFormat, usageFormat} from "../../common/medical-transformat";
    import WesternItem from "./western-item.vue";

    export default {
        name: 'HospitalPrescriptionWestern',
        components: {
            WesternItem,
        },
        props: {
            western: {
                type: Object,
                default: () => ({}),
            },
            headerConfig: {
                type: Object,
                default: () => ({}),
            },
            contentConfig: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                NUMBER_ICONS,
            }
        },
        computed: {
            goods() {
                return this.western.goods || [];
            },
            otherInfo() {
                return this.western.otherInfo || {};
            },
            // 分组后的 goods
            groupedGoods() {
                const goods = clone(this.goods);
                // 先从 otherInfo 中把每个西药的分组 id 取出来
                goods.forEach(good => {
                    good.groupId = this.otherInfo[good.id]?.groupId;
                });
                let sort = 1;
                goods.forEach((good, goodIndex) => {
                    if (goodIndex === 0 || good.groupId !== goods[goodIndex - 1].groupId) {
                        good.groupSort = sort++;
                    }
                });
                return goods;
            },
            handleStandardKindCountGoods() {
                const groupedGoods = clone(this.groupedGoods);
                if (!this.contentConfig.standardKindCount) {
                    return groupedGoods;
                }
                return this.handleSplitGoods(groupedGoods, 5, false);
            },
        },
        methods: {
            freqFormat,
            usageFormat,
            handleSplitGoods(goods, count = 5, isRepeat = true) {
                let cacheGoods = clone(goods);
                let len = goods.length;
                let res = [];
                if(isRepeat) {
                    // 不考虑去重，直接按照数量切分
                    let index = 0;
                    while(index < len) {
                        let splitGoods = cacheGoods.slice(index, index += count);
                        res.push(splitGoods);
                    }
                } else {
                    let groupSet = new Set();
                    let group = [];
                    cacheGoods.forEach(good => {
                        groupSet.add(good.id);
                        if(groupSet.size < 6) {
                            group.push(good);
                        } else {
                            res.push(group);
                            groupSet.clear();
                            groupSet.add(good.id);
                            group = [];
                            group.push(good);
                        }
                    })
                    if(group && group.length) {
                        res.push(group);
                    }
                }
                return res;
            },
        },
    }
</script>

<style lang="scss">
.hospital-prescription-western-wrapper {}
</style>
