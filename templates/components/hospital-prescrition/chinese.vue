<template>
    <div data-type="group">
        <print-row
            v-for="(goodsList, goodsListIndex) in splitGoods"
            :key="goodsListIndex"
            class="hospital-chinese-item"
            data-type="item"
        >
            <print-col
                v-for="(item, index) in goodsList"
                :key="`${item.id}-${index}`"
                :span="isShowAllCount ? 8 : 6"
                class="chinese-col"
            >
                <print-row>
                    <print-col
                        class="special-position"
                        :span="24"
                    >
                        <span v-if="contentConfig.chinesePosition && getCurrGoodsItem(item).position">
                            {{ getCurrGoodsItem(item).position }}
                        </span>
                        <span v-if="item.chargeFlag === OutpatientChargeTypeEnum.NO_CHARGE">
                            【自备】
                        </span>
                        <span v-else-if="item.remark">
                            [{{ item.remark }}]
                        </span>
                        <span v-else-if="item.usageInfo && item.usageInfo.specialRequirement">
                            {{ item.usageInfo.specialRequirement }}
                        </span>
                        <span
                            v-else
                            style="visibility: hidden;"
                        >
                            备注
                        </span>
                    </print-col>
                </print-row>
                <print-row>
                    <print-col
                        :span="24"
                        style="display: flex; align-items: flex-start;"
                    >
                        <span class="chinese-name chinese-item-name">{{ item.name || item.goodsName }}</span>
                        <span class="chinese-name max-chinese-count">
                            <template v-if="contentConfig.chineseSingleDoseEachItemCount">
                                {{ item.unitCount }}{{ item.unit || 'g' }}
                            </template>
                            <template v-if="isShowAllCount">
                                /
                            </template>
                            <template v-if="contentConfig.chineseTotalDoseEachItemCount">
                                {{ getTotalMedCount(item.unitCount) }}{{ item.unit || 'g' }}
                            </template>
                        </span>

                        <template v-if="item.verifySignatureStatus && item.verifySignatory">
                            <img
                                v-if="item.verifySignatory.handSign && isImgUrl(item.verifySignatory.handSign)"
                                :src="item.verifySignatory.handSign"
                                alt=""
                                class="verify-signatory verify-signatory-img"
                            />
                            <span
                                v-else-if="item.verifySignatory.name"
                                class="verify-signatory verify-signatory-text"
                            >{{ item.verifySignatory.name }}</span>
                        </template>
                    </print-col>
                </print-row>
            </print-col>
        </print-row>
    </div>
</template>

<script>
    import PrintCol from '../layout/print-col.vue';
    import PrintRow from '../layout/print-row.vue';
    import { OutpatientChargeTypeEnum } from "../../constant/print-constant";
    import Big from 'big.js';
    import { isImgUrl } from '../../common/utils';

    export default {
        name: 'HospitalPrescriptionChinese',
        components: {
            PrintCol,
            PrintRow,
        },
        props: {
            goods: {
                type: Array,
                required: true,
            },
            config: Object,
            span: {
                type: Number,
                default: 6,
            },
            groupCount: {
                type: Number,
                default: 4,
            },
            chineseOtherInfo: {
                type: Object,
                default: ()=> {
                    return {}
                },
            },
        },
        data() {
            return {
                OutpatientChargeTypeEnum,
            }
        },
        computed: {
            isShowAllCount() {
                return this.contentConfig.chineseSingleDoseEachItemCount && this.contentConfig.chineseTotalDoseEachItemCount;
            },
            splitGoods() {
                const splitNum = this.isShowAllCount ? 3 : 4;
                const cacheGoods = this.goods || [];
                const result = [];
                for (let i = 0; i < cacheGoods.length; i += splitNum) {
                    result.push(cacheGoods.slice(i, i + splitNum));
                }
                return result;
            },
            contentConfig() {
                return this.config && this.config.content || {};
            },
        },
        methods: {
            isImgUrl,
            getTotalMedCount(count) {
                if (Number.isNaN(Number(count))) return 0;

                const { doseCount, dosageCount } = this.chineseOtherInfo;

                if (doseCount && !Number.isNaN(Number(doseCount))) {
                    return Big(count).mul(Big(doseCount)).toNumber();
                }
                if (dosageCount && !Number.isNaN(Number(dosageCount))) {
                    return Big(count).mul(Big(dosageCount)).toNumber();
                }
                return 0;
            },
            getCurrGoodsItem(item) {
                return item.currGoodsItem || item.productInfo || {};
            },
        },
    }
</script>

<style lang="scss">
.hospital-chinese-item {
    position: relative;
    width: 100%;

    .chinese-col {
        position: relative;
        height: 38pt;
        padding-right: 8px;
        overflow: hidden;
        font-size: 0;
    }

    .special-requirement {
        position: absolute;
        top: -10pt;
        left: 24pt;
        overflow: hidden;
        font-size: 10pt;
        font-weight: 300;
        white-space: nowrap;
        zoom: 0.8;
    }

    .special-position {
        overflow: hidden;
        font-size: 10pt;
        font-weight: 300;
        line-height: 1;
        white-space: nowrap;
        zoom: 0.8;
    }

    .chinese-name {
        z-index: 1;
        display: inline-block;
        *display: inline;
        font-size: 10.5pt;
        line-height: 14pt;
        zoom: 1;
    }

    .chinese-item-name {
        max-width: 53%;
        word-break: break-all;
        word-wrap: break-word;
    }

    .max-chinese-count {
        max-width: 45%;
        padding-left: 4pt;
        overflow: hidden;
        white-space: nowrap;
    }

    .verify-signatory {
        position: absolute;
        top: 100%;
        left: 53%;
    }

    .verify-signatory-img {
        width: 44px;
    }

    .verify-signatory-text {
        font-size: 12px;
        color: #aab4bf;
    }

    .sign-name {
        position: absolute;
        top: 12pt;
        left: 1pt;
        width: 50px;
        height: 32px;
    }

    .sign-img-list {
        top: -4pt;
        bottom: 0;
        width: 28pt;
        height: 18pt;
        text-align: left;
        outline: none;

        img {
            width: auto;
            max-width: 100%;
            height: auto;
            max-height: 100%;
            border: 0;
            border-color: #ffffff;
        }
    }

    .sign-text {
        position: absolute;
        left: 2pt;
        width: 47pt;
        font-size: 6pt;
        text-align: left;
    }
}
</style>
