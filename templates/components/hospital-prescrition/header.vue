<template>
    <div class="hospital-prescription-header">
        <div class="hospital-prescription-title-wrapper">
            <!-- logo -->
            <div
                v-if="headerConfig.logo"
                class="hospital-prescription-logo-wrapper"
            >
                <img
                    v-if="clinicInfo.logo"
                    :src="clinicInfo.logo"
                    alt=""
                    class="hospital-prescription-logo-img"
                />
            </div>

            <!-- 抬头 -->
            <div class="hospital-prescription-title-content-wrapper">
                <div
                    v-if="headerConfig.title"
                    :class="[headerConfig.subtitle ? 'hospital-prescription-title' : 'hospital-prescription-single-title']"
                >
                    {{ headerConfig.title }}
                    <br />
                    <template v-if="headerConfig.subtitle">
                        {{ headerConfig.subtitle }}
                    </template>
                </div>
                <div
                    v-else
                    :class="[clinicTitle.subtitle ? 'hospital-prescription-title' : 'hospital-prescription-single-title']"
                >
                    {{ clinicTitle.title }}
                    <br />
                    <template v-if="clinicTitle.subtitle">
                        {{ clinicTitle.subtitle }}
                    </template>
                </div>
            </div>

            <!-- 精麻logo -->
            <div
                v-if="tag"
                class="hospital-prescription-tag-wrapper"
            >
                <span class="hospital-prescription-tag-text">{{ HospitalPsychotropicNarcoticTypeLabelEnum[tag] }}</span>
            </div>
            <!-- 二维码 -->
            <div
                v-else-if="headerConfig.qrcode && barcodeSrc"
                class="hospital-prescription-barcode-wrapper"
            >
                <img
                    :src="barcodeSrc"
                    alt=""
                    class="hospital-prescription-barcode-img"
                />
            </div>
        </div>
        
        <!-- 处方类型副标题 -->
        <div class="hospital-prescription-title-type-wrapper">
            <!-- 中药展示饮片 -->
            处方笺<template v-if="type === 3">
                饮片
            </template>
        </div>
        
        <!-- 患者基本信息 -->
        <div class="hospital-prescription-patient-base-info">
            <div
                class="hospital-prescription-patient-base-info-text"
                style="width: 38%;"
                overflow
            >
                姓名：{{ patient.name }}
                {{ patient.sex }}
                {{ patient.age && patient.age.year }}岁
            </div>
            <div
                class="hospital-prescription-patient-base-info-text"
                style="width: 32%;"
                overflow
            >
                住院号：{{ patientInfo.no }}
            </div>
            <div
                class="hospital-prescription-patient-base-info-text hospital-prescription-last-col"
                overflow
            >
                日期：{{ printDate | parseTime('y-m-d') }}
            </div>
        </div>
        
        <div class="hospital-prescription-patient-info">
            <!-- 住院信息 -->
            <div class="hospital-prescription-in-hospital-info">
                <div
                    class="hospital-prescription-patient-base-info-text"
                    style="width: 38%;"
                >
                    科室：{{ patientInfo.departmentName }}
                </div>
                <div
                    class="hospital-prescription-patient-base-info-text"
                    style="width: 32%;"
                >
                    病区：{{ patientInfo.wardName }}/{{ patientInfo.bedNo }}床
                </div>
                <div
                    class="hospital-prescription-patient-base-info-text hospital-prescription-last-col"
                >
                    费别：{{ patientInfo.feeTypeName }}
                </div>
            </div>

            <!-- 就诊信息 -->
            <div class="hospital-prescription-in-hospital-info">
                <div
                    class="hospital-prescription-patient-base-info-text"
                    style=" box-sizing: border-box; width: 70%; padding-right: 10px;"
                >
                    <div style="overflow: hidden;">
                        诊断：{{ displayDiagnoses }}
                    </div>
                </div>
                <div
                    v-if="headerConfig.mobile"
                    class="hospital-prescription-patient-base-info-text hospital-prescription-last-col"
                    style="width: 30%;"
                >
                    手机号：{{ patient.mobile | filterMobileV2(headerConfig.mobileType) }}
                </div>
            </div>

            <!-- 患者信息 -->
            <div class="hospital-prescription-in-hospital-info">
                <div
                    v-if="headerConfig.idCard"
                    class="hospital-prescription-patient-base-info-text"
                    style="width: 38%;"
                >
                    {{ getIdCardTypeStr(patient.idCardType) }}：{{ patient.idCard }}
                </div>
                <div
                    v-if="headerConfig.computerCode"
                    class="hospital-prescription-patient-base-info-text"
                    style="width: 32%;"
                >
                    个人编号：{{ shebaoPaymentInfo.psnNo }}
                </div>
                <div
                    v-if="headerConfig.socialCode"
                    class="hospital-prescription-patient-base-info-text hospital-prescription-last-col"
                >
                    医保号：{{ shebaoPaymentInfo.cardId }}
                </div>
            </div>

            <!-- 地址单独占一行 -->
            <div class="hospital-prescription-in-hospital-info">
                <div
                    v-if="headerConfig.address"
                    class="hospital-prescription-patient-base-info-text"
                    style="width: 100%;"
                >
                    地址：{{ formatAddress(patient.address) }}
                </div>
            </div>
        </div>
        
        <!-- Rp -->
        <div class="hospital-prescription-content-title">
            Rp.
        </div>
    </div>
</template>

<script>
    import {HospitalPsychotropicNarcoticTypeLabelEnum} from "../../constant/print-constant";
    import {formatAddress, getIdCardTypeStr, getLengthWithFullCharacter, parseTime} from "../../common/utils";
    import {HospitalPrescriptionDiagnosisTypeEnum, TITLE_MAX_LENGTH} from "../../common/constants";
    import {filterMobileV2} from "../../common/medical-transformat";

    export default {
        name: 'HospitalPrescriptionHeader',
        filters: {
            parseTime,
            filterMobileV2,
        },
        props: {
            patientInfo: {
                type: Object,
                default: () => ({}),
            },
            clinicInfo: {
                type: Object,
                default: () => ({}),
            },
            headerConfig: {
                type: Object,
                default: () => ({}),
            },
            barcodeSrc: {
                type: String,
                default: '',
            },
            // eslint-disable-next-line vue/require-default-prop
            tag: {
                type: Number,
            },
            // 1 西药 2 输液 3 中药
            type: {
                type: Number,
                default: 1,
            },
            shebaoPayment: {
                type: Object,
                default: () => ({}),
            },
            printDate: {
                type: String,
                default: '',
            },
            diagnoses: {
                type: Array,
                default() {
                    return [];
                },
            },
        },
        data() {
            return {
                HospitalPsychotropicNarcoticTypeLabelEnum,
            }
        },
        computed: {
            clinicTitle() {
                const clinicName = this.clinicInfo.name || '';
                let title = clinicName, subtitle = '';
                const {
                    fullCharacterLength, splitLength,
                } = getLengthWithFullCharacter(clinicName, TITLE_MAX_LENGTH);
                if (fullCharacterLength > TITLE_MAX_LENGTH) {
                    title = clinicName.slice(0, splitLength);
                    subtitle = clinicName.slice(splitLength);
                }
                return { title, subtitle };
            },
            patient() {
                return this.patientInfo.patient || {};
            },
            displayDiagnoses() {
                return (this.diagnoses || []).filter((d) => d.type === HospitalPrescriptionDiagnosisTypeEnum.inHospital).map((d) => d.diseaseName).join('、');
            },
            shebaoPaymentInfo() {
                return this.shebaoPayment || {};
            },
        },
        methods: {
            getIdCardTypeStr,
            formatAddress},
    }
</script>

<style lang="scss">
.hospital-prescription-header {
    .hospital-prescription-title-wrapper {
        position: relative;
        width: 100%;
        height: 40pt;
    }

    .hospital-prescription-title-content-wrapper {
        width: 100%;
        height: 40pt;
        text-align: center;
    }

    .hospital-prescription-title {
        font-family: SimSun;
        font-size: 14pt;
        line-height: 20pt;
        color: #000000;
    }

    .hospital-prescription-single-title {
        font-family: SimSun;
        font-size: 16pt;
        line-height: 40pt;
        color: #000000;
    }

    .hospital-prescription-logo-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 90pt;
        height: 40pt;
        line-height: 40pt;
    }

    .hospital-prescription-logo-img {
        display: inline-block;
        width: auto;
        max-width: 100%;
        height: 100%;
        vertical-align: middle;
    }

    .hospital-prescription-barcode-wrapper {
        position: absolute;
        top: 0;
        right: 0;
        width: 40pt;
        height: 40pt;
    }

    .hospital-prescription-barcode-img {
        display: inline-block;
        width: 100%;
        height: 100%;
    }

    .hospital-prescription-tag-wrapper {
        position: absolute;
        top: 0;
        right: 0;
        width: 36pt;
        height: 40pt;
        line-height: 40pt;
    }

    .hospital-prescription-tag-text {
        box-sizing: border-box;
        display: inline-block;
        width: 100%;
        height: 23pt;
        line-height: 21pt;
        text-align: center;
        border: #1d1f21 solid 1pt;
    }

    .hospital-prescription-title-type-wrapper {
        width: 100%;
        height: 14pt;
        margin-top: 5pt;
        line-height: 1;
        text-align: center;
    }

    .hospital-prescription-patient-base-info {
        width: 100%;
        padding-bottom: 3pt;
        margin-top: 19pt;
        font-size: 0;
        border-bottom: 1px solid #000000;
    }

    .hospital-prescription-patient-base-info-text {
        display: inline-block;
        width: 28%;
        overflow: hidden;
        font-family: MicrosoftYaHei;
        font-size: 10pt;
        font-weight: 300;
        line-height: 18pt;
        color: #000000;
        white-space: nowrap;
    }

    .hospital-prescription-last-col {
        width: 30%;
    }

    .hospital-prescription-patient-info {
        width: 100%;
        padding: 3pt 0;
        border-bottom: 1px solid #000000;
    }

    .hospital-prescription-in-hospital-info {
        width: 100%;
        font-size: 0;
    }

    .hospital-prescription-content-title {
        padding: 7pt 0;
        font-family: MicrosoftYaHei;
        font-size: 14px;
        line-height: 1;
        color: #000000;
    }
}
</style>
