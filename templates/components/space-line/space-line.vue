<template>
    <span class="line">
        <template
            v-for="i in count"
        >
            <span :key="i">&ensp;</span>
        </template>
    </span>
</template>
  
  <script>
    export default {
        props: {
            count: {
                type: Number,
                required: true
            },
        }
    }
  </script>
  <style lang="scss">

    .line {
        text-decoration: underline;
    }
</style>