<template>
    <div data-type="group">
        <div
            v-for="{
                name, remark, suggest, value, textValue
            } in data"
            :key="name"
            class="card-quota"
            data-type="item"
        >
            <div
                v-if="textValue || value"
                class="card-quota-name"
            >
                {{ name }}：{{ textValue || value }}
            </div>
            <div
                v-if="remark"
                class="card-quota-description"
            >
                结果：{{ remark }}
            </div>
            <div
                v-if="suggest"
                class="card-quota-description"
            >
                建议：{{ suggest }}
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'CardQuo<PERSON>',
        props: {
            data: {
                type: Array,
                default() {
                    return [];
                },
            },
        },
    };
</script>

<style lang="scss">

.card-quota {
    padding-bottom: 9pt;
    margin-bottom: 11pt;
    border-bottom: 1px dashed #000000;

    &:last-child {
        border-bottom: none;
    }

    &-name {
        min-height: 12pt;
        font-size: 11pt;
        font-weight: bold;
        line-height: 12pt;
    }

    &-level {
        min-height: 12pt;
        margin-bottom: 3pt;
        font-size: 11pt;
        line-height: 12pt;
    }

    &-description {
        margin-top: 3pt;
        font-size: 11pt;
        line-height: 12pt;
    }
}
</style>
