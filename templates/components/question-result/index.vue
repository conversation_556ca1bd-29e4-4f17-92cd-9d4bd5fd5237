<template>
    <div>
        <div>
            <card-quota :data="data.indicators || []"></card-quota>
        </div>
        <div
            v-for="r in data.formDataDetailResults || []"
            :key="r.questionName"
            style="margin-bottom: 12px;"
            class="question-result"
        >
            <result-table
                :table-header="[
                    {
                        prop: 'score',
                        label: '得分',
                    },
                    {
                        prop: 'result',
                        label: '评测结果',
                    },
                    {
                        prop: 'suggest',
                        label: '健康建议',
                    },
                ]"
                :data="r.groups || []"
            >
                <template #score="{ item }">
                    <span>{{ item.groupName }} {{ item.score }}分</span>
                    <span> 总分{{ item.totalScore }} </span>
                </template>
            </result-table>
        </div>
    </div>
</template>

<script>
    import ResultTable from './table.vue';
    import CardQuota from './card-quota.vue';
    export default {
        name: 'QuestionResult',
        components: {
            ResultTable,
            CardQuota,
        },
        props: {
            data: {
                type: Object,
            },
        },
    };
</script>
