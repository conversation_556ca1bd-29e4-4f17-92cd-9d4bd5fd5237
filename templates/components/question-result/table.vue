<template>
    <div class="table">
        <table>
            <thead>
                <tr>
                    <th
                        v-for="th in tableHeader"
                        :key="th.prop"
                    >
                        {{ th.label }}
                    </th>
                </tr>
            </thead>
            <tbody data-type="group">
                <tr
                    v-for="(row, index) in data"
                    :key="index"
                    data-type="item"
                >
                    <td
                        v-for="th in tableHeader"
                        :key="th.prop"
                    >
                        <slot
                            v-if="$scopedSlots[th.prop]"
                            :item="row"
                            :name="th.prop"
                        ></slot>
                        <span
                            v-else
                            :title="row[th.prop]"
                        >
                            {{ row[th.prop] }}
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
    export default {
        props: {
            tableHeader: {
                type: Array,
                default: () => [],
            },
            data: {
                type: Array,
                default: () => [],
            },
        },
    };
</script>

<style lang="scss">
.table {
    table {
        width: 100%;
        font-size: 10pt;
    }

    table,
    td,
    th {
        padding: 9pt;
        border: 1px solid #000000;
    }

    td {
        vertical-align: top;
        &:first-child {
            white-space: nowrap;
        }
    }
}
</style>
