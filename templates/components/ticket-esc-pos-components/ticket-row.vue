<template>
    <div
        class="ticket-row"
        :style="styles"
        :data-ticket-size="size"
        :data-ticket-bold="bold"
        :data-ticket-align="textAlign"
    >
        <slot></slot>
    </div>
</template>

<script>
    export default {
        name: 'TicketRow',
        inject: ['baseWidth'],
        props: {
            size: {
                type: String,
                default: 'normal',
                validator: (value) => ['normal', 'large'].includes(value),
            },
            bold: {
                type: Boolean,
                default: false,
            },
            textAlign: {
                type: String,
                default: 'left',
                validator: (value) => ['left', 'center', 'right'].includes(value),
            },
        },
        computed: {
            styles() {
                const fontSize = this.size === 'normal' ? 2 * this.baseWidth : 4 * this.baseWidth;
                return {
                    fontSize: `${fontSize}px`,
                    fontWeight: this.bold ? 'bold' : 'normal',
                    textAlign: this.textAlign,
                    justifyContent: this.textAlign === 'center' ? 'center' : this.textAlign === 'right' ? 'flex-end' : 'flex-start',
                };
            }
        },
    }
</script>

<style lang="scss">
.ticket-row {
    display: flex;
    width: 100%;
}
</style>
