<template>
    <div
        class="ticket-column"
        :style="styles"
        :data-ticket-size="size"
        :data-ticket-bold="bold"
        :data-ticket-align="textAlign"
        :overflow="!isLineBreak"
    >
        <slot></slot>
    </div>
</template>

<script>
    export default {
        name: 'TicketColumn',
        inject: ['baseWidth', 'characterCount'],
        props: {
            span: {
                type: Number,
                default() {
                    return this.characterCount || 32;
                },
            },
            paddingLeftSpan: {
                type: Number,
                default: 0,
            },
            size: {
                type: String,
                default: 'normal',
                validator: (value) => ['normal', 'large'].includes(value),
            },
            bold: {
                type: Boolean,
                default: false,
            },
            textAlign: {
                type: String,
                default: 'left',
                validator: (value) => ['left', 'center', 'right'].includes(value),
            },
            isLineBreak: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            styles() {
                const width = this.baseWidth * this.span;
                const fontSize = this.size === 'normal' ? 2 * this.baseWidth : 4 * this.baseWidth;
                const obj = {
                    width: `${width}px`,
                    maxWidth: `${width}px`,
                    minWidth: `${width}px`,
                    fontSize: `${fontSize}px`,
                    fontWeight: this.bold ? 'bold' : 'normal',
                    textAlign: this.textAlign,
                    paddingLeft: `${this.paddingLeftSpan * this.baseWidth}px`,
                };
                if (!this.isLineBreak) {
                    obj.whiteSpace = 'nowrap';
                    obj.overflow = 'hidden';
                }
                return obj;
            },
        },
    }
</script>

<style lang="scss">
.ticket-column {
    word-break: break-all;
    word-wrap: break-word;
    white-space: normal;
}
</style>
