<template>
    <img
        :src="src"
        alt=""
        crossorigin="anonymous"
        :style="styles"
    />
</template>

<script>
    export default {
        name: 'TicketImage',
        props: {
            src: {
                type: String,
                default: '',
            },
            width: {
                type: [Number, String],
                default: 120,
            },
            height: {
                type: [Number, String],
                default: null,
            },
        },
        computed: {
            styles() {
                const obj = {};
                if (typeof this.width === 'string' && this.width.endsWith('px')) {
                    obj.width = this.width;
                } else {
                    obj.width = `${this.width}px`;
                }
                if (this.height) {
                    if (typeof this.height === 'string' && this.height.endsWith('px')) {
                        obj.height = this.height;
                    } else {
                        obj.height = `${this.height}px`;
                    }
                }
                return obj;
            },
        },
    }
</script>
