<template>
    <div class="space-line-wrapper">
        <div
            class="margin-content"
            :style="{ 'height': `${topMargin}px` }"
        ></div>
        <div
            class="line-content"
            :style="{ 'border-top-width': `${lineHeight}px`, 'border-top': `1px ${lineType} #000000` }"
        ></div>
        <div
            class="margin-content"
            :style="{ 'height': `${bottomMargin}px` }"
        ></div>
    </div>
</template>

<script>
    export default {
        name: 'SpacingLine',
        props: {
            topMargin: {
                type: Number,
                default: 8,
            },
            bottomMargin: {
                type: Number,
                default: 8,
            },
            lineHeight: {
                type: Number,
                default: 1,
            },
            lineType: {
                type: String,
                default: 'dashed',
            }
        },
    }
</script>

<style lang="scss">
.space-line-wrapper {
    width: 100%;

    .margin-content {
        width: 100%;
        height: 8px;
    }

    .line-content {
        width: 100%;
        height: 0;
    }
}
</style>
