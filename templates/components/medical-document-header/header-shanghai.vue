<template>
    <div
        class="prescription-header-shanghai"
    >
        <div class="header-mark">
            <div class="no">
                No.
            </div>
            <div
                v-if="jinmaType"
                class="jinma-mark"
            >
                {{ jinmaType }}
            </div>
        </div>

        <div
            class="organ-name"
            :class="{'long-organ-name': curOrganTitle.length > 15}"
        >
            {{ curOrganTitle }}
            <span style="margint-left: 8px;">
                {{ printTitle }}
            </span>
        </div>

        <div class="outpatient-info">
            <print-row
                class="list-info"
                :gutter="2"
            >
                <print-col
                    :span="isChildPr ? 5 : 8"
                    overflow
                >
                    <row-box :label-width="34">
                        <span slot="label"> 姓名:</span>
                        <div slot="content">
                            <span class="space"></span>
                            {{ patient.name }}
                            <span class="space-big"></span>
                            <span class="space-big"></span>
                        </div>
                    </row-box>
                </print-col>
                <print-col
                    :span="isChildPr ? 6 : 7"
                    overflow
                >
                    <row-box
                        :label-width="40"
                        :underline="false"
                    >
                        <span slot="label">性别:</span>
                        <div
                            slot="content"
                            class="sex-section"
                        >
                            <span class="square-mark">
                                {{ patient.sex === '男' ? '&#x2713;' : '' }}
                            </span>
                            <span>男</span>
                            <span class="square-mark">
                                {{ patient.sex === '女' ? '&#x2713;' : '' }}
                            </span>
                            <span>女</span>
                        </div>
                    </row-box>
                </print-col>
                <print-col
                    :span="isChildPr ? 8 : 9"
                    overflow
                >
                    <div
                        class="patient-age"
                        style="justify-content: end"
                    >
                        <span>年龄:</span>
                        <span class="age-line">
                            {{ patient.age.year }}
                        </span>
                        <span>岁</span>
                        <span class="age-line">
                            {{ patient.age.month || '' }}
                        </span>
                        月
                        <span class="age-line">
                            {{ patient.age.day || '' }}
                        </span>
                        日
                    </div>
                </print-col>
                <print-col
                    v-if="isChildPr "
                    :span="5"
                    overflow
                >
                    <div
                        class="patient-age"
                        style="justify-content: end"
                    >
                        <span>体重</span>
                        <span class="age-line">
                            {{ patient.weight }}
                        </span>
                        千克
                    </div>
                </print-col>
            </print-row>
            <print-row class="list-info">
                <print-col :span="14">
                    <row-box :label-width="60">
                        <span slot="label">联系电话:</span>
                        <div slot="content">
                            {{ filterMobileV2(patient.mobile, headerConfig.mobile) }}
                        </div>
                    </row-box>
                </print-col>
            </print-row>

            <template v-if="isMaOrJING_1">
                <print-row
                    class="list-info"
                    :gutter="2"
                >
                    <print-col :span="8">
                        <row-box :label-width="60">
                            <span slot="label">身份证明:</span>
                            <div slot="content">
                            </div>
                        </row-box>
                    </print-col>
                    <print-col :span="16">
                        <row-box :label-width="40">
                            <span slot="label">编号:</span>
                            <div slot="content">
                            </div>
                        </row-box>
                    </print-col>
                </print-row>
                <div style="padding-top: 8px;">
                    <div class="ma-list-info">
                        <print-row
                            class="list-info"
                            :gutter="6"
                        >
                            <print-col :span="8">
                                <row-box :label-width="80">
                                    <span slot="label">代办人姓名:</span>
                                    <div slot="content">
                                    </div>
                                </row-box>
                            </print-col>
                            <print-col :span="16">
                                <row-box :label-width="60">
                                    <span slot="label">联系电话:</span>
                                    <div slot="content">
                                    </div>
                                </row-box>
                            </print-col>
                        </print-row>
                        <print-row
                            class="list-info"
                            :gutter="6"
                        >
                            <print-col :span="8">
                                <row-box :label-width="80">
                                    <span slot="label">代办人姓名:</span>
                                    <div slot="content">
                                    </div>
                                </row-box>
                            </print-col>
                            <print-col :span="16">
                                <row-box :label-width="60">
                                    <span slot="label">联系电话:</span>
                                    <div slot="content">
                                    </div>
                                </row-box>
                            </print-col>
                        </print-row>
                    </div>
                </div>
            </template>

            <print-row
                :gutter="2"
                class="list-info"
            >
                <print-col :span="10">
                    <row-box
                        :label-width="40"
                        :underline="false"
                    >
                        <span slot="label">费别:</span>
                        <div
                            slot="content"
                            class="sex-section"
                        >
                            <span class="square-mark">
                                {{ !!shebaoCardInfo ? '&#x2713;' : '' }}
                            </span>
                            <span>医保</span>
                            <span class="square-mark">
                                {{ !shebaoCardInfo ? '&#x2713;' : '' }}
                            </span>
                            <span>非医保</span>
                        </div>
                    </row-box>
                </print-col>
                <print-col :span="14">
                    <row-box
                        :label-width="88"
                    >
                        <span slot="label">医保就诊卡号:</span>
                        <div slot="content">
                            {{ printData.healthCardNo }}
                        </div>
                    </row-box>
                </print-col>
            </print-row>

            <print-row
                class="list-info"
                :gutter="2"
            >
                <print-col
                    :span="14"
                    overflow
                >
                    <row-box :label-width="114">
                        <span slot="label">门诊/住院病历号:</span>
                        <div slot="content">
                            <span class="space"></span>
                            {{ formatPatientOrderNo(printData.patientOrderNo) }}
                            <span class="space-big"></span>
                            <span class="space"></span>
                        </div>
                    </row-box>
                </print-col>
                <print-col
                    :span="10"
                    overflow
                >
                    <row-box :label-width="110">
                        <span slot="label">
                            科别/病区-床位号:
                        </span>
                        <div slot="content">
                            {{ printData.departmentName }}
                        </div>
                    </row-box>
                </print-col>
            </print-row>
            <print-row class="list-info">
                <print-col
                    :span="24"
                    overflow
                >
                    <row-box :label-width="60">
                        <span slot="label">
                            临床诊断:
                        </span>
                        <span slot="content">
                            <span class="space"></span>
                            {{ printData.diagnosis | filterDiagnose }}
                            <span v-if="printData.syndrome">（{{ printData.syndrome }}）</span>
                        </span>
                    </row-box>
                </print-col>
            </print-row>
            <print-row
                class="list-info"
                :gutter="2"
            >
                <print-col
                    :span="12"
                >
                    <row-box :label-width="60">
                        <span slot="label">
                            皮试结果:
                        </span>
                        <div slot="content">
                            <span class="space">
                                {{ astResult }}
                            </span>
                        </div>
                    </row-box>
                </print-col>
                <print-col
                    :span="12"
                >
                    <div
                        class="patient-age"
                        style="justify-content: end"
                    >
                        <span>开具日期:</span>
                        <span class="age-line">
                            {{ getYear }}
                        </span>
                        <span>年</span>
                        <span class="age-line">
                            {{ getMonth }}
                        </span>
                        月
                        <span class="age-line">
                            {{ getDay }}
                        </span>
                        日
                    </div>
                </print-col>
            </print-row>
        </div>
        <div
            class="rp-icon"
        >
            Rp
        </div>
    </div>
</template>

<script>
    import { formatAge, formatAddress, formatPatientOrderNo, parseTime, textToBase64BarCode } from '../../common/utils.js';
    import { filterMobile, filterMobileV2 } from "../../common/medical-transformat.js";
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';

    import { isDate } from '@tool/date';
    import { PsychotropicNarcoticTypeEnum, PsychotropicNarcoticTypeEnumStr } from "../../constant/print-constant";
    import RowBox from "./row-box.vue";

    export default {
        name: 'MedicalDocumentHeader',
        components: {
            PrintRow,
            PrintCol,
            RowBox,
        },
        filters: {
            filterDiagnose(val) {
                if (!val) return '';
                const valArr = val.split('<br>');
                return valArr.join('');
            },
            filterMobile,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            headerType: {
                type: String,
                default: '',
            },
            printTitle: {
                type: String,
                default: '治疗执行单',
            },
            organTitle: {
                type: String,
                default: '',
            },
            extendSpec: String,
            logoSrc: String,
            showRp: {
                type: Boolean,
                default: true,
            },

            isLandscape: {
                type: Boolean,
                default: false,
            },
            createdTime: {
                type: String,
                validator: (value) => {
                    return isDate(value)
                },
            },
            printJinma: {
                type: Number,
                default: 0,
            },
            astResult: {
                type: String,
                default: '',
            },
        },
        computed: {
            patient() {
                return this.printData.patient;
            },

            qrCodeSrc() {
                return this.printData.qrCode;
            },
            headerConfig() {
                return this.config.header || {};
            },
            curOrganTitle() {
                let title = '';
                if (!this.organTitle) {
                    title =  this.organ && this.organ.name || '';
                }
                title =  this.organTitle || '';
                if(title.length > 20) {
                    return title.substring(0, 20);
                }
                return title;
            },
            organ() {
                return this.printData.organ || '';
            },
            shebaoCardInfo() {
                return this.printData.shebaoCardInfo || null;
            },
            syndrome() {
                return this.printData.medicalRecord && this.printData.medicalRecord.syndrome || '';
            },
            barcodeSrc() {
                return textToBase64BarCode(this.printData.barcode);
            },
            curDate() {
                if (this.createdTime) {
                    return this.createdTime;
                }

                return this.printData.diagnosedDate;
            },
            getYear() {
                return this.curDate && this.curDate.split('-')[0] || ''
            },
            getMonth() {
                return this.curDate && this.curDate.split('-')[1] || ''
            },
            getDay() {
                const date = this.curDate && this.curDate.split('-')[2]
                if (date) {
                    return date.split('T')[0]
                }
                return ''
            },
            isChildPr() {
                return this.printJinma === PsychotropicNarcoticTypeEnum.CHILD;
            },
            isMaOrJING_1() {
                return [PsychotropicNarcoticTypeEnum.JING_1, PsychotropicNarcoticTypeEnum.MA_ZUI].includes(this.printJinma);
            },
            jinmaType() {
                return PsychotropicNarcoticTypeEnumStr[this.printJinma] || '';
            },
        },
        methods: {
            filterMobileV2,
            formatAge,
            formatAddress,
            formatPatientOrderNo,
            parseTime,
        },
    };
</script>

<style lang="scss">
@import "index.scss";

.prescription-header-shanghai {

  .row-box .row-box-content {
    border-color: #000 !important;
  }

  .header-mark {
    display: flex;
    justify-content: space-between;
    align-items: start;
    min-height: 38px;
    padding: 2px;

    .no {
      font-size: 12px;
      font-weight: 400;
      line-height: 12px; /* 100% */
    }

    .jinma-mark {
      border: 1px solid #000;
      padding: 6px 5px;
      font-size: 18px;
      font-weight: 600;
      letter-spacing: 4px;
    }
  }
  .organ-name {
    color: #000;
    text-align: center;
    font-size: 25px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    padding-top: 2px;
    max-height: 36px;
    overflow: hidden;

    &.long-organ-name {
      font-size: 20px;
    }
  }
  .outpatient-info {
    font-size: 13px;
    line-height: 19px;
    padding-top: 10px;


    .list-info:not(:first-child) {
      padding-top: 5px;
    }


    .sex-section {
      display: flex;
      align-items: center;
    }
    .square-mark {
      min-width: 11px;
      min-height: 11px;
      max-height: 11px;
      line-height: 11px;
      border: 1px solid #000;
      font-weight: bold;
      margin-right: 2px;
      & + span:not(:last-child) {
        margin-right: 6px;
      }
    }
    .patient-age {
      display: flex;
      align-items: center;
      .age-line {
        border-bottom: 1px solid #000;
        padding: 0 4px;
        margin: 0 2px;
        display: inline-block;
        min-height: 19px;
      }
    }
  }
  .rp-icon {
    color: #000;
    font-size: 22px;
    line-height: 26px;
    font-weight: 700;
    padding-bottom: 10px;
  }

  .ma-list-info {
    padding: 8px;
    border: 1px solid #000;
  }
}

</style>
