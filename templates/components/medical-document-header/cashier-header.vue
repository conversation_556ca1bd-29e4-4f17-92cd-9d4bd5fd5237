<template>
    <div class="cashier-header">
        <!-- 抬头 -->
        <div
            class="organ-name-new"
        >
            <template v-if="!curOrganSubtitle">
                {{ curOrganTitle }}
            </template>
            <template v-else>
                <div class="sub-title-line-height">
                    {{ curOrganTitle }}
                </div>
                <div class="sub-title-line-height">
                    {{ curOrganSubtitle }}
                </div>
            </template>
        </div>
        <div class="document-type">
            <div
                class="document-title"
                :class="curOrganSubtitle ? 'document-type-text-sub' : 'document-type-text-single'"
            >
                收费单
            </div>
        </div>
        <div
            v-if="clinicInfoConfig.qrCode && qrCodeSrc"
            class="qr-code-img"
        >
            <img
                :src="qrCodeSrc"
                alt=""
            />
        </div>
        <div>
            <div
                v-if="(config.barcode && barcodeSrc) || (config.titleStyle && organ.logo)"
                class="left-img-wrapper"
            >
                <img
                    v-if="config.barcode && barcodeSrc"
                    class="bar-img"
                    :src="barcodeSrc"
                    alt=""
                />
                <template v-else>
                    <img
                        v-if="organ.logo"
                        class="logo-img-content"
                        :src="organ.logo"
                        alt=""
                    />
                </template>
            </div>
        </div>

        <!-- 患者信息 -->
        <div class="patient-info-wrapper">
            <div class="spacing">
                <span>患者：</span>
                <span>{{ patient.name || '匿名患者' }}</span>
                <span v-if="patientInfo.sex">{{ patient.sex }}</span>
                <span v-if="patientInfo.age">{{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}</span>
            </div>
            <div
                v-if="patientInfo.mobile"
                class="spacing"
            >
                手机：{{ patient.mobile | filterMobileV2(patientInfo.mobileType) }}
            </div>
            <div
                v-if="patientInfo.patientOrderNo"
                class="spacing"
            >
                诊号：
                <template v-if="printData.patientOrderNo">
                    {{ printData.patientOrderNo }}
                </template>
            </div>
            <div
                v-if="patientInfo.doctor"
                class="spacing"
            >
                医生：
                <template v-if="printData.doctorName">
                    {{ printData.doctorName }}
                </template>
            </div>
            <div
                v-if="isSupportGlasses && patientInfo.optometrist"
                class="spacing"
            >
                验光师：
                <template v-if="glassesMedicineForms.length">
                    {{ glassesMedicineForms[0].optometristName }}
                </template>
            </div>
            <div
                v-if="patientInfo.sellerName"
                class="spacing"
            >
                开单人：
                <template v-if="printData.sellerName">
                    {{ printData.sellerName }}
                </template>
            </div>
        </div>
    </div>
</template>

<script>
    import {formatAge, getLengthWithFullCharacter, textToBase64BarCode} from "../../common/utils";
    import {TITLE_MAX_LENGTH} from "../../common/constants";
    import {filterMobileV2} from "../../common/medical-transformat";

    export default {
        name: 'CashierHeader',
        filters: {
            filterMobileV2,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            glassesMedicineForms: {
                type: Array,
                default: () => ([]),
            },
            organTitle: {
                type: String,
                default: '',
            },
            isSupportGlasses: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                curOrganTitle: '',
                curOrganSubtitle: '',
            }
        },
        computed: {
            patientInfo() {
                return this.config.patientInfo || {};
            },
            patient() {
                return this.printData.patient || {};
            },
            organ() {
                return this.printData.organ || {};
            },
            titleAndSubtitle() {
                return {
                    organTitle: this.organTitle,
                    headerConfigSubtitle: this.config.subtitle,
                    organName: this.organ.name,
                };
            },
            clinicInfoConfig() {
                return this.config.clinicInfo || {};
            },
            qrCodeSrc() {
                return this.printData.qrCode;
            },
            barcodeSrc() {
                const barcode = this.printData.patientOrderNo ? `${this.printData.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode);
            },
        },
        watch: {
            titleAndSubtitle: {
                handler(v) {
                    const { organTitle, headerConfigSubtitle, organName } = v;
                    let title = '', subtitle = '';
                    if(!organTitle) {
                        title = organName || '';
                    } else {
                        title = organTitle || '';
                    }

                    const cacheTitle = title;
                    const {
                        fullCharacterLength, splitLength,
                    } = getLengthWithFullCharacter(cacheTitle, TITLE_MAX_LENGTH);
                    if (fullCharacterLength > TITLE_MAX_LENGTH) {
                        title = cacheTitle.slice(0, splitLength);
                        subtitle = cacheTitle.slice(splitLength);
                    }

                    this.curOrganTitle = title;
                    if (subtitle) {
                        this.curOrganSubtitle = subtitle;
                    } else {
                        this.curOrganSubtitle = headerConfigSubtitle || '';
                    }
                },
                immediate: true,
                deep: true,
            }
        },
        methods: {formatAge},
    }
</script>

<style lang="scss">
.cashier-header {
    position: relative;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', 微软雅黑;

    .organ-name-new {
        height: 40pt;
        font-family: SimSun;
        font-size: 14pt;
        font-weight: bold;
        line-height: 40pt;
        text-align: center;

        .sub-title-line-height {
            font-family: SimSun;
            font-size: 14pt;
            font-weight: bold;
            line-height: 20pt;
            text-align: center;
        }
    }

    .document-type {
        font-family: SimSun;
        text-align: center;

        .document-title {
            position: relative;
            display: inline-block;
        }

        .document-type-text-sub {
            font-size: 12pt;
            line-height: 20pt;
        }

        .document-type-text-single {
            font-size: 14pt;
            line-height: 20pt;
        }
    }

    .qr-code-img {
        position: absolute;
        top: 0;
        right: -6pt;
        z-index: 1;
        width: 48pt;
        height: 48pt;

        > img {
            width: 48pt;
            height: 48pt;
        }
    }

    .left-img-wrapper {
        position: absolute;
        top: 0;
        left: -6pt;
        width: 90pt;
        height: 40pt;
        line-height: 40pt;

        .bar-img {
            width: 100%;
            height: 24pt;
            vertical-align: middle;
        }

        .logo-img-content {
            width: 100%;
            height: auto;
            max-height: 100%;
            vertical-align: middle;
        }
    }

    .patient-info-wrapper {
        width: 100%;
        padding-bottom: 3pt;
        margin-top: 10pt;
        margin-bottom: 6pt;
        font-size: 0;
        font-weight: 300;
        border-bottom: 1px solid #000000;

        .spacing {
            display: inline-block;
            width: 33%;
            font-size: 10pt;
            line-height: 18pt;
        }
    }
}
</style>