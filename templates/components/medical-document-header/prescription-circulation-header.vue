<template>
    <div :class="['print-prescription-circulation-header', { 'is-landscape': isLandscape }]">
        <div
            :class="['organ-name', {
                'organ-name-13': fixmedinsName.length >= 13,
                'organ-name-14': fixmedinsName.length >= 14,
                'organ-name-15': fixmedinsName.length >= 15
            }]"
        >
            {{ fixmedinsName }}
        </div>
        <!-- 电子处方追溯码 -->
        <template v-if="printData.rxTraceInfo">
            <div class="rxTraceInfo-info">
                <img
                    :src="printData.rxTraceInfo.rxTraceQrCode"
                    :class="fixmedinsName.length > limitLength ? 'rxTraceInfo-code' : 'rxTraceInfo-code-small'"
                />
                <div>{{ printData.rxTraceInfo.rxTraceCode }}</div>
                <div>医保电子处方追溯码</div>
            </div>
        </template>
        <!-- 机构公章 排除吉林 && 有机构公章 -->
        <template v-if="!region.isJilin && printData.institutionalSealBase64">
            <img
                :src="printData.institutionalSealBase64"
                class="institutional-seal"
                alt=""
            />
        </template>
        <!-- 处方标识 -->
        <div class="identification">{{ printData.prescriptionDesc }}</div>
        <!-- 处方签 -->
        <div class="document-type">处方签</div>
        <!-- 表头数据 -->
        <print-row class="base-info">
            <print-col
                v-for="item in printData.headerConfig"
                :key="item.key"
                :span="item.span"
                overflow
            >
                <div>{{ item.label }}：{{ item.value}}</div>
            </print-col>
        </print-row>
        <print-row class="line-split"/>
        <div
            v-if="showRp"
            class="rp-icon"
            :class="{'is-landscape-rp': isLandscape}"
        >
            Rp<sub>&#183;</sub>
        </div>
    </div>
</template>

<script>
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';

    export default {
        name: 'PrescriptionCirculationHeader',
        components: {
            PrintRow,
            PrintCol,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            showRp: {
                type: Boolean,
                default: true
            },
            isLandscape: {
                type: Boolean,
                default: false
            }
        },
        computed: {
            // 机构名称
            fixmedinsName() {
                return this.printData.fixmedinsName
            },
            // 地区
            region() {
                const region = this.printData.region
                const isJilin = region?.startsWith('jilin')
                const isJiangsu = region?.startsWith('jiangsu')
                return {
                    isJilin,
                    isJiangsu
                }
            },
            // 限制长度
            limitLength() {
                return this.printData.readerType === 'create' ? 20 : 19
            }
        }
    }
</script>

<style lang="scss">
.print-prescription-circulation-header {
    position: relative;
    box-sizing: border-box;
    padding-top: 45px;

    .organ-name {
        position: relative;
        z-index: 1;
        max-height: 42pt;
        padding: 0 88pt;
        overflow-y: hidden;
        font-size: 15pt;
        line-height: 21pt;
        text-align: center;
        word-break: break-all;

        &.organ-name-13 {
            max-height: 36pt;
            font-size: 14pt;
            line-height: 18pt;
        }

        &.organ-name-14 {
            max-height: 34pt;
            font-size: 13pt;
            line-height: 17pt;
        }

        &.organ-name-15 {
            max-height: 30pt;
            font-size: 11pt;
            line-height: 15pt;
        }
    }

    .rxTraceInfo-info {
        position: absolute;
        left: 0;
        top: 0;
        font-size: 7pt;
        text-align: center;

        .rxTraceInfo-code {
            width: 22mm;
            height: 22mm;
        }

        .rxTraceInfo-code-small {
            width: 19mm;
            height: 19mm;
        }
    }

    .institutional-seal {
        position: absolute;
        top: -7pt;
        left: 50%;
        margin-left: -40pt;
        width: 80pt;
        height: 80pt;
    }

    .identification {
        position: absolute;
        top: 0;
        right: 0;
        width: 62px;
        height: 40px;
        line-height: 40px;
        border: 1px solid #000000;
        text-align: center;
        font-size: 7pt;
    }

    .print-col {
        overflow: hidden;
        font-size: 10pt;
        word-break: keep-all;
        white-space: nowrap;
    }

    .document-type {
        font-size: 13pt;
        line-height: 19pt;
        text-align: center;
    }

    .base-info {
        margin-top: 5pt;
        font-weight: 300;
        line-height: 12pt;
        border-top: 1px solid #000000;
        margin: 6pt 0;
        line-height: 18pt;
        padding-top: 6pt;

        .print-col div {
            font-size: 8pt;
        }
    }

    .line-split {
        height: 0;
        border-top: 1px solid #000000;
    }

    .rp-icon {
        font-size: 10pt;
        line-height: 19pt;
    }
}

</style>
