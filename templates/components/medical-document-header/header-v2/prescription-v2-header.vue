<template>
    <div class="prescription-v2-header-wrapper">
        <div class="prescription-v2-header-prescription-v2-header-title-wrapper">
            <div
                v-if="showVirtualPharmacy || showProcess || showExpress || isMultiPharmacy"
                class="prescription-v2-header-process-text"
            >
                <span v-if="showVirtualPharmacy">
                    <span
                        v-if="showPharmacyName"
                        class="prescription-v2-header-protrude-item"
                    >代煎代配</span>
                    <span
                        v-if="showProcess"
                        class="prescription-v2-header-protrude-item"
                    >加工</span>
                    <span
                        v-if="showExpress"
                        class="prescription-v2-header-protrude-item"
                    >快递</span>
                </span>
                <template v-else>
                    <span
                        v-if="isMultiPharmacy && showPharmacyName && prescriptionShowPharmacyName"
                        class="prescription-v2-header-protrude-item"
                    >{{ pharmacyName }}</span>
                    <span
                        v-if="showProcess"
                        class="prescription-v2-header-protrude-item"
                    >加工</span>
                    <span
                        v-if="showExpress"
                        class="prescription-v2-header-protrude-item"
                    >快递</span>
                    <span v-if="takeMedicationTime && !showVirtualPharmacy">
                        {{ isToday(takeMedicationTime) ? '(今)' : '' }} {{ takeMedicationTime | parseTime('m-d h:i') }} 取药
                    </span>
                </template>
            </div>

            <div
                v-if="printJinma && (config.barcode && barcodeSrc)"
                class="prescription-v2-header-barcode-wrapper"
                style=" left: -5mm; align-items: flex-start;"
                :style="isHorizontal ? { left: 0 } : {}"
            >
                <div
                    v-if="config.barcode && barcodeSrc"
                    class="prescription-v2-header-barcode-img-wrapper"
                >
                    <img
                        :src="barcodeSrc"
                        class="prescription-v2-header-barcode-img"
                        alt=""
                    />
                </div>
            </div>
            <div
                v-else-if="logo"
                class="prescription-v2-header-logo-wrapper"
            >
                <img
                    :src="logo"
                    class="prescription-v2-header-logo-img"
                    alt=""
                />
            </div>

            <div class="prescription-v2-header-title-wrapper">
                <div
                    class="prescription-v2-header-title-text-wrapper"
                    :style="{ 'padding-top': !titleAndSubtitle.subTitle ? '6px' : '0px' }"
                >
                    {{ titleAndSubtitle.title }}
                    <br v-if="titleAndSubtitle.subTitle" />
                    {{ titleAndSubtitle.subTitle }}
                </div>
                <div class="prescription-v2-header-title-prescription-type">
                    {{ title }}
                    <template v-if="config.prescriptionType && handleExtendSpec">
                        ({{ handleExtendSpec }})
                    </template>
                </div>
            </div>

            <div
                v-if="printJinma"
                class="prescription-v2-header-jinma-wrapper"
            >
                {{ jinmaType }}
            </div>
            <div
                v-else
                class="prescription-v2-header-barcode-wrapper"
                style="right: -5mm;"
                :style="isHorizontal ? { right: 0 } : {}"
            >
                <div
                    v-if="config.barcode && barcodeSrc"
                    class="prescription-v2-header-barcode-img-wrapper"
                >
                    <img
                        :src="barcodeSrc"
                        class="prescription-v2-header-barcode-img"
                        alt=""
                    />
                </div>
            </div>
        </div>

        <div class="prescription-v2-header-info-wrapper">
            <div class="prescription-v2-header-patient">
                <div
                    :class="isHorizontal ? 'prescription-v2-horizontal-header-patient-name-column' : 'prescription-v2-header-first-column'"
                    :style="config.dateType && config.patientOrderNo ? { width: '100%', paddingBottom: '4px' } : {}"
                >
                    <div>
                        姓名：{{ patient.name }}
                    </div>
                    <div style="margin-left: 12px;">
                        {{ patient.sex }}
                    </div>
                    <div style="margin-left: 12px;">
                        {{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                    </div>
                </div>
                <div
                    v-if="!config.dateType && config.patientOrderNo"
                    :class="isHorizontal ? 'prescription-v2-horizontal-header-second-column' : 'prescription-v2-header-second-column'"
                >
                    诊号：{{ formatPatientOrderNo(patientOrderNo ) }}
                </div>
                <div
                    v-if="!config.dateType || (config.dateType && !config.patientOrderNo)"
                    :class="isHorizontal ? 'prescription-v2-horizontal-header-fourth-column' : 'prescription-v2-header-third-column'"
                    :style="config.dateType ? { flex: 1, paddingLeft: 0 } : !config.patientOrderNo ? { paddingLeft: 0 } : {}"
                >
                    日期：{{ parseTime(curDate, config.dateType ? 'y-m-d h:i:s' : 'y-m-d') }}
                </div>
            </div>

            <div
                v-if="config.dateType && config.patientOrderNo"
                class="prescription-v2-header-patient"
            >
                <div :class="isHorizontal ? 'prescription-v2-horizontal-header-patient-name-column' : 'prescription-v2-header-first-column'">
                    诊号：{{ formatPatientOrderNo(patientOrderNo ) }}
                </div>
                <div
                    :class="isHorizontal ? 'prescription-v2-horizontal-header-second-column' : 'prescription-v2-header-second-column'"
                    :style="{ width: isHorizontal ? '45%' : '60%' }"
                >
                    日期：{{ parseTime(curDate, 'y-m-d h:i:s') }}
                </div>
            </div>

            <spacing-line line-type="solid"></spacing-line>

            <div class="prescription-v2-header-patient-medical-info">
                <template v-if="config.clinicSocialCode && config.doctorSocialCode">
                    <div
                        class="prescription-v2-header-item"
                        :class="calcPatientInfoClass(0)"
                    >
                        <span style="flex: 1;">机构编码：{{ (printData.organShebaoInfo && printData.organShebaoInfo.hospitalCode) || '' }}</span>
                    </div>
                    <div
                        class="prescription-v2-header-item"
                        :class="calcPatientInfoClass(1)"
                        :style="{ width: isHorizontal ? '67%' : '60%' }"
                    >
                        <span style="flex: 1;">医生编码：{{ printData.nationalDoctorCode || '' }}</span>
                    </div>
                </template>

                <template v-if="patientMedicalInfo.length <= calcColumnNum">
                    <div
                        v-for="(item, index) in patientMedicalInfo"
                        :key="`prescription-patient-info-${index}`"
                        class="prescription-v2-header-item"
                        :class="calcPatientInfoClass(index)"
                    >
                        <span
                            style="flex: 1;"
                            :style="item.isTruncation ? { 'white-space': 'nowrap', 'overflow': 'hidden' } : {}"
                        >{{ item.value }}</span>
                    </div>
                    <div
                        v-if="config.idCard || isJingMaDu"
                        class="prescription-v2-header-address prescription-v2-header-item prescription-v2-header-id-card"
                    >
                        {{ getIdCardTypeStr(patient.idCardType) }}：{{ filterIdCard(patient.idCard, config.idCardDesensitization) }}
                    </div>
                </template>
                <template v-else>
                    <div
                        v-for="(item, index) in patientMedicalInfo.slice(0, calcColumnNum)"
                        :key="`prescription-patient-info-medical-1-${index}`"
                        class="prescription-v2-header-item"
                        :class="calcPatientInfoClass(index)"
                    >
                        <span
                            style="flex: 1;"
                            :style="item.isTruncation ? { 'white-space': 'nowrap', 'overflow': 'hidden' } : {}"
                        >{{ item.value }}</span>
                    </div>
                    <div
                        v-if="config.idCard || isJingMaDu"
                        class="prescription-v2-header-item prescription-v2-header-id-card"
                        :class="isHorizontal ? 'prescription-v2-horizontal-header-first-column' : 'prescription-v2-header-first-column'"
                    >
                        {{ getIdCardTypeStr(patient.idCardType) }}：{{ filterIdCard(patient.idCard, config.idCardDesensitization) }}
                    </div>
                    <div
                        v-for="(item, index) in patientMedicalInfo.slice(calcColumnNum)"
                        :key="`prescription-patient-info-medical-2-${index}`"
                        class="prescription-v2-header-item"
                        :class="calcPatientInfoClass(config.idCard ? index + 1 : index)"
                    >
                        <span
                            style="flex: 1;"
                            :style="item.isTruncation ? { 'white-space': 'nowrap', 'overflow': 'hidden' } : {}"
                        >{{ item.value }}</span>
                    </div>
                </template>

                <div
                    v-if="config.address"
                    class="prescription-v2-header-address prescription-v2-header-item"
                >
                    地址：{{ formatAddress(patient.address) }}
                </div>
            </div>

            <spacing-line
                v-if="(config.chief || config.allergy) && (patientMedicalInfo.length || config.idCard || config.address)"
                :top-margin="4"
            ></spacing-line>

            <div class="prescription-v2-header-diagnosis-wrapper">
                <div
                    v-if="config.chief"
                    class="prescription-v2-header-item"
                    :class="`${isHorizontal ? 'prescription-v2-horizontal-header-chief' : 'prescription-v2-header-chief'}`"
                >
                    主诉：{{ medicalRecord.chiefComplaint }}
                </div>
                <div
                    v-if="config.allergy"
                    class="prescription-v2-header-item"
                    :class="`${isHorizontal ? 'prescription-v2-horizontal-header-allergy' : 'prescription-v2-header-chief'}`"
                >
                    过敏史：{{ medicalRecord.allergicHistory }}
                </div>
                <div class="prescription-v2-header-chief prescription-v2-header-item">
                    诊断：{{ diagnosis | filterDiagnose }}<template v-if="syndrome">
                        （{{ syndrome }}）
                    </template>
                </div>
            </div>

            <div
                v-if="isJingMaDu"
                class="prescription-v2-header-diagnosis-wrapper"
            >
                <div class="prescription-v2-header-item">
                    代办人：<span>{{ psychotropicNarcoticEmployee.name || '-' }}</span>
                    <template v-if="psychotropicNarcoticEmployee.name">
                        <span v-if="psychotropicNarcoticEmployee.sex">{{ psychotropicNarcoticEmployee.sex }}</span>
                        <span
                            v-if="psychotropicNarcoticEmployee.age && psychotropicNarcoticEmployee.age.year"
                        >{{ psychotropicNarcoticEmployee.age.year }}岁</span>
                        <span v-if="psychotropicNarcoticEmployee.idCard">{{ psychotropicNarcoticEmployee.idCard }}</span>
                    </template>
                </div>
            </div>
            <spacing-line :top-margin="4"></spacing-line>

            <div class="prescription-v2-header-rp">
                Rp.
            </div>
        </div>
    </div>
</template>

<script>
    import {
        formatAddress,
        formatAge,
        formatPatientOrderNo, getIdCardTypeStr,
        getLengthWithFullCharacter, getPatientMaterialName, isToday,
        parseTime,
        textToBase64BarCode,
    } from "../../../common/utils";
    import { PsychotropicNarcoticTypeEnum, PsychotropicNarcoticTypeEnumStr } from "../../../constant/print-constant";
    import { TITLE_MAX_LENGTH } from "../../../common/constants";
    import SpacingLine from "../spacing-line.vue";
    import { filterDiagnose, filterIdCard, filterMobileV2 } from "../../../common/medical-transformat";

    export default {
        name: 'PrescriptionV2Header',
        components: { SpacingLine },
        filters: {
            filterDiagnose,
            parseTime,
        },
        props: {
            isHorizontal: {
                type: Boolean,
                default: false,
            },
            title: {
                type: String,
                default: '处方笺',
            },
            printData: {
                type: Object,
                default(){
                    return {}
                },
            },
            config: {
                type: Object,
                required: true,
            },
            showVirtualPharmacy: {
                type: Boolean,
                default: false,
            },
            showProcess: {
                type: Boolean,
                default: false,
            },
            showExpress: {
                type: Boolean,
                default: false,
            },
            prescriptionShowPharmacyName: {
                type: Boolean,
                default: true,
            },
            showPharmacyName: {
                type: Boolean,
                default: true,
            },
            pharmacyName: {
                default: '',
                type: String,
            },
            printJinma: {
                type: Number,
                default: 0,
            },
            patientOrderNo: {
                type: [Number, String],
                default: '',
            },
            diagnosis: {
                type: String,
                default: '',
            },
            openPharmacyFlag: {
                type: Number,
                default: 0,
            },
            organ: {
                type: Object,
                default: () => ({}),
            },
            patient: {
                type: Object,
                default: () => ({}),
            },
            diagnosedDate: {
                type: String,
                default: '',
            },
            shebaoCardInfo: {
                type: Object,
                default: () => ({}),
            },
            departmentName: {
                type: String,
                default: '',
            },
            healthCardPayLevel: {
                type: String,
                default: '',
            },
            revisitStatus: {
                type: Number,
                default: 1,
            },
            healthCardNo: {
                type: String,
                default: '',
            },
            medicalRecord: {
                type: Object,
                default: () => ({}),
            },
            extendSpec: {
                type: String,
                default: '',
            },
            takeMedicationTime: {
                type: String,
                default: '',
            },
        },
        computed: {
            syndrome() {
                return (this.printData.medicalRecord && this.printData.medicalRecord.syndrome) || '';
            },
            shebaoCardInformation() {
                return this.shebaoCardInfo || {};
            },
            calcColumnNum() {
                return this.isHorizontal ? 4 : 3;
            },
            barcodeSrc() {
                const barcode = this.patientOrderNo ? `${this.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode);
            },
            isMultiPharmacy() {
                return this.openPharmacyFlag === 20 || false;
            },
            jinmaType() {
                return PsychotropicNarcoticTypeEnumStr[this.printJinma] || '';
            },
            isJingMaDu() {
                return [
                    PsychotropicNarcoticTypeEnum.JING_1,
                    PsychotropicNarcoticTypeEnum.JING_2,
                    PsychotropicNarcoticTypeEnum.MA_ZUI,
                    PsychotropicNarcoticTypeEnum.DU,
                ].includes(this.printJinma);
            },
            psychotropicNarcoticEmployee() {
                return this.printData.psychotropicNarcoticEmployee || {};
            },
            titleAndSubtitle() {
                const res = {
                    title: '',
                    subTitle: '',
                };
                if (this.config.title) {
                    res.title = this.config.title;
                    if (this.config.subtitle) {
                        res.subTitle = this.config.subtitle;
                    }
                } else if (this.organ) {
                    const clinicName = this.organ.name || '';
                    const {
                        fullCharacterLength, splitLength,
                    } = getLengthWithFullCharacter(clinicName, TITLE_MAX_LENGTH);
                    if (fullCharacterLength > TITLE_MAX_LENGTH) {
                        res.title = clinicName.slice(0, splitLength);
                        res.subtitle = clinicName.slice(splitLength);
                    } else {
                        res.title = clinicName;
                    }
                }
                return res;
            },
            logo() {
                if (this.config.logoConfig === 1) return this.organ.logo || '';
                if (this.config.logoConfig === 2) return this.config.logoAddress || '';
                return '';
            },
            // 处方的时间都统一显示为完成诊断时间，有客户反馈处方打印的时间会因为处方修改而跟门诊单开出的时间不一致
            curDate() {
                return this.diagnosedDate;
            },
            patientMedicalInfo() {
                const res = [];
                if (this.config.clinicSocialCode && !this.config.doctorSocialCode) {
                    res.push({
                        value: `机构编码：${(this.printData.organShebaoInfo && this.printData.organShebaoInfo.hospitalCode) || ''}`,
                    });
                }
                if (this.config.doctorSocialCode && !this.config.clinicSocialCode) {
                    res.push({
                        value: `医生编码：${this.printData.nationalDoctorCode || ''}`,
                    });
                }
                if (this.config.department) {
                    res.push({
                        value: `科室：${this.departmentName || ''}`,
                    });
                }
                if (this.config.feeType) {
                    res.push({
                        value: `费别：${this.config.feeType ? (this.healthCardPayLevel || '') : ''}`,
                    });
                }
                if (this.config.mobileType || this.isJingMaDu) {
                    res.push({
                        value: `手机号：${this.filterMobileV2(this.patient.mobile, this.config.mobileDesensitization ? 1 : 2 )}`,
                    });
                }
                if (this.config.revisit) {
                    res.push({
                        value: `初复诊：${this.revisitStatus === 2 ? '复诊' : '初诊'}`,
                    });
                }
                if (this.config.personType) {
                    res.push({
                        value: `类别：${this.config.personType ? (this.shebaoCardInformation.feeType || '') : '' }`,
                    });
                }
                if (this.config.socialCode) {
                    res.push({
                        value: `医保号：${this.healthCardNo || ''}`,
                    });
                }
                if (this.config.computerCode) {
                    res.push({
                        value: `个人编号：${this.shebaoCardInformation.extend?.personalCode || ''}`,
                    });
                }
                if (this.config.fileNumber) {
                    res.push({
                        value: `档案号：${this.patient.sn || ''}`,
                        isTruncation: true,
                    });
                }
                if (this.config.birthday) {
                    res.push({
                        value: `生日：${this.patient.birthday || ''}`,
                    });
                }
                if (this.config.weight) {
                    res.push({
                        value: `体重：${this.patient.weight ? this.patient.weight + 'kg' : ''}`,
                    });
                }
                if (this.config.married) {
                    res.push({
                        value: `婚否：${getPatientMaterialName(this.patient.marital)}`,
                    });
                }
                if (this.config.nation) {
                    res.push({
                        value: `民族：${this.patient.ethnicity || ''}`,
                    });
                }
                if (this.config.profession) {
                    res.push({
                        value: `职业：${this.patient.profession || ''}`,
                    });
                }
                return res;
            },
            handleExtendSpec() {
                if (this.extendSpec === '中药饮片') {
                    return '饮片';
                }
                if (this.extendSpec === '中药颗粒') {
                    return '颗粒';
                }
                return this.extendSpec;
            },
        },
        methods: {
            getIdCardTypeStr,
            isToday,
            formatAge,
            formatPatientOrderNo,
            parseTime,
            filterMobileV2,
            filterIdCard,
            formatAddress,
            calcPatientInfoClass(index) {
                if (this.isHorizontal) {
                    return index % 4 === 0 ? 'prescription-v2-horizontal-header-first-column' : index % 4 === 1 || index % 4 === 2 ? 'prescription-v2-horizontal-header-second-column' : 'prescription-v2-horizontal-header-fourth-column';
                }
                return index % 3 === 0 ? 'prescription-v2-header-first-column' : index % 3 === 1 ? 'prescription-v2-header-second-column' : 'prescription-v2-header-third-column';
            },
        },
    }
</script>

<style lang="scss">
.prescription-v2-header-wrapper {
    position: relative;

    .prescription-v2-header-prescription-v2-header-title-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 70px;

        .prescription-v2-header-logo-wrapper {
            position: absolute;
            left: 0;
            display: flex;
            align-items: center;
            width: 99px;
            height: 48px;
        }

        .prescription-v2-header-logo-img {
            max-width: 100%;
            max-height: 100%;
        }

        .prescription-v2-header-barcode-img {
            width: 100%;
            height: 100%;
        }

        .prescription-v2-header-barcode-wrapper {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: center;
            width: 125px;
            height: 46px;
        }

        .prescription-v2-header-barcode-img-wrapper {
            width: 112px;
            height: 32px;
        }

        .prescription-v2-header-process-text {
            position: absolute;
            top: -2mm;
            left: -5mm;
            font-size: 13px;
            font-weight: bold;
        }

        .prescription-v2-header-protrude-item {
            padding: 0 2px;
            border: 1px solid #000000;
        }

        .prescription-v2-header-jinma-wrapper {
            position: absolute;
            right: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40pt;
            height: 20px;
            font-size: 13px;
            line-height: 16px;
            border: 1px solid #000000;
        }

        .prescription-v2-header-title-wrapper {
            display: flex;
            flex-direction: column;
            gap: 2px;
            align-items: center;
            height: 70px;
            font-family: SimSun, serif;
        }

        .prescription-v2-header-title-text-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 48px;
            font-size: 19px;
            font-weight: 800;
            line-height: 24px;
            text-align: center;
        }

        .prescription-v2-header-title-prescription-type {
            height: 22px;
            font-size: 16px;
            font-weight: 400;
            line-height: 22px;
        }
    }

    .prescription-v2-header-info-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding-top: 16px;
        font-size: 13px;
        font-weight: 300;
        line-height: 18px;

        .prescription-v2-header-patient {
            display: flex;
            width: 100%;
        }

        .prescription-v2-header-item {
            padding-bottom: 4px;
        }

        .prescription-v2-header-first-column {
            display: flex;
            width: 40%;
            padding-right: 7px;
        }

        .prescription-v2-horizontal-header-first-column {
            display: flex;
            width: 33%;
            padding-right: 24px;
        }

        .prescription-v2-horizontal-header-patient-name-column {
            display: flex;
            width: 55%;
            padding-right: 20px;
        }

        .prescription-v2-header-second-column {
            display: flex;
            width: 30%;
            padding-right: 6px;
        }

        .prescription-v2-horizontal-header-second-column {
            display: flex;
            width: 22%;
            padding-right: 20px;
        }

        .prescription-v2-header-third-column {
            display: flex;
            width: 30%;
            padding-left: 6px;
        }

        .prescription-v2-horizontal-header-fourth-column {
            display: flex;
            width: 22%;
        }

        .prescription-v2-header-patient-medical-info {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
        }

        .prescription-v2-header-address {
            display: flex;
            width: 100%;
        }

        .prescription-v2-header-diagnosis-wrapper {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
        }

        .prescription-v2-header-chief {
            display: flex;
            width: 100%;
        }

        .prescription-v2-horizontal-header-chief {
            display: flex;
            width: 55%;
        }

        .prescription-v2-horizontal-header-allergy {
            display: flex;
            width: 45%;
        }

        .prescription-v2-header-rp {
            display: flex;
            width: 100%;
            font-weight: normal;
        }
    }
}
</style>
