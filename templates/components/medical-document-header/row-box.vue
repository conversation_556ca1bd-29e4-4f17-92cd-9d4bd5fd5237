<template>
    <span class="row-box">
        <span class="row-box-label">
            <slot name="label"></slot>
        </span>
        <span
            :style="{marginLeft: labelWidth + 'px', borderBottom: underline ? '1px solid #a6a6a6' : ''}"
            class="row-box-content"
        >
            <slot name="content"></slot>
        </span>
    </span>
</template>

<script>
export default {
    name: 'RowBox',
    props: {
        labelWidth: {
            type: Number,
            default: 100,
        },
        underline: {
            type: Boolean,
            default: true,
        },
    }
}
</script>

<style lang="scss">
.row-box {
    width: 100%;
    position: relative;
    display: inline-block;
    .row-box-content {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        box-sizing: border-box;
    }
}
</style>