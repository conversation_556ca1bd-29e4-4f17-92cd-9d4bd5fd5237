<template>
    <div class="glasses-prescription-header">
        <div
            class="organ-name-new"
        >
            <template v-if="!curOrganSubtitle">
                {{ curOrganTitle }}
            </template>
            <template v-else>
                <div class="sub-title-line-height">
                    {{ curOrganTitle }}
                </div>
                <div class="sub-title-line-height">
                    {{ curOrganSubtitle }}
                </div>
            </template>
        </div>
        <div class="document-type">
            <div
                class="document-title"
                :class="curOrganSubtitle ? 'document-type-text-sub' : 'document-type-text-single'"
            >
                {{ printTitle }}
            </div>
        </div>
        <div
            v-if="headerConfig.qrcode && qrCodeSrc"
            class="qr-code-img"
        >
            <img
                :src="qrCodeSrc"
                alt=""
            />
        </div>
        <div
            v-if="(headerConfig.barcode && barcodeSrc) || (headerConfig.logo && organ.logo)"
            class="left-img-wrapper"
        >
            <img
                v-if="headerConfig.barcode && barcodeSrc"
                class="bar-img"
                :src="barcodeSrc"
                alt=""
            />
            <template v-else>
                <img
                    v-if="organ.logo"
                    class="logo-img-content"
                    :src="organ.logo"
                    alt=""
                />
            </template>
        </div>
        <div class="patient-info">
            <div
                class="patient-info-content fixed-info"
                :style="{ 'padding-bottom': isPatientInfoBottom ? '6pt' : 0, 'border-bottom': isPatientInfoBottom ? '1px solid #000000' : 'none', 'margin-bottom': isPatientInfoBottom ? '6pt' : 0 }"
            >
                <div class="patient-info-item-common item-1">
                    姓名：{{ patientName }}&nbsp;&nbsp;{{ patient.sex }}&nbsp;&nbsp;{{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                </div>
                <div
                    v-if="headerConfig.mobile"
                    class="patient-info-item-common item-2"
                >
                    手机：{{ patient.mobile | formatMobile(headerConfig.mobileDesensitization) }}
                </div>
                <div class="patient-info-item-common item-3">
                    日期：{{ parseTime(curDate, 'y-m-d') }}
                </div>
            </div>
            <div
                class="patient-info-content"
                :style="{ 'margin-bottom': headerConfig.address ? '6pt' : 0 }"
            >
                <div
                    v-if="headerConfig.idCard"
                    class="patient-info-item-common item-1"
                >
                    {{ getIdCardTypeStr(patient.idCardType) }}：{{ filterIdCard(patient.idCard, headerConfig.idCardDesensitization) }}
                </div>
                <div
                    v-if="headerConfig.birthday"
                    class="patient-info-item-common item-2"
                >
                    生日：{{ patient.birthday }}
                </div>
                <div
                    v-if="headerConfig.profession"
                    class="patient-info-item-common item-3"
                >
                    职业：{{ patient.profession }}
                </div>
            </div>
            <div
                v-if="headerConfig.address"
                class="address"
            >
                地址：{{ formatAddress(patient.address) }}
            </div>
        </div>
    </div>
</template>

<script>
    import {
        formatAddress,
        formatAge, getIdCardTypeStr,
        getLengthWithFullCharacter, parseTime,
        textToBase64BarCode
    } from "../../common/utils";
    import {filterIdCard, formatMobile} from "../../common/medical-transformat";
    import {TITLE_MAX_LENGTH} from "../../common/constants";

    export default {
        name: 'GlassesPrescriptionHeader',
        filters: {
            formatMobile,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            printTitle: {
                type: String,
                default: '配镜处方',
            },
            organTitle: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                curOrganTitle: '',
                curOrganSubtitle: '',
            }
        },
        computed: {
            patient() {
                return this.printData.patient || {};
            },
            patientName() {
                return this.patient.name
            },
            qrCodeSrc() {
                return this.printData.qrCode;
            },
            headerConfig() {
                return this.config.header || {};
            },
            organ() {
                return this.printData.organ || {};
            },
            barcodeSrc() {
                const barcode = this.printData.patientOrderNo ? `${this.printData.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode);
            },
            // 处方的时间都统一显示为完成诊断时间，有客户反馈处方打印的时间会因为处方修改而跟门诊单开出的时间不一致
            curDate() {
                return this.printData.diagnosedDate;
            },
            titleAndSubtitle() {
                return {
                    organTitle: this.organTitle,
                    headerConfigSubtitle: this.headerConfig.subtitle,
                    organName: this.organ.name,
                };
            },
            isPatientInfoBottom() {
                return this.headerConfig.idCard || this.headerConfig.birthday || this.headerConfig.profession || this.headerConfig.address;
            },
        },
        watch: {
            titleAndSubtitle: {
                handler(v) {
                    const { organTitle, headerConfigSubtitle, organName } = v;
                    let title = '', subtitle = '';
                    if(!organTitle) {
                        title = organName || '';
                    } else {
                        title = organTitle || '';
                    }

                    const cacheTitle = title;
                    const {
                        fullCharacterLength, splitLength,
                    } = getLengthWithFullCharacter(cacheTitle, TITLE_MAX_LENGTH);
                    if (fullCharacterLength > TITLE_MAX_LENGTH) {
                        title = cacheTitle.slice(0, splitLength);
                        subtitle = cacheTitle.slice(splitLength);
                    }

                    this.curOrganTitle = title;
                    if (subtitle) {
                        this.curOrganSubtitle = subtitle;
                    } else {
                        this.curOrganSubtitle = headerConfigSubtitle || '';
                    }
                },
                immediate: true,
                deep: true,
            }
        },
        methods: {
            getIdCardTypeStr,
            formatAge,
            formatAddress,
            parseTime,
            filterIdCard,
        },
    }
</script>

<style lang="scss">
.glasses-prescription-header {
    position: relative;
    font-family: Microsoft YaHei, 微软雅黑;

    .organ-name-new {
        height: 40pt;
        font-family: SimSun;
        font-size: 14pt;
        font-weight: bold;
        line-height: 40pt;
        text-align: center;

        .sub-title-line-height {
            font-family: SimSun;
            font-size: 14pt;
            font-weight: bold;
            line-height: 20pt;
            text-align: center;
        }
    }

    .document-type {
        font-family: SimSun;
        text-align: center;

        .document-type-text-sub {
            font-size: 12pt;
            line-height: 20pt;
        }

        .document-type-text-single {
            font-size: 14pt;
            line-height: 20pt;
        }
    }

    .qr-code-img {
        position: absolute;
        top: 0;
        right: -6pt;
        z-index: 1;
        width: 50pt;
        height: 50pt;

        > img {
            width: 50pt;
            height: 50pt;
        }
    }

    .left-img-wrapper {
        position: absolute;
        top: 0;
        left: -6pt;
        width: 80pt;
        height: 40pt;
        line-height: 40pt;

        .bar-img {
            width: 100%;
            height: 24pt;
            vertical-align: middle;
        }

        .logo-img-content {
            width: 100%;
            height: auto;
            max-height: 100%;
            vertical-align: middle;
        }
    }

    .patient-info {
        padding-bottom: 6pt;
        margin-top: 10pt;
        font-size: 10pt;
        font-weight: 300;
        line-height: 12pt;
        border-bottom: 1px solid #000000;

        .patient-info-content {
            word-spacing: -10px;

            .patient-info-item-common {
                display: inline-block;
                overflow: hidden;
                white-space: nowrap;
                word-spacing: 0;

                &.item-1 {
                    width: 43%;
                }

                &.item-2 {
                    width: 33%;
                }

                &.item-3 {
                    width: 24%;
                }
            }
        }

        .address {
            overflow: hidden;
            white-space: nowrap;
        }
    }
}
</style>