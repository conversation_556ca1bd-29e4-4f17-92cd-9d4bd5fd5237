<template>
    <div class="print-medical-document-header dispensing-A5-header-wrapper">
        <div
            v-if="(config.barcode && barcodeSrc) || (showProcess || showDelivery)"
            class="logo-img"
        >
            <div class="barcode-img-wrapper">
                <img
                    v-if="config.barcode && barcodeSrc"
                    class="bar-img"
                    :src="barcodeSrc"
                    alt=""
                />
            </div>
            <div
                v-if="isVirtualPharmacy"
                class="process-icon process-icon-text"
            >
                代煎代配
            </div>
            <div
                v-else
                class="process-icon process-icon-text"
            >
                <span v-if="showProcess">加工</span>
                <span v-if="showDelivery">快递</span>
                <div
                    v-if="takeMedicationTime"
                    class="take-medicine-time"
                >
                    {{ isToday(takeMedicationTime) ? '(今)' : '' }} {{ takeMedicationTime |parseTime('m-d h:i') }} 取药
                </div>
            </div>
        </div>


        <div
            class="organ-name-new"
        >
            <template v-if="!curOrganSubtitle">
                {{ curOrganTitle }}
            </template>
            <template v-else>
                <div class="sub-title-line-height">
                    {{ curOrganTitle }}
                </div>
                <div class="sub-title-line-height">
                    {{ curOrganSubtitle }}
                </div>
            </template>
            <div
                v-if="isUndispense"
                class="undispense"
            >
                退药
            </div>
        </div>

        <div class="document-type">
            <div
                class="document-title"
                :class="curOrganSubtitle ? 'document-type-text-sub' : 'document-type-text-single'"
            >
                {{ isUndispense ? `退药单${ chargeTransactionTime }` : '调剂发药单' }}
                <template v-if="handleCMSpec">
                    （{{ handleCMSpec }}）
                </template>
            </div>
        </div>

        <div
            v-if="ticketFooter.qrCode && qrCodeSrc"
            class="qr-code-img"
        >
            <img
                class="qr-img"
                :src="qrCodeSrc"
                alt=""
            />
        </div>

        <print-row
            class="base-info has-margin-top patient-info"
            style="margin-top: 10pt;"
        >
            <print-col
                :span="8"
                overflow
            >
                姓名：{{ patient.name }} <span class="space"></span> <template v-if="patientInfo.sex">
                    {{ patient.sex }}
                </template> <span class="space"></span> <template v-if="patientInfo.age">
                    {{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                </template>
            </print-col>
            <print-col
                v-if="patientInfo.mobile"
                :span="8"
                overflow
            >
                手机：{{ patient.mobile | filterMobileV2(patientInfo.mobileType) }}
            </print-col>

            <print-col
                v-if="patientInfo.patientOrderNo"
                :span="8"
                overflow
            >
                诊号：{{ printData.patientOrderNo }}
            </print-col>
            <print-col
                v-if="patientInfo.departmentName"
                :span="8"
                overflow
            >
                科室：{{ printData.departmentName }}
            </print-col>
            <print-col
                v-if="patientInfo.doctor"
                :span="8"
                overflow
            >
                医生：{{ printData.doctorName }}
            </print-col>

            <print-col
                v-if="patientInfo.diagnose"
                :span="8"
                overflow
            >
                诊断：{{ formatDiagnosis2Str(printData.diagnose) }}<template v-if="printData.syndrome">
                    （{{ printData.syndrome }}）
                </template>
            </print-col>
            <print-col
                v-if="ticketFooter.printTime"
                :span="10"
            >
                日期：{{ new Date() | parseTime('y-m-d h:i:s') }}
            </print-col>
        </print-row>
        <print-row class="line-split"></print-row>

        <div v-if="printData.openPharmacyFlag === 20 && otherConfig.takeMedicineLocation">
            <print-row
                class="base-info has-margin-top patient-info"
            >
                <print-col
                    :span="14"
                    style="font-size: 11pt; font-weight: 400;"
                    overflow
                >
                    取药地点：{{ pharmacyName }} {{ pharmacyAddress }}
                </print-col>
                <print-col
                    v-if="takeMedicationTime"
                    :span="10"
                    style="font-size: 11pt; font-weight: 400; text-align: right;"
                    overflow
                >
                    取药时间：预计 {{ takeMedicationTime | parseTime('m-d h:i') }}
                </print-col>
            </print-row>
            <print-row class="dashed-line-split"></print-row>
        </div>
    </div>
</template>

<script>
    import PrintRow from "../layout/print-row.vue";
    import PrintCol from "../layout/print-col.vue";

    import { filterMobileV2 } from "../../common/medical-transformat.js";
    import {
        formatAge,
        formatDiagnosis2Str,
        getLengthWithFullCharacter, isToday,
        parseTime,
        textToBase64BarCode
    } from "../../common/utils.js";
    import {PharmacyTypeEnum, TITLE_MAX_LENGTH} from "../../common/constants.js";
    export default {
        name: 'A5DispensingHeader',
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            filterMobileV2,
            parseTime,
        },
        props: {
            printData: {
                type: Object,
            },
            config: {
                type: Object,
            },
            cMSpec: {
                type: String,
            },
            showProcess: [Boolean, Number],
            showDelivery: [Boolean, Number],
            pharmacyType: Number,
            pharmacyName: {
                default: '',
                type: String,
            },
            pharmacyAddress: {
                default: '',
                type: String,
            },
            isUndispense: {
                default: false,
                type: Boolean,
            },
            takeMedicationTime: String
        },
        data() {
            return {
                curOrganTitle: '',
                curOrganSubtitle: '',
            }
        },
        computed: {
            patientInfo() {
                return this.config && this.config.patientInfo || {};
            },
            otherConfig() {
                return this.config && this.config.other || {};
            },
            patient() {
                return this.printData && this.printData.patient;
            },
            barcodeSrc() {
                return textToBase64BarCode(this.printData.patientOrderNo ?? '');
            },
            ticketFooter() {
                return this.config.ticketFooter || {};
            },
            qrCodeSrc() {
                return this.printData.qrCode || '';
            },
            isVirtualPharmacy() {
                return this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            handleCMSpec() {
                if (this.cMSpec === '中药饮片') {
                    return '饮片';
                }
                if (this.cMSpec === '中药颗粒') {
                    return '颗粒';
                }
                return this.cMSpec;
            },
            titleAndSubtitle() {
                return {
                    organTitle: this.printData.organ.name,
                    headerConfigTitle: this.config.title,
                    headerConfigSubtitle: this.config.subtitle,
                };
            },
            chargeTransactionTime() {
                let chargeTransactionTime = '(';
                if (this.printData?.chargeTransactionTime) {
                    return chargeTransactionTime += parseTime(this.printData.chargeTransactionTime,'m-d h:i') + ' 已退费)'
                }
                return ''
            },
        },
        watch: {
            titleAndSubtitle: {
                handler(v) {
                    const { organTitle, headerConfigTitle, headerConfigSubtitle } = v;
                    let title = '', subtitle = '';
                    title = headerConfigTitle || organTitle || '';

                    const cacheTitle = title;
                    const {
                        fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                    } = getLengthWithFullCharacter(cacheTitle, TITLE_MAX_LENGTH);
                    if (fullClinicCharacterLength > TITLE_MAX_LENGTH) {
                        title = cacheTitle.slice(0, splitClinicLength);
                        subtitle = cacheTitle.slice(splitClinicLength);
                    }

                    this.curOrganTitle = title;
                    if (subtitle) {
                        this.curOrganSubtitle = subtitle;
                    } else {
                        this.curOrganSubtitle = headerConfigSubtitle || '';
                    }
                },
                immediate: true,
                deep: true,
            }
        },
        methods: {
            isToday,
            formatAge,
            formatDiagnosis2Str
        },
    };
</script>
<style lang="scss">
@import './index';

.dispensing-A5-header-wrapper {
    .logo-img {
        width: 120pt !important;

        .barcode-img-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            line-height: 40pt;

            .bar-img {
                width: 90pt;
                height: 24pt !important;
                vertical-align: middle;
            }
        }

        .process-icon-text {
            position: absolute;
            top: 42pt;
            font-size: 10pt;
            font-weight: bold;
            .take-medicine-time {
                font-size: 12px;
            }
        }
    }

    .organ-name-new {
        height: 40pt;
        font-family: SimSun;
        font-size: 14pt;
        font-weight: bold;
        line-height: 40pt;
        text-align: center;
        position: relative;

        .sub-title-line-height {
            font-family: SimSun;
            font-size: 14pt;
            font-weight: bold;
            line-height: 20pt;
            text-align: center;
        }
      .undispense {
        font-family: SimSun;
        border: 2px solid black;
        border-radius: 8px;
        position: absolute;
        right: 0;
        top: 10px;
        width: 80px;
        height: 30px;
        line-height: 30px;
      }
    }

    .document-type {
        font-family: SimSun;

        .document-type-text-sub {
            font-size: 12pt;
            line-height: 20pt;
        }

        .document-type-text-single {
            font-size: 14pt;
            line-height: 20pt;
        }
    }
}
</style>
