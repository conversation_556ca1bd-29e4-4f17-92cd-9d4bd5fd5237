<template>
    <div
        class="print-medical-document-header custom-print-medical-prescription-header"
        :class="{'is-landscape': isLandscape}"
    >
        <div
            class="organ-name"
            :class="{
                'organ-name-13': curOrganTitle.length >= 13,
                'organ-name-14': curOrganTitle.length >= 14,
                'organ-name-15': curOrganTitle.length >= 15,
                'organ-name-no-barcode': !((headerConfig.qrcode && qrCodeSrc) || (headerConfig.barcode && barcodeSrc) || (headerConfig.logo && organ.logo))
            }"
        >
            {{ curOrganTitle }}
        </div>
        <div class="document-type">
            <div class="document-title">
                {{ printTitle }}
                <div
                    v-if="extendSpec && headerConfig.prescriptionType"
                    class="pr-type"
                >
                    {{ extendSpec }}
                </div>
            </div>
        </div>

        <div
            v-if="printJinma"
            class="jinma-style"
        >
            {{ jinmaType }}
        </div>

        <div class="head-box header-hc">
            <print-row>
                <print-col :span="20">
                    <row-box :label-width="40">
                        <span slot="label">费别：</span>
                        <div slot="content">
                            <span class="space"></span>
                            <span class="underline">自费</span>
                            <span class="space-big"></span>
                            <span class="underline">离休</span>
                            <span class="space-big"></span>
                            <span>二乙 </span>
                            <span class="space-big"></span>
                            <span>医保（自治区、市、县）</span>
                            <span class="space-big"></span>
                            <span class="space-big"></span>
                        </div>
                    </row-box>
                </print-col>
                <print-col span="4">
                    请对号划✓
                </print-col>
            </print-row>
            <print-row class="list-info">
                <print-col
                    :span="8"
                    overflow
                >
                    <row-box :label-width="40">
                        <span slot="label"> 姓名：</span>
                        <div slot="content">
                            <span style="padding-left: 6pt;">{{ patient.name }}</span>
                        </div>
                    </row-box>
                </print-col>
                <print-col
                    :span="6"
                    overflow
                >
                    <row-box :label-width="40">
                        <span slot="label">性别：</span>
                        <div slot="content">
                            <span class="space"></span>
                            {{ patient.sex }}
                            <span class="space-big"></span>
                            <span class="space"></span>
                        </div>
                    </row-box>
                </print-col>
                <print-col
                    :span="6"
                    overflow
                >
                    <row-box :label-width="40">
                        <span slot="label">年龄：</span>
                        <div slot="content">
                            <span class="space"></span>
                            {{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}
                            <span class="space-big"></span>
                            <span class="space"></span>
                        </div>
                    </row-box>
                </print-col>
            </print-row>
            <print-row class="list-info">
                <print-col
                    :span="14"
                    overflow
                >
                    <row-box :label-width="114">
                        <span slot="label">门诊/住院病历号：</span>
                        <div slot="content">
                            <span class="space"></span>
                            {{ formatPatientOrderNo(printData.patientOrderNo) }}
                            <span class="space-big"></span>
                            <span class="space"></span>
                        </div>
                    </row-box>
                </print-col>
                <print-col
                    :span="10"
                    overflow
                >
                    <row-box :label-width="40">
                        <span slot="label">
                            科室：
                        </span>
                        <div slot="content">
                            <span class="space"></span>
                            {{ printData.departmentName }}
                            <span class="space-big"></span>
                            <span class="space"></span>
                        </div>
                    </row-box>
                </print-col>
            </print-row>
            <print-row class="list-info">
                <print-col
                    :span="24"
                    overflow
                >
                    <row-box :label-width="114">
                        <span slot="label">
                            临床（初步)诊断：
                        </span>
                        <span slot="content">
                            <span class="space"></span>
                            {{ printData.diagnosis | filterDiagnose }}
                            <span v-if="printData.syndrome">（{{ printData.syndrome }}）</span>
                        </span>
                    </row-box>
                </print-col>
            </print-row>
            <print-row class="list-info">
                <print-col
                    :span="14"
                >
                    <row-box :label-width="68">
                        <span slot="label">
                            开具日期：
                        </span>
                        <div slot="content">
                            {{ getYear }}
                            年
                            {{ getMonth }}
                            月
                            {{ getDay }}
                            日
                        </div>
                    </row-box>
                </print-col>
                <print-col
                    :span="10"
                >
                    <row-box :label-width="40">
                        <span slot="label">
                            体温：
                        </span>
                        <div slot="content">
                            <span class="space"> </span>
                        </div>
                    </row-box>
                </print-col>
            </print-row>

            <div
                v-if="showRp"
                class="rp-icon"
                :class="{'is-landscape-rp': isLandscape}"
            >
                Rp<sub>&#183;</sub>
            </div>
        </div>
    </div>
</template>

<script>
    import { formatAge, formatAddress, formatPatientOrderNo, parseTime, textToBase64BarCode, getIdCardTypeStr } from '../../common/utils.js';
    import { filterMobile } from "../../common/medical-transformat.js";
    import { MARTIAL_LABEL_ENUM } from "../../common/constants.js";
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';

    import { isDate } from '@tool/date';
    import { PsychotropicNarcoticTypeEnumStr } from "../../constant/print-constant";
    import RowBox from "./row-box.vue";

    export default {
        name: 'MedicalDocumentHeader',
        components: {
            PrintRow,
            PrintCol,
            RowBox,
        },
        filters: {
            filterDiagnose(val) {
                if (!val) return '';
                const valArr = val.split('<br>');
                return valArr.join('');
            },
            filterMobile,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            headerType: {
                type: String,
                default: '',
            },
            printTitle: {
                type: String,
                default: '治疗执行单',
            },
            organTitle: {
                type: String,
                default: '',
            },
            extendSpec: String,
            logoSrc: String,
            showRp: {
                type: Boolean,
                default: true,
            },
            showProcess: {
                type: Boolean,
                default: false,
            },
            showExpress: {
                type: Boolean,
                default: false,
            },
            showVirtualPharmacy: {
                type: Boolean,
                default: false,
            },
            isLandscape: {
                type: Boolean,
                default: false,
            },
            createdTime: {
                type: String,
                validator: (value) => {
                    return isDate(value)
                },
            },
            printJinma: {
                type: Number,
                default: 0,
            },
        },
        computed: {
            patient() {
                return this.printData.patient;
            },
            getPatientInfoRenderData() {
                let renderPatient = [];
                let count = 0;
                const widthArr = [10, 8, 6]
                if (this.headerConfig.idCard) {
                    renderPatient.push({
                        label: getIdCardTypeStr(this.patient.idCardType),
                        value: this.patient.idCard,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if (this.headerConfig.fileNumber) {
                    renderPatient.push({
                        label: '档案号：',
                        value: this.patient.sn,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if (this.headerConfig.birthday) {
                    renderPatient.push({
                        label: '生日：',
                        value: this.patient.birthday,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if (this.headerConfig.computerCode) {
                    renderPatient.push({
                        label: '社保号：',
                        value: this.shebaoCardInfo.personalCode,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if (this.headerConfig.weight) {
                    renderPatient.push({
                        label: '体重：',
                        value: `${this.patient.weight ? this.patient.weight + 'kg' : ''}`,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if (this.headerConfig.married) {
                    renderPatient.push({
                        label: '婚否：',
                        value: this.getPatientMaterialName(),
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                return renderPatient;
            },

            qrCodeSrc() {
                return this.printData.qrCode;
            },
            headerConfig() {
                return this.config.header || {};
            },
            curOrganTitle() {
                if (!this.organTitle) {
                    return this.organ && this.organ.name || '';
                }
                return this.organTitle || '';
            },
            organ() {
                return this.printData.organ || '';
            },
            shebaoCardInfo() {
                return this.printData.shebaoCardInfo || {};
            },
            syndrome() {
                return this.printData.medicalRecord && this.printData.medicalRecord.syndrome || '';
            },
            clinicNameTitle() {
                let name = this.organ.name || '';
                name = name.length > 28 ? name.slice(0, 28) : name;
                const clinicTitle = [];
                if (name.length > 16) {
                    clinicTitle.push(name.slice(0, 16));
                    clinicTitle.push(name.slice(16, name.length));
                } else {
                    clinicTitle.push(name);
                }
                return clinicTitle;
            },
            barcodeSrc() {
                return textToBase64BarCode(this.printData.barcode);
            },
            curDate() {
                if (this.createdTime) {
                    return this.createdTime;
                }

                return this.printData.diagnosedDate;
            },
            getYear() {
                return this.curDate && this.curDate.split('-')[0] || ''
            },
            getMonth() {
                return this.curDate && this.curDate.split('-')[1] || ''
            },
            getDay() {
                const date = this.curDate && this.curDate.split('-')[2]
                if (date) {
                    return date.split('T')[0]
                }
                return ''
            },
            jinmaType() {
                return PsychotropicNarcoticTypeEnumStr[this.printJinma] || '';
            },
        },
        methods: {
            formatAge,
            formatAddress,
            formatPatientOrderNo,
            parseTime,
            getPatientMaterialName() {
                let status = MARTIAL_LABEL_ENUM.find(item => {
                    return item.value === this.patient.marital;
                })
                return status && status.label || '';
            },
        },
    };
</script>

<style lang="scss">
@import "index.scss";

.header-hc {
    margin-top: 15pt;
}

.head-box {
    //margin: 20pt;
    font-weight: 400;
}

.space-big {
    padding: 0 8pt;
}

.list-info {
    margin-top: 4px;
}

.custom-print-medical-prescription-header {
    .jinma-style {
        position: absolute;
        top: 0;
        right: -6pt;
        z-index: 1;
        width: 40pt;
        text-align: center;
        border: #1d1f21 solid 1pt;
    }
}
</style>
