<template>
    <div class="print-medical-document-header print-execute-header-wrapper">
        <div
            class="organ-name-new"
        >
            <template v-if="!curOrganSubtitle">
                {{ curOrganTitle }}
            </template>
            <template v-else>
                <div class="sub-title-line-height">
                    {{ curOrganTitle }}
                </div>
                <div class="sub-title-line-height">
                    {{ curOrganSubtitle }}
                </div>
            </template>
        </div>
        <div
            class="document-type"
            :class="curOrganSubtitle ? 'document-type-text-sub' : 'document-type-text-single'"
        >
            {{ printTitle }}
        </div>
        <div
            v-if="headerConfig.qrcode && qrCodeSrc"
            class="qr-code-img"
        >
            <img
                class="qr-img"
                :src="qrCodeSrc"
                alt=""
            />
        </div>

        <div
            v-if="(headerConfig.barcode && barcodeSrc) || (headerConfig.logo && organ.logo)"
            class="left-img-wrapper"
        >
            <img
                v-if="headerConfig.barcode && barcodeSrc"
                class="bar-img"
                :src="barcodeSrc"
                alt=""
            />
            <template v-else>
                <img
                    class="logo-img-content"
                    :src="organ.logo"
                    alt=""
                />
            </template>
        </div>

        <print-row
            class="base-info date-info"
            style="padding-bottom: 0; margin-top: 10pt; font-size: 10pt;"
        >
            <div
                class="patient-info"
                style="display: inline-block; min-width: 40%; max-width: 100%;"
            >
                姓名：<div style="display: inline-block; max-width: 71%; overflow: hidden; word-break: break-all; white-space: nowrap; vertical-align: bottom;">
                    {{ patientName }}
                </div>
                <span class="space"></span> {{ patient.sex }} <span class="space"></span> {{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                <span class="space"></span>
            </div>
            <div
                class="patient-info"
                style="display: inline-block;"
                :style="{width: headerConfig.revisit ? '33%' : '33%'}"
            >
                诊号：{{ formatPatientOrderNo(printData.patientOrderNo ) }}
            </div>
            <div
                class="patient-info"
                style="display: inline-block; width: 25%;"
            >
                日期：{{ printData.diagnosedDate | parseTime('y-m-d') }}
            </div>
        </print-row>

        <print-row class="base-info has-margin-top patient-info">
            <print-col :span="10">
                科室：{{ printData.departmentName }}
            </print-col>
            <print-col
                :span="8"
                style="word-break: break-all; white-space: normal;"
            >
                费别：<template v-if="headerConfig.feeType">
                    {{ printData.healthCardPayLevel }}
                </template>
                <template v-if="headerConfig.personType">
                    {{ shebaoCardInfo && shebaoCardInfo.feeType }}
                </template>
            </print-col>
            <print-col
                v-if="headerConfig.mobile"
                :span="6"
            >
                手机：{{ patient.mobile | formatMobile(headerConfig.mobileDesensitization) }}
            </print-col>
        </print-row>

        <print-row class="base-info patient-info">
            <template v-for="(headerItem, idx) in headerRenderList">
                <template v-if="headerItem.key === 'diagnosis'">
                    <print-col
                        :key="`diagnosis-${idx}`"
                        :span="headerItem.span"
                        :custom-style="headerItem.style"
                        style="overflow: visible; word-break: break-all; word-wrap: break-word; white-space: normal; vertical-align: top;"
                    >
                        诊断：{{ printData.diagnosis | filterDiagnose }}
                        <template v-if="syndrome">
                            （{{ syndrome }}）
                        </template>
                    </print-col>
                </template>

                <template v-if="headerItem.key === 'socialCode'">
                    <print-col
                        :key="`socialCode-${idx}`"
                        :span="headerItem.span"
                        style="vertical-align: top;"
                        :custom-style="headerItem.style"
                    >
                        医保号：{{ headerConfig.socialCode ? printData.healthCardNo : '' }}
                    </print-col>
                </template>

                <template v-if="headerItem.key === 'idCard'">
                    <print-col
                        v-if="headerConfig.idCard"
                        :key="`idCard-${idx}`"
                        :span="headerItem.span"
                        :custom-style="headerItem.style"
                    >
                        {{ getIdCardTypeStr(patient.idCardType) }}：{{ filterIdCard(patient.idCard, headerConfig.idCardDesensitization) }}
                    </print-col>
                </template>

                <template v-if="headerItem.key === 'computerCode'">
                    <print-col
                        :key="`computerCode-${idx}`"
                        style="word-break: break-all; white-space: normal;"
                        :span="headerItem.span"
                        :custom-style="headerItem.style"
                    >
                        个人编号：{{ shebaoCardInfo.personalCode || (shebaoCardInfo.extend && shebaoCardInfo.extend.personalCode) }}
                    </print-col>
                </template>

                <template v-if="headerItem.key === 'fileNumber'">
                    <print-col
                        :key="`fileNumber-${idx}`"
                        :span="headerItem.span"
                        :custom-style="headerItem.style"
                    >
                        档案号：{{ patient.sn }}
                    </print-col>
                </template>
            </template>
        </print-row>

        <template v-if="isFromExamination">
            <!--主诉-->
            <print-row
                v-if="headerConfig.chiefComplaint"
                class="base-info patient-info"
            >
                <print-col
                    :span="24"
                    style="overflow: visible; word-break: break-all; word-wrap: break-word; white-space: normal; vertical-align: top;"
                >
                    <div class="medical-item">
                        <div>
                            <span class="medical-item-label">
                                主 诉
                            </span>

                            <span>
                                ：
                            </span>
                        </div>
                        <span class="medical-item-content">
                            {{ printData.chiefComplaint }}
                        </span>
                    </div>
                </print-col>
            </print-row>

            <!--过敏史-->
            <print-row
                v-if="headerConfig.allergicHistory"
                class="base-info patient-info"
            >
                <print-col
                    :span="24"
                    style="overflow: visible; word-break: break-all; word-wrap: break-word; white-space: normal; vertical-align: top;"
                >
                    <div class="medical-item">
                        <div>
                            <span class="medical-item-label">
                                过 敏 史
                            </span>
                            <span>
                                ：
                            </span>
                        </div>
                        <span class="medical-item-content">
                            {{ printData.allergicHistory }}
                        </span>
                    </div>
                </print-col>
            </print-row>

            <!--诊断-->
            <print-row
                class="base-info patient-info"
            >
                <print-col
                    :span="24"
                    style="overflow: visible; word-break: break-all; word-wrap: break-word; white-space: normal; vertical-align: top;"
                >
                    <div class="medical-item">
                        <div>
                            <span class="medical-item-label">
                                诊 断
                            </span>

                            <span>
                                ：
                            </span>
                        </div>

                        <span class="medical-item-content">
                            {{ printData.diagnosis | filterDiagnose }}
                            <template v-if="syndrome">
                                （{{ syndrome }}）
                            </template>
                        </span>
                    </div>
                </print-col>
            </print-row>
        </template>

        <print-row class="line-split"></print-row>
    </div>
</template>

<script>
    import {
        formatAge,
        formatPatientOrderNo, getIdCardTypeStr,
        getLengthWithFullCharacter,
        parseTime,
        textToBase64BarCode,
    } from '../../common/utils.js';
    import {filterIdCard, filterMobileV2, formatMobile} from "../../common/medical-transformat.js";
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';
    import { TITLE_MAX_LENGTH } from "../../common/constants";
    import { ExecuteHeaderSourceType } from './constant'

    export default {
        name: 'MedicalDocumentHeader',
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            filterDiagnose(val) {
                if (!val) return '';
                const valArr = val.split('<br>');
                return valArr.join('');
            },
            filterMobileV2,
            parseTime,
            formatMobile,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            printTitle: {
                type: String,
                default: '治疗执行单',
            },
            organTitle: {
                type: String,
                default: "",
            },
            sourceType: {
                type: Number,
                default: -1,
            },
        },
        data() {
            return {
                curOrganTitle: '',
                curOrganSubtitle: '',
            }
        },
        computed: {
            patient() {
                return this.printData.patient || {};
            },

            patientName() {
                return this.patient.name
            },
            qrCodeSrc() {
                return this.printData.qrCode;
            },
            headerConfig() {
                return this.config.header || {};
            },
            organ() {
                return this.printData.organ || {};
            },
            syndrome() {
                return this.printData.syndrome || '';
            },
            shebaoCardInfo() {
                return this.printData.shebaoCardInfo || {};
            },
            clinicNameTitle() {
                let name = this.organ.name || '';
                name = name.length > 28 ? name.slice(0, 28) : name;
                const clinicTitle = [];
                if (name.length > 16) {
                    clinicTitle.push(name.slice(0, 16));
                    clinicTitle.push(name.slice(16, name.length));
                } else {
                    clinicTitle.push(name);
                }
                return clinicTitle;
            },
            barcodeSrc() {
                const barcode = this.printData.patientOrderNo ? `${this.printData.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode);
            },
            titleAndSubtitle() {
                return {
                    organTitle: this.organTitle,
                    headerConfigSubtitle: this.headerConfig.subtitle,
                    organName: this.organ.name,
                };
            },
            isFromExamination() {
                return this.sourceType === ExecuteHeaderSourceType.examination;
            },
            isFromTreatmentExecute() {
                return this.sourceType === ExecuteHeaderSourceType.treatmentExecute;
            },

            isRenderDiagnosis() {
                if(this.isFromTreatmentExecute) {
                    return !!this.headerConfig.diagnosis;
                }

                return true
            },

            headerRenderList() {
                const {
                    socialCode,
                    idCard,
                    computerCode,
                    fileNumber,
                } = this.headerConfig;

                const renderList = [];

                if(this.isRenderDiagnosis && !this.isFromExamination) {
                    renderList.push({
                        key: 'diagnosis',
                        span: socialCode ? 18 : 24,
                        style: {
                            marginBottom: '6pt',
                        },
                    });
                }

                if(socialCode) {
                    renderList.push({
                        key: 'socialCode',
                        span: 6,
                    });
                }

                if(idCard) {
                    renderList.push({
                        key: 'idCard',
                        span: 10,
                    });
                }

                if(computerCode) {
                    renderList.push({
                        key: 'computerCode',
                        span: 8,
                    });
                }

                if(fileNumber) {
                    renderList.push({
                        key: 'fileNumber',
                        span: 6,
                    });
                }

                // 非检查检验单的单据且渲染诊断，布局基本固定，无需调整
                if(this.isRenderDiagnosis && !this.isFromExamination) {
                    return renderList;
                }

                // 按 10 8 6 顺序填充空余
                const realRenderList = [];
                const spanList = [10, 8, 6];
                const len = renderList.length;

                for (let i = 0; i < len; i++) {
                    const curFindSpan = spanList[i % 3];
                    let targetItemIdx = renderList.findIndex(item => item.span === curFindSpan);

                    if(targetItemIdx === -1) {
                        targetItemIdx = renderList.findIndex(item => item.span < curFindSpan)
                    }

                    if(targetItemIdx === -1) {
                        targetItemIdx = i;
                    }

                    renderList[targetItemIdx].span = curFindSpan;

                    realRenderList.push(renderList[targetItemIdx]);
                    renderList.splice(targetItemIdx, 1);
                }

                const realRenderLen = realRenderList.length;
                const offset = realRenderLen % 3 === 0 ? 3 : realRenderLen % 3;
                realRenderList.forEach((item,idx) => {
                    if(idx < realRenderLen - offset) {
                        item.style = {
                            marginBottom: '6pt',
                        }
                    }
                })

                return realRenderList;
            },
        },
        watch: {
            titleAndSubtitle: {
                handler(v) {
                    const { organTitle, headerConfigSubtitle, organName } = v;
                    let title = '', subtitle = '';
                    if(!organTitle) {
                        title = organName || '';
                    } else {
                        title = organTitle || '';
                    }

                    const cacheTitle = title;
                    const {
                        fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                    } = getLengthWithFullCharacter(cacheTitle, TITLE_MAX_LENGTH);
                    if (fullClinicCharacterLength > TITLE_MAX_LENGTH) {
                        title = cacheTitle.slice(0, splitClinicLength);
                        subtitle = cacheTitle.slice(splitClinicLength);
                    }

                    this.curOrganTitle = title;
                    if (subtitle) {
                        this.curOrganSubtitle = subtitle;
                    } else {
                        this.curOrganSubtitle = headerConfigSubtitle || '';
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            filterIdCard,
            getIdCardTypeStr,
            formatAge,
            formatPatientOrderNo,
        },
    };
</script>

<style lang="scss">
@import "index.scss";

.print-execute-header-wrapper {
    .organ-name-new {
        height: 40pt;
        font-family: SimSun;
        font-size: 14pt;
        font-weight: bold;
        line-height: 40pt;
        text-align: center;

        .sub-title-line-height {
            font-family: SimSun;
            font-size: 14pt;
            font-weight: bold;
            line-height: 20pt;
            text-align: center;
        }
    }

    .left-img-wrapper {
        position: absolute;
        top: 0;
        left: -6pt;
        width: 90pt;
        height: 40pt;
        line-height: 40pt;

        .bar-img {
            width: 100%;
            height: 24pt;
            vertical-align: middle;
        }

        .logo-img-content {
            max-width: 100%;
            max-height: 100%;
            vertical-align: middle;
        }
    }

    .document-type {
        font-family: SimSun;

        &.document-type-text-sub {
            font-size: 12pt;
            line-height: 20pt;
        }

        &.document-type-text-single {
            font-size: 14pt;
            line-height: 20pt;
        }
    }

    .medical-item {
        display: flex;

        .medical-item-label {
            display: inline-block;
            width: 50pt;
            text-align: justify;
            text-align-last: justify;
            text-justify: distribute;
            word-break: break-all;
        }

        .medical-item-content {
            flex: 1;
        }
    }
}
</style>
