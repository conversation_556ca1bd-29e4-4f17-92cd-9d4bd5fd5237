<template>
    <div class="shandong-header">
        <div
            class="print-medical-document-header print-medical-header-wrapper"
            :class="{'is-landscape': isLandscape}"
        >
            <div
                class="organ-name-new"
                :style="!curOrganSubtitle ? 'height: 20pt' : 'height 40pt'"
            >
                <template v-if="!curOrganSubtitle">
                    {{ curOrganTitle }}
                </template>
                <template v-else>
                    <div>
                        {{ curOrganTitle }}
                    </div>
                    <div>
                        {{ curOrganSubtitle }}
                    </div>
                </template>
            </div>
            <div class="document-type">
                <div
                    class="document-title"
                    :class="curOrganSubtitle ? 'document-type-text-sub' : 'document-type-text-single'"
                >
                    {{ printTitle }}
                    <template v-if="handleExtendSpec && headerConfig.prescriptionType">
                        （{{ handleExtendSpec }}）
                    </template>
                </div>
            </div>
            <div
                v-if="printJinma"
                class="jinma-style"
                style="border-radius: 50%;width: 58pt;height: 30pt;line-height: 30pt;top: 6pt;right: 63pt;"
            >
                {{ jinmaType }}
            </div>

            <print-row
                class="base-info has-margin-top patient-info"
                style="border-bottom: 1px solid #000;"
            >
                <print-col
                    :span="!isGlasses ? 7 : 14"
                    overflow
                >
                    科别：{{ printData.departmentName }}
                </print-col>
                <!-- 配镜处方不显示该项 -->
                <print-col
                    v-if="!isGlasses"
                    :span="4"
                    overflow
                >
                    费别：<template v-if="headerConfig.feeType">
                        {{ formatHealthCardPayLevel(printData.healthCardPayLevel) }}
                    </template>
                    <template v-if="headerConfig.personType">
                        {{ shebaoCardInfo && shebaoCardInfo.feeType }}
                    </template>
                </print-col>
                <print-col
                    :span="6"
                >
                    门诊号：{{ formatBarcode(printData.barcode) }}
                </print-col>
                <print-col
                    :span="7"
                    style="text-align: right;"
                >
                    {{ getYear }}
                    年
                    {{ getMonth }}
                    月
                    {{ getDay }}
                    日
                </print-col>
            </print-row>

            <print-row
                class="base-info date-info "
                style="padding-bottom: 0; margin-top: 10pt; font-size: 10pt;"
            >
                <print-col
                    :span="11"
                    overflow
                >
                    姓名：{{ patientName }}
                </print-col>
                <print-col
                    :span="7"
                    overflow
                >
                    年龄：{{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                </print-col>
                <print-col
                    :span="6"
                    overflow
                >
                    <span class="space"></span>
                    性别：{{ patient.sex }}
                </print-col>
            </print-row>

            <print-row
                class="base-info patient-info"
                style="margin-top: 10px;"
            >
                <print-col
                    :span="24"
                    overflow
                >
                    临床诊断：{{ printData.diagnosis | filterDiagnose }}
                    <template v-if="syndrome">
                        （{{ syndrome }}）
                    </template>
                </print-col>
            </print-row>

            <print-row class="line-split"></print-row>
            <div
                v-if="!isGlasses && showRp"
                class="rp-icon"
                :class="{'is-landscape-rp': isLandscape}"
            >
                Rp<sub>&#183;</sub>
            </div>
        </div>
    </div>
</template>

<script>
    import {
        formatAge,
        formatAddress,
        formatPatientOrderNo,
        parseTime,
        textToBase64BarCode,
        getLengthWithFullCharacter,
    } from '../../common/utils.js';
    import { filterMobileV2 } from "../../common/medical-transformat.js";
    import { MARTIAL_LABEL_ENUM, TITLE_MAX_LENGTH } from "../../common/constants.js";
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';
    import { PsychotropicNarcoticTypeEnumStr } from "../../constant/print-constant";
    export default {
        name: 'ShandongHeader',
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            filterDiagnose(val) {
                if (!val) return '';
                const valArr = val.split('<br>');
                return valArr.join('');
            },
            filterMobileV2,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            printTitle: {
                type: String,
                default: '治疗执行单',
            },
            organTitle: {
                type: String,
                default: '',
            },
            extendSpec: {
                type: String,
                default: '',
            },
            showRp: {
                type: Boolean,
                default: true,
            },
            isLandscape: {
                type: Boolean,
                default: false,
            },
            printJinma: {
                type: Number,
                default: 0,
            },
            isGlasses: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                curOrganTitle: '',
                curOrganSubtitle: '',
            }
        },
        computed: {
            patient() {
                return this.printData.patient || {};
            },
            patientName() {
                return this.patient.name
            },
            isMutiPharmacy() {
                return this.printData.openPharmacyFlag === 20 || false
            },
            headerConfig() {
                return this.config.header || {};
            },
            organ() {
                return this.printData.organ || {};
            },
            shebaoCardInfo() {
                return this.printData.shebaoCardInfo || {};
            },
            syndrome() {
                return this.printData.medicalRecord && this.printData.medicalRecord.syndrome || '';
            },
            barcodeSrc() {
                const barcode = this.printData.patientOrderNo ? `${this.printData.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode);
            },
            // 处方的时间都统一显示为完成诊断时间，有客户反馈处方打印的时间会因为处方修改而跟门诊单开出的时间不一致
            curDate() {
                return this.printData.diagnosedDate;
            },
            getYear() {
                return this.curDate && this.curDate.split('-')[0] || ''
            },
            getMonth() {
                return this.curDate && this.curDate.split('-')[1] || ''
            },
            getDay() {
                const date = this.curDate && this.curDate.split('-')[2]
                if (date) {
                    return date.split('T')[0]
                }
                return ''
            },
            jinmaType() {
                return PsychotropicNarcoticTypeEnumStr[this.printJinma] || '';
            },
            handleExtendSpec() {
                if (this.extendSpec === '中药饮片') {
                    return '饮片';
                }
                if (this.extendSpec === '中药颗粒') {
                    return '颗粒';
                }
                return this.extendSpec;
            },
            titleAndSubtitle() {
                return {
                    organTitle: this.organTitle,
                    headerConfigSubtitle: this.headerConfig.subtitle,
                    organName: this.organ.name,
                };
            },
        },
        watch: {
            titleAndSubtitle: {
                handler(v) {
                    const { organTitle, headerConfigSubtitle, organName } = v;
                    let title = '', subtitle = '';
                    if(!organTitle) {
                        title = organName || '';
                    } else {
                        title = organTitle || '';
                    }

                    const cacheTitle = title;
                    const {
                        fullCharacterLength, splitLength,
                    } = getLengthWithFullCharacter(cacheTitle, TITLE_MAX_LENGTH);
                    if (fullCharacterLength > TITLE_MAX_LENGTH) {
                        title = cacheTitle.slice(0, splitLength);
                        subtitle = cacheTitle.slice(splitLength);
                    }

                    this.curOrganTitle = title;
                    if (subtitle) {
                        this.curOrganSubtitle = subtitle;
                    } else {
                        this.curOrganSubtitle = headerConfigSubtitle || '';
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            formatAge,
            formatAddress,
            formatPatientOrderNo,
            parseTime,
            getPatientMaterialName() {
                let status = MARTIAL_LABEL_ENUM.find( item => {
                    return item.value === this.patient.marital;
                })
                return status && status.label || '';
            },
            formatBarcode(barcode) {
                if (barcode) return barcode.slice(0,8)
                return ''
            },
            formatHealthCardPayLevel(healthCardPayLevel) {
                if (healthCardPayLevel) return healthCardPayLevel.slice(0,2)
                return ''
            },
        },
    };
</script>

<style lang="scss">
@import "index.scss";

.shandong-header{
    .print-medical-header-wrapper {
        .organ-name-new {
            height: 40pt;
            font-family: SimSun;
            font-size: 14pt;
            font-weight: 300;
            line-height: 20pt;
            text-align: center;

            .sub-title-line-height {
            font-family: SimSun;
            font-size: 14pt;
            font-weight: bold;
            line-height: 20pt;
            text-align: center;
            }
        }

        .document-type {
            font-family: SimSun;
            letter-spacing: 3pt;
            .document-type-text-sub {
                margin-top: 2pt;
                font-size: 17pt;
                line-height: 20pt;
                font-weight: bold;
            }

            .document-type-text-single {
                margin-top: 2pt;
                font-size: 19pt;
                line-height: 20pt;
                font-weight: bold;
            }
        }

        .left-img-wrapper {
            position: absolute;
            top: 0;
            left: -6pt;
            width: 90pt;
            height: 40pt;
            line-height: 40pt;

            .bar-img {
                width: 100%;
                height: 24pt;
                vertical-align: middle;
            }

            .logo-img-content {
                width: 100%;
                height: auto;
                max-height: 100%;
                vertical-align: middle;
            }
        }

        .process-text {
            position: absolute;
            top: 42pt;
            left: 0;
            font-size: 10pt;
            font-weight: bold;
        }
    }
}
</style>
