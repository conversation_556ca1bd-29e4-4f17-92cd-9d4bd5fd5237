<template>
    <div :class="['print-medical-document-header', { 'is-landscape': isLandscape }]">
        <div
            :class="['organ-name cur-organ-title', {
                'organ-name-13': curOrganTitle.length >= 13,
                'organ-name-14': curOrganTitle.length >= 14,
                'organ-name-15': curOrganTitle.length >= 15
            }]"
        >
            {{ curOrganTitle }}
        </div>
        <!-- 处方追溯码 -->
        <template v-if="printData.traceabilityInfo">
            <div class="traceability-info">
                <img
                    :src="printData.traceabilityInfo.rxTraceQrCode"
                    :class="curOrganTitle.length > 19 ? 'traceability-code' : 'traceability-code-small'"
                    alt=""
                />
                <span>{{ printData.traceabilityInfo.rxTraceCode }}</span>
                <span>医保电子处方追溯码</span>
            </div>
        </template>
        <!-- 机构公章 排除吉林 && 有机构公章 -->
        <template v-if="!isJiLin && printData.institutionalSeal">
            <img
                :src="printData.institutionalSeal"
                class="institutional-seal"
                alt=""
            />
        </template>
        <!-- 处方标识 -->
        <div class="identification">
            普通处方
        </div>
        <!-- 处方名称 -->
        <div class="document-type">
            <div class="document-title">
                处方签
                <template v-if="extendSpec && headerConfig.prescriptionType">
                    <div class="pr-type">
                        {{ extendSpec }}
                    </div>
                </template>
            </div>
        </div>
        <template v-if="headerConfig.qrcode && qrCodeSrc && !printJinma">
            <div class="qr-code-img">
                <img
                    class="qr-img"
                    :src="qrCodeSrc"
                    alt=""
                />
            </div>
        </template>
        <div
            v-if="printJinma"
            class="jinma-style"
        >
            {{ jinmaType }}
        </div>

        <print-row class="base-info has-margin-top patient-info">
            <print-col
                :span="12"
                overflow
            >
                <div>病例号：{{ printData.mdtrtId }}</div>
            </print-col>
            <print-col
                :span="12"
                overflow
            >
                <div>处方编号：{{ printData.patientOrderNo }}</div>
            </print-col>

            <print-col :span="7">
                <div>机构编码：{{ printData.organ.hospitalCode || '' }}</div>
            </print-col>
            <print-col :span="7">
                <div>医生编码：{{ printData.doctorCode || '' }}</div>
            </print-col>
            <print-col :span="4" overflow>
                <div>
                    费别：{{ printData.healthCardPayLevel || '处方外购' }}
                    <template v-if="headerConfig.personType">
                        {{ shebaoCardInfo.feeType }}
                    </template>
                </div>
            </print-col>
            <print-col :span="6">
                <div>开具日期：{{ printData.prscTime }}</div>
            </print-col>
            <print-col
                :span="7"
                overflow
            >
                <div>科别和床位：{{ printData.departmentName }}</div>
            </print-col>
            <print-col :span="7">
                <div>姓名：{{ patientName }}</div>
            </print-col>
            <print-col :span="4">
                <div>性别：{{ patient.sex }}</div>
            </print-col>
            <print-col :span="6">
                <div>年龄：{{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}</div>
            </print-col>
            <print-col :span="isJiLin ? 14 : 24">
                <div>
                    临床诊断：{{ printData.diagnosis | filterDiagnose }}
                    <template v-if="syndrome">
                        {{ syndrome }}
                    </template>
                </div>
            </print-col>
            <template v-if="isJiLin">
                <print-col :span="10">
                    <div>处方有效天数：{{ printData.valiDays }} 天</div>
                </print-col>
            </template>
        </print-row>
        <print-row class="line-split"></print-row>
        <div
            v-if="showRp"
            class="rp-icon"
            :class="{'is-landscape-rp': isLandscape}"
        >
            Rp<sub>&#183;</sub>
        </div>
    </div>
</template>

<script>
    import {
        formatAge,
        formatAddress,
        formatPatientOrderNo,
        parseTime,
        textToBase64BarCode,
        getIdCardTypeStr
    } from '../../common/utils.js';
    import { filterMobile } from "../../common/medical-transformat.js";
    import { MARTIAL_LABEL_ENUM } from "../../common/constants.js";
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';

    export default {
        name: 'MedicalDocumentHeader',
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            filterDiagnose(val) {
                if (!val) return '';
                const valArr = val.split('<br>');
                return valArr.join('');
            },
            filterMobile,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            headerType: {
                type: String,
                default: '',
            },

            organTitle: {
                type: String,
                default: '',
            },
            extendSpec: String,
            logoSrc: String,
            showRp: {
                type: Boolean,
                default: true
            },
            showProcess: {
                type: Boolean,
                default: false,
            },
            showExpress: {
                type: Boolean,
                default: false,
            },
            showVirtualPharmacy: {
                type: Boolean,
                default: false,
            },
            isLandscape: {
                type: Boolean,
                default: false
            },
            printJinma: {
                type: Number,
                default: 0
            }
        },
        computed: {
            patient() {
                return this.printData.patient || {};
            },
            patientName() {
                return this.patient.name && this.printData.patient.name.slice(0,8)
            },
            isJiLin() {
                return this.organ.region?.startsWith('jilin')
            },
            getPatientInfoRenderData() {
                let renderPatient = [];
                let count = 0;
                const widthArr = [10, 8, 6]
                if(this.headerConfig.idCard) {
                    renderPatient.push({
                        label: `${getIdCardTypeStr(this.patient.idCardType)}：`,
                        value: this.patient.idCard,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.fileNumber) {
                    renderPatient.push({
                        label: '档案号：',
                        value: this.patient.sn,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.birthday) {
                    renderPatient.push({
                        label: '生日：',
                        value: this.patient.birthday,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.computerCode) {
                    renderPatient.push({
                        label: '社保号：',
                        value: this.shebaoCardInfo.personalCode,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.weight) {
                    renderPatient.push({
                        label: '体重：',
                        value: `${this.patient.weight ? `${this.patient.weight}kg` : ''}`,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.married) {
                    renderPatient.push({
                        label: '婚否：',
                        value: this.getPatientMaterialName(),
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.nation) {
                    renderPatient.push({
                        label: '民族：',
                        value: this.patient.ethnicity,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.address) {
                    renderPatient.push({
                        label: '地址：',
                        value: formatAddress(this.patient.address),
                        width: count % 3 === 0 ? 24: 14,
                    })
                    count++;
                }
                return renderPatient;
            },

            qrCodeSrc() {
                return this.printData.qrCode;
            },
            headerConfig() {
                return this.config.header || {};
            },
            curOrganTitle() {
                if(!this.organTitle) {
                    return this.organ && this.organ.name || '';
                }
                return this.organTitle || '';
            },
            organ() {
                return this.printData.organ || '';
            },
            shebaoCardInfo() {
                return this.printData.shebaoCardInfo || {};
            },
            syndrome() {
                return this.printData.medicalRecord && this.printData.medicalRecord.syndrome || '';
            },
            clinicNameTitle() {
                let name = this.organ.name || '';
                name = name.length > 28 ? name.slice(0, 28) : name;
                const clinicTitle = [];
                if (name.length > 16) {
                    clinicTitle.push(name.slice(0, 16));
                    clinicTitle.push(name.slice(16, name.length));
                } else {
                    clinicTitle.push(name);
                }
                return clinicTitle;
            },
            barcodeSrc() {
                return textToBase64BarCode(this.printData.barcode);
            },
            jinmaType() {
                return this.printJinma === 1 ? '精一' : this.printJinma === 2 ? '精二' : this.printJinma === 3 ? '麻' : this.printJinma === 4 ? '毒' : ''
            }
        },
        methods: {
            formatAge,
            formatAddress,
            formatPatientOrderNo,
            parseTime,
            getPatientMaterialName() {
                let status = MARTIAL_LABEL_ENUM.find( item => {
                    return item.value === this.patient.marital;
                })
                return status && status.label || '';
            },
        }
    }
</script>

<style lang="scss">
.print-medical-document-header {
    position: relative;
    box-sizing: border-box;
    padding-top: 45px;

    .traceability-info {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 7pt;

        .traceability-code-small {
            width: 19mm;
            height: 19mm;
        }

        .traceability-code {
            width: 22mm;
            height: 22mm;
        }
    }

    .cur-organ-title {
        position: relative;
        z-index: 1;
        font-size: 11pt;
    }

    .institutional-seal {
        position: absolute;
        top: -7pt;
        left: 50%;
        width: 80pt;
        height: 80pt;
        margin-left: -40pt;
    }

    .identification {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 62px;
        padding: 16px 0;
        font-size: 7pt;
        border: 1px solid #000000;
    }

    .print-col {
        overflow: hidden;
        font-size: 10pt;
        word-break: keep-all;
        white-space: nowrap;
    }

    .organ-name {
        max-height: 42pt;
        padding: 0 88pt;
        overflow-y: hidden;
        font-size: 15pt;
        line-height: 21pt;
        text-align: center;
        word-break: break-all;

        &.organ-name-13 {
            max-height: 36pt;
            font-size: 14pt;
            line-height: 18pt;
        }

        &.organ-name-14 {
            max-height: 34pt;
            font-size: 13pt;
            line-height: 17pt;
        }

        &.organ-name-15 {
            max-height: 30pt;
            font-size: 11pt;
            line-height: 15pt;
        }
    }

    .document-type {
        font-size: 13pt;
        line-height: 19pt;
        text-align: center;

        .document-title {
            position: relative;
            display: inline-block;
            padding: 1pt 2pt;
        }

        .pr-type {
            position: absolute;
            top: 0;
            right: -86pt;
            width: 80pt;
            font-size: 11pt;
            font-weight: 300;
            text-align: left;
        }
    }

    .qr-code-img {
        position: absolute;
        top: 0;
        right: -6pt;
        z-index: 1;
        width: 50pt;
        height: 50pt;

        > img {
            width: 50pt;
            height: 50pt;
        }
    }

    .logo-img {
        position: absolute;
        top: 0;
        left: -6pt;
        width: 94pt;
        height: 40pt;

        > img {
            width: 100%;
            height: 100%;
        }

        .logo {
            width: 94pt;
            height: auto;
            max-height: 100%;
        }

        .process-icon {
            font-size: 10pt;
            font-weight: 500;
        }
    }

    .base-info {
        font-weight: 300;
        line-height: 12pt;
    }

    .has-margin-top {
        margin-top: 5pt;
    }

    .patient-info {
        padding-top: 6pt;
        margin: 6pt 0;
        line-height: 18pt;
        border-top: 1px solid #000000;

        .print-col div {
            font-size: 8pt;
        }
    }

    .line-split {
        height: 0;
        border-top: 1px solid #000000;
    }

    .rp-icon {
        font-size: 10pt;
        line-height: 19pt;
    }

    .jinma-style {
        position: absolute;
        top: 0;
        right: -6pt;
        z-index: 1;
        width: 40pt;
        text-align: center;
        border: #1d1f21 solid 1pt;
    }

    .examination-header {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
        min-height: 50pt;
        margin-bottom: 20pt;

        .left {
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 91pt;
            height: 100%;
            min-height: 50pt;

            >img {
                width: 91pt;
                height: 100%;
            }

            >span {
                display: inline-flex;
                align-content: center;
                justify-content: space-between;
                width: 100%;
                font-size: 10pt;
                line-height: 12pt;
            }
        }

        .center {
            width: 100%;
            padding: 0 50pt 0 91pt;
        }

        .right {
            position: absolute;
            top: 50%;
            right: 0;
            width: 50pt;
            height: 50pt;
            margin-top: -25pt;

            >img {
                width: 100%;
                height: 100%;
            }
        }
    }
}

</style>
