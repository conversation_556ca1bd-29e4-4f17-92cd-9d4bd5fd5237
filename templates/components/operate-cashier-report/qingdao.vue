<template>
    <div>
        <table
            class="report-table"
            style="margin-top: 20pt;"
        >
            <tbody>
                <tr>
                    <td
                        colSpan="6"
                        class="title-td"
                    >
                        收费方式
                    </td>
                </tr>
                <tr v-for="(rowItem,index) in payModeList">
                    <template v-for="(payItem,indexItem) in rowItem">
                        <td colspan="1">
                            {{ payItem.name }}
                        </td>
                        <td :colspan="(index * 3 + indexItem) === payMode.length - 1 ? lastRowCol : 1">
                            {{ payItem.value }}
                        </td>
                    </template>
                </tr>
            </tbody>
        </table>

        <table
            class="report-table"
            style="margin-top: 20pt;"
        >
            <tbody>
                <tr>
                    <td
                        colSpan="6"
                        class="title-td"
                    >
                        医保
                    </td>
                </tr>
                <tr>
                    <td
                        colSpan="1"
                        rowSpan="2"
                    >
                        普通门诊
                    </td>
                    <td colSpan="1">
                        统筹支付
                    </td>
                    <td colSpan="1">
                        {{ extraInfo.normalOutpatient.fundPayment || 0 }}
                    </td>
                    <td
                        colSpan="1"
                        rowSpan="2"
                    >
                        大病门诊
                    </td>
                    <td colSpan="1">
                        统筹支付
                    </td>
                    <td colSpan="1">
                        {{ extraInfo.seriousIllnessOutpatient.fundPayment || 0 }}
                    </td>
                </tr>
                <tr>
                    <td colSpan="1">
                        个人负担
                    </td>
                    <td colSpan="1">
                        {{ extraInfo.normalOutpatient.personalBurden || 0 }}
                    </td>
                    <td colSpan="1">
                        个人负担
                    </td>
                    <td colSpan="1">
                        {{ extraInfo.seriousIllnessOutpatient.personalBurden || 0 }}
                    </td>
                </tr>
                <tr>
                    <td
                        colSpan="1"
                        rowSpan="2"
                    >
                        长期护理
                    </td>
                    <td colSpan="1">
                        统筹支付
                    </td>
                    <td colSpan="1">
                        {{ extraInfo.longCare.fundPayment || 0 }}
                    </td>
                    <td colSpan="1"></td>
                    <td colSpan="1"></td>
                    <td colSpan="1"></td>
                </tr>
                <tr>
                    <td colSpan="1">
                        个人负担
                    </td>
                    <td colSpan="1">
                        {{ extraInfo.longCare.personalBurden || 0 }}
                    </td>
                    <td colSpan="1"></td>
                    <td colSpan="1"></td>
                    <td colSpan="1"></td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
    export default {
        name: 'Qingdao',
        props: {
            payMode: {
                type: Array,
                default: () => {
                    return [
                        {name: "微信", value: 246.71},
                        {name: "银行卡", value: 72.71},
                        {name: "支付宝", value: 6.01},
                        {name: "现金", value: 4.03},
                        {name: "医保卡", value: 51},
                        {name: "自定义收费方", value: 113},
                        {name: "外诊报销", value: 237.82}
                    ];
                },
            },
            extraInfo: {
                type: Object,
                default: () => {
                    return {}
                },
            },
        },
        data() {
            return {
                lastRow: false,
            }
        },
        computed: {
            payModeList() {
                return this.splitPayMode(this.payMode, 3)
            },
            lastRowCol() {
                return this.lastRow === 2 ? 3 : this.lastRow === 1 ? 5 : 1
            }
        },

        methods:{
            splitPayMode(payModeList, spiltLength) {
                let length = payModeList.length;
                let newPayModeList = [];
                for (let i = 0; i < length; i += spiltLength) {
                    newPayModeList.push(payModeList.slice(i,i + spiltLength))
                    if (i + spiltLength > length) {
                        this.lastRow = length % 3
                    }
                }
                return newPayModeList
            }
        },

    };
</script>
