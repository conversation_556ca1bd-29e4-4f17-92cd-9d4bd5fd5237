<template>
    <div>
        <table
            class="report-table"
            style="margin-top: 20pt;"
        >
            <tbody>
                <tr>
                    <td
                        colSpan="6"
                        class="title-td"
                    >
                        收费方式
                    </td>
                </tr>
                <tr v-for="(rowItem,index) in payModeList">
                    <template v-for="(payItem, itemIndex) in rowItem">
                        <td colspan="1">
                            {{ payItem.name }}
                        </td>
                        <td :colspan="(index * 3 + itemIndex) === payMode.length - 1 ? lastRowCol : 1">
                            {{ payItem.value }}
                        </td>
                    </template>
                </tr>
            </tbody>
        </table>

        <table
            v-if="isNeimenggu"
            class="report-table"
            style="margin-top: 20px;"
        >
            <tbody>
                <tr>
                    <td
                        colspan="6"
                        class="title-td"
                    >
                        医保
                    </td>
                </tr>
                <tr v-for="(rowItem,index) in neiMengGuMedicalInsurance">
                    <template v-for="(item, itemIndex) in rowItem">
                        <td colspan="1">
                            {{ item.name }}
                        </td>
                        <td :colspan="(index * 3 + itemIndex) === nationalMedicalInsurance.length - 1 ? lastMedicalRowCol : 1">
                            {{ item.value }}
                        </td>
                    </template>
                </tr>
            </tbody>
        </table>
        <table
            v-else
            class="report-table"
            style="margin-top: 20px;"
        >
            <tbody>
                <tr>
                    <td
                        colspan="6"
                        class="title-td"
                    >
                        医保
                    </td>
                </tr>
                <tr v-if="nationalMedicalInsurance && nationalMedicalInsurance.length">
                    <td
                        colspan="1"
                        rowspan="2"
                    >
                        {{ nationalMedicalInsurance[0].name || 0 }}
                    </td>
                    <td colspan="1">
                        医保支付
                    </td>
                    <td colspan="1">
                        {{ nationalMedicalInsurance[0].medicalInsurancePay || 0 }}
                    </td>
                    <td
                        v-if="nationalMedicalInsurance.length > 1"
                        colspan="1"
                        rowspan="2"
                    >
                        {{ nationalMedicalInsurance[1].name }}
                    </td>
                    <td colspan="1">
                        医保支付
                    </td>
                    <td
                        v-if="nationalMedicalInsurance.length > 1"
                        colspan="1"
                    >
                        {{ nationalMedicalInsurance[1].medicalInsurancePay || 0 }}
                    </td>
                </tr>
                <tr v-if="nationalMedicalInsurance.length > 1">
                    <td colspan="1">
                        现金支付
                    </td>
                    <td colspan="1">
                        {{ nationalMedicalInsurance[0].cashPay || 0 }}
                    </td>
                    <td colspan="1">
                        现金支付
                    </td>
                    <td
                        colspan="1"
                    >
                        {{ nationalMedicalInsurance[1].cashPay || 0 }}
                    </td>
                </tr>
                <template v-if="nationalMedicalInsurance.length > 2">
                    <tr>
                        <td
                            colspan="1"
                            rowspan="2"
                        >
                            {{ nationalMedicalInsurance[2].name }}
                        </td>
                        <td colspan="1">
                            医保支付
                        </td>
                        <td colspan="1">
                            {{ nationalMedicalInsurance[2].medicalInsurancePay || 0 }}
                        </td>
                        <td
                            colspan="1"
                            rowspan="1"
                        >
                        </td>
                        <td colspan="1">
                        </td>
                        <td colspan="1">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="1">
                            现金支付
                        </td>
                        <td colspan="1">
                            {{ nationalMedicalInsurance[2].cashPay || 0 }}
                        </td>
                        <td colspan="1">
                        </td>
                        <td colspan="1">
                        </td>
                        <td colspan="1">
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>
    </div>
</template>

<script>
    export default {
        name: 'National',
        props: {
            payMode: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            isNeimenggu: {
                type: Boolean,
                default: false,
            },

            nationalMedicalInsurance: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                lastRow: false,
                lastMedicalRow: false,
            }
        },
        computed: {
            payModeList() {
                return this.splitPayMode(this.payMode, 3)
            },
            neiMengGuMedicalInsurance() {
                return this.splitNationalMedical(this.nationalMedicalInsurance, 3)
            },
            lastRowCol() {
                return this.lastRow === 2 ? 3 : this.lastRow === 1 ? 5 : 1
            },
            lastMedicalRowCol() {
                return this.lastMedicalRow === 2 ? 3 : this.lastMedicalRow === 1 ? 5 : 1
            },
        },

        methods:{
            splitPayMode(payModeList, spiltLength) {
                let length = payModeList.length;
                let newPayModeList = [];
                for (let i = 0; i < length; i += spiltLength) {
                    newPayModeList.push(payModeList.slice(i,i + spiltLength))
                    if (i + spiltLength > length) {
                        this.lastRow = length % 3
                    }
                }
                return newPayModeList
            },
            splitNationalMedical(nationalMedicalInsurance, spiltLength) {
                let length = nationalMedicalInsurance.length;
                let newNationalMedicalInsurance = [];
                for (let i = 0; i < length; i += spiltLength) {
                    newNationalMedicalInsurance.push(nationalMedicalInsurance.slice(i,i + spiltLength))
                    if (i + spiltLength > length) {
                        this.lastMedicalRow = length % 3
                    }
                }
                return newNationalMedicalInsurance
            },
        },

    };
</script>
