<template>
    <span
        style="display: flex;"
        v-html="renderHTML"
    ></span>
</template>

<script>
    import { HospitalAstEnum, MedicalAdviceTypeEnum, TreatmentTypeEnum } from '../../common/constants.js';
    import { capitalizeFirstLetter, goodsSpecV2 } from "../../common/utils.js";

    export default {
        name: 'AdviceContent',
        props: {
            group: {
                type: Object,
                default: () => {
                },
            },
            item: {
                type: Object,
                default: () => {
                },
            },
            paddingRight: {
                type: Number,
                default: 0,
            },
        },
        data() {
            return {
                TreatmentTypeEnum,
            };
        },
        computed: {
            renderHTML() {
                const item = this.item;
                const group = this.group;

                let str = item.name;
                if (group.diagnosisTreatmentType === TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE) {
                    str += ` 出院原因：${item.dischargeHospitalReason} `;
                } else if ((group.diagnosisTreatmentType === TreatmentTypeEnum.INSPECTION ||
                    group.diagnosisTreatmentType === TreatmentTypeEnum.INSPECTION) &&
                    item.examApplySheetPurpose) {
                    str += ` 目的：${item.examApplySheetPurpose} `;
                } else {
                    if (group.type === MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE) {
                        str += '【出院带药】';
                    }
                    if (group.diagnosisTreatmentType === TreatmentTypeEnum.MEDICINE && item.westernPrimaryItemGoodsSnap) {
                        str += ` [${goodsSpecV2(item.westernPrimaryItemGoodsSnap)}] `;
                    }
                    str += ` ${capitalizeFirstLetter(group.freq)} `;
                    if (item.freqInfo && item.freqInfo.firstDayFrequency) {
                        str += ` 首日:${item.freqInfo.firstDayFrequency} `;
                    }

                    const astResult = item.astResult?.result;
                    const displayAstResult = astResult ? astResult === '阴性' ? '-' : '+' : ' ';
                    if (item.astFlag === HospitalAstEnum.PI_SHI) {
                        str += ` 皮试( ${displayAstResult} ) `;
                    }

                    if (item.astFlag === HospitalAstEnum.MIAN_SHI) {
                        str += ` 免试`;
                    }

                    if (item.astFlag === HospitalAstEnum.XU_YONG) {
                        str += ` 续用`;
                    }
                    const showSingleDosage = group.diagnosisTreatmentType !== TreatmentTypeEnum.MATERIALS;

                    str = `<span style="word-break: break-all;flex: 1; padding-right: ${this.paddingRight}px">${str}</span>`;
                    if (showSingleDosage) {
                        str += `<span class="border-left">${item.singleDosageCount}${item.singleDosageUnit || ''}</span>`;
                    } else {
                        str += `<span class="border-left"></span>`;
                    }
                }
                return str;
            },
        },
    };
</script>

