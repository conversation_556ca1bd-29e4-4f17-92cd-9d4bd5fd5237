<template>
    <div class="print-examination-header">
        <div class="top">
            <template v-if="headerConfig.barcode && barcode">
                <div class="barcode-wrapper">
                    <abc-print-barcode
                        :value="barcode"
                        show-code
                    ></abc-print-barcode>
                </div>
            </template>

            <template v-else>
                <div
                    v-if="customLogoShow"
                    class="logo"
                >
                    <img
                        src="https://static-common-cdn.abcyun.cn/img/print-baotou-report.png"
                        alt=""
                    />
                </div>

                <div
                    v-else-if="
                        headerConfig.logo &&
                            printData.organPrintView &&
                            printData.organPrintView.logo
                    "
                    class="logo"
                >
                    <img :src="printData.organPrintView.logo" />
                </div>
            </template>

            <div class="examination-title-wrapper">
                <print-row
                    class="clinic-name"
                    :class="{ 'clinic-sub-title-name': curOrganSubtitle }"
                >
                    {{ curOrganTitle }}
                </print-row>
                <print-row
                    v-if="curOrganSubtitle"
                    class="clinic-name"
                    :class="{ 'clinic-sub-title-name': curOrganSubtitle }"
                >
                    {{ curOrganSubtitle }}
                </print-row>
            </div>

            <print-row
                class="report-title"
                :class="{ 'report-title-sub': curOrganSubtitle }"
            >
                <span v-if="headerConfig.examineName">{{ printData.name }}</span>
                {{ reportTypeStr }}报告单
            </print-row>
        </div>

        <div class="row">
            <div
                v-for="(item, idx) in displayItemList"
                :key="idx"
                class="col overflow-hidden"
            >
                <span class="item-label"> {{ item.label }}: </span>

                <span
                    class="item-value"
                    v-html="item.value"
                >
                    <!-- {{ item.value }} -->
                </span>
            </div>

            <div class="clear-float"></div>
        </div>

        <div class="row">
            <span class="item-label"> {{ lastDisplayItem.label }}: </span>

            <span class="item-value">
                {{ lastDisplayItem.value }}
            </span>
        </div>
    </div>
</template>

<script>
    import PrintRow from '../layout/print-row.vue'
    import { formatAge, getLengthWithFullCharacter } from '../../common/utils.js'
    import { formatDentistry2Text } from '../../common/medical-transformat.js'
    import { BusinessTypeToFormLabel, TITLE_MAX_LENGTH } from '../../common/constants'
    import AbcPrintBarcode from '../layout/abc-print-barcode.vue'

    export default {
        name: 'ExaminationReportHeader',
        components: {
            AbcPrintBarcode,
            PrintRow,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            organTitle: {
                type: String,
                default: '',
            },
            isInspect: {
                type: Boolean,
                default: false,
            },
            headerConfig: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                curOrganTitle: '',
                curOrganSubtitle: '',
            }
        },
        computed: {
            patient() {
                return (this.printData && this.printData.patient) || {}
            },
            customLogoShow() {
                return !!this.headerConfig.isCustomLogo
            },
            reportTypeStr() {
                return this.isInspect ? '检查' : '检验'
            },
            diagnosis() {
                if (this.printData.extendDiagnosisInfos) {
                    return formatDentistry2Text(this.printData.extendDiagnosisInfos)
                }
                return this.printData.diagnosis
            },
            titleAndSubtitle() {
                return {
                    organTitle: this.organTitle,
                    headerConfigTitle: this.headerConfig.title,
                    headerConfigSubtitle: this.headerConfig.subtitle,
                }
            },
            barcode() {
                return this.printData.patientOrderNumber
            },

            displayItemList() {
                const { patient, isInspect, printData, headerConfig } = this

                const age = patient.age && formatAge(patient.age, { monthYear: 12, dayYear: 1 })

                return [
                    {
                        label: '姓名',
                        value: `${patient.name}${patient.sex ? `&nbsp;&nbsp;${patient.sex}` : ''}${age ? `&nbsp;&nbsp;${age}` : ''}`,
                    },
                    {
                        label: `${BusinessTypeToFormLabel[this.printData.businessType] || '门诊'}号`,
                        value: this.printData.businessType === 200 ? (printData.peSheetSimpleView?.no || '') : this.to8(printData.patientOrderNumber),
                    },
                    {
                        label: isInspect ? '科室' : '送检科室',
                        value: printData.departmentName || '--',
                    },
                    {
                        label: isInspect ? '申请医生' : '送检医生',
                        value: printData.doctorName || '--',
                    },
                    {
                        label: '样本类型',
                        value: printData.sampleType,
                        isHidden: isInspect,
                    },
                    {
                        label: '样本条码',
                        value: printData.orderNo,
                        isHidden: isInspect,
                    },
                    {
                        label: '设备型号',
                        value: printData.deviceName,
                        isHidden: !isInspect,
                    },
                    {
                        label: '档案号',
                        value: patient.sn,
                        isHidden: !headerConfig.patientSn,
                    },
                ].filter((item) => !item.isHidden)
            },

            lastDisplayItem() {
                return this.isInspect
                    ? {
                        label: '检查部位',
                        value: this.printData.name,
                    }
                    : {
                        label: '临床诊断',
                        value: this.diagnosis,
                    }
            },

            // 是否为送至中心门店检验的报告
            isSendToCenterOrgan() {
                return this.printData.coFlag === 1
            },

            cooperationCenterOrganName() {
                return this.printData.coClinicName
            },
        },
        watch: {
            titleAndSubtitle: {
                handler(v) {
                    const { organTitle, headerConfigTitle, headerConfigSubtitle } = v
                    let title = '',
                        subtitle = ''
                    // organTitle 是机构名称，当用户没有设置打印抬头时的兜底处理
                    title = this.isSendToCenterOrgan
                        ? this.cooperationCenterOrganName
                        : headerConfigTitle || organTitle || ''

                    const cacheTitle = title
                    const {
                        fullCharacterLength: fullClinicCharacterLength,
                        splitLength: splitClinicLength,
                    } = getLengthWithFullCharacter(cacheTitle, TITLE_MAX_LENGTH)
                    if (fullClinicCharacterLength > TITLE_MAX_LENGTH) {
                        title = cacheTitle.slice(0, splitClinicLength)
                        subtitle = cacheTitle.slice(splitClinicLength)
                    }

                    this.curOrganTitle = title
                    if (subtitle) {
                        this.curOrganSubtitle = subtitle
                    } else {
                        this.curOrganSubtitle = headerConfigSubtitle || ''
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            formatAge,

            to8(value = '') {
                value === null && (value = '')
                const srcStr = '00000000'
                return srcStr.slice(0, -`${value}`.length).concat(value)
            },
        },
    }
</script>

<style lang="scss">
    @import '../layout/print-layout';

    .print-examination-header {
        border-bottom: 1px solid #000000;

        .top {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            margin-bottom: 8pt;

            .logo {
                position: absolute;
                top: 0;
                left: -6pt;
                width: 90pt;
                height: 40pt;
                line-height: 40pt;

                > img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    vertical-align: middle;
                }
            }

            .barcode-wrapper {
                position: absolute;
                top: 0;
                left: -6pt;
                width: 90pt;
            }

            .print-row {
                box-sizing: border-box;
                padding-left: 50pt;
            }

            .examination-title-wrapper {
                width: 100%;
                height: 40pt;
                font-family: SimSun;
                font-weight: bold;
            }
        }

        .clinic-name {
            padding-left: 0 !important;
            font-size: 14pt;
            line-height: 40pt;
            text-align: center;

            &.clinic-sub-title-name {
                line-height: 20pt;
            }
        }

        .report-title {
            padding-left: 0 !important;
            font-family: SimSun;
            font-size: 14pt;
            line-height: 20pt;
            text-align: center;

            &.report-title-sub {
                font-size: 12pt;
            }
        }

        .row {
            line-height: 10pt;

            .col {
                float: left;
                width: 32%;
                box-sizing: border-box;

                &.overflow-hidden {
                    overflow: hidden;
                    white-space: nowrap;
                }

                &:nth-child(3n) {
                    width: 36%;
                }
            }

            .item-label {
                font-family: 'Microsoft YaHei', '微软雅黑';
                font-size: 10pt;
                line-height: 12pt;
            }

            .item-value {
                font-family: 'MicrosoftYaHeiLight';
                font-size: 10pt;
                line-height: 12pt;
            }

            .clear-float {
                display: block;
                clear: both;
            }
        }
    }
</style>
