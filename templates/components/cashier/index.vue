<template>
    <div class="print-ticket-content">
        <div data-type="header"></div>

        <div class="header-info">
            <div
                v-if="config.titleStyle === 1"
                class="logo-wrapper"
            >
                <img
                    v-if="logo"
                    :src="logo"
                    class="logo-image"
                    alt=""
                />
                <div
                    v-else-if="extra.isPreview"
                    class="no-logo"
                >
                    未上传Logo
                </div>
            </div>

            <div class="organ-title">
                {{ organTitle }}
            </div>

            <div
                v-if="clinicInfo.address && clinic.addressDetail"
                class="header-text-info"
            >
                {{ clinic.addressDetail }}
            </div>
            <div
                v-if="clinicInfo.mobile && clinic.contactPhone"
                class="header-text-info"
            >
                {{ clinic.contactPhone }}
            </div>

            <div
                v-if="showBarCode && barcodeSrc"
                class="barcode-wrapper"
            >
                <img
                    class="barcode-image"
                    :src="barcodeSrc"
                    alt=""
                />
            </div>

            <div class="header-type">
                {{ typeTitle }}
            </div>

            <div class="patient-info-wrapper content-item">
                <div
                    v-if="!onlyShowHasContentName || patient.name"
                    class="patient-info"
                >
                    <div class="left-title">
                        姓&nbsp;&nbsp;&nbsp;名：
                    </div>
                    <div class="right-content">
                        {{ patient.name || '匿名患者' }}
                        <template v-if="patientInfo.sex">
                            &nbsp;{{ patient.sex }}
                        </template>
                        <template v-if="patientInfo.age">
                            &nbsp;{{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}
                        </template>
                    </div>
                </div>
                <div class="patient-other-info">
                    <template v-if="patientInfo.mobile">
                        <div
                            v-if="!onlyShowHasContentMobile || patient.mobile"
                            class="other-info-new"
                        >
                            <div class="left-title">
                                手&nbsp;&nbsp;&nbsp;机：
                            </div>
                            <div class="right-content">
                                {{ patient.mobile | filterMobileV2(patientInfo.mobileType) }}
                            </div>
                        </div>
                    </template>

                    <div
                        v-if="showPatientOrderNo && patientInfo.patientOrderNo && printData.patientOrderNo"
                        class="other-info-new"
                    >
                        <div class="left-title">
                            诊&nbsp;&nbsp;&nbsp;号：
                        </div>
                        <div class="right-content">
                            {{ printData.patientOrderNo }}
                        </div>
                    </div>
                    <div
                        v-if="showDoctorInfo && patientInfo.doctor && printData.doctorName"
                        class="other-info-new"
                    >
                        <div class="left-title">
                            医&nbsp;&nbsp;&nbsp;生：
                        </div>
                        <div class="right-content">
                            {{ printData.doctorName }}
                        </div>
                    </div>
                    <div
                        v-if="isSupportGlasses && patientInfo.optometrist"
                        class="other-info-new"
                    >
                        <template v-if="glassesMedicineForms.length">
                            <div class="left-title">
                                验&nbsp;&nbsp;&nbsp;光：
                            </div>
                            <div class="right-content">
                                {{ glassesMedicineForms[0].optometristName }}
                            </div>
                        </template>
                        <template v-else>
                            验&nbsp;&nbsp;&nbsp;光：
                        </template>
                    </div>
                    <div
                        v-if="patientInfo.sellerName && printData.sellerName"
                        class="other-info-new"
                    >
                        <div class="left-title">
                            {{ openSheetPersonText }}
                        </div>
                        <div class="right-content">
                            {{ printData.sellerName }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="print-split-line"></div>

        <!-- 配镜处方 -->
        <template v-if="isSupportGlasses && feeInfo.glassesPrescription && glassesMedicineForms.length">
            <div
                v-for="glassesForm in glassesMedicineForms"
                :key="glassesForm.usage"
                data-type="mix-box"
                class="glasses-prescription"
            >
                <div
                    data-type="group"
                    class="overflow-hidden-item"
                >
                    <print-row data-type="item">
                        <print-col
                            :span="8"
                            class="glasses-text-info"
                            overflow
                        >
                            右眼
                        </print-col>
                        <print-col
                            :span="8"
                            class="glasses-text-info"
                            overflow
                        >
                            <template v-if="glassesForm.glassesType === 0">
                                {{ glassesForm.usage }}
                            </template>
                        </print-col>
                        <print-col
                            :span="8"
                            class="glasses-text-info"
                            overflow
                        >
                            左眼
                        </print-col>
                    </print-row>
                    <print-row
                        v-for="formItem in glassesForm.chargeFormItems"
                        :key="formItem.key"
                        data-type="item"
                    >
                        <template v-if="formItem.rightEyeValue || formItem.leftEyeValue">
                            <print-col
                                :span="8"
                                class="glasses-text-info"
                                overflow
                            >
                                {{ formItem.rightEyeValue }}
                            </print-col>
                            <print-col
                                :span="8"
                                class="glasses-text-info"
                                overflow
                            >
                                {{ formItem.name }}
                            </print-col>
                            <print-col
                                :span="8"
                                class="glasses-text-info"
                                overflow
                            >
                                {{ formItem.leftEyeValue }}
                            </print-col>
                        </template>
                    </print-row>

                    <template v-if="glassesForm.requirement">
                        <div
                            class="print-split-line"
                            style="margin-top: 8pt;"
                        ></div>

                        <print-row
                            data-type="item"
                            class="content-item"
                        >
                            <print-col
                                :span="24"
                                class="text-info is-wrap"
                                overflow
                            >
                                {{ glassesForm.requirement }}
                            </print-col>
                        </print-row>
                    </template>
                </div>

                <div
                    class="print-split-line"
                    :style="!glassesForm.requirement ? { 'margin-top': '8pt' } : null"
                ></div>
            </div>
        </template>

        <template v-if="feeInfo.chargeItem">
            <!-- 显示费用类型 -->
            <template v-if="isFeeCompose">
                <div
                    class="content-item"
                    data-type="mix-box"
                >
                    <div
                        class="overflow-hidden-item"
                        data-type="group"
                    >
                        <print-row
                            v-for="(medicalBill, mIndex) in calcMedicalBills"
                            :key="`${medicalBill.name + mIndex }`"
                            data-type="item"
                        >
                            <print-col
                                :span="18"
                                class="text-info"
                                overflow
                            >
                                {{ medicalBill.name }}
                            </print-col>
                            <print-col
                                :span="6"
                                class="text-right text-info"
                            >
                                {{
                                    medicalBill.totalFee | formatMoney
                                }}
                            </print-col>
                        </print-row>
                        <div
                            v-if="calcMedicalBills.length"
                            class="print-split-line"
                        ></div>
                    </div>
                </div>
            </template>

            <!-- 显示医嘱/费用项(医院管家) -->
            <template v-if="isFeeCompose">
                <template
                    v-if="(registrationForms.length) || decoctionForms.length || expressForms.length || examinationForms.length || treatmentForms.length || nursingForms.length || surgeryForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <product-form
                            v-for="(form, index) in registrationForms"
                            :key="`${form.id + index }`"
                            :form="form"
                        ></product-form>

                        <product-form
                            v-for="(form, index) in expressForms"
                            :key="`${form.id + index }`"
                            :form="form"
                        ></product-form>


                        <product-form
                            v-for="(form, index) in decoctionForms"
                            :key="`${form.id + index }`"
                            :form="form"
                        ></product-form>

                        <charge-form
                            :forms="examinationForms"
                            :config="2"
                            :shebao-config="shebaoConfig"
                            :auto-change-line="autoChangeLine"
                            total-title="检查检验费"
                            :total-fee="subTotals.examinationFee"
                            :is-fee-compose="isFeeCompose"
                            :is-optimization="isOptimization"
                        ></charge-form>

                        <charge-form
                            :forms="treatmentForms"
                            :config="2"
                            :shebao-config="shebaoConfig"
                            :auto-change-line="autoChangeLine"
                            total-title="治疗理疗费"
                            :total-fee="subTotals.treatmentFee"
                            :is-fee-compose="isFeeCompose"
                            :is-optimization="isOptimization"
                        ></charge-form>

                        <charge-form
                            :forms="nursingForms"
                            :config="2"
                            :shebao-config="shebaoConfig"
                            :auto-change-line="autoChangeLine"
                            total-title="护理费"
                            :total-fee="subTotals.nursingFee"
                            :is-fee-compose="isFeeCompose"
                            :is-optimization="isOptimization"
                        ></charge-form>

                        <charge-form
                            :forms="surgeryForms"
                            :config="2"
                            :shebao-config="shebaoConfig"
                            :auto-change-line="autoChangeLine"
                            total-title="手术费"
                            :total-fee="subTotals.surgeryFee"
                            :is-fee-compose="isFeeCompose"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>

                <template
                    v-if="composeForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <charge-form
                            :forms="composeForms"
                            :config="2"
                            :shebao-config="shebaoConfig"
                            :show-children="feeInfo.composeChildren"
                            :auto-change-line="autoChangeLine"
                            total-title="套餐费"
                            :total-fee="subTotals.composeProductFee"
                            :is-fee-compose="isFeeCompose"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>

                <template
                    v-if="westernMedicineForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <charge-form
                            :shebao-config="shebaoConfig"
                            :forms="westernMedicineForms"
                            :config="2"
                            :auto-change-line="autoChangeLine"
                            is-western
                            total-title="中西成药费"
                            :show-med-positon="westernMedicine.position"
                            :show-batch-number="westernMedicine.batchNumber"
                            :show-valid-date="westernMedicine.validityDate"
                            :show-manufacture="westernMedicine.manufacturer"
                            :show-spec="westernMedicine.spec"
                            :total-fee="subTotals.westernMedicineFee"
                            form-split-line
                            :is-fee-compose="isFeeCompose"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                </template>


                <template
                    v-if="chineseMedicineForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <chinese-form
                            :shebao-config="shebaoConfig"
                            :forms="chineseMedicineForms"
                            :config="2"
                            :position="chinese.position"
                            :special-requirement="showChineseSpecialRequirement"
                            :show-batch-number="chinese.batchNumber"
                            :auto-change-line="autoChangeLine"
                            :show-validity-date="chinese.validityDate"
                            :show-manufacturer="chinese.manufacturer"
                            :show-unit-count="showChineseUnitCount"
                            :show-total-count="showChineseTotalCount"
                            :show-total-dose-count="showChineseDoseCount"
                            total-title="中药费"
                            :total-fee="subTotals.chineseMedicineFee"
                            :is-fee-compose="isFeeCompose"
                            :is-optimization="isOptimization"
                        ></chinese-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>


                <template
                    v-if="materialGoodsForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <charge-form
                            :shebao-config="shebaoConfig"
                            :forms="materialGoodsForms"
                            :config="2"
                            :auto-change-line="autoChangeLine"
                            :show-med-positon="materialGoods.position"
                            total-title="材料商品费"
                            :total-fee="subTotals.materialFee"
                            :is-fee-compose="isFeeCompose"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>


                <template
                    v-if="otherForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <charge-form
                            :shebao-config="shebaoConfig"
                            :forms="otherForms"
                            :config="2"
                            :auto-change-line="autoChangeLine"
                            total-title="其他费用"
                            :total-fee="subTotals.otherFee"
                            :is-fee-compose="isFeeCompose"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>
            </template>

            <!-- 显示项目(诊所管家) -->
            <template v-else>
                <template
                    v-if="(feeInfo.registration && registrationForms.length) ||
                        decoctionForms.length ||
                        expressForms.length ||
                        examinationForms.length ||
                        treatmentForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <transition name="print-fade">
                            <template v-if="feeInfo.registration">
                                <product-form
                                    v-for="(form, index) in registrationForms"
                                    :key="`${form.id + index }`"
                                    :form="form"
                                ></product-form>
                            </template>
                        </transition>


                        <product-form
                            v-for="(form, index) in expressForms"
                            :key="`${form.id + index }`"
                            :form="form"
                        ></product-form>


                        <product-form
                            v-for="(form, index) in decoctionForms"
                            :key="`${form.id + index }`"
                            :form="form"
                        ></product-form>

                        <transition name="print-fade">
                            <charge-form
                                :forms="examinationForms"
                                :config="feeInfo.examination"
                                :shebao-config="shebaoConfig"
                                :auto-change-line="autoChangeLine"
                                total-title="检查检验费"
                                :total-fee="getTotalPriceByForms(examinationForms)"
                                :should-optimization="shouldOptimization"
                                :is-optimization="isOptimization"
                            ></charge-form>
                        </transition>


                        <charge-form
                            :forms="treatmentForms"
                            :config="feeInfo.treatment"
                            :shebao-config="shebaoConfig"
                            :auto-change-line="autoChangeLine"
                            total-title="治疗理疗费"
                            :total-fee="getTotalPriceByForms(treatmentForms)"
                            :should-optimization="shouldOptimization"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>

                <template
                    v-if="composeForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <charge-form
                            :forms="composeForms"
                            :config="feeInfo.compose"
                            :shebao-config="shebaoConfig"
                            :show-children="feeInfo.composeChildren"
                            :auto-change-line="autoChangeLine"
                            total-title="套餐费"
                            :total-fee="getTotalPriceByForms(composeForms)"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>

                <template
                    v-if="westernMedicineForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <charge-form
                            :shebao-config="shebaoConfig"
                            :forms="westernMedicineForms"
                            :config="feeInfo.westernMedicine"
                            :auto-change-line="autoChangeLine"
                            is-western
                            total-title="中西成药费"
                            :show-med-positon="westernMedicine.position"
                            :show-batch-number="westernMedicine.batchNumber"
                            :show-valid-date="westernMedicine.validityDate"
                            :show-manufacture="westernMedicine.manufacturer"
                            :show-spec="westernMedicine.spec"
                            :total-fee="getTotalPriceByForms(westernMedicineForms)"
                            form-split-line
                            :should-optimization="shouldOptimization"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                </template>


                <template
                    v-if="chineseMedicineForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <chinese-form
                            :shebao-config="shebaoConfig"
                            :forms="chineseMedicineForms"
                            :config="feeInfo.chineseMedicine"
                            :position="chinese.position"
                            :special-requirement="showChineseSpecialRequirement"
                            :show-batch-number="chinese.batchNumber"
                            :auto-change-line="autoChangeLine"
                            :show-validity-date="chinese.validityDate"
                            :show-manufacturer="chinese.manufacturer"
                            :show-unit-count="showChineseUnitCount"
                            :show-total-count="showChineseTotalCount"
                            :show-total-dose-count="showChineseDoseCount"
                            total-title="中药费"
                            :total-fee="getTotalPriceByForms(chineseMedicineForms)"
                            :is-optimization="isOptimization"
                        ></chinese-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>


                <template
                    v-if="materialGoodsForms.length"
                >
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <charge-form
                            :shebao-config="shebaoConfig"
                            :forms="materialGoodsForms"
                            :config="feeInfo.materialGoods"
                            :auto-change-line="autoChangeLine"
                            :show-med-positon="materialGoods.position"
                            total-title="材料商品费"
                            :total-fee="getTotalPriceByForms(materialGoodsForms)"
                            :should-optimization="shouldOptimization"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>


                <template v-if="otherForms.length">
                    <div
                        class="content-item"
                        data-type="mix-box"
                    >
                        <charge-form
                            :shebao-config="shebaoConfig"
                            :forms="otherForms"
                            :config="feeInfo.other"
                            :auto-change-line="autoChangeLine"
                            total-title="其他费用"
                            :total-fee="getTotalPriceByForms(otherForms)"
                            :should-optimization="shouldOptimization"
                            :is-optimization="isOptimization"
                        ></charge-form>
                    </div>
                    <div class="print-split-line"></div>
                </template>
            </template>

            <!-- 眼镜项目 -->
            <template
                v-if="isSupportGlasses && glassesForms.length"
            >
                <div
                    class="content-item"
                    data-type="mix-box"
                >
                    <charge-form
                        :shebao-config="shebaoConfig"
                        :forms="glassesForms"
                        :config="feeInfo.eyeglasses"
                        :auto-change-line="autoChangeLine"
                        total-title="眼镜费"
                        :show-spec="glassesGoods.spec"
                        :total-fee="subTotals.eyeFee"
                        :is-optimization="isOptimization"
                    ></charge-form>
                </div>
                <div class="print-split-line"></div>
            </template>
        </template>

        <div
            v-if="isRefund"
            class="content-item"
        >
            <print-row>
                <transition name="print-fade">
                    <print-col
                        v-if="cashierInfo.netIncomeFee"
                        :span="24"
                    >
                        实退：<span
                            v-for="(pay, index) in chargeTransactions"
                            :key="index"
                        >
                            ({{ pay.payModeDisplayName }}){{ pay.amount | formatMoney }}
                        </span>
                    </print-col>
                </transition>
            </print-row>
            <div class="print-split-line"></div>
        </div>
        <template v-else>
            <template
                v-if="showTotalDiscountFee"
            >
                <div class="content-item">
                    <print-row>
                        <transition name="print-fade">
                            <print-col
                                v-if="cashierInfo.totalFee"
                                :span="getTotalFeeSpan"
                            >
                                合计：{{ printData.totalFee | formatMoney }}
                            </print-col>
                        </transition>

                        <transition name="print-fade">
                            <template v-if="cashierInfo.discountFee">
                                <print-col
                                    v-if="printData.singlePromotionFee && !printData.packagePromotionFee"
                                    :span="shouldOptimization ? 24 : cashierInfo.totalFee ? 12 : 24"
                                >
                                    单项优惠：{{ printData.singlePromotionFee | formatMoney }}
                                </print-col>
                                <print-col
                                    v-else-if="printData.packagePromotionFee && !printData.singlePromotionFee"
                                    :span="shouldOptimization ? 24 : cashierInfo.totalFee ? 12 : 24"
                                >
                                    整单优惠：{{ printData.packagePromotionFee | formatMoney }}
                                </print-col>
                                <print-col
                                    v-else-if="printData.discountFee"
                                    :span="shouldOptimization ? 24 : cashierInfo.totalFee ? 12 : 24"
                                >
                                    优惠：{{ printData.discountFee | formatMoney }}
                                </print-col>
                            </template>
                        </transition>
                    </print-row>

                    <print-row v-if="cashierInfo.discountFee && printData.singlePromotionFee && printData.packagePromotionFee">
                        <print-col
                            v-if="printData.singlePromotionFee"
                            :span="shouldOptimization ? 24 : 12"
                        >
                            单项优惠：{{ printData.singlePromotionFee | formatMoney }}
                        </print-col>
                        <print-col
                            v-if="printData.packagePromotionFee"
                            :span="shouldOptimization ? 24 : 12"
                        >
                            整单优惠：{{ printData.packagePromotionFee | formatMoney }}
                        </print-col>
                    </print-row>
                </div>
                <div class="print-split-line"></div>
            </template>
        </template>


        <template v-if="((cashierInfo.receivableFee || cashierInfo.netIncomeFee) && !isRefund) || hasMemberCardPay">
            <div class="content-item">
                <print-row v-if="!isRefund">
                    <transition name="print-fade">
                        <print-col
                            v-if="cashierInfo.receivableFee"
                            class="receivable-fee"
                            style="text-align: left;"
                            :span="24"
                        >
                            应付：{{ printData.receivableFee | formatMoney }}
                        </print-col>
                    </transition>
                </print-row>

                <print-row v-if="!isRefund">
                    <transition name="print-fade">
                        <print-col
                            v-if="cashierInfo.netIncomeFee"
                            :span="24"
                        >
                            实付：<span
                                v-for="(pay, index) in actuallyReceiveTransactions"
                                :key="index"
                            >
                                ({{ pay.payModeDisplayName }}){{ pay.amount | formatMoney }}
                            </span>
                            <span v-if="!actuallyReceiveTransactions.length">0.00</span>
                        </print-col>
                    </transition>
                </print-row>
                <print-row v-if="!isRefund && latestOweTransaction">
                    <transition name="print-fade">
                        <print-col :span="24">
                            本次还款：<span>
                                {{ latestOweTransaction.amount | formatMoney }}
                            </span>
                        </print-col>
                    </transition>
                </print-row>
                <print-row v-if="!isRefund && isOweStatus">
                    <transition name="print-fade">
                        <print-col :span="24">
                            当前欠费：<span>
                                {{ printData.oweFee | formatMoney }}
                            </span>
                        </print-col>
                    </transition>
                </print-row>

                <template v-if="hasMemberCardPay">
                    <print-row>
                        <print-col :span="24">
                            会员卡
                            <template
                                v-if="printData.memberCardMobile"
                            >
                                ({{ getMobile(printData.memberCardMobile) }})
                            </template>
                            余额：{{ printData.memberCardBalance | formatMoney }}
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col :span="24">
                            会员卡原有余额：{{ printData.memberCardBeginningBalance | formatMoney }}
                        </print-col>
                    </print-row>
                </template>

                <template v-if="promotionBalances && promotionBalances.length">
                    <print-row
                        v-for="(item, index) in promotionBalances"
                        :key="`${index }promotionBalances`"
                    >
                        <print-col :span="24">
                            卡项
                            <template
                                v-if="item.name"
                            >
                                ({{ item.name }})
                            </template>
                            余额：{{ item.remainingBalance | formatMoney }}
                        </print-col>
                        <print-col :span="24">
                            卡项原有余额：{{ item.beginBalance | formatMoney }}
                        </print-col>
                    </print-row>
                </template>
            </div>

            <div class="print-split-line"></div>
        </template>

        <template v-if="memberInfo && (memberInfoConfig.level || memberInfoConfig.balance || memberInfoConfig.points)">
            <div class="content-item">
                <print-row>
                    <transition name="print-fade">
                        <print-col
                            v-if="memberInfoConfig.level"
                            style="text-align: left;"
                            :span="24"
                        >
                            会员等级：{{ memberInfo.memberType.name }}
                        </print-col>
                    </transition>
                </print-row>
                <print-row>
                    <transition name="print-fade">
                        <print-col
                            v-if="memberInfoConfig.balance"
                            :span="isOptimization ? 24 : 12"
                        >
                            会员余额：{{ memberInfo.cardBalance | formatMoney }}
                        </print-col>
                    </transition>
                    <transition name="print-fade">
                        <print-col
                            v-if="memberInfoConfig.points"
                            :span="isOptimization ? 24 : 12"
                        >
                            剩余积分：{{ memberInfo.points || 0 }}
                        </print-col>
                    </transition>
                </print-row>
                <div class="print-split-line"></div>
            </div>
        </template>

        <div
            v-if="hasHealthCardPay && (shebaoConfig.cardInfo || shebaoConfig.settlementInfo || shebaoConfig.balanceInfo)"
            class="content-item"
        >
            <hang-zhou
                v-if="isHangzhou"
                :shebao-config="shebaoConfig"
                :shebao-payment="shebaoPayment"
            ></hang-zhou>
            <yunnan
                v-else-if="isYunnan"
                :shebao-config="shebaoConfig"
                :shebao-payment="shebaoPayment"
            ></yunnan>
            <template v-else>
                <transition name="print-fade">
                    <!-- 医保卡信息-->
                    <template v-if="shebaoConfig.cardInfo">
                        <print-row>
                            <print-col
                                :span="24"
                                class="text-info"
                            >
                                医保号：{{ shebaoPayment.cardId }}
                            </print-col>

                            <print-col
                                :span="24"
                                class="text-info"
                            >
                                人员编号：{{ extraInfo.personalCode }}
                            </print-col>

                            <print-col
                                :span="14"
                                class="text-info"
                            >
                                持卡人：{{ shebaoPayment.cardOwner }}
                            </print-col>
                            <print-col
                                :span="10"
                                class="text-info"
                            >
                                关系：{{ shebaoPayment.relationToPatient }}
                            </print-col>
                        </print-row>
                    </template>
                </transition>

                <transition name="print-fade">
                    <!--结算信息-->
                    <template v-if="shebaoConfig.settlementInfo">
                        <print-row>
                            <print-col
                                :span="24"
                                class="text-info"
                            >
                                医疗类别：{{ shebaoPayment.medType }}
                            </print-col>
                            <print-col
                                v-if="isJinan"
                                :span="24"
                                class="text-info"
                            >
                                医保支付方式：{{ extraInfo.sfrzfs }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(shebaoPayment.fundPaymentFee) || isShowZeroSettlementInfo"
                                :span="24"
                                class="text-info"
                            >
                                基金支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(extraInfo.hifpPay) || isShowZeroSettlementInfo"
                                :span="24"
                                style="margin-left: 10px;"
                                class="text-info"
                            >
                                统筹支出：{{ extraInfo.hifpPay | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(extraInfo.cvlservPay) || isShowZeroSettlementInfo"
                                :span="24"
                                style="margin-left: 10px;"
                                class="text-info"
                            >
                                公务员补助：{{ extraInfo.cvlservPay | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(extraInfo.hifmiPay) || isShowZeroSettlementInfo"
                                :span="24"
                                style="margin-left: 10px;"
                                class="text-info"
                            >
                                大病保险：{{ extraInfo.hifmiPay | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(extraInfo.hifobPay) || isShowZeroSettlementInfo"
                                :span="24"
                                style="margin-left: 10px;"
                                class="text-info"
                            >
                                大额补助：{{ extraInfo.hifobPay | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(extraInfo.mafPay) || isShowZeroSettlementInfo"
                                :span="24"
                                style="margin-left: 10px;"
                                class="text-info"
                            >
                                医疗救助：{{ extraInfo.mafPay | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(extraInfo.hifesPay) || isShowZeroSettlementInfo"
                                :span="24"
                                style="margin-left: 10px;"
                                class="text-info"
                            >
                                企业补充医疗保险：{{ extraInfo.hifesPay | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(extraInfo.othPay) || isShowZeroSettlementInfo"
                                :span="24"
                                style="margin-left: 10px;"
                                class="text-info"
                            >
                                其他支出：{{ extraInfo.othPay | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(shebaoPayment.accountPaymentFee) || isShowZeroSettlementInfo"
                                :span="24"
                                class="text-info"
                            >
                                个账支付：{{ shebaoPayment.accountPaymentFee | formatMoney }}
                                <template v-if="!isEmpty(extraInfo.acctMulaidPay) || isShowZeroSettlementInfo">
                                    (共济支付: {{ extraInfo.acctMulaidPay | formatMoney }})
                                </template>
                            </print-col>
                            <print-col
                                v-if="isShowDisplayWltpayAmt"
                                :span="24"
                                class="text-info"
                            >
                                医保钱包：{{ extraInfo.wltpayAmt | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="!isEmpty(shebaoPayment.personalPaymentFee) || isShowZeroSettlementInfo"
                                :span="24"
                                class="text-info"
                            >
                                现金支付：{{ shebaoPayment.personalPaymentFee | formatMoney }}
                            </print-col>
                            <template>
                                <print-col
                                    v-if="!isEmpty(extraInfo.beforeDedcCumulative)"
                                    :span="24"
                                    class="text-info"
                                >
                                    起付线：本次{{ extraInfo.actPayDedc | formatMoney }}, 累计{{ extraInfo.beforeDedcCumulative | formatMoney }}
                                </print-col>
                                <print-col
                                    v-else
                                    :span="24"
                                    class="text-info"
                                >
                                    起付线：本次{{ extraInfo.actPayDedc | formatMoney }}
                                </print-col>
                            </template>
                        </print-row>
                    </template>
                </transition>

                <transition name="print-fade">
                    <!--余额信息-->
                    <template v-if="shebaoConfig.balanceInfo">
                        <print-row v-if="isYunnan">
                            <print-col
                                :span="24"
                                class="text-info"
                            >
                                支付前余额：{{ shebaoPayment.beforeCardBalance | formatMoney }}
                            </print-col>
                            <print-col
                                :span="24"
                                class="text-info"
                            >
                                支付后余额：{{ shebaoPayment.cardBalance | formatMoney }}
                            </print-col>
                        </print-row>
                        <print-row v-else>
                            <print-col
                                :span="24"
                                class="text-info"
                            >
                                医保余额：{{ shebaoPayment.cardBalance | formatMoney }}
                            </print-col>
                            <print-col
                                :span="24"
                                class="text-info"
                            >
                                原有余额：{{ shebaoPayment.beforeCardBalance | formatMoney }}
                            </print-col>
                            <template v-if="$abcSocialSecurity && $abcSocialSecurity.config.isNeedQueryFundQuota">
                                <print-col
                                    :span="24"
                                    class="text-info"
                                >
                                    统筹余额：{{ extraInfo.generalFundpayBalc | formatMoney }}
                                </print-col>
                                <print-col
                                    v-if="extraInfo.chronicDiseaseDBalc"
                                    :span="24"
                                    class="text-info"
                                >
                                    大病余额：{{ extraInfo.chronicDiseaseDBalc | formatMoney }}
                                </print-col>
                                <print-col
                                    v-if="extraInfo.chronicDiseaseMBalc"
                                    :span="24"
                                    class="text-info"
                                >
                                    慢病余额：{{ extraInfo.chronicDiseaseMBalc | formatMoney }}
                                </print-col>
                            </template>
                        </print-row>
                    </template>
                </transition>

                <transition name="print-fade">
                    <print-row v-if="shebaoConfig.settlementInfo && extraInfo.gongjiAccountPaymentFee">
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            授权人：{{ extraInfo.gongjiAuthorName }}
                        </print-col>
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            授权人关系：{{ extraInfo.gongjiRelation }}
                        </print-col>
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            授权人账户余额：{{ extraInfo.gongjiBalc | formatMoney }}
                        </print-col>
                    </print-row>
                </transition>

                <transition name="print-fade">
                    <print-row v-if="shebaoConfig.accumulatedInfo">
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            累计统筹支付金额：{{ extraInfo.ljtczf | formatMoney }}
                        </print-col>
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            累计大额支付金额：{{ extraInfo.ljdezf | formatMoney }}
                        </print-col>
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            累计个人负担金额：{{ extraInfo.ljgrzf | formatMoney }}
                        </print-col>
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            累计起付线：{{ extraInfo.ljqfx | formatMoney }}
                        </print-col>
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            门诊统筹额度累计：{{ extraInfo.bnptmzljbxje | formatMoney }}
                        </print-col>
                    </print-row>
                </transition>
            </template>
            <div class="print-split-line"></div>
        </div>

        <div
            v-if="hasPromotion"
            class="content-item"
        >
            <print-row>
                <print-col
                    :span="24"
                    class="text-info"
                >
                    返券：{{ formatGiftCoupons() }}
                </print-col>
            </print-row>
        </div>

        <div class="content-item">
            <transition name="print-fade">
                <print-row v-if="clinicInfo.chargeOperator">
                    <print-col
                        v-if="!isRefund"
                        class="text-info"
                        :span="24"
                    >
                        收费员：{{ printData.chargedByName }}
                    </print-col>
                    <print-col
                        v-else
                        class="text-info"
                        :span="24"
                    >
                        退费员：{{ printData.refundByName }}
                    </print-col>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row v-if="clinicInfo.chargeDate">
                    <print-col
                        class="text-info"
                        :span="24"
                    >
                        {{ isRefund ? '退费' : '收费' }}时间：{{ printData.chargedTime | parseTime('y-m-d h:i:s') }}
                    </print-col>
                </print-row>
            </transition>
            <transition name="print-fade">
                <print-row v-if="printData.latestOwePaidTime">
                    <print-col
                        :span="24"
                        class="text-info"
                    >
                        还款时间：{{ printData.latestOwePaidTime | parseTime('y-m-d h:i:s') }}
                    </print-col>
                </print-row>
            </transition>
            <transition name="print-fade">
                <print-row v-if="clinicInfo.printDate">
                    <print-col
                        :span="24"
                        class="text-info"
                    >
                        打印时间：{{ new Date() | parseTime('y-m-d h:i:s') }}
                    </print-col>
                </print-row>
            </transition>

            <template v-if="isTakeMedicationTime">
                <print-row
                    v-for="(item, index) in takeMedicationTimeList"
                    :key="index"
                >
                    <print-col
                        :span="24"
                        class="text-info"
                    >
                        <template v-if="item">
                            {{ takeMedicationTimeList.length === 1 ? '取药时间' : `处方${toHan(index + 1)}取药时间` }}：{{ item | parseTime('y-m-d h:i:s') }}
                        </template>
                        <template v-else>
                            {{ takeMedicationTimeList.length === 1 ? '取药时间' : `处方${toHan(index + 1)}取药时间` }}：无
                        </template>
                    </print-col>
                </print-row>
            </template>
            <transition name="print-fade">
                <print-row v-if="clinicInfo.remark">
                    <print-col
                        :span="24"
                        class="text-info"
                    >
                        收费备注：{{ printData.latestChargeComment || '无' }}
                    </print-col>
                </print-row>
            </transition>
            <div
                v-if="config.remark &&
                    (clinicInfo.address || clinicInfo.chargeOperator || clinicInfo.chargeDate || clinicInfo.printDate || clinicInfo.mobile)"
                class="print-split-line"
            ></div>
        </div>

        <div class="content-item">
            <transition name="print-fade">
                <print-row
                    v-if="config.remark"
                    class="remark-row"
                >
                    <print-col
                        class="text-info"
                        :span="24"
                        style="white-space: pre-wrap;"
                        v-html="config.remark"
                    ></print-col>
                </print-row>
            </transition>


            <transition name="print-fade">
                <div
                    v-if="clinicInfo.replacementReminder"
                    class="content-item"
                >
                    <div
                        v-if="config.remark"
                        class="print-split-line"
                    ></div>
                    <print-row>
                        <print-col

                            :span="24"
                            class="print-cashier-title text-info"
                        >
                            药品离柜，概不退换
                        </print-col>
                    </print-row>
                </div>
            </transition>
        </div>

        <div
            v-if="footerIsShowWeClinicQrCode && qrCode && clinicInfo.qrCode"
            class="content-item"
        >
            <print-row>
                <print-col
                    :span="24"
                    class="print-cashier-title text-info"
                >
                    <transition name="print-fade">
                        <div
                            :gutter="6"
                            class="qr-code-wrapper"
                        >
                            <img
                                class="qr-code"
                                borderthin="true"
                                :src="qrCode"
                                alt=""
                            />
                            <div class="qr-code-tips">
                                <div>微信就诊</div>
                                <div>挂号预约</div>
                                <div>就诊报告</div>
                                <div>用药医嘱</div>
                            </div>
                        </div>
                    </transition>
                </print-col>
            </print-row>
        </div>

        <div
            v-if="invoiceQrcode && config.invoiceCode"
            class="content-item"
            style="margin-bottom: 2pt;"
        >
            <print-row>
                <print-col
                    :span="24"
                    class="print-cashier-title text-info"
                >
                    <transition name="print-fade">
                        <div
                            :gutter="6"
                            class="qr-code-wrapper"
                        >
                            <img
                                class="qr-code"
                                borderthin="true"
                                :src="invoiceQrcode"
                                alt=""
                                style="vertical-align: middle;"
                            />
                            <div
                                class="qr-code-tips"
                                style="height: 100%; font-size: 10pt; text-align: left; vertical-align: middle;"
                            >
                                微信扫一扫查看发票
                            </div>
                        </div>
                    </transition>
                </print-col>
            </print-row>
        </div>
        <div
            v-if="hasHealthCardPay && clinicInfo.socialQrCode"
            class="content-item"
            style="margin-bottom: 2pt;"
        >
            <print-row>
                <print-col
                    :span="24"
                    class="print-cashier-title text-info"
                >
                    <transition name="print-fade">
                        <div
                            :gutter="6"
                            class="qr-code-wrapper"
                        >
                            <img
                                class="qr-code"
                                borderthin="true"
                                src="data:image/png;base64,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"
                                alt=""
                                style="vertical-align: middle;"
                            />
                            <div
                                class="qr-code-tips"
                                style="height: 100%; padding-left: 4pt; font-size: 10pt; text-align: left; vertical-align: middle;"
                            >
                                <div>
                                    杭州医保
                                </div>
                                <div>
                                    政策票据
                                </div>
                                <div>
                                    智能解读
                                </div>
                            </div>
                        </div>
                    </transition>
                </print-col>
            </print-row>
        </div>

        <div
            v-if="traceCodeQrCodeUrl && clinicInfo.traceCodeQrCode"
            class="content-item"
        >
            <print-row>
                <print-col
                    :span="24"
                    class="print-cashier-title text-info"
                >
                    <transition name="print-fade">
                        <div
                            :gutter="6"
                            class="qr-code-wrapper"
                        >
                            <img
                                class="qr-code"
                                borderthin="true"
                                :src="traceCodeQrCodeUrl"
                                alt=""
                                style="vertical-align: middle;"
                            />
                            <div
                                class="qr-code-tips"
                                style="vertical-align: middle;"
                            >
                                <div>扫码查看</div>
                                <div>药品追溯</div>
                                <div>码</div>
                            </div>
                        </div>
                    </transition>
                </print-col>
            </print-row>
        </div>

        <div
            data-type="footer"
            style="width: 100%; height: 8pt;"
        ></div>
    </div>
</template>

<script>
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';
    import ChargeForm from '../cashier-form/charge-form.vue';
    import ChineseForm from '../cashier-form/chinese-form.vue';
    import ProductForm from "../cashier-form/product-form.vue";
    import HangZhou from "../cashier-form/shebao/hangzhou.vue";
    import Yunnan from "../cashier-form/shebao/yunnan.vue";

    import {
        FEE_TYPE_ID_ABC_INNER_MAX,
        FEE_TYPE_SORT,
        GLASSES_TYPE,
        GoodsFeeType,
        PrintFormTypeEnum,
        SheBaoRegionPayment,
    } from "../../common/constants.js";
    import {
        formatAge,
        formatMoney,
        parseTime,
        textToBase64BarCode,
        toHan,
        getTotalPriceByForms, deepClone,
    } from "../../common/utils.js";
    import { filterMobileV2 } from "../../common/medical-transformat.js";
    import { filterGlassesUnit } from "../../common/filters";

    export default {
        name: "Index",
        components: {
            PrintRow,
            PrintCol,
            ChargeForm,
            ChineseForm,
            HangZhou,
            Yunnan,
            ProductForm,
        },
        filters: {
            formatMoney,
            parseTime,
            filterMobileV2,
            filterGlassesUnit,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            isRefund: {
                type: Boolean,
                default: false,
            },
            extra: {
                type: Object,
                default() {
                    return {}
                },
            },
            isOptimization: {
                type: Boolean,
                default: false,
            },
            viewDistributePrintConfig: {
                type: Object,
                default() {
                    return {}
                },
            },
        },
        computed: {
            feeComposeDetail() {
                return this.config.feeComposeDetail || {};
            },
            showTotalDiscountFee() {
                const {
                    totalFee,
                    discountFee: discountFeeSwitch,
                } = this.cashierInfo;
                const {
                    discountFee,
                    singlePromotionFee,
                    packagePromotionFee,
                } = this.printData;
                if(totalFee) return true;
                if(discountFeeSwitch) {
                    return discountFee || singlePromotionFee || packagePromotionFee
                }
                return false;
            },
            getTotalFeeSpan() {
                // 如果满足58mm和药店管家,则优化排版
                if (this.shouldOptimization) return 24;
                if(!this.cashierInfo.discountFee) return 24;
                const {
                    discountFee,
                    singlePromotionFee,
                    packagePromotionFee,
                } = this.printData;
                if(discountFee) return 12;
                if(singlePromotionFee && packagePromotionFee) return 24;
                if(singlePromotionFee || packagePromotionFee) return 12;
                return 24;
            },
            isSupportGlasses() {
                return this.printData.IS_GLASSES;
            },
            typeTitle() {
                return this.isRefund ? '退费单' : '收费单';
            },
            organ() {
                return this.printData.organ ?? {};
            },
            clinic() {
                return this.printData.organ ?? {};
            },
            qrCode() {
                return this.printData.qrCode;
            },
            traceCodeQrCodeUrl() {
                return this.printData.traceCodeQrCodeUrl || '';
            },
            invoiceQrcode() {
                return this.printData.invoiceQrcode;
            },
            extraInfo() {
                return this.shebaoPayment.extraInfo || {};
            },
            autoChangeLine() {
                return this.feeInfo.autoChangeLine
            },
            patientInfo() {
                return this.config.patientInfo || {};
            },
            feeInfo() {
                return this.config.feeInfo || {};
            },
            chinese() {
                return this.config.chinese || {};
            },
            westernMedicine() {
                return this.config.westernMedicine || {};
            },
            glassesGoods() {
                return this.config.glassesGoods|| {};
            },
            materialGoods() {
                return this.config.materialGoods|| {};
            },
            cashierInfo() {
                return this.config.cashierInfo || {};
            },
            memberInfoConfig() {
                return this.config.memberInfo || {};
            },
            clinicInfo() {
                return this.config.clinicInfo || {};
            },
            shebaoConfig() {
                return this.config.healthCardInfo || {};
            },
            // 是否打印金额为0的医保结算信息
            isShowZeroSettlementInfo(){
                return this.shebaoConfig.zeroSettlementInfo;
            },
            organTitle() {
                if (!this.config.title) {
                    return this.organ && this.organ.name || '';
                }
                return this.config.title || '';
            },
            region() {
                return (this.shebaoPayment && this.shebaoPayment.region) || '';
            },
            isHangzhou() {
                return this.region.indexOf('zhejiang_') === 0;
            },
            isYunnan() {
                return this.region === 'yunnan_kunming' || this.region === 'yunnan_baoshan';
            },
            isJinan() {
                return this.region === 'shandong_jinan';
            },
            // 是否打印钱包支付
            isShowDisplayWltpayAmt() {
                return this.$abcSocialSecurity?.config?.isShowWltAcctFlagSwitch && (!this.isEmpty(this.extraInfo.wltpayAmt) || !!this.isShowZeroSettlementInfo)
            },
            isTakeMedicationTime() {
                return this.printData._dispensingConfig?.isTakeMedicationTime;
            },
            takeMedicationTimeList() {
                return this.chineseMedicineForms.map(item => {
                    return item.takeMedicationTime;
                })
            },
            patient() {
                return this.printData.patient || {};
            },
            chargeForms() {
                return this.printData.chargeForms || [];
            },
            // 挂号费
            registrationForms() {
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const registrationFeeComposeDetail = parseInt(this.feeComposeDetail.registration);
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if ((registrationFeeComposeDetail === 0 && feeDetail === 0) || registrationFeeComposeDetail === 1) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.REGISTRATION);
                    }
                    if ((registrationFeeComposeDetail === 0 && feeDetail === 1) || registrationFeeComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.REGISTRATION) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.REGISTRATION).map((form) => {
                    // 处理挂号费 label
                    if (!this.feeInfo.registration) return;

                    const cacheForm = deepClone(form);
                    (cacheForm.chargeFormItems || []).forEach((item) => {
                        if (this.feeInfo.registration === 1) {
                            if (item.name === '诊费') {
                                item.name = this.$t('registrationFeeName');
                            }
                        } else if (this.feeInfo.registration === 2) {
                            if (item.name === this.$t('registrationFeeName')) {
                                item.name = '诊费';
                            }
                        }
                    })

                    return cacheForm;
                });
            },
            // 快递费
            expressForms() {
                return (
                    this.chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.EXPRESS_DELIVERY;
                    }) || []
                );
            },
            // 加工费
            decoctionForms() {
                return (
                    this.chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.PROCESS;
                    }) || []
                );
            },

            // 检验检查
            examinationForms() {
                const examinationFeeComposeDetail = parseInt(this.feeComposeDetail.examination);
                const feeDetail = parseInt(this.feeInfo.feeDetail);
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    if ((examinationFeeComposeDetail === 0 && feeDetail === 0) || examinationFeeComposeDetail === 1) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.EXAMINATION);
                    }
                    if ((examinationFeeComposeDetail === 0 && feeDetail === 1) || examinationFeeComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.EXAMINATION) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.EXAMINATION);
            },
            // 治疗理疗
            treatmentForms() {
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const treatmentFeeComposeDetail = parseInt(this.feeComposeDetail.treatment);
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if ((treatmentFeeComposeDetail === 0 && feeDetail === 0) || treatmentFeeComposeDetail === 1) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.TREATMENT);
                    }
                    if ((treatmentFeeComposeDetail === 0 && feeDetail === 1) || treatmentFeeComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.TREATMENT) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.TREATMENT);
            },
            // 护理项
            nursingForms() {
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const nursingFeeComposeDetail = parseInt(this.feeComposeDetail.nursing);
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if ((nursingFeeComposeDetail === 0 && feeDetail === 0) || nursingFeeComposeDetail === 1) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.NURSING);
                    }
                    if ((nursingFeeComposeDetail === 0 && feeDetail === 1) || nursingFeeComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.NURSING) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.NURSING);
            },
            // 手术项
            surgeryForms() {
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const surgeryComposeDetail = parseInt(this.feeComposeDetail.surgery);
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if ((surgeryComposeDetail === 0 && feeDetail === 0) || surgeryComposeDetail === 1) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.SURGERY);
                    }
                    if ((surgeryComposeDetail === 0 && feeDetail === 1) || surgeryComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.SURGERY) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.SURGERY);
            },
            // 套餐
            composeForms() {
                let composeForms = [];

                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const composeFeeComposeDetail = parseInt(this.feeComposeDetail.compose);
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if ((composeFeeComposeDetail === 0 && feeDetail === 0) || composeFeeComposeDetail === 1) {
                        // 打印医嘱项目
                        composeForms = deepClone(this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT);
                    } else if ((composeFeeComposeDetail === 0 && feeDetail === 1) || composeFeeComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        composeForms = result;
                    } else {
                        // 打印费用类型
                        composeForms = [];
                    }
                } else {
                    composeForms = deepClone(this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT);
                }

                // 如果只打印套餐子项
                if (parseInt(this.feeInfo.composeChildren) === 1) {
                    composeForms.forEach((composeForm) => {
                        let tempChargeFormItems = [];
                        if (composeForm.chargeFormItems && composeForm.chargeFormItems.length) {
                            tempChargeFormItems = deepClone(composeForm.chargeFormItems);
                            composeForm.chargeFormItems = [];
                        }
                        tempChargeFormItems.forEach((chargeFormItem) => {
                            if (chargeFormItem.composeChildren && chargeFormItem.composeChildren.length) {
                                composeForm.chargeFormItems = composeForm.chargeFormItems.concat(chargeFormItem.composeChildren);
                            }
                        });
                    });
                }
                return composeForms;
            },
            // 西药 + 输液 后台统一将 ADDITIONAL_SALE_PRODUCT_FORM PRESCRIPTION_INFUSION 转换成了 printFormType 4
            westernMedicineForms() {
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const westernMedicineFeeComposeDetail = parseInt(this.feeComposeDetail.westernMedicine);
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if ((westernMedicineFeeComposeDetail === 0 && feeDetail === 0) || westernMedicineFeeComposeDetail === 1) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.PRESCRIPTION_WESTERN || form.printFormType === PrintFormTypeEnum.PRESCRIPTION_EXTERNAL);
                    }
                    if ((westernMedicineFeeComposeDetail === 0 && feeDetail === 1) || westernMedicineFeeComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.PRESCRIPTION_WESTERN || form.printFormType === PrintFormTypeEnum.PRESCRIPTION_EXTERNAL) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.PRESCRIPTION_WESTERN || form.printFormType === PrintFormTypeEnum.PRESCRIPTION_EXTERNAL);
            },
            // 中药
            chineseMedicineForms() {
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const chineseMedicineFeeComposeDetail = parseInt(this.feeComposeDetail.chineseMedicine);
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if ((chineseMedicineFeeComposeDetail === 0 && feeDetail === 0) || chineseMedicineFeeComposeDetail === 1) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.PRESCRIPTION_CHINESE || form.printFormType === PrintFormTypeEnum.AIR_PHARMACY);
                    }
                    if ((chineseMedicineFeeComposeDetail === 0 && feeDetail === 1) || chineseMedicineFeeComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.PRESCRIPTION_CHINESE || form.printFormType === PrintFormTypeEnum.AIR_PHARMACY) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.PRESCRIPTION_CHINESE || form.printFormType === PrintFormTypeEnum.AIR_PHARMACY);
            },
            // 材料商品
            materialGoodsForms() {
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const materialGoodsFeeComposeDetail = parseInt(this.feeComposeDetail.materialGoods);
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if ((materialGoodsFeeComposeDetail === 0 && feeDetail === 0) || materialGoodsFeeComposeDetail === 1) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.MATERIAL || form.printFormType === PrintFormTypeEnum.FAMILY_DOCTOR_SIGN);
                    }
                    if ((materialGoodsFeeComposeDetail === 0 && feeDetail === 1) || materialGoodsFeeComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.MATERIAL || form.printFormType === PrintFormTypeEnum.FAMILY_DOCTOR_SIGN) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.MATERIAL || form.printFormType === PrintFormTypeEnum.FAMILY_DOCTOR_SIGN || form.printFormType === PrintFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM);
            },
            // 配镜处方
            glassesMedicineForms() {
                const result = [];
                const glassesForms = this.chargeForms.filter((chargeForm) => chargeForm.printFormType === PrintFormTypeEnum.PRESCRIPTION_GLASSES);
                glassesForms.forEach((glassesForm) => {
                    const cacheGlassesForm = deepClone(glassesForm);
                    const chargeFormItems = [];
                    cacheGlassesForm.glassesParams.items.forEach((item) => {
                        if (GLASSES_TYPE[cacheGlassesForm.glassesType].includes(item.key)) {
                            chargeFormItems.push(item);
                        }
                    });
                    delete cacheGlassesForm.glassesParams;
                    cacheGlassesForm.chargeFormItems = chargeFormItems;
                    result.push(cacheGlassesForm);
                });
                return result;
            },
            // 其他费用
            otherForms() {
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const otherFeeComposeDetail = parseInt(this.feeComposeDetail.other);
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if ((otherFeeComposeDetail === 0 && feeDetail === 0) || otherFeeComposeDetail === 1) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.OTHER);
                    }
                    if ((otherFeeComposeDetail === 0 && feeDetail === 1) || otherFeeComposeDetail === 2) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.OTHER) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.OTHER);
            },
            // 眼镜
            glassesForms() {
                // 如果是医费分离(医院管家)
                if (this.isFeeCompose) {
                    const feeDetail = parseInt(this.feeInfo.feeDetail);
                    if (feeDetail === 0) {
                        // 打印医嘱项目
                        return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.GLASSES);
                    }
                    if (feeDetail === 1) {
                        // 打印费用项目
                        const result = [];
                        const cacheChargeForm = deepClone(this.chargeForms || []);
                        cacheChargeForm.forEach((form) => {
                            if (form.printFormType === PrintFormTypeEnum.GLASSES) {
                                this.caleFeeItem(form);
                                result.push(form);
                            }
                        });
                        return result;
                    }
                    // 打印费用类型
                    return [];
                }

                // 不是医费分离(诊所管家)
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.GLASSES);
            },
            // 返券
            giftCoupons() {
                return this.printData.giftCoupons || [];
            },
            subTotals() {
                return this.printData && this.printData.subTotals;
            },

            chargeTransactions() {
                return this.printData && this.printData.chargeTransactions || [];
            },
            actuallyReceiveTransactions() {
                return this.chargeTransactions.filter(item => item.payMode !== 20)
            },
            latestOweTransaction() {
                return this.printData.latestOweTransaction;
            },
            repaymentTransactions() {
                return this.chargeTransactions.filter(item => item.chargeType === this.chargeType.OWE); // 还款流水
            },
            lastRepaymentFee() {
                const len = this.repaymentTransactions;
                return this.repaymentTransactions[len-1].amount;
            },
            healthCardExtraItems() {
                const { region } = this.shebaoPayment;
                const shebaoAttr = SheBaoRegionPayment[region];
                const extralItems = this.shebaoPayment.extraInfo;
                const res = [];
                if (shebaoAttr) {
                    for (const attr in shebaoAttr) {
                        if (shebaoAttr.hasOwnProperty(attr)) {
                            let temp = null;
                            if (region === 'chongqing') {
                                if (extralItems[attr] > 0) {
                                    temp = {};
                                    temp.label = shebaoAttr[attr];
                                    temp.value = extralItems[attr];
                                }
                            } else {
                                temp = {};
                                temp.label = shebaoAttr[attr];
                                temp.value = extralItems[attr];
                            }
                            if (temp) {
                                res.push(temp);
                            }
                        }
                    }
                }
                return res;
            },
            // 是否有收银信息
            hasCashierInfo() {
                return (
                    this.cashierInfo.totalFee ||
                    this.cashierInfo.discountFee ||
                    this.cashierInfo.receivableFee ||
                    this.cashierInfo.netIncomeFee ||
                    this.hasMemberCardPay ||
                    this.hasHealthCardPay
                );
            },
            // 是否会会员卡支付
            hasMemberCardPay() {
                return (
                    this.printData.memberCardBalance !== null &&
                    this.printData.memberCardBalance !== '' &&
                    this.printData.memberCardBalance !== undefined
                );
            },
            // cardId;                  //卡号
            // cardOwner;               //卡持有者
            // cardOwnerType;           //持卡人类型 职工 居民 离休干部 等
            // idCardNum;               //卡持有者身份证号
            // beforeCardBalance;   //刷卡前余额
            // cardBalance;         //卡余额
            // relationToPatient;       //社保支付的持卡人和患者的关系
            // // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
            // receivedFee;
            // accountPaymentFee;   //帐户支付金额
            // fundPaymentFee;      //统筹支付金额
            // otherPaymentFee;     //其它支付金额
            // private JsonNode extraInfo; 根据地区从 以下ExtraInfo中反序列化
            shebaoPayment() {
                return this.printData.shebaoPayment || null;
            },
            // 是否有社保卡支付
            hasHealthCardPay() {
                return !!this.shebaoPayment;
            },
            hasPromotion() {
                return this.giftCoupons.length;
            },
            // 卡项信息
            promotionBalances() {
                return this.printData.promotionBalances || [];
            },
            chargeType() {
                return {
                    NORMAL: 0,
                    HOSPITAL: 1,
                    OWE: 2,
                }
            },
            // 欠费状态常量
            owedStatus() {
                return {
                    NO_OWED: 0,
                    OWING: 10,
                }
            },
            // 欠费状态
            isOweStatus() {
                return this.printData.owedStatus === this.owedStatus.OWING;
            },
            isFeeCompose() {
                return !!this.printData.IS_FEE_COMPOSE;
            },
            barcodeSrc() {
                const barcode = this.printData.patientOrderNo ? `${this.printData.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode, { format: 'CODE128A' });
            },
            logo() {
                return this.organ.logo;
            },
            medicalBills() {
                return this.printData.medicalBills || [];
            },
            memberInfo() {
                return this.printData.memberInfo;
            },
            calcMedicalBills() {
                const medicalBills = new Map();

                const feeDetail = parseInt(this.feeInfo.feeDetail);
                const cacheChargeForms = deepClone(this.chargeForms || []);

                // 挂号费
                const registrationFeeComposeDetail = parseInt(this.feeComposeDetail.registration);
                if ((registrationFeeComposeDetail === 0 && feeDetail === 2) || registrationFeeComposeDetail === 3) {
                    const registrationForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.REGISTRATION);
                    this.transMedicalBills(medicalBills, registrationForms);
                }
                // 检验检查
                const examinationFeeComposeDetail = parseInt(this.feeComposeDetail.examination);
                if ((examinationFeeComposeDetail === 0 && feeDetail === 2) || examinationFeeComposeDetail === 3) {
                    const examinationForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.EXAMINATION);
                    this.transMedicalBills(medicalBills, examinationForms);
                }
                // 治疗理疗
                const treatmentComposeDetail = parseInt(this.feeComposeDetail.treatment);
                if ((treatmentComposeDetail === 0 && feeDetail === 2) || treatmentComposeDetail === 3) {
                    const treatmentForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.TREATMENT);
                    this.transMedicalBills(medicalBills, treatmentForms);
                }
                // 护理
                const nursingComposeDetail = parseInt(this.feeComposeDetail.nursing);
                if ((nursingComposeDetail === 0 && feeDetail === 2) || nursingComposeDetail === 3) {
                    const nursingForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.NURSING);
                    this.transMedicalBills(medicalBills, nursingForms);
                }
                // 手术
                const surgeryComposeDetail = parseInt(this.feeComposeDetail.surgery);
                if ((surgeryComposeDetail === 0 && feeDetail === 2) || surgeryComposeDetail === 3) {
                    const surgeryForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.SURGERY);
                    this.transMedicalBills(medicalBills, surgeryForms);
                }
                // 西药
                const westernMedicineComposeDetail = parseInt(this.feeComposeDetail.westernMedicine);
                if ((westernMedicineComposeDetail === 0 && feeDetail === 2) || westernMedicineComposeDetail === 3) {
                    const westernMedicineForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.PRESCRIPTION_WESTERN || form.printFormType === PrintFormTypeEnum.PRESCRIPTION_INFUSION);
                    this.transMedicalBills(medicalBills, westernMedicineForms);
                }
                // 中药
                const chineseMedicineComposeDetail = parseInt(this.feeComposeDetail.chineseMedicine);
                if ((chineseMedicineComposeDetail === 0 && feeDetail === 2) || chineseMedicineComposeDetail === 3) {
                    const chineseMedicineForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.PRESCRIPTION_CHINESE || form.printFormType === PrintFormTypeEnum.AIR_PHARMACY);
                    this.transMedicalBills(medicalBills, chineseMedicineForms);
                }
                // 材料商品
                const materialGoodsComposeDetail = parseInt(this.feeComposeDetail.materialGoods);
                if ((materialGoodsComposeDetail === 0 && feeDetail === 2) || materialGoodsComposeDetail === 3) {
                    const materialGoodsForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.MATERIAL || form.printFormType === PrintFormTypeEnum.FAMILY_DOCTOR_SIGN);
                    this.transMedicalBills(medicalBills, materialGoodsForms);
                }
                // 其他费用
                const otherComposeDetail = parseInt(this.feeComposeDetail.other);
                if ((otherComposeDetail === 0 && feeDetail === 2) || otherComposeDetail === 3) {
                    const otherForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.OTHER);
                    this.transMedicalBills(medicalBills, otherForms);
                }
                // 眼镜
                if (feeDetail === 2) {
                    const glassesForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.GLASSES);
                    this.transMedicalBills(medicalBills, glassesForms);
                }
                // 套餐
                let composeForms = [];
                const composeFeeComposeDetail = parseInt(this.feeComposeDetail.compose);
                if ((composeFeeComposeDetail === 0 && feeDetail === 2) || composeFeeComposeDetail === 3) {
                    const composeCacheForms = cacheChargeForms.filter((form) => form.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT);
                    composeCacheForms.forEach((form) => {
                        this.caleFeeItem(form);
                        composeForms.push(form);
                    });

                    composeForms.forEach((composeForm) => {
                        let tempChargeFormItems = [];
                        if (composeForm.chargeFormItems && composeForm.chargeFormItems.length) {
                            tempChargeFormItems = deepClone(composeForm.chargeFormItems);
                            composeForm.chargeFormItems = [];
                        }
                        tempChargeFormItems.forEach((chargeFormItem) => {
                            if (chargeFormItem.composeChildren && chargeFormItem.composeChildren.length) {
                                composeForm.chargeFormItems = composeForm.chargeFormItems.concat(chargeFormItem.composeChildren);
                            }
                        });
                    });

                    this.transMedicalBills(medicalBills, composeCacheForms);
                }

                const resMedicalBills = [];
                medicalBills.forEach((val) => {
                    resMedicalBills.push(val);
                });

                // 对费用类型进行排序
                resMedicalBills.sort((before, after) => {
                    const beforeSort = FEE_TYPE_SORT[before.id];
                    const afterSort = FEE_TYPE_SORT[after.id];
                    if (beforeSort && afterSort) {
                        return beforeSort - afterSort;
                    } else if (beforeSort) {
                        return -1;
                    } else if (afterSort) {
                        return 1;
                    }
                    return 0;
                });

                return resMedicalBills;
            },
            cashierPrintConfig() {
                return this.viewDistributePrintConfig.cashier;
            },

            showPatientOrderNo() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.showPatientOrderNo
                }
                return true
            },
            showChineseDoseCount() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.showChineseDoseCount
                }
                return true
            },
            //是否只展示有值的name
            onlyShowHasContentName() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.onlyShowHasContentName
                }
                return false
            },
            //是否只展示有值的手机号
            onlyShowHasContentMobile() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.onlyShowHasContentMobile
                }
                return false
            },
            // 药名排版优化
            medicineNameOptimization() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.medicineNameOptimization;
                }
                return false
            },
            // 满足药店+58mm条件则优化排版
            shouldOptimization() {
                return this.isOptimization && this.medicineNameOptimization;
            },
            // 是否展示医生
            showDoctorInfo() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.isSupportDoctorInfo
                }
                return true
            },
            // 开单人文案
            openSheetPersonText() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.openSheetPersonText
                }
                return '开单人'
            },

            // 是否支持中药明细煎法
            chineseMedicineDetailIsSupportSpecialRequirement() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.chineseMedicineDetailIsSupportSpecialRequirement
                }
                return true;
            },
            // 是否支持其中药明细剂量
            chineseMedicineDetailIsSupportUnitCount() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.chineseMedicineDetailIsSupportUnitCount
                }
                return true;
            },
            // 是否支持中药明细总重
            chineseMedicineDetailIsSupportTotalCount() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.chineseMedicineDetailIsSupportTotalCount
                }
                return true;
            },
            // 是否支持就诊条码
            isSupportOutpatientBarcode() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.isSupportOutpatientBarcode
                }
                return true;
            },
            // 是否展示其中药明细剂量
            showChineseUnitCount() {
                return this.chinese.unitCount && this.chineseMedicineDetailIsSupportUnitCount;
            },
            // 是否展示中药明细煎法
            showChineseSpecialRequirement() {
                return this.chinese.specialRequirement && this.chineseMedicineDetailIsSupportSpecialRequirement;
            },
            // 是否展示中药明细总重
            showChineseTotalCount() {
                return this.chinese.totalCount && this.chineseMedicineDetailIsSupportTotalCount;
            },
            // 是否展示就诊条码
            showBarCode() {
                return this.config.barcode && this.isSupportOutpatientBarcode;
            },
            // 是否展示微诊所二维码
            footerIsShowWeClinicQrCode() {
                if(this.cashierPrintConfig) {
                    return this.cashierPrintConfig.footerIsSupportWeClinicQrCode
                }
                return true;
            },
        },
        methods: {
            toHan,
            getTotalPriceByForms,
            isEmpty(value) {
                return value === undefined || value === null || value === '' || value === '0' || value === 0
            },
            formatAge,
            getMobile(str) {
                if (!str) return '';
                return str.substr(-4);
            },
            formatGiftCoupons() {
                let res = [];
                if (this.giftCoupons.length) {
                    res = this.giftCoupons.map((item) => {
                        return `${item.name}(${item.count}张，` + (item.validType === 0 ? `领券后${item.validDays || "0"}天内有效)` : `${item.validEnd}前有效)`);
                    });
                    return res.join('，');
                }
                return '';
            },
            // 从非套餐中筛选出费用项
            caleFeeItem(chargeForm) {
                let tempChargeFormItems = [];
                if (chargeForm.chargeFormItems && chargeForm.chargeFormItems.length) {
                    tempChargeFormItems = deepClone(chargeForm.chargeFormItems);
                    chargeForm.chargeFormItems = [];
                }
                tempChargeFormItems?.forEach((chargeFormItem) => {
                    if (chargeFormItem.goodsFeeType === GoodsFeeType.FEE_PARENT) {
                        // 如果是医嘱
                        if (chargeFormItem.composeChildren && chargeFormItem.composeChildren.length) {
                            chargeForm.chargeFormItems = chargeForm.chargeFormItems.concat(chargeFormItem.composeChildren);
                        }
                    } else {
                        chargeForm.chargeFormItems.push(chargeFormItem);
                    }
                });
            },
            // 从套餐中筛选出费用项
            calcFeeCompose(chargeForm) {
                if (chargeForm.chargeFormItems && chargeForm.chargeFormItems.length) {
                    chargeForm.chargeFormItems.forEach((chargeFormItem) => {
                        let tempComposeChildren = []
                        if (chargeFormItem.composeChildren && chargeFormItem.composeChildren.length) {
                            tempComposeChildren = deepClone(chargeFormItem.composeChildren);
                            chargeFormItem.composeChildren = [];
                        }
                        tempComposeChildren.forEach((children) => {
                            if (children.goodsFeeType === GoodsFeeType.FEE_PARENT) {
                                // 如果是医嘱
                                if (children.composeChildren && children.composeChildren.length) {
                                    chargeFormItem.composeChildren = chargeFormItem.composeChildren.concat(children.composeChildren);
                                }
                            } else {
                                chargeFormItem.composeChildren.push(children);
                            }
                        });
                    });
                }
            },
            transMedicalBills(medicalBills, forms) {
                const resultForms = [];
                forms.forEach((form) => {
                    this.caleFeeItem(form);
                    resultForms.push(form);
                });
                resultForms.forEach((form) => {
                    (form.chargeFormItems || []).forEach((item) => {
                        const { feeTypeId, feeTypeName } = item;
                        if (feeTypeId && feeTypeName) {
                            let medicalBill = medicalBills.get(feeTypeId);
                            if (medicalBill) {
                                medicalBill = {
                                    ...medicalBill,
                                    totalFee: medicalBill.totalFee + item.totalPrice,
                                };
                            } else {
                                medicalBill = { id: feeTypeId, name: feeTypeName, totalFee: item.totalPrice };
                            }
                            medicalBills.set(feeTypeId, medicalBill);
                        } else {
                            let medicalBill = medicalBills.get(FEE_TYPE_ID_ABC_INNER_MAX);
                            if (medicalBill) {
                                medicalBill = {
                                    ...medicalBill,
                                    totalFee: medicalBill.totalFee + item.totalPrice,
                                };
                            } else {
                                medicalBill = { id: FEE_TYPE_ID_ABC_INNER_MAX, name: '其他费用', totalFee: item.totalPrice };
                            }
                            medicalBills.set(FEE_TYPE_ID_ABC_INNER_MAX, medicalBill);
                        }
                    });
                });
            },
        },
    }
</script>


<style lang="scss">
@import "../../style/ticket-common.scss";

.print-ticket-content {
    .header-info {
        padding-top: 4pt;

        .logo-wrapper {
            width: 100%;
            margin-bottom: 8pt;
            text-align: center;

            .logo-image {
                width: auto;
                height: 40pt;
                vertical-align: middle;
                border: 0;
            }

            .no-logo {
                font-size: 10pt;
            }
        }

        .organ-title {
            font-size: 13pt;
            text-align: center;
        }

        .header-text-info {
            font-size: 10pt;
            line-height: 14pt;
            text-align: center;
        }

        .barcode-wrapper {
            margin: 4pt 0;
            text-align: center;

            .barcode-image {
                width: 148pt;
                height: 24pt;
                vertical-align: middle;
                border: 0;
            }
        }

        .header-type {
            margin: 2pt 0;
            font-size: 13pt;
            text-align: center;
        }

        .patient-info-wrapper {
            .left-title {
                position: absolute;
                top: 0;
                left: 0;
            }

            .right-content {
                box-sizing: border-box;
                display: inline-block;
                width: 100%;
                padding-left: 39pt;
                word-break: break-all;
                word-wrap: break-word;
            }

            .patient-info {
                position: relative;
                font-size: 10pt;
                font-weight: normal;
                line-height: 14pt;
            }

            .patient-other-info {
                font-size: 0;

                .other-info {
                    position: relative;
                    display: inline-block;
                    width: 54%;
                    font-size: 10pt;
                    font-weight: normal;
                    line-height: 14pt;
                    vertical-align: top;

                    &:nth-of-type(2n) {
                        width: 46%;
                    }
                }

                .other-info-new {
                    position: relative;
                    font-size: 10pt;
                    font-weight: normal;
                    line-height: 14pt;
                }
            }
        }
    }

    .split-solid-line {
        margin-top: 8pt;
    }

    .glasses-prescription {
        padding-top: 6pt;

        .glasses-text-info {
            height: 18pt;
            font-size: 12pt;
            font-weight: normal;
            line-height: 18pt;
            text-align: center;
        }

        .is-wrap {
            overflow: visible;
            word-break: break-all;
            white-space: normal;
        }
    }
}

[data-size=page_热敏小票（58mm）] {
    .barcode-image {
        width: 124pt !important;
    }

    .logo-image {
        height: 32pt !important;
    }
}
</style>
