<template>
    <div class="exam-cloud-chart-wrapper">
        <div
            ref="processBar"
            class="progress-bar"
        >
            <div class="progress-negative"></div>

            <div class="progress-positive"></div>


            <img
                v-if="isPositive"
                ref="indicator"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA9CAYAAADxoArXAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAVDSURBVHgB5ZtPbBVFHMd/s1uwCRg2wbQIhy4e8AT2jyZ4gW7koG0J7VEutAfRm3jkVDwYT0bUi4HEwkE5tgSsejCvfSSKKS0v1YsYZGsiCmngldJY377dcX5rG6F5nZ3fzsyC4ZM0eXk7nbffnd/vN7/f/N4DeMJgUDCl/m7Prd3rx9fxxo0TwdjlEAqkUMGl/r2+E0Ul8dJfeStMNmwIihTtQIG4UTQM/4lF8AGMQoEUJvhST+cwBxhscKm93Pvih1AQhZh0qeeldoclVzOGDe7/cvosWMa64AZ+ux5V4c8dtv3ZukkLvx2BbLGIhw+m1N/ugUWsCl7x227Cv/hu5Fr1Z2smXX6tq587kCsCs4S/s++rmZNgASuCCX67Lgl3OoLxqQoYxopJO1EdV9YHDURUH7Xhz8YFo98C8HbQB/3ZeFJiVHC5p3MwYewEGAID3r8P0BzGfDj123p0VdylcTMUDzEILl6ZAAMYW+E0SFkQm87N+Sg+UDCAEcHl3k7cO32wh2eqyNAWfKmv620O7BjYx0iRoeXDNv1WglaRkVswnlw40SJWQD4Ui1aRkduk3WjRtt+uh1aRkUuwpJgvCpGUNOXan8kmrVjMF0KeIoMk2ERRYBpqUkIyaUIxXxgiKRmh+HOT6kDMk4nF/MMftOlp2PzcLmhu3Q5PtTybvldfWoSlX69B9cdp0EBsjU2YB5xQGaxk0jqm7O3ugq17u6H1QF8quhEofP77Sfjt3ClYvnUTcqC8VSmtcFOtdkT4ig8EUFzb4aOw49DrSmO3iQeCf7+fPwdzX5xKHwIBz6lFuMqZGV/mCqN/OJFLSjDQbPe8/yk0t2yHPCzfvgmzx9+irrZY5XhnMFapygZlBi235mAfyAdFdMWmc7SszNFKmsMTe/Ng1qDsKM3YESCw69iwlthVcA6ciwY/lDVCKhjNmRKZWw8cTIOUKXAulRiwCt5r1hYlFezGbjcQaDv8BpgGAx+Jv+X3LBXMOVM+jMOtx4QprwUj+BaC1ThMbpFSwQz4C6DIM0KwLVpfOUgZ7ssuyoMWobDfJLIoW3h7OtUHM9Ymuyw3acJ2tNmi4PUytMbw/EHrcYEmWI4xwcRU8JHxvxB8X1RUpsiI0hCCIguzM2ALimDGWCi7nhW05kCRP7+9ALa4c3lCeSxP+ILsesYKM+X+7IIo4m2YNVZO8wTBIvOQ3rNUcMyA1JDGOtY0c5+fJo1PACZk1+VBq6leEcssrS8fBIt3zeOah8C5blFdBe9ZglQwFtOM01b52sl3UzPUBefAuUhwOK99ABAzRvpUPKVITys0ROc88RDmzMeyxigd4k32dd2lNswoZ1oP8sc3o9Ubn33s5QiAoWiy7cwapJh4sI+ACN7w9dMfwNTRgfDu9HeheEtmatU7P0xWZo+/Cb988l4esXggr9RRVDymFQd5dfeGZls03Ppyd3XL87uheduOavzXkrc8f7u6dP1nr/rTjF+/f09r7iSOg+DrSpg1ULnVUhIH8Q5jI/AYknA+FIzPnFEZS+otlXu7SjrdBxuIVHJi38Urgep4UvEQx/EQEPLrAgjjen2I8g/52qVOUir4aw4NSXjcEYxXSHkCuTzE7z8moi8Lj5jEcYaoYpFc9TAGiMSBAUraaQzxmanYC1NnIAd63+J5td13XLfIBnkozHggz8quonXigfse7n/ipf3fKjAYE82yDh2x6TxgiJV9eu3PdEwQiixqyNR3LY1/QdyUcNxf4yQ5q5pQKM8Llki3L0gGxQnEftGzUWvZMHFakfDJxGFjplZ0Lf8Abi8ami5eIT0AAAAASUVORK5CYII="
                class="indicator"
                :style="{
                    left: `${indicatorLeft}%`
                }"
            />

            <img
                v-else
                ref="indicator"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAAA9CAYAAAAeYmHpAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAVXSURBVHgB5ZtNbBNHFMffrG1Ma1tZ2kNUiRaHNhIHHLnHSFS1pUZyEFITbsVGTW6oF0oPrcIl4QLqhdBDq5yKUWN6a4xaEUtFwqhS4VLFdS4cILgREkWqYEns1Ik/pjOb+kCId+ftzmyC+ElRlOyMNf+d92beezMGeAUhsEOEf0glKCFxQmmperJQBA/RYAcI51KT7HXfJECn+W/zbw/xfKb1H1PRZgsebP0/0cjo6on5PHiApzPNBbdacHO7Z7QNl/lz8ABPRTfbZJoCRLd/SvVmi8zpcyM6KMYz0abfUjpi3YrGm/+uK/dvT3yar9R8wRJtT4GcqWXmL4EilIvu+HF3s94OYrDtLFlLXy+BApSbN/dTnGAO1TXaVubfSkVv7r80Dg7gL6q5tn4ZFOADRUSuDo+wkc+AOw4Fjvc/a/x07w5IRIlPO/NjCygkZYaqSsxbqmAGIXIDF+miw7PD0zIFc0z/boE0/5YqOpJLjbEhfg5qSISvDk+DBKT5tJlINMkCEKo0jJSRmEgRzffTVq2+INust4cYfh993/ikUAGHSDHvZm190hvBHPeJiWvRodzR0wr9uAvuEhNX5t2tIOAVThMTx6KlByCOcObfjs3buiDgFVTnLx7r345EixUEvMEMXGr1OUwfdMJhFgQAsrC7iGISE7RPR2ZTD9yY9YFQLxzpHYCePSHoCYTM/y0+XTJ//qo9Bucw/w4F+4zRvGHX0g8IuFlTihesM3En3h2CY/sH4QMmuBvlp/fhu7t5yC3dADxs/97cxs7YtRSeaaerNRc5M/gFvMNmWJRlNuOnbl+E3x6XAYs/tHef3WwLL2SNFhnBCp4YSMP1j75GCebw9rzfRCwNWJrVum2gJCxaA3oaEHDBZ2MZcMPZgQxeOCG24xQSbR62IWaZm7RbwR24cKt14EWo/v8O0xXBmSaoPZn7sEz45/HVXhjNerxiogl8CIKkDw6hfdgO/nnpviHxDtR6vIIzLV7G/eyQmkDt2NuDiNY0bhWa2opmqaOwYB54DOw7CCrgfn0AYUGNtY1ot2e2okm7LRzMx95QI7jDkd6YcFuN0GjXZ2AD0cRX7ZiuVnRPICzemFLn5r2bQK3gFkgV/axRg5cBuaI31IpeNJZABraiaRsqIIiTBAFD+Ym4aKtx24r2+8VF8+xIlXCeby8j8m2qaV0zLVvRZtGNEtvEvMMvD2+DCr69i6oIgdUtBsEwlApfg8gt/QrLVTcVkBfhn4csLBStHoqJpnALBOGL2ak7F0Em6M8j5E+rx6KrdxEQcL++sJgDGVwo59DrBDsEyFo9F6qGbszdq+w5/l4CEDk1Hyhhp+m4XPh5uODzi7OoPqz+Valm5ies2gjv0+ztXQMk58uzcPTGV2gfNzaqBu+HFWxC4JxdE2HRgUYwi1nFO/AZP3xtrPLlHzOVyuojy/5P1ldKkwvfG4fzY7qTrY/Psk+zd0VU3ZuVYaZYD1fXGPsj+0uJt+JG/M1+PeJ7zViu/a3/U18hPz/8vef+yiPd1aE+hXPVk4Upu2aeF/tVwWd5NVPoE2mLjr1ZsX8cdiGYcaFFm/e5qP1i4SmbZl0Ube74fJqZeZaZ+aew0xCSr6bnRzFdHKeWvsZedpJAlNzSFYeU/K8H0e7mWLQxnjf8jWCSv2nYGYrslDIpckq5FSlXqmRsZSgI+YaZtOPLPdIuz5m3BSmovVrFgiPig3G3l+eklYtW04WszwdJ9havgArY7PrDwT4ZX2NSefV5yvXqzsNeDa74NXrJzQ3BrSj9Dod5fXKtzs61ycfQhoRQiLkptMiCjVuBUDDrZKGy4z9r3hXCon65+AAAAABJRU5ErkJggg=="
                class="indicator"
                :style="{
                    left: `${indicatorLeft}%`
                }"
            />

            <div
                ref="indicatorText"
                class="indicator-text"
                :style="{
                    left: left ? `${left}%` : 0
                }"
            >
                <span class="text">{{ label }}：{{ value }}</span>

                <div
                    :class="[
                        'status',
                        {
                            'positive': isPositive
                        }
                    ]"
                >
                    {{ isPositive ? '阳性' : '阴性' }}
                    <span v-if="isPositive">↑</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {
        DEFAULT_THRESHOLD,
    } from './constant';

    export default {
        name: 'CloudReportChart',

        props: {
            value: {
                type: Number,
                required: true,
                default: 0,
            },

            threshold: {
                type: Number,
                default: DEFAULT_THRESHOLD,
            },

            label: {
                type: String,
                default: '',
            },

            left: {
                type: Number,
                default: 0,
            }
        },
      
        computed: {
            isPositive() {
                return this.value >= this.threshold;
            },

            formatValue() {
                return Math.min(Math.max(this.value, 0), 1);
            },

            indicatorLeft() {
                if (this.formatValue <= this.threshold) {
                    return this.formatValue * 2 * 100;
                }

                return (this.formatValue * 0.8 + 0.2) * 100;
            },
        },
    };
</script>

<style lang="scss" scoped>
.exam-cloud-chart-wrapper {
  position: relative;
  padding: 58px 10px 20px;

  .progress-bar {
    border-radius: 4px;
    font-size: 11px;
    font-weight: 300;
    position: relative;
    display: flex;

    .progress-negative {
      width: 20%;
      border-radius: 4px 0 0 4px;
      background: #C1E9D1;
      height: 8px;
      position: relative;

      &:before {
        content: '0';
        position: absolute;
        left: -3px;
        bottom: -16px;
      }

      &:after {
        content: '0.1';
        position: absolute;
        right: -8px;
        bottom: -16px;
      }
    }

    .progress-positive {
      width: 80%;
      border-radius: 0 4px 4px 0;
      background: #C14B33;
      height: 8px;
      position: relative;

      &:after {
        content: '1';
        position: absolute;
        right: -3px;
        bottom: -16px;
      }
    }

    .indicator {
      position: absolute;
      height: 20px;
      width: 20px;
      bottom: -2px;
      transform: translateX(-50%);
    }

    .indicator-text {
      font-size: 13px;
      font-weight: 400;
      position: absolute;
      top: -38px;
      display: flex;
      align-items: center;

      .text {
        white-space: nowrap;
      }

      .status {
        margin-left: 6px;
        width: 53px;
        height: 20px;
        font-size: 12px;
        font-weight: 500;
        background: #0CA448;
        border-radius: 50px;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;

        &.positive {
          background: #C14B33;
        }
      }
    }
  }
}
</style>
