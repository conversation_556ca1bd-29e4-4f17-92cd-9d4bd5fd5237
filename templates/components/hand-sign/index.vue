<template>
    <span :style="signBlock ? {...style, ...blockStyle} : style">
        <template v-if="isImage">
            <img
                :src="value"
                :style="{height:`${height }px`}"
            />
        </template>

        <template v-else>
            {{ value }}
        </template>
    </span>
</template>

<script>
    import {isImgUrl} from "../../common/utils";

    export default {
        name: 'HandSign',

        props: {
            value: {
                type: String,
                default: '',
            },
            signBlock:{
                type:Boolean,
                default:false,
            },
            height:{
                type:Number,
                default:32
            }
        },
        data(){
            return{
                style:{
                    fontSize:'9pt'
                },
                blockStyle:{
                    display:'inline-block',
                    minWidth:'50px'
                }
            }
        },
        computed: {
            isImage() {
                return isImgUrl(this.value)
            }
        },
    }
</script>