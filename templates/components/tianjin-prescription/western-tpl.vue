<template>
    <div>
        <block-box
            top="20"
            left="0"
            :font="7"
            class="organ"
            style="width: 100%; text-align: center"
        >
            {{ organTitle }}
        </block-box>
        <block-box
            top="30"
            left="112"
            :font="7"
        >
            {{ formatPatient<PERSON>rderNo(patientOrderNo) }}
        </block-box>

        <block-box
            top="30"
            left="60"
            :font="7"
        >
            {{ departmentName }}
        </block-box>
        <block-box
            top="38"
            left="29"
            :font="7"
        >
            {{ patientName }}
        </block-box>
        <block-box
            top="38"
            left="60"
            :font="7"
        >
            {{ patientSex }}
        </block-box>
        <block-box
            top="38"
            left="80"
            :font="7"
        >
            {{ patientAge }}
        </block-box>
        <block-box
            top="38"
            left="112"
            :font="7"
        >
            {{ healthCardPayLevel }}
        </block-box>
        <block-box
            top="46"
            left="29"
            :font="7"
        >
            {{ diagnosis }}
        </block-box>
        <block-box
            top="46"
            left="100"
            :font="7"
        >
            {{ diagnosisYear }}
        </block-box>
        <block-box
            top="46"
            left="114"
            :font="7"
        >
            {{ diagnosisMonth }}
        </block-box>
        <block-box
            top="46"
            left="124"
            :font="7"
        >
            {{ diagnosisDate }}
        </block-box>

        <block-box
            top="167"
            left="43"
            :font="7"
        >
            <span
                v-if="isImgUrl(doctorSignImgUrl)"
                class="sign-img"
            >
                <img :src="doctorSignImgUrl" />
            </span>
            <span
                v-else-if="doctorSignImgUrl"
            >
                {{ doctorSignImgUrl }}
            </span>
            <span
                v-else
            >
                {{ doctorName }}
            </span>
        </block-box>
        <block-box
            top="172"
            left="43"
            :font="7"
        >
            <span
                v-if="isImgUrl(compoundByHandSign)"
                class="sign-img"
            >
                <img :src="compoundByHandSign" />
            </span>
            <span
                v-else-if="compoundByHandSign"
            >
                {{ compoundByHandSign }}
            </span>
            <span
                v-else
            >
                {{ compoundName }}
            </span>
        </block-box>
        <block-box
            top="181"
            left="43"
            :font="7"
        >
            <span
                v-if="isImgUrl(auditHandSign)"
                class="sign-img"
            >
                <img :src="auditHandSign" />
            </span>
            <span
                v-else-if="auditHandSign"
            >
                {{ auditHandSign }}
            </span>
            <span
                v-else
            >
                {{ auditName }}
            </span>
        </block-box>
        <block-box
            top="174"
            left="110"
            :font="10"
        >
            {{ formatMoney(totalPrice) }}
        </block-box>
    </div>
</template>

<script>
    import BlockBox from "../medical-bill/national-medical-bill/block-box.vue";
    import {formatPatientOrderNo} from "../../common/utils.js";
    import {formatMoney} from "../../common/utils.js";
    import {isImgUrl} from "../../common/utils.js";

    export default {
        name: "WesternHeader",
        components: {
            BlockBox,
        },
        props: {
            organTitle: String,
            departmentName: String,
            patientName: String,
            patientSex: String,
            patientAge: String,
            diagnosis: String,
            diagnosisDate: String,
            diagnosisMonth: String,
            diagnosisYear: String,
            patientOrderNo: String,
            healthCardPayLevel: String,
            doctorName: String,
            compoundName: String,
            auditName: String,
            totalPrice: String,
            auditHandSign: String,
            doctorSignImgUrl: String,
            compoundByHandSign: String,
        },
        methods: {
            formatPatientOrderNo,
            formatMoney,
            isImgUrl,
        },
    }
</script>

<style lang="scss">
.organ {
    width: 25mm;
    word-break: break-all;
    word-wrap: break-word;
    line-height: 10pt;
}
.sign-img {
    position: absolute;
    top: -5mm;
    img {
        width: 58.5pt;
        height: 20pt;
        border: 0;
        border-color: #ffffff;
    }
}

</style>