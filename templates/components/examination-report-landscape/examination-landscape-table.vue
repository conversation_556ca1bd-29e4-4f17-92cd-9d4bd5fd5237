<template>
    <table
        class="examination-landscape-table"
        :class="{ 'single-column': isSingleColumn, [mode]: true }"
        data-type="complex-table"
    >
        <thead class="table-header">
            <tr class="table-tr">
                <th class="table-cell number">
                    序号
                </th>
                <th class="table-cell item-name">
                    检验项目
                </th>
                <th class="table-cell item-res">
                    结果
                </th>
                <th class="table-cell item-unit">
                    单位
                </th>
                <th class="table-cell item-range">
                    参考范围
                </th>
            </tr>
        </thead>

        <tbody class="table-body">
            <tr
                v-for="(item, index) in list"
                :key="index"
                class="table-tr"
            >
                <!--序号-->
                <td class="table-cell number">
                    {{ startIdx + index + 1 }}
                </td>

                <!--项目名称-->
                <td
                    class="table-cell item-name"
                    overflow
                >
                    <examination-identification :type="item.mutualRecognitionTag"></examination-identification>

                    {{ item.name }}
                    <template v-if="item.enName && contentConfig.itemCode">
                        ({{ item.enName }})
                    </template>
                </td>

                <!--结果-->
                <td
                    class="table-cell item-res"
                >
                    <template v-if="item.valueType !== 'IMAGE'">
                        {{ calValue2(item) }}
                    </template>
                    <nature-display :item="item"></nature-display>
                </td>

                <!--单位-->
                <td class="table-cell item-unit">
                    {{ item.unit }}
                </td>

                <!--参考范围-->
                <td class="table-cell item-range">
                    <div class="display-textarea-content">
                        <span>{{ calRange(item) }}</span>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
    import NatureDisplay from "../nature-display/index.vue";
    import {
        calRange,
        calValue2
    } from "../../common/medical-transformat";
    import ExaminationIdentification from "./examination-identification.vue";

    export default {
        name: "ExaminationLandscapeTable",
        components: {
            ExaminationIdentification,
            NatureDisplay
        },
        props: {
            list: {
                type: Array,
                default: () => []
            },

            startIdx: {
                type: Number,
                default: 0
            },

            contentConfig: {
                type: Object,
                default: () => ({})
            },

            isSingleColumn: Boolean,

            mode: {
                type: String,
                default: 'loose',
                validate(value) {
                    return ['loose', 'compact'].includes(value);
                }
            }
        },
        methods: {
            calRange,
            calValue2
        },
    }
</script>

<style lang="scss" scoped>
.examination-landscape-table {
    width: 100%;

    &.single-column {
        .table-tr {
            .table-cell {
                &.item-name {
                    width: 30%;
                }

                &.item-range {
                    width: 34%;
                }
            }
        }
    }

    &.loose {
        .table-tr {
            .table-cell {
                font-size: 9pt;
                line-height: 13pt;
            }
        }
    }

    &.compact {
        .table-tr {
            .table-cell {
                font-size: 9pt;
                line-height: 10pt;
            }
        }
    }

    .table-header {
        padding: 2pt 0;
        border-bottom: 1px solid #000000;
        font-weight: 500;
    }

    .table-body {
        font-weight: 400;
    }

    .table-tr {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        width: 100%;

        .table-cell {
            font-size: 9pt;
            line-height: 10pt;
            background-color: #ffffff;
            white-space: nowrap;
            overflow: hidden;

            &.number {
                width: 7%;
                padding-left: 2pt;
            }

            &.item-name {
                width: 42%;
                display: inline-flex;
                align-items: center;

                >svg {
                    min-width: 16px;
                }
            }

            &.item-res {
                width: 14%;
                white-space: normal;
                overflow: visible;
                word-break: break-all;
            }

            &.item-unit {
                width: 14%;
            }

            &.item-range {
                width: 22%;
            }

            .display-textarea-content {
                white-space: pre-wrap;
            }
        }
    }
}
</style>