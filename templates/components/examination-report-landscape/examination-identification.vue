<template>
    <component
        :is="tag"
    >
        <template v-if="type === IdentificationEnum.baotou">
            <span style="color: #0090FF">
                [HR]
            </span>
        </template>
        <template v-if="type === IdentificationEnum.hehehaote">
            <svg
                viewBox="0 0 14 10"
                width="14"
                height="10"
            >
                <polygon
                    points="7,1 13,9 1,9"
                    fill="#0090FF"
                    stroke="none"
                />
            </svg>
        </template>
    </component>
</template>

<script>
    const IdentificationEnum = {
        baotou: '1',
        hehehaote: '2',
    }
    export default {
        name: "ExaminationIdentification",
        props: {
            type: {
                type: String,
                default: undefined,
            },
            tag: {
                type: String,
                default: 'div'
            }
        },
        computed: {
            IdentificationEnum() {
                return IdentificationEnum
            },
        }
    }
</script>