<template>
    <div v-if="list.length">
        <div class="examination-report-landscape-image">
            <div
                v-for="(a, idx) in list"
                :key="idx"
                class="image-wrapper"
                :style="{ height: `${imgHeight}pt` }"
            >
                <img
                    :src="a.value"
                    alt="xxx"
                />
            </div>
        </div>
        <div style="height: 6pt;width: 100%"></div>
    </div>
</template>

<script>
    export default {
        name: "ExaminationLandscapeImages",
        props: {
            list: {
                type: Array,
                default: () => []
            },

            size: {
                type: String,
                default: "small",
                validator: (value) => ["small", "medium", "large"].includes(value),
            }
        },

        computed: {
            imgHeight() {
                const imgHeight2Size = {
                    small: 65,
                    medium: 85,
                    large: 105,
                };

                return imgHeight2Size[this.size];
            },
        },
    }
</script>

<style lang="scss" scoped>
.examination-report-landscape-image {
    box-sizing: border-box;
    width: 100%;
    display: flex;

    .image-wrapper {
        padding-right: 4pt;
        display: inline-block;
        font-size: 0;
        width: 19%;
        height: 65pt;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }
}
</style>