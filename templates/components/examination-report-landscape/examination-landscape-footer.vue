<template>
    <div class="print-examination-landscape-footer">
        <abc-print-row>
            <abc-print-col :span="8">
                <span class="item-label">{{ reportTypeStr }}:</span>

                <template v-if="footerConfig.testerSignature">
                    <hand-sign
                        :value="testerSignature"
                        :height="17"
                    ></hand-sign>
                </template>

                <template v-else>
                    <span class="item-value">{{ printData.testerName }}</span>
                </template>
            </abc-print-col>

            <abc-print-col :span="10">
                <span class="item-label"> 审核者: </span>

                <template v-if="footerConfig.checkerSignature">
                    <hand-sign
                        :value="checkerSignature"
                        :height="17"
                    ></hand-sign>
                </template>

                <template v-else>
                    <span class="item-value">{{ printData.checkerName }}</span>
                </template>
            </abc-print-col>

            <abc-print-col
                v-if="footerConfig.applyDate"
                :span="6"
            >
                <span class="item-label">申请时间:</span>
                <span class="item-value">{{ printData.created | parseTime('y-m-d h:i:s') }}</span>
            </abc-print-col>
        </abc-print-row>

        <abc-print-row>
            <abc-print-col
                v-if="footerConfig.checkDate"
                :span="8"
            >
                <span class="item-label"> 审核时间: </span>
                <span
                    v-if="printData.checkTime"
                    class="item-value"
                >
                    {{ printData.checkTime | parseTime('y-m-d h:i:s') }}
                </span>
            </abc-print-col>

            <abc-print-col
                v-if="footerConfig.printDate"
                :span="10"
            >
                <span class="item-label"> 打印时间: </span>
                <span class="item-value">
                    {{ new Date() | parseTime('y-m-d h:i:s') }}
                </span>
            </abc-print-col>

            <abc-print-col
                v-if="footerConfig.examineDate"
                :span="6"
            >
                <span class="item-label">检验时间:</span>
                <span class="item-value">{{ printData.testTime | parseTime('y-m-d h:i:s') }}</span>
            </abc-print-col>
        </abc-print-row>

        <abc-print-row class="tips">
            {{ footerConfig.remark }}
        </abc-print-row>

        <template v-if="footerConfig.mutualRecognitionDesc">
            <abc-print-row class="tips">
                <span>注：带</span>
                <abc-print-space
                    :value="2"
                    direction="vertical"
                ></abc-print-space>
                <examination-identification type="1"></examination-identification>
                <abc-print-space
                    :value="2"
                    direction="vertical"
                ></abc-print-space>
                <span> 标识项目为包头市互认项目，带 </span>
                <abc-print-space
                    :value="2"
                    direction="vertical"
                ></abc-print-space>
                <examination-identification type="2"></examination-identification>
                <abc-print-space
                    :value="2"
                    direction="vertical"
                ></abc-print-space>
                <span> 为呼包互认HR项目！ </span>
            </abc-print-row>
        </template>
    </div>
</template>

<script>
    import { parseTime } from '../../common/utils.js'
    import HandSign from '../hand-sign/index.vue'
    import AbcPrintRow from '../layout/abc-layout/abc-row.vue'
    import AbcPrintCol from '../layout/abc-layout/abc-col.vue'
    import ExaminationIdentification from './examination-identification.vue'
    import AbcPrintSpace from '../layout/space.vue'

    export default {
        name: 'ExaminationReportFooter',
        components: {
            AbcPrintSpace,
            ExaminationIdentification,
            HandSign,
            AbcPrintRow,
            AbcPrintCol
        },
        filters: {
            parseTime
        },
        props: {
            printData: {
                type: Object,
                required: true
            },
            isInspect: {
                type: Boolean,
                default: false
            },
            config: {
                type: Object,
                required: true
            }
        },
        computed: {
            patient() {
                return (this.printData && this.printData.patient) || {}
            },
            reportTypeStr() {
                return this.isInspect ? '检查者' : '检验者'
            },
            footerConfig() {
                return this.config.footer || {}
            },

            testerSignature() {
                return this.printData.tester?.handSign
            },

            checkerSignature() {
                return this.printData.checker?.handSign
            }
        }
    }
</script>

<style lang="scss">
    .abc-page-content__footer {
        background: #ffffff;
        z-index: 1;
    }
    .print-examination-landscape-footer {
        padding-top: 2pt;
        border-top: 1px solid #000000;
        font-size: 9pt;

        .tips {
            font-size: 9pt;
            font-family: 'Microsoft YaHei', '微软雅黑';

            span {
                color: #0090ff;
            }
        }
        .item-label,
        .item-value {
            font-size: 9pt;
            line-height: 10pt;
        }
        .item-label {
            font-family: 'Microsoft YaHei', '微软雅黑';
        }
        .item-value {
            font-weight: 300;
        }
    }
</style>
