<template>
    <div class="shanxi-table-charge-item-wrapper">
        <table class="shanxi-table-charge-item">
            <thead>
                <tr>
                    <th
                        :colspan="1"
                        class="shanxi-table-th"
                    >
                        项目(品名)
                    </th>
                    <th
                        :colspan="1"
                        class="shanxi-table-th"
                    >
                        规格
                    </th>
                    <th
                        :colspan="1"
                        class="shanxi-table-th"
                    >
                        单价*数量
                    </th>
                    <th
                        :colspan="1"
                        class="shanxi-table-th"
                    >
                        金额
                    </th>
                    <th
                        :colspan="1"
                        class="shanxi-table-th"
                    >
                        项目(品名)
                    </th>
                    <th
                        :colspan="1"
                        class="shanxi-table-th"
                    >
                        规格
                    </th>
                    <th
                        :colspan="1"
                        class="shanxi-table-th"
                    >
                        单价*数量
                    </th>
                    <th
                        :colspan="1"
                        class="shanxi-table-th"
                    >
                        金额
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td
                        :colspan="4"
                        class="shanxi-table-td"
                    >
                        <div class="shanxi-table-td-wrapper">
                            <div
                                v-for="(item, itemIndex) in splitChargeFormItems[0]"
                                :key="`shanxi-charge-item-${itemIndex}-1`"
                                class="shanxi-charge-item"
                            >
                                <div
                                    class="shanxi-item shanxi-item-name"
                                    :class="{ 'shanxi-item-name-without-spec': !isWesternMedicine(item) }"
                                >
                                    <template v-if="medicalFeeGrade2PrintStr(item.medicalFeeGrade) && isDisplaySocialCode(item, isFeeCompose, productInfoRuleConfig)">
                                        [{{ medicalFeeGrade2PrintStr(item.medicalFeeGrade) }}]
                                    </template>
                                    {{ item.name }}
                                </div>
                                <div
                                    v-if="isWesternMedicine(item)"
                                    class="shanxi-item shanxi-item-spec"
                                >
                                    |{{ item.displaySpec }}
                                </div>
                                <div class="shanxi-item shanxi-item-unit-price">
                                    |{{ item.discountedUnitPrice | formatMoney }}*{{ item.count }}
                                </div>
                                <div class="shanxi-item shanxi-item-price">
                                    |{{ item.discountedPrice | formatMoney }}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td
                        :colspan="4"
                        class="shanxi-table-td"
                    >
                        <div class="shanxi-table-td-wrapper">
                            <div
                                v-for="(item, itemIndex) in splitChargeFormItems[1]"
                                :key="`shanxi-charge-item-${itemIndex}-2`"
                                class="shanxi-charge-item"
                            >
                                <div
                                    class="shanxi-item shanxi-item-name"
                                    :class="{ 'shanxi-item-name-without-spec': !isWesternMedicine(item) }"
                                >
                                    <template v-if="medicalFeeGrade2PrintStr(item.medicalFeeGrade) && isDisplaySocialCode(item, isFeeCompose, productInfoRuleConfig)">
                                        [{{ medicalFeeGrade2PrintStr(item.medicalFeeGrade) }}]
                                    </template>
                                    {{ item.name }}
                                </div>
                                <div
                                    v-if="isWesternMedicine(item)"
                                    class="shanxi-item shanxi-item-spec"
                                >
                                    |{{ item.displaySpec }}
                                </div>
                                <div class="shanxi-item shanxi-item-unit-price">
                                    |{{ item.discountedUnitPrice | formatMoney }}*{{ item.count }}
                                </div>
                                <div class="shanxi-item shanxi-item-price">
                                    |{{ item.discountedPrice | formatMoney }}
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="shanxi-operate-info">
            <div
                class="shanxi-operate-text shanxi-no-wrap"
                style="width: 20%;"
                overflow
            >
                经办人：{{ chargedByName }}
            </div>
            <div
                class="shanxi-operate-text shanxi-no-wrap"
                style="width: 30%; text-align: right;"
                overflow
            >
                经办日期：{{ chargedTime | parseTime('y-m-d h:i:s') }}
            </div>
        </div>
    </div>
</template>

<script>
    import {formatMoney, isDisplaySocialCode, medicalFeeGrade2PrintStr, parseTime} from "../../common/utils.js";
    import {GoodsSubTypeEnum, GoodsTypeEnum} from "../../common/constants.js";

    export default {
        name: 'TableChargeItemShanxi',
        filters: {
            formatMoney,
            parseTime,
        },
        props: {
            printData: {
                type: Object,
                default: () => ({}),
            },
            chargeFormItems: {
                type: Array,
                default: () => [],
            },
            isFeeCompose: {
                type: Boolean,
                default: false,
            },
            productInfoRuleConfig: {
                type: Number,
                default: 1,
            },
            chargedByName: {
                type: String,
                default: '',
            },
            chargedTime: {
                type: String,
                default: '',
            },
        },
        computed: {
            splitChargeFormItems() {
                const res = [[], []];
                this.chargeFormItems.filter((item) => item.sourceItemType === 0).forEach((item, index) => {
                    if (index % 2 === 0) {
                        res[0].push(item);
                    } else {
                        res[1].push(item);
                    }
                });
                return res;
            },
        },
        methods: {
            medicalFeeGrade2PrintStr,
            isDisplaySocialCode,
            // 是否为西药
            isWesternMedicine(item) {
                return item.productType === GoodsTypeEnum.MEDICINE && item.productSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine;
            },
        },
    }
</script>

<style lang="scss">
.shanxi-table-charge-item-wrapper {
    .shanxi-table-charge-item {
        .shanxi-table-th {
            width: 12.5%;
        }

        .shanxi-table-td {
            width: 50%;
            padding: 4pt 6pt;
            font-size: 0;
            vertical-align: top;
        }

        .shanxi-table-td-wrapper {
            display: inline-block;
            width: 100%;
            height: 90pt;
            min-height: 90pt;
            max-height: 90pt;
        }

        .shanxi-charge-item {
            display: inline-block;
            width: 100%;
            font-size: 0;
        }

        .shanxi-item {
            display: inline-block;
            width: auto;
            font-size: 10pt;
            line-height: 15pt;
            white-space: nowrap;
            vertical-align: top;
        }

        .shanxi-item-name {
            max-width: 33%;
            word-break: break-all;
            word-wrap: break-word;
            white-space: normal;
        }

        .shanxi-item-name-without-spec {
            max-width: 61%;
        }

        .shanxi-item-spec {
            max-width: 28%;
            overflow: hidden;
        }

        .shanxi-item-unit-price {
            max-width: 22%;
            overflow: hidden;
        }

        .shanxi-item-price {
            max-width: 17%;
            overflow: hidden;
        }
    }

    .shanxi-operate-info {
        display: inline-block;
        width: 100%;
        padding-top: 6pt;
        font-size: 0;
        text-align: right;
    }

    .shanxi-operate-text {
        display: inline-block;
        width: 25%;
        font-size: 10pt;
        line-height: 12pt;
        text-align: left;
    }
}
</style>
