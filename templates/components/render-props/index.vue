<script>
    export default {
        name: 'AbcRenderProps',
        functional: true,
        props: {
            value: {
                type: Object,default: () => {}
            },
            render: {
                type: Function,default: () => {}
            },
            index: {
                type: Number,
                default: undefined,
            },
        },
        render: (createElement, context) => {
            const {
                render, value,index
            } = context.props;
            if (typeof render === 'function') {
                return render(createElement, value, index);
            }
            return '';
        },
    };
</script>

