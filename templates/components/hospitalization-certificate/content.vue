<template>
    <div class="hospitalization-certificate-content">
        <table class="certificate-table">
            <tbody data-type="group">
                <tr data-type="item">
                    <td
                        class="label-td"
                    >
                        姓名
                    </td>
                    <td>
                        {{ patient.name || '' }} {{ patient.sex || '' }} {{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}
                    </td>
                    <td
                        class="label-td"
                    >
                        联系电话
                    </td>
                    <td>
                        {{ patient.mobile || '' }}
                    </td>
                    <td
                        class="label-td"
                    >
                        婚姻
                    </td>
                    <td>
                        {{ MaritalStatusLabel[patient.marital] || '' }}
                    </td>
                </tr>
                <tr
                    data-type="item"
                >
                    <td
                        class="label-td"
                    >
                        民族
                    </td>
                    <td>
                        {{ patient.ethnicity || '' }}
                    </td>
                    <td
                        class="label-td"
                    >
                        家庭住址
                    </td>
                    <td colspan="3">
                        {{ (formatAddress(patient.address)) }}
                    </td>
                </tr>
                <tr
                    data-type="item"
                >
                    <td

                        class="label-td"
                    >
                        职业
                    </td>
                    <td>
                        {{ patient.profession || '' }}
                    </td>
                    <td

                        class="label-td"
                    >
                        联系人
                    </td>
                    <td>
                        {{ patient.contactName || '' }}
                    </td>
                    <td

                        class="label-td"
                    >
                        联系人电话
                    </td>
                    <td>
                        {{ patient.contactMobile || '' }}
                    </td>
                </tr>
                <tr
                    data-type="item"
                >
                    <td

                        class="label-td"
                    >
                        入院科室
                    </td>
                    <td>
                        {{ printData.departmentName || '' }}
                    </td>
                    <td

                        class="label-td"
                    >
                        入院病区
                    </td>
                    <td>
                        {{ printData.wardName || '' }}
                    </td>
                    <td

                        class="label-td"
                    >
                        入院途径
                    </td>
                    <td>
                        {{ inpatientSourceObj[printData.inpatientSource] || '' }}
                    </td>
                </tr>
                <tr
                    data-type="item"
                >
                    <td

                        class="label-td"
                    >
                        入院病情
                    </td>
                    <td>
                        {{ inpatientConditionObj[printData.inpatientCondition] || '' }}
                    </td>
                    <td

                        class="label-td"
                    >
                        门(急)诊诊断
                    </td>
                    <td colspan="3">
                        {{ outpatientDiagnosisInfo }}
                    </td>
                </tr>
                <tr
                    data-type="item"
                >
                    <td

                        class="label-td"
                    >
                        费别
                    </td>
                    <td>
                        {{ printData.feeTypeName || '' }}
                    </td>
                    <td

                        class="label-td"
                    >
                        建议押金
                    </td>
                    <td colspan="3">
                        {{ formatMoney(printData.adviceDeposit) || '' }}
                    </td>
                </tr>
                <tr
                    data-type="item"
                >
                    <td colspan="6">
                        <div class="certificate-table-remark">
                            <span class="certificate-table-remark-label">
                                备注：
                            </span>
                            <span
                                class="certificate-table-remark-content"
                                :style="{gap:patient.remark ? '4px' : 0}"
                            >
                                <span>{{ patient.remark || '' }}</span>
                                <span>{{ contentConfig.remark }}</span>
                            </span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
    import {formatAddress, formatAge, formatMoney} from "../../common/utils";

    export default {
        name:'HospitalizationCertificateContent',
        props:{
            contentConfig: {
                type: Object,
                default: () => ({}),
            },
            printData: {
                type: Object,
                default: () => ({}),
            },
        },
        data(){
            return{
                inpatientConditionObj : Object.freeze({
                    1: '病危',
                    2: '病重',
                    3: '普通病情',
                }),
                MaritalStatusLabel :Object.freeze({
                    1: '未婚',
                    2: '已婚',
                    3: '离异',
                    4: '丧偶',
                }),
                inpatientSourceObj : Object.freeze({
                    1: '急诊',
                    2: '门诊',
                    3: '其他医疗机构转入',
                    4: '其他',
                }),
            }
        },
        computed:{
            outpatientDiagnosisInfo() {
                return this.printData.preDiagnosisInfos?.[0]?.value[0]?.name || '';
            },
            patient(){
                return this.printData.patient || {}
            },
        },
        methods: {formatAge, formatAddress, formatMoney}
    }
</script>
<style lang="scss">
.hospitalization-certificate-content{
  margin-top: 24pt;
  margin-bottom: 6pt;
  table{
    border-collapse: collapse;
    border-spacing: 0;
  }
  .certificate-table {
    width: 100%;
    table-layout: fixed;
    tr{
      height: 24pt;
    }

    .label-td {
      width: 87px;
      text-align: center;
      vertical-align: middle;
    }

    td,
    th {
      word-wrap: break-word;
      box-sizing: border-box;
      padding: 3pt;
      font-size: 10pt;
      border: 1px solid #000000;
    }

    &-remark{
      display: flex;
      align-items: flex-start;
      &-label{
        white-space: nowrap;
      }
      &-content{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
      }
    }
  }
}
</style>