<template>
    <div class="hospitalization-certificate-header">
        <div class="hospitalization-certificate-title-wrapper">
            <!-- 抬头 -->
            <div class="hospitalization-certificate-title-content-wrapper">
                <div
                    v-if="headerConfig.title"
                    :class="[headerConfig.subtitle ? 'hospitalization-certificate-title' : 'hospitalization-certificate-single-title']"
                >
                    {{ headerConfig.title }}
                    <br />
                    <template v-if="headerConfig.subtitle">
                        {{ headerConfig.subtitle }}
                    </template>
                </div>
                <div
                    v-else
                    :class="[clinicTitle.subtitle ? 'hospitalization-certificate-title' : 'hospitalization-certificate-single-title']"
                >
                    {{ clinicTitle.title }}
                    <br />
                    <template v-if="clinicTitle.subtitle">
                        {{ clinicTitle.subtitle }}
                    </template>
                </div>
            </div>
        </div>
        <!-- 处方类型副标题 -->
        <div class="hospitalization-certificate-title-type-wrapper">
            住院证
        </div>
    </div>
</template>

<script>
    import {getLengthWithFullCharacter} from "../../common/utils";
    import {TITLE_MAX_LENGTH} from "../../common/constants";

    export default {
        name:'HospitalizationCertificateHeader',
        props:{
            headerConfig: {
                type: Object,
                default: () => ({}),
            },
            clinicInfo: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            clinicTitle() {
                const clinicName = this.clinicInfo.name || '';
                let title = clinicName, subtitle = '';
                const {
                    fullCharacterLength, splitLength,
                } = getLengthWithFullCharacter(clinicName, TITLE_MAX_LENGTH);
                if (fullCharacterLength > TITLE_MAX_LENGTH) {
                    title = clinicName.slice(0, splitLength);
                    subtitle = clinicName.slice(splitLength);
                }
                return {title, subtitle};
            },
        }
    }
</script>

<style lang="scss">
.hospitalization-certificate-header {
  .hospitalization-certificate-title-wrapper {
    position: relative;
    width: 100%;
    height: 30pt;
  }

  .hospitalization-certificate-title-content-wrapper {
    width: 100%;
    height: 30pt;
    text-align: center;
  }

  .hospitalization-certificate-title {
    font-size: 13pt;
    line-height: 15pt;
    color: #000000;
  }

  .hospitalization-certificate-single-title {
    font-size: 15pt;
    line-height: 30pt;
    color: #000000;
  }
}
.hospitalization-certificate-title-type-wrapper {
  width: 100%;
  height: 9pt;
  font-size: 12pt;
  margin-top: 3pt;
  line-height: 1;
  text-align: center;
}
</style>
