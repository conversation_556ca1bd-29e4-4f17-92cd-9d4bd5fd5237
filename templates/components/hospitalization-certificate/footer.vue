<template>
    <div
        class="hospitalization-certificate-footer"
        style="'justifyContent':'space-between'"
    >
        <div
            v-if="footerConfig.doctor"
            style="flex:1"
        >
            收治医生：
            <!-- 电脑签名 -->
            <template v-if="footerConfig.doctorSignatureType === 2">
                {{ printData.outpatientDoctorName || '' }}
            </template>
            <!-- 手写签名 -->
            <template v-else-if="footerConfig.doctorSignatureType === 3">
                <img
                    v-if="printData.outpatientDoctorHandSign"
                    :src="printData.outpatientDoctorHandSign"
                    alt=""
                    class="hand-sign-img"
                />
                <template v-else>
                    {{ printData.outpatientDoctorName || '' }}
                </template>
            </template>
        </div>

        <div
            v-if="footerConfig.hospitalTime"
            style="flex:1"
        >
            入院日期：{{ (printData.inpatientTime || printData.inpatientTimeRequest) | parseTime('y-m-d') }}
        </div>

        <div
            v-if="footerConfig.patientSign"
            style="flex:1"
        >
            患者签字：
        </div>
    </div>
</template>

<script>

    import {parseTime} from "../../common/utils";

    export default {
        name:'HospitalizationCertificateFooter',
        filters: {
            parseTime,
        },
        props:{
            footerConfig: {
                type: Object,
                default: () => ({}),
            },
            printData: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
    
        }
    }
</script>

<style lang="scss">
.hospitalization-certificate-footer{
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  font-size: 10.5pt;
  .hand-sign-img{
    height: 20pt;
  }
}
</style>
