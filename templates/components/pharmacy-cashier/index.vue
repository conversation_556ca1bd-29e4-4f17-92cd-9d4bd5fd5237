<template>
    <div class="pharmacy-cashier-wrapper">
        <div class="pharmacy-cashier-header">
            <!-- logo -->
            <img
                v-if="config.titleStyle && config.titleStyleCustomLogo"
                :src="config.titleStyleCustomLogo"
                alt=""
                class="logo"
            />

            <!-- Title -->
            <div class="pharmacy-info">
                <span class="pharmacy-name">
                    {{ config.title || organ.name }}
                </span>
                <span
                    v-if="clinicInfo.address"
                    style="text-align: center"
                >{{ organ.addressDetail }}</span>
                <span v-if="clinicInfo.mobile">{{ organ.contactPhone }}</span>
            </div>

            <!-- Receipt Title -->
            <span class="title">{{ isRefund ? '退费小票' : '收费小票' }}</span>
        </div>

        <div data-type="mix-box">
            <div
                class="pharmacy-cashier-content"
                data-type="group"
            >
                <div
                    class="patient-info"
                    data-type="item"
                >
                    <p v-if="patientInfo.sellerName">
                        销售员：{{ printData.sellerName }}
                    </p>
                    <p v-if="patientInfo.pharmacistName">
                        药师姓名：{{ printData.pharmacistName }}
                    </p>
                    <p v-if="patientInfo.pharmacistCode">
                        药师编码：{{ printData.pharmacistNationalDoctorCode }}
                    </p>
                </div>

                <div
                    v-if="feeInfo.chargeItem"
                    class="charge-form"
                >
                    <spacing-line
                        line-type="solid"
                        :top-margin="6"
                        :bottom-margin="6"
                        data-type="item"
                    ></spacing-line>
                    <div
                        class="form-row normal"
                        data-type="item"
                    >
                        <span class="name">名称</span>
                        <div class="item-info">
                            <p class="count">
                                数量
                            </p>
                            <p class="price">
                                单价
                            </p>
                            <p class="total-price">
                                小计
                            </p>
                        </div>
                    </div>
                    <template v-for="(form, formIndex) in chargeForms">
                        <div :key="formIndex">
                            <spacing-line
                                :top-margin="formIndex ? 6 : 4"
                                :bottom-margin="6"
                                data-type="item"
                            ></spacing-line>
                            <template v-for="(item, index) in form.chargeFormItems">
                                <div
                                    :key="`${formIndex}-${index}`"
                                    class="form-item"
                                    data-type="item"
                                >
                                    <div
                                        class="form-row"
                                        :class="{
                                            'normal': !feeInfo.autoChangeLine,
                                            'long-name': feeInfo.autoChangeLine
                                        }"
                                    >
                                        <span class="name">{{ item.printName }}</span>
                                        <div class="item-info">
                                            <p class="count">
                                                {{ `${item.unitCount}${item.unit}` }}{{ form.printFormType === 6 ? `/${item.count}${item.unit}` : '' }}
                                            </p>
                                            <p class="price">
                                                {{ item.unitPrice | formatMoney }}
                                            </p>
                                            <p class="total-price">
                                                {{ item.totalPrice | formatMoney }}
                                            </p>
                                        </div>
                                    </div>
                                    <div
                                        v-if="item.showGoodsDetailNum || item.promotionName"
                                        class="goods-info"
                                    >
                                        <div
                                            v-if="item.promotionName"
                                            class="promotion-info"
                                        >
                                            <p>{{ item.promotionName }}</p>
                                            <p
                                                v-if="item.totalPromotionFee"
                                                style="text-align: right"
                                            >
                                                {{ item.totalPromotionFee | formatMoney }}
                                            </p>
                                        </div>
                                        <div class="goods-info-item">
                                            <p v-if="itemDetail[item.printGoodsType].spec">
                                                {{ item.productInfo ? item.productInfo.displaySpec : '' }}
                                            </p>
                                            <p v-if="itemDetail[item.printGoodsType].dosageForm">
                                                剂型：{{ item.productInfo && item.productInfo.dosageFormTypeName }}
                                            </p>
                                            <p v-if="!(itemDetail[item.printGoodsType].spec && itemDetail[item.printGoodsType].dosageForm) && itemDetail[item.printGoodsType].position && item.position">
                                                柜号：{{ item.position }}
                                            </p>
                                        </div>
                                        <p v-if="itemDetail[item.printGoodsType].spec && itemDetail[item.printGoodsType].dosageForm && itemDetail[item.printGoodsType].position && item.position">
                                            柜号：{{ item.position }}
                                        </p>
                                        <div
                                            v-for="(batch, batchIndex) in item.goodsStockInfos"
                                            :key="batchIndex"
                                            class="goods-info-item"
                                        >
                                            <p v-if="itemDetail[item.printGoodsType].batchNumber">
                                                批号：{{ batch.batchNo }}
                                            </p>
                                            <p v-if="itemDetail[item.printGoodsType].validityDate">
                                                效期：{{ batch.expiryDate }}
                                            </p>
                                        </div>
                                        <p v-if="itemDetail[item.printGoodsType].manufacturer">
                                            厂家：{{ item.productInfo ? item.productInfo.manufacturerFull : '' }}
                                        </p>
                                        <p v-if="itemDetail[item.printGoodsType].mha">
                                            上市许可持有人：{{ item.productInfo ? item.productInfo.mha : '' }}
                                        </p>
                                    </div>
                                </div>
                            </template>
                            <div v-if="form.printFormType === 6 && form.doseCount">
                                <span>共 {{ form.doseCount }} 剂，{{ form.chargeFormItems.length }} 味</span>
                            </div>
                        </div>
                    </template>
                </div>

                <div
                    v-if="cashierInfo.totalFee || (cashierInfo.singlePromotionFee === 1 && !isEmpty(printData.singlePromotionFee))"
                    class="pay-info-wrapper"
                    data-type="item"
                >
                    <spacing-line></spacing-line>
                    <div
                        v-if="cashierInfo.totalFee"
                        class="pay-info"
                    >
                        <span>合计</span>
                        <span>{{ cashierInfo.singlePromotionFee === 2 ? printData.singlePromotionedTotalFee : printData.totalFee | formatMoney }}</span>
                    </div>
                    <div
                        v-if="cashierInfo.singlePromotionFee === 1 && !isEmpty(printData.singlePromotionFee)"
                        class="pay-info"
                    >
                        <span>优惠</span>
                        <span>{{ printData.singlePromotionFee | formatMoney }}</span>
                    </div>
                </div>

                <div
                    class="pay-info-wrapper"
                    data-type="item"
                >
                    <spacing-line></spacing-line>
                    <div
                        v-if="cashierInfo.receivableFee"
                        class="pay-info receivableFee"
                    >
                        <span>应收</span>
                        <span>{{ printData.receivableFee | formatMoney }}</span>
                    </div>
                    <div
                        v-if="cashierInfo.netIncomeFee"
                        class="pay-info"
                    >
                        <span>实收</span>
                        <span>{{ netIncomeFee }}</span>
                    </div>
                </div>

                <div
                    v-if="(patient.name && (memberInfoConfig.nameType || memberInfoConfig.sex || memberInfoConfig.age)) ||
                        (memberInfoConfig.mobileType && patient.mobile) ||
                        (memberInfo && (memberInfoConfig.level || memberInfoConfig.balance || memberInfoConfig.changePoints || memberInfoConfig.points))"
                    class="pay-info-wrapper"
                    data-type="item"
                >
                    <spacing-line></spacing-line>
                    <div
                        v-if="patient.name && (memberInfoConfig.nameType || memberInfoConfig.sex || memberInfoConfig.age)"
                        class="flex-between"
                    >
                        <span>会员</span>
                        <div>
                            <span v-if="memberInfoConfig.nameType">{{ patient.name | filterName(memberInfoConfig.nameType) }}</span>
                            <span v-if="memberInfoConfig.sex">{{ patient.sex }}</span>
                            <span v-if="memberInfoConfig.age">{{ formatAge(patient.age, {monthYear: 12, dayYear: 1}) }}</span>
                        </div>
                    </div>
                    <div
                        v-if="memberInfoConfig.mobileType && patient.mobile"
                        class="flex-between"
                    >
                        <span>手机号</span>
                        <span>
                            {{ patient.mobile | filterMobileV2(memberInfoConfig.mobileType) }}
                        </span>
                    </div>
                    <template v-if="memberInfo && (memberInfoConfig.level || memberInfoConfig.balance || memberInfoConfig.changePoints || memberInfoConfig.points)">
                        <div
                            v-if="memberInfoConfig.level"
                            class="pay-info"
                        >
                            <span>会员等级</span>
                            <span>{{ memberInfo.memberType.name }}</span>
                        </div>
                        <div
                            v-if="memberInfoConfig.balance"
                            class="pay-info"
                        >
                            <span>会员余额</span>
                            <span>{{ memberInfo.cardBalance | formatMoney }}</span>
                        </div>
                        <div
                            v-if="memberInfoConfig.changePoints"
                            class="pay-info"
                        >
                            <span>本次增加积分</span>
                            <span>{{ memberInfo.changePoints }}</span>
                        </div>
                        <div
                            v-if="memberInfoConfig.points"
                            class="pay-info"
                        >
                            <span>剩余积分</span>
                            <span>{{ memberInfo.points }}</span>
                        </div>
                    </template>
                </div>

                <div
                    v-if="shebaoPayment && (healthCardInfo.cardInfo || healthCardInfo.settlementInfo || healthCardInfo.balanceInfo)"
                    class="pay-info-wrapper"
                    data-type="item"
                >
                    <spacing-line></spacing-line>
                    <template v-if="healthCardInfo.cardInfo">
                        <div class="pay-info">
                            <span>持卡人</span>
                            <span>{{ shebaoPayment.cardOwner }}</span>
                        </div>
                        <div class="pay-info">
                            <span>医保卡号</span>
                            <span>{{ shebaoPayment.cardId }}</span>
                        </div>
                        <div class="pay-info">
                            <span>人员编号</span>
                            <span>{{ shebaoPayment.extraInfo.psnNo }}</span>
                        </div>
                    </template>
                    <template v-if="healthCardInfo.settlementInfo">
                        <div class="pay-info">
                            <span>医疗类别</span>
                            <span>{{ shebaoPayment.extraInfo.insutype }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.extraInfo.actPayDedc) || healthCardInfo.settlementInfo === 1"
                            class="pay-info"
                        >
                            <span>本次起付线</span>
                            <span>{{ shebaoPayment.extraInfo.actPayDedc | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.fundPaymentFee) || healthCardInfo.settlementInfo === 1"
                            class="pay-info"
                        >
                            <span>基金支付</span>
                            <span>{{ shebaoPayment.fundPaymentFee | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.extraInfo.hifpPay) || healthCardInfo.settlementInfo === 1"
                            class="pay-info pay-info-offset"
                        >
                            <span>基本统筹</span>
                            <span>{{ shebaoPayment.extraInfo.hifpPay | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.extraInfo.cvlservPay) || healthCardInfo.settlementInfo === 1"
                            class="pay-info pay-info-offset"
                        >
                            <span>公务员补助</span>
                            <span>{{ shebaoPayment.extraInfo.cvlservPay | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.extraInfo.hifobPay) || healthCardInfo.settlementInfo === 1"
                            class="pay-info pay-info-offset"
                        >
                            <span>职工大额补助</span>
                            <span>{{ shebaoPayment.extraInfo.hifobPay | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.extraInfo.hifmiPay) || healthCardInfo.settlementInfo === 1"
                            class="pay-info pay-info-offset"
                        >
                            <span>居民大病保险</span>
                            <span>{{ shebaoPayment.extraInfo.hifmiPay | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.extraInfo.mafPay) || healthCardInfo.settlementInfo === 1"
                            class="pay-info pay-info-offset"
                        >
                            <span>医疗救助</span>
                            <span>{{ shebaoPayment.extraInfo.mafPay | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.extraInfo.hifesPay) || healthCardInfo.settlementInfo === 1"
                            class="pay-info pay-info-offset"
                        >
                            <span>企业补充医疗保险</span>
                            <span>{{ shebaoPayment.extraInfo.hifesPay | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.extraInfo.othPay) || healthCardInfo.settlementInfo === 1"
                            class="pay-info pay-info-offset"
                        >
                            <span>其他</span>
                            <span>{{ shebaoPayment.extraInfo.othPay | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.accountPaymentFee) || healthCardInfo.settlementInfo === 1"
                            class="pay-info"
                        >
                            <span>个账支付</span>
                            <span>{{ shebaoPayment.accountPaymentFee | formatMoney }}</span>
                        </div>
                        <div
                            v-if="!isEmpty(shebaoPayment.extraInfo.acctMulaidPay) || healthCardInfo.settlementInfo === 1"
                            class="pay-info pay-info-offset"
                        >
                            <span>共济账户</span>
                            <span>{{ shebaoPayment.extraInfo.acctMulaidPay | formatMoney }}</span>
                        </div>
                    </template>
                    <div
                        v-if="healthCardInfo.balanceInfo"
                        class="pay-info"
                    >
                        <span>个账余额</span>
                        <span>{{ `(结算前${formatMoney(shebaoPayment.beforeCardBalance)}) ${formatMoney(shebaoPayment.cardBalance)}` }}</span>
                    </div>
                </div>

                <div
                    class="pay-info-wrapper"
                    data-type="item"
                >
                    <spacing-line></spacing-line>
                    <div
                        v-if="clinicInfo.chargeOperator"
                        class="pay-info"
                    >
                        <span>收费员</span>
                        <span>{{ printData.chargedByName }}</span>
                    </div>
                    <div
                        v-if="clinicInfo.chargeDate"
                        class="pay-info"
                    >
                        <span>收费时间</span>
                        <span>{{ printData.chargedTime | formatDate('YYYY-MM-DD HH:mm:ss') }}</span>
                    </div>
                    <div
                        v-if="clinicInfo.printDate"
                        class="pay-info"
                    >
                        <span>打印时间</span>
                        <span>{{ new Date() | formatDate('YYYY-MM-DD HH:mm:ss') }}</span>
                    </div>
                    <div v-if="remark">
                        <span>备注:</span>
                        <span>{{ remark }}</span>
                    </div>
                    <div
                        v-if="clinicInfo.patientSign"
                        class="pay-info"
                    >
                        <span>顾客签字：</span>
                    </div>
                </div>

                <div v-if="config.remark">
                    <spacing-line :bottom-margin="6"></spacing-line>
                    <p
                        data-type="item"
                        v-html="config.remark"
                    ></p>
                </div>

                <spacing-line
                    line-type="solid"
                    :top-margin="6"
                    :bottom-margin="12"
                ></spacing-line>

                <div
                    class="footer"
                    data-type="item"
                >
                    <span v-if="clinicInfo.replacementReminder">药品离柜，概不退换</span>
                    <div
                        v-if="config.invoiceCode && invoiceQrcode"
                        class="invoice-qrcode"
                    >
                        <img
                            :src="invoiceQrcode"
                            alt=""
                        />
                        <span>扫码查看电子发票</span>
                    </div>
                    <div
                        v-if="clinicInfo.traceCodeQrCode && traceCodeQrCodeUrl"
                        class="invoice-qrcode"
                    >
                        <img
                            :src="traceCodeQrCodeUrl"
                            alt=""
                        />
                        <span>扫码查看药品追溯码</span>
                    </div>
                </div>

                <div
                    class="end-point"
                    data-type="item"
                >
                    <div></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {formatAge, formatMoney, medicalFeeGrade2PrintStr, clone, add} from '../../common/utils';
    import {filterMobileV2, filterName} from '../../common/medical-transformat';
    import SpacingLine from '../../components/medical-document-header/spacing-line.vue';
    import {formatDate} from '@tool/date';
    import { PromotionTypeEnum, RuleTypeEnum } from "../../constant/print-constant";

    export default {
        name: 'PharmacyCashierTemplate',
        components: {SpacingLine},
        filters: {
            filterMobileV2,
            formatDate,
            formatMoney,
            filterName,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            isRefund: {
                type: Boolean,
                default: false,
            },
            isOptimization: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            clinicInfo() {
                return this.config.clinicInfo || {};
            },
            patientInfo() {
                return this.config.patientInfo || {};
            },
            feeInfo() {
                return this.config.feeInfo || {};
            },
            itemDetail() {
                const itemDetail = clone(this.feeInfo.itemDetail) || []
                return itemDetail.reduce((obj, current) => {
                    if (Array.isArray(current.items)) {
                        current.items = (current.items || []).reduce((pre, cur) => {
                            pre[cur.key] = cur.value;
                            return pre;
                        }, {});
                    }
                    obj[current.type] = current.items;
                    return obj;
                }, {});
            },
            cashierInfo() {
                return this.config.cashierInfo || {};
            },
            memberInfoConfig() {
                return this.config.memberInfo || {};
            },
            healthCardInfo() {
                return this.config.healthCardInfo || {};
            },
            remark() {
                let remark = [];
                if (this.clinicInfo.retailRemark) {
                    remark.push(this.printData.remarks);
                }
                if (this.clinicInfo.remark) {
                    remark.push(this.printData.latestChargeComment);
                }
                return remark.filter(item => item).join('；');
            },

            organ() {
                return this.printData.organ || {};
            },
            patient() {
                return this.printData.patient || {};
            },
            chargeForms() {
                const chargeForms = (this.printData.chargeForms || []).sort((a, b) => {
                    // 顺序： 中西成药、非配方饮片、配方饮片、商品、其他、赠品
                    const priorityOrder = { 4: 0, 28: 1, 6: 2, 9: 3, 10: 5 };
                    const getPriority = (type) => priorityOrder[type] !== undefined ? priorityOrder[type] : 4;

                    const priorityA = getPriority(a.printFormType);
                    const priorityB = getPriority(b.printFormType);

                    if (priorityA !== priorityB) return priorityA - priorityB;
                    return a.printFormType - b.printFormType;
                });
                let index = 0;
                chargeForms.forEach(form => {
                    form.chargeFormItems = form.chargeFormItems.map(item => {
                        const itemInfo = {
                            ...item,
                            index: ++index,
                        }
                        itemInfo.printGoodsType = this.getChargeFormType(item.productType, item.productSubType);
                        itemInfo.showGoodsDetailNum = this.calcShowGoodsDetailNum(itemInfo.printGoodsType);
                        itemInfo.printName = this.createName(form, itemInfo);
                        itemInfo.promotionName = this.createPromotionInfo(form, itemInfo)
                        return itemInfo;
                    });
                });
                return chargeForms;
            },
            memberInfo() {
                return this.printData.memberInfo;
            },
            shebaoPayment() {
                return this.printData.shebaoPayment;
            },
            netIncomeFee() {
                return this.printData.chargeTransactions.reduce((str, cur) => str + `(${cur.payModeName})${formatMoney(cur.amount)}`, '');
            },
            invoiceQrcode() {
                return this.printData.invoiceQrcode || '';
            },
            traceCodeQrCodeUrl() {
                return this.printData.traceCodeQrCodeUrl || '';
            },
        },
        methods: {
            add,
            formatAge,
            formatMoney,
            medicalFeeGrade2PrintStr,
            isEmpty(value) {
                return value === undefined || value === null || value === '' || value === '0' || value === 0
            },
            getChargeFormType(productType, productSubType) {
                if (productType === 1 && productSubType === 2) return 'chinese';
                if (productType === 1) return 'westernMedicine';
                if (productType === 2) return 'materialGoods';
                return 'productGoods';
            },
            createName(form, item) {
                let name = '';
                if (this.feeInfo.itemNo) {
                    name += item.index ? `${item.index}.` : '';
                }
                const isExitMedicalFeeGrade = this.itemDetail[item.printGoodsType].medicalFeeGrade && item.medicalFeeGrade;
                const isExitOwnExpenseRatio = this.itemDetail[item.printGoodsType].ownExpenseRatio && item.productInfo.selfPayProp;
                if (form.printFormType === 10) {
                    name += '[赠品]';
                }
                if (isExitMedicalFeeGrade || isExitOwnExpenseRatio) {
                    name += '[';
                }
                if (isExitMedicalFeeGrade) {
                    name += this.medicalFeeGrade2PrintStr(item.medicalFeeGrade);
                }
                if (isExitOwnExpenseRatio) {
                    name += this.getSelfPayProp(item.productInfo.selfPayProp);
                }
                if (isExitMedicalFeeGrade || isExitOwnExpenseRatio) {
                    name += ']';
                }
                name += item.name;
                return name;
            },
            calcShowGoodsDetailNum(formType) {
                return (this.feeInfo.itemDetail.find(item => item.type === formType)?.items || []).reduce((pre, cur) => pre + (cur.value ? 1 : 0), 0);
            },
            getSelfPayProp(selfPayList) {
                const target = selfPayList[this.shebaoPayment?.extraInfo?.insutypeCode];
                if (target) return `${target}%`;
                const keys = Object.keys(selfPayList);
                return `${selfPayList[keys[0]]}%`
            },
            createPromotionInfo(form, item) {
                let promotionName = null;
                if (this.cashierInfo.singlePromotionFee === 2) {
                    if (form.printFormType === 10 || item.unitAdjustmentFee) {
                        return '单品优惠';
                    }
                    if (item.singlePromotionFee) {
                        const target = (item.singlePromotions || []).find(item => item.checked);
                        if (target) {
                            const {
                                hitRuleDetail = {}
                            } = target;
                            return this.transCalc2promotionStr({
                                ...hitRuleDetail,
                                displayPackageUnit: item.unit,
                                goodsId: item.productId,
                            }) || target.name;
                        }
                    }
                }
                return promotionName;
            },
            transCalc2promotionStr(data) {
                if (!data) return '';
                const {
                    type = 2, // 折扣类型 1:类型; 2:商品
                    discountWay = PromotionTypeEnum.discount,
                    ruleType = RuleTypeEnum.fixed,
                    displayPackageUnit = '盒',
                    goodsTypeName,
                    discount,
                    enoughNRuleDetails,
                    theNRuleDetail,
                    giftBySingleGoodsRuleDetails,
                    goodsId,
                } = clone(data);
                console.log('data',
                            type, 
                            discountWay,
                            ruleType,
                            displayPackageUnit,
                            goodsTypeName,
                            discount,
                            enoughNRuleDetails,
                            theNRuleDetail,
                            giftBySingleGoodsRuleDetails,
                            goodsId,
                );

                let displayUnit = '';
                if (discountWay === PromotionTypeEnum.discount) {
                    displayUnit = '折';
                } else if (discountWay === PromotionTypeEnum.special) {
                    displayUnit = '元';
                }

                // 分类折扣特殊展示
                if (
                    discountWay === PromotionTypeEnum.discount &&
                    ruleType === RuleTypeEnum.fixed &&
                    type === 1
                ) {
                    const discountValue = this.getDiscountValue(discountWay, discount);
                    return `${goodsTypeName}${discountValue}${displayUnit}`;
                }

                switch (ruleType) {
                    case RuleTypeEnum.fixed: {
                        if (!discount) return '';
                        const discountValue = this.getDiscountValue(discountWay, discount);
                        return `每${displayPackageUnit}${discountValue}${displayUnit}`;
                    }
                    case RuleTypeEnum.enoughN: {
                        const {
                            isCycle,
                            thresholdCount,
                            discountValue,
                        } = enoughNRuleDetails || {};
                        if (!discountValue) return '';
                        const _value = this.getDiscountValue(discountWay, discountValue) ;
                        let res = '';
                        if (isCycle) {
                            res += '每';
                        } else {
                            res += `买满${thresholdCount}${displayPackageUnit}，前`;
                        }
                        res += `${thresholdCount}${displayPackageUnit}${discountWay === PromotionTypeEnum.special ? '共' : ''}${_value}${displayUnit}`;
                        return res;
                    }
                    case RuleTypeEnum.theN: {
                        const {
                            isCycle,
                            thresholdCount,
                            discountValue,
                        } = theNRuleDetail || {};
                        if (!discountValue) return '';
                        const _value = this.getDiscountValue(discountWay, discountValue) ;
                        let res = '';
                        if (isCycle) {
                            res += `每买${thresholdCount}${displayPackageUnit}，`;
                        } else {
                            res += '仅';
                        }
                        res += `第${thresholdCount}${displayPackageUnit}${_value}${displayUnit}`;
                        return res;
                    }
                    case RuleTypeEnum.gift: {
                        const {
                            isCycle,
                            thresholdCount,
                            discountValue,
                            giftGoodsId,
                            giftGoodsName,
                            giftGoodsUnit,
                        } = giftBySingleGoodsRuleDetails || {};

                        if (!discountValue) return '';
                        const isSameGoods = goodsId === giftGoodsId;
                        const _value = this.getDiscountValue(discountWay, discountValue) ;
                        if (isSameGoods) {
                            if (isCycle) {
                                return `每满${thresholdCount}${displayPackageUnit}，赠${_value}${displayPackageUnit}`;
                            }
                            return `买${thresholdCount}赠${_value}`;
                        }
                        const disGiftGoods = `${giftGoodsName.slice(0,3)}${giftGoodsName.slice.length > 3 ? '...' : ''}*${_value}${giftGoodsUnit}`;
                        if (isCycle) {
                            return `每满${thresholdCount}${displayPackageUnit}，赠${disGiftGoods}`;
                        }
                        return `买满${thresholdCount}${displayPackageUnit}赠${disGiftGoods}`;
                    }
                    default:
                        return '';
                }
            },
            getDiscountValue(discountType, discountValue) {
                if (discountType === PromotionTypeEnum.discount && discountValue) {
                    discountValue *= 10;
                    discountValue = discountValue.toFixed(2);
                    discountValue = +discountValue;
                }
                return discountValue;
            }
        },
    }
</script>

<style lang="scss">
.pharmacy-cashier-wrapper {
    font-family: "Microsoft YaHei", "微软雅黑";
    font-size: 13.33px;
    line-height: 20px;
    padding: 16px 0;

    .flex-between {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .pharmacy-cashier-header {
        display: flex;
        flex-direction: column;
        align-items: center;

        .logo {
            width: 80%;
            height: auto;
        }
        .pharmacy-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1px;
            .pharmacy-name {
                font-size: 18px;
                line-height: 25px;
                font-weight: bold;
                text-align: center;
            }
        }
        .title {
            font-size: 16px;
            line-height: 20px;
            padding: 4px 0 12px;
        }
    }

    .patient-info {
        display: flex;
        flex-direction: column;
        gap: 1px;
    }

    .charge-form {
        .form-row {
            display: flex;
            justify-content: space-between;
            width: 100%;

            &.normal {
                .name {
                    overflow: hidden;
                    white-space: nowrap;
                    text-align: left;
                }

                .item-info {
                    display: inline-flex;
                    justify-content: space-between;

                    .count, .price, .total-price {
                        text-align: right;
                        min-width: 44px;
                    }
                }
            }

            &.long-name {
                flex-wrap: wrap;
                .name {
                    overflow: visible;
                    white-space: normal;
                    word-break: break-word;
                }
                .item-info {
                    display: inline-flex;
                    justify-content: space-between;
                    margin-left: auto;

                    .count, .price, .total-price {
                        text-align: right;
                        margin-left: 2px;
                        min-width: 44px;
                    }
                }
            }
        }
        .form-item + .form-item {
            margin-top: 2px;
        }

        .goods-info {
            padding-left: 12px;
            margin: 2px 0 3px;

            .goods-info-item {
                display: flex;
                > p {
                    flex: 1;
                }
            }

            .promotion-info {
                display: flex;
                justify-content: space-between;
            }
        }
    }

    .pay-info-wrapper {
        display: flex;
        flex-direction: column;
        gap: 1px;
        .pay-info {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            > span:not(:only-child):last-child {
                flex: 1;
                text-align: right;
            }
        }
        .receivableFee {
            font-size: 16px;
            font-weight: bold;
        }

        .pay-info-offset {
            padding-left: 12px;
        }
    }

    .footer {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        font-size: 20px;
        margin-bottom: 18px;

        .invoice-qrcode {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            font-size: 13px;
            > img {
                width: 96px;
                height: 96px;
            }
        }
    }
    .end-point {
        display: flex;
        justify-content: center;
        > div {
            height: 1px;
            width: 1px;
            border-top: 1px solid #000;
        }
    }
}
[data-size=page_热敏小票（58mm）] {
    .goods-info-item {
        flex-wrap: wrap;
        > p {
            width: 100%;
            flex: none !important;
        }
    }
    .footer {
        font-size: 16px !important;
    }
    .long-name {
        .item-info {
            .count, .price, .total-price {
                margin-left: 3px !important;
            }
        }
    }
}
</style>
