<template>
    <div
        class="doctor-advice"
        :class="{'is-medical-record': isMedicalRecord}"
        data-type="mix-box"
    >
        <span class="label">医嘱：</span>
        <div
            data-type="group"
            class="advice-content-print"
        >
            <div
                v-for="(adviceItem, itemIndex) in doctorAdvice"
                :key="itemIndex"
                class="doctor-advice-item"
                data-type="item"
                v-html="adviceItem"
            ></div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "Index",
        props: {
            doctorAdvice: {
                type: Array,
                default: function () {
                    return []
                }
            },
            isMedicalRecord: {
                type: Boolean,
                default: false
            }
        }
    }
</script>

<style lang="scss">
.doctor-advice.__is-split_start__ {
    .advice-content-print {
        border-top-color: #ffffff;
    }
}

.doctor-advice {
    position: relative;
    overflow: hidden;

    .label {
        position: absolute;
        top: 7pt;
        left: 0;
        width: 30pt;
        font-size: 10pt;
        line-height: 12pt;
        z-index: 1;
    }

    .advice-content-print {
        border-top: 1px dashed #000000;
        font-weight: 300;
        padding-top: 6pt;
    }

    .doctor-advice-item {
        position: relative;
        padding-left: 30pt;
        font-size: 10pt;
        line-height: 12pt;
        margin-bottom: 6pt;
        &:last-child {
            margin-bottom: 0;
        }
    }
}
</style>
