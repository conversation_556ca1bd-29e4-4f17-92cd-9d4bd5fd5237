<template>
    <div
        class="infusion-item-wrapper"
        data-type="group"
    >
        <div
            class="group-col left-col ie-group-col"
            :style="{
                width: config.infusionUsageSingleLine ? '92%' : isIvgttUnitLatin ? '75%' : '73%',
            }"
        >
            <span
                v-if="groupId"
                class="group-number-icon"
            >
                {{ NUMBER_ICONS[groupId] }}
            </span>
            <div
                v-for="(formItem, index) in groupFormItems"
                :class="{ 'first-item': !index}"
                class="infusion-group-item"
                data-type="item"
            >
                <print-row>
                    <div
                        class="item-wrapper"
                        style="display: inline-block; white-space: normal; vertical-align: top;"
                        :style="formItem.ast ? { 'width': 'calc(100% - 126px)' } : { 'width': 'calc(100% - 76px)' }"
                    >
                        <div
                            class="item-base-info"
                            :class="{'has-group-id': groupId}"
                            style="width: 100%;"
                        >
                            <div style="display: inline-block; font-size: 10pt; line-height: 12pt; word-break: break-all; word-wrap: break-word; white-space: normal;">
                                {{ formatSpec(formItem) }}
                            </div>

                            <div
                                v-if="formItem.verifySignatureStatus"
                                class="infusion-sign-name"
                            >
                                <img
                                    v-if="printData.doctorSignImgUrl"
                                    :src="printData.doctorSignImgUrl"
                                />
                                <template v-else>
                                    {{ printData.doctorName }}
                                </template>
                            </div>
                        </div>
                    </div>

                    <div
                        class="prescription-infusion-item-ast-and-dosage"
                        :style="formItem.ast ? { 'width': '126px', 'max-width': '126px', 'min-width': '126px' } : { 'width': '76px', 'max-width': '76px', 'min-width': '76px' }"
                    >
                        <div
                            v-if="formItem.ast"
                            class="group-name"
                            style="width: 50px; padding-left: 6px; text-align: right;"
                        >
                            {{ formatAstInfo(formItem) }}
                        </div>

                        <div
                            class="dosage-count"
                            style="max-width: 76px; padding-left: 6px; text-align: right;"
                        >
                            <span v-if="config.infusionUsageSingleLine">单量 </span>
                            <span :class="{'has-number-icon': groupId}">
                                <template v-if="formItem.sourceItemType === OutpatientChargeTypeEnum.NO_CHARGE || formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                                    [自备]
                                </template>
                                {{ formItem.dosage }}{{ formItem.dosageUnit }}
                            </span>
                        </div>
                    </div>
                </print-row>
                <print-row class="usage-info">
                    <print-col
                        v-if="contentConfig.westernPosition"
                        :span="12"
                        :style="{
                            'padding-left': groupId ? '24pt' : '12pt'
                        }"
                    >
                        <span v-if="formItem.productInfo && formItem.productInfo.position">{{ formItem.productInfo.position }}</span>
                    </print-col>
                    <print-col
                        :span="12"
                        style="overflow: hidden; white-space: nowrap;"
                        :style="{
                            'padding-left': groupId ? '24pt' : '12pt'
                        }"
                    >
                        <span v-if="formItem.specialRequirement">{{ formItem.specialRequirement }}</span>
                    </print-col>
                    <print-col
                        v-if="contentConfig.manufacturer"
                        :span="12"
                        class="overflow"
                        :style="{
                            'padding-left': groupId ? '24pt' : '12pt'
                        }"
                        overflow
                    >
                        <span v-if="formItem.productInfo && formItem.productInfo.manufacturer">厂家：{{ formItem.productInfo.manufacturer || '' }}</span>
                    </print-col>
                    <print-col
                        v-if="contentConfig.westernUnitPrice || contentConfig.westernTotalPrice"
                        :span="12"
                        :style="{
                            'padding-left': groupId ? '24pt' : '12pt'
                        }"
                    >
                        {{ formatPrice(formItem) }}
                    </print-col>
                </print-row>
            </div>

            <div
                class="group-line"
                :style="getGroupStyle"
            ></div>
        </div>
        <div
            class="group-col right-col"
            :style="{
                width: config.infusionUsageSingleLine ? '6%' : isIvgttUnitLatin ? '23%' : '25%',
            }"
            style="padding-left: 4px;"
        >
            <print-row v-if="config.infusionUsageSingleLine">
                <print-col
                    :span="24"
                    class="usage"
                >
                    <template v-if="contentConfig.westernMedicineDays || isExecuteType || showDays">
                        {{ currentFormItem.days ? `${currentFormItem.days}天` : '' }}
                    </template>
                </print-col>
            </print-row>
            <template v-else>
                <print-row>
                    <print-col
                        :span="24"
                        class="usage"
                    >
                        {{ freqFormat(currentFormItem.freq, contentConfig.medicalLatin) }}
                        <template v-if="contentConfig.westernMedicineDays || isExecuteType || showDays">
                            {{ currentFormItem.days ? `${currentFormItem.days}天` : '' }}
                        </template>
                    </print-col>
                </print-row>
                <print-row>
                    <print-col
                        :span="24"
                        class="usage"
                    >
                        {{ usageFormat(currentFormItem.usage, contentConfig.medicalLatin) }}
                        {{ currentFormItem.ivgtt ? currentFormItem.ivgtt : '' }}{{ !currentFormItem.ivgtt ? '' : isIvgttUnitLatin ? 'd/min' : currentFormItem.ivgttUnit }}
                    </print-col>
                </print-row>
            </template>
        </div>
        <div
            v-if="config.infusionUsageSingleLine"
            class="infusion-group-item"
            data-type="item"
        >
            <print-row>
                <print-col
                    :span="24"
                    style=" padding-left: 16pt; font-size: 10pt;"
                >
                    <p>
                        {{ contentConfig.medicalLatin ? 'Sig' : '用法' }}:
                        {{ usageFormat(currentFormItem.usage, contentConfig.medicalLatin) }}
                        {{ currentFormItem.ivgtt ? currentFormItem.ivgtt : '' }}{{ !currentFormItem.ivgtt ? '' : isIvgttUnitLatin ? 'd/min' : currentFormItem.ivgttUnit }}
                        {{ freqFormat(currentFormItem.freq, contentConfig.medicalLatin) }}
                    </p>
                </print-col>
            </print-row>
        </div>
    </div>
</template>

<script>
    import { NUMBER_ICONS } from "../../../common/constants.js";
    import { formatAstInfo, goodsSpec } from "../../../common/utils.js";
    import { usageFormat, freqFormat } from "../../../common/medical-transformat.js";
    import PrintRow from '../../layout/print-row.vue';
    import PrintCol from '../../layout/print-col.vue';
    import { OutpatientChargeTypeEnum } from "../../../constant/print-constant.js";
    import { formatAst,formatAstResult } from "../../../common/medical-transformat.js";
    import { formatMoney } from '../../../common/utils.js'

    export default {
        name: "InfusionItem",
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            formatAst,
            formatAstResult,
        },
        props: {
            printData: {
                type: Object,
                default: ()=> {
                    return {}
                },
                required: true,
            },
            groupFormItems: {
                type: Array,
                required: true,
            },
            executeFormItem: {
                type: Object,
                default: () => {
                    return null;
                },
            },
            config: Object,
            showDays: {
                type: Boolean,
                default: false,
            },
            formType: {
                type: String,
                default: 'prescription',
            },
            isIvgttUnitLatin: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                NUMBER_ICONS,
                OutpatientChargeTypeEnum,
            }
        },
        computed: {
            getGroupStyle() {
                const len = this.groupFormItems.length;
                return {
                    top: len === 1 ? '0' : '6pt',
                    height: 'calc(100% - 16pt)',
                }
            },
            contentConfig() {
                return this.config && this.config.content || {};
            },
            isExecuteType() {
                return this.formType === 'infusionExecute';
            },
            showTotalCount() {
                return !this.isExecuteType || this.config.content.medicineTotalCount;
            },
            groupId() {
                if(this.executeFormItem && this.executeFormItem.groupId) {
                    return this.executeFormItem.groupId;
                }
                if(this.groupFormItems[0] && this.groupFormItems[0].groupId) {
                    return this.groupFormItems[0].groupId;
                }
                return 0;
            },
            currentFormItem() {
                if (this.executeFormItem) return this.executeFormItem;
                return this.groupFormItems[0];
            },
        },
        methods: {
            formatAstInfo,
            formatMoney,
            freqFormat,
            usageFormat,
            showXIcon(formItem) {
                const spec = goodsSpec(formItem.productInfo);
                return spec && (this.contentConfig.westernMedicineSpec || this.contentConfig.medicineSpec)
            },
            formatSpec(formItem) {
                let str = '';
                if (this.contentConfig.medicineTradeName) {
                    str += formItem.name || '';
                } else {
                    str += formItem.medicineCadn || formItem.name || '';
                }
                if (this.contentConfig.westernMedicineSpec || this.contentConfig.medicineSpec || this.showTotalCount) {
                    str += '(';
                    if ((this.contentConfig.westernMedicineSpec || this.contentConfig.medicineSpec) && formItem.productInfo) {
                        str += goodsSpec(formItem.productInfo) || '';
                    }
                    if (this.showTotalCount) {
                        if (this.showXIcon(formItem)) {
                            str += ' ×';
                        }
                        str += (formItem.unitCount || '') + (formItem.unit || '');
                    }
                    str += ')';
                }
                return str;
            },
            formatPrice(formItem) {
                let str = '';
                if (this.contentConfig.westernUnitPrice) {
                    str += `${this.$t('currencySymbol')}${formatMoney(formItem.printUnitPrice)}/${formItem.unit}`;
                }
                if (this.contentConfig.westernUnitPrice && this.contentConfig.westernTotalPrice) {
                    str += '，';
                }
                if (this.contentConfig.westernTotalPrice) {
                    str += `共${this.$t('currencySymbol')}${formatMoney(formItem.printTotalPrice)}`;
                }
                return str;
            },
        },
    }
</script>

<style lang="scss">
    @import "index.scss";

    .infusion-item-wrapper {
        .overflow {
            overflow: hidden;
            word-break: keep-all;
            white-space: nowrap;
        }
    }

    .prescription-infusion-item-ast-and-dosage {
        display: inline-block;
        width: 98px;
        min-width: 98px;
        max-width: 98px;
        text-align: right;
        vertical-align: top;
    }
</style>
