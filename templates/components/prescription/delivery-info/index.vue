<template>
    <div
        v-if="addressDetail"
        class="delivery-info-wrapper"
        data-type="mix-box"
    >
        <span class="label">快递：</span>
        <div
            data-type="group"
            class="delivery-content"
        >
            <div
                class="delivery-info-wrapper-item"
                data-type="item"
            >
                {{ recipientInfo }}
            </div>
            <div
                class="delivery-info-wrapper-item"
                data-type="item"
            >
                {{ addressDetail }}
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "Index",
        props: {
            deliveryInfo: {
                type: Object,
            },
        },
        computed: {
            recipientInfo() {
                const {
                    deliveryCompany,
                    deliveryPayType,
                    deliveryName,
                    deliveryMobile,
                } = this.deliveryInfo;
                let str = '';
                if (deliveryCompany) {
                    str += deliveryCompany.name + '-';
                }
                str += `${deliveryPayType ? '寄付，' : '到付，'}`;
                str += (deliveryName || '') + ' ';
                str += (deliveryMobile || '');
                return str;
            },
            addressDetail() {
                if (this.deliveryInfo) {
                    const {
                        addressCityName,
                        addressDetail,
                        addressDistrictName,
                        addressProvinceName,
                    } = this.deliveryInfo;
                    let str = '';
                    str += addressProvinceName || '';
                    str += addressCityName || '';
                    str += addressDistrictName || '';
                    str += addressDetail || '';
                    return `${str}`;
                }
                return '';
            },
        }

    }
</script>

<style lang="scss">
.delivery-info-wrapper.__is-split_start__ {
    .delivery-content {
        border-top-color: #ffffff;
    }
}

.delivery-info-wrapper {
    position: relative;
    overflow: hidden;
    padding-bottom: 6pt;

    .label {
        position: absolute;
        top: 6pt;
        left: 0;
        width: 30pt;
        font-size: 10pt;
        line-height: 12pt;
        z-index: 1;
    }

    .delivery-content {
        border-top: 1px dashed #000000;
        font-weight: 300;
        padding-top: 6pt;
    }

    .delivery-info-wrapper-item {
        position: relative;
        padding-left: 30pt;
        font-size: 10pt;
        line-height: 12pt;
    }
}
</style>
