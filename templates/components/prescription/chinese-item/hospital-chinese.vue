<template>
    <print-row
        class="chinese-item"
        data-type="group"
    >
        <print-col
            v-for="item in formItems"
            :span="spanRange"
            class="chinese-col"
            data-type="item"
        >
            <print-row>
                <print-col
                    class="special-position"
                    overflow
                    :span="24"
                >
                    <span
                        v-if="contentConfig.position && item.productInfo && item.productInfo.position"
                        style="line-height: 11pt;"
                    >
                        {{ item.productInfo && item.productInfo.position }}
                    </span>
                    <span
                        v-if="item.specialRequirement && item.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE"
                        style="line-height: 11pt;"
                        :style="{'margin-left': contentConfig.position && item.productInfo && item.productInfo.position ? '0pt' : '26pt'}"
                    >
                        [{{ item.specialRequirement }}]
                    </span>
                    <span
                        v-if="item.chargeType === OutpatientChargeTypeEnum.NO_CHARGE"
                        style="line-height: 11pt;"
                        :style="{'margin-left': contentConfig.position && item.productInfo && item.productInfo.position ? '0pt' : '26pt'}"
                    >
                        【自备】
                    </span>
                </print-col>
            </print-row>

            <div
                class="chinese-name"
                overflow
                multiline
            >
                {{ item.name }}
            </div>
            <div
                v-if="contentConfig.chineseMedicineCount"
                :class="showTotalMedCount ? 'max-chinese-count' : 'chinese-count'"
                class="chinese-count"
            >
                {{ item.unitCount }}<span style="line-height: 15pt;">{{ item.unit || 'g' }}</span>
                <span v-if="showTotalMedCount">/ {{ getTotalMedCount(item.unitCount) }}<span style="line-height: 15pt;">{{ item.unit || 'g' }}</span></span>
                <div class="sign-name">
                    <span
                        v-if="item.verifySignatureStatus && printData.doctorSignImgUrl"
                        class="sign-img-list"
                    >
                        <img :src="printData.doctorSignImgUrl" />
                    </span>
                    <span
                        v-if="item.verifySignatureStatus && !printData.doctorSignImgUrl"
                        class="sign-text"
                    >
                        {{ printData.doctorName }}
                    </span>
                </div>
            </div>
        </print-col>
    </print-row>
</template>

<script>
    import PrintRow from '../../layout/print-row.vue'
    import PrintCol from '../../layout/print-col.vue'
    import { OutpatientChargeTypeEnum } from "../../../constant/print-constant.js";
    import Big from 'big.js';
    export default {
        name: "HospitalChinesePr",
        components: {
            PrintCol,
            PrintRow,
        },
        props: {
            formItems: {
                type: Array,
                required: true,
            },
            printData: {
                type: Object,
                default: ()=> {
                    return {}
                },
                required: true,
            },
            config: Object,
            span: {
                type: Number,
                default: 6
            },
            groupCount: {
                type: Number,
                default: 4
            },
            chineseForm: {
                type: Object,
                default: ()=> {
                    return {}
                }
            }
        },
        data() {
            return {
                OutpatientChargeTypeEnum
            }
        },
        computed: {
            contentConfig() {
                return this.config && this.config.content || {};
            },
            spanRange() {
                if (this.span !== 6) {
                    return this.span
                }
                if (this.groupCount !== 4 ) {
                    return 8
                }
                return this.span
            },
            showTotalMedCount() {
                return this.contentConfig.chineseMedicineCountTotal
            }
        },
        methods: {
            getTotalMedCount(count) {
                const { doseCount } = this.chineseForm
                if (!Number.isNaN(Number(count)) && doseCount && !Number.isNaN(Number(doseCount))) {
                    return Big(count).mul(Big(doseCount)).toNumber();
                }
                return 0;
            }
        }
    }
</script>

<style lang="scss">
@import "index";

.sign-name {
  position: absolute;
  top: 12pt;
  left: 1pt;
  width: 50px;
  height: 32px;
}

.chinese-item {
  .chinese-count {
    overflow: visible;
  }
}

.sign-img-list {
  top: -4pt;
  bottom: 0;
  width: 28pt;
  height: 18pt;
  text-align: left;
  outline: none;

  img {
    width: auto;
    max-width: 100%;
    height: auto;
    max-height: 100%;
    border: 0;
    border-color: #ffffff;
  }
}

.sign-text {
  position: absolute;
  left: 2pt;
  width: 47pt;
  font-size: 6pt;
  text-align: left;
}

</style>
