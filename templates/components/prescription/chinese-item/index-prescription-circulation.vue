<template>
    <print-row class="chinese-item" data-type="group">
        <print-col
            v-for="item in formItems"
            :span="6"
            class="chinese-col"
            data-type="item"
        >
            <div
                class="chinese-name"
                overflow
                multiline
                style="max-width: 67% !important"
            >
                {{ item.drugGenname }}
            </div>
            <div class="chinese-count" style="box-sizing: border-box">
                {{ item.sinDoscnt }}<span style="line-height: 15pt;">{{ item.drugDosunt || 'g' }}</span>
            </div>
        </print-col>
    </print-row>
</template>

<script>
    import PrintRow from '../../layout/print-row.vue'
    import PrintCol from '../../layout/print-col.vue'

    export default {
        name: "ChinesePr",
        components: {
            PrintCol,
            PrintRow,
        },
        props: {
            formItems: {
                type: Array,
                required: true,
            },
            config: Object,
            span: {
                type: Number,
                default: 6
            }
        }
    }
</script>

<style lang="scss">
@import "index";

.sign-name {
    position: absolute;
    top: 12pt;
    left: 1pt;
    width: 50px;
    height: 32px;
}

.chinese-item {
    .chinese-count {
        overflow: visible;
    }
}

.sign-img-list {
    top: -4pt;
    bottom: 0;
    width: 28pt;
    height: 18pt;
    text-align: left;
    outline: none;

    img {
        width: auto;
        max-width: 100%;
        height: auto;
        max-height: 100%;
        border: 0;
        border-color: #ffffff;
    }
}

.sign-text {
    position: absolute;
    left: 2pt;
    width: 47pt;
    font-size: 6pt;
    text-align: left;
}

</style>
