.chinese-item {
    .chinese-col {
        position: relative;
        height: 38pt;
        font-size: 0;
    }

    .special-requirement {
        position: absolute;
        top: -10pt;
        left: 24pt;
        font-size: 10pt;
        overflow: hidden;
        white-space: nowrap;
        font-weight: 300;
        zoom: 0.8;
    }
    .special-position {
        position: absolute;
        top: -10pt;
        left: 0;
        overflow: hidden;
        white-space: nowrap;
        font-size: 10pt;
        font-weight: 300;
        zoom: 0.8;
    }

    .chinese-name,
    .chinese-count {
        display: inline-block;
        *display: inline;
        overflow: hidden;
        font-size: 10.5pt;
        line-height: 14pt;
        word-break: break-all;
        vertical-align: text-top;
        zoom: 1;
        position: relative;
        z-index: 1;
    }

    .chinese-name {
        width: auto;
        max-width: 68pt;
        max-height: 28pt;
        overflow: hidden;
        &.long-name {
            font-size: 10pt;
        }
    }

    .chinese-count {
        width: auto;
        min-width: 14pt;
        text-align: right;
        max-width: 32%;
        padding-left: 4pt;
    }
    .max-chinese-count {
        width: auto;
        min-width: 14pt;
        text-align: right;
        max-width: 52% !important;
        padding-left: 4pt;
    }
}
