<template>
    <div class="doctor-advice-v2-wrapper">
        <div class="doctor-advice-v2-label">
            医嘱：
        </div>

        <div class="doctor-advice-v2-content-wrapper">
            <div
                v-for="(adviceItem, itemIndex) in doctorAdvice"
                :key="itemIndex"
                class="doctor-advice-v2-advice-item"
                v-html="adviceItem"
            ></div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'DoctorAdviceV2',
        props: {
            doctorAdvice: {
                type: Array,
                default: () => ([]),
            },
        },
    }
</script>

<style lang="scss">
.doctor-advice-v2-wrapper {
    display: flex;
    width: 100%;
    font-size: 13px;
    font-weight: 300;
    line-height: 16px;

    .doctor-advice-v2-content-wrapper {
        display: flex;
        flex: 1;
        flex-direction: column;
    }

    .doctor-advice-v2-advice-item {
        &:not(:first-child) {
            padding-top: 5px;
        }
    }
}
</style>
