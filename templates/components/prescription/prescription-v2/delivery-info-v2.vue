<template>
    <div class="delivery-info-v2-wrapper">
        <div class="delivery-info-v2-label">
            快递：
        </div>

        <div
            v-if="isHorizontal"
            class="delivery-info-v2-content-wrapper"
            style="flex-direction: row;"
        >
            <template v-if="recipientInfo">
                {{ recipientInfo }}
            </template>
            <template v-if="addressDetail">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ addressDetail }}
            </template>
        </div>

        <div
            v-else
            class="delivery-info-v2-content-wrapper"
        >
            <div v-if="recipientInfo">
                {{ recipientInfo }}
            </div>
            <div
                v-if="addressDetail"
                class="delivery-info-v2-address-detail"
            >
                {{ addressDetail }}
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'DeliveryInfoV2',
        props: {
            deliveryInfo: {
                type: Object,
                required: true,
            },
            isHorizontal: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            recipientInfo() {
                if (this.deliveryInfo) {
                    const {
                        deliveryCompany,
                        deliveryPayType,
                        deliveryName,
                        deliveryMobile,
                    } = this.deliveryInfo;
                    let str = '';
                    if (deliveryCompany) {
                        str += deliveryCompany.name + '-';
                    }
                    str += `${deliveryPayType ? '寄付，' : '到付，'}`;
                    str += (deliveryName || '') + ' ';
                    str += (deliveryMobile || '');
                    return str;
                }
                return '';
            },
            addressDetail() {
                if (this.deliveryInfo) {
                    const {
                        addressCityName,
                        addressDetail,
                        addressDistrictName,
                        addressProvinceName,
                    } = this.deliveryInfo;
                    let str = '';
                    str += addressProvinceName || '';
                    str += addressCityName || '';
                    str += addressDistrictName || '';
                    str += addressDetail || '';
                    return `${str}`;
                }
                return '';
            },
        },
    }
</script>

<style lang="scss">
.delivery-info-v2-wrapper {
    display: flex;
    width: 100%;
    font-size: 13px;
    font-weight: 300;
    line-height: 16px;

    .delivery-info-v2-content-wrapper {
        display: flex;
        flex: 1;
        flex-direction: column;
    }

    .delivery-info-v2-address-detail {
        padding-top: 5px;
    }
}
</style>
