<template>
    <div
        class="prescription-v2-infusion-wrapper"
        :class="!isFirst ? 'prescription-v2-infusion-wrapper-padding' : ''"
        data-type="group"
    >
        <!-- 横版 -->
        <template v-if="isHorizontal">
            <div class="prescription-v2-infusion-medicine-info-wrapper">
                <div
                    v-for="(formItem, index) in groupFormItems"
                    data-type="item"
                    class="prescription-v2-infusion-medicine-usage-content prescription-v2-infusion-horizontal-medicine-usage-content"
                >
                    <div class="prescription-v2-infusion-medicine-name">
                        <div
                            v-if="groupId"
                            :style="{ visibility: index === 0 ? 'visible' : 'hidden' }"
                        >
                            {{ NUMBER_ICONS[groupId] }}
                        </div>
                        <div class="prescription-v2-infusion-medicine-name-text">
                            {{ getItemName(formItem) }}
                        </div>
                        <!-- 双签 -->
                        <div
                            v-if="formItem.verifySignatureStatus"
                            class="prescription-v2-infusion-sign-name"
                        >
                            <img
                                v-if="isImgUrl(doctorSignImgUrl)"
                                :src="doctorSignImgUrl"
                                class="prescription-v2-infusion-sign-img"
                                alt=""
                            />
                            <template v-else>
                                {{ doctorName }}
                            </template>
                        </div>
                        <div class="prescription-v2-infusion-ast-and-dosage">
                            <div
                                v-if="formItem.ast"
                                class="prescription-v2-infusion-ast"
                            >
                                {{ formatAstInfo(formItem) }}
                            </div>
                            <div
                                v-if="config.infusionUsageSingleLine"
                                class="prescription-v2-infusion-single-dosage-text"
                            >
                                单量
                            </div>
                            <div class="prescription-v2-infusion-dosage">
                                <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                                    [自备]
                                </template>
                                {{ formItem.dosage }}{{ formItem.dosageUnit }}
                            </div>
                        </div>
                    </div>

                    <div class="prescription-v2-infusion-medicine-info">
                        <div
                            v-if="config.westernPosition && formItem.productInfo && formItem.productInfo.position"
                            class="prescription-v2-infusion-medicine-info-item"
                        >
                            <div class="prescription-v2-infusion-span">
                                {{ formItem.productInfo.position }}
                            </div>
                        </div>
                        <div
                            v-if="formItem.specialRequirement"
                            class="prescription-v2-infusion-medicine-info-item"
                        >
                            <div class="prescription-v2-infusion-span">
                                备注：{{ formItem.specialRequirement }}
                            </div>
                        </div>
                        <div
                            v-if="config.westernManufacturer && formItem.productInfo && formItem.productInfo.manufacturer"
                            class="prescription-v2-infusion-medicine-info-item"
                        >
                            <div class="prescription-v2-infusion-span">
                                {{ formItem.productInfo.manufacturer }}
                            </div>
                        </div>
                        <div
                            v-if="config.westernUnitPrice || config.westernTotalPrice"
                            class="prescription-v2-infusion-medicine-info-item"
                        >
                            <div class="prescription-v2-infusion-span">
                                <span>{{ formatPrice(formItem) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="prescription-v2-infusion-group-line"
                    :style="getGroupStyle"
                ></div>
            </div>

            <div
                class="prescription-v2-infusion-medicine-usage-wrapper"
                :style="usageStyle"
            >
                <template v-if="config.infusionUsageSingleLine">
                    <template v-if="config.westernMedicineDays">
                        {{ groupFormItems[0] && groupFormItems[0].days ? `${groupFormItems[0].days}天` : '' }}
                    </template>
                </template>
                <template v-else>
                    <div>
                        {{ freqFormat(groupFormItems[0].freq, config.westernMedicalLatin) }}
                        <template v-if="config.westernMedicineDays">
                            {{ groupFormItems[0] && groupFormItems[0].days ? `${groupFormItems[0].days}天` : '' }}
                        </template>
                    </div>

                    <div>
                        {{ usageFormat(groupFormItems[0].usage, config.westernMedicalLatin) }}
                        {{ groupFormItems[0] && groupFormItems[0].ivgtt ? groupFormItems[0].ivgtt : '' }}{{ config.westernMedicalLatin ? 'd/min' : groupFormItems[0].ivgttUnit }}
                    </div>
                </template>
            </div>

            <div
                v-if="config.infusionUsageSingleLine"
                class="prescription-v2-infusion-medicine-usage-wrapper"
                style="width: 100%; padding-top: 8px; padding-left: 18px;"
                data-type="item"
            >
                <print-row>
                    <print-col
                        :span="24"
                    >
                        <p>
                            {{ config.westernMedicalLatin ? 'Sig' : '用法' }}:
                            {{ usageFormat(groupFormItems[0].usage, config.westernMedicalLatin) }}
                            {{ groupFormItems[0].ivgtt ? groupFormItems[0].ivgtt : '' }}{{ groupFormItems[0].ivgttUnit }}
                            {{ freqFormat(groupFormItems[0].freq, config.westernMedicalLatin) }}
                        </p>
                    </print-col>
                </print-row>
            </div>
        </template>

        <!-- 竖版 -->
        <template v-else>
            <div
                class="prescription-v2-infusion-medicine-info-wrapper"
                :style="{
                    width: config.infusionUsageSingleLine ? '87%' : '77%',
                }"
            >
                <div
                    v-for="(formItem, index) in groupFormItems"
                    data-type="item"
                    class="prescription-v2-infusion-medicine-usage-content prescription-v2-infusion-vertical-medicine-usage-content"
                >
                    <div class="prescription-v2-infusion-medicine-name">
                        <div
                            v-if="groupId"
                            :style="{ visibility: index === 0 ? 'visible' : 'hidden' }"
                        >
                            {{ NUMBER_ICONS[groupId] }}
                        </div>
                        <div class="prescription-v2-infusion-medicine-name-text">
                            {{ getItemName(formItem) }}
                        </div>
                        <!-- 双签 -->
                        <div
                            v-if="formItem.verifySignatureStatus"
                            class="prescription-v2-infusion-sign-name"
                        >
                            <img
                                v-if="isImgUrl(doctorSignImgUrl)"
                                :src="doctorSignImgUrl"
                                class="prescription-v2-infusion-sign-img"
                                alt=""
                            />
                            <template v-else>
                                {{ doctorName }}
                            </template>
                        </div>
                        <div class="prescription-v2-infusion-ast-and-dosage">
                            <div
                                v-if="formItem.ast"
                                class="prescription-v2-infusion-ast"
                            >
                                {{ formatAstInfo(formItem) }}
                            </div>
                            <div
                                v-if="config.infusionUsageSingleLine"
                                class="prescription-v2-infusion-single-dosage-text"
                            >
                                单量
                            </div>
                            <div class="prescription-v2-infusion-dosage">
                                <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                                    [自备]
                                </template>
                                {{ formItem.dosage }}{{ formItem.dosageUnit }}
                            </div>
                        </div>
                    </div>

                    <div class="prescription-v2-infusion-medicine-info">
                        <div
                            v-if="config.westernPosition && formItem.productInfo && formItem.productInfo.position"
                            class="prescription-v2-infusion-medicine-info-item"
                        >
                            <div class="prescription-v2-infusion-span">
                                {{ formItem.productInfo.position }}
                            </div>
                        </div>
                        <div
                            v-if="formItem.specialRequirement"
                            class="prescription-v2-infusion-medicine-info-item"
                        >
                            <div class="prescription-v2-infusion-span">
                                备注：{{ formItem.specialRequirement }}
                            </div>
                        </div>
                        <div
                            v-if="config.westernManufacturer && formItem.productInfo && formItem.productInfo.manufacturer"
                            class="prescription-v2-infusion-medicine-info-item"
                        >
                            <div class="prescription-v2-infusion-span">
                                {{ formItem.productInfo.manufacturer }}
                            </div>
                        </div>
                        <div
                            v-if="config.westernUnitPrice || config.westernTotalPrice"
                            class="prescription-v2-infusion-medicine-info-item"
                        >
                            <div class="prescription-v2-infusion-span">
                                <span>{{ formatPrice(formItem) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div
                    class="prescription-v2-infusion-group-line"
                    :style="getGroupStyle"
                ></div>
            </div>

            <div
                class="prescription-v2-infusion-medicine-usage-wrapper"
                style="width: 23%;"
                :style="usageStyle"
            >
                <template v-if="config.infusionUsageSingleLine">
                    <template v-if="config.westernMedicineDays">
                        {{ groupFormItems[0] && groupFormItems[0].days ? `${groupFormItems[0].days}天` : '' }}
                    </template>
                </template>
                <template v-else>
                    <div>
                        {{ freqFormat(groupFormItems[0].freq, config.westernMedicalLatin) }}
                        <template v-if="config.westernMedicineDays">
                            {{ groupFormItems[0] && groupFormItems[0].days ? `${groupFormItems[0].days}天` : '' }}
                        </template>
                    </div>

                    <div>
                        {{ usageFormat(groupFormItems[0].usage, config.westernMedicalLatin) }}
                        {{ groupFormItems[0] && groupFormItems[0].ivgtt ? groupFormItems[0].ivgtt : '' }}{{ config.westernMedicalLatin ? 'd/min' : groupFormItems[0].ivgttUnit }}
                    </div>
                </template>
            </div>

            <div
                v-if="config.infusionUsageSingleLine"
                class="prescription-v2-infusion-medicine-usage-wrapper"
                style="width: 100%; padding-top: 10px; padding-left: 18px;"
                data-type="item"
            >
                <print-row>
                    <print-col
                        :span="24"
                    >
                        <p>
                            {{ config.westernMedicalLatin ? 'Sig' : '用法' }}:
                            {{ usageFormat(groupFormItems[0].usage, config.westernMedicalLatin) }}
                            {{ groupFormItems[0].ivgtt ? groupFormItems[0].ivgtt : '' }}{{ groupFormItems[0].ivgttUnit }}
                            {{ freqFormat(groupFormItems[0].freq, config.westernMedicalLatin) }}
                        </p>
                    </print-col>
                </print-row>
            </div>
        </template>
    </div>
</template>

<script>
    import { NUMBER_ICONS } from "../../../common/constants";
    import { formatAstInfo, formatGoodsDosageSpec, formatMoney, isImgUrl } from "../../../common/utils";
    import { formatAst, formatAstResult, freqFormat, usageFormat } from "../../../common/medical-transformat";
    import PrintCol from "../../layout/print-col.vue";
    import PrintRow from "../../layout/print-row.vue";
    import { OutpatientChargeTypeEnum } from '../../../constant/print-constant';

    export default {
        name: 'InfusionItemV2',
        components: { PrintRow, PrintCol },
        filters: {
            formatGoodsDosageSpec,
            formatAst,
            formatAstResult,
        },
        props: {
            isHorizontal: {
                type: Boolean,
                default: false,
            },
            config: {
                type: Object,
                required: true,
            },
            groupFormItems: {
                type: Array,
                required: true,
            },
            executeFormItem: {
                type: Object,
                default: () => null,
            },
            doctorName: {
                type: String,
                default: '',
            },
            doctorSignImgUrl: {
                type: String,
                default: '',
            },
            isFirst: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                NUMBER_ICONS,
                OutpatientChargeTypeEnum,
            }
        },
        computed: {
            groupId() {
                if (this.executeFormItem && this.executeFormItem.groupId) {
                    return this.executeFormItem.groupId;
                }
                if (this.groupFormItems[0] && this.groupFormItems[0].groupId) {
                    return this.groupFormItems[0].groupId;
                }
                return 0;
            },
            isSingleRow() {
                const len = this.groupFormItems.length;
                return len === 1 && !this.config.westernPosition && !this.config.westernManufacturer && !this.config.westernUnitPrice && !this.config.westernTotalPrice;
            },
            getGroupStyle() {
                return {
                    top: this.isSingleRow ? '5px' : '13px',
                    height: this.isSingleRow ? '16px' : 'calc(100% - 20px)',
                }
            },
            usageStyle() {
                if (this.config.infusionUsageSingleLine) {
                    return {
                        width: '13%',
                        textAlign: 'center',
                    }
                }
                let obj = {};
                if (this.isSingleRow) {
                    obj = {
                        'padding-top': 0,
                        'margin-top': '-2px',
                    }
                }
                return obj;
            },
        },
        methods: {
            formatAstInfo,
            formatMoney,
            freqFormat,
            usageFormat,
            isImgUrl,
            showXIcon(formItem) {
                const spec = formatGoodsDosageSpec(formItem.productInfo);
                const { pieceNum, pieceUnit } = formItem.productInfo || {};
                return (spec || (pieceNum && pieceUnit)) && this.config.westernMedicineSpec;
            },
            showUnitIcon(formItem) {
                const spec = formatGoodsDosageSpec(formItem.productInfo);
                const { pieceNum, pieceUnit } = formItem.productInfo || {};
                return spec && pieceNum && pieceUnit && this.config.westernMedicineSpec;
            },
            getItemName(formItem) {
                let name = '';
                if (this.config.westernMedicineTradeName) {
                    name += formItem.name || '';
                } else {
                    name += formItem.medicineCadn || formItem.name || '';
                }
                name += '(';
                if (this.config.westernMedicineSpec && formItem.productInfo) {
                    name += formatGoodsDosageSpec(formItem.productInfo);
                    if (this.showUnitIcon(formItem)) {
                        name += '*';
                    }
                    if (formItem.productInfo.pieceNum && formItem.productInfo.pieceUnit) {
                        name += formItem.productInfo.pieceNum + formItem.productInfo.pieceUnit;
                    }
                }
                if (this.showXIcon(formItem)) {
                    name += '×';
                }
                name += formItem.unitCount + formItem.unit + ')';
                return name;
            },
            formatPrice(formItem) {
                let str = '';
                if (this.config.westernUnitPrice) {
                    str += `${this.$t('currencySymbol')}${formatMoney(formItem.printUnitPrice)}/${formItem.unit}`;
                }
                if (this.config.westernUnitPrice && this.config.westernTotalPrice) {
                    str += '，';
                }
                if (this.config.westernTotalPrice) {
                    str += `共${this.$t('currencySymbol')}${formatMoney(formItem.printTotalPrice)}`;
                }
                return str;
            },
        },
    }
</script>

<style lang="scss">
.prescription-v2-infusion-wrapper {
    display: flex;
    flex-wrap: wrap;

    &.prescription-v2-infusion-wrapper-padding {
        padding-top: 18px;
    }

    .prescription-v2-infusion-medicine-info-wrapper {
        position: relative;
        width: 85%;
    }

    .prescription-v2-infusion-medicine-usage-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: calc(15% - 4px);
        padding-top: 5px;
        padding-left: 4px;
        font-size: 13px;
        font-weight: 300;
        line-height: 16px;
    }

    .prescription-v2-infusion-group-line {
        position: absolute;
        top: 0;
        right: 4px;
        width: 6px;
        height: 100%;
        min-height: 12px;
        content: '';
        border-top: 1px solid #000000;
        border-right: 1pt solid #000000;
        border-bottom: 1px solid #000000;
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
    }

    .prescription-v2-infusion-medicine-usage-content {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding-top: 5px;
        padding-right: 14px;
    }

    .prescription-v2-infusion-vertical-medicine-usage-content {
        &:not(:first-child) {
            padding-top: 10px;
        }
    }

    .prescription-v2-infusion-horizontal-medicine-usage-content {
        &:first-child {
            padding-top: 8px;
        }
    }

    .prescription-v2-infusion-medicine-name {
        display: flex;
        width: 100%;
        font-size: 13px;
        font-weight: 400;
        line-height: 16px;
    }

    .prescription-v2-infusion-medicine-name-text {
        position: relative;
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        align-items: flex-start;
        padding-left: 5px;
        font-size: 13px;
        white-space: normal;
    }

    .prescription-v2-infusion-medicine-name-item {
        font-size: 13px;
    }

    .prescription-v2-infusion-name-text {
        flex-wrap: wrap;
        white-space: normal;
    }

    .prescription-v2-infusion-sign-name {
        display: flex;
        align-items: center;
        height: 16px;
        padding-left: 5px;
        line-height: 16px;
    }

    .prescription-v2-infusion-sign-img {
        width: auto;
        height: 100%;
    }

    .prescription-v2-infusion-ast-and-dosage {
        display: flex;
        align-items: flex-start;
        justify-content: flex-end;
        width: auto;
    }

    .prescription-v2-infusion-ast {
        width: auto;
        min-width: 50px;
        padding-left: 6px;
        text-align: right;
    }

    .prescription-v2-infusion-dosage {
        width: auto;
        padding-left: 6px;
        overflow: hidden;
        white-space: nowrap;
    }

    .prescription-v2-infusion-single-dosage-text {
        width: auto;
        min-width: 34px;
        padding-left: 6px;
        overflow: hidden;
        white-space: nowrap;
    }

    .prescription-v2-infusion-medicine-info {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        padding-left: 45px;
        font-size: 13px;
        font-weight: 300;
        line-height: 16px;
    }

    .prescription-v2-infusion-medicine-info-item {
        display: flex;
        flex-wrap: nowrap;
        width: 50%;
        padding-top: 3px;
        overflow: hidden;
        white-space: nowrap;

        &:nth-child(2n+1) {
            padding-right: 8px;
        }
    }

    .prescription-v2-infusion-span {
        overflow: hidden;
        white-space: nowrap;
    }
}
</style>
