<template>
    <div
        class="prescription-v2-external-wrapper"
        data-type="mix-box"
    >
        <div class="prescription-v2-external-serial-number">
            {{ NUMBER_ICONS[serialNumber] }}
        </div>

        <div
            class="prescription-v2-external-content"
            data-type="group"
        >
            <div
                class="prescription-v2-external-name"
                data-type="item"
            >
                <div class="prescription-v2-external-name-item">
                    {{ formItem.name }}<template v-if="config.mergeExternal">
                        ({{ getUsageTypeStr(formItem) || getUsageTypeStr(externalForm) }})
                    </template> / {{ formItem.unit }}
                </div>
                <div class="prescription-v2-external-unit-item">
                    ×{{ formItem.unitCount }}
                </div>
            </div>

            <div
                class="prescription-v2-external-item-row"
                data-type="item"
            >
                <div
                    v-for="(goodsItems, goodsIndex) in getChineseGroupItems(formItem.externalGoodsItems, 4)"
                    class="prescription-v2-external-item-column"
                >
                    <div
                        v-for="(item, itemIndex) in goodsItems"
                        :key="`prescription-v2-external-item-${goodsIndex}-${itemIndex}`"
                        class="prescription-v2-external-item"
                    >
                        <span>{{ item.name }}</span>
                        <span style="padding-left: 5px;">{{ item.unitCount }}{{ item.unit || 'g' }}</span>
                    </div>
                </div>
            </div>

            <div
                v-if="formatAcupoints(formItem.acupoints)"
                data-type="item"
                class="prescription-v2-external-acupuncture-points"
                :style="{ 'padding-top': isHorizontal ? '8px' : '10px' }"
            >
                <div class="prescription-v2-external-acupuncture-points-title">
                    <template v-if="formItem.acupointUnit">
                        {{ formItem.acupointUnit === '部位' ? '部位：' : '穴位：' }}
                    </template>
                    <template v-else>
                        {{ getExternalTypeStr(form, formItem) }}
                    </template>
                </div>

                <div class="prescription-v2-external-acupuncture-points-content">
                    {{ formatAcupoints(formItem.acupoints) }}
                </div>
            </div>

            <div
                data-type="item"
                class="prescription-v2-external-acupuncture-points"
                :style="{ 'padding-top': isHorizontal ? '8px' : '10px' }"
            >
                <div class="prescription-v2-external-acupuncture-points-title">
                    用法：
                </div>

                <div
                    class="prescription-v2-external-acupuncture-points-content"
                    style="font-weight: 400;"
                >
                    <template v-if="formItem.unitCount">
                        共 {{ formItem.dosage }} 次
                    </template>
                    <template v-if="formItem.freq">
                        ，{{ formItem.freq }}
                    </template>
                    <template v-if="formItem.specialRequirement">
                        ，{{ formItem.specialRequirement }}
                    </template>
                    <template
                        v-if="
                            formItem.unitCount &&
                                formItem.dosage &&
                                formItem.acupoints &&
                                formItem.acupoints.length"
                    >
                        <tempalte v-if="formItem.acupointUnitCount && formItem.acupointUnit">
                            ，每次{{ formItem.acupointUnitCount }}{{ formItem.acupointUnit }}
                            ，共{{ formItem.dosage * formItem.acupointUnitCount }}{{ formItem.acupointUnit }}
                        </tempalte>
                        <template v-else-if="isBasedOnAcupoint(externalForm, formItem)">
                            ，每次{{ calcDosageCount(formItem) }}穴
                            ，共{{ formItem.dosage * calcDosageCount(formItem) }}穴
                        </template>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {NUMBER_ICONS} from "../../../common/constants";
    import {formatAcupoints, getChineseGroupItems, isNumber, getExternalTypeStr} from "../../../common/medical-transformat";
    import {ExternalPRUsageTypeEnumRevert, ExternalPRUsageTypeEnum} from "../../../constant/print-constant";

    export default {
        name: 'ExternalForm',
        props: {
            isHorizontal: {
                type: Boolean,
                default: false,
            },
            serialNumber: {
                type: Number,
                required: true,
            },
            form: {
                type: Object,
                required: true,
            },
            formItem: {
                type: Object,
                required: true,
            },
            usageType: {
                type: Number,
                required: true,
            },
            usageSubType: {
                type: Number,
                required: true,
            },
            config: {
                type: Object,
                default() {
                    return {};
                }
            },
            externalForm: {
                type: Object,
                default() {
                    return {};
                }
            },
            getUsageTypeStr: {
                type: Function,
                required: true,
            }
        },
        data() {
            return {
                NUMBER_ICONS,
                ExternalPRUsageTypeEnumRevert,
            }
        },
        methods: {
            getChineseGroupItems,
            formatAcupoints,
            getExternalTypeStr,
            calcDosageCount(item) {
                if (item.acupointUnitCount && item.acupointUnit) {
                    return item.acupointUnitCount;
                }
                let count = 0;
                (item.acupoints || []).forEach((it) => {
                    if (it.name) {
                        if (isNumber(it.position)) {
                            count += parseFloat(it.position);
                        } else if (it.position === '双') {
                            count += 2;
                        } else if (it.position !== '-') {
                            count++;
                        }
                    }
                });
                return count || 1;
            },
            isBasedOnAcupoint(item) {
                return [
                    ExternalPRUsageTypeEnum.tieFu,
                    ExternalPRUsageTypeEnum.zhenCi,
                    ExternalPRUsageTypeEnum.aiJiu,
                ].includes(item.usageType ?? this.form.usageType);
            },
        },
    }
</script>

<style lang="scss">
.prescription-v2-external-wrapper {
    display: flex;
    align-items: flex-start;
    width: 100%;
    padding-top: 5px;

    .prescription-v2-external-serial-number {
        padding-right: 5px;
        font-size: 13px;
        font-weight: 400;
        line-height: normal;
    }

    .prescription-v2-external-content {
        flex: 1;
        width: 100%;
    }

    .prescription-v2-external-name {
        display: flex;
        align-items: flex-start;
        width: 100%;
        font-size: 13px;
        font-weight: 400;
        line-height: normal;
    }

    .prescription-v2-external-name-item {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        width: 75%;
        max-width: 75%;
        padding-right: 8px;
        word-break: break-all;
        word-wrap: break-word;
    }

    .prescription-v2-external-unit-item {
        display: flex;
        flex-wrap: wrap;
        width: 25%;
        max-width: 25%;
        word-break: break-all;
        word-wrap: break-word;
    }

    .prescription-v2-external-item-row {
        display: flex;
        flex-direction: column;
        width: 100%;
        font-size: 13px;
        font-weight: 300;
        line-height: normal;
    }

    .prescription-v2-external-item-column {
        display: flex;
        width: 100%;
        padding-top: 2px;
    }

    .prescription-v2-external-item {
        display: flex;
        width: 25%;
        max-height: 36px;
        overflow: hidden;
        line-height: 18px;

        &:not(:last-child) {
            padding-right: 5px;
        }
    }

    .prescription-v2-external-acupuncture-points {
        display: flex;
        align-items: flex-start;
    }

    .prescription-v2-external-acupuncture-points-title {
        font-size: 13px;
        font-weight: 400;
        line-height: normal;
    }

    .prescription-v2-external-acupuncture-points-content {
        flex: 1;
        font-size: 13px;
        font-weight: 300;
        line-height: normal;
    }
}
</style>
