<template>
    <div class="prescription-v2-western-wrapper">
        <!-- 横版 -->
        <template v-if="isHorizontal">
            <div class="prescription-v2-western-item-wrapper prescription-v2-western-item-wrapper-horizontal">
                <div class="prescription-v2-western-item">
                    <div class="prescription-v2-western-name-wrapper">
                        <div
                            class="prescription-v2-western-group-icon"
                            :style="{ visibility: formItem.showGroupId && showGroupIcon ? 'visible' : 'hidden' }"
                        >
                            {{ formItem.groupId ? NUMBER_ICONS[formItem.groupId] : '-' }}
                        </div>
                        <div class="prescription-v2-western-name">
                            <template v-if="config.westernMedicineTradeName">
                                {{ formItem.name }}
                            </template>
                            <template v-else>
                                {{ formItem.medicineCadn || formItem.name }}
                            </template>
                        </div>
                    </div>

                    <!-- 双签 -->
                    <div
                        v-if="formItem.verifySignatureStatus"
                        class="prescription-v2-western-sign-name"
                    >
                        <img
                            v-if="isImgUrl(doctorSignImgUrl)"
                            :src="doctorSignImgUrl"
                            class="prescription-v2-western-sign-img"
                            alt=""
                        />
                        <template v-else>
                            {{ doctorName }}
                        </template>
                    </div>

                    <div
                        v-if="config.westernMedicineSpec"
                        class="prescription-v2-western-info-wrapper prescription-v2-western-landscape-info-spec"
                    >
                        {{ goodsSpec(productInfo, showPackageUnit) }}
                    </div>
                    <div class="prescription-v2-western-info-wrapper prescription-v2-western-landscape-info-unit-count">
                        <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                            [自备]
                        </template><template v-else>
                            ×
                        </template>{{ formItem.unitCount }}{{ formItem.unit }}
                    </div>
                    <div
                        v-if="config.westernMedicineDays && formItem.days"
                        class="prescription-v2-western-info-wrapper prescription-v2-western-landscape-info-days"
                    >
                        {{ formItem.days }}天
                    </div>
                    <div class="prescription-v2-western-info-wrapper prescription-v2-western-landscape-info-usage">
                        用法：每次{{ formItem.dosage }}{{ formItem.dosageUnit }}，{{ freqFormat( formItem.freq, config.westernMedicalLatin ) }}，{{ usageFormat(formItem.usage, config.westernMedicalLatin) }}
                    </div>
                </div>

                <div class="prescription-v2-western-landscape-usage-wrapper">
                    <template v-if="config.westernManufacturer || (config.westernPosition && productInfo.position)">
                        <template v-if="config.westernManufacturer && productInfo.manufacturer">
                            {{ productInfo.manufacturer }}
                        </template>
                        <template v-if="config.westernManufacturer && productInfo.manufacturer && config.westernPosition && productInfo.position">
                            /
                        </template>
                        <template v-if="!(config.westernManufacturer && productInfo.manufacturer) && config.westernPosition && productInfo.position">
                            柜号：
                        </template>
                        <template v-if="config.westernPosition && productInfo.position">
                            {{ productInfo.position }}
                        </template>
                    </template>
                    <template v-if="((config.westernManufacturer && productInfo.manufacturer) || (config.westernPosition && productInfo.position)) && (config.westernUnitPrice || config.westernTotalPrice)">
                        /
                    </template>
                    <template v-if="config.westernUnitPrice || config.westernTotalPrice">
                        <template v-if="config.westernUnitPrice">
                            单价：{{ formatMoney(formItem.printUnitPrice) }}
                        </template>
                        <template v-if="config.westernUnitPrice && config.westernTotalPrice">
                            /
                        </template>
                        <template v-if="config.westernTotalPrice">
                            小计：{{ formatMoney(formItem.printTotalPrice) }}
                        </template>
                    </template>
                    <template v-if="formItem.specialRequirement">
                        /
                        备注：{{ formItem.specialRequirement }}
                    </template>
                </div>
            </div>
        </template>

        <!-- 竖版 -->
        <template v-else>
            <div class="prescription-v2-western-item-wrapper prescription-v2-western-item-wrapper-vertical">
                <div class="prescription-v2-western-item">
                    <div class="prescription-v2-western-name-wrapper">
                        <div
                            class="prescription-v2-western-group-icon"
                            :style="{ visibility: formItem.showGroupId && showGroupIcon ? 'visible' : 'hidden' }"
                        >
                            {{ formItem.groupId ? NUMBER_ICONS[formItem.groupId] : '-' }}
                        </div>
                        <div class="prescription-v2-western-name">
                            <template v-if="config.westernMedicineTradeName">
                                {{ formItem.name }}
                            </template>
                            <template v-else>
                                {{ formItem.medicineCadn || formItem.name }}
                            </template>
                        </div>
                    </div>

                    <!-- 双签 -->
                    <div
                        v-if="formItem.verifySignatureStatus"
                        class="prescription-v2-western-sign-name"
                    >
                        <img
                            v-if="isImgUrl(doctorSignImgUrl)"
                            :src="doctorSignImgUrl"
                            class="prescription-v2-western-sign-img"
                            alt=""
                        />
                        <template v-else>
                            {{ doctorName }}
                        </template>
                    </div>

                    <div
                        v-if="config.westernMedicineSpec"
                        class="prescription-v2-western-info-wrapper prescription-v2-western-info-spec"
                    >
                        {{ goodsSpec(productInfo, showPackageUnit) }}
                    </div>
                    <div
                        class="prescription-v2-western-info-wrapper prescription-v2-western-info-unit-count"
                        :style="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE ? { width: '57px', minWidth: '57px' } : {}"
                    >
                        <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                            [自备]
                        </template><template v-else>
                            ×
                        </template>{{ formItem.unitCount }}{{ formItem.unit }}
                    </div>
                    <div
                        v-if="config.westernMedicineDays && formItem.days"
                        class="prescription-v2-western-info-wrapper prescription-v2-western-info-days"
                    >
                        {{ formItem.days }}天
                    </div>
                </div>

                <div class="prescription-v2-western-usage-wrapper">
                    <div
                        class="prescription-v2-western-usage-info prescription-v2-western-usage-info-special"
                        :style="`${formItem.specialRequirement ? 'width: 100%' : ''}`"
                    >
                        <div class="prescription-v2-western-span">
                            {{ formatUsageInfo(formItem) }}
                        </div>
                    </div>
                    <div
                        v-if="(config.westernManufacturer && productInfo.manufacturer) || (config.westernPosition && productInfo.position)"
                        class="prescription-v2-western-usage-info"
                        :class="calcManufacturerClass"
                    >
                        <div class="prescription-v2-western-span">
                            <template v-if="config.westernManufacturer && productInfo.manufacturer">
                                {{ productInfo.manufacturer }}
                            </template>
                            <template v-if="config.westernManufacturer && productInfo.manufacturer && config.westernPosition && productInfo.position">
                                /
                            </template>
                            <template v-if="!(config.westernManufacturer && productInfo.manufacturer) && config.westernPosition && productInfo.position">
                                柜号：
                            </template>
                            <template v-if="config.westernPosition && productInfo.position">
                                {{ productInfo.position }}
                            </template>
                        </div>
                    </div>
                    <div
                        v-if="config.westernUnitPrice || config.westernTotalPrice"
                        class="prescription-v2-western-usage-info"
                        :class="calcPriceClass"
                    >
                        <div class="prescription-v2-western-span">
                            <template v-if="config.westernUnitPrice">
                                单价：{{ formatMoney(formItem.printUnitPrice) }}
                            </template>
                            <template v-if="config.westernUnitPrice && config.westernTotalPrice">
                                /
                            </template>
                            <template v-if="config.westernTotalPrice">
                                小计：{{ formatMoney(formItem.printTotalPrice) }}
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
    import { NUMBER_ICONS } from "../../../common/constants";
    import { formatMoney, goodsSpec, isImgUrl } from "../../../common/utils";
    import { OutpatientChargeTypeEnum } from "../../../constant/print-constant";
    import { freqFormat, usageFormat } from "../../../common/medical-transformat";

    export default {
        name: 'WesternItemV2',
        props: {
            isHorizontal: {
                type: Boolean,
                default: false,
            },
            config: {
                type: Object,
                required: true,
            },
            formItem: {
                type: Object,
                required: true,
            },
            showGroupIcon: {
                type: Boolean,
                default: false,
            },
            showPackageUnit: {
                type: Boolean,
                default: true,
            },
            doctorName: {
                type: String,
                default: '',
            },
            doctorSignImgUrl: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                NUMBER_ICONS,
                OutpatientChargeTypeEnum,
            }
        },
        computed: {
            productInfo() {
                return this.formItem.productInfo || {};
            },
            calcManufacturerClass() {
                if (this.formItem.specialRequirement) return 'prescription-v2-western-usage-info-special';
                return '';
            },
            calcPriceClass() {
                const showUsage = !!this.formItem.specialRequirement;
                const showManufacturer = (this.config.westernManufacturer && this.productInfo.manufacturer) || (this.config.westernPosition && this.productInfo.position);
                if (!showUsage && showManufacturer) {
                    return 'prescription-v2-western-usage-info-special';
                }
                if (showUsage && !showManufacturer) {
                    return 'prescription-v2-western-usage-info-special';
                }
                return '';
            },
        },
        methods: {
            goodsSpec,
            freqFormat,
            usageFormat,
            formatMoney,
            isImgUrl,
            formatUsageInfo(formItem) {
                let str = '';
                if (this.config.westernMedicalLatin) {
                    str += 'Sig：';
                } else {
                    str += '用法：';
                }
                str += `每次${formItem.dosage || ''}${formItem.dosageUnit || ''}，${freqFormat(formItem.freq, this.config.westernMedicalLatin)}，${usageFormat(formItem.usage, this.config.westernMedicalLatin)}`;
                if (formItem.specialRequirement) {
                    str += `，${formItem.specialRequirement}`;
                }
                return str;
            },
        },
    }
</script>

<style lang="scss">
.prescription-v2-western-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    font-size: 13px;
    line-height: 16px;

    .prescription-v2-western-item-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        max-width: 100%;
    }

    .prescription-v2-western-item {
        display: flex;
        align-items: flex-start;
        width: 100%;
        font-weight: 400;
    }

    .prescription-v2-western-name-wrapper {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        align-items: flex-start;
        white-space: normal;
    }

    .prescription-v2-western-group-icon {
    }

    .prescription-v2-western-name {
        flex: 1;
        flex-wrap: wrap;
        padding: 0 0 0 5px;
        white-space: normal;
    }

    .prescription-v2-western-info-wrapper {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        padding-left: 5px;
        overflow: hidden;
        white-space: nowrap;
    }

    .prescription-v2-western-sign-name {
        display: flex;
        align-items: center;
        height: 16px;
        padding-left: 5px;
        line-height: 16px;
    }

    .prescription-v2-western-sign-img {
        width: auto;
        height: 100%;
    }

    .prescription-v2-western-info-spec {
        min-width: 118px;
        padding-left: 16px !important;
        justify-content: flex-end;
    }

    .prescription-v2-western-landscape-info-spec {
        min-width: 125px;
        padding-left: 16px !important;
        justify-content: flex-end;
    }

    .prescription-v2-western-landscape-info-usage {
        width: 220px;
        min-width: 220px;
        margin-left: 22px;
    }

    .prescription-v2-western-info-unit-count {
        justify-content: flex-end;
        width: 53px;
        min-width: 53px;
    }

    .prescription-v2-western-landscape-info-unit-count {
        justify-content: flex-end;
        width: 58px;
        min-width: 58px;
    }

    .prescription-v2-western-info-days {
        justify-content: flex-end;
        width: 53px;
        min-width: 53px;
    }

    .prescription-v2-western-landscape-info-days {
        justify-content: flex-end;
        width: 52px;
        min-width: 52px;
    }

    .prescription-v2-western-usage-wrapper {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        font-size: 13px;
        font-weight: 300;
        line-height: 16px;
    }

    .prescription-v2-western-landscape-usage-wrapper {
        display: flex;
        flex-wrap: nowrap;
        width: 100%;
        padding: 3px 0 0 45px;
        overflow: hidden;
        font-size: 13px;
        font-weight: 300;
        line-height: 16px;
        white-space: nowrap;
    }

    .prescription-v2-western-usage-info {
        flex-wrap: nowrap;
        width: 45%;
        padding-top: 3px;
        overflow: hidden;
        white-space: nowrap;
    }

    .prescription-v2-western-usage-info-special {
        width: 55%;
        padding: 3px 8px 0 45px;
    }

    .prescription-v2-western-landscape-usage-info {
        width: 33%;
    }

    .prescription-v2-western-landscape-manufacturer-info {
        flex: 1;
    }

    .prescription-v2-western-span {
        overflow: hidden;
        white-space: nowrap;
    }
}
</style>
