<template>
    <div class="prescription-v2-chinese-usage-wrapper">
        <div class="prescription-v2-chinese-usage-title">
            用法：
        </div>
        
        <div
            v-if="isHorizontal"
            class="prescription-v2-chinese-landscape-usage-info-wrapper"
        >
            <span v-html="chineseFormUsageTotalInfo"></span>
            <span v-if="chineseFormUsageInfo">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <span
                v-if="chineseFormUsageInfo"
                v-html="chineseFormUsageInfo"
            ></span>
        </div>

        <div
            v-else
            class="prescription-v2-chinese-usage-info-wrapper"
        >
            <div class="prescription-v2-chinese-usage-total">
                <span v-html="chineseFormUsageTotalInfo"></span>
            </div>

            <div
                v-if="chineseFormUsageInfo"
                class="prescription-v2-chinese-usage-info"
            >
                <span v-html="chineseFormUsageInfo"></span>
            </div>
        </div>
    </div>
</template>

<script>
    import {doseTotal} from "../../../common/medical-transformat";
    import {specialUsages} from "../../../common/constants";

    export default {
        name: 'ChineseUsage',
        props: {
            form: {
                type: Object,
                required: true,
            },
            chineseMedicineTotalCount: {
                type: Number,
                default: 0,
            },
            isHorizontal: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            chineseFormUsageTotalInfo() {
                return this.getChineseFormUsage(this.form).totalInfo; 
            },
            chineseFormUsageInfo() {
                return this.getChineseFormUsage(this.form).usageInfo;
            },
        },
        methods: {
            // 获取中药处方用法
            getChineseFormUsage(form) {
                let usageStr = '';
                let usageArray = [];
                if(form.usage) {
                    usageArray.push(`<span style="font-weight: bold;">${form.usage}</span>`);
                }
                if(form.dailyDosage) {
                    usageArray.push(form.dailyDosage);
                }
                if(form.freq) {
                    usageArray.push(form.freq)
                }
                if(form.usageLevel) {
                    usageArray.push(form.usageLevel)
                }
                if(form.usageDays && specialUsages.includes(form.usage)) {
                    usageArray.push(form.usageDays);
                }
                if(form.requirement) {
                    usageArray.push(form.requirement);
                }
                usageStr = usageArray.join('，')

                let _str = '';
                _str += `共 <span style="font-weight: bold;">${form.doseCount || ''}</span> 剂`;
                if(this.chineseMedicineTotalCount) {
                    _str += `，${doseTotal(form.prescriptionFormItems).kind} 味`;
                    if (Number(doseTotal(form.prescriptionFormItems).count)) {
                        _str += `，单剂 ${
                            doseTotal(form.prescriptionFormItems).count
                        } g，总重 ${
                            (doseTotal(form.prescriptionFormItems).count * form.doseCount).toFixed(2)
                        } g`
                    } else {
                        _str += `，${usageStr}`;
                        usageStr = '';
                    }
                } else {
                    _str += `，${usageStr}`;
                    usageStr = '';
                }

                return {
                    totalInfo: _str,
                    usageInfo: usageStr ,
                }
            },
        },
    }
</script>

<style lang="scss">
.prescription-v2-chinese-usage-wrapper {
    display: flex;
    width: 100%;
    padding-top: 16px;
    font-size: 13px;
    font-weight: 400;
    line-height: 16px;

    .prescription-v2-chinese-usage-title {
    }

    .prescription-v2-chinese-usage-info-wrapper {
        display: flex;
        flex: 1;
        flex-direction: column;
    }

    .prescription-v2-chinese-landscape-usage-info-wrapper {
        display: flex;
        flex: 1;
    }

    .prescription-v2-chinese-usage-total {
    }

    .prescription-v2-chinese-usage-info {
        padding-top: 4px;
    }
}
</style>
