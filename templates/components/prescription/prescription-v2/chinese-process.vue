<template>
    <div class="prescription-v2-chinese-process-wrapper">
        <div class="prescription-v2-chinese-process-title">
            加工：
        </div>

        <div class="prescription-v2-chinese-process-info">
            <span>{{ processInfoStr }}</span>
            <span v-if="processRemark">，{{ processRemark }}</span>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'ChineseProcess',
        props: {
            processInfoStr: {
                type: String,
                default: '',
            },
            processRemark: {
                type: String,
                default: '',
            },
        },
    }
</script>

<style lang="scss">
.prescription-v2-chinese-process-wrapper {
    display: flex;
    width: 100%;
    font-size: 13px;
    font-weight: 300;
    line-height: 16px;

    .prescription-v2-chinese-process-title {
    }

    .prescription-v2-chinese-process-info {
        flex: 1;
    }
}
</style>
