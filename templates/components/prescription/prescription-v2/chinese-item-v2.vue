<template>
    <div
        class="prescription-v2-chinese-wrapper"
        data-type="group"
    >
        <div
            v-for="item in formItems"
            :key="item.id"
            data-type="item"
            class="prescription-v2-chinese-item-wrapper"
            :style="{ width: itemWidth }"
        >
            <div class="prescription-v2-chinese-item-info">
                <div
                    v-if="config.chinesePosition && item.productInfo && item.productInfo.position"
                    class="prescription-v2-chinese-item-info-item"
                >
                    {{ item.productInfo.position }}
                </div>
                <div
                    v-if="item.specialRequirement && item.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE"
                    class="prescription-v2-chinese-item-info-item"
                    :class="!(config.chinesePosition && item.productInfo && item.productInfo.position) && !(config.chineseUnitPrice || config.chineseTotalPrice) ? 'prescription-v2-chinese-item-spec' : ''"
                >
                    [{{ item.specialRequirement }}]
                </div>
                <div
                    v-if="item.chargeType === OutpatientChargeTypeEnum.NO_CHARGE"
                    class="prescription-v2-chinese-item-info-item"
                    :class="!(config.chinesePosition && item.productInfo && item.productInfo.position) && !(config.chineseUnitPrice || config.chineseTotalPrice) ? 'prescription-v2-chinese-item-spec' : ''"
                >
                    【自备】
                </div>
                <div v-if="config.chineseUnitPrice || config.chineseTotalPrice">
                    <span v-if="config.chineseUnitPrice">{{ `${$t('currencySymbol')}${formatMoney(item.printUnitPrice)}/${item.unit}` }}</span>
                    <span v-if="config.chineseUnitPrice && config.chineseTotalPrice">,</span>
                    <span v-if="config.chineseTotalPrice">{{ `共${$t('currencySymbol')}${formatMoney(item.printTotalPrice)}` }}</span>
                </div>
            </div>

            <div
                class="prescription-v2-chinese-item-name"
                :style="{ 'min-height': showSignatureStatus ? '32px' : '26px' }"
            >
                <span
                    class="prescription-v2-chinese-item-name-text"
                    :style="{ 'max-width': config.chineseMedicineCount ? '57%' : '100%', 'min-height': showSignatureStatus ? '32px' : '26px' }"
                    overflow
                    multiline
                >
                    {{ item.name }}
                </span>
                <span
                    v-if="config.chineseMedicineCount || item.verifySignatureStatus"
                    class="prescription-v2-chinese-item-unit-count"
                >
                    <template v-if="config.chineseMedicineCount">
                        <span>{{ item.unitCount }}{{ item.unit || 'g' }}</span>
                        <span v-if="config.chineseMedicineCountTotal">/ {{ getTotalMedCount(item.unitCount) }}{{ item.unit || 'g' }}</span>
                    </template>

                    <!-- 双签 -->
                    <div
                        v-if="item.verifySignatureStatus"
                        class="prescription-v2-chinese-sign-name"
                        :style="{ paddingTop: !isImgUrl(doctorSignImgUrl) ? '4px' : '' }"
                    >
                        <img
                            v-if="isImgUrl(doctorSignImgUrl)"
                            :src="doctorSignImgUrl"
                            class="prescription-v2-chinese-sign-img"
                            alt=""
                        />
                        <template v-else>
                            {{ doctorName }}
                        </template>
                    </div>
                </span>
            </div>
        </div>
    </div>
</template>

<script>
    import { OutpatientChargeTypeEnum } from "../../../constant/print-constant";
    import { formatMoney, isImgUrl } from "../../../common/utils";
    import Big from 'big.js';

    export default {
        name: 'ChineseItemV2',
        props: {
            isHorizontal: {
                type: Boolean,
                default: false,
            },
            config: {
                type: Object,
                required: true,
            },
            formItems: {
                type: Array,
                required: true,
            },
            chineseForm: {
                type: Object,
                default: () => ({}),
            },
            groupCount: {
                type: Number,
                default: 3,
            },
            doctorName: {
                type: String,
                default: '',
            },
            doctorSignImgUrl: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                OutpatientChargeTypeEnum,
            }
        },
        computed: {
            rowCount() {
                if (this.isHorizontal) return 4;
                return this.groupCount;
            },
            itemWidth() {
                return `${Math.floor(100 / this.rowCount)}%`;
            },
            showSignatureStatus() {
                return this.formItems.some((it) => it.verifySignatureStatus);
            },
        },
        methods: {
            formatMoney,
            isImgUrl,
            getTotalMedCount(count) {
                const { doseCount } = this.chineseForm;
                if (!Number.isNaN(Number(count)) && doseCount && !Number.isNaN(Number(doseCount))) {
                    return Big(count).mul(Big(doseCount)).toNumber();
                }
                return 0;
            },
        },
    }
</script>

<style lang="scss">
.prescription-v2-chinese-wrapper {
    display: flex;
    width: 100%;

    &:first-child {
        .prescription-v2-chinese-item-wrapper {
            padding-top: 5px;
        }
    }

    .prescription-v2-chinese-item-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        padding-bottom: 2px;
    }

    .prescription-v2-chinese-item-info {
        display: flex;
        flex-wrap: nowrap;
        width: 100%;
        min-height: 13px;
        overflow: hidden;
        font-size: 10px;
        font-weight: 300;
        line-height: 13px;
        white-space: nowrap;
    }

    .prescription-v2-chinese-item-info-item {
        margin-right: 4px;
    }

    .prescription-v2-chinese-item-spec {
        padding-left: 30px;
    }

    .prescription-v2-chinese-sign-name {
        position: absolute;
        top: 11px;
        left: 0;
        display: flex;
        justify-items: flex-end;
        width: 50px;
        height: 32px;
        max-height: 32px;
        overflow: hidden;
        line-height: 16px;
    }

    .prescription-v2-chinese-sign-img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .prescription-v2-chinese-item-name {
        display: flex;
        align-items: flex-start;
        height: auto;
        max-height: 32px;
        font-size: 14px;
        font-weight: 400;
        line-height: 16px;
    }

    .prescription-v2-chinese-item-name-text {
        height: auto;
        max-height: 32px;
        overflow: hidden;
    }

    .prescription-v2-chinese-item-unit-count {
        position: relative;
        padding-left: 5px;
        font-size: 13px;
    }
}
</style>
