import clone from "../../../common/clone";
import {PharmacyTypeEnum} from "../../../common/constants";
import {
    checkExistedGroupId,
    getChineseGroupItems,
    getDoctorAdviceArray,
    getInfusionGroupItems
} from "../../../common/medical-transformat";

export default {
    props:{
        injectConfig: {
            type: Object,
            default() {
                return {};
            },
        },
    },
    computed: {
        doctorAdvice() {
            return getDoctorAdviceArray(this.printData.doctorAdvice);
        },
        getUsageStrByScopeId() {
            return this.injectConfig && this.injectConfig.getUsageStrByScopeId
        },
        getMedicineStateStrByScopeId() {
            return this.injectConfig && this.injectConfig.getMedicineStateStrByScopeId
        },
        UsageScopeIdEnum() {
            return this.injectConfig && this.injectConfig.UsageScopeIdEnum
        },
    },
    methods: {
        checkExistedGroupId,
        getInfusionGroupItems,
        getChineseGroupItems,
        groupMedicine( prescriptionFormItems ) {
            let _group = {};
            let tempFormItems = clone(prescriptionFormItems)
            let noGroupIdItems = [];
            //分组
            tempFormItems.forEach( ( medicine ) => {
                if(!medicine.groupId) {
                    medicine.showGroupId = true;
                    noGroupIdItems.push(medicine);
                } else {
                    if (!(_group[ Number( medicine.groupId ) ] instanceof Array)) {
                        _group[ Number( medicine.groupId ) ] = [];
                    }
                    medicine.showGroupId = !_group[Number(medicine.groupId)].length;
                    _group[ Number( medicine.groupId ) ].push(  medicine );
                }

            } );
            let res = [];
            for( let item in _group) {
                res = res.concat(_group[item])
            }
            return res.concat(noGroupIdItems);
        },
        splitFormItems(formItems, count = 5, isRepeat = true) {
            let tempFormItems = clone(formItems);
            let len = formItems.length;
            let res = [];
            if(isRepeat) {
                // 不考虑去重，直接按照数量切分
                let index = 0;
                while(index < len) {
                    let tempItems = tempFormItems.slice(index, index += count);
                    res.push(tempItems);
                }
            } else {
                let groupSet = new Set();
                let group = [];
                tempFormItems.forEach( item => {
                    groupSet.add(item.goodsId);
                    if(groupSet.size < 6) {
                        group.push(item);
                    } else {
                        res.push(group);
                        groupSet.clear();
                        groupSet.add(item.goodsId);
                        group = [];
                        group.push(item);
                    }
                })
                if(group && group.length) {
                    res.push(group);
                }
            }
            return res;
        },
        transStandardForm() {
            const wsForms = clone(this.renderData.printData.prescriptionWesternForms) || [];
            const inForms = clone(this.renderData.printData.prescriptionInfusionForms) || [];
            let resWsForm = [];
            let resInForm = [];
            wsForms.forEach( form => {
                let tempForm = clone(form);
                tempForm.prescriptionFormItems = [];
                let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5,false);
                resWsForm.push({
                    ...tempForm,
                    isStandardForm: true,
                    prescriptionFormItems: formItemsGroups
                })
            })
            inForms.forEach( form => {
                let tempForm = clone(form);
                tempForm.prescriptionFormItems = [];
                let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5, false);
                resInForm.push({
                    ...tempForm,
                    isStandardForm: true,
                    prescriptionFormItems: formItemsGroups
                })

            })
            return {
                prescriptionWesternForms: resWsForm,
                prescriptionInfusionForms: resInForm,
            }
        },
        // 输注 & 中西成药处方，如果一个处方上的药来自多个药房，处方单上不显示药房名
        calcCurrentPrescriptionShowPharmacyName(form) {
            return !(form.prescriptionFormItems?.find(item => item.pharmacyName !== form.pharmacyName));
        },
        getPharmacyName(form) {
            const {
                pharmacyType,
                pharmacyName,
            } = form;
            if (pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) return '';
            return pharmacyName
        },
        getProcessInfoStr(form) {
            const {
                isDecoction,
                pharmacyType,
                processBagUnitCount = 1,
                totalProcessCount,
                usageType,
                usageSubType,
                usageScopeId,
                medicineStateScopeId,
                processBagUnit,
            } = form;
            if(!isDecoction) return '';
            if (pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                const usageTypeInfo = (this.printData.usageTypes || []).find((it) => it.type === usageType);
                if (!usageTypeInfo) return '';

                const _arr = [];

                const usageSubTypeInfo = usageTypeInfo.children.find((it) => it.subType === usageSubType);
                if (usageSubTypeInfo) {
                    _arr.push(usageSubTypeInfo.name);
                } else {
                    _arr.push(usageTypeInfo.name);
                }
                // 加工方式为煎药
                if (usageType === 1) {
                    if (processBagUnitCount) {
                        _arr.push(`1剂煎 ${processBagUnitCount} 袋`);
                    }
                    if (totalProcessCount) {
                        _arr.push(`共 ${totalProcessCount} 袋`);
                    }
                }

                return _arr.join('，');
            }

            if (pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                const _arr = ['煎药'];
                if (processBagUnitCount) {
                    _arr.push(`1剂煎 ${processBagUnitCount} 袋`);
                }
                if (totalProcessCount) {
                    _arr.push(`共 ${totalProcessCount} 袋`);
                }
                return _arr.join('，');
            }

            if (pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                const _arr = [this.getUsageStrByScopeId(usageScopeId)];
                if (usageScopeId === this.UsageScopeIdEnum.jianYao) {
                    if (processBagUnitCount) {
                        _arr.push(`1剂煎 ${processBagUnitCount} 袋`);
                    }
                    if (totalProcessCount) {
                        _arr.push(`共 ${totalProcessCount} 袋`);
                    }
                } else if (usageScopeId === this.UsageScopeIdEnum.keLi) {
                    const unit = processBagUnit || '袋';
                    _arr.push(`1剂调 ${processBagUnitCount} ${unit}`);
                } else {
                    _arr.push(`（${this.getMedicineStateStrByScopeId(medicineStateScopeId)}）`);
                }
                return _arr.join('，');
            }

            return '';
        },
    },
}
