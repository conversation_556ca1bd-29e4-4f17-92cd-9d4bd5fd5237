<template>
    <div
        class="western-item"
        :class="{'ws-landscape-item': isLandscape}"
        data-type="mix-box"
    >
        <template v-if="isLandscape">
            <print-row>
                <print-col
                    :span="contentConfig.westernMedicineSpec ? 9 : 12"
                    class="western-name"
                >
                    <div
                        class="number-icon"
                        :style="{ visibility: formItem.showGroupId && showGroupIcon ? 'visible' : 'hidden' }"
                    >
                        {{ formItem.groupId ? NUMBER_ICONS[formItem.groupId] : '-' }}
                    </div>
                    <div
                        v-if="contentConfig.medicineTradeName"
                        class="western-item-name"
                    >
                        {{ formItem.name }}
                    </div>
                    <div
                        v-else
                        class="western-item-name"
                    >
                        {{ formItem.medicineCadn || formItem.name }}
                    </div>

                    <div class="western-sign-name">
                        <span
                            v-if="formItem.verifySignatureStatus && printData.doctorSignImgUrl"
                            class="sign-img-list"
                        >
                            <img
                                :src="printData.doctorSignImgUrl"
                                alt=""
                            />
                        </span>
                        <span
                            v-if="formItem.verifySignatureStatus && !printData.doctorSignImgUrl"
                            class="sign-text"
                        >
                            {{ printData.doctorName }}
                        </span>
                    </div>
                </print-col>

                <print-col
                    v-if="contentConfig.westernMedicineSpec"
                    :span="4"
                    class="spec"
                    overflow
                >
                    <span style="line-height: 17px;">{{ goodsSpec(formItem.productInfo, showPackageUnit) }}</span>
                </print-col>


                <print-col
                    :span="9"
                    class="spec"
                >
                    每次{{ formItem.dosage }}{{ formItem.dosageUnit }}
                    {{ freqFormat( formItem.freq, contentConfig.medicalLatin ) }}
                    {{ usageFormat(formItem.usage, contentConfig.medicalLatin) }}
                    <span v-if="formItem.specialRequirement && formItem.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE">
                        ({{ formItem.specialRequirement }})
                    </span>
                    <span v-if="contentConfig.westernMedicineDays">{{ formItem.days }}天</span>
                </print-col>

                <print-col
                    :span="2"
                    class="spec text-right"
                    overflow
                >
                    <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                        [自备]
                    </template>
                    <template v-else>
                        ×
                    </template>{{ formItem.unitCount }}{{ formItem.unit }}
                </print-col>
            </print-row>
        </template>

        <template v-else>
            <print-row>
                <print-col
                    :span="contentConfig.westernMedicineSpec ? 13 : 19"
                    class="western-name"
                >
                    <div
                        class="number-icon"
                        :style="{ visibility: formItem.showGroupId && showGroupIcon ? 'visible' : 'hidden' }"
                    >
                        {{ formItem.groupId ? NUMBER_ICONS[formItem.groupId] : '-' }}
                    </div>
                    <div
                        v-if="contentConfig.medicineTradeName"
                        class="western-item-name"
                    >
                        {{ formItem.name }}
                    </div>
                    <div
                        v-else
                        class="western-item-name"
                    >
                        {{ formItem.medicineCadn || formItem.name }}
                    </div>
                    <div
                        v-if="formItem.verifySignatureStatus"
                        class="western-sign-name"
                    >
                        <img
                            v-if="printData.doctorSignImgUrl"
                            :src="printData.doctorSignImgUrl"
                        />
                        <template v-else>
                            {{ printData.doctorName }}
                        </template>
                    </div>
                </print-col>
                <print-col
                    v-if="contentConfig.westernMedicineSpec"
                    :span="6"
                    class="spec"
                    overflow
                >
                    <span style="line-height: 16px;">{{ goodsSpec(formItem.productInfo, showPackageUnit) }}</span>
                </print-col>
                <print-col
                    :span="contentConfig.westernMedicineDays ? 3 : 5"
                    class="spec text-right"
                    overflow
                >
                    <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                        [自备]
                    </template><template v-else>
                        ×
                    </template>{{ formItem.unitCount }}{{ formItem.unit }}
                </print-col>
                <print-col
                    v-if="contentConfig.westernMedicineDays"
                    :span="2"
                    class="spec text-right"
                    overflow
                >
                    {{ formItem.days }}天
                </print-col>
            </print-row>
            <print-row>
                <print-col
                    v-if="hasWesternPosition"
                    :span="6"
                    class="usage-info"
                >
                    <span style="line-height: 16px;">{{ formItem.productInfo && formItem.productInfo.position }}</span>
                </print-col>
                <print-col
                    :span="18"
                    class="usage-info overflow"
                >
                    {{ contentConfig.medicalLatin ? 'Sig' : '用法' }}：每次{{ formItem.dosage }}{{ formItem.dosageUnit }}
                    {{ freqFormat( formItem.freq, contentConfig.medicalLatin ) }}
                    {{ usageFormat(formItem.usage, contentConfig.medicalLatin) }}
                    <span v-if="formItem.specialRequirement">
                        ({{ formItem.specialRequirement }})
                    </span>
                </print-col>
            </print-row>
            <print-row>
                <print-col
                    v-if="hasManufacturer"
                    :span="12"
                    class="usage-info overflow"
                    overflow
                >
                    <span style="line-height: 16px;">{{ `厂家：${formItem.productInfo && (formItem.productInfo.manufacturer || '')}` }}</span>
                </print-col>
                <print-col
                    v-if="contentConfig.westernUnitPrice || contentConfig.westernTotalPrice"
                    :span="12"
                    class="usage-info overflow"
                    overflow
                >
                    <span style="line-height: 16px;">
                        {{ formatPrice(formItem) }}
                    </span>
                </print-col>
            </print-row>
        </template>
    </div>
</template>

<script>
    import { NUMBER_ICONS } from "../../../common/constants.js";
    import { OutpatientChargeTypeEnum } from "../../../constant/print-constant.js";
    import {goodsSpec} from "../../../common/utils.js";
    import { usageFormat,freqFormat } from "../../../common/medical-transformat.js";
    import { formatMoney } from '../../../common/utils.js'

    import PrintRow from '../../layout/print-row.vue'
    import PrintCol from '../../layout/print-col.vue'

    export default {
        name: "WesternPr",
        components: {
            PrintRow,
            PrintCol,
        },
        props: {
            printData: {
                type: Object,
                default: ()=> {
                    return {}
                },
                required: true,
            },
            formItem: {
                type: Object,
                required: true,
            },
            groupNumber: Number,
            config: Object,
            showGroupIcon: {
                type: Boolean,
                default: false,
            },
            isLandscape: {
                type: Boolean,
                default: false
            },
            showPackageUnit: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                NUMBER_ICONS,
                OutpatientChargeTypeEnum,
            }
        },
        computed: {
            contentConfig() {
                return this.config && this.config.content || {};
            },
            hasWesternPosition() {
                return this.contentConfig.westernPosition && this.formItem.productInfo && this.formItem.productInfo.position;
            },
            hasManufacturer() {
                return this.contentConfig.manufacturer;
            },
        },
        methods: {
            goodsSpec,
            usageFormat,
            freqFormat,
            formatMoney,
            formatPrice(formItem) {
                let str = '';
                if (this.contentConfig.westernUnitPrice) {
                    str += `${this.$t('currencySymbol')}${formatMoney(formItem.printUnitPrice)}/${formItem.unit}`;
                }
                if (this.contentConfig.westernUnitPrice && this.contentConfig.westernTotalPrice) {
                    str += '，';
                }
                if (this.contentConfig.westernTotalPrice) {
                    str += `共${this.$t('currencySymbol')}${formatMoney(formItem.printTotalPrice)}`;
                }
                return str;
            },
        }
    }
</script>

<style lang="scss">
.western-item {
    &:not(:first-child) {
        margin-top: 10pt;
    }

    &.ws-landscape-item {
        margin-bottom: 8pt;
    }

    .spec,
    .name {
        overflow: hidden;
        font-size: 10pt;
        line-height: 12pt;
        word-break: keep-all;
        white-space: nowrap;
    }

    .name {
        padding-right: 2pt;
    }

    .western-name {
        padding-right: 14px;
        font-size: 10pt;
        line-height: 12pt;
        word-break: break-all;
        word-wrap: break-word;
    }

    .overflow {
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
    }

    .spec {
        padding-left: 2pt;
    }

    .western-item-name {
        display: inline-block;
        padding-left: 16pt;
        overflow: visible;
        text-indent: 0;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
    }

    .number-icon {
        position: absolute;
        top: 0;
        left: 0;
        display: inline-block;
        width: 16pt;
        font-size: 10pt;
        line-height: 12pt;
        vertical-align: top;
    }

    .text-right {
        text-align: right;
    }

    .usage-info {
        padding-left: 10pt;
        margin-top: 2pt;
        margin-bottom: 2pt;
        font-size: 10pt;
        font-weight: 300;
        line-height: 14pt;

        &:first-child {
            padding-left: 36pt;
        }
    }

    .western-sign-name {
        top: -1pt;
        display: inline-block;
        width: auto;
        height: 14pt;
        margin-left: 4pt;
        font-size: 6pt;

        img {
            width: auto;
            height: 14pt;
            border: 0;
        }
    }
}

</style>
