<template>
    <div
        class="diagnosis-treatment-container"
        data-type="mix-box"
    >
        <print-row class="print-row-margin-bottom">
            <!-- 名称 -->
            <print-col
                :span="13"
                class="name"
                overflow
            >
                <div
                    class="number-icon"
                    :style="{ visibility: showGroupIcon ? 'visible' : 'hidden' }"
                >
                    -
                </div>
                <span>
                    {{ formItem.name }}
                </span>
            </print-col>

            <!-- 规格/频率 -->
            <print-col
                :span="6"
                class="spec"
            >
                <!-- 材料/商品 -->
                <template v-if="calcIsMaterialAndProduct(formItem.type)">
                    <span style="line-height: 16px;">
                        {{ calcMaterialAndProductSpec(formItem.productInfo) }}
                    </span>
                </template>
                <template v-else>
                    <span
                        style="line-height: 16px;"
                        :style="{ visibility: supportInputDays && formItem.dailyDosage > 0 ? 'visible' : 'hidden' }"
                    >
                        每天{{ formItem.dailyDosage }}{{ formItem.unit }}
                    </span>
                </template>
            </print-col>

            <!-- 总量 -->
            <print-col
                :span="3"
                class="spec text-right"
            >
                <span v-if="formItem.unitCount > 0">
                    <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                        [自备]
                    </template>
                    <template v-else>
                        ×
                    </template>
                    {{ formItem.unitCount }}{{ formItem.unit }}
                </span>
                <span
                    v-else
                    style="visibility: hidden"
                >
                    -
                </span>
            </print-col>

            <!-- 天数 -->
            <print-col
                :span="2"
                class="spec text-right"
            >
                <span v-if="formItem.days > 0">
                    {{ formItem.days }}天
                </span>
                <span
                    v-else
                    style="visibility: hidden"
                >
                    -
                </span>
            </print-col>
        </print-row>

        <!-- 套餐子项 -->
        <template v-if="calcIsCompose(formItem) && formItem.composeChildren.length">
            <template v-for="children in formItem.composeChildren">
                <print-row
                    :key="children.id"
                    class="print-row-margin-bottom"
                >
                    <!-- 名称 -->
                    <print-col
                        :span="19"
                        class="name compose-item-name"
                        overflow
                    >
                        <div
                            class="number-icon"
                            style="visibility: hidden;"
                        >
                            -
                        </div>
                        <span>
                            {{ children.name }}
                        </span>
                    </print-col>

                    <!-- 总量 -->
                    <print-col
                        :span="3"
                        class="spec text-right"
                    >
                        <span v-if="formItem.unitCount > 0">
                            <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                                [自备]
                            </template>
                            <template v-else>
                                ×
                            </template>
                            {{ formItem.unitCount }}{{ formItem.unit }}
                        </span>
                        <span
                            v-else
                            style="visibility: hidden"
                        >
                            -
                        </span>
                    </print-col>

                    <!-- 天数 -->
                    <print-col
                        :span="2"
                        class="spec text-right"
                    >
                        <span v-if="formItem.days > 0">
                            {{ formItem.days }}天
                        </span>
                        <span
                            v-else
                            style="visibility: hidden"
                        >
                            -
                        </span>
                    </print-col>
                </print-row>
            </template>
        </template>
    </div>
</template>

<script>
    import PrintRow from "../../layout/print-row.vue";
    import PrintCol from "../../layout/print-col.vue";
    import { OutpatientChargeTypeEnum } from "../../../constant/print-constant";

    export default {
        name: 'DiagnosisTreatment',
        components: {
            PrintRow,
            PrintCol,
        },
        props: {
            formItem: {
                type: Object,
                default: () => {},
            },
            showGroupIcon: {
                type: Boolean,
                default: false,
            },
            supportInputDays: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                OutpatientChargeTypeEnum,
            };
        },
        methods: {
            // 判断是否是材料或商品
            calcIsMaterialAndProduct(type) {
                return type === 2 || type === 7;
            },
            // 判断是否显示频率
            isShowFreq(item) {
                if (!(item.type === 4 || item.type === 11)) {
                    return false;
                }
                return item.days > 0 && item.unitCount > 0;
            },
            // 计算频率
            calcFreq(item) {
                const freq = Math.ceil(item.unitCount / item.days);
                const { unit } = item;
                return `每天${freq}${unit}`;
            },
            // 判断是否是套餐
            calcIsCompose(item) {
                return item.type === 11 && item.composeType === 1;
            },
            calcMaterialAndProductSpec(productInfo = {}) {
                let {
                    pieceNum,
                    pieceUnit,
                    packageUnit,
                    materialSpec,
                    displaySpec
                } = productInfo;
                if (displaySpec) {
                    return displaySpec;
                }
                let goodsSep = `${pieceNum || 1}`;
                if (pieceUnit) {
                    goodsSep += pieceUnit;
                }
                if (packageUnit) {
                    goodsSep += '/' + packageUnit;
                }
                if (materialSpec) {
                    goodsSep = materialSpec + '*' + goodsSep;
                }
                return goodsSep;
            },
        },
    }
</script>

<style lang="scss">
.diagnosis-treatment-container {
    .print-row-margin-bottom {
        margin-bottom: 12pt;
    }

    .number-icon {
        font-size: 10pt;
        line-height: 12pt;
        display: inline-block;
        width: 16pt;
        vertical-align: top;
    }

    .spec,
    .name {
        font-size: 10pt;
        line-height: 12pt;
        overflow: hidden;
        white-space: nowrap;
        word-break: keep-all;
    }

    .name {
        padding-right: 2pt;
    }

    .spec {
        padding-left: 2pt;
    }

    .compose-item-name {
        text-indent: 1em;
    }

    .text-right{
        text-align: right;
    }
}
</style>
