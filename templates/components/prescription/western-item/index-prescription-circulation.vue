<template>
    <div
        class="western-item"
        :class="{'ws-landscape-item': isLandscape}"
        data-type="mix-box"
    >
        <template v-if="isLandscape">
            <print-row>
                <print-col
                    :span="20"
                    class="name"
                    overflow
                >
                    <div style="display: inline-block; overflow: hidden; vertical-align: top;">
                        {{ formItem.drugGenname }}
                    </div>
                    <div class="spec-style">
                        （{{
                            formItem.drugSpec ? (
                                region.isJilin && formItem.drugSpec.indexOf('*') !== -1 ? formItem.drugSpec.split('*')[0] : formItem.drugSpec
                            ) : ''
                        }}）
                    </div>
                </print-col>
                <print-col :span="2" class="spec text-right">
                    ×{{ formItem.drugCnt }}{{ formItem.drugDosunt }}
                </print-col>
                <print-col :span="2" class="spec text-right">
                    {{ formItem.medcDays }}天
                </print-col>

                <print-col
                    :span="24"
                    class="usage-info"
                >
                    用法：每次
                    {{ formItem.sinDoscnt }}{{ formItem.sinDosunt }}
                    {{ formItem.usedFrquName }} {{ formItem.medcWayDscr }}
                    <span v-if="contentConfig.westernMedicineDays">{{ formItem.medcDays }}天</span>
                </print-col>
            </print-row>
        </template>

        <template v-else>
            <print-row>
                <print-col
                    :span="20"
                    class="name"
                    overflow
                >
                    <div class="drug-name">
                        {{ formItem.drugGenname }}
                    </div>
                    <div class="spec-style">
                        （{{
                            formItem.drugSpec ? (
                                region.isJilin && formItem.drugSpec.indexOf('*') !== -1 ? formItem.drugSpec.split('*')[0] : formItem.drugSpec
                            ) : ''
                        }}）
                    </div>
                </print-col>
                <print-col :span="2" class="spec text-right">
                    ×{{ formItem.drugCnt }}{{ formItem.drugDosunt }}
                </print-col>
                <print-col :span="2" class="spec text-right">
                    {{ formItem.medcDays }}天
                </print-col>
            </print-row>
            <print-row>
                <print-col :span="24" class="usage-info">
                    用法：每次
                    {{ formItem.sinDoscnt }}{{ formItem.sinDosunt }}
                    {{ formItem.usedFrquName }} {{ formItem.medcWayDscr }}
                </print-col>
            </print-row>
        </template>
    </div>
</template>

<script>
    import PrintRow from '../../layout/print-row.vue'
    import PrintCol from '../../layout/print-col.vue'

    export default {
        name: "WesternPr",
        components: {
            PrintRow,
            PrintCol,
        },
        props: {
            formItem: {
                type: Object,
                required: true,
            },
            config: Object,
            isLandscape: {
                type: Boolean,
                default: false
            },
            printData: {
                type: Object,
                default: () => ({})
            }
        },
        computed: {
            contentConfig() {
                return this.config && this.config.content || {}
            },
            // 地区
            region() {
                const region = this.printData.region
                const isJilin = region?.startsWith('jilin')
                return {
                    isJilin
                }
            },
        }
    }
</script>

<style lang="scss">
.western-item {
    font-size: 8pt;
    line-height: 10pt;

    & .ws-landscape-item {
        margin-bottom: 8pt;
    }
    .spec, .name {
        overflow: hidden;
        white-space: nowrap;
        word-break: keep-all;

        .drug-name {
            display: inline-block;
            overflow: hidden;
            vertical-align: top;
        }

        .spec-style {
            display: inline-block;
            vertical-align: top;
        }
    }
    .name {
        padding-right: 2pt;
    }

    .text-right {
        text-align: right;
    }

    .usage-info {
        margin-top: 3pt;
        padding-left: 20pt;
        margin-bottom: 12pt;
        font-weight: 300;
    }
}

</style>
