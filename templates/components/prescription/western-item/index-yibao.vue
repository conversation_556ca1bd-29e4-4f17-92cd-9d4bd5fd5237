<template>
    <div
        class="western-item"
        :class="{'ws-landscape-item': isLandscape}"
        data-type="mix-box"
    >
        <template v-if="isLandscape">
            <print-row>
                <print-col
                    :span="8"
                    class="name"
                    overflow
                >
                    <div
                        class="number-icon"
                        :style="{ visibility: formItem.showGroupId && showGroupIcon ? 'visible' : 'hidden' }"
                    >
                        {{ formItem.groupId ? NUMBER_ICONS[formItem.groupId] : '-' }}
                    </div>
                    <div
                        style="display: inline-block; overflow: hidden; vertical-align: top;"
                        :style="{ 'max-width': contentConfig.westernMedicineSpec ? '80%' : 'none' }"
                    >
                        <template v-if="contentConfig.medicineTradeName">
                            {{ formItem.name }}
                        </template>
                        <template v-else>
                            {{ formItem.medicineCadn || formItem.name }}
                        </template>
                    </div>
                </print-col>

                <print-col
                    :span="4"
                    class="spec text-left"
                >
                    <div
                        v-if="formItem.specification"
                        style="display: inline-block; vertical-align: top;"
                    >
                        {{ formItem.specification }}
                    </div>
                </print-col>

                <print-col
                    :span="9"
                    class="spec"
                >
                    每次{{ formItem.dosage }}{{ formItem.dosageUnit }}
                    {{ freqFormat( formItem.freq, contentConfig.medicalLatin ) }}
                    {{ usageFormat(formItem.usage, contentConfig.medicalLatin) }}
                    <span v-if="!formItem.payType && formItem.specialRequirement && formItem.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE">
                        ({{ formItem.specialRequirement }})
                    </span>
                    <span v-if="contentConfig.westernMedicineDays">{{ formItem.days }}天</span>
                </print-col>

                <print-col
                    :span="2"
                    class="spec text-right"
                >
                    <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                        [自备]
                    </template>
                    <template v-else>
                        ×
                    </template>{{ formItem.unitCount }}{{ formItem.drugUnit }}
                </print-col>
            </print-row>
        </template>

        <template v-else>
            <print-row>
                <print-col
                    :span="12"
                    class="name"
                    overflow
                >
                    <div
                        class="number-icon"
                        :style="{ visibility: formItem.showGroupId && showGroupIcon ? 'visible' : 'hidden' }"
                    >
                        {{ formItem.groupId ? NUMBER_ICONS[formItem.groupId] : '-' }}
                    </div>
                    <div
                        style="display: inline-block; overflow: hidden; vertical-align: top;"
                        :style="{ 'max-width': contentConfig.westernMedicineSpec ? '70%' : 'none' }"
                    >
                        <template v-if="contentConfig.medicineTradeName">
                            {{ formItem.name }}
                        </template>
                        <template v-else>
                            {{ formItem.medicineCadn || formItem.name }}
                        </template>
                    </div>
                </print-col>
                <print-col
                    :span="7"
                    class="spec text-left"
                >
                    <div
                        v-if="formItem.specification"
                        style="display: inline-block; vertical-align: top;"
                    >
                        {{ formItem.specification }}
                    </div>
                </print-col>
                <print-col
                    :span="3"
                    class="spec text-right"
                >
                    <template v-if="formItem.chargeType === OutpatientChargeTypeEnum.NO_CHARGE">
                        [自备]
                    </template>
                    <template v-else>
                        ×
                    </template>{{ formItem.unitCount }}{{ formItem.drugUnit }}
                </print-col>
                <print-col
                    :span="2"
                    class="spec text-right"
                >
                    {{ formItem.days }}天
                </print-col>
            </print-row>
            <print-row>
                <print-col
                    :span="24"
                    class="usage-info"
                >
                    {{ contentConfig.medicalLatin ? 'Sig' : '用法' }}：每次{{ formItem.dosage }}{{ formItem.dosageUnit }}
                    {{ freqFormat(formItem.freq, contentConfig.medicalLatin) }}
                    {{ usageFormat(formItem.usage, contentConfig.medicalLatin) }}
                    <span v-if="!formItem.payType && formItem.specialRequirement && formItem.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE">
                        ({{ formItem.specialRequirement }})
                    </span>
                </print-col>
            </print-row>
        </template>
    </div>
</template>

<script>
    import { NUMBER_ICONS } from "../../../common/constants.js";
    import { OutpatientChargeTypeEnum } from "../../../constant/print-constant.js";
    import {goodsSpec} from "../../../common/utils.js";
    import { usageFormat,freqFormat } from "../../../common/medical-transformat.js";

    import PrintRow from '../../layout/print-row.vue'
    import PrintCol from '../../layout/print-col.vue'

    export default {
        name: "WesternPr",
        components: {
            PrintRow,
            PrintCol,
        },
        props: {
            formItem: {
                type: Object,
                required: true,
            },
            groupNumber: Number,
            config: Object,
            showGroupIcon: {
                type: Boolean,
                default: false,
            },
            isLandscape: {
                type: Boolean,
                default: false
            }
        },
        data() {
            return {
                NUMBER_ICONS,
                OutpatientChargeTypeEnum,
            }
        },
        computed: {
            contentConfig() {
                return this.config && this.config.content || {};
            }
        },
        methods: {
            goodsSpec,
            usageFormat,
            freqFormat
        }
    }
</script>

<style lang="scss">
.western-item {
    font-size: 8pt;
    line-height: 10pt;

    &.ws-landscape-item {
        margin-bottom: 8pt;
    }
    .spec,
    .name {
        overflow: hidden;
        white-space: nowrap;
        word-break: keep-all;
    }
    .name {
        padding-right: 2pt;
    }

    .spec {
        padding-left: 2pt;
    }

    .number-icon {
        display: inline-block;
        width: 10pt;
        vertical-align: top;
    }

    .text-right{
        text-align: right;
    }

    .usage-info {
        margin-top: 3pt;
        padding-left: 36pt;
        margin-bottom: 12pt;
        font-weight: 300;
    }
}

</style>
