<template>
    <div>
        <template v-for="(form, formIndex) in printData.prescriptionWesternForms">
            <outpatient-header
                data-type="header"
                :print-data="printData"
                :created-time="form.created"
                :config="config"
                print-title="处方笺"
                :organ-title="organTitle"
                :show-express="!!form.deliveryInfo && contentConfig.expressInfo"
                :data-pendants-index="`${formIndex}WP`"
                :print-jinma="form && form.psychotropicNarcoticType"
            ></outpatient-header>
            <print-row class="list-info item-name list-title">
                <print-col
                    :span="12"
                    overflow
                >
                    药品名
                </print-col>
                <print-col :span="9">
                    规格
                </print-col>
                <print-col :span="3">
                    总量
                </print-col>
            </print-row>
            <template v-if="form.isStandardForm">
                <template v-for="(formItems, formItemsIndex) in form.prescriptionFormItems">
                    <western-item
                        v-for="formItem in formItems"
                        :show-group-icon="checkExistedGroupId(form)"
                        :config="config"
                        :form-item="formItem"
                    ></western-item>
                    <div
                        v-if="form.prescriptionFormItems.length - 1 !== formItemsIndex"
                        data-type="new-page"
                    ></div>
                </template>
            </template>
            <template v-else>
                <western-item
                    v-for="formItem in form.prescriptionFormItems"
                    :show-group-icon="checkExistedGroupId(form)"
                    :config="config"
                    :form-item="formItem"
                ></western-item>
            </template>

            <!-- 快递 -->
            <template
                v-if="contentConfig.expressInfo &&
                    form.deliveryInfo &&
                    form.deliveryInfo.deliveryCompany &&
                    form.deliveryInfo.deliveryCompany.id &&
                    form.deliveryInfo.chargeStatus < ChargeItemStatusEnum.REFUND"
            >
                <delivery-info
                    :key="formIndex"
                    :delivery-info="form.deliveryInfo"
                ></delivery-info>
            </template>
            <!-- 医嘱 -->
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice
                    :key="formIndex"
                    :doctor-advice="doctorAdvice"
                ></doctor-advice>
            </template>

            <next-is-blank></next-is-blank>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}WP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :print-config="config"
                    :print-time="printData.printTime"
                    :pr-form="form"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>

        <template v-for="(form, formIndex) in printData.prescriptionInfusionForms">
            <outpatient-header
                data-type="header"
                :print-data="printData"
                :organ-title="organTitle"
                :created-time="form.created"
                :config="config"
                print-title="处方笺"
                :show-express="!!form.deliveryInfo && contentConfig.expressInfo"
                :data-pendants-index="`${formIndex}IP`"
                :print-jinma="form && form.psychotropicNarcoticType"
            ></outpatient-header>
            <template v-if="form.isStandardForm">
                <template v-for="(formItems, formItemsIndex) in form.prescriptionFormItems">
                    <div
                        v-for="(groupFormItems, groupIndex) in getInfusionGroupItems(formItems)"
                        data-type="mix-box"
                    >
                        <infusion-item
                            :print-data="printData"
                            show-days
                            :config="config"
                            :group-form-items="groupFormItems"
                        ></infusion-item>
                    </div>
                    <div
                        v-if="form.prescriptionFormItems.length - 1 !== formItemsIndex"
                        data-type="new-page"
                    ></div>
                </template>
            </template>
            <template v-else>
                <div
                    v-for="(groupFormItems, groupIndex) in getInfusionGroupItems(form.prescriptionFormItems)"
                    data-type="mix-box"
                >
                    <infusion-item
                        :print-data="printData"
                        show-days
                        :config="config"
                        :group-form-items="groupFormItems"
                    ></infusion-item>
                </div>
            </template>

            <!-- 快递 -->
            <template
                v-if="contentConfig.expressInfo &&
                    form.deliveryInfo &&
                    form.deliveryInfo.deliveryCompany &&
                    form.deliveryInfo.deliveryCompany.id &&
                    form.deliveryInfo.chargeStatus < ChargeItemStatusEnum.REFUND"
            >
                <delivery-info
                    :key="formIndex"
                    :delivery-info="form.deliveryInfo"
                ></delivery-info>
            </template>
            <!-- 医嘱 -->
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice
                    :key="formIndex"
                    :doctor-advice="doctorAdvice"
                ></doctor-advice>
            </template>

            <next-is-blank></next-is-blank>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}IP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :pr-form="form"
                    :print-time="printData.printTime"
                    :print-config="config"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>
        <template v-for="(chineseForm, formIndex) in printData.prescriptionChineseForms">
            <outpatient-header
                data-type="header"
                :print-data="printData"
                :created-time="chineseForm.created"
                :organ-title="organTitle"
                :config="config"
                :extend-spec="chineseForm.specification"
                print-title="处方笺"
                :show-virtual-pharmacy="chineseForm.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY"
                :data-pendants-index="`${formIndex}CP`"
                :show-process="
                    contentConfig.processInfo &&
                        chineseForm.processInfo &&
                        chineseForm.processInfo.chargeStatus < ChargeItemStatusEnum.REFUND
                "
                :show-express="
                    contentConfig.expressInfo &&
                        chineseForm.deliveryInfo &&
                        chineseForm.deliveryInfo.chargeStatus < ChargeItemStatusEnum.REFUND
                "
                :print-jinma="chineseForm && chineseForm.psychotropicNarcoticType"
            ></outpatient-header>

            <div data-type="mix-box">
                <template v-for="(groupItems, groupIndex) in getChineseGroupItems(chineseForm.prescriptionFormItems, groupCount)">
                    <chinese-item
                        :config="config"
                        :form-items="groupItems"
                        :print-data="printData"
                        :group-count="groupCount"
                        :data-avoid-first-page-break-ref-id="groupIndex === getChineseGroupItems(chineseForm.prescriptionFormItems, groupCount).length - 1 ? `${formIndex}CP` : undefined"
                    ></chinese-item>
                </template>
            </div>

            <div :data-avoid-first-page-break-id="`${formIndex}CP`">
                <!--用法-->
                <print-row
                    style="font-size: 10pt;font-weight: 400"
                >
                    <print-col
                        :span="24"
                    >
                        <span>用法：</span>
                        <span v-html="getChineseFormUsage(chineseForm, contentConfig).totalInfo"></span>
                    </print-col>
                </print-row>

                <print-row
                    v-if="getChineseFormUsage(chineseForm, contentConfig).usageInfo"
                >
                    <print-col
                        class="remark-col"
                        :span="24"
                        v-html="getChineseFormUsage(chineseForm, contentConfig).usageInfo"
                    >
                    </print-col>
                </print-row>

                <!-- 加工-->
                <print-row
                    v-if="contentConfig.processInfo &&
                        chineseForm.processInfo &&
                        chineseForm.processInfo.chargeStatus < ChargeItemStatusEnum.REFUND &&
                        (getProcessInfoStr(chineseForm) || chineseForm.processRemark)
                    "
                    :key="formIndex"
                    style="margin-top: 6pt;border-top: 1px dashed #000;"
                >
                    <print-col
                        class="remark-col"
                        :span="24"
                    >
                        <span class="label">加工：</span>
                        {{ getProcessInfoStr(chineseForm) }}
                        <template v-if="chineseForm.processRemark">
                            ，{{ chineseForm.processRemark }}
                        </template>
                    </print-col>
                </print-row>
                <!-- 快递 -->
                <template
                    v-if="contentConfig.expressInfo &&
                        chineseForm.deliveryInfo &&
                        chineseForm.deliveryInfo.deliveryCompany &&
                        chineseForm.deliveryInfo.deliveryCompany.id &&
                        chineseForm.deliveryInfo.chargeStatus < ChargeItemStatusEnum.REFUND"
                >
                    <delivery-info
                        :key="formIndex"
                        :delivery-info="chineseForm.deliveryInfo"
                    ></delivery-info>
                </template>
            </div>

            <!-- 医嘱 -->
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice
                    :key="formIndex"
                    :doctor-advice="doctorAdvice"
                ></doctor-advice>
            </template>

            <next-is-blank></next-is-blank>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}CP`"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :pr-form="chineseForm"
                    is-chinese
                    :print-time="printData.printTime"
                    :print-config="config"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>

        <template v-for="(externalForm, formIndex) in printData.prescriptionExternalForms">
            <outpatient-header
                :pharmacy-name="externalForm.pharmacyName"
                data-type="header"
                :show-express="!!externalForm.deliveryInfo"
                :print-data="printData"
                :organ-title="organTitle"
                :config="config"
                :created-time="externalForm.created"
                :print-jinma="externalForm && externalForm.psychotropicNarcoticType"
                :extend-spec="config.header && config.header.prescriptionType ? ExternalPRUsageTypeEnumRevert[externalForm.usageType] : ''"
                print-title="处方笺"
                :data-pendants-index="`${formIndex}EP`"
            ></outpatient-header>
            <template v-for="(formItem, formItemIndex) in externalForm.prescriptionFormItems">
                <print-row>
                    <print-col
                        :span="24"
                        class="external-item"
                    >
                        {{ formItemIndex + 1 }}.{{ formItem.name }}
                    </print-col>
                </print-row>
                <!--药品-->
                <external-item
                    v-for="(goodsItems, goodsIndex) in getChineseGroupItems(formItem.externalGoodsItems, 4)"
                    type="goods"
                    :config="config"
                    :form-items="goodsItems"
                ></external-item>


                <print-row
                    v-if="formatAcupoints(formItem.acupoints)"
                    class="remark-row first-remark-row external-usage-row"
                    data-type="mix-box"
                >
                    <print-col
                        class="remark-col"
                        :span="24"
                    >
                        <span class="label">穴位：</span>
                        {{ formatAcupoints(formItem.acupoints) }}
                    </print-col>
                </print-row>
                <print-row
                    class="remark-row first-remark-row external-usage-row"
                    data-type="mix-box"
                >
                    <print-col
                        class="remark-col"
                        :span="24"
                    >
                        <span class="label">用法：</span>
                        <template v-if="formItem.unitCount">
                            共 {{ formItem.dosage }} 次
                        </template>
                        <template v-if="formItem.freq">
                            ，{{ formItem.freq }}
                        </template>
                        <template v-if="formItem.specialRequirement">
                            ，{{ formItem.specialRequirement }}
                        </template>
                        <template v-if="formItem.unitCount && formItem.dosage">
                            ，每次{{ calcDosageCount(formItem) }}{{ externalForm.usageType === 0 ? '贴' : '穴' }}
                        </template>
                        <template v-if="formItem.dosage">
                            ，共{{ formItem.dosage * calcDosageCount(formItem) }}{{ externalForm.usageType === 0 ? '贴' : '穴' }}
                        </template>
                        <template v-if="externalForm.usageType === 0 && externalForm.usageSubType === 0 && formItem.productInfo.type !== 4">
                            ，共{{ formItem.unitCount }}{{ formItem.unit }}
                        </template>
                    </print-col>
                </print-row>
            </template>

            <!--            <template v-if="contentConfig.expressInfo && externalForm.deliveryInfo">-->
            <!--                <delivery-info :delivery-info="externalForm.deliveryInfo"></delivery-info>-->
            <!--            </template>-->
            <template v-if="contentConfig.doctorAdvice && doctorAdvice.length">
                <doctor-advice :doctor-advice="doctorAdvice"></doctor-advice>
            </template>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}EP`"
                :not-last-footer="printData.prescriptionExternalForms && (printData.prescriptionExternalForms.length - 1) !== formIndex"
            >
                <outpatient-footer
                    :config="config"
                    :print-data="printData"
                    :pr-form="externalForm"
                    :print-config="config"
                    :print-time="printData.printTime"
                    print-type="prescription"
                ></outpatient-footer>
            </div>
        </template>

        <div
            class="next-page"
            data-type="next-page"
        >
            (接下页)
        </div>
        <div
            class="prev-page"
            data-type="prev-page"
        >
            (接上页)
        </div>
    </div>
</template>

<script>
    import {formatAge, formatMoney} from '../../../common/utils.js'
    import { ChargeItemStatusEnum } from "../../../common/constants.js";

    import OutpatientHeader from '../../medical-document-header/header-hetu.vue'
    import OutpatientFooter from '../../medical-document-footer/footer-hetu.vue'
    import ChineseItem from '../chinese-item/index.vue';
    import PrintRow from '../../layout/print-row.vue';
    import PrintCol from '../../layout/print-col.vue';
    import {ExternalPRUsageTypeEnumRevert} from "../../../constant/print-constant.js";
    import NextIsBlank from '../../next-is-blank/index.vue';

    import {
        checkExistedGroupId,
        doseTotal,
        formatAcupoints,
        getChineseGroupItems,
        getDoctorAdviceArray,
        getInfusionGroupItems,
        isNumber
    } from "../../../common/medical-transformat.js";
    import clone from "../../../common/clone.js";
    import {PharmacyTypeEnum} from "../../../common/constants.js";
    import WesternItem from "../western-item/index.vue";
    import InfusionItem from "../infusion-item/index.vue";
    import DoctorAdvice from "../doctor-advice/index.vue";
    import DeliveryInfo from "../delivery-info/index.vue";

    export default {
        components: {
            DeliveryInfo,
            DoctorAdvice,
            OutpatientHeader,
            OutpatientFooter,
            ChineseItem,
            WesternItem,
            InfusionItem,
            PrintCol,
            PrintRow,
            NextIsBlank,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        data() {
            return {
                ExternalPRUsageTypeEnumRevert,
                PharmacyTypeEnum,
                ChargeItemStatusEnum,
            }
        },
        computed: {
            printData() {
                this.renderData.printData.prescriptionWesternForms && this.renderData.printData.prescriptionWesternForms.forEach( form => {
                    form.prescriptionFormItems = this.groupMedicine(form.prescriptionFormItems);
                })
                if(this.renderData.config &&
                    this.renderData.config.medicalDocuments &&
                    this.renderData.config.medicalDocuments.prescription &&
                    this.renderData.config.medicalDocuments.prescription.content.standardKindCount) {
                    const {prescriptionWesternForms, prescriptionInfusionForms} = this.transStandardForm();
                    const data =  {
                        ...this.renderData.printData,
                        prescriptionWesternForms: prescriptionWesternForms,
                        prescriptionInfusionForms: prescriptionInfusionForms,
                    }

                    console.log('自动分页过后的printData', data)
                    return data;
                } else {
                    return this.renderData.printData || null;
                }
            },
            config() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.prescription) {
                    let prescription = {};
                    prescription.header = {
                        address: 1,
                        barcode: 1,
                        birthday: 1,
                        computerCode: 1,
                        fileNumber: 1,
                        idCard: 1,
                        logo: 1,
                        married: 1,
                        mobile: 1,
                        personType: 1,
                        templateType: 1,
                        prescriptionType: 1,
                        qrcode: 1,
                        revisit: 1,
                        socialCode: 1,
                        title: this.renderData.config.medicalDocuments.prescription.header && this.renderData.config.medicalDocuments.prescription.header.title
                    }
                    prescription.content = {
                        chineseMedicineCount: 1,
                        chineseMedicineTotalCount: 1,
                        doctorAdvice: this.renderData.config.medicalDocuments.prescription.content && this.renderData.config.medicalDocuments.prescription.content.doctorAdvice,
                        expressInfo: this.renderData.config.medicalDocuments.prescription.content && this.renderData.config.medicalDocuments.prescription.content.expressInfo,
                        medicalLatin: this.renderData.config.medicalDocuments.prescription.content && this.renderData.config.medicalDocuments.prescription.content.medicalLatin,
                        medicineTradeName: 1,
                        processInfo: this.renderData.config.medicalDocuments.prescription.content && this.renderData.config.medicalDocuments.prescription.content.processInfo,
                        chineseLayout: this.renderData.config.medicalDocuments.prescription.content && this.renderData.config.medicalDocuments.prescription.content.chineseLayoutForHechi,
                        standardKindCount: this.renderData.config.medicalDocuments.prescription.content && this.renderData.config.medicalDocuments.prescription.content.standardKindCount,
                        westernMedicineDays: 0,
                        westernMedicineSpec: 1,
                    }
                    prescription.footer = {
                        amount: this.renderData.config.medicalDocuments.prescription.footer && this.renderData.config.medicalDocuments.prescription.footer.amount,
                        assinger: 1,
                        check: 1,
                        dispense: 1,
                        printDate: 1,
                        remark: ""
                    }
                    prescription.infusionUsageSingleLine = this.renderData.config.medicalDocuments.prescription.infusionUsageSingleLine;
                    return prescription;
                }
                return {};
            },
            contentConfig() {
                return this.config && this.config.content || {};
            },
            organ() {
                return this.printData && this.printData.organ;
            },
            organTitle() {
                return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.prescription || '';
            },
            clinicName() {
                return this.organ.name;
            },
            patient() {
                return this.printData.patient;
            },
            doctorAdvice() {
                return getDoctorAdviceArray(this.printData.doctorAdvice);
            },
            groupCount() {
                const { chineseLayout } = this.contentConfig
                if (chineseLayout) {
                    return 3;
                }
                return 4;
            }
        },
        methods: {
            formatAge,
            formatMoney,
            getInfusionGroupItems,
            getChineseGroupItems,
            formatAcupoints,
            checkExistedGroupId,
            // 获取中药处方用法
            getChineseFormUsage(form, prContentConfig = {}) {
                let usageStr = '';
                let usageArray = [];
                if(form.usage) {
                    usageArray.push(form.usage);
                }
                if(form.dailyDosage) {
                    usageArray.push(form.dailyDosage);
                }
                if(form.freq) {
                    usageArray.push(form.freq)
                }
                if(form.usageLevel) {
                    usageArray.push(form.usageLevel)
                }
                if(form.usageDays) {
                    usageArray.push(form.usageDays);
                }
                if(form.requirement) {
                    usageArray.push(form.requirement);
                }
                usageStr = usageArray.join('，')

                let _str = '';
                const {chineseMedicineTotalCount} = prContentConfig;
                _str += `共 <span class="bold-text">${form.doseCount || ''}</span> 剂`;
                if(chineseMedicineTotalCount) {
                    _str += `，${doseTotal(form.prescriptionFormItems).kind} 味`;
                    if (Number(doseTotal(form.prescriptionFormItems).count)) {
                        _str += `，单剂 ${
                            doseTotal(form.prescriptionFormItems).count
                        } g，总重 ${
                            (doseTotal(form.prescriptionFormItems).count * form.doseCount).toFixed(2)
                        } g`
                    } else {
                        _str += `，${usageStr}`;
                        usageStr = '';
                    }
                } else {
                    _str += `，${usageStr}`;
                    usageStr = '';
                }
                return {
                    totalInfo:  _str,
                    usageInfo:  usageStr ,
                }
            },
            formatPatientOrderNo( value = '' ) {
                let srcStr = '00000000';
                if (!value)
                    return srcStr;
                return (srcStr + ('' + value)).slice( -8 );
            },
            splitFormItems(formItems, count = 5, isRepeat = true) {
                let tempFormItems = clone(formItems);
                let len = formItems.length;
                let res = [];
                if(isRepeat) {
                    // 不考虑去重，直接按照数量切分
                    let index = 0;
                    while(index < len) {
                        let tempItems = tempFormItems.slice(index, index += count);
                        res.push(tempItems);
                    }
                } else {
                    let groupSet = new Set();
                    let group = [];
                    tempFormItems.forEach( item => {
                        groupSet.add(item.goodsId);
                        if(groupSet.size < 6) {
                            group.push(item);
                        } else {
                            res.push(group);
                            groupSet.clear();
                            groupSet.add(item.goodsId);
                            group = [];
                            group.push(item);
                        }
                    })
                    if(group && group.length) {
                        res.push(group);
                    }
                }
                return res;
            },
            transStandardForm() {
                const wsForms = clone(this.renderData.printData.prescriptionWesternForms) || [];
                const inForms = clone(this.renderData.printData.prescriptionInfusionForms) || [];
                let resWsForm = [];
                let resInForm = [];
                wsForms.forEach( form => {
                    let tempForm = clone(form);
                    tempForm.prescriptionFormItems = [];
                    let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5,false);
                    resWsForm.push({
                        ...tempForm,
                        isStandardForm: true,
                        prescriptionFormItems: formItemsGroups
                    })
                })
                inForms.forEach( form => {
                    let tempForm = clone(form);
                    tempForm.prescriptionFormItems = [];
                    let formItemsGroups = this.splitFormItems(form.prescriptionFormItems, 5, false);
                    resInForm.push({
                        ...tempForm,
                        isStandardForm: true,
                        prescriptionFormItems: formItemsGroups
                    })

                })
                return {
                    prescriptionWesternForms: resWsForm,
                    prescriptionInfusionForms: resInForm,
                }
            },
            groupMedicine( prescriptionFormItems ) {
                let _group = {};
                let tempFormItems = clone(prescriptionFormItems)
                let noGroupIdItems = [];
                //分组
                tempFormItems.forEach( ( medicine ) => {
                    if(!medicine.groupId) {
                        medicine.showGroupId = true;
                        noGroupIdItems.push(medicine);
                    } else {
                        if (!(_group[ Number( medicine.groupId ) ] instanceof Array)) {
                            _group[ Number( medicine.groupId ) ] = [];
                        }
                        if(!_group[ Number( medicine.groupId ) ].length ) {
                            medicine.showGroupId = true;
                        } else{
                            medicine.showGroupId = false;
                        }
                        _group[ Number( medicine.groupId ) ].push(  medicine );
                    }

                } );
                let res = [];
                for( let item in _group) {
                    res = res.concat(_group[item])
                }
                return res.concat(noGroupIdItems);
            },
            calcDosageCount(item) {
                if (item.acupointUnitCount && item.acupointUnit) {
                    return item.acupointUnitCount;
                }
                let count = 0;
                (item.acupoints || []).forEach((it) => {
                    if (it.name) {
                        if (isNumber(it.position)) {
                            count += parseFloat(it.position);
                        } else if (it.position === '双') {
                            count += 2;
                        } else if (it.position !== '-') {
                            count++;
                        }
                    }
                });
                return count || 1;
            },
            getProcessInfoStr(form) {
                const {
                    isDecoction,
                    pharmacyType,
                    processBagUnitCount = 1,
                    totalProcessCount,
                    usageType,
                    usageSubType,
                } = form;
                if(!isDecoction) return '';
                if (pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                    const usageTypeInfo = this.printData.usageTypes.find((it) => it.type === usageType);
                    if (!usageTypeInfo) return '';

                    const _arr = [];

                    const usageSubTypeInfo = usageTypeInfo.children.find((it) => it.subType === usageSubType);
                    if (usageSubTypeInfo) {
                        _arr.push(usageSubTypeInfo.name);
                    } else {
                        _arr.push(usageTypeInfo.name);
                    }
                    // 加工方式为煎药
                    if (usageType === 1) {
                        if (processBagUnitCount) {
                            _arr.push(`1剂煎 ${processBagUnitCount} 袋`);
                        }
                        if (totalProcessCount) {
                            _arr.push(`共 ${totalProcessCount} 袋`);
                        }
                    }
                    return _arr.join('，');
                }
                return '';
            },
        }
    }
</script>

<style lang="scss">
@import "../../layout/print-layout";
@import "../../../style/reset";

.abc-page-content{
  padding: 8pt;
  box-sizing: border-box;
  overflow: hidden;
  font-family: "Microsoft YaHei", "微软雅黑";
}

.remark-col {
  position: relative;
  padding-left: 33pt;
  font-size: 10pt;
  line-height: 12pt;
  font-weight: 300;

  .label {
    position: absolute;
    top: 6pt;
    left: 0;
    width: 30pt;
  }
}
.bold-text {
  font-weight: bold;
}
.list-title {
  padding: 4pt 0;
  margin-left: 18pt;
  font-size: 10pt;
  font-weight: bold;
}
.remark-row {
  margin-bottom: 6pt;
}

.first-remark-row {
  .remark-col {
    padding-top: 6pt;
    border-top: 1px dashed #000000;
  }
  &.external-usage-row {
    .remark-col {
      padding-top: 0;
      border-top: none;
      .label {
        top: 0;
      }
    }
  }
}

.last-remark-row {
  margin-bottom: 0;
}

.external-item {
  margin-bottom: 6pt;
  font-size: 10pt;
  line-height: 12pt;
  font-weight: bold;
}

[data-type~=footer] {
  padding-top: 10pt;
}

.next-page {
  font-size: 8pt;
  text-align: center;
  font-weight: lighter;
  position: relative;
}

.next-page_align_footer {
  position: absolute;
  top: -2pt;
  left: 0;
  width: 100%;
}

.prev-page {
  position: absolute;
  bottom: 8px;
  left: 60px;
  font-size: 8pt;
  font-weight: lighter;
}
.label {
  font-weight: normal;
}
</style>
