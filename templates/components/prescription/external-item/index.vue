<template>
    <print-row
        class="external-item"
        style="margin-bottom: 3pt;"
    >
        <print-col
            v-for="item in formItems"
            :key="item.id"
            :span="6"
            class="external-col"
        >
            <template v-if="type === 'goods'">
                <span
                    class="external-name"
                    overflow
                >
                    {{ item.name }}
                </span>
                <span
                    class="external-count"
                >
                    {{ item.unitCount }}<span style="line-height: 15pt;">{{ item.unit || 'g' }}</span>
                </span>
            </template>
            <template v-if="type === 'acupoints'">
                <span class="acupoint-name">
                    [{{ item.position }}]{{ item.name }}
                </span>
            </template>
        </print-col>
    </print-row>
</template>

<script>
    import PrintRow from '../../layout/print-row.vue'
    import PrintCol from '../../layout/print-col.vue'
    export default {
        name: "ExternalPr",
        components: {
            PrintRow,
            PrintCol,
        },
        props: {
            formItems: {
                type: Object,
                required: true,
            },
            type: {
                type: String,
                default: 'goods',
            }
        },
    }
</script>

<style lang="scss">
@import "index";
.external-item .external-col .acupoint-name {
  font-weight: normal;
}
</style>
