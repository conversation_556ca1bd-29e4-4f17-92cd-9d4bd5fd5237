<template>
    <div class="report-common-header">
        <template v-if="isSinglePage">
            <div
                class="organ-name"
            >
                {{ coverConfig.institutionName }}
            </div>
            <div
                class="report-title"
            >
                {{ printData.typeName }}报告单
            </div>
        </template>

        <template v-else>
            <div class="report-title-no-single">
                {{ printData.typeName }}报告单
            </div>
        </template>

        <div
            v-if="headerItemList.length"
            class="base-field-list"
        >
            <div
                v-for="(headerItem, i) in headerItemList"
                :key="i"
                :style="{
                    width: headerItem.width || '33%'
                }"
                class="header-item"
            >
                <span>
                    {{ headerItem.label }}:
                </span>

                <span>
                    {{ headerItem.value }}
                </span>
            </div>
        </div>
    </div>
</template>

<script>
    import PrintRow from '../../layout/print-row.vue';
    import PrintCol from '../../layout/print-col.vue';

    export default {
        name: 'CommonHeader',

        components: {
            PrintRow,
            PrintCol,
        },
        
        props: {
            headerItemList: {
                type: Array,
                default: () => [],
            },

            printData: {
                type: Object,
                default: () => ({}),
            },

            coverConfig: {
                type: Object,
                default: () => ({}),
            },

            isSinglePage: Boolean,
        }
    }
</script>

<style lang='scss'>
@import "../../layout/_print-layout";

$border-color: #D7D7D7;

.report-common-header {
    margin-bottom: 12pt;
    
    .organ-name {
        color: #000000;
        font-size: 17pt;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        letter-spacing: 0.51pt;
        margin-bottom: 6pt;
        text-align: center;
    }

    .report-title {
        text-align: center;
        font-size: 12pt;
        font-weight: 400;
        margin-bottom: 16pt;
    }

    .report-title-no-single {
        font-size: 12pt;
        font-weight: 500;
        margin-bottom: 8pt;
    }

    .base-field-list {
        padding: 6pt 0;
        border-top: 0.5pt dashed $border-color;
        border-bottom: 0.5pt dashed $border-color;
        display: flex;
        gap: 6pt 2pt;
        flex-wrap: wrap;
        
        .header-item {
            display: inline-flex;

             >span {
                 font-size: 10pt;
                 line-height: 12pt;
                 font-weight: 400;

                 &:first-child {
                     color:#8B8E98;
                     white-space: nowrap;
                     margin-right: 4pt;
                 }

                 &:last-child {
                    flex: 1;
                 }
             }
          }
    }
}
</style>