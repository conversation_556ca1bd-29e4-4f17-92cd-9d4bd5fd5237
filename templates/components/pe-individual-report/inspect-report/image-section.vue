<template>
    <div
        v-if="imageFiles.length"
        class="section-item image-section-wrapper"
    >
        <div
            v-for="(img,i) in imageFiles"
            :key="i"
            :class="[
                'image-item',
                {
                    'small-image-item': small
                }
            ]"
        >
            <img :src="img.url" />
        </div>
    </div>
</template>

<script>
    export default {
        name: 'ImageSection',

        props: {
            imageFiles: {
                type: Array,
                default: () => [],
            },

            small: {
                type: Boolean,
                default: false,
            }
        },
    }
</script>

<style lang='scss'>
.section-item + .section-item {
    margin-top: 16pt;
}

.image-section-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8pt;

    .image-item {
        width: 32%;
        font-size: 0;

        &.small-image-item {
          width: calc((100% - 24pt) / 4);
        }

        img {
            width: 100%;
        }
    }
}
</style>