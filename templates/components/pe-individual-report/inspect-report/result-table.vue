<template>
    <table
        class="report-result-table"
        data-type="complex-table"
    >
        <colgroup>
            <col
                v-for="(h, i) in renderColumns"
                :key="i"
                :width="h.percentWidth"
                :align="h.align"
            />
        </colgroup>

        <thead>
            <tr class="report-result-table-tr">
                <th
                    v-for="(h, i) in renderColumns"
                    :key="i"
                    class="report-result-table-cell table-header"
                    :align="h.align"
                    :class="{ 'has-border': border }"
                >
                    {{ h.label }}
                </th>
            </tr>
        </thead>

        <tbody
            class="report-result-table-body"
            data-type="group"
        >
            <tr
                v-for="(item, i) in list"
                :key="i"
                data-type="item"
                class="report-result-table-tr"
            >
                <td
                    v-for="column in columns" 
                    :key="column.key"
                    class="report-result-table-cell"
                    :class="{ 'has-border': border }"
                >
                    <template v-if="column.render">
                        <render-props
                            :render="column.render"
                            :value="item"
                            :index="i"
                        ></render-props>
                    </template>

                    <template v-else>
                        {{ item[column.key] }}
                    </template>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
    import RenderProps from '../../render-props/index.vue';

    /**
     * columns: {
     *  width: 1, // 比例
     *  align: 'left', // 对齐方式
     *  span: 2, // 合并列
     *  key: 'name', // 数据key
     *  label: '姓名', // 标题
     *  render: (h, row) => { return ( <div></div> ) }
     * }
     */

    export default {
        name: 'ReportResultTable',

        components: {
            RenderProps,
        },

        props: {
            columns: {
                type: Array,
                default: () => [],
            },

            list: {
                type: Array,
                default: () => [],
            },

            border: Boolean,
        },

        computed: {
            renderColumns() {
                const columns = this.columns || [];

                const total = this.columns.reduce((total, item) => {
                    total += item.width && typeof (item.width * 1) === 'number' ? item.width * 1 : 1;
                    return total;
                }, 0)

                columns.forEach(h => {
                    h.align = h.align || 'left';
                    h.width = h.width && typeof (h.width * 1) === 'number' ? h.width * 1 : 1;
                    h.percentWidth = `${((h.width * 1) / total) * 100}%`;
                })

                return columns;
            },
        },
    }
</script>

<style lang='scss'>
.report-result-table {
    width: calc(100% + 8pt);
    border-spacing: 4pt 0;
    border-collapse: separate;
    margin-left: -4pt;

    .report-result-table-cell {
        padding: 6pt 10pt 6pt 12pt;
        min-height: 24pt;
        color: #000000;
        font-size: 10pt;
        font-style: normal;
        line-height: 12pt;
        font-weight: 350;

        &.table-header {
            height: 20pt;
            padding: 0 10pt 0 12pt;
            color: #FFFFFF;
            font-size: 10pt;
            font-style: normal;
            font-weight: 500;
            background: #8A8E99;
        }
    }

    .report-result-table-body {
        .report-result-table-tr {
            &:nth-child(2n) {
                .report-result-table-cell {
                    background: #F0F0F0;
                }
            }    
        }
    }
}
</style>