<template>
    <div
        class="section-item result-section"
        :style="{ minHeight: resultItem.height ? resultItem.height : 'auto' }"
    >
        <div class="result-title">
            {{ resultItem.label }}
        </div>
    
        <div class="result-content">
            <abc-html :value="resultItem.value"></abc-html>
        </div>

        <slot></slot>
    </div>
</template>

<script>
    import AbcHtml from '../../layout/abc-html.vue';

    export default {
        name: 'ResultSection',

        components: {
            AbcHtml,
        },

        props: {
            resultItem: {
                type: Object,
                default: () => ({}),
            }
        },
    }
</script>

<style lang='scss'>
.result-section {
    .result-title {
        margin-bottom: 12pt;
        font-size: 12pt;
        font-weight: 600;
    }

    .result-content {
        color: #37393F;
        font-size: 10pt;
        font-style: normal;
        font-weight: 350;
        line-height: 14pt; /* 140% */
    }
}
</style>