<template>
    <div class="common-footer">
        <span>
            {{ testerPrefix }}医师：

            <template v-if="printConfig.testerSignature">
                <hand-sign :value="printData.testerHandSign"></hand-sign>
            </template>

            <template v-else>
                <span class="doctor-name">{{ printData.testerName }}</span>
            </template>
        </span>

        <span>
            审核医师：

            <template v-if="printConfig.checkerSignature">
                <hand-sign :value="printData.checkerHandSign"></hand-sign>
            </template>

            <template v-else>
                <span class="doctor-name">{{ printData.checkerName }}</span>
            </template>
        </span>
    </div>
</template>

<script>
    import HandSign from "../../hand-sign/index.vue";

    export default {
        name: 'CommonFooter',

        components: { HandSign },

        props: {
            printData: {
                type: Object,
                default: () => ({}),
            },
            printConfig: {
                type: Object,
                default: () => ({}),
            },
            isExamination: Boolean,

            testerPrefix: {
                type: String,
                default: '报告',
            }
        }
    }
</script>

<style lang='scss'>
.common-footer {
    padding-top: 24pt;
    text-align: right;
    margin-top: 4pt;
    color: #8B8E98;

    span + span {
        margin-left: 32pt;
    }

    span {
        font-size: 10pt;
        font-weight: 400;
        display: inline-flex;
        align-items: center;

        .doctor-name {
            display: inline-block;
            width: 100pt;
            color: #000000;
            border-bottom: 0.83pt solid #D7D7D7;
            padding-bottom: 3pt;
            text-align: center;
        }
    }

}
</style>