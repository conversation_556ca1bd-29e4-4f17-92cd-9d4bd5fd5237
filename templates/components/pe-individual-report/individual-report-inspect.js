import { calRange, calValue2 } from '../../common/medical-transformat.js'
import {
    checkInspectResultIsAbnormal,
    formatAge,
    formatInspectDiagnosisAdvice,
    parseTime,
    checkExaminationResultIsAbnormal
} from '../../common/utils.js'
import {
    EXAM_TYPE,
    INSPECT_DEVICE_TYPE,
    INSPECT_DEVICE_TYPE_TEXT
} from '../../constant/print-constant.js'
import ResultTip from '../hospital-inspect/result-tip.vue'
import NatureDisplay from '../nature-display/index.vue'
import InspectItemResult from '../hospital-inspect/inspect-item-result.vue'

export const examinationColumns = [
    {
        label: '项目',
        width: 4,
        render: (createElement, row) => {
            const res = checkExaminationResultIsAbnormal(row)
            return createElement(
                'div',
                {
                    style: {
                        position: 'relative'
                    }
                },
                [
                    res.isNormal
                        ? null
                        : createElement('span', {
                            style: {
                                position: 'absolute',
                                width: '6pt',
                                height: '6pt',
                                borderRadius: '5pt',
                                backgroundColor: '#FF5C00',
                                left: '-12pt',
                                top: '50%',
                                transform: 'translateY(-50%)'
                            }
                        }),
                    createElement('div', null, `${row.name}${row.enName ? `(${row.enName})` : ''}`)
                ]
            )
        }
    },
    {
        label: '结果',
        width: 4,
        render: (createElement, row) => {
            if (row.valueType !== 'IMAGE') {
                const res = checkExaminationResultIsAbnormal(row)
                return createElement(
                    'div',
                    {
                        style: {
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            width: '100%'
                        }
                    },
                    [
                        createElement(
                            'span',
                            {
                                style: {
                                    color: res.isNormal ? '#000000' : '#FF1818',
                                    fontWeight: res.isNormal ? 'inherit' : 'normal'
                                }
                            },
                            calValue2(row)
                        ),
                        createElement('span', null, row.unit)
                    ]
                )
            }

            return
        }
    },
    {
        label: '提示',
        width: 2,
        render: (createElement, row) => {
            if (row.valueType !== 'IMAGE') {
                return createElement('div', null, [
                    createElement(NatureDisplay, {
                        props: {
                            item: row
                        }
                    })
                ])
            }

            return
        }
    },
    {
        label: '参考范围',
        width: 3,
        render: (createElement, row) => {
            return createElement(
                'div',
                {
                    style: {
                        'word-break': 'break-word',
                        'white-space': 'normal'
                    }
                },
                calRange(row)
            )
        }
    }
]

export const clinicalColumns = [
    {
        label: '项目',
        key: 'name',
        width: 1
    },
    {
        label: '结果',
        key: 'result',
        width: 1,
        render: (createElement, row) => {
            return createElement(InspectItemResult, {
                props: {
                    item: row
                }
            })
        }
    }
]

export const normalReportColumns = [
    {
        label: '项目',
        key: 'name',
        width: 1,
        render: (createElement, row) => {
            const { isAbnormal } = checkInspectResultIsAbnormal(row)

            if (isAbnormal) {
                return createElement(
                    'div',
                    {
                        style: {
                            position: 'relative'
                        }
                    },
                    [
                        createElement('span', {
                            style: {
                                position: 'absolute',
                                width: '6pt',
                                height: '6pt',
                                borderRadius: '5pt',
                                backgroundColor: '#FF5C00',
                                left: '-12pt',
                                top: '50%',
                                transform: 'translateY(-50%)'
                            }
                        }),
                        createElement('span', { style: { color: '#FF5C00' } }, row.name)
                    ]
                )
            } else {
                return createElement('span', null, row.name)
            }
        }
    },
    {
        label: '结果',
        key: 'result',
        width: 1,
        render: (createElement, row) => {
            return createElement(InspectItemResult, {
                props: {
                    item: row
                }
            })
        }
    },
    {
        label: '提示',
        key: 'tip',
        width: 1,
        render: (createElement, row) => {
            return createElement(ResultTip, {
                props: {
                    item: row
                }
            })
        }
    },
    {
        label: '参考范围',
        key: 'referenceRange',
        width: 1,
        render: (createElement, row) => {
            console.log(row)
            const { ref } = row
            if (ref.min && ref.max) {
                return createElement('div', null, `${ref.min}-${ref.max}${row.unit}`)
            }

            return ''
        }
    }
]

export default {
    data() {
        return {
            INSPECT_DEVICE_TYPE,
            INSPECT_DEVICE_TYPE_TEXT
        }
    },

    methods: {
        getReportList(individualReport) {
            const _reportList = this.handleMultiplyReport(individualReport.resultsDetail);
            const reportList = this.sortReportList(
                _reportList.map(this.createRenderConfig)
            )

            for (let i = 0; i < reportList.length - 1; i++) {
                const curItem = reportList[i]
                const nextItem = reportList[i + 1]
                // 当前报告顺排且下一个报告顺排, 添加间隔
                if (!this.isSinglePageReport(curItem) && !this.isSinglePageReport(nextItem)) {
                    curItem.needSpace = true
                }
                // 下一个报告是单独一页开始，添加分页
                if (this.isSinglePageReport(nextItem)) {
                    curItem.needAddNewPage = true
                }

                // 当前报告单独一页开始，下一个报告顺排，添加分页标识
                if (this.isSinglePageReport(curItem) && !this.isSinglePageReport(nextItem)) {
                    curItem.needAddNewPage = true
                    nextItem.needHeaderSpace = true
                }
            }

            console.debug(reportList, 'reportList')

            return reportList
        },

        isSinglePageReport(item) {
            return (
                item.type === EXAM_TYPE.inspect &&
                [
                    INSPECT_DEVICE_TYPE.CT,
                    INSPECT_DEVICE_TYPE.DR,
                    INSPECT_DEVICE_TYPE.MG,
                    INSPECT_DEVICE_TYPE.MR,
                    INSPECT_DEVICE_TYPE['彩超'],
                    INSPECT_DEVICE_TYPE['心电图'],
                    INSPECT_DEVICE_TYPE.MDB,
                    INSPECT_DEVICE_TYPE.ASO,
                    INSPECT_DEVICE_TYPE.BODY_COMPOSITION,
                    INSPECT_DEVICE_TYPE.C13_14,
                    INSPECT_DEVICE_TYPE['其他']
                ].includes(item.deviceType)
            )
        },

        isExamination(type) {
            return type === EXAM_TYPE.examination
        },

        isInspect(type) {
            return type === EXAM_TYPE.inspect
        },

        isNormalInspect(deviceType) {
            return +deviceType === INSPECT_DEVICE_TYPE.NORMAL
        },

        isClinicalInspect(deviceType) {
            return [
                INSPECT_DEVICE_TYPE.INTERNAL,
                INSPECT_DEVICE_TYPE.SURGERY,
                INSPECT_DEVICE_TYPE.MOUTH,
                INSPECT_DEVICE_TYPE.ENT,
                INSPECT_DEVICE_TYPE.EYE,
                INSPECT_DEVICE_TYPE.GYNECOLOGY
            ].includes(+deviceType)
        },

        isRadiologyInspect(deviceType) {
            return [
                INSPECT_DEVICE_TYPE.CT,
                INSPECT_DEVICE_TYPE.DR,
                INSPECT_DEVICE_TYPE.MG,
                INSPECT_DEVICE_TYPE.MR
            ].includes(+deviceType)
        },

        isGastroscopyInspect(deviceType) {
            return +deviceType === INSPECT_DEVICE_TYPE['内窥镜']
        },

        isCDUInspect(deviceType) {
            return +deviceType === INSPECT_DEVICE_TYPE['彩超']
        },

        isECGInspect(deviceType) {
            return [
                INSPECT_DEVICE_TYPE['心电图'],
            ].includes(+deviceType)
        },

        isC1314Inspect(deviceType) {
            return [
                INSPECT_DEVICE_TYPE.C13_14,
            ].includes(+deviceType)
        },

        // 动脉硬化报告
        isASOInspect(deviceType) {
            return +deviceType === INSPECT_DEVICE_TYPE.ASO
        },

        isMDBInspect(deviceType) {
            return +deviceType === INSPECT_DEVICE_TYPE.MDB
        },

        isBodyCompositionInspect(deviceType) {
            return +deviceType === INSPECT_DEVICE_TYPE.BODY_COMPOSITION
        },

        isOtherInspect(deviceType) {
            return +deviceType === INSPECT_DEVICE_TYPE['其他']
        },

        // 创建检验渲染配置
        createExaminationRenderConfig(resultItem) {
            const { footer } = this.renderData.config.medicalDocuments.examineReport

            return {
                ...resultItem,
                typeName: resultItem.name,
                columns: examinationColumns,
                imageFiles: resultItem.itemsValue
                    .filter((item) => item.valueType === 'IMAGE')
                    .map((item) => {
                        item.url = item.value
                        return item
                    }),
                itemsValue: resultItem.itemsValue.filter((item) => item.valueType !== 'IMAGE'),
                remark: resultItem.remark,
                header: [],
                resultList: [
                    {
                        label: '备注',
                        value: resultItem.remark,
                        isHidden: !resultItem.remark
                    }
                ].filter((r) => !r.isHidden),
                printConfig: { footer },
                testerHandSign: resultItem.tester?.handSign,
                checkerHandSign: resultItem.checker?.handSign
            }
        },

        // 创建一般检查渲染配置
        createNormalRenderConfig(resultItem) {
            return {
                ...resultItem,
                typeName: INSPECT_DEVICE_TYPE_TEXT[resultItem.deviceType],
                columns: normalReportColumns,
                diagnosisAdvice: formatInspectDiagnosisAdvice(
                    resultItem.examinationReport?.diagnosisEntryItems || []
                )
            }
        },

        // 创建临床检查渲染配置
        createClinicalRenderConfig(resultItem) {
            return {
                ...resultItem,
                typeName: INSPECT_DEVICE_TYPE_TEXT[resultItem.deviceType],
                columns: clinicalColumns,
                diagnosisAdvice: formatInspectDiagnosisAdvice(
                    resultItem.examinationReport?.diagnosisEntryItems || []
                )
            }
        },

        // 创建放射渲染配置
        createRadiologyRenderConfig(resultItem) {
            const reportConfigEnum = Object.freeze({
                [INSPECT_DEVICE_TYPE.CT]: this.renderData.config?.medicalDocuments?.ctReport || {},
                [INSPECT_DEVICE_TYPE.DR]: this.renderData.config?.medicalDocuments?.drReport || {},
                [INSPECT_DEVICE_TYPE.MR]: this.renderData.config?.medicalDocuments?.mrReport || {},
                [INSPECT_DEVICE_TYPE.MG]: this.renderData.config?.medicalDocuments?.mgReport || {}
            })

            const { footer = {} } = reportConfigEnum[resultItem.deviceType] || {}

            return {
                ...resultItem,
                header: this.getHeaderDisplayItemList(resultItem),
                resultList: this.getRadiologyResultItemList(resultItem),
                typeName: INSPECT_DEVICE_TYPE_TEXT[resultItem.deviceType],
                imageFiles: resultItem.examinationReport.imageFiles || [],
                printConfig: {
                    footer: {
                        testerSignature: footer.reportDoctorSignature || 0,
                        checkerSignature: footer.auditDoctorSignature || 0
                    }
                },
                testerHandSign: resultItem.tester?.handSign,
                checkerHandSign: resultItem.checker?.handSign
            }
        },

        // 创建胃镜渲染配置
        createGastroscopyRenderConfig(resultItem) {
            const header = this.getHeaderDisplayItemList(resultItem)
            header.splice(-1, 0, {
                label: '镜型',
                value: resultItem.examinationReport.deviceModelDesc
            })
            return {
                ...resultItem,
                header,
                resultList: this.getGastroscopyResultItemList(resultItem),
                typeName: INSPECT_DEVICE_TYPE_TEXT[resultItem.deviceType],
                imageFiles: resultItem.examinationReport.imageFiles || [],
                printConfig: {}
            }
        },

        // 创建彩超渲染配置
        createCDURenderConfig(resultItem) {
            const { footer = {} } = this.renderData.config?.medicalDocuments?.cdusReport || {}

            return {
                ...resultItem,
                header: this.getHeaderDisplayItemList(resultItem),
                resultList: this.getCDUResultItemList(resultItem),
                typeName: INSPECT_DEVICE_TYPE_TEXT[resultItem.deviceType],
                imageFiles: resultItem.examinationReport.imageFiles || [],
                printConfig: {
                    footer: {
                        testerSignature: footer.recordDoctorSignature || 0,
                        checkerSignature: footer.operateDoctorSignatur || 0
                    }
                },
                testerHandSign: resultItem.tester?.handSign,
                checkerHandSign: resultItem.checker?.handSign
            }
        },

        // 创建心电图/C13_14渲染配置
        createECGRenderConfig(resultItem) {
            return {
                ...resultItem,
                header: this.getHeaderDisplayItemList(resultItem),
                resultList: this.getECGResultItemList(resultItem),
                typeName: INSPECT_DEVICE_TYPE_TEXT[resultItem.deviceType],
                imageFiles: resultItem.examinationReport.imageFiles || [],
                singleImg: resultItem.examinationReport.imageFiles?.[0]?.url,
                printConfig: {}
            }
        },

        // 创建骨密度渲染配置 - 暂时不用改配置
        // createMDBRenderConfig(resultItem) {
        //     return {
        //         ...resultItem,
        //         header: this.getHeaderDisplayItemList(resultItem),
        //         typeName: INSPECT_DEVICE_TYPE_TEXT[resultItem.deviceType],
        //     }
        // },

        // 创建其他检查渲染配置
        createOtherInspectRenderConfig(resultItem){
            return {
                ...resultItem,
                header: this.getHeaderDisplayItemList(resultItem),
                typeName: INSPECT_DEVICE_TYPE_TEXT[resultItem.deviceType],
                imageFiles: resultItem.examinationReport.imageFiles || [],
                resultList: this.getOtherResultItemList(resultItem),
                printConfig: {}
            }
        },
        createRenderConfig(resultItem) {
            const handlerList = {
                [INSPECT_DEVICE_TYPE.NORMAL]: this.createNormalRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.INTERNAL]: this.createClinicalRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.SURGERY]: this.createClinicalRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.MOUTH]: this.createClinicalRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.ENT]: this.createClinicalRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.EYE]: this.createClinicalRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.GYNECOLOGY]: this.createClinicalRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.CT]: this.createRadiologyRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.DR]: this.createRadiologyRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.MR]: this.createRadiologyRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.MG]: this.createRadiologyRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE['内窥镜']]: this.createGastroscopyRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE['彩超']]: this.createCDURenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.MDB]: this.createECGRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE['心电图']]: this.createECGRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.C13_14]: this.createECGRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.ASO]: this.createECGRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE.BODY_COMPOSITION]: this.createECGRenderConfig.bind(this),
                [INSPECT_DEVICE_TYPE['其他']]: this.createOtherInspectRenderConfig.bind(this)
            }

            let handleFn
            if (resultItem.type === EXAM_TYPE.inspect) {
                handleFn = handlerList[resultItem.deviceType]
            }

            if (resultItem.type === EXAM_TYPE.examination) {
                handleFn = this.createExaminationRenderConfig.bind(this)
            }

            return typeof handleFn === 'function' ? handleFn(resultItem) : resultItem
        },

        getHeaderDisplayItemList(resultItem) {
            const { orderNo, doctorDepartmentName, created, peNo, name } = resultItem

            const physicalExaminationHeader = [
                {
                    label: '检查单号',
                    value: orderNo
                },
                {
                    label: '申请科室',
                    value: doctorDepartmentName
                },
                {
                    label: '体检号',
                    value: peNo
                },
                {
                    label: '申请日期',
                    value: parseTime(created, 'y-m-d h:i', true)
                },
                {
                    label: '项目名称',
                    value: name,
                    width: '100%'
                }
            ]

            return physicalExaminationHeader
        },

        getExaminationHeaderList(resultItem) {
            const { patient } = this

            const age = patient.age && formatAge(patient.age, { monthYear: 12, dayYear: 1 })

            return [
                {
                    label: '姓名',
                    value: `${patient.name}${patient.sex ? ` ${patient.sex}` : ''}${age ? ` ${age}` : ''}`
                },
                {
                    label: '体检号',
                    value: resultItem.peNo
                },
                {
                    label: '送检科室',
                    value: resultItem.departmentName || '--'
                },
                {
                    label: '送检医生',
                    value: resultItem.doctorName || '--'
                },
                {
                    label: '样本类型',
                    value: resultItem.sampleType
                },
                {
                    label: '样本条码',
                    value: resultItem.orderNo
                }
            ]
        },

        getRadiologyResultItemList(resultItem) {
            const prefix = '影像'

            const { videoDescription, diagnosisEntryItems = [] } = resultItem.examinationReport

            const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems)

            return [
                {
                    label: `${prefix}所见`,
                    value: videoDescription || '',
                    height: '180pt'
                },
                {
                    label: `诊断意见`,
                    value: diagnosisResult || '',
                    height: '50pt'
                }
            ]
        },

        getGastroscopyResultItemList(resultItem) {
            const {
                videoDescription,
                diagnosisEntryItems = [],
                suggestion,
                inspectionSite
            } = resultItem.examinationReport

            const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems)

            return [
                {
                    label: '检查所见',
                    value: videoDescription || ''
                },
                {
                    label: `诊断意见`,
                    value: diagnosisResult || ''
                },
                {
                    label: `活检部位`,
                    value: inspectionSite || ''
                },
                {
                    label: '医生建议',
                    value: suggestion || ''
                }
            ]
        },

        getCDUResultItemList(resultItem) {
            const prefix = '超声'

            const { videoDescription, diagnosisEntryItems = [] } = resultItem.examinationReport

            const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems)

            return [
                {
                    label: `${prefix}所见`,
                    value: videoDescription || ''
                },
                {
                    label: `诊断意见`,
                    value: diagnosisResult || ''
                }
            ]
        },

        getECGResultItemList(resultItem) {
            const { diagnosisEntryItems = [] } = resultItem.examinationReport

            const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems)

            return [
                {
                    label: '诊断意见',
                    value: diagnosisResult || ''
                }
            ]
        },

        getOtherResultItemList(resultItem) {
            const { videoDescription, diagnosisEntryItems = [] } = resultItem.examinationReport

            const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems)

            return [
                {
                    label: '检查描述',
                    value: videoDescription || ''
                },
                {
                    label: '诊断意见',
                    value: diagnosisResult || ''
                }
            ]
        },

        to8(value = '') {
            value === null && (value = '')
            const srcStr = '00000000'
            return srcStr.slice(0, -`${value}`.length).concat(value)
        },

        sortReportList(list = []) {
            const weightMap = {
                [EXAM_TYPE.inspect]: {
                    [INSPECT_DEVICE_TYPE.NORMAL]: 1,
                    [INSPECT_DEVICE_TYPE.INTERNAL]: 2,
                    [INSPECT_DEVICE_TYPE.SURGERY]: 3,
                    [INSPECT_DEVICE_TYPE.MOUTH]: 4,
                    [INSPECT_DEVICE_TYPE.ENT]: 5,
                    [INSPECT_DEVICE_TYPE.EYE]: 6,
                    [INSPECT_DEVICE_TYPE.GYNECOLOGY]: 7,
                    [INSPECT_DEVICE_TYPE['彩超']]: 9,
                    [INSPECT_DEVICE_TYPE.DR]: 10,
                    [INSPECT_DEVICE_TYPE.CT]: 11,
                    [INSPECT_DEVICE_TYPE.MG]: 12,
                    [INSPECT_DEVICE_TYPE.MR]: 13,
                    [INSPECT_DEVICE_TYPE['心电图']]: 14,
                    [INSPECT_DEVICE_TYPE.MDB]: 15,
                    [INSPECT_DEVICE_TYPE.ASO]: 16,
                    [INSPECT_DEVICE_TYPE.BODY_COMPOSITION]: 17,
                    [INSPECT_DEVICE_TYPE.C13_14]: 18,
                    [INSPECT_DEVICE_TYPE['内窥镜']]: 19,
                    [INSPECT_DEVICE_TYPE['其他']]: 20
                },
                [EXAM_TYPE.examination]: 8
            }

            const sortedList = list

            return sortedList
                .map((item) => {
                    item.weight =
                        item.type === EXAM_TYPE.inspect
                            ? weightMap[item.type]?.[item.deviceType]
                            : weightMap[item.type]

                    return item
                })
                .sort((a, b) => {
                    return a.weight - b.weight
                })
        },

        handleMultiplyReport(reportList) {
            return reportList.reduce((res, report) => {
                let additionalReports = [];
                if(report.additionalExaminationSheetReports?.length) {
                    additionalReports = report.additionalExaminationSheetReports.map(item => {
                        return {
                            ...report,
                            examinationReport: item,
                        }
                    })
                }
                return res.concat([report, ...additionalReports]);
            }, [])
        },
    }
}
