<template>
    <div class="pe-introduction-wrapper">
        <p class="greet">
            尊敬的
            <span class="patient-name">{{ patientName }}</span> 
            {{ sexText }}，您好！
        </p>

        <abc-html
            class="introduction-content"
            :value="config.content"
        >
        </abc-html>

        <p class="explain">
            报告说明：本报告仅作临床参考，不作任何证明依据
        </p>

        <template v-if="config.abnormalImage">
            <div class="abnormal-display">
                <h3>体检重要异常结果图示</h3>

                <div class="item-statistic">
                    <div>
                        <span class="count">{{ resultsDetail.length }}项</span>
                        <span class="label">总指标</span>
                    </div>

                    <div class="spilt-line"></div>

                    <div>
                        <span class="count abnormal">{{ physicalPosition.abnormalCount }}项</span>
                        <span class="label">异常结果</span>
                    </div>
                </div>

                <div class="abnormal-img-display">
                    <div class="left-position">
                        <div 
                            v-for="(p,idx) in physicalPosition.left"
                            :key="idx"
                            class="position-item"
                            :class="{
                                'is-abnormal': p.isAbnormal
                            }"
                        >
                            <span class="the-point"></span>
                            <span>
                                {{ p.value }}
                            </span>
                        </div>
                    </div>

                    <div 
                        class="the-img"
                        :class="{
                            male: isMale,
                            female: isFemale
                        }"
                    >
                        <div class="bottom-title">
                            <span class="the-point"></span>
                            <span>异常结果</span>
                        </div>
                    </div>

                    <div class="right-position">
                        <div 
                            v-for="(p,idx) in physicalPosition.right"
                            :key="idx"
                            class="position-item"
                            :class="{
                                'is-abnormal': p.isAbnormal
                            }"
                        >
                            <span>
                                {{ p.value }}
                            </span>

                            <span class="the-point"></span>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
    import AbcHtml from '../layout/abc-html.vue';
    import { GenderEnumStr } from '../../constant/physical-examination.js';
    import { EXAM_TYPE } from '../../constant/print-constant.js';

    export default {
        name: "PeIntroduction",

        components: {
            AbcHtml,
        },

        props: {
            patient: {
                type: Object,
                default: () => ({}),
            },
            config: {
                type: Object,
                default: () => ({}),
            },
            resultsDetail: {
                type: Array,
                default: () => {
                    return [];
                }
            },
        },

        computed: {
            sexText() {
                return this.isMale ? '男士' : '女士'
            },

            isMale() {
                return this.patient.sex === '男';
            },

            isFemale() {
                return this.patient.sex === '女';
            },

            patientName() {
                return this.patient.name;
            },

            abnormalItemTextJoin() {
                const abnormalItems = this.resultsDetail.filter(item => {
                    return item.isAbnormal && item.type !== EXAM_TYPE.examination;
                }).reduce((res, cur) => {
                    const diagnosisEntryItems = cur?.examinationReport?.diagnosisEntryItems || [];
                    res.push(...diagnosisEntryItems);
                    return res;
                }, []);

                return abnormalItems.map(item => {
                    return item.name;
                }).join('，');
            },

            physicalPosition() {
                const res =  {
                    left: [
                        '脑',
                        '眼部',
                        '咽',
                        '喉',
                        '心',
                        '肝',
                        '胆',
                        '肾',
                        '输尿管',
                        this.isMale ? '前列腺' : '子宫',
                        '膀胱',
                        this.isMale ? '睾丸' : '卵巢',
                        '骨骼',
                        '其他部位',
                        '实验室检查',
                    ],
                    right: [
                        '耳',
                        '鼻咽部',
                        '口腔',
                        '甲状腺',
                        '食管',
                        '胸腺',
                        '乳房',
                        '肺',
                        '脾',
                        '胃',
                        '胰',
                        '肛肠',
                        '血管',
                        '一般检查',
                    ]
                };

                res.left = res.left.map(item => {
                    return {
                        value: item,
                        isAbnormal: this.checkIsAbnormal(item),
                    }
                })
                
                res.right = res.right.map(item => {
                    return {
                        value: item,
                        isAbnormal: this.checkIsAbnormal(item),
                    }
                })

                res.abnormalCount = [
                    ...res.left,
                    ...res.right,
                ].filter(item => item.isAbnormal).length;

                return res;
            },

        },

        methods: {
            checkIsAbnormal(v) {
                if(v === '实验室检查') {
                    const examinationItems = this.resultsDetail.filter(item => item.type === EXAM_TYPE.examination);
                    return examinationItems.findIndex(item => item.isAbnormal === 1) !== -1;
                }
                return this.abnormalItemTextJoin.includes(v);
            },
        }
    }
</script>

<style lang='scss' scoped>
.pe-introduction-wrapper {
    .greet {
        font-size: 12pt;
        font-weight: 600;
        padding-bottom: 12pt;

        .patient-name {
            margin: 0 2pt;
            color: #FF5C00;
        }
    }

    .introduction-content {
        font-size: 10pt;
        font-weight: 350;
        line-height: 18pt;
        color: #37393F;
        height: 180pt;
        overflow: hidden;
    }

    .explain {
        padding-top: 12pt;
        font-size: 10pt;
        font-weight: 600;
        line-height: 18pt;
        padding-bottom: 12pt;
        border-bottom: 0.8pt dashed #D7D7D7;
    }

    .abnormal-display {
        >h3 {
            font-size: 20pt;
            font-weight: 600;
            letter-spacing: 0.5pt;
            padding: 12pt 0;
            text-align: center;
        }
        .item-statistic {
            display: flex;
            justify-content: center;
            align-items: center;

            >div:not(.spilt-line) {
                display: inline-flex;
                align-items: center;
                flex-direction: column;

                .count {
                    font-size: 13pt;
                    font-weight: 600;
                    padding-bottom: 4pt;

                    &.abnormal {
                        color: #FF5C00;
                    }
                }


                .label {
                    color: #8B8E98;
                    font-size: 8pt;
                    font-weight: 400;
                }
            }

            .spilt-line {
                width: 1pt;
                height: 27pt;
                background-color: #D7D7D7;
                margin: 0 28pt;
            }
        }

        .abnormal-img-display {
            display: flex;
            justify-content: center;

            .left-position,
            .right-position {
                display: flex;
                flex-direction: column;
                align-items: center;
                
                .position-item {
                    width: 100%;
                    display: inline-flex;
                    align-items: center;
                    color: #000000;
                    font-size: 10pt;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;

                    &.is-abnormal {
                        justify-content: space-between;
                        color: #FF5C00;

                        .the-point {
                            width: 6pt;
                            height: 6pt;
                            background-color: #FF5C00;
                            border-radius: 50%;
                        }
                    }

                    & + .position-item {
                        margin-top: 6pt;
                    }
                }
            }

            .the-img {
                width: 434.7pt;
                height: 369pt;
                background-position: center center;
                background-repeat: no-repeat;
                background-size: cover;
                position: relative;

                &.male {
                    background-image: url("https://cd-cis-static-common.oss-cn-chengdu.aliyuncs.com/img/male%403x.png");
                }

                &.female {
                    background-image: url("https://cd-cis-static-common.oss-cn-chengdu.aliyuncs.com/img/female%403x.png");
                }

                .bottom-title {
                    position: absolute;
                    bottom: 10pt;
                    left: 50%;
                    transform: translateX(-50%);
                    line-height: 12pt;
                    font-size: 9pt;
                    font-style: normal;
                    font-weight: 400;

                    .the-point {
                        display: inline-block;
                        width: 8pt;
                        height: 8pt;
                        background-color: #FF5C00;
                        border-radius: 50%;
                    }
                }
            }
            
            .left-position {
                width: 74pt;
                padding-top: 25pt;
                margin-right: -66pt;

                .position-item {
                    justify-content: flex-end;
                }
            }

            .right-position {
                width: 64pt;
                padding-top: 25pt;
                margin-left: -58pt;

                .position-item {
                    justify-content: flex-start;
                }
            }
        }
    }

}
</style>