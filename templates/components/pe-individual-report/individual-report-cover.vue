<template>
    <div
        class="individual-report-cover"
    >
        <div class="individual-report-cover-header">
            <div
                v-if="config.institutionLogoUrl"
                class="logo-img-wrapper"
            >
                <img 
                    :src="config.institutionLogoUrl" 
                />
            </div>

            <span class="organ-name">
                {{ institutionName }}
            </span>
        </div>

        <div class="individual-report-cover-body">
            <div class="individual-report-cover-title">
                <div class="title-chinese">
                    <span
                        v-for="k in mainTitle"
                        :key="k"
                    >
                        {{ k }}
                    </span>
                </div>

                <div class="title-english">
                    <span
                        v-for="k in subTitle"
                        :key="k"
                    >
                        {{ k }}
                    </span>
                </div>
            </div>

            <div
                class="individual-report-cover-info"
                :style="personalInfoStyle"
            >
                <div class="user-info">
                    <div class="patient-info">
                        <span>{{ patient.name }}</span>
                        <span>{{ patient.sex }}</span>
                        <span>{{ formatAge(patient.age) }}</span>
                    </div>
                    <div class="ph-no">
                        <template v-if="config.personInfo.peOrderNo">
                            <span>体检单号</span>
                            {{ orderNo }}
                        </template>
                    </div>
                </div>

                <div 
                    v-for="(item, k) in baseInfo"
                    :key="k"
                    class="info-item"
                >
                    <span>
                        {{ item.label }}
                    </span>
                    
                    <span>
                        {{ item.value }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {formatAddress, formatAge, parseTime} from "../../common/utils.js";

    export default {
        name: 'IndividualReportCover',
        props: {
            patient: Object,
            businessTime: String,
            orderNo: String,
            reportReleased: String,
            config: {
                type: Object,
                default: () => ({}),
            },
            address: {
                type: Object,
                default: () => ({}),
            },
            printCompany: {
                type: String,
                default: '',
            },
        },
        computed: {
            mainTitle() {
                return this.config.mainTitle || [];
            },

            subTitle() {
                return( this.config.subTitle || '').split(' ');
            },

            institutionName() {
                return this.config.institutionName || '';
            },

            baseInfo() {
                const {
                    personInfo: {
                        peDate,
                        peOrganName,
                        reportDate,
                        mobile,
                        company,
                        address,
                    },
                    institutionName,
                } = this.config;

                const isShow = (v) => v === 1;

                return [
                    {
                        label: '体检日期：',
                        value: parseTime(this.businessTime, 'y-m-d', true),
                        isShow: isShow(peDate),
                    },
                    {
                        label: '体检单位：',
                        value: institutionName,
                        isShow: isShow(peOrganName),
                    },
                    {
                        label: '报告日期：',
                        value: this.reportReleased ? parseTime(this.reportReleased, 'y-m-d', true) : '',
                        isShow: isShow(reportDate),
                    },
                    {
                        label: '联系手机：',
                        value: this.patient.mobile || '',
                        isShow: isShow(mobile),
                    },
                    {
                        label: '所属单位：',
                        value: this.printCompany,
                        isShow: isShow(company),
                    },
                    {
                        label: '联系地址：',
                        value: formatAddress(this.address),
                        isShow: isShow(address),
                    },
                ].filter(v => v.isShow);
            },

            ySpace() {
                const canUseMaxHeight = 480;
                const willShowItems = this.baseInfo.length;
                const singleItemHeight = 28;
                return (canUseMaxHeight - willShowItems * singleItemHeight) / 40 ;
            },

            personalInfoStyle() {
                const SPACE = 10;
                const MAX_X_AXIS = 46;
                const MAX_Y_AXIS = 40;

                const {
                    personInfo: {
                        xAxis,
                        yAxis,
                    },
                } = this.config;

                const covertNumber = (v, maxValue) => {
                    if(/^\d+$/.test(v)) {
                        let value = Number(v);
                        return value > maxValue ? maxValue : value;
                    }
                    return 0;
                }

                const _xAxis = covertNumber(xAxis, MAX_X_AXIS);
                const _yAxis = covertNumber(yAxis, MAX_Y_AXIS);

                return {
                    'margin-top': `${_yAxis * this.ySpace}pt`,
                    'margin-left': `${(_xAxis - 23) * SPACE}pt`,
                };
            }
        },

        methods: {
            formatAge,
        }
    };
</script>

<style lang='scss'>
.individual-report-cover {
    .individual-report-cover-header {
        display: flex;
        align-items: center;
        .logo-img-wrapper {
            width: 48px;
            font-size: 0;

            img {
                width: 100%;
                object-fit: contain;
            }
        }
        .organ-name {
            margin-left: 12pt;
            font-size: 14pt;
            font-weight: 400;
        }
    }

    .individual-report-cover-body {
        display: flex;
        flex-direction: column;
        align-items: center;

        .individual-report-cover-title {
            margin-top: 50pt;
            width: 276pt;

            .title-chinese {
                margin-bottom: 4pt;
                font-weight: 600;
                font-size: 36pt;
                word-spacing: 12pt;
                display: flex;
                line-height: 48pt;
                justify-content: space-between;
            }

            .title-english {
                margin-top: 4pt;
                font-size: 16pt;
                line-height: 21pt;
                display: flex;
                justify-content: space-between;
                font-weight: 300;
                color: #8B8E98;
            }
        }

        .individual-report-cover-info {
            margin-top: 165pt;
            font-size: 11pt;
            font-weight: 400;
            width: 276pt;

            .user-info {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                padding-bottom: 6pt;
                border-bottom: 0.6px dashed #D7D7D7;
                margin-bottom: 12pt;

                .patient-info {
                    font-size: 16pt;
                    font-weight: 600;
                    display: inline-flex;
                    gap: 10pt;

                    >span:last-child {
                        white-space: nowrap;
                    }
                }

                .ph-no {
                    font-size: 10pt;
                    color: #8B8E98;
                    line-height: 12pt;
                }
            }

            .info-item {
                display: flex;
                align-items: center;
                gap: 10pt;
                
                >span {
                    padding: 6pt 0;
                }

                >span:first-child {
                    width: 60pt;
                    color: #8B8E98;
                }

                >span:last-child {
                    width: 221pt;
                    border-bottom: 0.6px solid #D7D7D7;
                }
            }
        }
    }
}
</style>