<template>
    <div
        class="positive-data-advice-wrapper"
        data-type="mix-box"
    >
        <div data-type="group">
            <div
                class="positive-data-advice-title"
                data-type="item"
            >
                主要异常
            </div>

            <div
                v-for="(item, index) in abnormalItem"
                :key="item.id"
                data-type="item"
                class="advice-item"
            >
                <p class="advice-item-name">
                    {{ index + 1 > 10 ? index + 1 : `0${index + 1}` }}
                    <span style="margin-left: 8pt">
                        {{ item.name }}
                    </span>
                </p>
                <div
                    class="advice-item-content"
                    v-html="renderReport(item)"
                >
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { parseTime } from "../../common/utils.js";

    export default {
        name: "PositiveDataAdvice",
        props: {
            resultsDetail: {
                type: Array,
                default: () => {
                    return [];
                }
            },
        },
        computed: {
            abnormalItem() {
                return this.resultsDetail.filter( item => {
                    return item.isAbnormal;
                })
            },
        },
        methods: {
            abnormalTagText({tag, tagText}) {
                if(tag === 10) {
                    return '升高';
                }
                if(tag === 20) {
                    return '降低';
                }
                if(tag === 30) {
                    return '阳性'
                }
                if(tag === 40) {
                    return tagText;
                }
                return '';
            },
            renderReport(item) {
                const {examinationReport} = item;
                if(!examinationReport) return '';
                const {diagnosisEntryItems} = examinationReport;
                if(diagnosisEntryItems && diagnosisEntryItems.length) {
                    let res = [];
                    diagnosisEntryItems.forEach( (item, index) => {
                        let resStr = `${index+1}.${item.name} `
                        if(item.tag) {
                            resStr += `<span style="margin-left: 4pt">${this.abnormalTagText(item)}</span>`
                        }
                        res.push(`<div>${resStr}</div>`)
                    })
                    return res.join('')
                }
                return '';

            },

            parseTime,
        }
    }
</script>

<style lang="scss">
.positive-data-advice-wrapper {
    .positive-data-advice-title {
        font-size: 17pt;
        font-weight: 600;
        padding-bottom: 16pt;
        letter-spacing: 0.5pt;
        text-align: center;
    }

    .advice-item {
        & + .advice-item {
            padding-top: 16pt;
        }

        .advice-item-name {
            color: #8B8E98;
            font-size: 12pt;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            padding-bottom: 7pt;
        }

        .advice-item-content {
            color: #37393F;
            font-size: 11pt;
            font-style: normal;
            font-weight: 600;
            line-height: 16pt;
            padding-left: 26pt;
        }
    }
}
</style>