<template>
    <table
        class="settlement-preview-table"
        data-type="group"
    >
        <thead>
            <tr>
                <th class="settlement-order-no">
                    货物或应税劳务、服务
                </th>
                <th class="settlement-type">
                    规格型号
                </th>
                <th class="settlement-time">
                    单位
                </th>
                <th class="settlement-operator">
                    数量
                </th>
                <th class="settlement-kind">
                    单价
                </th>
                <th class="settlement-count">
                    价税合计
                </th>
                <th class="settlement-price">
                    金额
                </th>
                <th class="settlement-tax">
                    税率
                </th>
                <th class="settlement-clinic">
                    税额
                </th>
            </tr>
        </thead>
        <tbody>
            <tr
                v-for="item in items"
                :key="item.id"
                data-type="item"
            >
                <td class="settlement-order-no">
                    {{ item.name }}
                </td>
                <td class="settlement-type">
                    {{ item.spec }}
                </td>
                <td class="settlement-time">
                    {{ item.unit }}
                </td>
                <td class="settlement-operator">
                    {{ item.count }}
                </td>
                <td class="settlement-kind">
                    {{ item.unitPrice | formatMoney }}
                </td>
                <td class="settlement-count">
                    {{ item.amount | formatMoney }}
                </td>
                <td class="settlement-price">
                    {{ item.amountExcludingTax | formatMoney }}
                </td>
                <td class="settlement-tax">
                    {{ item.taxRat }}%
                </td>
                <td class="settlement-clinic">
                    {{ item.tax | formatMoney }}
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
    import {formatMoney} from "../../common/utils";

    export default {
        name: 'PreviewTable',
        filters: {
            formatMoney,
        },
        props: {
            items: {
                type: Array,
                required: true,
            },
        },
    }
</script>

<style lang="scss">
.settlement-preview-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;

    th,
    td {
        flex-wrap: wrap;
        padding: 8px;
        border: 1px solid #000000;
    }

    th {
        font-size: 13px;
        line-height: 16px;
    }

    td {
        font-size: 13px;
        font-weight: 300;
        line-height: 16px;
    }

    .settlement-order-no {
        width: 20%;
        text-align: left;
    }

    .settlement-type {
        text-align: left;
    }

    .settlement-time {
        width: 7%;
        text-align: left;
    }

    .settlement-operator {
        width: 7%;
        text-align: right;
    }

    .settlement-kind {
        width: 11%;
        text-align: right;
    }

    .settlement-count {
        width: 11%;
        text-align: right;
    }

    .settlement-price {
        width: 11%;
        text-align: right;
    }

    .settlement-tax {
        width: 7%;
        text-align: right;
    }

    .settlement-clinic {
        width: 11%;
        text-align: right;
    }
}
</style>
