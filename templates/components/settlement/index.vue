<template>
    <div class="settlement-wrapper">
        <div data-type="header">
            <div class="settlement-header">
                {{ title }}-{{ orderNo }}
            </div>

            <div
                v-if="supplierName"
                class="settlement-supplier"
            >
                {{ supplierName }}
            </div>
        </div>
        
        <div
            class="settlement-content"
            data-type="mix-box"
        >
            <application-table
                v-if="isApplication"
                :settlements="settlements"
            ></application-table>
            <preview-table
                v-else
                :items="items"
            ></preview-table>
        </div>
        
        <div
            data-type="footer"
            class="settlement-footer"
        >
            <div class="settlement-total-info">
                <span v-if="isApplication">单据数量（总计）：{{ settlements.length }}</span>
                <span>价税合计(总计)：{{ printData.amount | formatMoney }}</span>
                <span>金额(总计)：{{ printData.amountExcludingTax | formatMoney }}</span>
                <span>税额(总计)：{{ printData.tax | formatMoney }}</span>
            </div>
            <spacing-line
                :top-margin="16"
                :bottom-margin="12"
                line-type="solid"
            ></spacing-line>
            <div class="settlement-supplier-info">
                <div class="settlement-supplier-item">
                    <span>提交人：{{ createdUserName }}</span>
                    <span>提交时间：{{ printData.createdDate | parseTime('y-m-d') }}</span>
                </div>
                <div class="settlement-supplier-item">
                    <span>审核人：{{ reviewUserName }}</span>
                    <span>审核时间：{{ printData.reviewDate | parseTime('y-m-d') }}</span>
                </div>
            </div>
            <div class="settlement-page-no">
                第 <span data-page-no="PageNo"></span> 页 / 共 <span data-page-count="PageCount"></span> 页
            </div>
        </div>
    </div>
</template>

<script>
    import ApplicationTable from "./application-table.vue";
    import SpacingLine from "../medical-document-header/spacing-line.vue";
    import {formatMoney, parseTime} from "../../common/utils";
    import PreviewTable from "./preview-table.vue";

    export default {
        name: 'Settlement',
        filters: {
            formatMoney,
            parseTime,
        },
        components: {PreviewTable, SpacingLine, ApplicationTable},
        props: {
            printData: {
                type: Object,
                required: true,
            },
            settlementType: {
                type: String,
                required: true,
            }
        },
        computed: {
            isApplication() {
                return this.settlementType === 'application';
            },
            title() {
                return this.isApplication ? '结算单' : '结算明细';
            },
            orderNo() {
                return this.printData.orderNo || '';
            },
            supplierName() {
                return this.printData.supplierObj?.name || '';
            },
            settlements() {
                return this.printData.settlements || [];
            },
            items() {
                return this.printData.items || [];
            },
            createdUserName() {
                return this.printData.createdUser?.name || '';
            },
            reviewUserName() {
                return this.printData.reviewUser?.name || '';
            },
        },
    }
</script>

<style lang="scss">
.settlement-wrapper {
    box-sizing: border-box;

    * {
        box-sizing: border-box;
    }

    .settlement-header {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-bottom: 8px;
        font-family: SimSun, serif;
        font-size: 21px;
        font-weight: 700;
        line-height: 21px;
    }

    .settlement-content {
        display: flex;
        flex-direction: column;
    }

    .settlement-supplier {
        padding-bottom: 18px;
        font-family: SimSun, serif;
        font-size: 15px;
        line-height: normal;
        text-align: center;
    }

    .settlement-footer {
        display: flex;
        flex-direction: column;
        padding-top: 8px;
    }

    .settlement-total-info {
        display: flex;
        gap: 16px;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
        font-size: 15px;
        line-height: 16px;
    }

    .settlement-supplier-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        font-size: 13px;
        line-height: 16px;
    }

    .settlement-supplier-item {
        display: flex;
        gap: 36px;
        align-items: center;
    }

    .settlement-page-no {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding-top: 12px;
        font-size: 13px;
        line-height: 16px;
    }
}
</style>
