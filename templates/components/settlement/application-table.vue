<template>
    <table
        class="settlement-application-table"
        data-type="group"
    >
        <thead>
            <tr>
                <th class="settlement-order-no">
                    单号
                </th>
                <th class="settlement-type">
                    类型
                </th>
                <th class="settlement-time">
                    出/入库时间
                </th>
                <th class="settlement-operator">
                    操作人
                </th>
                <th class="settlement-kind">
                    品种
                </th>
                <th class="settlement-count">
                    数量
                </th>
                <th class="settlement-price">
                    金额
                </th>
                <th class="settlement-tax">
                    税额
                </th>
                <th class="settlement-clinic">
                    出入库门店
                </th>
            </tr>
        </thead>
        <tbody>
            <tr
                v-for="item in settlements"
                :key="item.id"
                data-type="item"
            >
                <td class="settlement-order-no">
                    {{ item.refOrderNo }}
                </td>
                <td class="settlement-type">
                    {{ formatOrderType(item) }}
                </td>
                <td class="settlement-time">
                    {{ item.refOrderDate | parseTime('y-m-d') }}
                </td>
                <td class="settlement-operator">
                    {{ item.refOrderUserName }}
                </td>
                <td class="settlement-kind">
                    {{ item.kindCount }}
                </td>
                <td class="settlement-count">
                    {{ item.count }}
                </td>
                <td class="settlement-price">
                    {{ formatAmount(item.type, item.amountExcludingTax) | formatMoney }}
                </td>
                <td class="settlement-tax">
                    {{ formatAmount(item.type, item.tax) | formatMoney }}
                </td>
                <td class="settlement-clinic">
                    {{ formatClinicName(item.orderClinic) }}
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script>
    import {formatAmount, formatClinicName, formatMoney, formatOrderType, parseTime} from "../../common/utils";

    export default {
        name: 'ApplicationTable',
        filters: {
            parseTime,
            formatMoney,
        },
        props: {
            settlements: {
                type: Array,
                required: true,
            },
        },
        methods: {
            formatClinicName,
            formatOrderType,
            formatAmount,
        },
    }
</script>

<style lang="scss">
.settlement-application-table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;

    th,
    td {
        flex-wrap: wrap;
        padding: 8px;
        border: 1px solid #000000;
    }

    th {
        font-size: 13px;
        line-height: 16px;
    }

    td {
        font-size: 13px;
        font-weight: 300;
        line-height: 16px;
    }

    .settlement-order-no {
        width: 18%;
        text-align: center;
    }

    .settlement-type {
        width: 8.3%;
        text-align: center;
    }

    .settlement-time {
        width: 13%;
        text-align: center;
    }

    .settlement-operator {
        width: 8.3%;
        text-align: left;
    }

    .settlement-kind {
        width: 8.3%;
        text-align: right;
    }

    .settlement-count {
        width: 8.3%;
        text-align: right;
    }

    .settlement-price {
        width: 11.3%;
        text-align: right;
    }

    .settlement-tax {
        width: 11.3%;
        text-align: right;
    }

    .settlement-clinic {
        text-align: left;
    }
}
</style>
