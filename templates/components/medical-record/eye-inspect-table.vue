<template>
    <abc-table
        :columns="columns"
        :list="list"
        class="medical-record-eye-inspect-table"
        :class="{
            'page-is-a4': currentPageIsA4
        }"
    ></abc-table>
</template>

<script>
    import AbcTable from "../layout/abc-table.vue";
    import {AbcHtml} from "../layout";

    export default {
        name: 'EyeInspectTable',

        components: {AbcTable},

        props: {
            list: {
                type: Array,
                default: () => []
            },

            styleConfig: {
                type: Object,
                default: () => ({})
            },

            currentPageIsA5: {
                type: <PERSON>olean,
            },

            currentPageIsA4: <PERSON>olean,
        },

        computed: {
            columns() {
                const thStyle = {
                    background: this.styleConfig.themeColor || '#A8A8A8',
                }
                return [
                    {
                        key: 'name',
                        label: '项目',
                        width: 144,
                        thStyle,
                        customProps: {
                            style: {
                                textAlign: 'center',
                            }
                        }
                    },
                    {
                        key: 'rightEyeValue',
                        label: '右眼（OD）',
                        width: 324,
                        thStyle,
                        render: (createElement, row) => {
                            return (
                                createElement(
                                    AbcHtml,
                                    {
                                        props: {
                                            value: row.rightEyeValue
                                        }
                                    },
                                )
                            )
                        }
                    },
                    {
                        key: 'leftEyeValue',
                        label: '左眼（OS）',
                        width: 324,
                        thStyle,
                        render: (createElement, row) => {
                            return (
                                createElement(
                                    AbcHtml,
                                    {
                                        props: {
                                            value: row.leftEyeValue
                                        }
                                    },
                                )
                            )
                        }
                    }
                ]
            }
        }
    }
</script>

<style lang="scss">
.medical-record-eye-inspect-table {
    font-size: 10px;
    border-collapse: collapse;
    border: 1px solid #A6A6A6;
    width: 100%;

    //a4 下字体变大，默认 a5 的字体大小
    &.page-is-a4 {
        thead {
            .abc-table-tr {
                .abc-table-cell {
                    font-size: 10pt;
                    line-height: 12pt; /* 120% */
                }
            }
        }

        .abc-table-cell {
            font-size: 10pt;
            line-height: 12pt;
        }
    }

    thead {
        .abc-table-tr {
            .abc-table-cell {
                color: #ffffff;
                font-family: "Microsoft YaHei UI";
                font-size: 10px;
                padding: 6pt;
                font-style: normal;
                font-weight: 700;
                text-align: center;
            }
        }
    }

    .abc-table-tr {
        &:nth-child(2n) {
            background: #EFEFEF;
        }
    }
    .abc-table-cell {
        border-collapse: collapse;
        border: 1px solid #A6A6A6;
        font-size: 10px;
        padding: 6pt;
        color: #000000;
        font-family: "Microsoft YaHei UI";
        font-style: normal;
        font-weight: 300;
        line-height: 12px;
    }
}
</style>