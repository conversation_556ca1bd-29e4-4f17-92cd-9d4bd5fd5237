<template>
    <div
        class="print-medical-document-header print-medical-header-wrapper"
        :class="{'is-landscape': isLandscape}"
    >
        <div
            class="organ-name-new"
        >
            <template v-if="!curOrganSubtitle">
                {{ curOrganTitle }}
            </template>
            <template v-else>
                <div class="sub-title-line-height">
                    {{ curOrganTitle }}
                </div>
                <div class="sub-title-line-height">
                    {{ curOrganSubtitle }}
                </div>
            </template>
        </div>
        <div
            v-if="headerConfig.emrTitle"
            class="document-type"
        >
            <div
                class="document-title"
                :class="curOrganSubtitle ? 'document-type-text-sub' : 'document-type-text-single'"
            >
                {{ printTitle }}
                <template v-if="handleExtendSpec && headerConfig.prescriptionType">
                    （{{ handleExtendSpec }}）
                </template>
            </div>
        </div>
        <div
            v-if="headerConfig.qrcode && qrCodeSrc && !printJinma"
            class="qr-code-img"
        >
            <img
                class="qr-img"
                :src="qrCodeSrc"
                alt=""
            />
        </div>
        <div
            v-if="printJinma"
            class="jinma-style"
        >
            {{ jinmaType }}
        </div>
        <div>
            <div
                v-if="(headerConfig.barcode && barcodeSrc) || (headerConfig.logo && organ.logo)"
                class="left-img-wrapper"
            >
                <img
                    v-if="headerConfig.barcode && barcodeSrc"
                    class="bar-img"
                    :src="barcodeSrc"
                    alt=""
                />
                <template v-else>
                    <img
                        v-if="organ.logo"
                        class="logo-img-content"
                        :src="organ.logo"
                        alt=""
                    />
                </template>
            </div>

            <!-- 配镜处方不显示该项 -->
            <div
                v-if="!isGlasses && (showVirtualPharmacy || showProcess || showExpress || isMutiPharmacy)"
                class="process-icon process-text"
            >
                <span v-if="showVirtualPharmacy">代煎代配</span>
                <template v-else>
                    <span v-if="showProcess">加工</span>
                    <span v-if="showExpress">快递</span>
                    <span v-if="isMutiPharmacy && prescriptionShowPharmacyName">{{ pharmacyName }}</span>
                </template>
            </div>
        </div>

        <print-row
            class="base-info date-info "
            style=" padding-bottom: 0; margin-top: 10pt; font-size: 0;"
            :style="headerConfig.dateType ? { borderBottom: 'none' } : {}"
        >
            <div
                class="patient-info"
                style="display: inline-block; width: 41.66%; font-size: 10pt;"
                :style="headerConfig.dateType ? { width: '100%' } : {}"
            >
                姓名：<div style="display: inline-block; max-width: 71%; overflow: hidden; word-break: break-all; white-space: nowrap; vertical-align: bottom;">
                    {{ patientName }}
                </div>
                <span class="space"></span> {{ patient.sex }} <span class="space"></span> {{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                <span class="space"></span>
            </div>
            <div
                v-if="!headerConfig.dateType"
                class="patient-info"
                style="display: inline-block; width: 33%; font-size: 10pt;"
            >
                诊号：{{ formatPatientOrderNo(printData.patientOrderNo ) }}
                <template v-if="headerConfig.revisit">
                    {{ printData.revisitStatus === 2 ? '复诊' : '初诊' }}
                </template>
            </div>
            <div
                v-if="!headerConfig.dateType"
                class="patient-info"
                style="display: inline-block; width: 25%; font-size: 10pt;"
            >
                日期：{{ parseTime(curDate, 'y-m-d') }}
            </div>
        </print-row>

        <print-row
            v-if="headerConfig.dateType"
            class="base-info date-info"
            style="padding-bottom: 0; margin-top: 0; font-size: 0;"
        >
            <div
                class="patient-info"
                style="display: inline-block; width: 41.66%; font-size: 10pt;"
            >
                诊号：{{ formatPatientOrderNo(printData.patientOrderNo ) }}
                <template v-if="headerConfig.revisit">
                    {{ printData.revisitStatus === 2 ? '复诊' : '初诊' }}
                </template>
            </div>
            <div
                class="patient-info"
                style="display: inline-block; width: 58%; font-size: 10pt;"
            >
                日期：{{ parseTime(curDate, 'y-m-d h:i:s') }}
            </div>
        </print-row>

        <print-row class="base-info has-margin-top">
            <print-col
                :span="!isGlasses ? 10 : 18"
                overflow
                class="patient-info"
            >
                科室：{{ printData.departmentName }}
            </print-col>

            <!-- 配镜处方不显示该项 -->
            <print-col
                v-if="!isGlasses"
                :span="8"
                overflow
                class="patient-info"
            >
                费别： <template v-if="headerConfig.feeType">
                    {{ printData.healthCardPayLevel }}
                </template>
                <template v-if="headerConfig.personType">
                    {{ shebaoCardInfo.feeType }}
                </template>
            </print-col>

            <print-col
                v-if="headerConfig.mobile"
                :span="!isGlasses ? 6 : 8"
                class="patient-info"
            >
                手机：{{ patient.mobile | formatMobile(headerConfig.mobileDesensitization) }}
            </print-col>

            <!--<print-col-->
            <!--    v-if="!isGlasses && headerConfig.socialCode"-->
            <!--    :span="6"-->
            <!--    overflow-->
            <!--&gt;-->
            <!--    医保号：{{ printData.healthCardNo }}-->
            <!--</print-col>-->
        </print-row>

        <print-row
            class="base-info"
        >
            <template v-for="item in getPatientInfoRenderData">
                <print-col
                    v-if="['出生日期：'].includes(item.label)"
                    :key="item.label"
                    class="patient-info"
                    :span="item.width"
                    :style="{
                        'white-space': 'normal'
                    }"
                >
                    {{ item.label }}{{ item.value }}
                </print-col>
                <print-col
                    v-else
                    :key="item.label"
                    class="patient-info"
                    style=" overflow: hidden; word-break: normal; white-space: normal;"
                    :style="{
                        'word-break': item.label === '个人编号：' ? 'break-all' : 'normal',
                        'white-space': item.label === '地址：' ? 'nowrap' : 'normal'
                    }"
                    overflow
                    :span="item.width"
                >
                    {{ item.label }}{{ item.value }}
                </print-col>
            </template>
        </print-row>

        <print-row class="line-split"></print-row>
        <div
            v-if="!isGlasses && showRp"
            class="rp-icon"
            :class="{'is-landscape-rp': isLandscape}"
        >
            Rp<sub>&#183;</sub>
        </div>
    </div>
</template>

<script>
    import {
        formatAge,
        formatAddress,
        formatPatientOrderNo,
        parseTime,
        textToBase64BarCode,
        getLengthWithFullCharacter, getIdCardTypeStr,
    } from '../../common/utils.js';
    import { filterIdCard, formatMobile } from "../../common/medical-transformat.js";
    import { MARTIAL_LABEL_ENUM, TITLE_MAX_LENGTH } from "../../common/constants.js";
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';

    import { isDate } from '@tool/date';
    import { PsychotropicNarcoticTypeEnumStr } from "../../constant/print-constant";
    export default {
        name: 'MedicalDocumentHeader',
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            filterDiagnose(val) {
                if (!val) return '';
                const valArr = val.split('<br>');
                return valArr.join('');
            },
            formatMobile,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            headerType: {
                type: String,
                default: '',
            },
            printTitle: {
                type: String,
                default: '治疗执行单',
            },
            organTitle: {
                type: String,
                default: '',
            },
            extendSpec: String,
            logoSrc: String,
            showRp: {
                type: Boolean,
                default: true,
            },
            showProcess: {
                type: Boolean,
                default: false,
            },
            showExpress: {
                type: Boolean,
                default: false,
            },
            showVirtualPharmacy: {
                type: Boolean,
                default: false,
            },
            isLandscape: {
                type: Boolean,
                default: false,
            },
            prescriptionShowPharmacyName: {
                type: Boolean,
                default: true,
            },
            createdTime: {
                type: String,
                validator: (value) => {
                    return isDate(value)
                },
            },
            printJinma: {
                type: Number,
                default: 0,
            },
            pharmacyName: {
                default: '',
                type: String,
            },
            isGlasses: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                curOrganTitle: '',
                curOrganSubtitle: '',
            }
        },
        computed: {
            patient() {
                return this.printData.patient || {};
            },
            patientName() {
                return this.patient.name
            },
            isMutiPharmacy() {
                return this.printData.openPharmacyFlag === 20 || false
            },
            getPatientInfoRenderData() {
                let renderPatient = [];
                let count = 0;
                const widthArr = [10, 8, 6]

                if(this.headerConfig.idCard) {
                    renderPatient.push({
                        label: `${getIdCardTypeStr(this.patient.idCardType)}：`,
                        value: filterIdCard(this.patient.idCard, this.headerConfig.idCardDesensitization),
                        width: widthArr[count % 3],
                        isIdCard: true,
                    })
                    count++;
                }
                if(this.headerConfig.computerCode) {
                    renderPatient.push({
                        label: '个人编号：',
                        value: this.shebaoCardInfo.personalCode ||
                            (this.shebaoCardInfo.extend && this.shebaoCardInfo.extend.personalCode),
                        width: widthArr[count % 3],
                    })
                    count++;
                }

                if(!this.isGlasses && this.headerConfig.socialCode) {
                    renderPatient.push({
                        label: '医保号：',
                        value: this.printData.healthCardNo,
                        width: widthArr[count % 3],
                    })
                    count++;
                }

                if(this.headerConfig.fileNumber) {
                    renderPatient.push({
                        label: '档案号：',
                        value: this.patient.sn,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.birthday) {
                    renderPatient.push({
                        label: '出生日期：',
                        value: this.patient.birthday,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(!this.isGlasses && this.headerConfig.weight) {
                    renderPatient.push({
                        label: '体重：',
                        value: `${this.patient.weight ? this.patient.weight + 'kg' : ''}`,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(!this.isGlasses && this.headerConfig.married) {
                    renderPatient.push({
                        label: '婚否：',
                        value: this.getPatientMaterialName(),
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(!this.isGlasses && this.headerConfig.nation) {
                    renderPatient.push({
                        label: '民族：',
                        value: this.patient.ethnicity,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.profession) {
                    renderPatient.push({
                        label: '职业：',
                        value: this.patient.profession,
                        width: widthArr[count % 3],
                    })
                    count++;
                }
                if(this.headerConfig.address) {
                    renderPatient.push({
                        label: '地址：',
                        value: formatAddress(this.patient.address),
                        width: 24,
                    })
                    count++;
                }
                return renderPatient;
            },

            qrCodeSrc() {
                return this.printData.qrCode;
            },

            headerConfig() {
                return this.config.header || {};
            },
            organ() {
                return this.printData.organ || {};
            },
            shebaoCardInfo() {
                return this.printData.shebaoCardInfo || {};
            },
            syndrome() {
                return this.printData.medicalRecord && this.printData.medicalRecord.syndrome || '';
            },
            clinicNameTitle() {
                let name = this.organ.name || '';
                name = name.length > 28 ? name.slice(0, 28) : name;
                const clinicTitle = [];
                if (name.length > 16) {
                    clinicTitle.push(name.slice(0, 16));
                    clinicTitle.push(name.slice(16, name.length));
                } else {
                    clinicTitle.push(name);
                }
                return clinicTitle;
            },
            barcodeSrc() {
                const barcode = this.printData.patientOrderNo ? `${this.printData.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode);
            },
            // 处方的时间都统一显示为完成诊断时间，有客户反馈处方打印的时间会因为处方修改而跟门诊单开出的时间不一致
            curDate() {
                return this.printData.diagnosedDate;
            },
            jinmaType() {
                return PsychotropicNarcoticTypeEnumStr[this.printJinma] || '';
            },
            handleExtendSpec() {
                if (this.extendSpec === '中药饮片') {
                    return '饮片';
                }
                if (this.extendSpec === '中药颗粒') {
                    return '颗粒';
                }
                return this.extendSpec;
            },
            titleAndSubtitle() {
                return {
                    organTitle: this.organTitle,
                    headerConfigSubtitle: this.headerConfig.subtitle,
                    organName: this.organ.name,
                };
            },
        },
        watch: {
            titleAndSubtitle: {
                handler(v) {
                    const { organTitle, headerConfigSubtitle, organName } = v;
                    let title = '', subtitle = '';
                    if(!organTitle) {
                        title = organName || '';
                    } else {
                        title = organTitle || '';
                    }

                    const cacheTitle = title;
                    const {
                        fullCharacterLength, splitLength,
                    } = getLengthWithFullCharacter(cacheTitle, TITLE_MAX_LENGTH);
                    if (fullCharacterLength > TITLE_MAX_LENGTH) {
                        title = cacheTitle.slice(0, splitLength);
                        subtitle = cacheTitle.slice(splitLength);
                    }

                    this.curOrganTitle = title;
                    if (subtitle) {
                        this.curOrganSubtitle = subtitle;
                    } else {
                        this.curOrganSubtitle = headerConfigSubtitle || '';
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            formatAge,
            formatAddress,
            formatPatientOrderNo,
            parseTime,
            getPatientMaterialName() {
                let status = MARTIAL_LABEL_ENUM.find( item => {
                    return item.value === this.patient.marital;
                })
                return status && status.label || '';
            },
        },
    };
</script>

<style lang="scss">
@import "index.scss";

.print-medical-header-wrapper {
    .organ-name-new {
        height: 40pt;
        font-family: SimSun;
        font-size: 14pt;
        font-weight: bold;
        line-height: 40pt;
        text-align: center;

        .sub-title-line-height {
            font-family: SimSun;
            font-size: 14pt;
            font-weight: bold;
            line-height: 20pt;
            text-align: center;
        }
    }

    .document-type {
        font-family: SimSun;

        .document-type-text-sub {
            font-size: 12pt;
            line-height: 20pt;
        }

        .document-type-text-single {
            font-size: 14pt;
            line-height: 20pt;
        }
    }

    .left-img-wrapper {
        position: absolute;
        top: 0;
        left: -6pt;
        width: 90pt;
        height: 40pt;
        line-height: 40pt;

        .bar-img {
            width: 100%;
            height: 24pt;
            vertical-align: middle;
        }

        .logo-img-content {
            max-width: 100%;
            max-height: 100%;
            vertical-align: middle;
        }
    }

    .process-text {
        position: absolute;
        top: 42pt;
        left: 0;
        font-size: 10pt;
        font-weight: bold;
    }
}
</style>
