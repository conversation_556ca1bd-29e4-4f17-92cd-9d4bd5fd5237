<template>
    <div>
        <slot></slot>
    </div>
</template>

<script>
    export default {
        name: 'Question',
        provide() {
            return {
                question: this.question,
            };
        },
        inject: ['form'],
        data() {
            return {
                question: [],
            };
        },
        created() {
            if (Array.isArray(this.form.questions)) {
                this.form.questions.push(this.question);
            }
        },
        beforeDestroy() {
            const questions = this.form.questions;
            if (Array.isArray(questions)) {
                const index = questions.indexOf(this.question);
                if (index !== -1) {
                    questions.splice(index, 1);
                }
            }
        },
    };
</script>
