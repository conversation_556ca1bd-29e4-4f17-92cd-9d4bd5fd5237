<template>
    <div class="question-radio">
        <span v-for="{ _id, name } in config.options" :key="_id" :label="_id">
          <div class="circle"></div>
          <div class="label">{{ name }}</div>
        </span>
    </div>
</template>

<script>
    export default {
        name: 'QuestionRadio',
        inject: ['form', 'question'],
        props: {
            config: {
                type: Object,
            },
        },
        computed: {
            currentValue() {
                const id = this.id;
                const { value } = this.formValue[id] || {};
                return value;
            },
            isFinished() {
                return !!this.currentValue;
            },
            formValue() {
                return this.form.value;
            },
            id() {
                return this.config._id;
            },
        },
        created() {
            if (Array.isArray(this.question)) {
                this.question.push(this);
            }
        },
        beforeDestroy() {
            if (Array.isArray(this.question)) {
                const question = this.question;
                const index = question.indexOf(this);
                if (index !== -1) {
                    question.splice(index, 1);
                }
            }
        },
        methods: {
            onInput(value) {
                this.form.$emit('input', {
                    ...this.formValue,
                    [this.id]: {
                        kind: 'Radio',
                        value,
                    },
                });
            },
        },
    };
</script>
