<template>
    <div class="form-question">
        <slot></slot>
    </div>
</template>
<script>
    export default {
        props: {
            value: {
                type: Object,
            },
        },
        // 跨组件传递顶层实例
        // 避免多级传递value和event
        provide() {
            return {
                form: this,
            };
        },
        data() {
            return {
                // 表单中question的集合
                questions: [],
            };
        },
        created() {
            // 当前question的所有subQuestion完成
            this.$on('input', function () {
                this.getCurrentQuestion((subQuestions) => {
                    this.$nextTick(() => {
                        if (subQuestions.every((subQuestion) => subQuestion.isFinished)) {
                            this.$emit('currentQuestionComplete');
                        }
                    });
                });
            });
        },
        methods: {
            // 获取当前页面上的question
            getCurrentQuestion(next) {
                next && next(this.questions[0] || []);
            },
        },
    };
</script>
