<template>
    <table data-type="group" border>
        <template v-for="(group, groupQuestionIndex) in data">
            <question-group
                v-for="(subQuestion, subQuestionIndex) in group.children"
                :key="subQuestion._id"
                tag-name="tr"
                data-type="item"
            >
                <td v-if="subQuestionIndex === 0" :rowspan="group.children.length" style="white-space: nowrap;">
                    {{ group.label }}
                </td>
                <td>
                    {{ getSubIndexOfGroupQuestion(data, groupQuestionIndex, subQuestionIndex) }}.{{ subQuestion.label }}
                </td>
                <td>
                    <question-radio v-if="subQuestion.kind === 'Radio'" :config="subQuestion"></question-radio>
                </td>
            </question-group>
        </template>
    </table>
</template>

<script>
    import { QuestionGroup, QuestionRadio } from '../index';

    export default {
        name: 'QuestionTable',
        components: {
            QuestionGroup,
            QuestionRadio,
        },
        props: {
            data: {
                type: Array,
                default: () => [],
            },
        },
        methods: {
            // 获取分组中某评测题的索引值
            getSubIndexOfGroupQuestion(data = [], groupQuestionIndex, subQuestionIndex) {
                return (
                    data.slice(0, groupQuestionIndex).reduce((total, a) => {
                        return total + a.children.length;
                    }, 1) + subQuestionIndex
                );
            },
        },
    };
</script>
