<template>
    <div
        class="shandong-detail-items-wrapper"
        :class="{ 'no-self-rate': noSelfRate }"
        :style="Object.assign({'top': `${detailTop }mm`, height: `${detailHeight }mm`}, itemsWrapperStyles)"
    >
        <div
            v-for="(item, index) in formItems"
            :key="index"
            :class="{ 'left-part': index % 2 === 0, 'right-part': index % 2 === 1 }"
            class="charge-item"
            :style="{ top: `${Math.floor(index / 2) * 4 + 4 }mm` }"
        >
            <div
                class="item-name"
                overflow
            >
                {{ item.name }}
            </div>
            <div
                class="item-count"
                overflow
            >
                {{ item.count }}{{ item.unit }}
            </div>
            <div
                class="item-amount"
                overflow
            >
                {{ item.discountedPrice | formatMoney }}
            </div>
            <div
                class="item-proportion"
                overflow
            >
                {{ handleOwnExpensePrice(item) | formatMoney }}
            </div>
        </div>
        <div
            v-if="hasOverPageTip"
            class="only-one-page form-item-tr"
        >
            *** 因纸张限制，部分项目未打印 ***
        </div>
        <template v-else>
            <div
                v-if="pageIndex !== renderPageLength - 1"
                class="only-one-page"
            >
                *** 接下页 ***
            </div>
        </template>
    </div>
</template>

<script>
    import { formatMoney } from "../../common/utils.js";
    export default {
        name: "ShandongChargeFormItem",
        filters: {
            formatMoney
        },
        props: {
            formItems: Array,
            hasOverPageTip: Boolean,
            detailTop: {
                type: Number,
                default: 38
            },
            detailHeight: {
                type: Number,
                default: 40
            },
            disableProportion: {
                type: Boolean,
                default: false
            },
            noSelfRate: {
                type: Boolean,
                default: false,
            },
            itemsWrapperStyles: {
                type: Object,
                default() {
                    return {}
                }
            },
            pageIndex: {
                type: Number,
                default: 0,
            },
            renderPageLength: {
                type: Number,
                default: 1,
            },
        },
        methods: {
            handleOwnExpensePrice(item) {
                if (item.ownExpenseRatio === '-' ) return '-';
                if (item.ownExpenseRatio === undefined || item.ownExpenseRatio === null) return item.discountedPrice;
                try {
                    const ownExpenseRatio = Number(item.ownExpenseRatio);
                    return item.discountedPrice * ownExpenseRatio;
                } catch (e) {
                    console.warn(e);
                    return item.discountedPrice;
                }
            },
        },
    }
</script>

<style lang="scss">
.shandong-detail-items-wrapper {
  position: absolute;
  width: 100%;
  height: 40mm;
  font-size: 10pt;

  .left-part,
  .right-part {
    position: absolute;
  }

  .left-part {
    left: 24mm;
  }

  .right-part {
    left: 107mm;
  }

  .item-name,
  .item-count,
  .item-amount,
  .item-proportion {
    max-height: 11pt;
    overflow: hidden;
    word-break: keep-all;
    white-space: nowrap;
    position: absolute;
  }

  .item-name {
    left: 0;
    width: 40mm;
  }

  .item-count {
    left: 42mm;
    width: 10mm;
  }

  .item-amount {
    left: 53mm;
    width: 13mm;
  }

  .item-proportion {
    left: 67mm;
    width: 13mm;
  }

  .only-one-page {
    position: absolute;
    bottom: 6pt;
    left: 50%;
    margin-left: -94px;
  }

  &.no-self-rate {
    .item-proportion {
      display: none;
    }
  }
}

</style>
