<template>
    <div class="not-chinese-form-items-wrapper">
        <div class="items-wrapper">
            <div
                class="not-chinese-form-item"
            >
                <span
                    class="item-name"
                    overflow
                >{{ item.name }}</span>
                <span class="unit-price">{{ item.discountedUnitPrice| formatMoney }}</span>
                <span class="item-count">{{ item.count }}{{ item.unit }}</span>
                <span class="item-price">{{ item.discountedPrice | formatMoney }}</span>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'NotChineseFormItem',
        props: {
            item: Object,
        },
    };
</script>

