<template>
    <div class="chinese-form-items-wrapper">
        <div
            v-for="(item, index) in items"
            :key="index"
            class="chinese-form-item"
        >
            <span class="name" overflow>{{ item.name }}</span>
            <span class="count" overflow>{{ item.count }}{{ item.unit }}，</span>
            <span class="price" overflow>{{ item.discountedPrice | formatMoney }}；</span>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'ChineseFormItem',
        props: {
            items: Array,
        },
    };
</script>
