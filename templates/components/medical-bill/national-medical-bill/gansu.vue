<template>
    <div>
        <template v-for="(page, pageIndex) in renderPage">
            <div
                :key="pageIndex"
                class="gansu-medical-bill-content"
                :style="offsetStyle"
            >
                <div
                    v-if="blueInvoiceData"
                    style="position: absolute; top: 0.2cm; left: 2.3cm;"
                >
                    销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                </div>

                <refund-icon
                    v-if="isRefundBill"
                    top="0.2cm"
                    left="179mm"
                ></refund-icon>

                <div
                    v-if="!showChargeFormItems"
                    class="items-wrapper"
                    :style="{ top: detailTop ? `${detailTop }mm` : '' }"
                >
                    <div
                        v-for="(item, index) in medicalBills"
                        :key="index"
                        class="charge-item"
                        :style="{ top: `${Math.floor(index / 2) * 5 + 4 }mm`, left: index % 2 === 0 ? '23mm' : '97mm' }"
                    >
                        <div class="name">
                            {{ item.name }}
                        </div>
                        <div class="count">
                            {{ item.totalCount }}{{ item.unit }}
                        </div>
                        <div class="amount">
                            {{ item.totalFee | formatMoney }}
                        </div>
                    </div>
                </div>
                <charge-form-item
                    v-else
                    :form-items="page.formItems"
                    :has-over-page-tip="hasOverPageTip"
                    :detail-top="detailTop"
                    :detail-height="detailHeight"
                    :disable-proportion="disableProportion"
                    :no-self-rate="noSelfRate"
                    :items-wrapper-styles="itemsWrapperStyles"
                    :page-index="pageIndex"
                    :render-page-length="renderPage.length"
                ></charge-form-item>
                <slot></slot>
            </div>
            <div
                v-if="pageIndex !== renderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import BillDataMixins from '../../../mixins/bill-data';
    import NationalBillData from "../../../mixins/national-bill-data.js";
    import RefundIcon from '../../../components/refund-icon/refund-icon.vue';
    import ChargeFormItem from '../gansu-charge-form-items.vue';
    import { splitChargeFormItems } from '../../../common/medical-bill.js';

    export default {
        name: "MedicalBillGansu",
        components: {
            RefundIcon,
            ChargeFormItem,
        },
        mixins: [BillDataMixins, NationalBillData],
        props: {
            offsetStyle: {
                type: Object
            },
            detailTop: {
                type: Number
            },
            detailHeight: {
                type: Number
            },
            splitCount: {
                type: Number,
                default: 16
            },
            disableProportion: {
                type: Boolean,
                default: false
            },
            noSelfRate: {
                type: Boolean,
                default: false,
            },
            itemsWrapperStyles: {
                type: Object,
                default() {
                    return {}
                }
            },
            showNormalInvoice: {
                type: Boolean,
            },
            isNeedResetComposeSocialFee: {
                type: Boolean,
                default: false,
            }
        },
        computed: {
            pagesData() {
                return splitChargeFormItems(this.chargeFormItems, this.splitCount);
            },
        },
    }
</script>

<style lang="scss">
.gansu-medical-bill-content {
  @import '../../../components/refund-icon/refund-icon.scss';

  position: relative;
  top: 0;
  bottom: 0;
  left: 0;
  font-size: 10pt;
  line-height: 10pt;

  .id-number,
  .patient-name,
  .created-date,
  .charge-item,
  .total-price,
  .charge-clinic,
  .charger,
  .items-wrapper,
  .invoice,
  .shebao-item {
    position: absolute;
  }

  .invoice {
    left: 154mm;
  }

  .invoice-code {
    top: 0.2cm;
  }

  .invoice-number {
    top: 7mm;
  }

  .id-number {
    top: 25mm;
    left: 54mm;
  }

  .patient-name {
    top: 28mm;
    left: 31mm;
  }

  .created-date {
    top: 28mm;
    left: 162mm;
  }

  .items-wrapper {
    position: absolute;
    top: 34mm;
    width: 100%;
    height: 40mm;
  }

  .charge-item {
    .name,
    .count,
    .unit,
    .amount {
      position: absolute;
    }

    .name {
      width: 27mm;
    }

    .count {
      left: 28mm;
      width: 9mm;
    }

    .amount {
      left: 40mm;
    }
  }

  .total-price {
    top: 79mm;

    &.upper {
      left: 44mm;
    }

    &.lower {
      left: 136mm;
    }
  }

  .charge-clinic,
  .charger {
    top: 115mm;
  }

  .charge-clinic {
    left: 40mm;
  }

  .charger {
    left: 160mm;
  }
}

</style>
