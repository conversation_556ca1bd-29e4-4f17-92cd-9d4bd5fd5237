<template>
    <div
        class="block-box"
        :style="{
            left: `${left}mm`, top: `${top}mm`, position: 'absolute',fontSize:`${fontSize}pt`
        }"
    >
        <slot></slot>
    </div>
</template>

<script>
    export default {
        name: 'BlockBox',
        props: {
            top: {
                type: [Number, String],
                default: 0
            },
            left: {
                type: [Number, String],
                default: 0
            },
            font: {
                type: Number,
                default: 9
            }
        },
        computed: {
            fontSize() {
                return this.font + 1;
            }
        }
    }
</script>
