<template>
    <div>
        <template v-for="(page, pageIndex) in renderPage">
            <div
                :key="pageIndex"
                class="national-medical-bill-content"
                :class="{'national-medical-bill-content-wrap': isWrap}"
                :style="offsetStyle"
            >
                <div
                    v-if="blueInvoiceData"
                    style="position: absolute; top: 0.9cm; left: 2.3cm;"
                >
                    销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                </div>

                <block-box
                    :top="normalInvoiceNumberTop"
                    :left="normalInvoiceNumberLeft"
                    :font="fontSize"
                >
                    {{ normalInvoice && normalInvoice.invoiceNumbers && normalInvoice.invoiceNumbers[pageIndex] }}
                </block-box>

                <block-box
                    :top="normalInvoiceCodeTop"
                    :left="normalInvoiceCodeLeft"
                    :font="fontSize"
                >
                    {{ normalInvoice && normalInvoice.invoiceCode }}
                </block-box>

                <div v-if="showNormalInvoice">
                    <div class="invoice invoice-code">
                        {{ normalInvoice && normalInvoice.invoiceCode }}
                    </div>
                    <div class="invoice invoice-number">
                        {{ normalInvoice && normalInvoice.invoiceNumbers && normalInvoice.invoiceNumbers[pageIndex] }}
                    </div>
                </div>

                <refund-icon
                    v-if="isRefundBill"
                    top="0.7cm"
                    left="2.3cm"
                ></refund-icon>

                <div
                    v-if="!showChargeFormItems"
                    class="items-wrapper"
                    :style="{ top: detailTop ? `${detailTop }mm` : '' }"
                >
                    <div
                        v-for="(item, index) in medicalBills"
                        :key="index"
                        class="charge-item"
                        :style="{ top: `${Math.floor(index / 2) * 4 + 4}mm`, left: index % 2 === 0 ? '24mm' : '114mm' }"
                    >
                        <div class="name">
                            {{ item.name }}
                        </div>
                        <div class="count">
                            {{ item.totalCount }}{{ item.unit }}
                        </div>
                        <div class="amount">
                            {{ item.totalFee | formatMoney }}
                        </div>
                    </div>
                </div>
                <charge-form-item
                    v-else
                    :form-items="page.formItems"
                    :show-item-title="showItemTitle"
                    :has-over-page-tip="hasOverPageTip"
                    :detail-top="detailTop"
                    :detail-height="detailHeight"
                    :disable-proportion="disableProportion"
                    :no-self-rate="noSelfRate"
                    :is-wrap="isWrap"
                    :items-wrapper-styles="itemsWrapperStyles"
                    :product-info-rule-config="productInfoRuleConfig"
                ></charge-form-item>
                <slot></slot>
            </div>
            <div
                v-if="pageIndex !== renderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import BillDataMixins from '../../../mixins/bill-data';
    import NationalBillData from "../../../mixins/national-bill-data.js";
    import RefundIcon from '../../../components/refund-icon/refund-icon.vue';
    import ChargeFormItem from "../../../components/medical-bill/charge-form-items.vue";
    import BlockBox from '../../../components/medical-bill/national-medical-bill/block-box.vue';

    import {splitChargeFormItems} from "../../../common/medical-bill.js";
    import {resetChargeItemComposeSocialFee} from "../../../mixins/reset-compose-social-fee";

    export default {
        name: "MedicalBillNational",
        components: {
            RefundIcon,
            ChargeFormItem,
            BlockBox
        },
        mixins: [BillDataMixins, NationalBillData],
        props: {
            offsetStyle: {
                type: Object
            },
            detailTop: {
                type: Number
            },
            detailHeight: {
                type: Number
            },
            splitCount: {
                type: Number,
                default: 16
            },
            disableProportion: {
                type: Boolean,
                default: false
            },
            noSelfRate: {
                type: Boolean,
                default: false,
            },
            itemsWrapperStyles: {
                type: Object,
                default() {
                    return {}
                }
            },
            showNormalInvoice: {
                type: Boolean,
            },
            normalInvoiceCodeTop: {
                type: Number,
                default: 22,
            },
            normalInvoiceCodeLeft: {
                type: Number,
                default: 41,
            },
            normalInvoiceNumberTop: {
                type: Number,
                default: 19,
            },
            normalInvoiceNumberLeft: {
                type: Number,
                default: 40,
            },
            isWrap: {
                type: Boolean,
                default: false,
            },
            fontSize: {
                type: Number,
                default: 9,
            },
            showItemTitle: {
                type: Boolean,
                default: true
            },
            isNeedResetComposeSocialFee: {
                type: Boolean,
                default: false,
            }
        },
        computed: {
            pagesData() {
                return splitChargeFormItems(this.chargeFormItems, this.splitCount);
            },
        }
    }
</script>

<style lang="scss">
.national-medical-bill-content {
  @import '../../../components/refund-icon/refund-icon.scss';

  position: relative;
  top: 0;
  bottom: 0;
  left: 0;
  font-size: 10pt;
  line-height: 10pt;

  &.national-medical-bill-content-wrap {
    font-size: 8pt;
    line-height: 9pt;
  }

  .id-number,
  .patient-name,
  .created-date,
  .charge-item,
  .total-price,
  .charge-clinic,
  .charger,
  .items-wrapper,
  .invoice,
  .shebao-item {
    position: absolute;
  }
    .invoice {
        top: 32mm;
    }

    .invoice-code {
        left: 34mm;
    }

    .invoice-number {
        left: 160mm;
    }


    .id-number {
    top: 25mm;
    left: 54mm;
  }

  .patient-name {
    top: 28mm;
    left: 31mm;
  }

  .created-date {
    top: 28mm;
    left: 162mm;
  }

  .items-wrapper {
    position: absolute;
    top: 34mm;
    width: 100%;
    height: 40mm;
  }

  .charge-item {
    .name,
    .count,
    .unit,
    .amount {
      position: absolute;
    }

    .name {
      width: 30mm;
    }

    .count {
      left: 32mm;
      width: 20mm;
    }

    .amount {
      left: 48mm;
    }
  }

  .total-price {
    top: 79mm;
    &.upper {
      left: 44mm;
    }

    &.lower {
      left: 136mm;
    }
  }

  .charge-clinic,
  .charger {
    top: 115mm;
  }

  .charge-clinic {
    left: 40mm;
  }

  .charger {
    left: 160mm;
  }
}

</style>
