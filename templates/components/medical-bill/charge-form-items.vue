<template>
    <div
        class="detail-items-wrapper"
        :class="{ 'no-self-rate': noSelfRate, 'detail-items-wrapper-wrap': isWrap }"
        :style="Object.assign({'top': `${detailTop }mm`, height: `${detailHeight }mm`}, itemsWrapperStyles)"
    >
        <div
            v-if="showItemTitle"
            class="left-part"
        >
            <span
                class="item-name"
                overflow
            >名称</span>
            <span class="item-count">数量/单位</span>
            <span class="item-price">单价</span>
            <span class="item-amount">金额</span>
            <span
                v-if="!disableProportion"
                class="item-proportion"
            >自付比例</span>
        </div>
        <div
            v-if="showItemTitle"
            class="right-part"
        >
            <span
                class="item-name"
                overflow
            >名称</span>
            <span class="item-count">数量/单位</span>
            <span class="item-price">单价</span>
            <span class="item-amount">金额</span>
            <span
                v-if="!disableProportion"
                class="item-proportion"
            >自付比例</span>
        </div>
        <div
            v-for="(item, index) in formItems"
            :key="index"
            :class="{ 'left-part': index % 2 === 0, 'right-part': index % 2 === 1 }"
            class="charge-item"
            :style="{ top: calcTop(index) }"
        >
            <div
                class="item-name"
                :overflow="!isWrap"
            >
                <template v-if="isShowSocialCode(item)">
                    [{{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}]
                </template>
                {{ item.name }}
            </div>
            <div class="item-count">
                {{ item.count }}&nbsp;&nbsp;&nbsp;{{ item.unit }}
            </div>
            <div class="item-price">
                {{ item.discountedUnitPrice | formatMoney }}
            </div>
            <div class="item-amount">
                {{ item.discountedPrice | formatMoney }}
            </div>
            <div
                v-if="!disableProportion"
                class="item-proportion"
            >
                {{ item.ownExpenseRatio | filterOwnExpenseRatio }}
            </div>
        </div>
        <div
            v-if="hasOverPageTip"
            class="only-one-page form-item-tr"
            :class="{'only-one-page-wrap': isWrap}"
        >
            *** 因纸张限制，部分项目未打印 ***
        </div>
    </div>
</template>

<script>
    import { filterOwnExpenseRatio, formatMoney, medicalFeeGrade2PrintStr } from "../../common/utils.js";

    export default {
        name: "ChargeFormItem",
        filters: {
            filterOwnExpenseRatio,
            formatMoney,
            medicalFeeGrade2PrintStr
        },
        props: {
            formItems: Array,
            hasOverPageTip: Boolean,
            detailTop: {
                type: Number,
                default: 38
            },
            detailHeight: {
                type: Number,
                default: 40
            },
            disableProportion: {
                type: Boolean,
                default: false
            },
            noSelfRate: {
                type: Boolean,
                default: false,
            },
            itemsWrapperStyles: {
                type: Object,
                default() {
                    return {}
                }
            },
            productInfoRuleConfig: {
                type: Number,
                default: 0
            },
            isWrap: {
                type: Boolean,
                default: false,
            },
            showItemTitle: {
                type: Boolean,
                default: true,
            }
        },
        methods: {
            // 是否展示医保等级
            isShowSocialCode(chargeItem) {
                if (!medicalFeeGrade2PrintStr(chargeItem.medicalFeeGrade)) {
                    // 商品无医保码时不展示医保等级
                    return false
                }
                // 项目信息规则选展示收费项目时，诊疗项目不展示医保等级
                if (!this.productInfoRuleConfig
                    && (chargeItem.productType == 3 || chargeItem.productType == 4 || chargeItem.productType == 19)
                ) {
                    return false
                }
                // 药品有医保码或项目规则选择要展示等级时均显示医保等级
                return true
            },
            calcTop(index){
                if(this.isWrap) {
                    return `${Math.floor(index / 2) * 6 + 4 }mm`
                }
                return `${Math.floor(index / 2) * 4 + 4 }mm`
            }
        }
    }
</script>

<style lang="scss">
.detail-items-wrapper {
    position: absolute;
    width: 100%;
    height: 40mm;
    font-size: 10pt;

  &.detail-items-wrapper-wrap {
    font-size: 8pt;
    line-height: 9pt;
    .item-name,
    .item-count,
    .item-price,
    .item-amount,
    .item-proportion {
      max-height: 18pt;
      overflow: hidden;
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
    }
  }

    .left-part,
    .right-part {
        position: absolute;
    }

    .left-part {
        left: 24mm;
    }

    .right-part {
        left: 110mm;
    }

    .item-name,
    .item-count,
    .item-price,
    .item-amount,
    .item-proportion {
        max-height: 11pt;
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
        position: absolute;
    }

    .item-name {
        left: 0;
        width: 26mm;
    }

    .item-count {
        left: 27mm;
        width: 14mm;
    }

    .item-price {
        left: 41mm;
        width: 12mm;
    }

    .item-amount {
        left: 55mm;
        width: 13mm;
    }

    .item-proportion {
        left: 68mm;
        width: 15mm;
    }

    .only-one-page {
      position: absolute;
      bottom: -5pt;
      left: 50%;
      margin-left: -94px;

      &.only-one-page-wrap {
        font-size: 8pt;
        bottom: -22pt;
      }
    }

    &.no-self-rate {
      .item-proportion {
        display: none;
      }

      .item-name {
        left: 0;
        width: 41mm;
      }

      .item-count {
        left: 42mm;
        width: 14mm;
      }

      .item-price {
        left: 56mm;
        width: 12mm;
      }

      .item-amount {
        left: 70mm;
        width: 13mm;
      }
    }
}

</style>
