<template>
    <div class="qingdao-medical-bill-page">
        <div
            v-for="count in 3"
            :key="count"
            class="stub-form"
            :class="{ 'bookkeeping-sheet': count === 2, 'invoice' : count === 3 }"
        >
            <refund-icon
                v-if="isRefundBill"
                top="0.2cm"
                left="0.6cm"
            ></refund-icon>
            <div
                v-if="blueInvoiceData"
                style="position: absolute; top: 0.2cm; left: 0.6cm; width: 6cm;"
            >
                销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[0] }}
            </div>
            <p class="organ-name">
                {{ qingdao.institutionName }}
            </p>
            <div class="patient">
                <div class="patient-detail">
                    <span>{{ patient.name }}</span>
                </div>
            </div>
            <div>
                <div class="bill-title">
                    <span>项目名称</span>
                    <span style="margin-left: 3cm;">金额</span>
                </div>
                <div
                    class="bill-detail bill-items"
                    style="top: 34mm"
                >
                    <div
                        v-for="(item, index) in medicalBills"
                        :key="index"
                        class="charge-item"
                    >
                        {{ item.name }}
                        <span style="margin-left: 1mm;">{{ item.totalFee | formatMoney }}</span>
                    </div>
                </div>
                <div class="bill-shebao-info">
                    <div>
                        <span>合计：</span>
                        <span>{{ $t('currencySymbol') }}{{ finalFee | formatMoney }}</span>
                        <span style="margin-left: 1mm;">统筹基金：{{ shebaoPayment.fundPaymentFee | formatMoney }}</span>
                    </div>
                    <div>
                        <span
                            v-for="(pay, index) in chargeTransactions"
                            :key="index"
                        >
                            ({{ pay.payModeDisplayName }}){{ pay.amount | formatMoney }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="charge-time">
                {{ printData.chargedTime | parseTime('y-m-d') }}
            </div>
            <div class="charger">
                {{ printData.chargedByName }}
            </div>
        </div>
    </div>
</template>

<script>
    import RefundIcon from '../../components/refund-icon/refund-icon.vue';
    import BillDataMixins from '../../mixins/bill-data.js';
    export default {
        name: 'MedicalBills',
        components: {
            RefundIcon,
        },
        mixins: [BillDataMixins],
        computed: {
            qingdao() {
                return this.config.qingdao || {};
            },
        }
    };
</script>
