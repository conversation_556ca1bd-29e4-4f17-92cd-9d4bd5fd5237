import PrintRow from '../layout/print-row.vue';
import PrintCol from '../layout/print-col.vue';
import {filterMobile} from "../../common/medical-transformat.js";
import { formatAge, formatMoney, parseTime } from "../../common/utils.js";
import { ClinicRoleEnum } from "../../constant/print-constant.js";

export default {
    components: {
        PrintRow,
        PrintCol,
    },
    filters: {
        filterMobile,
        formatMoney,
        parseTime
    },
    computed: {
        organTitle() {
            if(!this.config.title) {
                return this.organ && this.organ.name || '';
            }
            return this.config.title || '';
        },
        isTreatment() {
            return this.printData.REGISTRATION_TYPE === 'treatment';
        },
        typeTitle() {
            return this.isTreatment ? '理疗预约单' : '挂号单';
        },
        roleIds() {
            return this.printData.roleIds || [];
        },
        operateRole() {
            if(this.isTreatment) {
                return '理疗师'
            }
            if(this.roleIds.length === 1 && this.roleIds.includes(ClinicRoleEnum.OPTOMETRIST)) {
                return '视光师'
            }
            return '医生';
        },
        patientInfo() {
            return this.config && this.config.patientInfo  || {};
        },
        patient() {
            return (this.printData && this.printData.patient) || {};
        },
        clinic() {
            return (this.printData && this.printData.organ) || {};
        },
        
        registrationInfo() {
            return (this.config && this.config.registrationInfo) || {};
        },
        feeInfo() {
            return (this.config && this.config.feeInfo) || {};
        },
        outpatientInfo() {
            return (this.config && this.config.outpatientInfo) || {};
        },
        ticketFooter() {
            return (this.config && this.config.ticketFooter) || {};
        },
        hasOutpatientInfo() {
            return (
                this.outpatientInfo.outpatientDate ||
                this.outpatientInfo.outpatientTime ||
                this.outpatientInfo.consultingRoom ||
                this.outpatientInfo.signTime
            );
        },
        showFooterInfo() {
            return (this.ticketFooter.operator && this.printData.createdBy) ||
                (this.ticketFooter.reserveDate && this.printData.isReserved) ||
                (this.ticketFooter.address && this.clinic.addressDetail) ||
                (this.ticketFooter.mobile && this.clinic.contactPhone)
        },
        /**
         * @desc 显示实付，实付配置打开，有支付方式，并且已经支付了
         */
        showNetIncome() {
            return this.feeInfo.netIncomeFee && this.printData.payStatus && (this.chargeTransactions.length || this.printData.receivedFee === 0
            );
        },
        /**
         * @desc 支付方式支付金额
         */
        chargeTransactions() {
            return this.printData.chargeTransactions || [];
        },
        payModeStr() {
            const pay = this.chargeTransactions.map((item) => {
                return item.payModeDisplayName;
            });
            return pay.join('+');
        },
    },
    methods: {
        formatAge,
    }
}