<template>
    <div class="print-registration-content">
        <div class="content-item">
            <img
                v-if="config.barcode && barcodeSrc"
                class="barcode-image"
                :src="barcodeSrc"
                alt=""
            />

            <print-row>
                <print-col
                    :span="24"
                    class="print-registration-title"
                >
                    {{ organTitle }}
                </print-col>
            </print-row>
            <print-row>
                <print-col
                    :span="24"
                    class="print-registration-type"
                >
                    {{ registOrganTitle }}
                </print-col>
            </print-row>
            <print-row class="item-row">
                <print-col :span="24">
                    姓名：{{ patient.name }}&nbsp;&nbsp;
                    <transition name="print-fade">
                        <span v-if="patientInfo.sex">
                            {{ patient.sex }}&nbsp;&nbsp;
                        </span>
                    </transition>
                    <transition name="print-fade">
                        <span v-if="patientInfo.age">
                            {{ formatAge( patient.age, { monthYear: 12, dayYear: 1 } ) }}
                        </span>
                    </transition>
                </print-col>
            </print-row>

            <transition name="print-fade">
                <print-row
                    v-if="patientInfo.birthday"
                    class="item-row"
                >
                    <print-col :span="24">
                        生日：{{ patient.birthday }}
                    </print-col>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="patientInfo.mobile"
                    class="item-row"
                >
                    <print-col :span="24">
                        手机号：{{ patient.mobile | filterMobileV2(patientInfo.mobileType) }}
                    </print-col>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="registrationInfo.doctor"
                    class="item-row"
                >
                    <print-col
                        :span="24"
                    >
                        {{ operateRole }}：<span>{{ printData.doctorName || '不指定' }}</span>
                        <transition name="print-fade">
                            <span v-if="!isTreatment && registrationInfo.revisitStatus">
                                {{ printData.revisitStatus === 2 ? '复诊' : '初诊' }}
                            </span>
                        </transition>
                    </print-col>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="registrationInfo.department && printData.departmentName"
                    class="item-row"
                >
                    <print-col :span="24">
                        科室：<span>{{ printData.departmentName }}</span>
                    </print-col>
                </print-row>
            </transition>

            <template v-if="registrationEnableCategory">
                <transition name="print-fade">
                    <print-row
                        v-if="registrationInfo.registrationCategory"
                        class="item-row"
                    >
                        <print-col :span="24">
                            号种：<span>{{ registrationCategoryName }}</span>
                        </print-col>
                    </print-row>
                </transition>
            </template>

            <transition name="print-fade">
                <print-row
                    v-if="registrationInfo.orderNo && !registType"
                    class="item-row text-bottom"
                >
                    <print-col :span="24">
                        序号：
                        <span
                            class="print-registration-orderno text-bold"
                        >
                            <template v-if="printData.isEnableRegUpgrade">
                                <template v-if="printData.orderNoStr">{{ `${orderNoPrefix}${printData.orderNoStr}号` }}</template>
                                <span
                                    v-else
                                    style="font-weight: normal;"
                                >{{ isGenerateOrderNoOnSign ? '签到后确定就诊号数' : '' }}</span>
                            </template>
                            <template v-else>
                                <template v-if="isTreatment">
                                    {{ `${orderNoPrefix}${printData.displayOrderNo}` }}
                                </template>
                                <template v-else>
                                    {{ printData.orderNoStr ? `${orderNoPrefix}${printData.orderNoStr}号` : '' }}
                                </template>
                            </template>
                        </span>
                    </print-col>
                </print-row>
            </transition>
        </div>

        <div
            v-if="hasOutpatientInfo"
            class="print-split-line"
        ></div>

        <div class="content-item">
            <transition name="print-fade">
                <print-row
                    v-if="outpatientInfo.outpatientDate"
                    class="item-row"
                >
                    <print-col

                        :span="24"
                    >
                        就诊日期：{{ printData.reserveDate }}{{ printData.dayOfWeek }}
                    </print-col>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="outpatientInfo.outpatientTime"
                    class="item-row"
                >
                    <print-col :span="24">
                        就诊时间：{{ printData.reserveStart }}~{{ printData.reserveEnd }}
                    </print-col>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="outpatientInfo.signInTime && printData.signInTime"
                    class="item-row"
                >
                    <print-col :span="24">
                        签到时间：{{ printData.signInTime }}
                    </print-col>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="outpatientInfo.consultingRoom"
                    class="item-row"
                >
                    <print-col :span="24">
                        就诊诊室：{{ printData.consultingRoomName }}
                    </print-col>
                </print-row>
            </transition>
            <transition name="print-fade">
                <print-row
                    v-if="outpatientInfo.formItemProducts && registType"
                    class="item-row"
                >
                    <print-col :span="24">
                        预约项目：{{ registrationProducts }}
                    </print-col>
                </print-row>
            </transition>
            <transition name="print-fade">
                <print-row
                    v-if="outpatientInfo.consultingRemark"
                    class="item-row"
                >
                    <print-col :span="24">
                        就诊备注：{{ printData.visitSourceRemark || printData.remark || '' }}
                    </print-col>
                </print-row>
            </transition>
        </div>

        <template v-if="!isTreatment">
            <div
                v-if="feeInfo.amount || showNetIncome "
                class="print-split-line"
            ></div>

            <div class="content-item">
                <transition name="print-fade">
                    <print-row
                        v-if="feeInfo.amount"
                        class="item-row"
                    >
                        <print-col :span="24">
                            金额：{{ printData.registrationFee | formatMoney }}元
                            <template v-if="!printData.payStatus">
                                (未付)
                            </template>
                        </print-col>
                    </print-row>
                </transition>

                <transition name="print-fade">
                    <print-row
                        v-if="showNetIncome"
                        class="item-row"
                    >
                        <print-col :span="24">
                            实付：<span v-if="payModeStr">({{ payModeStr }})</span>{{ printData.receivedFee | formatMoney }}元
                        </print-col>
                    </print-row>
                </transition>
            </div>
        </template>

        <template v-if="hasHealthCardPay && (shebaoConfig.cardInfo || shebaoConfig.settlementInfo || shebaoConfig.balanceInfo)">
            <div class="print-split-line"></div>

            <div class="content-item">
                <!-- 医保卡信息 -->
                <transition name="print-fade">
                    <print-row
                        v-if="shebaoConfig.cardInfo"
                        class="item-row"
                    >
                        <print-col :span="24">
                            医保卡号：{{ shebaoPayment.cardId }}
                        </print-col>
                        <print-col :span="24">
                            持卡人：{{ shebaoPayment.cardOwner }}
                        </print-col>
                    </print-row>
                </transition>

                <!-- 结算信息 -->
                <transition name="print-fade">
                    <print-row
                        v-if="shebaoConfig.settlementInfo"
                        class="item-row"
                    >
                        <print-col
                            v-if="shebaoPayment.fundPaymentFee && shebaoPayment.fundPaymentFee !== '0'"
                            :span="24"
                        >
                            基金支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}
                        </print-col>
                        <print-col
                            v-if="extraInfo.hifpPay && extraInfo.hifpPay !== '0'"
                            :span="24"
                            style="margin-left: 10px;"
                        >
                            统筹支出：{{ extraInfo.hifpPay | formatMoney }}
                        </print-col>
                        <print-col
                            v-if="extraInfo.cvlservPay && extraInfo.cvlservPay !== '0'"
                            :span="24"
                            style="margin-left: 10px;"
                        >
                            公务员补助：{{ extraInfo.cvlservPay | formatMoney }}
                        </print-col>
                        <print-col
                            v-if="extraInfo.hifmiPay && extraInfo.hifmiPay !== '0'"
                            :span="24"
                            style="margin-left: 10px;"
                        >
                            大病保险：{{ extraInfo.hifmiPay | formatMoney }}
                        </print-col>
                        <print-col
                            v-if="extraInfo.hifobPay && extraInfo.hifobPay !== '0'"
                            :span="24"
                            style="margin-left: 10px;"
                        >
                            大额补助：{{ extraInfo.hifobPay | formatMoney }}
                        </print-col>
                        <print-col
                            v-if="extraInfo.mafPay && extraInfo.mafPay !== '0'"
                            :span="24"
                            style="margin-left: 10px;"
                        >
                            医疗救助：{{ extraInfo.mafPay | formatMoney }}
                        </print-col>
                        <print-col
                            v-if="extraInfo.hifesPay && extraInfo.hifesPay !== '0'"
                            :span="24"
                            style="margin-left: 10px;"
                        >
                            企业补充医疗保险：{{ extraInfo.hifesPay | formatMoney }}
                        </print-col>
                        <print-col
                            v-if="extraInfo.othPay && extraInfo.othPay !== '0'"
                            :span="24"
                            style="margin-left: 10px;"
                        >
                            其他支出：{{ extraInfo.othPay | formatMoney }}
                        </print-col>
                        <print-col :span="24">
                            账户支付：{{ shebaoPayment.accountPaymentFee | formatMoney }}
                        </print-col>
                        <print-col :span="24">
                            现金支付：{{ shebaoPayment.personalPaymentFee | formatMoney }}
                        </print-col>
                    </print-row>
                </transition>

                <!-- 余额信息 -->
                <transition name="print-fade">
                    <print-row
                        v-if="shebaoConfig.balanceInfo"
                        class="item-row"
                    >
                        <print-col :span="24">
                            支付前余额：{{ shebaoPayment.beforeCardBalance | formatMoney }}
                        </print-col>
                        <print-col :span="24">
                            支付后余额：{{ shebaoPayment.cardBalance | formatMoney }}
                        </print-col>
                        <template v-if="$abcSocialSecurity && $abcSocialSecurity.config.isNeedQueryFundQuota">
                            <print-col
                                :span="24"
                            >
                                统筹余额：{{ extraInfo.generalFundpayBalc | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="extraInfo.chronicDiseaseDBalc"
                                :span="24"
                            >
                                大病余额：{{ extraInfo.chronicDiseaseDBalc | formatMoney }}
                            </print-col>
                            <print-col
                                v-if="extraInfo.chronicDiseaseMBalc"
                                :span="24"
                            >
                                慢病余额：{{ extraInfo.chronicDiseaseMBalc | formatMoney }}
                            </print-col>
                        </template>
                    </print-row>
                </transition>
            </div>
        </template>

        <div
            v-if="showFooterInfo"
            class="print-split-line"
        ></div>

        <div class="content-item">
            <transition name="print-fade">
                <print-row
                    v-if="ticketFooter.operator && printData.createdBy"
                    class="item-row"
                >
                    <transition name="print-fade">
                        <print-col
                            :span="24"
                        >
                            操作员：{{ printData.createdBy }}
                        </print-col>
                    </transition>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="(ticketFooter.reserveDate && printData.isReserved) || isTreatment"
                    class="item-row"
                >
                    <transition name="print-fade">
                        <print-col
                            :span="24"
                        >
                            预约日期：{{ printData.created }}
                        </print-col>
                    </transition>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="ticketFooter.printDate"
                    class="item-row"
                >
                    <transition name="print-fade">
                        <print-col
                            :span="24"
                        >
                            打印时间：{{ new Date() | parseTime('y-m-d h:i:s') }}
                        </print-col>
                    </transition>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="ticketFooter.address && clinic.addressDetail"
                    class="item-row"
                >
                    <print-col :span="24">
                        地址：{{ clinic.addressDetail }}
                    </print-col>
                </print-row>
            </transition>

            <transition name="print-fade">
                <print-row
                    v-if="ticketFooter.mobile && clinic.contactPhone"
                    class="item-row"
                >
                    <print-col :span="24">
                        电话：{{ clinic.contactPhone }}
                    </print-col>
                </print-row>
            </transition>
        </div>

        <template v-if="config.remark">
            <div class="print-split-line"></div>
            <div class="content-item">
                <print-row class="item-row">
                    <print-col
                        :span="24"
                        style="white-space: pre-wrap;"
                        v-html="config.remark"
                    ></print-col>
                </print-row>
            </div>
        </template>

        <template v-if="ticketFooter.expiredReminder">
            <div class="print-split-line"></div>
            <transition name="print-fade">
                <print-row
                    v-if="ticketFooter.expiredReminder"
                    class="item-row"
                >
                    <print-col
                        :span="24"
                        class="print-registration-type"
                    >
                        当日有效&nbsp;&nbsp;过期作废
                    </print-col>
                </print-row>
            </transition>
        </template>

        <div
            v-if="!isTreatment && ticketFooter.invoiceCode && invoiceQrcode"
            class="content-item"
            style="margin-bottom: 2pt;"
        >
            <print-row>
                <print-col
                    :span="24"
                    class="print-registration-title text-info"
                >
                    <transition name="print-fade">
                        <div class="qr-code-wrapper">
                            <img
                                class="qr-code"
                                borderthin="true"
                                :src="invoiceQrcode"
                                alt=""
                                style="vertical-align: middle;"
                            />
                            <div class="qr-code-tips">
                                微信扫一扫查看发票
                            </div>
                        </div>
                    </transition>
                </print-col>
            </print-row>
        </div>
    </div>
</template>

<script>
    import RegistrationMixin from './registration.js'
    import {GENERATE_ORDER_NO_TIME_TYPE, RegistType, RESERVATION_TIME_TYPE} from "../../constant/print-constant";
    import {textToBase64BarCode} from "../../common/utils";
    import {filterMobileV2} from "../../common/medical-transformat";

    export default {
        name: "Index",
        filters: {
            filterMobileV2,
        },
        mixins: [ RegistrationMixin ],
        props: {
            printData: {
                type: Object,
                required: true,
                default: () => {
                    return {}
                }
            },
            config: {
                type: Object,
                required: true,
            }
        },
        computed: {
            registrationEnableCategory() {
                return this.printData.registrationEnableCategory
            },

            registrationCategoryName() {
                if (!this.registrationEnableCategory) return '';

                const categoryEnum = {
                    0: '普通门诊',
                    1: '专家门诊',
                    2: '便民门诊'
                }

                return categoryEnum[this.printData.registrationCategory] || ''
            },

            orderNoPrefix() {
                if (!this.registrationEnableCategory) return '';

                if (this.printData.registrationCategory === 2) return '便民';

                return ''
            },

            registType () {
                return this.printData.registrationModeType === RegistType.LINGHUOTIMETYOE? 1 : 0
            },
            registOrganTitle() {
                if (this.registType) {
                    return this.isTreatment ? '理疗预约单' : '预约单'
                }
                return this.typeTitle
            },
            registrationProducts() {
                let registrationProductsList = this.printData.registrationProducts || []
                return  registrationProductsList.map(item=>{
                    return item.displayName;
                }).join(', ')
            },
            barcodeSrc() {
                const barcode = this.printData.patientOrderNo ? `${this.printData.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode);
            },
            // 固定模式 - 分段&开启签到取号
            isGenerateOrderNoOnSign() {
                const {
                    modeType, fixedOrderDisplayServiceType, generateOrderNoTime,
                } = this.printData?.registrationConfig || {};
                return modeType === RegistType.HAOYUANTYPE && fixedOrderDisplayServiceType === RESERVATION_TIME_TYPE.OTHER && generateOrderNoTime === GENERATE_ORDER_NO_TIME_TYPE.SIGN_IN;
            },
            shebaoPayment() {
                return this.printData.shebaoPayment;
            },
            // 是否是医保支付
            hasHealthCardPay() {
                return !!this.shebaoPayment;
            },
            extraInfo() {
                return this.shebaoPayment?.extraInfo || {};
            },
            shebaoConfig() {
                return this.config.healthCardInfo || {};
            },
            invoiceQrcode() {
                return this.printData.invoiceQrcode;
            },
        }
    }
</script>

<style lang="scss">
.print-registration-content {
    padding-top: 0 !important;

    .content-item {
        .barcode-image {
            width: 94pt;
            height: 40pt;
            margin-top: 4pt;
            vertical-align: middle;
            border: 0;
        }

        .print-registration-title {
            .qr-code-wrapper {
                margin-top: 8pt;

                .qr-code {
                    width: 100pt;
                    height: 100pt;
                }

                .qr-code-tips {
                    margin-top: 4pt;
                    font-size: 14pt;
                }
            }
        }
    }
}
</style>
