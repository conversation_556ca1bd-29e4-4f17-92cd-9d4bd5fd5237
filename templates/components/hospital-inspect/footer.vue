<template>
    <div class="hospital-exam-footer-wrapper">
        <abc-print-space :value="8"></abc-print-space>

        <div
            :class="[
                'exam-doctor-info',
                {
                    'custom-doctor-info': isGastroscopy
                }
            ]"
        >
            <abc-print-row :gap-y="6">
                <abc-print-col
                    v-for="(item,i) in footerItems"
                    :key="i"
                    :span="item.span"
                    overflow
                >
                    <span>{{ item.label }}</span>

                    <span :style="{opacity: !!item.label ? 1 : 0}">：</span>

                    <template v-if="item.is">
                        <component
                            :is="item.is"
                            :value="item.value"
                        ></component>
                    </template>

                    <template v-else>
                        <span>{{ item.value }}</span>
                    </template>
                </abc-print-col>
            </abc-print-row>
        </div>
        <div>
            <abc-print-row class="exam-remark">
                <abc-print-col :span="24">
                    {{ footerConfig.remark }}
                </abc-print-col>
            </abc-print-row>
        </div>
    </div>
</template>

<script>
    import AbcPrintRow from '../layout/abc-layout/abc-row.vue'
    import AbcPrintCol from '../layout/abc-layout/abc-col.vue'
    import AbcPrintSpace from "../layout/space.vue";
    import { INSPECT_DEVICE_TYPE } from "../../constant/print-constant";

    export default {
        name: "ExamFooter",
        components: {
            AbcPrintSpace,
            AbcPrintRow,
            AbcPrintCol,
        },
        props: {
            printData: {
                type: Object,
                default: () => ({}),
            },
            footerItems: {
                type: Array,
                default: () => [],
            },
            config: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            organPrintView() {
                return this.printData.organPrintView || {};
            },
            footerConfig(){
                return this.config.footer || {};
            },

            isGastroscopy() {
                return +this.printData.deviceType === INSPECT_DEVICE_TYPE['内窥镜'];
            },

            suggestion() {
                return this.printData.examinationSheetReport?.suggestion || '';
            },

            inspectionSite() {
                return this.printData.examinationSheetReport?.inspectionSite || '';
            },
        },
    }
</script>

<style lang="scss">
@import "../layout/_print-layout";

.hospital-exam-footer-wrapper {
    .exam-doctor-info {
        padding: 8pt 0;
        border-bottom: 1pt solid #000000;
      
        &.custom-doctor-info {
          border-bottom: none;
          padding-bottom: 0;
        }

        .abc-print-col {
            // padding-bottom: 6pt;
            white-space: nowrap;
            word-break: keep-all;
        }
    }

    .exam-remark {
        padding: 8pt 4pt 0 0;
    }
}

</style>