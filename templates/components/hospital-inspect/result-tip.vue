<template>
    <div>
        <template v-if="currentTip.isText">
            <span :style="currentTip.style">
                {{ currentTip.content }}
            </span>
        </template>

        <template v-if="currentTip.isArrowUp">
            <span :style="{ color: '#ff3333' }">
                ↑
            </span>
        </template>

        <template v-if="currentTip.isArrowDown">
            <span :style="{ color: '#2680f7' }">↓</span> 
        </template>
    </div>
</template>

<script>
    import { checkInspectResultIsAbnormal } from '../../common/utils.js';

    export default {
        name: 'ResultTip',

        props: {
            item: {
                type: Object,
                default: () => ({}),
            },
        },

        computed: {
            currentTip() {
                return checkInspectResultIsAbnormal(this.item);
            },
        },
    };
</script>