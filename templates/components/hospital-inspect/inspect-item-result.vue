<template>
    <div
        class="inspect-item-result-wrapper"
        :style="{ justifyContent: isText ? 'space-between' : 'flex-start' }"
    >
        <template v-if="isText">
            <span>
                {{ textValue }}
            </span>

            <span v-if="unit">
                {{ unit }}
            </span>
        </template>

        <template v-if="isToothPosition">
            <div
                style="margin-right: 8px"
                v-html="toothHtml"
            ></div>

            <span>
                {{ textValue }}
            </span>
        </template>
    </div>
</template>

<script>
    import {formatToothNos2Html} from "../../common/medical-transformat";

    const COMPONENT_TYPE_ENUM = {
        input: 3,
        select: 6,
        toothSelect: 10,
    };

    export default {
        name: 'InspectItemResult',

        props: {
            item: {
                type: Object,
                default: () => ({})
            }
        },

        computed: {
            isText() {
                return [COMPONENT_TYPE_ENUM.input, COMPONENT_TYPE_ENUM.select].includes(this.item.componentType);
            },

            isToothPosition() {
                return this.item.componentType === COMPONENT_TYPE_ENUM.toothSelect;
            },

            unit() {
                return this.item.unit;
            },

            toothNos() {
                return this.item.value?.toothNos || [];
            },

            textValue() {
                if(this.isText) {
                    return this.item.value;
                }

                if(this.isToothPosition) {
                    return this.item.value?.value;
                }

                return ''
            },

            toothHtml() {
                return formatToothNos2Html(this.toothNos, '144px')
            }
        }
    }
</script>

<style lang="scss">
.inspect-item-result-wrapper {
    display: flex;
    align-items: center;

    .global-tooth-selected-quadrant {
        position: relative;
        display: inline-flex;
        flex-direction: column;
        width: auto;
        min-width: 32pt;
        height: 17pt;
        vertical-align: middle;

        .top-tooth,
        .bottom-tooth {
            display: flex;
            align-items: center;
            width: 100%;
            min-width: 32pt;
        }

        .left-tooth,
        .right-tooth {
            display: flex;
            align-items: center;
            width: 50%;
            height: 7pt;
            padding: 0 1pt;
            font-family: 'MyKarlaRegular';
            font-size: 9pt;
            letter-spacing: 1px;
            user-select: none;
        }

        .left-tooth {
            justify-content: flex-end;
            border-right: 1pt solid #7a8794;
        }

        .top-tooth {
            min-width: 32pt;
            border-bottom: 1pt solid #7a8794;

            > div {
                padding-bottom: 1px;
            }
        }

        .bottom-tooth {
            > div {
                padding-top: 1px;
            }
        }

        &.all-tooth {
            display: inline-flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }

        .all-tooth {
            min-width: 18pt;
            height: 11pt;
            font-size: 9pt;
            line-height: 11pt;
        }

        &.no-data {
            .left-tooth {
                border-right: 1px dashed #000000;
            }

            .top-tooth {
                border-bottom: 1px dashed #000000;
            }
        }
    }
}
</style>