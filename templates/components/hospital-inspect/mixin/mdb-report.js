import { parseTime } from "../../../common/utils.js";
import {
    INSPECT_DEVICE_TYPE,
} from "../../../constant/print-constant.js";

// 彩超打印字段
export default {
    computed: {
        isMDB() {
            return  INSPECT_DEVICE_TYPE.MDB === +this.printData.deviceType;
        },

        mdbFooterItems() {
            return [
                {
                    label: '操作医师',
                    value: this.printData.checkerName,
                },
                {
                    label: '记录医师',
                    value: this.printData.examinationSheetReport.recordDoctorName,
                },
                this.printData.examinationSheetReport.consultationDoctorName ? {
                    label: '会诊医师',
                    value: this.printData.examinationSheetReport.consultationDoctorName,
                } : {
                    label: '',
                    value: ''
                },
                {
                    label: '检查时间',
                    value: parseTime(
                        this.printData.testTime,
                        'y-m-d h:i',
                        true
                    ),
                },
                {
                    label: '报告时间',
                    value: parseTime(
                        this.printData.reportTime,
                        'y-m-d h:i',
                        true
                    ),
                },
                {
                    label: '审核时间',
                    value: parseTime(
                        this.printData.checkTime,
                        'y-m-d h:i',
                        true
                    ),
                },
            ];
        },
    }
}