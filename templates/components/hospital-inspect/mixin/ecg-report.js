import { formatInspectDiagnosisAdvice, parseTime } from '../../../common/utils.js'
import { INSPECT_DEVICE_TYPE } from '../../../constant/print-constant.js'

// 彩超打印字段
export default {
    computed: {
        isASO() {
            return INSPECT_DEVICE_TYPE.ASO === +this.printData.deviceType
        },

        isECG() {
            return INSPECT_DEVICE_TYPE['心电图'] === +this.printData.deviceType
        },

        isC1314() {
            return INSPECT_DEVICE_TYPE.C13_14 === +this.printData.deviceType
        },

        isBodyComposition() {
            return INSPECT_DEVICE_TYPE.BODY_COMPOSITION === +this.printData.deviceType
        },

        ECGOrASOOrMDBImage() {
            return this.imageFileList[0]?.[0]?.url || ''
        },

        ecgFooterItems() {
            return [
                {
                    label: '报告医师',
                    value: this.printData.checkerName,
                },
                {
                    label: '记录人',
                    value: this.printData.examinationSheetReport.recordDoctorName,
                },
                {
                    label: '',
                    value: '',
                },
                {
                    label: '检查时间',
                    value: parseTime(this.printData.testTime, 'y-m-d h:i', true),
                },
                {
                    label: '报告时间',
                    value: parseTime(this.printData.reportTime, 'y-m-d h:i', true),
                },
                {
                    label: '审核时间',
                    value: parseTime(this.printData.checkTime, 'y-m-d h:i', true),
                },
            ]
        },

        asoFooterItems() {
            return [
                {
                    label: '操作医师',
                    value: this.printData.checkerName,
                },
                {
                    label: '记录医师',
                    value: this.printData.examinationSheetReport.recordDoctorName,
                },
                this.printData.examinationSheetReport.consultationDoctorName
                    ? {
                        label: '会诊医师',
                        value: this.printData.examinationSheetReport.consultationDoctorName,
                    }
                    : {
                        label: '',
                        value: '',
                    },
                {
                    label: '检查时间',
                    value: this.printData.testTime ? parseTime(this.printData.testTime, 'y-m-d h:i', true) : '',
                },
                {
                    label: '报告时间',
                    value: this.printData.reportTime ? parseTime(this.printData.reportTime, 'y-m-d h:i', true) : '',
                },
                {
                    label: '审核时间',
                    value: this.printData.checkTime ? parseTime(this.printData.checkTime, 'y-m-d h:i', true) : '',
                },
            ]
        },

        ecgResultItemList() {
            const { diagnosisEntryItems = [] } = this.printData.examinationSheetReport

            const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems)

            return [
                {
                    label: '诊断意见',
                    value: diagnosisResult || '',
                },
            ]
        },
    },
}
