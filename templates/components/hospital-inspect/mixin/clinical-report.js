import { formatInspectDiagnosisAdvice, parseTime } from "../../../common/utils.js";
import {
    INSPECT_DEVICE_TYPE,
} from "../../../constant/print-constant.js";
import InspectItemResult from "../inspect-item-result.vue";

export const clinicalColumns = [
    {
        label: '项目',
        key: 'name',
        width: 1,
    },
    {
        label: '结果',
        key: 'result',
        width: 1,
        render: (createElement, row) => {
            return createElement(
                InspectItemResult,
                {
                    props: {
                        item: row,
                    },
                },
            )
        },
    },
];

// 临床检查（不含一般检查）检查打印字段
export default {
    computed: {
        isClinical() {
            return [
                INSPECT_DEVICE_TYPE.INTERNAL,
                INSPECT_DEVICE_TYPE.SURGERY,
                INSPECT_DEVICE_TYPE.ENT,
                INSPECT_DEVICE_TYPE.MOUTH,
                INSPECT_DEVICE_TYPE.EYE,
                INSPECT_DEVICE_TYPE.GYNECOLOGY,
            ].includes(this.printData.deviceType);
        },

        clinicalFooterItems() {
            return [
                {
                    label: '检查医师',
                    value: this.printData.testerName,
                },
                {
                    label: '审核医师',
                    value: this.printData.checkerName,
                },
                {
                    label: '',
                    value: '',
                },
                {
                    label: '检查时间',
                    value: this.printData.testTime ? parseTime(
                        this.printData.testTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                },
                {
                    label: '报告时间',
                    value: this.printData.reportTime ? parseTime(
                        this.printData.reportTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                },
                {
                    label: '审核时间',
                    value:  this.printData.checkTime ? parseTime(
                        this.printData.checkTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                },
            ];
        },

        clinicalColumns() {
            return clinicalColumns;
        },

        clinicalList() {
            return this.printData.itemsValue || [];
        },

        clinicalDiagnosisItem() {
            const {
                diagnosisEntryItems = [],
            } = this.printData.examinationSheetReport;

            const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems);

            return {
                label: '诊断意见',
                value: diagnosisResult || '',
                height: '50pt',
            };
        },
    },
}