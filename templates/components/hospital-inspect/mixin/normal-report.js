import { formatInspectDiagnosisAdvice, parseTime } from "../../../common/utils.js";
import {
    INSPECT_DEVICE_TYPE,
} from "../../../constant/print-constant.js";
import ResultTip from '../result-tip.vue';
import InspectItemResult from "../inspect-item-result.vue";

export const normalReportColumns = [
    {
        label: '项目',
        key: 'name',
        width: 1,
    },
    {
        label: '结果',
        key: 'result',
        width: 1,
        render: (createElement, row) => {
            return createElement(
                InspectItemResult,
                {
                    props: {
                        item: row,
                    },
                },
            )
        },
    },
    {
        label: '提示',
        key: 'tip',
        width: 1,
        render: (createElement, row) => {
            return createElement(
                ResultTip, 
                {
                    props: {
                        item: row,
                    },
                },
            )
        },
    },
    {
        label: '参考范围',
        key: 'referenceRange',
        width: 1,
        render: (createElement, row) => {
            console.log(row);
            const { ref } = row;
            if (ref.min && ref.max) {
                return (
                    createElement(
                        'div',
                        null,
                        `${ref.min}-${ref.max}${row.unit}`,
                    )
                );
            }

            return '';
        },
    },
]

// 一般检查打印字段
export default {
    computed: {
        isNormal() {
            return +this.printData.deviceType === INSPECT_DEVICE_TYPE.NORMAL;
        },

        normalFooterItems() {
            return [
                {
                    label: '检查医师',
                    value: this.printData.testerName,
                },
                {
                    label: '审核医师',
                    value: this.printData.checkerName,
                },
                {
                    label: '',
                    value: '',
                },
                {
                    label: '检查时间',
                    value: this.printData.testTime ? parseTime(
                        this.printData.testTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                },
                {
                    label: '报告时间',
                    value: this.printData.reportTime ? parseTime(
                        this.printData.reportTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                },
                {
                    label: '审核时间',
                    value: this.printData.checkTime ? parseTime(
                        this.printData.checkTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                },
            ];
        },

        normalColumns() {
            return normalReportColumns;
        },

        normalList() {
            return this.printData.itemsValue || [];
        },

        normalDiagnosisItem() {
            const {
                diagnosisEntryItems = [],
            } = this.printData.examinationSheetReport;

            const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems);

            return {
                label: '诊断意见',
                value: diagnosisResult || '',
                height: '50pt',
            };
        },
    },
}