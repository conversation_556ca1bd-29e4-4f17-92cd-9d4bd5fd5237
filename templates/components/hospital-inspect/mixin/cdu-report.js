import {
    formatInspectDiagnosisAdvice,
    parseTime,
} from "../../../common/utils.js";
import {
    INSPECT_DEVICE_TYPE,
} from "../../../constant/print-constant.js";
import HandSign from "../../hand-sign/index.vue";

// 彩超打印字段
export default {
    computed: {
        isCDU() {
            return +this.printData.deviceType === INSPECT_DEVICE_TYPE['彩超'];
        },

        cduFooterItems() {
            const footerConfig = this.config.footer || {};
            return [
                {
                    label: '报告医师',
                    value: footerConfig.operateDoctorSignature === 1 ? this.printData.tester?.handSign : footerConfig.operateDoctorSignature === 0 ? this.printData.testerName : '',
                    is: footerConfig.operateDoctorSignature === 1 ? HandSign : '',
                    isHidden:!footerConfig.operateDoctor,
                },
                {
                    label: '审核医师',
                    value: footerConfig.checkDoctorSignature === 1 ? this.printData.checker?.handSign : footerConfig.checkDoctorSignature === 0 ? this.printData.checkerName : '',
                    is: footerConfig.checkDoctorSignature === 1 ? HandSign : '',
                    isHidden:!footerConfig.checkDoctor,
                },
                {
                    label: '记录医师',
                    value: footerConfig.recordDoctorSignature === 1 ?
                        this.printData.examinationSheetReport.recordDoctor?.handSign : footerConfig.recordDoctorSignature === 0 ? this.printData.examinationSheetReport.recordDoctorName : '',
                    is: footerConfig.recordDoctorSignature === 1 ? HandSign : '',
                    isHidden:!footerConfig.recordDoctor,
                },
                {
                    label: '会诊医师',
                    value: footerConfig.consultationDoctorSignature === 1 ?
                        this.printData.examinationSheetReport.consultationDoctor?.handSign : footerConfig.consultationDoctorSignature === 0 ? this.printData.examinationSheetReport.consultationDoctorName : '',
                    is: footerConfig.consultationDoctorSignature === 1 ? HandSign : '',
                    isHidden:!footerConfig.consultationDoctor,
                },
                {
                    label: '检查时间',
                    value: this.printData.testTime ? parseTime(
                        this.printData.testTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                    isHidden:!footerConfig.inspectionTime,
                },
                {
                    label: '报告时间',
                    value: this.printData.reportTime ? parseTime(
                        this.printData.reportTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                    isHidden:!footerConfig.reportTime,
                },
                {
                    label: '审核时间',
                    value: this.printData.checkTime ? parseTime(
                        this.printData.checkTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                    isHidden:!footerConfig.auditTime,
                },
            ];
        },

        cduReportList() {
            return this.reportList.map((report) => {
                const {
                    videoDescription,
                    diagnosisEntryItems = [],
                    imageFiles = [],
                } = report;
                const contentConfig = this.config.content || {};

                const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems);
                const hasImage = imageFiles.length > 0;

                return {
                    imageList: this.generateImageRowList(imageFiles || [], 4),
                    resultItemList: [
                        {
                            label: contentConfig.inspectionHeader || '超声所见',
                            value: videoDescription || '',
                            height: hasImage ? '' : '40%',
                        },
                        {
                            label: contentConfig.conclusionHeader || '诊断意见',
                            value: diagnosisResult || '',
                            height: hasImage ? '' : '20%',
                        },
                    ],
                }
            })
        },
    },
}