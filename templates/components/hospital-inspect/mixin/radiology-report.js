import { formatInspectDiagnosisAdvice, parseTime } from "../../../common/utils.js";
import {
    INSPECT_DEVICE_TYPE,
} from "../../../constant/print-constant.js";
import HandSign from "../../hand-sign/index.vue";

// 放射科打印字段
export default {
    computed: {
        isRadiology() {
            return [
                INSPECT_DEVICE_TYPE.CT,
                INSPECT_DEVICE_TYPE.DR,
                INSPECT_DEVICE_TYPE.MG,
                INSPECT_DEVICE_TYPE.MR,
            ].includes(this.printData.deviceType);
        },

        radiologyFooterItems() {
            const footerConfig = this.config.footer || {};
            const {
                auditDoctorSignature,
                reportDoctorSignature,
            } = footerConfig;

            return [
                {
                    label: '报告医师',
                    value: reportDoctorSignature === 1 ? this.printData.tester?.handSign : reportDoctorSignature === 0 ? this.printData.testerName : '',
                    is: reportDoctorSignature === 1 ? HandSign : '',
                    isHidden:!footerConfig.reportDoctor,
                },
                {
                    label: '审核医师',
                    value: auditDoctorSignature === 1 ? this.printData.checker?.handSign : auditDoctorSignature === 0 ? this.printData.checkerName : '',
                    is: auditDoctorSignature === 1 ? HandSign : '',
                    isHidden:!footerConfig.auditDoctor,
                },
                {
                    label: '会诊医师',
                    value: footerConfig.consultationDoctorSignature === 1 ?
                        this.printData.examinationSheetReport.consultationDoctor?.handSign :  footerConfig.consultationDoctorSignature === 0 ? this.printData.examinationSheetReport.consultationDoctorName : '',
                    is: footerConfig.consultationDoctorSignature === 1 ? HandSign : '',
                    isHidden:!footerConfig.consultationDoctor,
                },
                {
                    label: '检查时间',
                    value: this.printData.testTime ? parseTime(
                        this.printData.testTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                    isHidden:!footerConfig.inspectionTime,
                },
                {
                    label: '报告时间',
                    value: this.printData.reportTime ? parseTime(
                        this.printData.reportTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                    isHidden:!footerConfig.reportTime,
                },
                {
                    label: '审核时间',
                    value: this.printData.checkTime ? parseTime(
                        this.printData.checkTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                    isHidden:!footerConfig.auditTime,
                },
            ];
        },

        radiologyReportList() {
            return this.reportList.map(report => {
                const {
                    videoDescription,
                    diagnosisEntryItems = [],
                    imageFiles = [],
                } = report;

                const hasImage = imageFiles.length > 0;
                const contentConfig = this.config.content || {};

                const diagnosisResult = formatInspectDiagnosisAdvice(diagnosisEntryItems);

                return {
                    imageList: this.generateImageRowList(report.imageFiles || [], 4),

                    resultItemList: [
                        {
                            label: contentConfig.inspectionHeader ||`影像所见`,
                            value: videoDescription || '',
                            height: hasImage ? '' : '40%',
                        },
                        {
                            label: contentConfig.conclusionHeader ||`诊断意见`,
                            value: diagnosisResult || '',
                            height: hasImage ? '' : '20%',
                        },
                    ],
                }
            });
        },
    },
}