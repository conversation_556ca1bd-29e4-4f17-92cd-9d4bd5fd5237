import { formatInspectDiagnosisAdvice, parseTime } from "../../../common/utils.js";
import {
    INSPECT_DEVICE_TYPE,
} from "../../../constant/print-constant.js";
import HandSign from "../../hand-sign/index.vue";

// 彩超打印字段
export default {
    computed: {
        isGastroscopy() {
            return +this.printData.deviceType === INSPECT_DEVICE_TYPE['内窥镜'];
        },

        gastroscopyFooterItems() {
            const footerConfig = this.config.footer || {};

            return [
                {
                    label: '检查医师',
                    value: footerConfig.reportDoctorSignature === 2 ? this.printData.tester?.handSign : (
                        footerConfig.reportDoctorSignature === 1 ? this.printData.testerName : ''
                    ),
                    is: footerConfig.reportDoctorSignature === 2 ? HandSign : '',
                    isHidden:!footerConfig.reportDoctor,
                },
                {
                    label: '审核医师',
                    value: footerConfig.auditDoctorSignature === 2 ? this.printData.checker?.handSign : (
                        footerConfig.auditDoctorSignature === 1 ? this.printData.checkerName : ''
                    ),
                    is: footerConfig.auditDoctorSignature === 2 ? HandSign : '',
                    isHidden:!footerConfig.auditDoctor,
                },
                {
                    label: '报告时间',
                    value: this.printData.reportTime ? parseTime(
                        this.printData.reportTime,
                        'y-m-d h:i',
                        true,
                    ) : '',
                    isHidden:!footerConfig.reportTime,
                },
            ];
        },

        gastroscopyReportList() {
            return this.reportList.map(report => {
                const hasImage = report.imageFiles.length > 0;

                return {
                    ...report,

                    imageList: this.generateImageRowList(report.imageFiles || [], 4),

                    resultItemList: [
                        {
                            label: '检查所见',
                            value: report.videoDescription || '',
                            height: hasImage ? '' : '40%',
                        },
                        {
                            label: `诊断意见`,
                            value: formatInspectDiagnosisAdvice(report.diagnosisEntryItems || []) || '',
                            height: hasImage ? '' : '20%',
                        },
                    ],
                }
            });
        },
    },
}