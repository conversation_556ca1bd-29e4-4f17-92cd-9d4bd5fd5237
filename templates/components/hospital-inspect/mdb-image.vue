<template>
    <div
        id="MdbImage"
        class="mdb-image-wrapper"
    >
    </div>
</template>

<script>
    import * as d3 from 'd3';
    import {
        MDB_AGE_MAX, MDB_T_MAX, MDB_T_MIN, MDB_AGE_MIN,
        MDB_SVG_HEIGHT, MDB_SVG_MARGIN_BOTTOM, MDB_SVG_WIDTH, MDB_SVG_MARGIN_TOP, MDB_SVG_MARGIN_LEFT, MDB_SVG_MARGIN_RIGHT,
        DASH_ARRAY, CIRCLE_RADIUS, MDB_RECT_DATA_SET,
    } from '../../constant/print-constant';

    export default {
        name: 'MdbImage',

        props: {
            age: {
                type: Number,
                default: 0,
            },

            t: {
                type: Number,
                default: 0,
            },
        },

        mounted() {
            // Declare the x (horizontal position) scale.
            const x = d3.scaleLinear()
                .domain([MDB_AGE_MIN, MDB_AGE_MAX])
                .range([MDB_SVG_MARGIN_LEFT, MDB_SVG_WIDTH - MDB_SVG_MARGIN_RIGHT]);

            // Declare the y (vertical position) scale.
            const y = d3.scaleLinear()
                .domain([MDB_T_MIN, MDB_T_MAX])
                .range([MDB_SVG_HEIGHT - MDB_SVG_MARGIN_BOTTOM, MDB_SVG_MARGIN_TOP]);

            // Create the SVG container.
            const svg = d3.select(this.$el)
                .append('svg')
                .attr('width', MDB_SVG_WIDTH)
                .attr('height', MDB_SVG_HEIGHT);

            // Add the x-axis.
            svg.append('g')
                .attr('transform', `translate(0,${MDB_SVG_HEIGHT - MDB_SVG_MARGIN_BOTTOM})`)
                .call(d3.axisBottom(x));

            // Add the y-axis.
            svg.append('g')
                .attr('transform', `translate(${MDB_SVG_MARGIN_LEFT},0)`)
                .call(d3.axisLeft(y));

            // Add the x-axis label.
            svg.append('text')
                .attr('transform', `translate(${MDB_SVG_WIDTH / 2},${MDB_SVG_HEIGHT - 5})`)
                .attr('fill', '#7a8794')
                .style('text-anchor', 'middle')
                .style('font-size', '12px')
                .text('年龄/岁');


            // Add the y-axis label.
            svg.append('text')
                .attr('transform', `translate(${MDB_SVG_MARGIN_LEFT / 2 - 10},${MDB_SVG_HEIGHT / 2})rotate(-90)`)
                .style('text-anchor', 'middle')
                .attr('fill', '#7a8794')
                .style('font-size', '12px')
                .text('T值');

            // Add rectangle.
            svg.selectAll('.rect')
                .data(MDB_RECT_DATA_SET)
                .enter()
                .append('rect')
                .attr('x', (d) => x(d.x))
                .attr('y', (d) => y(d.y))
                .attr('width', x(MDB_AGE_MAX) - x(MDB_AGE_MIN))
                .attr('height', (d) => y(d.h1) - y(d.h2))
                .attr('fill', (d) => d.color)
                .attr('transform', 'translate(1, 0)');


            if (!this.age || !this.t) return;

            // Add circle.

            const circleDataSet = [
                {
                    fill: '#FFFFFF', x: this.age, y: this.t, stroke: '#6cbaf2',
                },
            ];
            svg.selectAll('.circle')
                .data(circleDataSet)
                .enter()
                .append('circle')
                .attr('cx', (d) => x(d.x))
                .attr('cy', (d) => y(d.y))
                .attr('r', CIRCLE_RADIUS)
                .attr('fill', (d) => d.fill)
                .attr('stroke', (d) => d.stroke)
                .attr('stroke-width', 1);


            // Add y line.
            svg.selectAll('.line')
                .data(circleDataSet)
                .enter()
                .append('line')
                .attr('x1', (d) => x(d.x))
                .attr('y1', (d) => y(d.y) + CIRCLE_RADIUS)
                .attr('x2', (d) => x(d.x))
                .attr('y2', MDB_SVG_HEIGHT - MDB_SVG_MARGIN_BOTTOM)
                .attr('stroke', (d) => d.fill)
                .attr('stroke-dasharray', DASH_ARRAY)
                .attr('stroke-width', 1)
                .attr('transform', 'translate(0.5, 0)');

            // Add x line.
            svg.selectAll('.line')
                .data(circleDataSet)
                .enter()
                .append('line')
                .attr('x1', (d) => x(d.x) - CIRCLE_RADIUS)
                .attr('y1', (d) => y(d.y))
                .attr('x2', MDB_SVG_MARGIN_LEFT)
                .attr('y2', (d) => y(d.y))
                .attr('stroke', (d) => d.fill)
                .attr('stroke-dasharray', DASH_ARRAY)
                .attr('stroke-width', 1)
                .attr('transform', 'translate(0, 0.5)');
        },
    };
</script>

<style lang="scss" scoped>
.mdb-image-wrapper {
  width: 300px;
  height: 200px;
}
</style>
