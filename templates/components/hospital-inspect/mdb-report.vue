<template>
    <div class="mdb-report-wrapper">
        <div class="mdb-title">
            检测结果
        </div>

        <div class="mdb-result-section-wrapper">
            <div class="mdb-result-item-wrapper">
                <div
                    v-for="(o, key) in resultOptions"
                    :key="key"
                    class="mdb-result-item"
                >
                    <div class="mdb-result-item-title">
                        {{ o.name }}
                    </div>

                    <div class="mdb-result-item-value">
                        {{ getItemByValue(o.name).value }}<span v-if="o.unit">{{ o.unit }}</span>
                    </div>
                </div>
            </div>

            <div
                v-if="getItemByValue('reportSheet').value"
                class="mdb-result-image-wrapper"
            >
                <img
                    v-if="getItemByValue('reportSheet').value"
                    :src="getItemByValue('reportSheet').value"
                    alt=""
                />
            </div>
        </div>

        <div class="mdb-title">
            诊断结论
        </div>

        <div class="mdb-diagnosis-section-wrapper">
            <div
                v-for="(o, key) in MDB_DIAGNOSIS_OPTION"
                :key="key"
                class="mdb-diagnosis-item"
            >
                <img
                    :src="o.url"
                    class="mdb-diagnosis-item-avatar"
                />

                <div class="mdb-diagnosis-item-content">
                    <div
                        class="mdb-diagnosis-item-radio"
                    >
                        <img
                            v-if="resultIsChecked(o.value)"
                            src="https://static-common-cdn.abcyun.cn/img/print-mdb-check.png"
                            alt=""
                        />
                        <img
                            v-else
                            src="https://static-common-cdn.abcyun.cn/img/print-mdb-not-check.png"
                            alt=""
                        />
                    </div>

                    <div class="mdb-diagnosis-item-label">
                        {{ o.label }}
                    </div>
                </div>
            </div>
        </div>

        <div class="mdb-title">
            医生建议
        </div>

        <div
            class="mdb-suggestion-section-wrapper"
            v-html="getItemByValue('diagnosis').value"
        >
        </div>
    </div>
</template>

<script>
    import { MDB_DIAGNOSIS_OPTION, MDB_RESULT_FILTER_KEY } from "../../constant/print-constant";
    // import MdbImage from "./mdb-image.vue";

    export default {
        name: "MdbReport",

        components: {
            // MdbImage
        },

        props: {
            printData: {
                type: Object,
                default: () => ({})
            }
        },

        data() {
            return {
                MDB_DIAGNOSIS_OPTION,
            };
        },

        computed: {
            itemsValue() {
                return this.printData?.itemsValue ?? [];
            },

            resultOptions() {
                return this.itemsValue
                    .filter((o) => !MDB_RESULT_FILTER_KEY.includes(o.enName)) // 过滤掉除开result的字段
            },
        },

        methods: {
            getItemByValue(val) {
                return this.itemsValue.find((o) => o.name === val) || {};
            },

            resultIsChecked(val) {
                const res = this.getItemByValue('checkResultStr').value;

                return val === res;
            }
        },
    }
</script>

<style lang="scss" scoped>
.mdb-report-wrapper {
  padding: 12px 0;
}

.mdb-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.mdb-result-section-wrapper {
  display: flex;
  padding-bottom: 12px;
}

.mdb-result-item-wrapper {
  width: 280px;
  display: flex;
  flex-wrap: wrap;
  align-self: flex-start;

  .mdb-result-item {
    display: flex;
    align-items: center;
    width: 140px;
    height: 32px;
    box-sizing: border-box;

    &:nth-child(odd) {
      padding-right: 12px;
    }

    &:nth-child(even) {
      padding-left: 12px;
    }

    .mdb-result-item-title {
      width: 70px;
      color: #7A8794;
    }

    .mdb-result-item-value {
      flex: 1;
      text-align: right;
    }
  }
}

.mdb-result-image-wrapper {
  width: 300px;
  height: 200px;
  margin-left: auto;

  > img {
    height: 100%;
    width: 100%;
  }
}

.mdb-diagnosis-section-wrapper {
  display: flex;
  padding-bottom: 16px;

  .mdb-diagnosis-item + .mdb-diagnosis-item {
    margin-left: 40px;
  }

  .mdb-diagnosis-item {
    display: flex;

    .mdb-diagnosis-item-avatar {
      width: 80px;
      height: 80px;
      margin-right: 4px;
      border-radius: 4px;
    }

    .mdb-diagnosis-item-content {
      width: 36px;
      height: 80px;
      border: 1px solid #E6EAEE;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .mdb-diagnosis-item-radio {
        width: 16px;
        height: 16px;

        img {
          height: 100%;
          width: 100%;
        }
      }

      .mdb-diagnosis-item-label {
        font-size: 14px;
        width: 20px;
        margin-top: 3px;
        line-height: 24px;
        color: #7A8794;
        text-align: center;
        white-space: normal;
      }
    }
  }
}

.mdb-suggestion-section-wrapper {
  white-space: pre-line;
}
</style>