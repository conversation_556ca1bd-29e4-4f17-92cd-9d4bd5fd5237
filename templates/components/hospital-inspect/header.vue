<template>
    <div class="hospital-exam-header-wrapper">
        <div class="clinic">
            <!--呼和浩特、包头定制，互认标识-->
            <div
                class="baotou-identification"
                :style="{
                    width: headerConfig.isCustomLogo ? '120pt' : '90pt'
                }"
            >
                {{ printData.isMutualRecognition ? '呼包HR 包头市HR' : '' }}
            </div>

            <!--包头市定制 logo-->
            <div
                v-if="headerConfig.isCustomLogo"
                class="custom-logo"
                :style="{
                    paddingTop: printData.isMutualRecognition ? '22px' : 0
                }"
            >
                <img
                    src="https://static-common-cdn.abcyun.cn/img/print-baotou-report.png"
                    alt=""
                />
            </div>

            <!--logo-->
            <div
                v-else-if="headerConfig.logo && organPrintView && organPrintView.logo"
                class="logo"
                :style="{
                    paddingTop: printData.isMutualRecognition ? '22px' : 0
                }"
            >
                <img
                    :src="organPrintView.logo"
                    alt=""
                />
            </div>

            <!--机构名称-->
            <div class="examination-title-wrapper">
                <print-row
                    class="clinic-name"
                    :class="{ 'clinic-sub-title-name': curOrganSubtitle }"
                    :style="{ color: curOrganTitleColor }"
                >
                    {{ curOrganTitle }}
                </print-row>
                <print-row
                    v-if="curOrganSubtitle"
                    class="clinic-name"
                    :class="{ 'clinic-sub-title-name': curOrganSubtitle }"
                    :style="{ color: curOrganTitleColor }"
                >
                    {{ curOrganSubtitle }}
                </print-row>
            </div>

            <!--报告名称-->
            <div class="product-name">
                <span>{{
                    headerConfig.assistantTitleUseProductName
                        ? `${printData.name}报告单`
                        : headerConfig.assistantTitle
                }}</span>
            </div>

            <!--条形码-->
            <div
                v-if="headerConfig.barcode && barcode"
                class="barcode-wrapper"
            >
                <abc-print-barcode
                    :value="barcode"
                    show-code
                ></abc-print-barcode>
            </div>
        </div>

        <abc-print-row style="justify-content: space-between;padding: 16px 0 8px">
            <abc-print-col
                :span="layoutSpanMap[0]"
            >
                姓名：<span style="white-space: pre">{{ patientInfo }}</span>
            </abc-print-col>

            <abc-print-col :span="layoutSpanMap[1]">
                <span v-if="headerConfig.deviceModelDesc && deviceModelDesc">
                    镜型：{{ deviceModelDesc }}
                </span>
            </abc-print-col>

            <abc-print-col :span="layoutSpanMap[2]">
                <span v-if="deviceView && deviceView.deviceUuid && headerConfig.inspectionDevice">
                    设备：{{ deviceView.deviceUuid }}
                </span>
            </abc-print-col>
        </abc-print-row>

        <abc-print-row
            class="exam-patient-info"
            :gap-y="headerGapY"
        >
            <abc-print-col
                v-for="(item, i) in headerItems"
                :key="i"
                :span="item.span"
                overflow
            >
                <span
                    class="label"
                    :style="item.labelStyle"
                >
                    {{ item.label }}：
                </span>
                <span class="value">{{ item.value }}</span>
            </abc-print-col>
        </abc-print-row>
    </div>
</template>

<script>
    import PrintRow from '../layout/print-row.vue'
    import { formatAge, getLengthWithFullCharacter, parseTime } from '../../common/utils.js'
    import { INSPECT_DEVICE_TYPE, INSPECT_DEVICE_TYPE_TEXT } from '../../constant/print-constant.js'
    import { TITLE_MAX_LENGTH } from '../../common/constants'
    import AbcPrintBarcode from '../layout/abc-print-barcode.vue'
    import AbcPrintRow from '../layout/abc-layout/abc-row.vue'
    import AbcPrintCol from '../layout/abc-layout/abc-col.vue'

    export default {
        name: 'OutpatientHeader',
        components: {
            AbcPrintCol,
            AbcPrintRow,
            AbcPrintBarcode,
            PrintRow,
        },
        props: {
            printData: {
                type: Object,
                default: () => ({}),
            },
            headerItems: {
                type: Array,
                default: () => [],
            },
            config: {
                type: Object,
                default: () => ({}),
            },
            organTitle: {
                type: String,
                default: '',
            },
            deviceModelDesc: {
                type: String,
                default: '',
            },
            layoutSpanMap: {
                type: Array,
                default: () => [10, 7, 7],
            },
        },
        data() {
            return {
                INSPECT_DEVICE_TYPE_TEXT,
                curOrganTitle: '',
                curOrganSubtitle: '',
            }
        },
        computed: {
            headerConfig() {
                const originConfig = this.config.header || {}
                // 自定义logo的门店列表
                const customLogoClinicIdList = [
                    'ffffffff0000000034a7aff0c70f8002', // 包头稀土高新区稀土路街道办事处社区卫生服务中心
                ]
                // 自定义logo的报告类型
                const customLogoTypeList = [
                    INSPECT_DEVICE_TYPE.CT,
                    INSPECT_DEVICE_TYPE.DR,
                    INSPECT_DEVICE_TYPE['彩超'],
                ]
                return {
                    ...originConfig,
                    isCustomLogo:
                        customLogoClinicIdList.includes(this.printData.organPrintView?.id) &&
                        customLogoTypeList.includes(+this.printData.deviceType),
                }
            },
            contentConfig() {
                return this.config.content || {}
            },
            patientInfo() {
                const patient = this.printData.patient || {}
                const {
                    patientName,
                    patientSex,
                    patientAge,
                } = this.contentConfig;
                return [
                    patientName ? patient.name : '',
                    patientSex ? patient.sex : '',
                    patientAge ? formatAge(patient.age, {
                        monthYear: 12,
                        dayYear: 1,
                    }) : '',
                ].join('   ');
            },
            patient() {
                return this.printData.patient || {};
            },
            organPrintView() {
                return this.printData.organPrintView
            },
            deviceView() {
                return this.printData.deviceView
            },
            barcode() {
                return this.printData.orderNo || ''
            },

            titleAndSubtitle() {
                return {
                    organTitle: this.organTitle,
                    headerConfigTitle: this.headerConfig.title,
                    clinicPrintName: this.printData.clinicPrintName,
                    headerConfigSubtitle: this.headerConfig.subtitle,
                }
            },
            curOrganTitleColor() {
                const color = this.headerConfig.titleColor
                const colorEnum = Object.freeze({
                    0: 'black',
                    1: 'blue',
                    2: 'red',
                })
                return colorEnum[color]
            },

            // 是否为送至中心门店检验的报告
            isSendToCenterOrgan() {
                return this.printData.coFlag === 1
            },

            cooperationCenterOrganName() {
                return this.printData.coClinicName
            },

            fontSizeMode() {
                const fontSizeMap = ['small', 'medium', 'large'];
                return fontSizeMap[this.config?.style?.fontSize || 0];
            },

            headerGapY() {
                if(this.fontSizeMode === 'small') {
                    return 6;
                }

                return 8;
            },
        },
        watch: {
            titleAndSubtitle: {
                handler(v) {
                    const { organTitle, headerConfigTitle, clinicPrintName, headerConfigSubtitle } =
                        v
                    let title = '',
                        subtitle = ''
                    title = this.isSendToCenterOrgan
                        ? this.cooperationCenterOrganName
                        : organTitle || headerConfigTitle || clinicPrintName || ''

                    const cacheTitle = title
                    const {
                        fullCharacterLength: fullClinicCharacterLength,
                        splitLength: splitClinicLength,
                    } = getLengthWithFullCharacter(cacheTitle, TITLE_MAX_LENGTH)
                    if (fullClinicCharacterLength > TITLE_MAX_LENGTH) {
                        title = cacheTitle.slice(0, splitClinicLength)
                        subtitle = cacheTitle.slice(splitClinicLength)
                    }

                    this.curOrganTitle = title
                    if (subtitle) {
                        this.curOrganSubtitle = subtitle
                    } else {
                        this.curOrganSubtitle = headerConfigSubtitle || ''
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            formatAge,
            parseTime,
        },
    }
</script>

<style lang="scss">
    @import '../layout/_print-layout';

    .hospital-exam-header-wrapper {
        width: 100%;

        &.font-size-medium {
            .clinic-name {
                font-size: 24px;
                line-height: 28px;
            }

            .product-name {
                font-size: 20px;
            }
        }

        &.font-size-large {
            .clinic-name {
                font-size: 28px;
                line-height: 28px;
            }

            .product-name {
                font-size: 22px;
            }
        }

        .clinic {
            position: relative;
            width: 100%;

            .barcode-wrapper {
                position: absolute;
                top: 0;
                right: -6pt;
                width: 90pt;
            }
        }

        .baotou-identification {
            position: absolute;
            top: 0;
            left: -6pt;
            width: 90pt;
            font-size: 12px;
            color: #0090ff;
            text-align: center;
        }

        .examination-title-wrapper {
            width: 100%;
            font-family: SimSun;
        }

        .clinic-name {
            padding-left: 0 !important;
            font-size: 21px;
            font-weight: 700;
            line-height: 28px;
            text-align: center;

            &.clinic-sub-title-name {
                line-height: 20pt;
            }
        }

        .product-name {
            margin-top: 12px;
            font-size: 18px;
            font-weight: 400;
            text-align: center;
            font-family: "Source Han Serif CN";
        }

        .custom-logo {
            position: absolute;
            top: -6pt;
            left: -12pt;
            width: 120pt;

            img {
                width: 100%;
                height: auto;
            }
        }

        .logo {
            position: absolute;
            top: 0;
            left: -6pt;
            width: 90pt;
            height: 40pt;

            img {
                width: 100%;
                height: 100%;
                vertical-align: middle;
                object-fit: contain;
            }
        }

        .device-info {
            min-height: 12pt;
            text-align: right;

            .device-info-desc {
                margin-left: 8pt;
            }
        }

        .exam-patient-info {
            padding: 8px 0;
            border-top: 1px solid #000000;
            border-bottom: 1px dashed #000000;
        }
    }
</style>
