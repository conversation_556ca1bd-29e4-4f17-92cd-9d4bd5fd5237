<template>
    <div class="prescription-footer-shanghai">
        <print-row>
            <print-col
                :span="8"
            >
                <span>审核：</span>
                <span
                    v-if="isImgUrl(auditHandSign)"
                    class="sign-img-hetu under-box "
                >
                    <img :src="auditHandSign" />
                </span>
                <span
                    v-else-if="auditHandSign"
                    class="sign-img-hetu under-box"
                >
                    {{ auditHandSign }}
                </span>
                <span
                    v-else
                    class="under-box"
                >
                    {{ auditName }}
                </span>
            </print-col>
            <print-col
                :span="8"
            >
                <span>调配：</span>
                <span
                    v-if="isImgUrl(compoundByHandSign)"
                    class="sign-img-hetu under-box "
                >
                    <img :src="compoundByHandSign" />
                </span>
                <span
                    v-else-if="compoundByHandSign"
                    class="sign-img-hetu under-box "
                >
                    {{ compoundByHandSign }}
                </span>
                <span
                    v-else
                    class="under-box"
                >
                    {{ compoundName }}
                </span>
            </print-col>
            <print-col
                :overflow="!printData.doctorSignImgUrl"
                :class="{'no-wrap': !printData.doctorSignImgUrl}"
                :span="8"
                style="text-align: right;"
            >
                <span>
                    医师：
                </span>
                <span
                    class="under-box"
                    style="text-align: left;"
                >
                    <span
                        v-if="isImgUrl(printData.doctorSignImgUrl)"
                        class="sign-img-hetu"
                    >
                        <img :src="printData.doctorSignImgUrl" />
                    </span>
                    <span v-else-if="printData.doctorSignImgUrl">
                        {{ printData.doctorSignImgUrl }}
                    </span>
                    <span v-else>
                        {{ printData.doctorName }}
                    </span>
                </span>
            </print-col>
        </print-row>

        <print-row>
            <print-col
                :span="12"
            >
                <span>核对、发药：</span>
                <span
                    v-if="isImgUrl(dispensedByHandSign)"
                    class="sign-img-hetu under-box under-box-normal"
                >
                    <img :src="dispensedByHandSign" />
                </span>
                <span
                    v-else-if="dispensedByHandSign"
                    class="under-box under-box-normal"
                >
                    {{ dispensedByHandSign }}
                </span>
                <span
                    v-else
                    class="under-box under-box-normal"
                >
                    {{ dispensedByName }}
                </span>
            </print-col>
            <print-col
                :span="12"
                style="text-align: right;"
            >
                <span>药品金额（元）：</span>
                <span
                    class="under-box"
                    style="min-width: 50pt;text-align: left;"
                >
                    <template v-if="totalPrice">
                        {{ totalPrice | formatMoney }}
                    </template>
                </span>
            </print-col>
        </print-row>
    </div>
</template>

<script>
    import {formatMoney, formatAddress, parseTime, isImgUrl} from '../../common/utils.js';
    import PrintRow from '../layout/print-row.vue'
    import PrintCol from '../layout/print-col.vue'

    const PrintTypes = {
        TREATMENT_EXECUTE: 'treatmentExecute', // 治疗执行单
        INFUSION_EXECUTE: 'infusionExecute', // 输注执行单
        PRESCRIPTION: 'prescription', // 处方笺
        EXAMINATION: 'examination', // 检查检验单
        MEDICAL: 'medical', // 病历
        MEDICAL_CERTIFICATE: 'medicalCertificate', // 病情证明书
        CHILD_TEST_RESULT: 'childTestResult', // 儿童测试报告
        CHILD_HEALTH_REPORT: 'childHealthReport', // 儿童健康报告
        CHILD_QUESTION_TABLE: 'childQuestionTable', // 儿童问卷
        CHRONIC_CARE: 'chronicCare', // 慢病管理
        A5_DISPENSING: 'A5Dispensing', // A5发药单打印
        STAT: 'statPrint', // 统计打印
        FAMILY_DOCTOR_AGREEMENT: 'familyDoctorAgreementPrint', // 家庭医生打印
    }
    export default {
        name: 'OutpatientFooter',
        components: {
            PrintCol,
            PrintRow,
        },
        filters: {
            parseTime,
            formatMoney,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            printType: {
                type: String,
                default: '',
            },
            prForm: {
                type: Object,
                default: () => {
                    return null;
                }
            },
            showTotalPrice: Boolean,
            printTime: {
                type: String,
                default: '',
            },
            isChinese: Boolean,
            isExternal: {
                type: Boolean,
                default: false,
            },
            dispensingInfo: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            PrintTypes,
            footerConfig() {
                return this.config.footer || {};
            },
            address() {
                return this.printData.patient.address;
            },
            patient() {
                return this.printData.patient;
            },
            addressDetail() {
                return formatAddress(this.address);
            },
            totalPrice() {
                if (this.footerConfig.amount === 2) {
                    const {displayTotalPrice} = this.printData;
                    return displayTotalPrice;
                }
                // 药品金额
                if (this.footerConfig.amount === 1) {
                    const {
                        displayTotalPrice,
                        medicinePriceInfo
                    } = this.prForm || {};
                    const {
                        displayTotalPrice: medicineDisplayTotalPrice,
                    } = medicinePriceInfo || {};
                    if (this.isChinese) {
                        // 中药处方包含了加工费、快递费，需要用medicine-前缀
                        return medicineDisplayTotalPrice;
                    } else {
                        return displayTotalPrice;
                    }
                }
                return '';
            },
            chargedByName() {
                return this.prForm && this.prForm.chargedByName || null
            },
            chargedByHandSign() {
                return this.prForm && this.prForm.chargedByHandSign || null
            },
            remark() {
                if (!this.footerConfig.remark) {
                    return ''
                }
                return this.footerConfig.remark.split('\n').filter(it => !!it).map(string => {
                    return string.split('')
                        .map(charactor => `<div style="height: 8pt;display: inline-block;max-width: 8pt;"><span style="font-size: 10pt; zoom: 0.8;">${charactor}</span></div>`)
                        .join('')
                }).join('<br/>')
            },
            prescriptionDispensingInfo() {
                return this.dispensingInfo || {};
            },
            auditHandSign() {
                return this.isExternal ? this.printData.auditHandSign : this.prescriptionDispensingInfo.auditHandSign;
            },
            auditName() {
                return this.isExternal ? this.printData.auditName : this.prescriptionDispensingInfo.auditName;
            },
            dispensedByHandSign() {
                return this.isExternal ? this.printData.dispensedByHandSign : this.prescriptionDispensingInfo.dispensedByHandSign;
            },
            dispensedByName() {
                return this.isExternal ? this.printData.dispensedByName : this.prescriptionDispensingInfo.dispensedByName;
            },

            compoundByHandSign() {
                return this.isExternal ? this.printData.compoundByHandSign : this.prescriptionDispensingInfo.compoundByHandSign;
            },
            compoundName() {
                return this.isExternal ? this.printData.compoundName : this.prescriptionDispensingInfo.compoundName;
            },
        },
        methods: {
            formatAddress,
            isImgUrl
        }
    };
</script>

<style lang="scss">
[data-type~=footer] {
    padding-top: 0!important;
}

.prescription-footer-shanghai {
  font-size: 13px;
  line-height: 19px;

  .sign-img-hetu {
    bottom: 0;
    width: auto;
    height: 16pt;
    outline: none;

    img {
      width: 44pt;
      height: 19.5px;
      border: 0;
      border-color: #ffffff;
      float: left;
    }
  }


  .under-box {
    display: inline-block;
    border-bottom: 1px solid #000;
    min-width: 50pt;
    &:after {
      content: '\200B';
    }
  }

}
</style>
