<template>
    <div :class="['print-prescription-circulation-footer', { 'small-width': printData.readerType === 'create' }]">
        <!-- 机构公章 吉林 && 有机构公章 -->
        <template v-if="region.isJilin && printData.institutionalSealBase64">
            <div class="institutional-box">
                <img
                    :src="printData.institutionalSealBase64"
                    class="institutional-seal"
                />
            </div>
        </template>
        <div class="print-prescription-circulation-content">
            <template v-if="region.isMountainwest">
                <print-row class="sign-row-mountainwest">
                    <!-- 第一行 -->
                    <print-col :span="8" class="item">
                        <span class="light-font">医师：</span>
                        <div class="down-line">
                            <template v-if="printData.doctorSignBase64">
                                <span class="sign-img">
                                    <img :src="printData.doctorSignBase64" />
                                </span>
                            </template>
                            <template v-else>
                                <span class="text">{{ printData.doctorName }}</span>
                            </template>
                        </div>
                    </print-col>
                    <print-col :span="8" class="item">
                        <span class="light-font">初审药师:</span>
                        <div class="down-line"></div>
                    </print-col>
                    <print-col :span="8" class="item">
                        <span class="light-font">药品金额:</span>
                        <div class="down-line"></div>
                    </print-col>
                </print-row>
                <print-row class="sign-row-mountainwest line-bottom">
                    <print-col :span="8" class="item">
                        <span class="light-font">审核药师：</span>
                        <div class="down-line">
                            <template v-if="printData.pharmacistSignImgUrl">
                                <span class="sign-img">
                                    <img :src="printData.pharmacistSignImgUrl" />
                                </span>
                            </template>
                            <template v-else>
                                <span class="text">{{ printData.pharName }}</span>
                            </template>
                        </div>
                    </print-col>
                    <print-col :span="8" class="item">
                        <span class="light-font">调剂:</span>
                        <div class="down-line"></div>
                    </print-col>
                    <print-col :span="8" class="item">
                        <span class="light-font">复核/发药:</span>
                        <div class="down-line"></div>
                    </print-col>
                </print-row>
            </template>
            <template v-else>
                <print-row class="sign-row">
                    <print-col :span="12">
                        <span class="light-font">开方医师：</span>
                        <template v-if="printData.doctorSignBase64">
                            <span class="sign-img">
                                <img :src="printData.doctorSignBase64" />
                            </span>
                            <template v-if="printData.isNeedShowDrCode && printData.drCode">
                                <span class="code-style">（{{ printData.drCode }}）</span>
                            </template>
                        </template>
                        <template v-else>
                            <span class="text">
                                {{ printData.doctorName }}
                                <template v-if="printData.isNeedShowDrCode && printData.drCode">
                                    （{{ printData.drCode }}）
                                </template>
                            </span>
                        </template>
                    </print-col>
                    <print-col :span="12">
                        <span class="light-font">审核药师：</span>
                        <template v-if="printData.pharmacistSignBase64">
                            <div class="sign-img">
                                <img :src="printData.pharmacistSignBase64" />
                            </div>
                        </template>
                        <template v-else>
                            <span class="text">{{ printData.pharName }}</span>
                        </template>
                    </print-col>
                    <print-col
                        :span="12"
                        class="item"
                        overflow
                    >
                        <span class="light-font">调配、复核药师:</span>
                    </print-col>
                    <print-col
                        :span="12"
                        class="item"
                        overflow
                    >
                        <span class="light-font">核对、发药药师:</span>
                        <span class="text">
                            {{ printData.dispensedByName }}
                        </span>
                    </print-col>
                </print-row>
            </template>
            <!-- 吉林 -->
            <template v-if="region.isJilin">
                <print-row class="extend-row">
                    <print-col class="extends-tips" overflow>
                        延长处方用量原因：
                    </print-col>
                    <print-col class="extend-line" overflow>
                        {{ printData.extendsReason }}
                    </print-col>
                </print-row>
            </template>
            <!-- 山西 -->
            <template v-else-if="region.isMountainwest">
                <print-row class="extend-row">
                    <print-col :span="24" class="item">
                        注意： 处方有效期为3天，擅自下载打印无效。
                    </print-col>
                </print-row>
            </template>
            <template v-else>
                <print-row class="tips-row">
                    <print-col
                        :span="24"
                        class="item"
                        overflow
                    >
                        为了保证患者用药安全、有效、请注意
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        1. 认真核对药名和数量等处方信息
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        2. 严禁将本处方药品予他人使用
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        3. 药品一经发出，不得退换
                    </print-col>
                </print-row>
            </template>
        </div>
    </div>
</template>

<script>
    import PrintRow from '../layout/print-row.vue'
    import PrintCol from '../layout/print-col.vue'

    export default {
        name: 'PrescriptionCirculationFooter',
        components: {
            PrintCol,
            PrintRow,
        },
        props: {
            printData: {
                type: Object,
                default: () => ({})
            }
        },
        computed: {
            // 地区
            region() {
                const region = this.printData.region
                const isJilin = region?.startsWith('jilin')
                const isMountainwest = region?.startsWith('mountainwest')
                return {
                    isJilin,
                    isMountainwest
                }
            }
        }
    }
</script>

<style lang="scss">

.print-prescription-circulation-footer {
    width: calc(100% - 16pt);

    .institutional-box {
        position: relative;
        width: 100%;
        height: 90pt;

        .institutional-seal {
            position: absolute;
            bottom: 10pt;
            right: 0;
            width: 127pt;
            height: 85pt;
        }
    }
}

.small-width {
    width: calc(100% - 8pt);
}
.print-prescription-circulation-content {
    padding-top: 5pt;
    border-top: 1px solid #000000;

    .light-font {
        font-size: 8pt;
        line-height: 12pt;
    }

    .print-col {
        position: relative;
    }

    .sign-img {
        position: absolute;
        top: -4pt;
        bottom: 0;
        left: 30pt;
        width: 61.5pt;
        height: 24pt;
        outline: none;

        img {
            margin-left: 13pt;
            width: 61.5pt;
            height: 24pt;
            border: 0;
            border-color: #ffffff;
        }
    }

    .code-style {
        font-size: 8pt;
        margin-left: 61.5pt;
    }

    .text {
        font-size: 8pt;
        line-height: 12pt;
    }


    .sign-row-mountainwest {
        padding-bottom: 2pt;
        height: 30px;

        .item {
            margin-top: 5pt;
            box-sizing: border-box;
            padding: 0 5pt;
            display: flex;
        }

        .line-bottom {
            margin-top: 8pt;
        }

        .down-line {
            display: flex;
            flex: 1;
            margin-left: 5px;
            border-bottom: 1px solid #000;
        }
    }

    .sign-row {
        padding-bottom: 4pt;
        height: 50px;
    }

    .extend-row {
        display: flex;
        padding: 10pt 0;
        border-top: 1px solid #000;
        font-size: 8pt;

        .extends-tips {
            width: 100px;
        }

        .extend-line {
            flex: 1;
            border-bottom: 1px solid #000;
        }
    }

    .tips-row {
        padding-top: 8pt;
        font-size: 7pt;
        line-height: 16pt;
        border-top: 1px solid #000;
    }
}

</style>
