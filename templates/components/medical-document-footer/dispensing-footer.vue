<template>
    <div
        class="print-medical-document-footer-wrapper"
        style="padding-bottom: 6pt"
    >
        <div class="print-medical-document-footer">
            <print-row class="sign-row">
                <print-col
                    v-if="ticketFooter.amount"
                    :span="5"
                    class="item"
                    overflow
                >
                    <span class="light-font">{{ isUndispense ? '已退费' : '金额' }}:</span>
                    <span class="text">
                        {{ money | formatMoney }}
                    </span>
                </print-col>
                <print-col
                    v-if="ticketFooter.chargedByName"
                    :span="6"
                    class="item"
                    overflow
                >
                    <span class="light-font">收费员:</span>
                    <span class="text">
                        {{ printData.chargedByName }}
                    </span>
                </print-col>
                <print-col
                    v-if="ticketFooter.check"
                    :span="5"
                    overflow
                    class="item"
                >
                    <span class="light-font">审核:</span>
                    <span class="text">
                        {{ printData.auditName }}
                    </span>
                </print-col>
                <print-col
                    v-if="ticketFooter.assigner"
                    :span="4"
                    class="item"
                    overflow
                >
                    <span class="light-font">调配:</span>
                    <span class="text">
                        {{ printData.compoundName }}
                    </span>
                </print-col>
                <print-col
                    v-if="ticketFooter.dispense"
                    :span="4"
                    class="item"
                    overflow
                >
                    <span class="light-font">核发:</span>
                    <span class="text">
                        {{ printData.dispensedByName || '' }}
                    </span>
                </print-col>
            </print-row>
        </div>
        <div class="tips-wrapper">
            <div
                class="left row-1"
                style="width: 55%"
                overflow
            >
                <div v-if="ticketFooter.replacementReminder && !isUndispense">
                    药品离柜 概不退换
                </div>
                {{ config.remark }}
            </div>
            <div
                v-if="organ.contactPhone && ticketFooter.mobile"
                class="right row-1"
                style="width: 45%; text-align: right"
                overflow
            >
                电话：{{ organ.contactPhone }}
            </div>
            <div
                v-if="organ.addressDetail && ticketFooter.address"
                class="right row-2"
                style="width: 45%;text-align: right"
                overflow
            >
                地址：{{ organ.addressDetail }}
            </div>
        </div>
    </div>
</template>

<script>
    import PrintRow from "../layout/print-row.vue";
    import PrintCol from "../layout/print-col.vue";
    import { formatMoney } from "../../common/utils.js";
    export default {
        name: 'DispensingFooter',
        components: {
            PrintCol,
            PrintRow,
        },
        filters: {
            formatMoney,
        },
        props: {
            printData: {
                type: Object,
            },
            config: {
                type: Object,
            },
            dispensingForm: {
                type: [Object, Array],
            },
            isUndispense: {
                default: false,
                type: Boolean,
            },
        },
        computed: {
            organ() {
                return this.printData.organ;
            },
            ticketFooter() {
                return (this.config && this.config.ticketFooter) || {};
            },
            money() {
                if (this.ticketFooter.amount === 1 || this.isUndispense) {
                    let dispensingMoney = 0
                    if (Array.isArray(this.dispensingForm)) {
                        this.dispensingForm.forEach((item)=>{
                            if (item.receivedPrice) {
                                dispensingMoney += item.receivedPrice
                            }
                        })
                        return dispensingMoney
                    } else {
                        return this.dispensingForm.receivedPrice || 0
                    }
                } else {
                    return this.printData.netIncomeFee
                }

            }
        },
    };
</script>
<style lang="scss">
@import "./index.scss";
</style>
