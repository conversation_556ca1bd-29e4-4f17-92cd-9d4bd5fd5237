<template>
    <div class="print-medical-document-footer-wrapper foot-box">
        <print-row>
            <print-col
                :overflow="!printData.doctorSignImgUrl"
                :class="{'no-wrap': !printData.doctorSignImgUrl}"
                :span="24"
                style="text-align: right;margin-bottom: 2pt;"
            >
                    <span>
                        医师：
                    </span>
                <span class="under-box under-box-normal">
                         <span v-if="isImgUrl(printData.doctorSignImgUrl)" class="sign-img-hetu">
                            <img :src="printData.doctorSignImgUrl"/>
                        </span>
                        <span v-else-if="printData.doctorSignImgUrl">
                            {{ printData.doctorSignImgUrl }}
                        </span>
                        <span v-else>
                            {{ printData.doctorName }}
                        </span>
                    </span>
            </print-col>
        </print-row>

        <div class="print-medical-document-footer">
            <print-row>
                <print-col
                    :span="12"
                >
                    <span>药品金额 {{ $t('currencySymbol') }}：</span>
                    <span
                        class="under-box"
                        style="min-width: 50pt;"
                    >
                        <template v-if="totalPrice">
                            {{ totalPrice | formatMoney }}
                        </template>
                    </span>
                    <span>元</span>
                </print-col>

                <print-col
                    :span="12"
                    style="text-align: right;"
                >
                    <span>收费员：</span>
                    <span
                        v-if="isImgUrl(chargedByHandSign)"
                        class="sign-img-hetu under-box under-box-normal"
                    >
                        <img :src="chargedByHandSign"/>
                    </span>
                    <span
                        v-else-if="chargedByHandSign"
                        class="under-box under-box-normal"
                    >
                            {{ chargedByHandSign }}
                        </span>
                    <span
                        v-else
                        class="under-box under-box-normal"
                    >
                        {{ chargedByName }}
                    </span>
                </print-col>
            </print-row>

            <print-row>
                <print-col
                    :span="12"
                >
                    注&nbsp; 射 费 {{ $t('currencySymbol') }}：
                    <span
                        class="under-box"
                        style="min-width: 50pt;"
                    >
                    </span>
                    元
                </print-col>

                <print-col
                    :span="12"
                    style="text-align: right;"
                >
                    <span>审核、调配：</span>
                    <span
                        v-if="isImgUrl(auditHandSign)"
                        class="sign-img-hetu under-box under-box-normal"
                    >
                        <img :src="auditHandSign"/>
                    </span>
                    <span
                        v-else-if="auditHandSign"
                        class="sign-img-hetu under-box under-box-normal"
                    >
                        {{ auditHandSign }}
                    </span>
                    <span
                        v-else
                        class="under-box under-box-normal"
                    >
                        {{ auditName }}
                    </span>
                </print-col>
            </print-row>

            <print-row>
                <print-col :span="12">
                    <span>(收款票据请贴附在处方背面)</span>
                </print-col>

                <print-col
                    :span="12"
                    style="text-align: right;"
                >
                    <span>核对、发药：</span>
                    <span v-if="isImgUrl(dispensedByHandSign)"
                          class="sign-img-hetu under-box under-box-normal"
                    >
                        <img :src="dispensedByHandSign"/>
                    </span>
                    <span
                        v-else-if="dispensedByHandSign"
                        class="under-box under-box-normal"
                    >
                        {{ dispensedByHandSign }}
                    </span>
                    <span
                        v-else
                        class="under-box under-box-normal"
                    >
                        {{ dispensedByName }}
                    </span>
                </print-col>
            </print-row>
            <print-row>
                <print-col
                    :span="16"
                    overflow
                >
                    大额处方患者意见:
                    <div
                        class="under-box"
                        style="min-width: 70pt;"
                    >
                        <span style="margin-left: 6pt">同意</span>
                        <span style="margin-left: 5pt">不同意</span>
                    </div>
                </print-col>
                <print-col
                    :span="8"
                    overflow
                    style="text-align: right;"
                >
                    患者签名：
                    <div
                        class="under-box"
                        style="min-width: 50pt;text-align: left;"
                    >
                    </div>
                </print-col>
            </print-row>
            <print-row>
                <print-col
                    :span="24"
                    overflow
                >
                    <row-box :label-width="196">
                        <span slot="label"> 联系地址或电话(患者自愿填写)：</span>
                        <span v-if="patient.mobile" slot="content">{{ patient.mobile }}</span>
                    </row-box>
                </print-col>
            </print-row>
        </div>
    </div>
</template>

<script>
import {formatMoney, formatAddress, parseTime, isImgUrl} from '../../common/utils.js';
import PrintRow from '../layout/print-row.vue'
import PrintCol from '../layout/print-col.vue'
import RowBox from "../medical-document-header/row-box.vue";

const PrintTypes = {
    TREATMENT_EXECUTE: 'treatmentExecute', // 治疗执行单
    INFUSION_EXECUTE: 'infusionExecute', // 输注执行单
    PRESCRIPTION: 'prescription', // 处方笺
    EXAMINATION: 'examination', // 检查检验单
    MEDICAL: 'medical', // 病历
    MEDICAL_CERTIFICATE: 'medicalCertificate', // 病情证明书
    CHILD_TEST_RESULT: 'childTestResult', // 儿童测试报告
    CHILD_HEALTH_REPORT: 'childHealthReport', // 儿童健康报告
    CHILD_QUESTION_TABLE: 'childQuestionTable', // 儿童问卷
    CHRONIC_CARE: 'chronicCare', // 慢病管理
    A5_DISPENSING: 'A5Dispensing', // A5发药单打印
    STAT: 'statPrint', // 统计打印
    FAMILY_DOCTOR_AGREEMENT: 'familyDoctorAgreementPrint', // 家庭医生打印
}
export default {
    name: 'OutpatientFooter',
    components: {
        PrintCol,
        PrintRow,
        RowBox
    },
    filters: {
        parseTime,
        formatMoney,
    },
    props: {
        printData: {
            type: Object,
            required: true,
        },
        config: {
            type: Object,
            required: true,
        },
        printType: {
            type: String,
            default: '',
        },
        prForm: {
            type: Object,
            default: () => {
                return null;
            }
        },
        showTotalPrice: Boolean,
        printTime: {
            type: String,
            default: '',
        },
        isChinese: Boolean,
        isExternal: {
            type: Boolean,
            default: false,
        },
        dispensingInfo: {
            type: Object,
            default: () => ({}),
        },
    },
    computed: {
        PrintTypes,
        footerConfig() {
            return this.config.footer || {};
        },
        address() {
            return this.printData.patient.address;
        },
        showAmount() {
            return (this.config && this.config.pr && this.config.pr.amount) || 0;
        },
        patient() {
            return this.printData.patient;
        },
        addressDetail() {
            return formatAddress(this.address);
        },
        /**
         * @desc 是否是执行单
         */
        isExecute() {
            return this.printType === PrintTypes.INFUSION_EXECUTE || this.printType === PrintTypes.TREATMENT_EXECUTE;
        },
        isExamination() {
            return this.printType === PrintTypes.EXAMINATION;
        },
        isTreatment() {
            return this.printType === PrintTypes.TREATMENT_EXECUTE;
        },
        isMedical() {
            return this.printType === PrintTypes.MEDICAL;
        },
        isMedicalCert() {
            return this.printType === PrintTypes.MEDICAL_CERTIFICATE;
        },
        totalPrice() {
            if (this.footerConfig.amount === 2) {
                const {displayTotalPrice} = this.printData;
                return displayTotalPrice;
            }
            // 药品金额
            if (this.footerConfig.amount === 1) {
                const {
                    displayTotalPrice,
                    medicinePriceInfo
                } = this.prForm || {};
                const {
                    displayTotalPrice: medicineDisplayTotalPrice,
                } = medicinePriceInfo || {};
                if (this.isChinese) {
                    // 中药处方包含了加工费、快递费，需要用medicine-前缀
                    return medicineDisplayTotalPrice;
                } else {
                    return displayTotalPrice;
                }
            }
            return 0;
        },
        chargedByName() {
            return this.prForm && this.prForm.chargedByName || null
        },
        chargedByHandSign() {
            return this.prForm && this.prForm.chargedByHandSign || null
        },
        remark() {
            if (!this.footerConfig.remark) {
                return ''
            }
            return this.footerConfig.remark.split('\n').filter(it => !!it).map(string => {
                return string.split('')
                    .map(charactor => `<div style="height: 8pt;display: inline-block;max-width: 8pt;"><span style="font-size: 10pt; zoom: 0.8;">${charactor}</span></div>`)
                    .join('')
            }).join('<br/>')
        },
        curPrintTime() {
            return this.printTime || new Date();
        },
        curPrintTimeStr() {
            let str = `打印时间：${parseTime(this.curPrintTime, 'y-m-d h:i:s')}`;
            return str.split('\n').map(string => {
                return string.split('')
                    .map(charactor => {
                        if (charactor !== ' ') {
                            return `<div style="height: 8pt;display: inline-block;max-width: 8pt;"><span style="font-size: 10pt; zoom: 0.8;">${charactor}</span></div>`;
                        }
                        return `&nbsp;`
                    })
                    .join('')
            }).join('<br/>')
        },
        prescriptionDispensingInfo() {
            return this.dispensingInfo || {};
        },
        auditHandSign() {
            return this.isExternal ? this.printData.auditHandSign : this.prescriptionDispensingInfo.auditHandSign;
        },
        auditName() {
            return this.isExternal ? this.printData.auditName : this.prescriptionDispensingInfo.auditName;
        },
        dispensedByHandSign() {
            return this.isExternal ? this.printData.dispensedByHandSign : this.prescriptionDispensingInfo.dispensedByHandSign;
        },
        dispensedByName() {
            return this.isExternal ? this.printData.dispensedByName : this.prescriptionDispensingInfo.dispensedByName;
        },
    },
    methods: {
        formatAddress,
        isImgUrl
    }
};
</script>

<style lang="scss">
[data-type~=footer] {
    padding-top: 0!important;
}

.print-medical-document-footer {
    padding-top: 2pt;
}

.under-box {
    display: inline-block;
    border-bottom: 1px solid #a6a6a6;
    &:after {
        content: '\200B';
    }
}

.under-box-normal {
    min-width: 50pt;
    text-align: left
}

.foot-box {
    font-size: 10pt;
    font-weight: 400;
    padding-top: 0;
    .print-row {
        margin-top: 2px;
    }
}

.right-line {
    text-align: right;
}

.sign-img-hetu {
    bottom: 0;
    width: auto;
    height: 16pt;
    outline: none;

    img {
        width: 44pt;
        height: 19.5px;
        border: 0;
        border-color: #ffffff;
        float: left;
    }
}
</style>
