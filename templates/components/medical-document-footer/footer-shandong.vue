<template>
    <div class="shandong-footer">
        <div class="print-medical-document-footer-wrapper foot-box">
            <div 
                class="print-medical-document-footer" 
                style="padding-left: 20pt;"
            >
                <print-row>
                    <print-col
                        :overflow="!printData.doctorSignImgUrl"
                        :class="{'no-wrap': !printData.doctorSignImgUrl}"
                        :span="8"
                        style="margin-bottom: 2pt;"
                    >
                        医师
                        <span class="under-box under-box-normal">
                            <span 
                                v-if="isImgUrl(printData.doctorSignImgUrl)" 
                                class="sign-img-hetu"
                            >
                                <img :src="printData.doctorSignImgUrl" />
                            </span>
                            <span v-else-if="printData.doctorSignImgUrl">
                                {{ printData.doctorSignImgUrl }}
                            </span>
                            <span v-else>
                                {{ printData.doctorName }}
                            </span>
                        </span>
                    </print-col>
                    <print-col
                        :span="8"
                    >
                        <span>审核</span>
                        <span
                            v-if="isImgUrl(auditHandSign)"
                            class="sign-img-hetu under-box under-box-normal"
                        >
                            <img :src="auditHandSign" />
                        </span>
                        <span
                            v-else-if="auditHandSign"
                            class="sign-img-hetu under-box under-box-normal"
                        >
                            {{ auditHandSign }}
                        </span>
                        <span
                            v-else
                            class="under-box under-box-normal"
                        >
                            {{ auditName }}
                        </span>
                    </print-col>
                    <print-col
                        :span="8"
                    >
                        金额
                        <span
                            class="under-box under-box-normal"
                        >
                            <template v-if="totalPrice">
                                {{ totalPrice | formatMoney }}
                            </template>
                        </span>
                    </print-col>
                </print-row>
                <print-row>
                    <print-col
                        :span="8"
                    >
                        调配
                        <span
                            v-if="isImgUrl(printData.compoundByHandSign)"
                            class="sign-img-hetu under-box under-box-normal"
                        >
                            <img :src="printData.compoundByHandSign" />
                        </span>
                        <span
                            v-else-if="printData.compoundByHandSign"
                            class="under-box under-box-normal"
                        >
                            {{ printData.compoundByHandSign }}
                        </span>
                        <span
                            v-else
                            class="under-box under-box-normal"
                        >
                            {{ printData.compoundName }}
                        </span>
                    </print-col>
                    <print-col
                        :span="8"
                    >
                        核对
                        <span
                            v-if="isImgUrl(dispensedByHandSign)"
                            class="sign-img-hetu under-box under-box-normal"
                        >
                            <img :src="dispensedByHandSign" />
                        </span>
                        <span
                            v-else-if="dispensedByHandSign"
                            class="under-box under-box-normal"
                        >
                            {{ dispensedByHandSign }}
                        </span>
                        <span
                            v-else
                            class="under-box under-box-normal"
                        >
                            {{ dispensedByName }}
                        </span>
                    </print-col>
                    <print-col
                        :span="8"
                    >
                        发药
                        <span 
                            v-if="isImgUrl(dispensedByHandSign)"
                            class="sign-img-hetu under-box under-box-normal"
                        >
                            <img :src="dispensedByHandSign" />
                        </span>
                        <span
                            v-else-if="dispensedByHandSign"
                            class="under-box under-box-normal"
                        >
                            {{ dispensedByHandSign }}
                        </span>
                        <span
                            v-else
                            class="under-box under-box-normal"
                        >
                            {{ dispensedByName }}
                        </span>
                    </print-col>
                </print-row>
            </div>
        </div>
    </div>
</template>

<script>
    import {formatMoney, formatAddress, parseTime, isImgUrl} from '../../common/utils.js';
    import PrintRow from '../layout/print-row.vue'
    import PrintCol from '../layout/print-col.vue'

    export default {
        name: 'ShandongFooter',
        components: {
            PrintCol,
            PrintRow
        },
        filters: {
            parseTime,
            formatMoney,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            prForm: {
                type: Object,
                default: () => {
                    return null;
                }
            },
            isChinese: Boolean,
            isExternal: {
                type: Boolean,
                default: false,
            },
            dispensingInfo: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            footerConfig() {
                return this.config.footer || {};
            },
            address() {
                return this.printData.patient.address;
            },
            patient() {
                return this.printData.patient;
            },
            totalPrice() {
                if (this.footerConfig.amount === 2) {
                    const {displayTotalPrice} = this.printData;
                    return displayTotalPrice;
                }
                // 药品金额
                if (this.footerConfig.amount === 1) {
                    const {
                        displayTotalPrice,
                        medicinePriceInfo
                    } = this.prForm || {};
                    const {
                        displayTotalPrice: medicineDisplayTotalPrice,
                    } = medicinePriceInfo || {};
                    if (this.isChinese) {
                        // 中药处方包含了加工费、快递费，需要用medicine-前缀
                        return medicineDisplayTotalPrice;
                    } else {
                        return displayTotalPrice;
                    }
                }
                return 0;
            },
            prescriptionDispensingInfo() {
                return this.dispensingInfo || {};
            },
            auditHandSign() {
                return this.isExternal ? this.printData.auditHandSign : this.prescriptionDispensingInfo.auditHandSign;
            },
            auditName() {
                return this.isExternal ? this.printData.auditName : this.prescriptionDispensingInfo.auditName;
            },
            dispensedByHandSign() {
                return this.isExternal ? this.printData.dispensedByHandSign : this.prescriptionDispensingInfo.dispensedByHandSign;
            },
            dispensedByName() {
                return this.isExternal ? this.printData.dispensedByName : this.prescriptionDispensingInfo.dispensedByName;
            },
        },
        methods: {
            formatAddress,
            isImgUrl
        }
    };
</script>

<style lang="scss">

.shandong-footer{
    padding: 0 8pt;
    [data-type~=footer] {
        padding-top: 0!important;
    }

    .print-medical-document-footer {
        padding-top: 2pt;
    }

    .under-box {
        display: inline-block;
        border-bottom: 1px solid #a6a6a6;
        &:after {
            content: '\200B';
        }
    }

    .under-box-normal {
        min-width: 75pt;
        text-align: center;
    }

    .foot-box {
        font-size: 10pt;
        font-weight: 400;
        padding-top: 0;
        border-top: 1px solid #000;
        .print-row {
            margin-top: 15px;
        }
    }

    .right-line {
        text-align: right;
    }

    .sign-img-hetu {
        bottom: 0;
        width: auto;
        height: 16pt;
        outline: none;

        img {
            width: 44pt;
            height: 19.5px;
            border: 0;
            border-color: #ffffff;
            float: left;
        }
    }
}
</style>
