<template>
    <div class="print-medical-document-footer-wrapper">
        <div
            class="print-medical-document-footer"
            :class="{ 'no-border-top': !isMedical && !isTreatment && !isExamination && !isMedicalCert }"
        >
            <template v-if="isMedical">
                <print-row class="sign-row">
                    <print-col
                        :overflow="!printData.doctorSignImgUrl"
                        :class="{'no-wrap': !printData.doctorSignImgUrl}"
                        :span="footerConfig.amount ? 8 : 14"
                    >
                        <span class="light-font">医生：</span>
                        <span
                            v-if="isImgUrl(printData.doctorSignImgUrl)"
                            class="sign-img"
                        >
                            <img :src="printData.doctorSignImgUrl" />
                        </span>
                        <span
                            v-else-if="printData.doctorSignImgUrl"
                            class="text"
                        >
                            {{ printData.doctorSignImgUrl }}
                        </span>
                        <span
                            v-else
                            class="text"
                        >
                            {{ printData.doctorName }}
                        </span>
                    </print-col>
                    <print-col
                        v-if="footerConfig.amount"
                        :span="6"
                    >
                        <span class="light-font">金额：</span>
                        <span class="text">
                            {{ totalPrice | formatMoney }}
                        </span>
                    </print-col>
                    <print-col
                        v-if="footerConfig.printDate"
                        :span="10"
                        style="text-align: right;"
                    >
                        <span class="light-font">打印时间：</span>
                        <span
                            class="text"
                        >
                            {{ new Date() | parseTime('y-m-d h:i:s') }}
                        </span>
                    </print-col>
                </print-row>

                <print-row>
                    <print-col
                        v-if="remark"
                        :span="footerConfig.telephone || footerConfig.address ? 12 : 24"
                        class="light-font"
                        v-html="remark"
                    >
                    </print-col>

                    <print-col
                        :span="remark ? 12 : 24"
                    >
                        <print-row>
                            <print-col
                                v-if="footerConfig.telephone"
                                span="24"
                                style="font-size: 8pt; text-align: right;"
                            >
                                <span>电话：</span>
                                <span>
                                    {{ organNumber }}
                                </span>
                            </print-col>

                            <print-col
                                v-if="footerConfig.address"
                                span="24"
                                class="light-font"
                                style="font-size: 8pt; text-align: right;"
                            >
                                <span>地址：</span>
                                <span>
                                    {{ organAddress }}
                                </span>
                            </print-col>
                        </print-row>
                    </print-col>
                </print-row>
            </template>
            <template v-else-if="isTreatment">
                <print-row
                    class="sign-row"
                    style="padding-top: 3pt"
                >
                    <print-col
                        v-if="footerConfig.seller"
                        :span="calcWidth('seller')"
                    >
                        <span class="light-font">开单人：</span>
                        <span
                            v-if="printData.sellerName"
                            class="text"
                        >
                            {{ printData.sellerName }}
                        </span>
                        <span
                            v-else-if="isImgUrl(printData.doctorSignImgUrl)"
                            class="sign-img"
                        >
                            <img
                                :src="printData.doctorSignImgUrl"
                                alt=""
                            />
                        </span>
                        <span
                            v-else-if="printData.doctorSignImgUrl"
                            class="text"
                        >
                            {{ printData.doctorSignImgUrl }}
                        </span>
                        <span
                            v-else
                            class="text"
                        >
                            {{ printData.doctorName }}
                        </span>
                    </print-col>
                    <print-col
                        v-if="footerConfig.doctor"
                        :span="calcWidth('doctor')"
                    >
                        <span class="light-font">医生：</span>
                        <span
                            v-if="isImgUrl(printData.doctorSignImgUrl)"
                            class="sign-img"
                        >
                            <img
                                :src="printData.doctorSignImgUrl"
                                alt=""
                            />
                        </span>
                        <span
                            v-else-if="printData.doctorSignImgUrl"
                            class="text"
                        >
                            {{ printData.doctorSignImgUrl }}
                        </span>
                        <span
                            v-else
                            class="text"
                        >
                            {{ printData.doctorName }}
                        </span>
                    </print-col>
                    <print-col
                        v-if="footerConfig.amount"
                        :span="6"
                    >
                        <span class="light-font">金额：</span>
                        <span class="text">
                            {{ totalPrice | formatMoney }}
                        </span>
                    </print-col>
                    <print-col
                        v-if="footerConfig.chargedByName"
                        :span="9"
                    >
                        <span class="light-font">收费员：</span>
                        <span
                            v-if="isImgUrl(printData.chargedByHandSign)"
                            class="sign-img"
                        >
                            <img :src="printData.chargedByHandSign" />
                        </span>
                        <span
                            v-else-if="printData.chargedByHandSign"
                            class="text"
                        >
                            {{ printData.chargedByHandSign }}
                        </span>
                        <span
                            v-else
                            class="text"
                        >
                            {{ printData.chargedByName }}
                        </span>
                    </print-col>
                    <print-col
                        v-if="footerConfig.printDate"
                        :span="10"
                    >
                        <span class="light-font">打印时间：</span>
                        <span
                            class="text"
                        >
                            {{ new Date() | parseTime('y-m-d h:i:s') }}
                        </span>
                    </print-col>
                </print-row>
                <print-row>
                    <print-col
                        v-if="remark"
                        :span="24"
                        class="light-font"
                        v-html="remark"
                    >
                    </print-col>
                </print-row>
            </template>
            <template v-else-if="isExamination">
                <print-row class="sign-row">
                    <print-col :span="footerConfig.amount ? 8 : 14">
                        <span class="light-font">开单人：</span>
                        <span
                            v-if="isImgUrl(printData.doctorSignImgUrl)"
                            class="sign-img"
                        >
                            <img :src="printData.doctorSignImgUrl" />
                        </span>
                        <span
                            v-else-if="printData.doctorSignImgUrl"
                            class="text"
                        >
                            {{ printData.doctorSignImgUrl }}
                        </span>
                        <span
                            v-else
                            class="text"
                        >
                            {{ printData.doctorName || printData.sellerName }}
                        </span>
                    </print-col>
                    <print-col
                        v-if="footerConfig.amount"
                        :span="6"
                    >
                        <span class="light-font">金额：</span>
                        <span class="text">
                            {{ totalPrice | formatMoney }}
                        </span>
                    </print-col>
                    <print-col
                        v-if="footerConfig.printDate"
                        :span="10"
                    >
                        <span class="light-font">打印时间：</span>
                        <span
                            class="text"
                        >
                            {{ new Date() | parseTime('y-m-d h:i:s') }}
                        </span>
                    </print-col>
                </print-row>
                <print-row>
                    <print-col
                        v-if="remark"
                        :span="24"
                        class="light-font"
                        v-html="remark"
                    >
                    </print-col>
                </print-row>
            </template>
            <template v-else-if="isMedicalCert">
                <print-row class="sign-row">
                    <print-col
                        :overflow="!printData.doctorSignImgUrl"
                        :class="{'no-wrap': !printData.doctorSignImgUrl}"
                        :span="8"
                    >
                        <span class="light-font">医生：</span>
                        <span
                            v-if="isImgUrl(printData.doctorSignImgUrl)"
                            class="sign-img"
                        >
                            <img :src="printData.doctorSignImgUrl" />
                        </span>
                        <span
                            v-else-if="printData.doctorSignImgUrl"
                            class="text"
                        >
                            {{ printData.doctorSignImgUrl }}
                        </span>
                        <span
                            v-else
                            class="text"
                        >
                            {{ printData.doctorName || printData.sellerName }}
                        </span>
                    </print-col>
                    <print-col
                        :span="6"
                    >
                        <span class="light-font">盖章：</span>
                        <span class="text">

                        </span>
                    </print-col>
                    <print-col
                        :span="10"
                    >
                        <span class="light-font">打印时间：</span>
                        <span
                            class="text"
                        >
                            {{ new Date() | parseTime('y-m-d h:i:s') }}
                        </span>
                    </print-col>
                </print-row>
                <print-row>
                    <print-col
                        :span="24"
                        class="footer-tips light-font"
                    >
                        1. 此证明书未经加盖公章无效
                    </print-col>
                    <print-col
                        :span="24"
                        class="footer-tips light-font"
                    >
                        2. 此证明书仅用于证明患者当前病情不作其他使用
                    </print-col>
                </print-row>
            </template>
            <template v-else-if="isPrescription">
                <print-row
                    v-if="footerConfig.billSign"
                    data-type="dae-sign"
                    style="width: 95.5%;"
                >
                    <print-col
                        :span="16"
                        style="text-align: right;"
                    >
                        <span class="light-font">大额处方意见:</span>
                    </print-col>
                    <print-col
                        :span="4"
                    >
                        &nbsp;
                        <span class="sign-border"></span>
                        <span class="light-font">同意</span>
                    </print-col>

                    <print-col
                        :span="4"
                    >
                        <span class="light-font">签名：</span>
                    </print-col>
                </print-row>
                <div class="inner-wrapper">
                    <print-row class="sign-row">
                        <print-col
                            :overflow="!printData.doctorSignImgUrl"
                            :class="{'no-wrap': !printData.doctorSignImgUrl}"
                            :style="{ width: `${prescriptionSpan}%` }"
                        >
                            <span class="light-font">医生：</span>
                            <template v-if="footerConfig.doctorSignature">
                                <span
                                    v-if="isImgUrl(printData.doctorSignImgUrl)"
                                    class="sign-img"
                                    style="left: 25pt;"
                                >
                                    <img :src="printData.doctorSignImgUrl" />
                                </span>
                                <span
                                    v-else-if="printData.doctorSignImgUrl"
                                    class="text"
                                >
                                    {{ printData.doctorSignImgUrl }}
                                </span>
                                <span
                                    v-else
                                    class="text"
                                >
                                    {{ printData.doctorName }}
                                </span>
                            </template>
                        </print-col>
                        <print-col
                            v-if="footerConfig.amount"
                            :style="{ width: `${prescriptionSpan}%` }"
                        >
                            <span class="light-font">金额：</span>
                            <span
                                class="text"
                            >
                                {{ totalPrice | formatMoney }}
                            </span>
                        </print-col>
                        <print-col
                            v-if="footerConfig.check"
                            :style="{ width: `${prescriptionSpan}%` }"
                        >
                            <span
                                class="light-font"
                            >审核：
                                <template v-if="footerConfig.checkSignature">
                                    <span
                                        v-if="isImgUrl(auditHandSign)"
                                        class="sign-img"
                                        style="left: 25pt;"
                                    >
                                        <img
                                            :src="auditHandSign"
                                        />
                                    </span>
                                    <span
                                        v-else-if="auditHandSign"
                                        class="text"
                                    >
                                        {{ auditHandSign }}
                                    </span>
                                    <span
                                        v-else
                                        class="text"
                                    >
                                        {{ auditName }}
                                    </span>
                                </template>
                            </span>
                        </print-col>
                        <print-col
                            v-if="footerConfig.assinger"
                            :style="{ width: `${prescriptionSpan}%` }"
                        >
                            <span class="light-font">调配：
                                <template v-if="footerConfig.assingerSignature">
                                    <span
                                        v-if="isImgUrl(compoundByHandSign)"
                                        class="sign-img"
                                        style="left: 25pt;"
                                    >
                                        <img
                                            :src="compoundByHandSign"
                                        />
                                    </span>
                                    <span
                                        v-else-if="compoundByHandSign"
                                        class="text"
                                    >
                                        {{ compoundByHandSign }}
                                    </span>
                                    <span
                                        v-else
                                        class="text"
                                    >
                                        {{ compoundName }}
                                    </span>
                                </template>

                            </span>
                        </print-col>
                        <print-col
                            v-if="footerConfig.dispense"
                            :style="{ width: `${prescriptionSpan}%` }"
                        >
                            <span class="light-font">核发：
                                <template v-if="footerConfig.dispenseSignature">
                                    <!-- 核发人 -->
                                    <span
                                        v-if="isImgUrl(dispensedByHandSign)"
                                        class="sign-img"
                                        style="left: 25pt;"
                                    >
                                        <img
                                            :src="dispensedByHandSign"
                                        />
                                    </span>
                                    <span
                                        v-else-if="dispensedByHandSign"
                                        class="text"
                                    >
                                        {{ dispensedByHandSign }}
                                    </span>
                                    <span
                                        v-else-if="dispensedByName"
                                        class="text"
                                    >
                                        {{ dispensedByName }}
                                    </span>
                                    <!--如果未发药 此时还没有发药人 核发签名应该是页面选择的发药人 -->
                                    <span
                                        v-else-if="isImgUrl(dispensedByHandSignInWaiting)"
                                        class="sign-img"
                                        style="left: 25pt;"
                                    >
                                        <img
                                            :src="dispensedByHandSignInWaiting"
                                        />
                                    </span>
                                    <span
                                        v-else-if="dispensedByHandSignInWaiting"
                                        class="text"
                                    >
                                        {{ dispensedByHandSignInWaiting }}
                                    </span>
                                    <span
                                        v-else
                                        class="text"
                                    >
                                        {{ dispensedByNameInWaiting }}
                                    </span>
                                </template>
                            </span>
                        </print-col>
                    </print-row>
                    <print-row>
                        <print-col
                            v-if="remark"
                            class="light-font overflow-hidden-item"
                            overflow
                            multiline
                            v-html="remark"
                        >
                        </print-col>
                        <print-col
                            v-if="footerConfig.printDate"
                            class="light-font"
                            v-html="curPrintTimeStr"
                        >
                        </print-col>
                    </print-row>
                </div>
            </template>
            <template v-else-if="isInfusionExecute">
                <div class="inner-wrapper">
                    <print-row class="sign-row">
                        <print-col
                            :overflow="!printData.doctorSignImgUrl"
                            :class="{'no-wrap': !printData.doctorSignImgUrl}"
                            :span="5"
                        >
                            <span class="light-font">医生：</span>
                            <template v-if="footerConfig.doctorSignature">
                                <span
                                    v-if="isImgUrl(printData.doctorSignImgUrl)"
                                    class="sign-img"
                                >
                                    <img :src="printData.doctorSignImgUrl" />
                                </span>
                                <span
                                    v-else-if="printData.doctorSignImgUrl"
                                    class="text"
                                >
                                    {{ printData.doctorSignImgUrl }}
                                </span>
                                <span
                                    v-else
                                    class="text"
                                >
                                    {{ printData.doctorName }}
                                </span>
                            </template>
                        </print-col>
                        <print-col
                            v-if="footerConfig.amount"
                            :span="5"
                        >
                            <span class="light-font">金额：</span>
                            <span
                                class="text"
                            >
                                {{ totalPrice | formatMoney }}
                            </span>
                        </print-col>
                        <print-col
                            v-if="footerConfig.check"
                            :span="5"
                        >
                            <span
                                class="light-font"
                            >审核：
                                <template v-if="footerConfig.checkSignature">
                                    <span
                                        v-if="isImgUrl(prForm.auditHandSign) "
                                        class="sign-img"
                                    >
                                        <img
                                            :src="prForm.auditHandSign "
                                        />
                                    </span>
                                    <span
                                        v-else-if="prForm.auditHandSign"
                                        class="text"
                                    >
                                        {{ prForm.auditHandSign }}
                                    </span>
                                    <span
                                        v-else
                                        class="text"
                                    >
                                        {{ prForm.auditName }}
                                    </span>
                                </template>
                            </span>
                        </print-col>
                        <print-col
                            v-if="footerConfig.assinger"
                            :span="5"
                        >
                            <span class="light-font">调配：
                                <template v-if="footerConfig.assingerSignature">
                                    <span
                                        v-if="isImgUrl(prForm.compoundByHandSign)"
                                        class="sign-img"
                                    >
                                        <img
                                            :src="prForm.compoundByHandSign "
                                        />
                                    </span>
                                    <span
                                        v-else-if="prForm.compoundByHandSign"
                                        class="text"
                                    >
                                        {{ prForm.compoundByHandSign }}
                                    </span>
                                    <span
                                        v-else
                                        class="text"
                                    >
                                        {{ prForm.compoundName }}
                                    </span>
                                </template>

                            </span>
                        </print-col>
                        <print-col
                            v-if="footerConfig.dispense"
                            :span="(!footerConfig.check || !footerConfig.assinger || !footerConfig.amount) ? 6 : 4"
                        >
                            <span class="light-font">核发：
                                <template v-if="footerConfig.dispenseSignature">
                                    <span
                                        v-if="isImgUrl(prForm.dispensedByHandSign)"
                                        class="sign-img"
                                    >
                                        <img
                                            :src="prForm.dispensedByHandSign "
                                        />
                                    </span>
                                    <span
                                        v-else-if="prForm.dispensedByHandSign"
                                        class="text"
                                    >
                                        {{ prForm.dispensedByHandSign }}
                                    </span>
                                    <span
                                        v-else
                                        class="text"
                                    >
                                        {{ prForm.dispensedByName }}
                                    </span>
                                </template>
                            </span>
                        </print-col>
                    </print-row>

                    <print-row>
                        <print-col
                            v-if="remark"
                            class="light-font overflow-hidden-item"
                            overflow
                            multiline
                            v-html="remark"
                        >
                        </print-col>
                        <print-col
                            v-if="footerConfig.printDate"
                            class="light-font"
                            v-html="curPrintTimeStr"
                        >
                        </print-col>
                    </print-row>
                </div>
            </template>
            <template v-else>
                <print-row
                    v-if="footerConfig.billSign"
                    data-type="dae-sign"
                    style="width: 95.5%;"
                >
                    <print-col
                        :span="16"
                        style="text-align: right;"
                    >
                        <span class="light-font">大额处方意见:</span>
                    </print-col>
                    <print-col
                        :span="4"
                    >
                        &nbsp;
                        <span class="sign-border"></span>
                        <span class="light-font">同意</span>
                    </print-col>

                    <print-col
                        :span="4"
                    >
                        <span class="light-font">签名：</span>
                    </print-col>
                </print-row>
                <div class="inner-wrapper">
                    <print-row class="sign-row">
                        <print-col
                            :overflow="!printData.doctorSignImgUrl"
                            :class="{'no-wrap': !printData.doctorSignImgUrl}"
                            :span="5"
                        >
                            <span class="light-font">医生：</span>
                            <template v-if="footerConfig.doctorSignature || !isPrOrInfusionExecute">
                                <span
                                    v-if="isImgUrl(printData.doctorSignImgUrl)"
                                    class="sign-img"
                                >
                                    <img :src="printData.doctorSignImgUrl" />
                                </span>
                                <span
                                    v-else-if="printData.doctorSignImgUrl"
                                    class="text"
                                >
                                    {{ printData.doctorSignImgUrl }}
                                </span>
                                <span
                                    v-else
                                    class="text"
                                >
                                    {{ printData.doctorName }}
                                </span>
                            </template>
                        </print-col>
                        <print-col
                            v-if="footerConfig.amount"
                            :span="5"
                        >
                            <span class="light-font">金额：</span>
                            <span
                                class="text"
                            >
                                {{ totalPrice | formatMoney }}
                            </span>
                        </print-col>
                        <print-col
                            v-if="footerConfig.check"
                            :span="5"
                        >
                            <span
                                class="light-font"
                            >审核：
                                <template v-if="footerConfig.checkSignature || !isPrOrInfusionExecute">
                                    <span
                                        v-if="isImgUrl(printData.auditHandSign) "
                                        class="sign-img"
                                    >
                                        <img
                                            :src="printData.auditHandSign "
                                        />
                                    </span>
                                    <span
                                        v-else-if="printData.auditHandSign"
                                        class="text"
                                    >
                                        {{ printData.auditHandSign }}
                                    </span>
                                    <span
                                        v-else
                                        class="text"
                                    >
                                        {{ printData.auditName }}
                                    </span>
                                </template>
                            </span>
                        </print-col>
                        <print-col
                            v-if="footerConfig.assinger"
                            :span="5"
                        >
                            <span class="light-font">调配：
                                <template v-if="footerConfig.assingerSignature || !isPrOrInfusionExecute">
                                    <span
                                        v-if="isImgUrl(printData.compoundByHandSign)"
                                        class="sign-img"
                                    >
                                        <img
                                            :src="printData.compoundByHandSign "
                                        />
                                    </span>
                                    <span
                                        v-else-if="printData.compoundByHandSign"
                                        class="text"
                                    >
                                        {{ printData.compoundByHandSign }}
                                    </span>
                                    <span
                                        v-else
                                        class="text"
                                    >
                                        {{ printData.compoundName }}
                                    </span>
                                </template>

                            </span>
                        </print-col>
                        <print-col
                            v-if="footerConfig.dispense"
                            :span="(!footerConfig.check || !footerConfig.assinger || !footerConfig.amount) ? 6 : 4"
                        >
                            <span class="light-font">核发：
                                <template v-if="footerConfig.dispenseSignature || !isPrOrInfusionExecute">
                                    <span
                                        v-if="isImgUrl(printData.dispensedByHandSign)"
                                        class="sign-img"
                                    >
                                        <img
                                            :src="printData.dispensedByHandSign "
                                        />
                                    </span>
                                    <span
                                        v-else-if="printData.dispensedByHandSign"
                                        class="text"
                                    >
                                        {{ printData.dispensedByHandSign }}
                                    </span>
                                    <span
                                        v-else
                                        class="text"
                                    >
                                        {{ printData.dispensedByName }}
                                    </span>
                                </template>
                            </span>
                        </print-col>
                    </print-row>

                    <print-row>
                        <print-col
                            v-if="remark"
                            class="light-font overflow-hidden-item"
                            overflow
                            multiline
                            v-html="remark"
                        >
                        </print-col>
                        <print-col
                            v-if="footerConfig.printDate"
                            class="light-font"
                            v-html="curPrintTimeStr"
                        >
                        </print-col>
                    </print-row>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
    import { formatMoney, formatAddress, parseTime, isImgUrl } from '../../common/utils.js';
    import PrintRow from '../layout/print-row.vue'
    import PrintCol from '../layout/print-col.vue'


    const PrintTypes = {
        TREATMENT_EXECUTE: 'treatmentExecute', // 治疗执行单
        INFUSION_EXECUTE: 'infusionExecute', // 输注执行单
        PRESCRIPTION: 'prescription', // 处方笺
        EXAMINATION: 'examination', // 检查检验单
        MEDICAL: 'medical', // 病历
        MEDICAL_CERTIFICATE: 'medicalCertificate', // 病情证明书
        CHILD_TEST_RESULT: 'childTestResult', // 儿童测试报告
        CHILD_HEALTH_REPORT: 'childHealthReport', // 儿童健康报告
        CHILD_QUESTION_TABLE: 'childQuestionTable', // 儿童问卷
        CHRONIC_CARE: 'chronicCare', // 慢病管理
        A5_DISPENSING: 'A5Dispensing', // A5发药单打印
        STAT: 'statPrint', // 统计打印
        FAMILY_DOCTOR_AGREEMENT: 'familyDoctorAgreementPrint', // 家庭医生打印
    }
    export default {
        name: 'OutpatientFooter',
        components: {
            PrintCol,
            PrintRow,
        },
        filters: {
            parseTime,
            formatMoney,
        },
        props: {
            printData: {
                type: Object,
                required: true,
                default: () => {
                    return {};
                },
            },
            config: {
                type: Object,
                required: true,
            },
            printType: {
                type: String,
                default: '',
            },
            prForm: {
                type: Object,
                default: () => {
                    return null;
                },
            },
            showTotalPrice: Boolean,
            printTime: {
                type: String,
                default: '',
            },
            isChinese: Boolean,
            dispensingInfo: {
                type: Object,
                default: () => ({}),
            },
            isExternal: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                PrintTypes,
            };
        },
        computed: {
            footerConfig() {
                return this.config.footer || {};
            },
            address() {
                return this.printData.patient.address;
            },
            showAmount() {
                return (this.config && this.config.pr && this.config.pr.amount) || 0;
            },
            addressDetail() {
                return formatAddress(this.address);
            },
            composeChildren() {
                return this.config.content?.composeChildren ?? 1;
            },
            /**
             * @desc 是否是执行单
             */
            isExecute() {
                return this.printType === this.PrintTypes.INFUSION_EXECUTE || this.printType === this.PrintTypes.TREATMENT_EXECUTE;
            },
            isExamination() {
                return this.printType === this.PrintTypes.EXAMINATION;
            },
            isTreatment() {
                return this.printType === this.PrintTypes.TREATMENT_EXECUTE;
            },
            isPr() {
                return this.printType === this.PrintTypes.PRESCRIPTION;
            },
            isMedical() {
                return this.printType === this.PrintTypes.MEDICAL;
            },
            isMedicalCert() {
                return this.printType === this.PrintTypes.MEDICAL_CERTIFICATE;
            },
            isPrescription() {
                return this.printType === this.PrintTypes.PRESCRIPTION;
            },
            isInfusionExecute() {
                return this.printType === this.PrintTypes.INFUSION_EXECUTE;
            },
            isPrOrInfusionExecute() {
                return this.isPr || this.isInfusionExecute;
            },
            totalPrice() {
                if (this.isTreatment) {
                    return this.printData.totalPrice
                }
                if (this.isExamination) {
                    let totalPrice = 0;
                    if (this.printData && Array.isArray(this.printData.examinationFormItems)) {
                        // 如果展示包含套餐母项，使用套餐母项的总价
                        if (this.composeChildren === 0 || this.composeChildren === 2) {
                            this.printData.examinationFormItems.forEach(examItem => {
                                if (examItem.composeChildren && examItem.composeChildren.length) {
                                    // 套餐总价使用composeTotalPrice
                                    totalPrice = totalPrice + (examItem.composeTotalPrice ?? examItem.totalPrice);
                                } else {
                                    // 非套餐总价使用totalPrice
                                    totalPrice = totalPrice + examItem.totalPrice;
                                }
                            })
                        } else {
                            // 如果只展示套餐子项，使用所有套餐子项的总价和
                            this.printData.examinationFormItems.forEach(examItem => {
                                if (examItem.composeChildren && examItem.composeChildren.length) {
                                    for (let child of examItem.composeChildren) {
                                        totalPrice = totalPrice + child.totalPrice;
                                    }
                                } else {
                                    // 非套餐总价使用totalPrice
                                    totalPrice = totalPrice + examItem.totalPrice;
                                }
                            })
                        }
                    }
                    return totalPrice;
                }
                if (this.isExecute) {
                    return this.prForm && this.prForm.totalPrice
                }
                if (this.isMedical) {
                    const { displayTotalPrice } = this.printData;
                    return displayTotalPrice;
                }
                // 总金额
                if (this.footerConfig.amount === 2) {
                    const { displayTotalPrice } = this.printData;
                    return displayTotalPrice;
                }
                // 药品金额
                if (this.footerConfig.amount === 1) {
                    const {
                        displayTotalPrice,
                        medicinePriceInfo,
                    } = this.prForm || {};
                    const {
                        displayTotalPrice: medicineDisplayTotalPrice,
                    } = medicinePriceInfo || {};
                    if (this.isChinese) {
                        // 中药处方包含了加工费、快递费，需要用medicine-前缀
                        return medicineDisplayTotalPrice;
                    } else {
                        return displayTotalPrice;
                    }
                }
                return 0;
            },
            remark() {
                if(!this.footerConfig.remark) {
                    return ''
                }
                return this.footerConfig.remark.split('\n').filter(it => !!it).map(string => {
                    return `
                        <div style="line-height: 12pt; font-size: 8pt;">${string.trim()}</div>
                    `;
                }).join('');
            },
            curPrintTime() {
                return this.printTime || new Date();
            },
            curPrintTimeStr() {
                let str  = `打印时间：${ parseTime(this.curPrintTime, 'y-m-d h:i:s')}`;
                return `
                    <div style="line-height: 12pt; font-size: 8pt;">${str}</div>
                `;
            },
            prescriptionDispensingInfo() {
                return this.dispensingInfo || {};
            },
            auditHandSign() {
                return this.prescriptionDispensingInfo.auditHandSign;
            },
            auditName() {
                return this.prescriptionDispensingInfo.auditName;
            },
            compoundByHandSign() {
                return this.prescriptionDispensingInfo.compoundByHandSign;
            },
            compoundName() {
                return  this.prescriptionDispensingInfo.compoundName;
            },
            dispensedByHandSign() {
                return this.prescriptionDispensingInfo.dispensedByHandSign;
            },
            dispensedByName() {
                return this.prescriptionDispensingInfo.dispensedByName;
            },
            dispensedByHandSignInWaiting(){
                return this.printData.dispensedByHandSignInWaiting ? this.printData.dispensedByHandSignInWaiting :''
            },
            dispensedByNameInWaiting(){
                return this.printData.dispensedByNameInWaiting ? this.printData.dispensedByNameInWaiting: ''
            },
            organAddress() {
                const { organ } = this.printData;
                const addressDetail = organ.addressDetail || '';
                // 详细地址中包含省名则认为地址为地图选点，包含了省市区位置
                if(organ.addressProvinceName && addressDetail.includes(organ.addressProvinceName)) {
                    return addressDetail;
                }
                return (organ.addressProvinceName || '') + ( organ.addressCityName || '') + (organ.addressDistrictName || '') + (organ.addressDetail || '');
            },
            organNumber() {
                const { organ } = this.printData;
                return organ.contactPhone || '';
            },
            prescriptionSpan() {
                let count = 1;
                if (this.footerConfig.amount) {
                    count += 1;
                }
                if (this.footerConfig.check) {
                    count += 1;
                }
                if (this.footerConfig.assinger) {
                    count += 1;
                }
                if (this.footerConfig.dispense) {
                    count += 1;
                }
                return 100 / count;
            },
        },
        methods: {
            isImgUrl,
            calcWidth(type) {
                if (type === 'doctor') {
                    if (!this.footerConfig.seller && !this.footerConfig.amount && !this.footerConfig.printDate) {
                        return 24;
                    } else if (this.footerConfig.seller && !this.footerConfig.amount && !this.footerConfig.printDate) {
                        return 12;
                    } else if (!this.footerConfig.seller && this.footerConfig.amount && !this.footerConfig.printDate) {
                        return 18;
                    } else if (!this.footerConfig.seller && !this.footerConfig.amount && this.footerConfig.printDate) {
                        return 14;
                    } else if (this.footerConfig.seller && this.footerConfig.amount && !this.footerConfig.printDate) {
                        return 9;
                    } else if (this.footerConfig.seller && !this.footerConfig.amount && this.footerConfig.printDate) {
                        return 7;
                    } else if (!this.footerConfig.seller && this.footerConfig.amount && this.footerConfig.printDate) {
                        return 8;
                    } else {
                        return 9;
                    }
                } else if (type === 'seller') {
                    if (!this.footerConfig.doctor && !this.footerConfig.amount && !this.footerConfig.printDate) {
                        return 24;
                    } else if (this.footerConfig.doctor && !this.footerConfig.amount && !this.footerConfig.printDate) {
                        return 12;
                    } else if (!this.footerConfig.doctor && this.footerConfig.amount && !this.footerConfig.printDate) {
                        return 18;
                    } else if (!this.footerConfig.doctor && !this.footerConfig.amount && this.footerConfig.printDate) {
                        return 14;
                    } else if (this.footerConfig.doctor && this.footerConfig.amount && !this.footerConfig.printDate) {
                        return 9;
                    } else if (this.footerConfig.doctor && !this.footerConfig.amount && this.footerConfig.printDate) {
                        return 7;
                    } else if (!this.footerConfig.doctor && this.footerConfig.amount && this.footerConfig.printDate) {
                        return 8;
                    } else {
                        return 9;
                    }
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'index';

.print-medical-document-footer-wrapper {
    .print-medical-document-footer {
        &.no-border-top {
            padding-top: 0;
            border-top: none;
        }

        .inner-wrapper {
            padding-top: 5pt;
            border-top: 1px solid #000000;
        }
    }
}
</style>
