<template>
    <div class="print-medical-document-footer-wrapper">
        <!-- 机构公章 吉林 && 有机构公章 -->
        <template v-if="isJiLin && printData.institutionalSeal">
            <div class="institutional-box">
                <img
                    :src="printData.institutionalSeal"
                    class="institutional-seal"
                    alt=""
                />
            </div>
        </template>
        <div class="print-medical-document-footer">
            <template v-if="isMountainwest">
                <print-row class="sign-row-mountainwest">
                    <!-- 第一行 -->
                    <print-col
                        :span="8"
                        class="item"
                    >
                        <span class="light-font">医师：</span>
                        <div class="down-line">
                            <template v-if="printData.doctorSignImgUrl">
                                <span class="sign-img">
                                    <img :src="printData.doctorSignImgUrl" />
                                </span>
                            </template>
                            <template v-else>
                                <span class="text">{{ printData.doctorName }}</span>
                            </template>
                        </div>
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        <span class="light-font">初审药师:</span>
                        <div class="down-line"></div>
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        <span class="light-font">药品金额:</span>
                        <div class="down-line"></div>
                    </print-col>
                </print-row>
                <print-row class="sign-row-mountainwest line-bottom">
                    <print-col
                        :span="8"
                        class="item"
                    >
                        <span class="light-font">审核药师：</span>
                        <div class="down-line">
                            <template v-if="printData.auditNameSignImgUrl">
                                <span class="sign-img">
                                    <img :src="printData.auditNameSignImgUrl" />
                                </span>
                            </template>
                            <template v-else>
                                <span class="text">{{ printData.auditName }}</span>
                            </template>
                        </div>
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        <span class="light-font">调剂:</span>
                        <div class="down-line"></div>
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        <span class="light-font">复核/发药:</span>
                        <div class="down-line"></div>
                    </print-col>
                </print-row>
            </template>
            <template v-else>
                <print-row class="sign-row">
                    <print-col :span="12">
                        <span class="light-font">开方医师：</span>
                        <template v-if="printData.doctorSignImgUrl">
                            <span class="sign-img">
                                <img :src="printData.doctorSignImgUrl" />
                            </span>
                        </template>
                        <template v-else>
                            <span class="text">{{ printData.doctorName }}</span>
                        </template>
                    </print-col>
                    <print-col :span="12">
                        <span class="light-font">审核药师：</span>
                        <template v-if="printData.auditNameSignImgUrl">
                            <span class="sign-img">
                                <img :src="printData.auditNameSignImgUrl" />
                            </span>
                        </template>
                        <template v-else>
                            <span class="text">{{ printData.auditName }}</span>
                        </template>
                    </print-col>
                    <print-col
                        :span="12"
                        class="item"
                        overflow
                    >
                        <span class="light-font">调配、复核药师:</span>
                    </print-col>
                    <print-col
                        :span="12"
                        class="item"
                        overflow
                    >
                        <span class="light-font">核对、发药药师:</span>
                        <span class="text">
                            {{ printData.dispensedByName }}
                        </span>
                    </print-col>
                </print-row>
            </template>
            <template v-if="isJiLin">
                <print-row class="extend-row">
                    <print-col
                        class="extends-tips"
                        overflow
                    >
                        延长处方用量原因：
                    </print-col>
                    <print-col
                        class="extend-line"
                        overflow
                    >
                        {{ printData.extendsReason }}
                    </print-col>
                </print-row>
            </template>
            <template v-else-if="isMountainwest">
                <print-row class="extend-row">
                    <print-col
                        :span="24"
                        class="item"
                        overflow
                    >
                        注意： 处方有效期为3天，擅自下载打印无效。
                    </print-col>
                </print-row>
            </template>
            <template v-else>
                <print-row class="tips-row">
                    <print-col
                        :span="24"
                        class="item"
                        overflow
                    >
                        为了保证患者用药安全、有效、请注意
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        1. 认真核对药名和数量等处方信息
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        2. 严禁将本处方药品予他人使用
                    </print-col>
                    <print-col
                        :span="8"
                        class="item"
                        overflow
                    >
                        3. 药品一经发出，不得退换
                    </print-col>
                </print-row>
            </template>
        </div>
    </div>
</template>

<script>
    import { formatMoney,formatAddress,parseTime } from '../../common/utils.js';
    import PrintRow from '../layout/print-row.vue'
    import PrintCol from '../layout/print-col.vue'

    const PrintTypes = {
        TREATMENT_EXECUTE: 'treatmentExecute', // 治疗执行单
        INFUSION_EXECUTE: 'infusionExecute', // 输注执行单
        PRESCRIPTION: 'prescription', // 处方笺
        EXAMINATION: 'examination', // 检查检验单
        MEDICAL: 'medical', // 病历
        MEDICAL_CERTIFICATE: 'medicalCertificate', // 病情证明书
        CHILD_TEST_RESULT: 'childTestResult', // 儿童测试报告
        CHILD_HEALTH_REPORT: 'childHealthReport', // 儿童健康报告
        CHILD_QUESTION_TABLE: 'childQuestionTable', // 儿童问卷
        CHRONIC_CARE: 'chronicCare', // 慢病管理
        A5_DISPENSING: 'A5Dispensing', // A5发药单打印
        STAT: 'statPrint', // 统计打印
        FAMILY_DOCTOR_AGREEMENT: 'familyDoctorAgreementPrint', // 家庭医生打印
    }

    export default {
        name: 'OutpatientFooter',
        components: {
            PrintCol,
            PrintRow,
        },
        filters: {
            parseTime,
            formatMoney,
        },
        props: {
            printData: {
                type: Object,
                required: true,
                default: () => {
                    return {};
                }
            },
            config: {
                type: Object,
                required: true,
            },
            printType: {
                type: String,
                default: '',
            },
            prForm: {
                type: Object,
                default: () => {
                    return null;
                }
            },
            showTotalPrice: Boolean,
            printTime: {
                type: String,
                default: '',
            }
        },
        computed: {
            // 吉林配置
            isJiLin() {
                return this.printData.organ?.region?.startsWith('jilin')
            },
            // 山西配置
            isMountainwest() {
                return this.printData.organ?.region?.startsWith('mountainwest')
            },
            footerConfig() {
                return this.config.footer || {};
            },
            address() {
                return this.printData.patient.address;
            },
            showAmount() {
                return (this.config && this.config.pr && this.config.pr.amount) || 0;
            },
            addressDetail() {
                return formatAddress(this.address);
            },
            /**
             * @desc 是否是执行单
             */
            isExecute() {
                return this.printType === PrintTypes.INFUSION_EXECUTE || this.printType === PrintTypes.TREATMENT_EXECUTE;
            },
            isExamination() {
                return this.printType === PrintTypes.EXAMINATION;
            },
            isTreatment() {
                return this.printType === PrintTypes.TREATMENT_EXECUTE;
            },
            dispensedByName() {
                return this.printData.dispensedByName
            },

            isMedical() {
                return this.printType === PrintTypes.MEDICAL;
            },
            isMedicalCert() {
                return this.printType === PrintTypes.MEDICAL_CERTIFICATE;
            },
            totalPrice() {
                if (this.isTreatment) {
                    let totalPrice = 0
                    this.printData.executeForms.forEach(formItem => {
                        formItem.executeFormItems.forEach(groupItem => {
                            groupItem.groupItems.forEach(treatItem => {
                                totalPrice = totalPrice + treatItem.totalPrice
                            })
                        })
                    })
                    return totalPrice
                }
                if (this.isExamination) {
                    let totalPrice = 0
                    this.printData.examinationFormItems.forEach(examItem => {
                        totalPrice = totalPrice + examItem.totalPrice
                    })
                    return totalPrice
                } if (this.isExecute) {
                    return this.prForm && this.prForm.totalPrice
                }
                return this.footerConfig.amount === 2 ? this.printData.totalPrice : (this.prForm && this.prForm.totalPrice || null);
            },
            remark() {
                if(!this.footerConfig.remark) {
                    return ''
                }
                return this.footerConfig.remark.split('\n').filter(it => !!it).map(string => {
                    return string.split('')
                        .map(charactor => `<div style="height: 8pt;display: inline-block;max-width: 8pt;"><span style="font-size: 10pt; zoom: 0.8;">${charactor}</span></div>`)
                        .join('')
                }).join('<br/>')
            },
            curPrintTime() {
                return this.printTime || new Date();
            },
            curPrintTimeStr() {
                let str  = `打印时间：${ parseTime(this.curPrintTime, 'y-m-d h:i:s')}`;
                return str.split('\n').map(string => {
                    return string.split('')
                        .map(charactor => {
                            if(charactor !== ' ') {
                                return `<div style="height: 8pt;display: inline-block;max-width: 8pt;"><span style="font-size: 10pt; zoom: 0.8;">${charactor}</span></div>`;
                            }
                            return `&nbsp;`
                        })
                        .join('')
                }).join('<br/>')
            }
        },
    };
</script>

<style lang="scss">
@import 'index';

.print-medical-document-footer-wrapper {
    .institutional-box {
        position: relative;
        width: 100%;
        height: 90pt;

        .institutional-seal {
            position: absolute;
            bottom: 10pt;
            right: 0;
            width: 127pt;
            height: 85pt;
        }
    }
}

.print-medical-document-footer {

    .sign-row {
        padding-bottom: 4pt;
        height: 50px;
    }

    .sign-row-mountainwest {
        padding-bottom: 2pt;
        height: 30px;

        .item {
            margin-top: 5pt;
            box-sizing: border-box;
            padding: 0 5pt;
            display: flex;
        }

        .line-bottom {
            margin-top: 8pt;
        }

        .down-line {
            display: flex;
            flex: 1;
            margin-left: 5px;
            border-bottom: 1px solid #000;
        }

        .sign-img img {
            margin-left: 15pt;
        }
    }

    .light-font, .text {
        font-size: 8pt;
    }

    .extend-row {
        display: flex;
        padding: 10pt 0;
        border-top: 1px solid #000;
        font-size: 8pt;

        .extends-tips {
            width: 100px;
        }

        .extend-line {
            width: 100%;
            flex: 1;
            border-bottom: 1px solid #000;
        }
    }

    .tips-row {
        padding-top: 8pt;
        font-size: 7pt;
        line-height: 16pt;
        border-top: 1px solid #000;
    }
}
</style>
