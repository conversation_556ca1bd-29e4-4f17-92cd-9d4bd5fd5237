<template>
    <div class="prescription-v2-footer-wrapper">
        <spacing-line
            :top-margin="0"
            line-type="solid"
        ></spacing-line>
        
        <div class="prescription-v2-footer-content">
            <div class="prescription-v2-footer-info-wrapper">
                <div class="prescription-v2-footer-sign-wrapper">
                    <div
                        v-for="(item, index) in footerInfoList"
                        :key="`prescription-v2-footer-${index}`"
                        class="prescription-v2-footer-sign-item"
                        :class="`${item.isWidthDouble ? 'prescription-v2-footer-sign-item-width-double' : ''}`"
                        :style="item.style"
                    >
                        {{ item.label }}<template v-if="!item.isEmpty">
                            <img
                                v-if="item.isImage"
                                :src="item.value"
                                alt=""
                                class="prescription-v2-footer-sign-img"
                            />
                            <template v-else>
                                {{ item.value }}
                            </template>
                        </template>
                    </div>
                </div>

                <div
                    v-if="remark"
                    class="prescription-v2-footer-remark-wrapper"
                    v-html="remark"
                ></div>

                <div
                    v-if="config.printDate"
                    class="prescription-v2-footer-remark-wrapper"
                    style="padding-top: 3px;"
                >
                    打印时间：{{ curPrintTime | parseTime }}
                </div>
            </div>
            
            <div
                v-if="config.qrcode && qrCode"
                class="prescription-v2-footer-qr-code-wrapper"
            >
                <div class="prescription-v2-footer-qr-code-img-wrapper">
                    <img
                        :src="qrCode"
                        alt=""
                        class="prescription-v2-footer-qr-code-img"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import SpacingLine from "../../medical-document-header/spacing-line.vue";
    import {formatMoney, isImgUrl, parseTime} from "../../../common/utils";

    export default {
        name: 'PrescriptionV2Footer',
        components: {SpacingLine},
        filters: {
            parseTime,
        },
        props: {
            config: {
                type: Object,
                required: true,
            },
            qrCode: {
                type: String,
                default: '',
            },
            doctorSignImgUrl: {
                type: String,
                default: '',
            },
            doctorName: {
                type: String,
                default: '',
            },
            auditName: {
                type: String,
                default: '',
            },
            auditHandSign: {
                type: String,
                default: '',
            },
            compoundName: {
                type: String,
                default: '',
            },
            compoundByHandSign: {
                type: String,
                default: '',
            },
            dispensedByNameInWaiting: {
                type: String,
                default: '',
            },
            dispensedByHandSignInWaiting:{
                type: String,
                default: '',
            },
            dispensedByName: {
                type: String,
                default: '',
            },
            dispensedByHandSign: {
                type: String,
                default: '',
            },
            displayTotalPrice: {
                type: Number,
                default: 0,
            },
            form: {
                type: Object,
                default: () => ({}),
            },
            isChinese: {
                type: Boolean,
                default: false,
            },
            billSign: {
                type: Number,
                default: 0,
            },
            printTime: {
                type: String,
                default: '',
            },
        },
        computed: {
            totalPrice() {
                // 总金额
                if (this.config.amount === 2) {
                    return this.displayTotalPrice;
                }
                // 药品金额
                if (this.config.amount === 1) {
                    const {
                        displayTotalPrice,
                        medicinePriceInfo
                    } = this.form;
                    const {
                        displayTotalPrice: medicineDisplayTotalPrice,
                    } = medicinePriceInfo || {};
                    if (this.isChinese) {
                        return medicineDisplayTotalPrice;
                    } else {
                        return displayTotalPrice;
                    }
                }
                return 0;
            },
            footerInfoList() {
                const res = [];
                let count = 1;
                if (this.config.check) {
                    count++;
                }
                if (this.config.assinger) {
                    count++;
                }
                if (this.config.dispense) {
                    count++;
                }
                const widthCopies = 100 / count;

                const isDoctorImage = isImgUrl(this.doctorSignImgUrl);
                res.push({
                    label: '医生：',
                    value: isDoctorImage ? this.doctorSignImgUrl : this.doctorName,
                    isEmpty: !this.config.doctorSignature,
                    isImage: isDoctorImage,
                    style: { width: count === 1 ? '50%' : `${widthCopies}%` },
                })
                if (this.config.check) {
                    const isAuditImage = isImgUrl(this.auditHandSign);
                    res.push({
                        label: '审核：',
                        value: isAuditImage ? this.auditHandSign : this.auditName,
                        isEmpty: !this.config.checkSignature,
                        isImage: isAuditImage,
                        style: { width: `${widthCopies}%` },
                    })
                }
                if (this.config.assinger) {
                    const isCompoundImage = isImgUrl(this.compoundByHandSign);
                    res.push({
                        label: '调配：',
                        value: isCompoundImage ? this.compoundByHandSign : this.compoundName,
                        isEmpty: !this.config.assingerSignature,
                        isImage: isCompoundImage,
                        style: { width: `${widthCopies}%` },
                    })
                }
                if (this.config.dispense) {
                    const isDispensedImage = isImgUrl(this.dispensedByHandSign);
                    const isDispensedInWaitingImage= isImgUrl(this.dispensedByHandSignInWaiting)
                    //如果未发药 此时还没有发药人 核发签名应该是页面选择的发药人
                    res.push({
                        label: '核发：',
                        value: isDispensedImage ? this.dispensedByHandSign : (this.dispensedByName?this.dispensedByName: (isDispensedInWaitingImage?this.dispensedByHandSignInWaiting:this.dispensedByNameInWaiting)),
                        isEmpty: !this.config.dispenseSignature,
                        isImage: isDispensedImage || (!this.dispensedByName && isDispensedInWaitingImage),
                        style: { width: `${widthCopies}%` },
                    })
                }
                if (this.config.amount) {
                    res.push({
                        label: '金额：',
                        value: formatMoney(this.totalPrice),
                        isEmpty: false,
                        isImage: false,
                        style: { width: count === 1 ? '50%' : `${widthCopies}%` },
                    })
                }
                if (this.billSign) {
                    res.push({
                        label: '大额处方签名：',
                        value: '',
                        isEmpty: true,
                        isImage: false,
                        isWidthDouble: true,
                        style: count === 1 ? { width: '100%' } : { flex: 1 },
                    })
                }

                return res;
            },
            remark() {
                if (!this.config.remark) return '';
                const remarkList = this.config.remark.split('\n').filter((str) => !!str);
                return remarkList.map((str, index) => {
                    if (index < remarkList.length) {
                        return `${str}<br />`;
                    }
                    return str;
                }).join('');
            },
            curPrintTime() {
                return this.printTime || new Date();
            },
        },
    }
</script>

<style lang="scss">
.prescription-v2-footer-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 99px;
    padding-top: 24px;

    .prescription-v2-footer-content {
        display: flex;
        justify-content: space-between;
        width: 100%;
        min-height: 65px;

        .prescription-v2-footer-info-wrapper {
            display: flex;
            flex: 1;
            flex-direction: column;
            height: 100%;
            font-weight: 300;
            line-height: 16px;

            .prescription-v2-footer-sign-wrapper {
                display: flex;
                flex-wrap: wrap;
                gap: 5px 0;
                width: 100%;
                font-size: 13px;
            }

            .prescription-v2-footer-sign-item {
                position: relative;
                display: flex;
                align-items: center;
                width: 25%;
                padding-right: 8px;

                &:nth-child(4n) {
                    padding-right: 0;
                }
            }

            .prescription-v2-footer-sign-img {
                position: absolute;
                top: -7px;
                left: 34px;
                width: auto;
                height: 30px;
            }

            .prescription-v2-footer-sign-item-width-double {
                width: 50%;
            }

            .prescription-v2-footer-remark-wrapper {
                display: flex;
                width: 100%;
                padding-top: 8px;
                font-size: 10px;
                line-height: normal;
            }
        }

        .prescription-v2-footer-qr-code-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            width: 50px;
            height: 100%;

            .prescription-v2-footer-qr-code-img-wrapper {
                width: 48px;
                height: 48px;
            }

            .prescription-v2-footer-qr-code-img {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>
