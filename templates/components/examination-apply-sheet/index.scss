.print-medical-document-header {
    position: relative;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', 微软雅黑;
    //&.is-landscape {
    //    margin-bottom: 10pt;
    //}

    .space {
        padding: 0 3pt;
    }

    .print-col {
        font-size: 10pt;
        overflow: hidden;
        white-space: nowrap;
        word-break: keep-all;
    }

    .organ-name {
        max-height: 42pt;
        padding: 0 88pt;
        overflow-y: hidden;
        font-size: 16pt;
        line-height: 21pt;
        text-align: center;
        word-break: break-all;

        &.organ-name-13 {
            max-height: 36pt;
            font-size: 15pt;
            line-height: 18pt;
        }
        &.organ-name-14 {
            max-height: 34pt;
            font-size: 14pt;
            line-height: 17pt;
        }
        &.organ-name-15 {
            max-height: 30pt;
            font-size: 12pt;
            line-height: 15pt;
        }
        &.organ-name-no-barcode {
            padding: 0 42pt;
        }
    }

    .document-type {
        font-size: 13pt;
        line-height: 19pt;
        text-align: center;

        .document-title {
            position: relative;
            display: inline-block;
            padding: 0 2pt;
        }

        .pr-type {
            position: absolute;
            top: 0;
            right: -86pt;
            width: 80pt;
            font-size: 11pt;
            font-weight: 300;
            text-align: left;
        }
    }

    .qr-code-img {
        position: absolute;
        top: 0;
        right: -6pt;
        width: 50pt;
        height: 50pt;
        z-index: 1;

        > img {
            width: 50pt;
            height: 50pt;
        }
    }

    .logo-img {
        position: absolute;
        top: 0;
        left: -6pt;
        width: 94pt;
        height: 40pt;

        > img {
            width: 100%;
            height: 100%;
        }
        .logo {
            width: 94pt;
            height: auto;
            max-height: 100%;
        }

        .process-icon {
            font-size: 10pt;
            font-weight: 500;
        }
    }

    .base-info {
        font-weight: 300;
        line-height: 12pt;
    }

    .date-info {
        padding-bottom: 6pt;
        margin-top: 20pt;
        border-bottom: 1px solid #000000;
    }

    .has-margin-top {
        margin-top: 5pt;
    }

    .patient-info {
        margin-bottom: 6pt;
    }

    .line-split {
        height: 0;
        border-top: 1px solid #000000;
    }

    .rp-icon {
        font-size: 10pt;
        line-height: 19pt;
        //&.is-landscape-rp {
        //    position: absolute;
        //    left: 0;
        //    bottom: -22pt;
        //}
    }
    .jinma-style {
        position: absolute;
        top: 0;
        right: -6pt;
        width: 40pt;
        text-align: center;
        z-index: 1;
        border: #1d1f21 solid 1pt;
    }

    .examination-header {
        display: flex;
        align-items: center;
        width: 100%;
        min-height: 50pt;
        margin-bottom: 20pt;
        position: relative;
       

        .left {
            position: absolute;
            left: 0;
            top: 0;
            width: 91pt;
            height: 100%;
            min-height: 50pt;
            display: flex;
            flex-direction: column;
            align-items: center;

            >img {
                width: 91pt;
                height: 100%;
            }

            >span {
                font-size: 10pt;
                line-height: 12pt;
                width: 100%;
                display: inline-flex;
                justify-content: space-between;
                align-content: center;
            }
        }
        .center {
            width: 100%;
            padding: 0 50pt 0 91pt;
        }
        .right {
            position: absolute;
            right: 0;
            top: 50%;
            margin-top: -25pt;
            width: 50pt;
            height: 50pt;

            >img {
                width:100%;
                height: 100%;
            }
        }
    }
}
