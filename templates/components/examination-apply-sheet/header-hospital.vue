<template>
    <div class="print-medical-document-header print-medical-document-header-examination-report">
        <div class="examination-header">
            <div
                v-if="barcode"
                class="header-left"
            >
                <img
                    class="barcode-image"
                    :src="barcodeSrc()"
                />
                
                <span class="barcode-numbers">
                    <template
                        v-for="(c) in barcodeArr"
                    >{{ c }}</template>
                </span>
            </div>

            <div class="header-center">
                <div
                    class="organ-name-new"
                >
                    <template v-if="!curOrganSubtitle">
                        {{ curOrganTitle }}
                    </template>
                    <template v-else>
                        <div class="sub-title-line-height">
                            {{ curOrganTitle }}
                        </div>
                        <div class="sub-title-line-height">
                            {{ curOrganSubtitle }}
                        </div>
                    </template>
                </div>

                <div
                    class="document-type"
                    :class="curOrganSubtitle ? 'document-type-text-sub' : 'document-type-text-single'"
                >
                    {{ printTitle }}
                    <template v-if="extendSpec">
                        ({{ extendSpec }})
                    </template>
                </div>
            </div>

            <div
                v-if="headerConfig.qrcode && qrCodeSrc"
                class="right"
            >
                <img
                    class="qr-img"
                    :src="qrCodeSrc"
                    alt=""
                />
            </div>
        </div>
            
        <print-row class="base-info date-info examination">
            <div
                class="patient-info"
                style="float: left; min-width: 41.67%; max-width: 100%;"
            >
                姓名：
                <div 
                    style="
                        display: inline-block;
                        max-width: 71%;
                        overflow: hidden;
                        word-break: break-all;
                        white-space: nowrap;
                        vertical-align: bottom;
"
                >
                    {{ patientName }}
                </div>
                <span class="space">
                </span> 
                {{ patient.sex }} 
                <span class="space"></span> 
                {{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                <span class="space"></span>
            </div>

            <div
                class="patient-info"
                style="float: left; width: 33.33%;"
            >
                住院号：{{ printData.inpatientNo }}
            </div>

            <div
                class="patient-info"
                style="float: left; width: 25%;"
            >
                日期：{{ printData.diagnosedDate | parseTime('y-m-d') }}
            </div>
        </print-row>

        <print-row class="base-info has-margin-top patient-info examination">
            <print-col :span="10">
                科室：{{ printData.departmentName }}
            </print-col>

            <print-col :span="8">
                病区：{{ printData.wardName }}
            </print-col>

            <print-col :span="6">
                床号：{{ bedNo }}
            </print-col>
        </print-row>

        <print-row class="base-info patient-info examination">
            <print-col
                :span="18"
                overflow
            >
                诊断：{{ printData.diagnosis | filterDiagnose }}
                <template v-if="syndrome">
                    （{{ syndrome }}）
                </template>
            </print-col>

            <print-col :span="6">
                手机：{{ patient.mobile | filterMobileV2(headerConfig.mobile) }}
            </print-col>
        </print-row>

        <print-row class="base-info examination examination-apply-sheet-hospital-header-patient-info">
            <print-col
                v-if="headerConfig.idCard"
                :span="10"
            >
                {{ getIdCardTypeStr(patient.idCardType) }}：{{ patient.idCard }}
            </print-col>

            <print-col :span="8">
                费别：
                <template v-if="headerConfig.feeType">
                    {{ printData.healthCardPayLevel }}
                </template>
                <template v-if="headerConfig.personType">
                    {{ shebaoCardInfo && shebaoCardInfo.feeType }}
                </template>
            </print-col>

            <print-col
                :span="6"
                overflow
            >
                医保号：{{ headerConfig.socialCode ? printData.healthCardNo : '' }}
            </print-col>

            <print-col
                v-if="headerConfig.computerCode"
                style="word-break: break-all; white-space: normal;"
                :span="10"
            >
                个人编号：{{ shebaoCardInfo.personalCode || (shebaoCardInfo.extend && shebaoCardInfo.extend.personalCode) }}
            </print-col>

            <print-col
                v-if="headerConfig.fileNumber"
                :span="8"
                overflow
            >
                档案号：{{ patient.sn }}
            </print-col>
        </print-row>

        <print-row
            class="line-split"
            style="border-color: #000000;"
        ></print-row>
    </div>
</template>

<script>
    import {
        formatAge,
        formatPatientOrderNo, getIdCardTypeStr,
        getLengthWithFullCharacter,
        parseTime,
        textToBase64BarCode,
    } from '../../common/utils.js';
    import { filterMobile, filterMobileV2 } from "../../common/medical-transformat.js";
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';
    import { TITLE_MAX_LENGTH } from "../../common/constants";
    
    export default {
        name: 'MedicalDocumentHeader',
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            filterDiagnose(val) {
                if (!val) return '';
                const valArr = val.split('<br>');
                return valArr.join('');
            },
            filterMobile,
            parseTime,
            filterMobileV2,
        },
        props: {
            printData: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            printTitle: {
                type: String,
                default: '治疗执行单',
            },
            organTitle: {
                type: String,
                default: "",
            },
        },
        data() {
            return {
                curOrganTitle: '',
                curOrganSubtitle: '',
            }
        },
        computed: {
            patient() {
                return this.printData.patient || {};
            },

            patientName() {
                return this.patient.name
            },
            qrCodeSrc() {
                return this.printData.qrCode;
            },
            barcode() {
                return this.printData.barcode;
            },
            barcodeArr() {
                return (this.barcode + '').split('') || []
            },
            headerConfig() {
                return this.config.header || {};
            },
            organ() {
                return this.printData.organ || {};
            },
            syndrome() {
                return this.printData.syndrome || '';
            },
            shebaoCardInfo() {
                return this.printData.shebaoCardInfo || {};
            },
            clinicNameTitle() {
                let name = this.organ.name || '';
                name = name.length > 28 ? name.slice(0, 28) : name;
                const clinicTitle = [];
                if (name.length > 16) {
                    clinicTitle.push(name.slice(0, 16));
                    clinicTitle.push(name.slice(16, name.length));
                } else {
                    clinicTitle.push(name);
                }
                return clinicTitle;
            },
            titleAndSubtitle() {
                return {
                    organTitle: this.headerConfig.title || this.organTitle,
                    headerConfigSubtitle: this.headerConfig.subtitle,
                    organName: this.organ.name,
                };
            },
            bedNo() {
                if (!this.printData.bedNo) return '';
                return this.printData.bedNo.padStart(2, '0');
            },
        },
        watch: {
            titleAndSubtitle: {
                handler(v) {
                    const { organTitle, headerConfigSubtitle, organName } = v;
                    let title = '', subtitle = '';
                    if(!organTitle) {
                        title = organName || '';
                    } else {
                        title = organTitle || '';
                    }

                    const cacheTitle = title;
                    const {
                        fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                    } = getLengthWithFullCharacter(cacheTitle, TITLE_MAX_LENGTH);
                    if (fullClinicCharacterLength > TITLE_MAX_LENGTH) {
                        title = cacheTitle.slice(0, splitClinicLength);
                        subtitle = cacheTitle.slice(splitClinicLength);
                    }

                    this.curOrganTitle = title;
                    if (subtitle) {
                        this.curOrganSubtitle = subtitle;
                    } else {
                        this.curOrganSubtitle = headerConfigSubtitle || '';
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            formatAge,
            formatPatientOrderNo,
            getIdCardTypeStr,
            barcodeSrc() {
                return textToBase64BarCode(this.barcode);
            },
        },
    };
</script>

<style lang="scss">
@import "index.scss";

.print-medical-document-header-examination-report {
    .examination-header {
        .header-left {
            position: absolute;
            top: 0;
            left: -6pt;
            width: 90pt;

            img {
                width: 100%;
                height: 40pt;
            }

            .barcode-numbers {
                font-size: 10pt;
                line-height: 12pt;
            }
        }

        .header-center {
            width: 100%;

            .organ-name-new {
                height: 40pt;
                font-family: SimSun;
                font-size: 14pt;
                font-weight: bold;
                line-height: 40pt;
                text-align: center;

                .sub-title-line-height {
                    font-family: SimSun;
                    font-size: 14pt;
                    font-weight: bold;
                    line-height: 20pt;
                    text-align: center;
                }
            }

            .document-type {
                font-family: SimSun;

                &.document-type-text-sub {
                    font-size: 12pt;
                    line-height: 20pt;
                }

                &.document-type-text-single {
                    font-size: 14pt;
                    line-height: 20pt;
                }
            }
        }
    }
}

.base-info {
    &.examination {
        padding-bottom: 0;
        font-size: 10pt;
        font-weight: normal;
        border-color: #000000;
    }

    .patient-info {
        &.examination {
            display: inline-block;
            min-width: 40%;
            max-width: 100%;
        }
    }
}

.examination-apply-sheet-hospital-header-patient-info {
    .print-col {
        margin-bottom: 6pt;

        &:nth-child(3n+1) {
            width: 41.67% !important;
        }

        &:nth-child(3n+2) {
            width: 33.33% !important;
        }

        &:nth-child(3n+3) {
            width: 25% !important;
        }
    }
}
</style>
