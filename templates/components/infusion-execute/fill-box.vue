<template>
  <table
      class="infusion-sign-table"
      style="table-layout: fixed"
  >
    <thead>
    <tr>
      <td colspan="2" v-for="item in curTableHeader">{{item}}</td>
    </tr>
    </thead>
    <tbody data-type="group">
    <tr data-type="item" v-for="item in tableRowNumber" :key="item">
      <td colspan="2" v-for="item in curTableHeader"></td>
    </tr>
    </tbody>
  </table>
</template>
<script>
export default {
    props: {
        tableHeader: {
            type: Array,
            required: true,
        },
        tableRowNumber: {
            type: Number,
            default: 7,
        },
    },
    computed: {
        curTableHeader() {
            return this.tableHeader;
        }
    }
}
</script>
