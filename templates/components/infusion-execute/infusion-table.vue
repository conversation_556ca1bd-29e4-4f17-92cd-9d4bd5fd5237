<template>
    <table
        data-type="mix-box"
        class="infusion-table-wrapper"
    >
        <thead>
            <tr>
                <td
                    colspan="6"
                    class="title-td"
                >
                    <div style="font-size: 0;">
                        <span class="item-title-wrapper">
                            医嘱内容
                        </span>
                        <span class="dosage-unit-title">
                            单次剂量
                        </span>
                    </div>
                </td>


                <td
                    v-for="item in tableHeader"
                    class="title-td"
                    :colspan="3 / (tableHeader.length || 1)"
                    :class="{'small-td': item.length > 2}"
                >
                    {{ item }}
                </td>
            </tr>
        </thead>

        <tbody data-type="group">
            <tr
                v-for="formItem in form.executeFormItems"
                data-type="item"
            >
                <td
                    colspan="6"
                >
                    <div
                        class="product-infusion-item"
                    >
                        <div
                            v-if="formItem.groupId"
                            class="number-icon"
                        >
                            {{ formItem.groupId ? NUMBER_ICONS[ formItem.groupId ] : '-' }}
                        </div>

                        <template v-for="groupItem in formItem.groupItems">
                            <div
                                class="infusion-item"
                                :class="{'has-group-id':formItem.groupId}"
                            >
                                <div
                                    class="left-part-info"
                                    :class="{'has-ast-info': groupItem.ast}"
                                    overflow
                                >
                                    <div
                                        v-if="contentConfig.medicineTradeName"
                                        class="execute-content"
                                        overflow
                                    >
                                        {{ groupItem.name }}
                                    </div>

                                    <div
                                        v-else
                                        class="execute-content"
                                        overflow
                                    >
                                        {{ groupItem.medicineCadn || groupItem.name }}
                                    </div>
                                    <div
                                        v-if="contentConfig.westernMedicineSpec || contentConfig.medicineSpec || contentConfig.medicineTotalCount"
                                        class="group-spec"
                                    >
                                        (<span
                                            v-if="(contentConfig.westernMedicineSpec || contentConfig.medicineSpec) && groupItem.productInfo"
                                        >{{ groupItem.productInfo | formatGoodsDosageSpec }}</span>
                                        <span
                                            v-if="contentConfig.medicineTotalCount"
                                        ><template v-if="showXIcon(groupItem)">×</template>{{ groupItem.unitCount }}{{ groupItem.unit }}</span>)
                                    </div>
                                </div>
                                <div
                                    v-if="groupItem.ast"
                                    class="ast-part-info"
                                >
                                    {{ groupItem.ast | formatAst }}
                                    <template v-if="groupItem.ast === 1">
                                        ({{ groupItem.astResult | formatAstResult }})
                                    </template>
                                </div>
                                <div
                                    class="right-part-info"
                                    :class="{'has-ast-info': groupItem.ast}"
                                >
                                    {{ groupItem.dosage }}{{ groupItem.dosageUnit }}
                                </div>
                                <div
                                    v-if="groupItem.specialRequirement "
                                    class="remark"
                                >
                                    {{ groupItem.specialRequirement }}
                                </div>
                            </div>
                            <div
                                v-if="contentConfig.manufacturer && groupItem.productInfo && groupItem.productInfo.manufacturer"
                                class="infusion-item"
                                style="font-size: 10pt !important; line-height: 12pt !important;"
                                :style="{ paddingLeft: formItem.groupId ? '36pt' : '20pt' }"
                            >
                                厂家：{{ groupItem.productInfo.manufacturer }}
                            </div>
                        </template>
                        <div
                            class="usage-info-text"
                            :class="{'has-group-id':formItem.groupId}"
                        >
                            {{ usageFormat( formItem.usage, contentConfig.medicalLatin ) }}&nbsp;
                            {{ freqFormat( formItem.freq, contentConfig.medicalLatin ) }}&nbsp;
                            {{ formItem.days }}天
                            <template v-if="formItem.ivgtt">
                                {{ formItem.ivgtt }}
                                <template v-if="formItem.ivgttUnit">
                                    {{ contentConfig.medicalLatin ? 'd/min' : formItem.ivgttUnit }}
                                </template>
                            </template>
                        </div>
                    </div>
                </td>
                <td
                    v-for="item in tableHeader"
                    :colspan="3 / (tableHeader.length || 1)"
                ></td>
            </tr>
        </tbody>
    </table>
</template>

<script>
    import {
        transIvgttUnit,
        usageFormat,
        freqFormat,
        formatAst,
        formatAstResult,
    } from "../../common/medical-transformat.js";
    import { formatGoodsDosageSpec } from "../../common/utils.js";
    import { NUMBER_ICONS } from "../../common/constants.js";

    export default {
        name: "InfusionTable",
        filters: {
            formatAst,
            formatAstResult,
            formatGoodsDosageSpec,
        },
        props: {
            form: {
                type: Object,
                required: true,
            },
            contentConfig: {
                type: Object,
                required: true,
            },
            isHospital: Boolean,
        },
        data() {
            return {
                NUMBER_ICONS,
            }
        },
        computed: {
            tableHeader() {
                return this.isHospital ? this.contentConfig.hospitalRightTableHeader : this.contentConfig.rightHeader;
            },
        },
        methods: {
            transIvgttUnit,
            usageFormat,
            freqFormat,
            showXIcon(formItem) {
                const spec = formatGoodsDosageSpec(formItem.productInfo);
                return spec && (this.contentConfig.westernMedicineSpec || this.contentConfig.medicineSpec)
            },
        },
    }
</script>

<style lang="scss">
.infusion-table-wrapper {
    width: 100%;
    margin-top: 6pt;
    overflow: hidden;
    font-size: 10pt;
    font-weight: 300;
    table-layout: fixed;

    .title-td {
        padding: 4pt 6pt;
    }

    td.small-td {
        font-size: 9pt;
        line-height: 11pt;
    }

    td {
        padding: 6pt;
        overflow: hidden;
        border: 1px solid #000000;
    }

    .infusion-item {
        box-sizing: border-box;
        width: 100%;
        font-size: 0;
    }

    .remark {
        padding-top: 2pt;
        padding-left: 10pt;
        font-size: 10pt;
        line-height: 12pt;
    }

    .usage-info-text {
        padding-bottom: 6pt;
        font-size: 10pt;

        &.has-group-id {
            padding-left: 16pt;
        }
    }

    .right-part-info,
    .ast-part-info,
    .dosage-unit-title,
    .item-title-wrapper,
    .left-part-info {
        box-sizing: border-box;
        display: inline-block;
        font-size: 10pt;
        line-height: 12pt;
        vertical-align: middle;
    }

    .left-part-info {
        width: 76%;
        max-width: 76%;

        &.has-ast-info {
            width: 66%;
            max-width: 66%;
        }
    }

    .right-part-info {
        width: 23%;
        max-width: 23%;
        text-align: right;

        &.has-ast-info {
            width: 23%;
            max-width: 23%;
        }
    }

    .ast-part-info {
        width: 10%;
        max-width: 10%;
    }

    .item-title-wrapper {
        width: 70%;
        max-width: 70%;
    }

    .dosage-unit-title {
        width: 30%;
        max-width: 30%;
        font-size: 10pt;
        text-align: right;
    }

    .product-infusion-item {
        position: relative;
        box-sizing: border-box;
        display: inline-block;
        width: 100%;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        vertical-align: middle;

        .number-icon {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            display: inline-block;
            width: 16pt;
            font-size: 10pt;
            line-height: 12pt;
            vertical-align: middle;
        }

        .infusion-item {
            padding-bottom: 6pt;

            &.has-group-id {
                padding-left: 16pt;
            }

            &:last-child {
                padding-bottom: 0;
            }
        }
    }

    .execute-content {
        display: inline-block;
        max-width: 62%;
        overflow: hidden;
        font-size: 10pt;
        line-height: 12pt;
        word-break: keep-all;
        white-space: nowrap;
        vertical-align: middle;
    }

    .group-spec {
        display: inline-block;
        vertical-align: middle;
        //min-width: 37%;
        //max-width: 37%;
    }
}

</style>
