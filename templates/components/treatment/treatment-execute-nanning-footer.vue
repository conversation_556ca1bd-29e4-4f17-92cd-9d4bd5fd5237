<template>
    <div class="treatment-execute-nanning-footer">
        <div class="treatment-execute-nanning-doctor">
            <div>医生：</div>
            <div
                v-if="isImgUrl(printData.doctorSignImgUrl)"
                class="treatment-execute-nanning-doctor-img-wrapper"
            >
                <img
                    :src="printData.doctorSignImgUrl"
                    class="treatment-execute-nanning-doctor-img"
                    alt=""
                />
            </div>
            <div v-else-if="printData.doctorSignImgUrl">
                {{ printData.doctorSignImgUrl }}
            </div>
            <div v-else-if="printData.doctorName">
                {{ printData.doctorName }}
            </div>
            <div
                v-else
                style="width: 82px;"
            ></div>
        </div>

        <spacing-line line-type="solid"></spacing-line>
        
        <div class="treatment-execute-nanning-document-info">
            <div>
                打印时间：{{ new Date() | formatDate('YYYY-MM-DD HH:mm:ss') }}
            </div>
            <div>
                金额：{{ printData.totalPrice | formatMoney }}
            </div>
        </div>

        <div
            v-if="remarks && remarks.length"
            class="treatment-execute-nanning-remark"
        >
            <div
                v-for="(remark, idx) in remarks"
                :key="`treatment-execute-nanning-remark-${idx}`"
            >
                {{ remark }}
            </div>
        </div>
    </div>
</template>

<script>
    import {formatMoney, isImgUrl} from '../../common/utils';
    import SpacingLine from '../medical-document-header/spacing-line.vue';
    import {formatDate} from '@tool/date';

    export default {
        name: 'TreatmentExecuteNanningFooter',
        components: {SpacingLine},
        filters: {
            formatMoney,
            formatDate,
        },
        props: {
            printData: {
                type: Object,
                default() {
                    return {};
                },
                require: true,
            },
            config: {
                type: Object,
                default() {
                    return {};
                },
                require: true,
            },
        },
        computed: {
            footerConfig() {
                return this.config.footer || {};
            },
            remarks() {
                if (!this.footerConfig.remark) return [];
                return this.footerConfig.remark.split('\n');
            },
        },
        methods: {
            isImgUrl,
        },
    }
</script>

<style lang="scss">
.treatment-execute-nanning-footer {
    font-size: 14px;

    .treatment-execute-nanning-doctor {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
    }

    .treatment-execute-nanning-doctor-img-wrapper {
        display: flex;
        align-items: flex-end;
        width: 82px;
        height: 32px;
    }

    .treatment-execute-nanning-doctor-img {
        max-width: 100%;
        max-height: 100%;
    }

    .treatment-execute-nanning-document-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .treatment-execute-nanning-remark {
        display: flex;
        flex-direction: column;
        font-size: 10px;
        line-height: 13px;
    }
}
</style>
