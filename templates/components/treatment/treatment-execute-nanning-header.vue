<template>
    <div class="treatment-execute-nanning-header">
        <!-- logo 或者 就诊条码 -->
        <div
            v-if="(headerConfig.barcode && barcodeSrc) || (headerConfig.logo && organ.logo)"
            class="treatment-execute-nanning-logo"
        >
            <img
                v-if="headerConfig.barcode && barcodeSrc"
                :src="barcodeSrc"
                alt=""
                class="treatment-execute-nanning-bar-code"
            />
            <img
                v-else
                :src="organ.logo"
                alt=""
                class="treatment-execute-nanning-img"
            />
        </div>
        
        <!-- 微诊所二维码 -->
        <div
            v-if="headerConfig.qrcode && qrCodeSrc"
            class="treatment-execute-nanning-qrcode"
        >
            <img
                :src="qrCodeSrc"
                alt=""
                class="treatment-execute-nanning-qrcode-img"
            />
        </div>
        
        <!-- 标题 -->
        <div class="treatment-execute-nanning-title-wrapper">
            <div>
                {{ title }}
            </div>
            <div v-if="subTitle">
                {{ subTitle }}
            </div>
        </div>
        
        <!-- 类型 -->
        <div class="treatment-execute-nanning-type">
            {{ printTitle }}
        </div>
        
        <div class="treatment-execute-nanning-patient-wrapper">
            <div class="treatment-execute-nanning-patient-info">
                <div>费别：</div>
                <div class="treatment-execute-nanning-fee-type">
                    <div>自费</div>
                    <div>离休</div>
                    <div>二乙</div>
                    <div>医保（自治区、市、县）</div>
                </div>
                <div>请对号划✓</div>
            </div>

            <div class="treatment-execute-nanning-patient-info">
                <div class="treatment-execute-nanning-patient">
                    <div>姓名：</div>
                    <div class="treatment-execute-nanning-patient-item">
                        {{ patient.name }}
                    </div>
                </div>
                <div class="treatment-execute-nanning-patient">
                    <div>性别：</div>
                    <div class="treatment-execute-nanning-patient-item">
                        {{ patient.sex }}
                    </div>
                </div>
                <div class="treatment-execute-nanning-patient">
                    <div>年龄：</div>
                    <div class="treatment-execute-nanning-patient-item">
                        {{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                    </div>
                </div>
            </div>

            <div class="treatment-execute-nanning-patient-info">
                <div class="treatment-execute-nanning-patient-no">
                    <div>门诊/住院病历号：</div>
                    <div class="treatment-execute-nanning-patient-item">
                        {{ formatPatientOrderNo(printData.patientOrderNo) }}
                    </div>
                </div>
                <div class="treatment-execute-nanning-department">
                    <div>科室：</div>
                    <div class="treatment-execute-nanning-patient-item">
                        {{ printData.departmentName || '' }}
                    </div>
                </div>
            </div>

            <div class="treatment-execute-nanning-patient-info">
                <div class="treatment-execute-nanning-diagnosis">
                    <div>临床（初步）诊断：</div>
                    <div class="treatment-execute-nanning-patient-item">
                        <template v-if="printData.diagnosis">
                            {{ printData.diagnosis | filterDiagnose }}
                        </template>
                        <template v-if="printData.syndrome">
                            （{{ printData.syndrome }}）
                        </template>
                    </div>
                </div>
            </div>

            <div class="treatment-execute-nanning-patient-info">
                <div class="treatment-execute-nanning-patient-no">
                    <div>开具日期：</div>
                    <div class="treatment-execute-nanning-patient-item">
                        {{ diagnosedDate }}
                    </div>
                </div>
                <div class="treatment-execute-nanning-department">
                    <div>体温：</div>
                    <div class="treatment-execute-nanning-patient-item"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {formatAge, formatPatientOrderNo, getLengthWithFullCharacter, textToBase64BarCode} from '../../common/utils';
    import clone from '../../common/clone';
    import {TITLE_MAX_LENGTH} from '../../common/constants';
    import {filterDiagnose} from '../../common/medical-transformat';
    import {formatDate} from '@tool/date';

    export default {
        name: 'TreatmentExecuteNanningHeader',
        filters: {
            filterDiagnose,
        },
        props: {
            printData: {
                type: Object,
                default() {
                    return {};
                },
                require: true,
            },
            config: {
                type: Object,
                default() {
                    return {};
                },
                require: true,
            },
            printTitle: {
                type: String,
                default: '治疗执行单',
            },
        },
        computed: {
            headerConfig() {
                return this.config.header || {};
            },
            barcodeSrc() {
                const barcode = this.printData.patientOrderNo ? `${this.printData.patientOrderNo}`.padStart(8, '0') : '';
                return textToBase64BarCode(barcode);
            },
            organ() {
                return this.printData.organ || {};
            },
            qrCodeSrc() {
                return this.printData.qrCode;
            },
            title() {
                if (this.headerConfig.title) return this.headerConfig.title;
                return this.formatTitle(this.organ.name);
            },
            subTitle() {
                if (this.headerConfig.title) return this.headerConfig.subtitle || '';
                return this.formatSubtitle(this.organ.name);
            },
            patient() {
                return this.printData.patient || {};
            },
            diagnosedDate() {
                if (this.printData.diagnosedDate) return formatDate(this.printData.diagnosedDate, 'YYYY[年]MM[月]DD[日]');
                return formatDate(new Date(), 'YYYY[年]MM[月]DD[日]');
            },
        },
        watch: {
            printData: {
                handler(v) {
                    console.log('%c printData\n', 'background: green; padding: 0 5px', clone(v));
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            formatAge,
            formatPatientOrderNo,
            formatTitle(title) {
                let res = title;
                const {
                    fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                } = getLengthWithFullCharacter(title, TITLE_MAX_LENGTH);
                if (fullClinicCharacterLength > TITLE_MAX_LENGTH) {
                    res = title.slice(0, splitClinicLength);
                }
                return res;
            },
            formatSubtitle(title) {
                let res = title;
                const {
                    fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                } = getLengthWithFullCharacter(title, TITLE_MAX_LENGTH);
                if (fullClinicCharacterLength > TITLE_MAX_LENGTH) {
                    res = title.slice(splitClinicLength);
                }
                return res;
            },
        },
    }
</script>

<style lang="scss">
.treatment-execute-nanning-header {
    position: relative;

    .treatment-execute-nanning-logo {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        width: 120px;
        height: 54px;
    }

    .treatment-execute-nanning-img {
        max-width: 100%;
        max-height: 100%;
    }

    .treatment-execute-nanning-bar-code {
        width: 100%;
        height: 32px;
    }

    .treatment-execute-nanning-qrcode {
        position: absolute;
        top: 0;
        right: 0;
        width: 66px;
        height: 66px;
    }

    .treatment-execute-nanning-qrcode-img {
        width: 66px;
        height: 66px;
    }

    .treatment-execute-nanning-title-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 54px;
        font-family: SimSun;
        font-size: 20px;
        font-weight: bold;
    }

    .treatment-execute-nanning-type {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-bottom: 13px;
        font-family: SimSun;
        font-size: 17px;
    }

    .treatment-execute-nanning-patient-wrapper {
        display: flex;
        flex-direction: column;
        gap: 4px;
        font-size: 14px;
    }

    .treatment-execute-nanning-patient-info {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .treatment-execute-nanning-fee-type {
        display: flex;
        flex: 1;
        gap: 22px;
        align-items: center;
        padding-left: 8px;
        border-bottom: 1px solid #a6a6a6;
    }

    .treatment-execute-nanning-patient {
        display: flex;
        align-items: center;
        width: 33%;
    }

    .treatment-execute-nanning-patient-item {
        flex: 1;
        height: 20px;
        padding-left: 8px;
        border-bottom: 1px solid #a6a6a6;
    }

    .treatment-execute-nanning-patient-no {
        display: flex;
        align-items: center;
        width: 66%;
    }

    .treatment-execute-nanning-department {
        display: flex;
        align-items: center;
        width: 33%;
    }

    .treatment-execute-nanning-diagnosis {
        display: flex;
        flex: 1;
        align-items: center;
    }
}
</style>
