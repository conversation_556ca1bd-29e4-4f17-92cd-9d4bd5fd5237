<template>
    <span>
        <template v-if="isTextAbnormal">
            <span style="color: #ff3366">
                {{ item.abnormalText }}
            </span>
        </template>

        <template v-else-if="abnormalFlagDisplayConfig.isUp">
            <span
                v-for="n in abnormalFlagDisplayConfig.upCount"
                :key="n"
                class="result-icon up"
                :style="{
                    color: needColor ? '#FF1818' : 'inherit',
                }"
            >
                ↑
            </span>
        </template>

        <template v-else-if="abnormalFlagDisplayConfig.isDown">
            <span
                v-for="n in abnormalFlagDisplayConfig.lowCount"
                :key="n"
                class="result-icon down"
                :style="{
                    color: needColor ? '#239CF4' : 'inherit',
                }"
            >
                ↓
            </span>
        </template>

        <span
            v-else-if="abnormalFlagDisplayConfig.isBad"
            class="result-icon"
        >*</span>
    </span>
</template>

<script>
    import { checkExaminationResultIsAbnormal } from '../../common/utils'

    export default {
        name: 'NatureDisplay',
        props: {
            item: {
                type: Object,
                required: true,
                default: () => {},
            },
            result: {
                type: [undefined, Object, String, Number],
                default: undefined,
            },
            needColor: Boolean,
        },
        computed: {
            abnormalFlagDisplayConfig() {
                return checkExaminationResultIsAbnormal(this.item, this.result);
            },

            isTextAbnormal() {
                return this.item.abnormalFlag === 'TA';
            },
        },
    };
</script>
<style lang="scss">
.result-icon{
    font-size: 10pt;
    font-weight: bold;

    //width: 12pt;
    //height: 12pt;
    //display: inline-block;

    //&.up {
    //    background: url("/static/assets/print/up-arrow.png") no-repeat center;
    //    background-size: cover;
    //}
    //&.down {
    //    background: url("/static/assets/print/down-arrow.png") no-repeat center;
    //    background-size: cover;
    //}
}
</style>