<template>
    <div data-type="group">
        <div v-if="config === 0 && totalFee">
            <print-row

                data-type="item"
            >
                <print-col
                    :span="12"
                    class="text-info"
                >
                    {{ totalTitle }}
                </print-col>
                <print-col
                    :span="12"
                    class="text-right text-info"
                >
                    {{ totalFee | formatMoney }}
                </print-col>
            </print-row>
            <div
                v-if="formSplitLine"
                class="print-split-line"
            ></div>
        </div>

        <div
            v-if="config === 1"
        >
            <div
                v-for="(form, index) in forms"
                :key="`${form.id + index }`"
                class="overflow-hidden-item"
            >
                <template v-for="(formItem, tIndex) in form.chargeFormItems">
                    <div v-if="formItem.sourceItemType !== 1">
                        <print-row
                            :key="`${formItem.id + tIndex }`"
                            data-type="item"
                        >
                            <print-col
                                :overflow="!isAutoChangeLine"
                                :span="18"
                                class="text-info"
                                :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                            >
                                {{ displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio) }}{{ formItem.medicineCadn || formItem.name }}
                            </print-col>
                            <print-col
                                :overflow="!isAutoChangeLine"
                                :span="6"
                                class="text-right text-info"
                                :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                            >
                                {{ formItem.count }}{{ getFormItemUnit(formItem) }}
                            </print-col>
                        </print-row>
                        <print-row
                            v-if="showSpec || showMedPositon"
                            data-type="item"
                            class="child-row"
                        >
                            <print-col
                                v-if="showSpec"
                                :span="12"
                                overflow
                                class="text-info"
                            >
                                <span>{{ formItem.displaySpec }}</span>
                            </print-col>
                            <print-col
                                v-if="showMedPositon"
                                :span="12"
                                overflow
                            >
                                {{ formItem.position }}
                            </print-col>
                        </print-row>

                        <!-- 厂家信息 -->
                        <print-row
                            v-if="showManufacture"
                            class="child-row"
                        >
                            <print-col
                                :span="24"
                                overflow
                            >
                                厂家：{{ (formItem.productInfo && formItem.productInfo.manufacturer) || '' }}
                            </print-col>
                        </print-row>
                        <print-row
                            v-if="formItem.goodsStockInfos && showDetail"
                            :key="`${tIndex }child`"
                            class="child-row-wrapper"
                        >
                            <template v-if="isOptimization">
                                <template v-for="(stock, sIndex) in formItem.goodsStockInfos">
                                    <print-row
                                        :key="`${sIndex}-1`"
                                        data-type="item"
                                        class="child-row"
                                    >
                                        <print-col
                                            v-if="showBatchNumber"
                                            class="text-info"
                                            overflow
                                            :span="24"
                                        >
                                            批号：{{ stock.batchNo }}
                                        </print-col>
                                    </print-row>
                                    <print-row
                                        :key="`${sIndex}-2`"
                                        data-type="item"
                                        class="child-row"
                                    >
                                        <print-col
                                            v-if="showValidDate"
                                            class="text-info"
                                            overflow
                                            :span="24"
                                        >
                                            效期：{{ stock.expiryDate }}
                                        </print-col>
                                    </print-row>
                                </template>
                            </template>

                            <template v-else>
                                <print-row
                                    v-for="(stock, sIndex) in formItem.goodsStockInfos"
                                    :key="sIndex"
                                    data-type="item"
                                    class="child-row"
                                >
                                    <print-col
                                        v-if="showBatchNumber"
                                        class="text-info"
                                        overflow
                                        :span="12"
                                    >
                                        批号：{{ stock.batchNo }}
                                    </print-col>
                                    <print-col
                                        v-if="showValidDate"
                                        class="text-info"
                                        overflow
                                        :span="12"
                                    >
                                        效期：{{ stock.expiryDate }}
                                    </print-col>
                                </print-row>
                            </template>
                        </print-row>

                        <print-row
                            v-if="showChildren"
                            :key="`${tIndex }compose-child`"
                            class="compose-child-wrapper"
                        >
                            <print-row
                                v-for="(child, cIndex) in formItem.composeChildren"
                                :key="cIndex"
                                data-type="item"
                            >
                                <print-col
                                    :span="18"
                                    class="child-name text-info"
                                    :overflow="!isAutoChangeLine"
                                    :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                                >
                                    {{ child.name }}
                                </print-col>
                                <print-col
                                    :span="6"
                                    class="text-right text-info"
                                    style="position: relative;"
                                    :overflow="!isAutoChangeLine"
                                    :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                                >
                                    {{ child.count }}{{ getFormItemUnit(child) }}
                                </print-col>
                            </print-row>
                        </print-row>
                    </div>
                </template>

                <div
                    v-if="formSplitLine"
                    class="print-split-line"
                ></div>
            </div>
        </div>
        <div v-if="config === 2">
            <div
                v-for="(form, index) in forms"
                :key="`${form.id + index }`"
                class="overflow-hidden-item"
            >
                <template v-for="(formItem, tIndex) in form.chargeFormItems">
                    <div v-if="formItem.sourceItemType !== 1">
                        <template v-if="shouldOptimization">
                            <print-row
                                :key="`${formItem.id + tIndex }`"
                                data-type="item"
                                class="item-row two-item-row"
                            >
                                <print-col
                                    :span="24"
                                    class="text-info"
                                    :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                                    :overflow="!isAutoChangeLine"
                                    multiline
                                >
                                    <!-- 医保等级为 4 的不展示 -->
                                    <!-- 深圳地区才会返回 4 -->
                                    {{ displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio) }}{{ formItem.name }}
                                </print-col>
                            </print-row>
                            <print-row
                                v-if="showSpec"
                                data-type="item"
                                overflow
                                class="child-row"
                            >
                                <print-col
                                    :span="24"
                                    overflow
                                >
                                    {{ formItem.displaySpec }}
                                </print-col>
                            </print-row>
                            <print-row
                                :key="`${formItem.id + tIndex}-2`"
                                data-type="item"
                                class="item-row two-item-row"
                            >
                                <print-col
                                    :span="9"
                                    overflow
                                    class="text-info child-row"
                                >
                                    {{ formItem.unitPrice | formatMoney }}
                                </print-col>
                                <print-col
                                    :span="6"
                                    :multiline="!isAutoChangeLine"
                                    class="text-right text-info"
                                    :overflow="!isAutoChangeLine"
                                    :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                                >
                                    {{ formItem.count }}{{ getFormItemUnit(formItem) }}
                                </print-col>
                                <print-col
                                    :span="9"
                                    overflow
                                    class="text-right text-info"
                                >
                                    {{ formItem.totalPrice | formatMoney }}
                                </print-col>
                            </print-row>
                            <print-row
                                v-if="showMedPositon"
                                data-type="item"
                                overflow
                                class="child-row"
                            >
                                <print-col
                                    v-if="isWestern"
                                    :span="24"
                                    overflow
                                >
                                    {{ formItem.position }}
                                </print-col>
                            </print-row>
                        </template>
                        <template v-else>
                            <print-row
                                :key="`${formItem.id + tIndex }`"
                                data-type="item"
                                class="item-row two-item-row"
                            >
                                <print-col
                                    :span="!isFeeCompose ? 8 : 14"
                                    class="text-info"
                                    :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                                    :overflow="!isAutoChangeLine"
                                    multiline
                                >
                                    <!-- 医保等级为 4 的不展示 -->
                                    <!-- 深圳地区才会返回 4 -->
                                    {{ displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio) }}{{ formItem.name }}
                                </print-col>
                                <print-col
                                    v-if="!isFeeCompose"
                                    :span="6"
                                    overflow
                                    class="text-right text-info"
                                >
                                    {{ formItem.unitPrice | formatMoney }}
                                </print-col>
                                <print-col
                                    :span="4"
                                    class="text-right text-info"
                                    :overflow="!isAutoChangeLine"
                                    :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                                >
                                    {{ formItem.count }}{{ getUnit(form, formItem) }}
                                </print-col>
                                <print-col
                                    :span="6"
                                    overflow
                                    class="text-right text-info"
                                >
                                    {{ formItem.totalPrice | formatMoney }}
                                </print-col>
                            </print-row>
                            <print-row>
                                <print-col
                                    v-if="!isWestern && showMedPositon && formItem.position"
                                    :span="8"
                                    class=" text-info child-row"
                                >
                                    {{
                                        formItem.position
                                    }}
                                </print-col>
                            </print-row>
                            <print-row
                                v-if="showSpec || showMedPositon"
                                data-type="item"
                                overflow
                                class="child-row"
                            >
                                <print-col
                                    :span="12"
                                    overflow
                                >
                                    <span v-if="showSpec">{{ formItem.displaySpec }}</span>
                                </print-col>
                                <print-col
                                    v-if="isWestern && showMedPositon"
                                    :span="12"
                                    overflow
                                >
                                    {{ formItem.position }}
                                </print-col>
                            </print-row>
                        </template>
                        <!-- 批号厂家信息 -->
                        <print-row
                            v-if="showManufacture"
                            class="child-row"
                        >
                            <print-col
                                :span="24"
                                overflow
                            >
                                厂家：{{ (formItem.productInfo && formItem.productInfo.manufacturer) || '' }}
                            </print-col>
                        </print-row>
                        <print-row
                            v-if="formItem.goodsStockInfos && showDetail"
                            :key="`${tIndex }child`"
                            class="child-row-wrapper"
                        >
                            <template v-if="isOptimization">
                                <template v-for="(stock, sIndex) in formItem.goodsStockInfos">
                                    <print-row
                                        :key="`${sIndex}-1`"
                                        data-type="item"
                                        class="item-row child-row"
                                        :gutter="2"
                                    >
                                        <print-col
                                            v-if="showBatchNumber"
                                            :span="24"
                                            overflow
                                            class="text-info"
                                        >
                                            批号：{{ stock.batchNo }}
                                        </print-col>
                                    </print-row>
                                    <print-row
                                        :key="`${sIndex}-2`"
                                        data-type="item"
                                        class="item-row child-row"
                                        :gutter="2"
                                    >
                                        <print-col
                                            v-if="showValidDate"
                                            :span="24"
                                            class="text-info"
                                            overflow
                                        >
                                            效期：{{ stock.expiryDate }}
                                        </print-col>
                                    </print-row>
                                </template>
                            </template>

                            <template v-else>
                                <print-row
                                    v-for="(stock, sIndex) in formItem.goodsStockInfos"
                                    :key="sIndex"
                                    data-type="item"
                                    class="item-row child-row"
                                    :gutter="2"
                                >
                                    <print-col
                                        v-if="showBatchNumber"
                                        :span="12"
                                        overflow
                                        class="text-info"
                                    >
                                        批号：{{ stock.batchNo }}
                                    </print-col>
                                    <print-col
                                        v-if="showValidDate"
                                        :span="12"
                                        class="text-info"
                                        overflow
                                    >
                                        效期：{{ stock.expiryDate }}
                                    </print-col>
                                </print-row>
                            </template>
                        </print-row>

                        <!-- 套餐子项信息 -->
                        <print-row
                            v-if="showChildren === 2"
                            :key="`${tIndex }compose-child`"
                            class="compose-child-wrapper"
                        >
                            <print-row
                                v-for="(child, cIndex) in formItem.composeChildren"
                                :key="cIndex"
                                data-type="item"
                                class="item-row compose-child two-item-row"
                            >
                                <print-col
                                    :span="!isFeeCompose ? 9 : 14"
                                    class="child-name text-info"
                                    :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                                    :overflow="!isAutoChangeLine"
                                    :multiline="!isAutoChangeLine"
                                >
                                    {{ child.name }}
                                </print-col>
                                <print-col
                                    v-if="!isFeeCompose"
                                    :span="5"
                                    class="text-right text-info"
                                    overflow
                                >
                                    {{ child.unitPrice | formatMoney }}
                                </print-col>
                                <print-col
                                    :span="4"
                                    class="text-right text-info"
                                    style="position: relative;"
                                    :overflow="!isAutoChangeLine"
                                    :class="{'word-break': isAutoChangeLine, 'no-change': !isAutoChangeLine}"
                                >
                                    {{ child.count }}{{ getFormItemUnit(child) }}
                                </print-col>
                                <print-col
                                    :span="6"
                                    class="text-right text-info"
                                    overflow
                                >
                                    {{ child.totalPrice | formatMoney }}
                                </print-col>
                            </print-row>
                        </print-row>
                    </div>
                </template>
                <div
                    v-if="formSplitLine"
                    class="print-split-line"
                ></div>
            </div>
        </div>
    </div>
</template>

<script>
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';
    import {
        displayMedicalFeeGradeText,
        filterOwnExpenseRatio,
        formatMoney,
        formatTreatmentUnit,
        medicalFeeGrade2PrintStr,
    } from '../../common/utils.js';
    import { GoodsTypeEnum, MedicalFeeGradeEnum, SourceFormTypeEnum } from "../../common/constants.js";

    export default {
        name: 'PrintChargeForm',
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            formatMoney,
            medicalFeeGrade2PrintStr,
        },
        props: {
            forms: {
                type: [Array, Object],
                required: true,
            },
            config: {
                type: Number,
                required: true,
                default: 2,
            },
            totalFee: {
                type: Number,
            },
            totalTitle: {
                type: String,
            },
            shebaoConfig: {
                type: Object,
            },
            showChildren: {
                type: Number,
            },
            showBatchNumber: {
                type: Number,
            },
            showValidDate: {
                type: Number,
            },
            showManufacture: {
                type: Number,
            },
            showSpec: {
                type: Number,
                default: 0,
            },
            showMedPositon: {
                type: Number,
            },
            formSplitLine: {
                type: Boolean,
                default: false,
            },
            isWestern: {
                type: Boolean,
                default: false,
            },
            autoChangeLine: {
                type: [Boolean, Number],
                default: false,
            },
            isFeeCompose: {
                type: Boolean,
                default: false,
            },
            shouldOptimization: {
                type: Boolean,
                default: false,
            },
            isOptimization: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                MedicalFeeGradeEnum,
            };
        },
        computed: {
            healthCardInfo() {
                return this.shebaoConfig || {};
            },
            showDetail() {
                return this.showBatchNumber || this.showValidDate;
            },
            isAutoChangeLine() {
                return this.autoChangeLine
            },
        },
        methods: {
            filterOwnExpenseRatio,
            displayMedicalFeeGradeText,
            getUnit(form, formItem) {
                return (form.sourceFormType === SourceFormTypeEnum.TREATMENT ||
                    form.sourceFormType === SourceFormTypeEnum.EXAMINATION ||
                    form.sourceFormType === SourceFormTypeEnum.OTHER ||
                    form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL) ?
                    formItem.unit ?
                        formatTreatmentUnit(formItem.unit, '*') :
                        '次' :
                    formItem.unit;
            },
            getFormItemUnit(formItem) {
                return (formItem.productType === GoodsTypeEnum.TREATMENT ||
                    formItem.productType === GoodsTypeEnum.EXAMINATION ||
                    formItem.productType === GoodsTypeEnum.OTHER) ?
                    formItem.unit ?
                        formatTreatmentUnit(formItem.unit, '*') :
                        '次' :
                    formItem.unit;
            },
        },
    };
</script>
