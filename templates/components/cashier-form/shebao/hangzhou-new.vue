<template>
    <div
        data-type="group"
        class="hangzhou-pay-info pay-info-group"
    >
        <template v-if="shebaoConfig.cardInfo">
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                医保号：{{ shebaoPayment.cardId }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                人员编号：{{ extraInfo.psnNo }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                持卡人：{{ shebaoPayment.cardOwner }}
            </div>
        </template>
        
        <template v-if="shebaoConfig.settlementInfo">
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                基金支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                现金支付：{{ extraInfo.cashPayment | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                自付：{{ shebaoPayment.selfPaymentFee | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                自费：{{ extraInfo.fulamtOwnpayAmt | formatMoney }}
            </div>
        </template>
        <template v-if="shebaoConfig.balanceInfo">
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                本年支付：{{ extraInfo.curYearAccountPaymentFee | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                本年余额：{{ extraInfo.curYearBalance | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                历年支付：{{ extraInfo.allYearAccountPaymentFee | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                历年余额：{{ extraInfo.allYearBalance | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                起付线：本次{{ extraInfo.actPayDedc | formatMoney }}, 累计{{ extraInfo.beforeDedcCumulative | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                年度累计金额：{{ extraInfo.annualCumulative | formatMoney }}
            </div>
        </template>
    </div>
</template>

<script>
    import {formatMoney} from "../../../common/utils";

    export default {
        name: 'HangzhouNew',
        filters: {
            formatMoney,
        },
        props: {
            shebaoPayment: {
                type: Object,
                required: true,
            },
            shebaoConfig: {
                type: Object,
                required: true,
            }
        },
        computed: {
            extraInfo() {
                return this.shebaoPayment.extraInfo || {};
            },
        },
    }
</script>

<style lang="scss">
.hangzhou-pay-info {
    &.pay-info-group {
        width: 100%;
        font-size: 0;
    }

    .pay-text-info {
        display: inline-block;
        width: 33%;
        font-size: 8pt;
        line-height: 10pt;
    }

    .no-change-line {
        overflow: hidden;
        text-overflow: clip;
        white-space: nowrap;
    }
}
</style>