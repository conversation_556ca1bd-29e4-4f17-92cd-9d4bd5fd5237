<template>
    <div>
        <print-row v-if="shebaoConfig.cardInfo">
            <print-col
                :span="24"
                class="text-info"
            >
                医保号：{{ shebaoPayment.cardId }}
            </print-col>
            <print-col
                :span="24"
                class="text-info"
            >
                人员编号：{{ extraInfo.psnNo }}
            </print-col>
            <print-col
                :span="14"
                class="text-info"
            >
                持卡人：{{ shebaoPayment.cardOwner }}
            </print-col>
            <print-col
                :span="10"
                class="text-info"
            >
                关系：{{ shebaoPayment.relationToPatient }}
            </print-col>
        </print-row>
        <print-row v-if="shebaoConfig.settlementInfo">
            <print-col
                v-if="!isEmpty(shebaoPayment.accountPaymentFee) || isShowZeroSettlementInfo"
                :span="24"
            >
                账户支付：{{ shebaoPayment.accountPaymentFee | formatMoney }}
                <template v-if="!isEmpty(extraInfo.acctMulaidPay) || isShowZeroSettlementInfo">
                    (共济支付: {{ extraInfo.acctMulaidPay | formatMoney }})
                </template>
            </print-col>
            <print-col
                v-if="!isEmpty(extraInfo.psnCashPay) || isShowZeroSettlementInfo"
                :span="24"
            >
                现金支付：{{ extraInfo.psnCashPay | formatMoney }}
            </print-col>
            <print-col
                v-if="!isEmpty(extraInfo.hifpPay) || isShowZeroSettlementInfo"
                :span="24"
            >
                基本医疗基金支出：{{ extraInfo.hifpPay | formatMoney }}
            </print-col>
            <print-col
                v-if="!isEmpty(extraInfo.hifobPay) || isShowZeroSettlementInfo"
                :span="24"
            >
                大额医疗基金支出：{{ extraInfo.hifobPay | formatMoney }}
            </print-col>
            <print-col
                v-if="!isEmpty(extraInfo.cvlservPay) || isShowZeroSettlementInfo"
                :span="24"
            >
                公务员补助基金支出：{{ extraInfo.cvlservPay | formatMoney }}
            </print-col>
            <print-col
                v-if="!isEmpty(extraInfo.mafPay) || isShowZeroSettlementInfo"
                :span="24"
            >
                医疗救助：{{ extraInfo.mafPay | formatMoney }}
            </print-col>
            <print-col
                v-if="!isEmpty(extraInfo.othPay) || isShowZeroSettlementInfo"
                :span="12"
            >
                其他补助：{{ extraInfo.othPay | formatMoney }}
            </print-col>
        </print-row>
        <print-row v-if="shebaoConfig.balanceInfo">
            <print-col :span="12">
                医保余额：{{ shebaoPayment.cardBalance | formatMoney }}
            </print-col>
            <print-col :span="12">
                原有余额：{{ shebaoPayment.beforeCardBalance | formatMoney }}
            </print-col>

            <print-col :span="24">
                公务员补助余额：{{ extraInfo.cvlservLmtRm | formatMoney }}
            </print-col>
            <print-col :span="24">
                慢特病统筹余额：{{ extraInfo.chrdiseLmt | formatMoney }}
            </print-col>
            <print-col :span="24">
                起付线自付金额：{{ extraInfo.actPayDedc | formatMoney }}
            </print-col>
        </print-row>
    </div>
</template>

<script>
    import PrintCol from '../../layout/print-col.vue';
    import PrintRow from '../../layout/print-row.vue';
    import { formatMoney } from "../../../common/utils.js";

    export default {
        name: 'Yunnan',
        components: {
            PrintCol,
            PrintRow,
        },
        filters: {
            formatMoney,
        },
        props: {
            shebaoPayment: {
                type: Object,
                required: true,
            },
            shebaoConfig: {
                type: Object,
                required: true,
            }
        },
        computed: {
            extraInfo() {
                return this.shebaoPayment.extraInfo || {};
            },
            // 是否打印金额为0的医保结算信息
            isShowZeroSettlementInfo(){
                return this.shebaoConfig.zeroSettlementInfo;
            },
        },
        methods: {
            isEmpty(value) {
                return value === undefined || value === null || value === '' || value === '0' || value === 0
            },
        }
    };
</script>
