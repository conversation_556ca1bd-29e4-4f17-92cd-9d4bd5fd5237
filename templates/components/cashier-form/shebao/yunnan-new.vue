<template>
    <div
        data-type="group"
        class="hangzhou-pay-info pay-info-group"
    >
        <template v-if="shebaoConfig.cardInfo">
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                医保号：{{ shebaoPayment.cardId }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                人员编号：{{ extraInfo.psnNo }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                持卡人：{{ shebaoPayment.cardOwner }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                关系：{{ shebaoPayment.relationToPatient }}
            </div>
        </template>

        <template v-if="shebaoConfig.settlementInfo">
            <template v-if="!isEmpty(shebaoPayment.accountPaymentFee) || isShowZeroSettlementInfo">
                <div
                    data-type="item"
                    class="pay-text-info no-change-line"
                    overflow
                >
                    账户支付：{{ shebaoPayment.accountPaymentFee | formatMoney }}
                </div>
                <div
                    v-if="!isEmpty(extraInfo.acctMulaidPay) || isShowZeroSettlementInfo"
                    data-type="item"
                    class="pay-text-info no-change-line"
                    overflow
                >
                    共济支付: {{ extraInfo.acctMulaidPay | formatMoney }}
                </div>
            </template>
            <div
                v-if="!isEmpty(extraInfo.psnCashPay) || isShowZeroSettlementInfo"
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                现金支付：{{ extraInfo.psnCashPay | formatMoney }}
            </div>
            <div
                v-if="!isEmpty(extraInfo.hifpPay) || isShowZeroSettlementInfo"
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                基本医疗基金支出：{{ extraInfo.hifpPay | formatMoney }}
            </div>
            <div
                v-if="!isEmpty(extraInfo.hifobPay) || isShowZeroSettlementInfo"
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                大额医疗基金支出：{{ extraInfo.hifobPay | formatMoney }}
            </div>
            <div
                v-if="!isEmpty(extraInfo.cvlservPay) || isShowZeroSettlementInfo"
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                公务员补助基金支出：{{ extraInfo.cvlservPay | formatMoney }}
            </div>
            <div
                v-if="!isEmpty(extraInfo.mafPay) || isShowZeroSettlementInfo"
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                医疗救助：{{ extraInfo.mafPay | formatMoney }}
            </div>
            <div
                v-if="!isEmpty(extraInfo.othPay) || isShowZeroSettlementInfo"
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                其他补助：{{ extraInfo.othPay | formatMoney }}
            </div>
        </template>
        <template v-if="shebaoConfig.balanceInfo">
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                医保余额：{{ shebaoPayment.cardBalance | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                原有余额：{{ shebaoPayment.beforeCardBalance | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                公务员补助余额：{{ extraInfo.cvlservLmtRm | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                慢特病统筹余额：{{ extraInfo.chrdiseLmt | formatMoney }}
            </div>
            <div
                data-type="item"
                class="pay-text-info no-change-line"
                overflow
            >
                起付线自付金额：{{ extraInfo.actPayDedc | formatMoney }}
            </div>
        </template>
    </div>
</template>

<script>
    import {formatMoney} from "../../../common/utils";

    export default {
        name: 'YunnanNew',
        filters: {
            formatMoney,
        },
        props: {
            shebaoPayment: {
                type: Object,
                required: true,
            },
            shebaoConfig: {
                type: Object,
                required: true,
            }
        },
        computed: {
            extraInfo() {
                return this.shebaoPayment.extraInfo || {};
            },
            // 是否打印金额为0的医保结算信息
            isShowZeroSettlementInfo(){
                return this.shebaoConfig.zeroSettlementInfo;
            },
        },
        methods: {
            isEmpty(value) {
                return value === undefined || value === null || value === '' || value === '0' || value === 0;
            },
        },
    }
</script>

<style lang="scss">
.hangzhou-pay-info {
    &.pay-info-group {
        width: 100%;
        font-size: 0;
    }

    .pay-text-info {
        display: inline-block;
        width: 33%;
        font-size: 8pt;
        line-height: 10pt;
    }

    .no-change-line {
        overflow: hidden;
        text-overflow: clip;
        white-space: nowrap;
    }
}
</style>