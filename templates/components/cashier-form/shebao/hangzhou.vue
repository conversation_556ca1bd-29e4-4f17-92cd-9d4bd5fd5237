<template>
    <div>
        <print-row v-if="shebaoConfig.cardInfo">
            <print-col
                :span="24"
                class="text-info"
            >
                医保号：{{ shebaoPayment.cardId }}
            </print-col>
            <print-col
                :span="24"
                class="text-info"
            >
                人员编号：{{ extraInfo.psnNo }}
            </print-col>
            <print-col
                :span="24"
                class="text-info"
            >
                持卡人：{{ shebaoPayment.cardOwner }}
            </print-col>
        </print-row>

        <print-row v-if="shebaoConfig.settlementInfo">
            <print-col
                :span="12"
                class="item-text"
            >
                基金支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}
            </print-col>
            <print-col
                :span="24"
                class="item-text"
            >
                现金支付：{{ extraInfo.cashPayment | formatMoney }}
            </print-col>
            <print-col :span="24">
                自付：{{ shebaoPayment.selfPaymentFee | formatMoney }}
            </print-col>
            <print-col :span="24">
                自费：{{ extraInfo.fulamtOwnpayAmt | formatMoney }}
            </print-col>
        </print-row>

        <print-row v-if="shebaoConfig.balanceInfo">
            <print-row>
                <print-col :span="24">
                    本年支付：{{ extraInfo.curYearAccountPaymentFee | formatMoney }}
                </print-col>
            </print-row>
            <print-row>
                <print-col :span="24">
                    本年余额：{{ extraInfo.curYearBalance | formatMoney }}
                </print-col>
            </print-row>
            <print-row>
                <print-col :span="24">
                    历年支付：{{ extraInfo.allYearAccountPaymentFee | formatMoney }}
                </print-col>
            </print-row>
            <print-row>
                <print-col :span="24">
                    历年余额：{{ extraInfo.allYearBalance | formatMoney }}
                </print-col>
            </print-row>
            <print-row>
                <print-col :span="24">
                    起付线：本次{{ extraInfo.actPayDedc | formatMoney }}, 累计{{ extraInfo.beforeDedcCumulative | formatMoney }}
                </print-col>
            </print-row>
            <print-row>
                <print-col
                    :span="24"
                >
                    年度累计金额：{{
                        extraInfo.annualCumulative | formatMoney
                    }}
                </print-col>
            </print-row>
        </print-row>
    </div>
</template>

<script>
    import PrintRow from '../../layout/print-row.vue';
    import PrintCol from '../../layout/print-col.vue';
    import {formatMoney} from "../../../common/utils.js";

    export default {
        name: 'Hangzhou',
        components: {
            PrintCol,
            PrintRow,
        },
        filters: {
            formatMoney
        },
        props: {
            shebaoPayment: {
                type: Object,
                required: true
            },
            shebaoConfig: {
                type: Object,
                required: true
            }
        },
        computed: {
            extraInfo() {
                return this.shebaoPayment.extraInfo || {};
            },
        },
    };
</script>
