<template>
    <div
        class="overflow-hidden-item"
        data-type="group"
    >
        <print-row
            v-for="(formItem, tIndex) in form.chargeFormItems"
            :key="`${formItem.id + tIndex }`"
            data-type="item"
        >
            <print-col
                :span="nameWidth"
                class="text-info"
                overflow
            >
                {{ formItem.name }}
            </print-col>
            <print-col
                :span="unitWidth"
                overflow
                class="text-right text-info"
            >
                {{ formItem.count }}次
            </print-col>
            <print-col
                :span="totalPriceWidth"
                class="text-right text-info"
            >
                {{
                    formItem.totalPrice | formatMoney
                }}
            </print-col>
        </print-row>
    </div>
</template>

<script>
    import PrintRow from "../layout/print-row.vue";
    import PrintCol from "../layout/print-col.vue";
    import {formatMoney} from "../../common/utils.js";

    export default {
        name: "ProductForm",
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            formatMoney,
        },
        props: {
            form: {
                type: Object,
                required: true,
            },
            totalPriceWidth: {
                type: Number,
                default: 6,
            },
            unitWidth: {
                type: Number,
                default: 4,
            },
        },
        computed: {
            nameWidth() {
                return 24 - this.unitWidth - this.totalPriceWidth;
            },
        },
    }
</script>
