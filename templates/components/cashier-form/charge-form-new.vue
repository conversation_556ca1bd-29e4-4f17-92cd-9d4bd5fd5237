<template>
    <div
        data-type="group"
        class="charge-form-new"
    >
        <template v-if="config === 0 && totalFee">
            <print-row data-type="item">
                <print-col
                    :span="12"
                    class="text-info no-change-line"
                    overflow
                >
                    {{ totalTitle }}
                </print-col>
                <print-col
                    :span="12"
                    class="text-right text-info no-change-line"
                    overflow
                >
                    {{ totalFee | formatMoney }}
                </print-col>
            </print-row>
        </template>

        <template v-if="config === 1">
            <div
                v-for="(form, index) in forms"
                :key="`${form.id + index}`"
                class="overflow-hidden-item"
            >
                <template v-for="(formItem, tIndex) in form.chargeFormItems">
                    <div
                        v-if="formItem.sourceItemType !== 1"
                        :key="`${formItem.id + tIndex}`"
                    >
                        <print-row data-type="item">
                            <print-col
                                :span="18"
                                class="text-info"
                                :class="{'change-line': isAutoChangeLine, 'no-change-line': !isAutoChangeLine}"
                                :overflow="!isAutoChangeLine"
                            >
                                {{ displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio) }}{{ formItem.medicineCadn || formItem.name }}
                            </print-col>
                            <print-col
                                :span="6"
                                class="text-right text-info change-line"
                            >
                                {{ formItem.count }}{{ getUnit(form, formItem) }}
                            </print-col>
                        </print-row>

                        <!-- 批号 -->
                        <print-row v-if="!isWestern && showMedPositon && formItem.position">
                            <print-col
                                :span="24"
                                class="small-text-info no-change-line"
                            >
                                {{ formItem.position }}
                            </print-col>
                        </print-row>

                        <!-- 因为批次信息可能有多个，需要循环展示，但是柜号只有一个，且要展示在第一列，所以这里特殊处理 -->
                        <template v-if="isWestern">
                            <print-row
                                v-if="!(formItem.goodsStockInfos && formItem.goodsStockInfos.length && showDetail)"
                                :key="`${tIndex}-child`"
                                data-type="item"
                            >
                                <print-col
                                    v-if="showMedPositon && formItem.position"
                                    :span="4"
                                    class="small-text-info no-change-line"
                                    overflow
                                >
                                    柜号：{{ formItem.position }}
                                </print-col>
                                <print-col
                                    v-if="showManufacture && formItem.productInfo && formItem.productInfo.manufacturer"
                                    :span="20"
                                    class="small-text-info no-change-line"
                                    overflow
                                >
                                    厂家：{{ formItem.productInfo.manufacturer }}
                                </print-col>
                            </print-row>
                            <template v-if="formItem.goodsStockInfos && formItem.goodsStockInfos.length && showDetail">
                                <print-row
                                    v-for="(stock, sIndex) in formItem.goodsStockInfos"
                                    :key="`${tIndex}-child-${sIndex}`"
                                    data-type="item"
                                >
                                    <print-col
                                        v-if="showMedPositon && sIndex === 0 && formItem.position"
                                        :span="4"
                                        class="small-text-info no-change-line"
                                        overflow
                                    >
                                        柜号：{{ formItem.position }}
                                    </print-col>
                                    <print-col
                                        v-if="showManufacture && sIndex === 0 && formItem.productInfo && formItem.productInfo.manufacturer"
                                        class="small-text-info no-change-line"
                                        :span="9"
                                        overflow
                                    >
                                        厂家：{{ formItem.productInfo.manufacturer }}
                                    </print-col>
                                    <print-col
                                        v-if="showBatchNumber && stock.batchNo"
                                        :span="6"
                                        overflow
                                        class="small-text-info no-change-line"
                                    >
                                        批号：{{ stock.batchNo }}
                                    </print-col>
                                    <print-col
                                        v-if="showValidDate && stock.expiryDate"
                                        :span="5"
                                        class="small-text-info no-change-line"
                                        overflow
                                    >
                                        效期：{{ stock.expiryDate }}
                                    </print-col>
                                </print-row>
                            </template>
                        </template>

                        <!-- 套餐子项 -->
                        <print-row
                            v-if="showChildren === 2"
                            :key="`${tIndex}-compose-child`"
                        >
                            <print-row
                                v-for="(child, cIndex) in formItem.composeChildren"
                                :key="cIndex"
                                data-type="item"
                            >
                                <print-col
                                    :span="!isFeeCompose ? 9 : 14"
                                    class="child-name text-info"
                                    :class="{'change-line': isAutoChangeLine, 'no-change-line': !isAutoChangeLine}"
                                    :overflow="!isAutoChangeLine"
                                    multiline
                                >
                                    {{ child.name }}
                                </print-col>
                                <print-col
                                    v-if="!isFeeCompose"
                                    :span="5"
                                    class="text-right text-info no-change-line"
                                    overflow
                                >
                                    {{ child.unitPrice | formatMoney }}
                                </print-col>
                                <print-col
                                    :span="4"
                                    class="text-right text-info no-change-line"
                                    overflow
                                >
                                    {{ child.count }}{{ getFormItemUnit(child) }}
                                </print-col>
                                <print-col
                                    :span="6"
                                    class="text-right text-info no-change-line"
                                    overflow
                                >
                                    {{ child.totalPrice | formatMoney }}
                                </print-col>
                            </print-row>
                        </print-row>
                    </div>
                </template>
            </div>
        </template>
        
        <template v-if="config === 2">
            <div
                v-for="(form, index) in forms"
                :key="`${form.id + index }`"
                class="overflow-hidden-item"
            >
                <template v-for="(formItem, tIndex) in form.chargeFormItems">
                    <div
                        v-if="formItem.sourceItemType !== 1"
                        :key="`${formItem.id + tIndex}`"
                    >
                        <print-row data-type="item">
                            <print-col
                                :span="showSpec && !isFeeCompose ? 9 : showSpec && isFeeCompose ? 12 : !showSpec && !isFeeCompose ? 15 : 18"
                                class="text-info"
                                :class="{'change-line': isAutoChangeLine, 'no-change-line': !isAutoChangeLine}"
                                :overflow="!isAutoChangeLine"
                                multiline
                            >
                                {{ displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio) }}{{ formItem.name }}
                            </print-col>
                            <print-col
                                v-if="showSpec"
                                :span="6"
                                overflow
                                class="text-right text-info no-change-line"
                            >
                                {{ formItem.displaySpec }}
                            </print-col>
                            <print-col
                                v-if="!isFeeCompose"
                                :span="3"
                                overflow
                                class="text-right text-info no-change-line"
                            >
                                {{ formItem.unitPrice | formatMoney }}
                            </print-col>
                            <print-col
                                :span="3"
                                overflow
                                class="text-right text-info no-change-line"
                            >
                                {{ formItem.count }}{{ getUnit(form, formItem) }}
                            </print-col>
                            <print-col
                                :span="3"
                                overflow
                                class="text-right text-info no-change-line"
                            >
                                {{ formItem.totalPrice | formatMoney }}
                            </print-col>
                        </print-row>

                        <!-- 批号 -->
                        <print-row v-if="!isWestern && showMedPositon && formItem.position">
                            <print-col
                                :span="24"
                                class="small-text-info no-change-line"
                            >
                                {{ formItem.position }}
                            </print-col>
                        </print-row>
                        
                        <!-- 因为批次信息可能有多个，需要循环展示，但是柜号只有一个，且要展示在第一列，所以这里特殊处理 -->
                        <template v-if="isWestern">
                            <print-row
                                v-if="!(formItem.goodsStockInfos && formItem.goodsStockInfos.length) || !showDetail"
                                :key="`${tIndex}-child`"
                                data-type="item"
                            >
                                <print-col
                                    v-if="showMedPositon && formItem.position"
                                    :span="4"
                                    class="small-text-info no-change-line"
                                    overflow
                                >
                                    柜号：{{ formItem.position }}
                                </print-col>
                                <print-col
                                    v-if="showManufacture && formItem.productInfo && formItem.productInfo.manufacturer"
                                    :span="20"
                                    class="small-text-info no-change-line"
                                    overflow
                                >
                                    厂家：{{ formItem.productInfo.manufacturer }}
                                </print-col>
                            </print-row>
                            <template v-if="formItem.goodsStockInfos && showDetail">
                                <print-row
                                    v-for="(stock, sIndex) in formItem.goodsStockInfos"
                                    :key="`${tIndex}-child-${sIndex}`"
                                    data-type="item"
                                    class="no-change-line"
                                    overflow
                                >
                                    <print-col
                                        v-if="showMedPositon && sIndex === 0 && formItem.position"
                                        :span="4"
                                        class="small-text-info no-change-line"
                                        overflow
                                    >
                                        柜号：{{ formItem.position }}
                                    </print-col>
                                    <print-col
                                        v-if="showManufacture && sIndex === 0 && formItem.productInfo && formItem.productInfo.manufacturer"
                                        class="small-text-info no-change-line"
                                        :span="9"
                                        overflow
                                    >
                                        厂家：{{ formItem.productInfo.manufacturer }}
                                    </print-col>
                                    <print-col
                                        v-if="showBatchNumber && stock.batchNo"
                                        :span="6"
                                        overflow
                                        class="small-text-info no-change-line"
                                    >
                                        批号：{{ stock.batchNo }}
                                    </print-col>
                                    <print-col
                                        v-if="showValidDate && stock.expiryDate"
                                        :span="5"
                                        class="small-text-info no-change-line"
                                        overflow
                                    >
                                        效期：{{ stock.expiryDate }}
                                    </print-col>
                                </print-row>
                            </template>
                        </template>

                        <!-- 套餐子项 -->
                        <print-row
                            v-if="showChildren === 2"
                            :key="`${tIndex}-compose-child`"
                        >
                            <print-row
                                v-for="(child, cIndex) in formItem.composeChildren"
                                :key="cIndex"
                                data-type="item"
                            >
                                <print-col
                                    :span="!isFeeCompose ? 9 : 14"
                                    class="child-name text-info"
                                    :class="{'change-line': isAutoChangeLine, 'no-change-line': !isAutoChangeLine}"
                                    :overflow="!isAutoChangeLine"
                                    multiline
                                >
                                    {{ child.name }}
                                </print-col>
                                <print-col
                                    v-if="!isFeeCompose"
                                    :span="5"
                                    class="text-right text-info no-change-line"
                                    overflow
                                >
                                    {{ child.unitPrice | formatMoney }}
                                </print-col>
                                <print-col
                                    :span="4"
                                    class="text-right text-info no-change-line"
                                    overflow
                                >
                                    {{ child.count }}{{ getFormItemUnit(child) }}
                                </print-col>
                                <print-col
                                    :span="6"
                                    class="text-right text-info no-change-line"
                                    overflow
                                >
                                    {{ child.totalPrice | formatMoney }}
                                </print-col>
                            </print-row>
                        </print-row>
                    </div>
                </template>
            </div>
        </template>
    </div>
</template>

<script>
    import PrintCol from "../layout/print-col.vue";
    import PrintRow from "../layout/print-row.vue";
    import {
        medicalFeeGrade2PrintStr,
        filterOwnExpenseRatio,
        formatMoney,
        formatTreatmentUnit, displayMedicalFeeGradeText
    } from '../../common/utils.js';
    import {GoodsTypeEnum, MedicalFeeGradeEnum, SourceFormTypeEnum} from "../../common/constants";

    export default {
        name: 'ChargeFormNew',
        components: {PrintRow, PrintCol},
        filters: {
            medicalFeeGrade2PrintStr,
            formatMoney,
        },
        props: {
            config: {
                type: Number,
                required: true,
                default: 2,
            },
            forms: {
                type: [Array, Object],
                required: true,
            },
            isFeeCompose: {
                type: Boolean,
                default: false,
            },
            autoChangeLine: {
                type: Boolean,
                default: false,
            },
            shebaoConfig: {
                type: Object,
                default: () => ({}),
            },
            isWestern: {
                type: Boolean,
                default: false,
            },
            showSpec: {
                type: Number,
                default: 0,
            },
            showMedPositon: {
                type: Number,
                default: 0,
            },
            showValidDate: {
                type: Number,
                default: 0,
            },
            showBatchNumber: {
                type: Number,
                default: 0,
            },
            showManufacture: {
                type: Number,
                default: 0,
            },
            showChildren: {
                type: Number,
                default: 0,
            },
            formSplitLine: {
                type: Boolean,
                default: false,
            },
            totalFee: {
                type: Number,
                default: 0,
            },
            totalTitle: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                MedicalFeeGradeEnum,
            };
        },
        computed: {
            isAutoChangeLine() {
                return this.autoChangeLine;
            },
            healthCardInfo() {
                return this.shebaoConfig;
            },
            showDetail() {
                return this.showBatchNumber || this.showValidDate;
            },
        },
        methods: {
            filterOwnExpenseRatio,
            displayMedicalFeeGradeText,
            getUnit(form, formItem) {
                return (form.sourceFormType === SourceFormTypeEnum.TREATMENT ||
                    form.sourceFormType === SourceFormTypeEnum.EXAMINATION ||
                    form.sourceFormType === SourceFormTypeEnum.OTHER) ?
                    formItem.unit ?
                        formatTreatmentUnit(formItem.unit, '*') :
                        '次' :
                    formItem.unit;
            },
            getFormItemUnit(formItem) {
                return (formItem.productType === GoodsTypeEnum.TREATMENT ||
                    formItem.productType === GoodsTypeEnum.EXAMINATION ||
                    formItem.productType === GoodsTypeEnum.OTHER) ?
                    formItem.unit ?
                        formatTreatmentUnit(formItem.unit, '*') :
                        '次' :
                    formItem.unit;
            },
        },
    }
</script>

<style lang="scss">
.charge-form-new {
    .text-info {
        font-size: 10pt;
        line-height: 12pt;
    }

    .small-text-info {
        font-size: 8pt;
        font-weight: 300;
        line-height: 10pt;
    }

    .change-line {
        overflow: visible;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
    }

    .no-change-line {
        overflow: hidden;
        text-overflow: clip;
        white-space: nowrap;
    }

    .text-right {
        text-align: right;
    }

    .child-name {
        padding-left: 7pt;
    }

    .print-split-line {
        height: 0;
        font-size: 0;
        border-bottom: 1pt dashed #000000;
    }
}
</style>