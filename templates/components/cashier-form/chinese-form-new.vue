<template>
    <div
        data-type="group"
        class="chinese-form-new-wrapper"
    >
        <print-row
            v-if="config === 0"
            data-type="item"
        >
            <print-col
                :span="12"
                class="text-info no-change-line"
                overflow
            >
                {{ totalTitle }}{{ isChineseMedicineType }}
            </print-col>
            <print-col
                :span="12"
                class="text-right text-info no-change-line"
                overflow
            >
                {{ totalFee | formatMoney }}
            </print-col>
        </print-row>
        
        <template v-else>
            <template v-for="(form, formIndex) in forms">
                <print-row
                    v-for="(formItems, formItemsIndex) in splitFormItems(form)"
                    :key="`${formIndex}-${formItemsIndex}`"
                    data-type="item"
                >
                    <print-col
                        v-for="(formItem, formItemIndex) in formItems"
                        :key="formItemIndex"
                        :span="config === 2 ? 8 : 6"
                    >
                        <!-- 医保等级 -->
                        <div class="chinese-line">
                            <div
                                v-if="displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio)"
                                class="small-text-info no-change-line chinese-text-margin-left"
                                :style="{ width: config === 2 ? '34%' : '32%' }"
                                overflow
                            >
                                {{ displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio) }}
                            </div>
                            <!-- 柜号 -->
                            <div
                                v-if="position && formItem.position"
                                class="small-text-info no-change-line chinese-text-margin-left"
                                :class="{ 'text-right': displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio) }"
                                :style="{ width: config === 2 ? '29%' : '29%' }"
                                overflow
                            >
                                {{ formItem.position }}
                            </div>
                            <!-- 煎法 -->
                            <div
                                v-if="specialRequirement && formItem.specialRequirement"
                                class="small-text-info no-change-line chinese-text-margin-left"
                                :class="{ 'text-right': displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio) }"
                                :style="{ width: config === 2 ? '23%' : '29%' }"
                                overflow
                            >
                                {{ formItem.specialRequirement && formItem.specialRequirement.substr(0, 2) }}
                            </div>
                        </div>
                        <!-- 中药 -->
                        <div
                            class="chinese-line no-change-line"
                            overflow
                        >
                            <div
                                class="text-info chinese-text-margin-left"
                                :class="{'change-line': isAutoChangeLine, 'no-change-line': !isAutoChangeLine}"
                                :style="{ width: config === 2 ? '23%' : 'auto', 'max-width': config === 1 ? '78%' : null }"
                                :overflow="!isAutoChangeLine"
                            >
                                {{ formItem.name }}
                            </div>
                            <div
                                v-if="config === 2 && !isFeeCompose"
                                class="text-info no-change-line text-right chinese-text-margin-left"
                                style="width: 23%;"
                                overflow
                            >
                                {{ formItem.unitPrice | formatMoney(false) }}
                            </div>
                            <div
                                v-if="showUnitCount"
                                class="text-info no-change-line text-right chinese-text-margin-left"
                                style="width: 15%;"
                                overflow
                            >
                                {{ formItem.unitCount }}{{ formItem.unit }}
                            </div>
                            <div
                                v-if="config === 2"
                                class="text-info no-change-line text-right chinese-text-margin-left"
                                style="width: 23%;"
                                overflow
                            >
                                {{ formItem.totalPrice | formatMoney }}
                            </div>
                        </div>
                        <!-- 批次信息：中药暂时不显示批次信息 -->
                        <!--<template v-for="(stock, sIndex) in formItem.goodsStockInfos">-->
                        <!--    <div-->
                        <!--        :key="sIndex"-->
                        <!--        class="chinese-line"-->
                        <!--    >-->
                        <!--        <div-->
                        <!--            v-if="showBatchNumber"-->
                        <!--            class="small-text-info no-change-line chinese-text-margin-left"-->
                        <!--            :style="{ width: config === 2 ? '44%' : '46%' }"-->
                        <!--            overflow-->
                        <!--        >-->
                        <!--            批号：{{ stock.batchNo }}-->
                        <!--        </div>-->
                        <!--        <div-->
                        <!--            v-if="showValidityDate"-->
                        <!--            class="small-text-info no-change-line chinese-text-margin-left"-->
                        <!--            :style="{ width: config === 2 ? '44%' : '46%' }"-->
                        <!--            overflow-->
                        <!--        >-->
                        <!--            效期：{{ stock.expiryDate }}-->
                        <!--        </div>-->
                        <!--    </div>-->
                        <!--    <div-->
                        <!--        v-if="showManufacturer"-->
                        <!--        :key="sIndex"-->
                        <!--        class="chinese-line"-->
                        <!--    >-->
                        <!--        <div-->
                        <!--            class="small-text-info no-change-line"-->
                        <!--            :style="{ width: config === 2 ? '90%' : '94%' }"-->
                        <!--            overflow-->
                        <!--        >-->
                        <!--            厂家：{{ stock.manufacturer }}-->
                        <!--        </div>-->
                        <!--    </div>-->
                        <!--</template>-->
                    </print-col>
                </print-row>

                <div
                    :key="formIndex"
                    class="print-split-line"
                ></div>

                <print-row
                    :key="formIndex"
                    data-type="item"
                >
                    <print-col
                        :span="24"
                        class="text-info no-change-line text-font-weight-small"
                        overflow
                    >
                        <!-- 除用法两个字保留400字重外,其他的字重设置300 -->
                        <span style="font-weight: 400;">用法：</span>{{ doseCount(form) }}<template v-if="config === 2">
                            ，{{ form.totalPrice | formatMoney }}
                        </template>，{{ doseTotal(form).kind }}味<template v-if="showTotalCount">
                            （单剂{{ doseTotal(form).count }}g，总重{{ doseTotal(form).totalWeight }}g）
                        </template>
                    </print-col>
                </print-row>

                <print-row
                    v-if="usageInfo(form)"
                    :key="formIndex"
                    data-type="item"
                >
                    <print-col
                        :span="24"
                        class="text-info no-change-line text-font-weight-small"
                        style="padding-left: 30pt;"
                        overflow
                    >
                        {{ usageInfo(form) }}
                    </print-col>
                </print-row>

                <print-row
                    v-if="form.processUsageInfo"
                    :key="formIndex"
                    data-type="item"
                >
                    <print-col
                        :span="24"
                        class="text-info no-change-line text-font-weight-small"
                        overflow
                    >
                        <span style="font-weight: 400;">加工：</span>{{ form.processUsageInfo }}
                    </print-col>
                </print-row>
            </template>
        </template>
    </div>
</template>

<script>
    import PrintCol from "../layout/print-col.vue";
    import PrintRow from "../layout/print-row.vue";
    import {
        displayMedicalFeeGradeText,
        filterOwnExpenseRatio,
        formatMoney, medicalFeeGrade2PrintStr,
    } from '../../common/utils.js';
    import Clone from "../../common/clone";
    import {MedicalFeeGradeEnum} from "../../common/constants";

    export default {
        name: 'ChineseFormNew',
        components: {PrintRow, PrintCol},
        filters: {
            formatMoney,
            medicalFeeGrade2PrintStr,
        },
        props: {
            forms: {
                type: Array,
                required: true,
            },
            config: {
                type: Number,
                required: true,
            },
            totalTitle: {
                type: String,
                default: '中药费',
            },
            totalFee: {
                type: [Number, String],
            },
            showBatchNumber: {
                type: Number,
                default: 0,
            },
            showManufacturer: {
                type: Number,
                default: 0,
            },
            showValidityDate: {
                type: Number,
                default: 0,
            },
            showUnitCount: {
                type: Number,
                default: 0,
            },
            shebaoConfig: {
                type: Object,
                default: () => ({}),
            },
            position: {
                type: Number,
                default: 0,
            },
            specialRequirement: {
                type: Number,
                default: 0,
            },
            autoChangeLine: {
                type: Number,
                default: 0,
            },
            showTotalCount: {
                type: Number,
                default: 0,
            },
            isFeeCompose: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                MedicalFeeGradeEnum,
            };
        },
        computed: {
            isChineseMedicineType() {
                const MedicineTypePill = this.forms.some(item => item.specification === "中药饮片")
                const MedicineTypeGrain = this.forms.some(item => item.specification === "中药颗粒")
                const MedicineTypeBoth = this.forms.some(item => item.specification === "中药颗粒,中药饮片")
                return (MedicineTypeGrain && MedicineTypePill) || MedicineTypeBoth ? "(饮片+颗粒)" : MedicineTypePill ? "(饮片)" : MedicineTypeGrain ? "(颗粒)" : ""
            },
            healthCardInfo() {
                return this.shebaoConfig || {};
            },
            isAutoChangeLine() {
                return this.autoChangeLine;
            },
        },
        methods: {
            filterOwnExpenseRatio,
            displayMedicalFeeGradeText,
            splitFormItems(form) {
                let res = [];
                const maxSize = this.config === 2 ? 3 : 4;
                const cacheFormItems = Clone(form.chargeFormItems || []);
                for (let i = 0; i < cacheFormItems.length; i += maxSize) {
                    const chunk = cacheFormItems.slice(i, i + maxSize);
                    res.push(chunk);
                }
                return res;
            },
            doseCount(form) {
                if (!form || !form.doseCount) return '';
                return `共${form.doseCount}剂`;
            },
            doseTotal(form) {
                let count = 0;
                const kinds = [];
                const NUM = 10000;
                form.chargeFormItems.forEach((item) => {
                    if (item.unit === 'g') {
                        count += item.unitCount * NUM || 0;
                    }
                    if (kinds.indexOf(item.name) === -1) {
                        kinds.push(item.name);
                    }
                });
                return {
                    kind: kinds.length,
                    count: count / NUM,
                    totalWeight: (count / NUM) * form.doseCount,
                };
            },
            usageInfo(item) {
                if (!item) return '';
                const dailyDosage = item.dailyDosage ? `${item.dailyDosage}` : '';
                const freq = item.freq ? `，${item.freq}` : '';
                const usageLevel = item.usageLevel ? `，${item.usageLevel}` : '';
                return dailyDosage + freq + usageLevel;
            },
        },
    }
</script>

<style lang="scss">
.chinese-form-new-wrapper {
    .text-info {
        display: inline-block;
        font-size: 10pt;
        line-height: 12pt;
        vertical-align: top;
    }

    .small-text-info {
        display: inline-block;
        font-size: 8pt;
        font-weight: 300;
        line-height: 10pt;
        vertical-align: top;
    }

    .text-font-weight-small {
        font-weight: 300;
    }

    .change-line {
        overflow: visible;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
    }

    .no-change-line {
        overflow: hidden;
        text-overflow: clip;
        white-space: nowrap;
    }

    .chinese-text-margin-left {
        &:not(:first-child) {
            margin-left: 1.5%;
        }
    }

    .text-right {
        text-align: right;
    }

    .chinese-line {
        width: 100%;
        font-size: 0;
    }

    .child-name {
        padding-left: 7pt;
    }

    .print-split-line {
        height: 0;
        font-size: 0;
        border-bottom: 1pt dashed #000000;
    }
}
</style>