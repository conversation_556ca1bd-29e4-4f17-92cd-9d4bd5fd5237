<template>
    <div data-type="group">
        <print-row v-if="config === 0">
            <print-col
                :span="12"
                class="text-info"
            >
                {{ totalTitle }}{{ isChineseMedicineType }}
            </print-col>
            <print-col
                :span="12"
                class="text-right text-info"
            >
                {{ totalFee | formatMoney }}
            </print-col>
        </print-row>
        <template v-else>
            <div v-if="needTowRow">
                <div
                    v-for="(form, index) in forms"
                    :key="`${form.id + index }`"
                >
                    <div
                        v-for="(formItem, tIndex) in form.chargeFormItems"
                        :key="`${formItem.id + tIndex }`"
                        class="overflow-hidden-item"
                        data-type="item"
                    >
                        <div v-if="formItem.sourceItemType !== 1">
                            <print-row class="item-row">
                                <!--药名-->
                                <print-col
                                    :span="nameSpan"
                                    class="text-info"
                                    :class="{'word-break': isAutoChangeLine}"
                                    style="position: relative;"
                                    :overflow="!isAutoChangeLine"
                                >
                                    {{ displayMedicalFeeGradeText(formItem, healthCardInfo.medicalFeeGrade, healthCardInfo.ownExpenseRatio) }}{{ formItem.name }}
                                </print-col>

                                <print-col
                                    v-if="config === 2 && !isFeeCompose"
                                    :span="5"
                                    class="text-right text-info"
                                >
                                    {{ formItem.unitPrice | formatMoney(false) }}
                                </print-col>
                                <!--数量-->
                                <print-col
                                    v-if="showUnitCount"
                                    :span="!isFeeCompose ? 4 : 9"
                                    class="text-right text-info"
                                >
                                    {{ formItem.unitCount }}{{ formItem.unit }}
                                </print-col>

                                <!--金额-->
                                <print-col
                                    v-if="config === 2"
                                    :span="showUnitCount ? 6 : !isFeeCompose ? 10 : 15"
                                    overflow
                                    class="text-right text-info"
                                >
                                    {{ formItem.totalPrice | formatMoney }}
                                </print-col>

                                <!--自费比例-->
                            </print-row>
                            <!--柜号煎法，有自费比例，下一行-->
                            <print-row
                                class="child-row-wrapper"
                                style="margin-bottom: 4pt;"
                            >
                                <print-col
                                    v-if="position && formItem.position"
                                    :span="12"
                                    class="text-left text-info child-row child-row-wrapper"
                                    overflow
                                >
                                    {{ formItem.position }}
                                </print-col>
                                <print-col
                                    v-if="specialRequirement && formItem.specialRequirement"
                                    class="text-left text-info "
                                    :span="12"
                                >
                                    <span style="margin-left: 1mm;">{{ formItem.specialRequirement && formItem.specialRequirement.substr(0, 2) }}</span>
                                </print-col>
                            </print-row>

                            <!--批号厂家信息-->
                            <print-row
                                v-if="showManufacturer"
                                class="child-row"
                            >
                                <print-col
                                    :span="24"
                                    overflow
                                >
                                    厂家：{{ (formItem.productInfo && formItem.productInfo.manufacturer) || '' }}
                                </print-col>
                            </print-row>
                            <template v-if="formItem.goodsStockInfos">
                                <template v-if="isOptimization">
                                    <template v-for="(stock, sIndex) in formItem.goodsStockInfos">
                                        <print-row
                                            :key="`${sIndex}-1`"
                                            class="child-row child-row-wrapper"
                                        >
                                            <print-col
                                                v-if="showBatchNumber"
                                                overflow
                                                class="text-info"
                                                :span="24"
                                            >
                                                批号：{{ stock.batchNo }}
                                            </print-col>
                                        </print-row>
                                        <print-row
                                            :key="`${sIndex}-2`"
                                            class="child-row child-row-wrapper"
                                        >
                                            <print-col
                                                v-if="showValidityDate"
                                                class="text-info"
                                                overflow
                                                :span="24"
                                            >
                                                效期：{{ stock.expiryDate }}
                                            </print-col>
                                        </print-row>
                                    </template>
                                </template>

                                <template v-else>
                                    <print-row
                                        v-for="(stock, sIndex) in formItem.goodsStockInfos"
                                        :key="sIndex"
                                        class="child-row child-row-wrapper"
                                    >
                                        <print-col
                                            v-if="showBatchNumber"
                                            overflow
                                            class="text-info"
                                            :span="12"
                                        >
                                            批号：{{ stock.batchNo }}
                                        </print-col>
                                        <print-col
                                            v-if="showValidityDate"
                                            class="text-info"
                                            overflow
                                            :span="12"
                                        >
                                            效期：{{ stock.expiryDate }}
                                        </print-col>
                                    </print-row>
                                </template>
                            </template>
                        </div>
                    </div>

                    <print-row
                        v-if="showTotalDoseCount"
                        data-type="item"
                    >
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            {{ doseCount(form) }}
                            <template v-if="config === 2">
                                ，{{ form.totalPrice | formatMoney }}
                            </template>
                            ，{{ doseTotal(form).kind }}味
                            <template v-if="showTotalCount">
                                （单剂{{
                                    doseTotal(form).count
                                }}g，总重{{ doseTotal(form).totalWeight }}g）
                            </template>
                        </print-col>
                    </print-row>

                    <print-row
                        v-if="usageInfo(form)"
                        data-type="item"
                    >
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            {{ usageInfo(form) }}
                        </print-col>
                    </print-row>

                    <print-row
                        v-if="form.processUsageInfo"
                        data-type="item"
                    >
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            加工：{{ form.processUsageInfo }}
                        </print-col>
                    </print-row>

                    <div
                        v-if="index + 1 !== forms.length"
                        class="print-split-line"
                    ></div>
                </div>
            </div>

            <template v-else>
                <div
                    v-for="(form, index) in forms"
                    :key="`${index + form.id }`"
                >
                    <print-row
                        v-for="(row, tIndex) in splitForm(form)"
                        :key="tIndex"
                        class="overflow-hidden-item"
                    >
                        <print-col
                            v-if="row[0]"
                            :key="`${row[0].id + tIndex }`"
                            class="text-info"
                            :span="12"
                            style="padding-right: 4%;"
                        >
                            <print-row class="item-row">
                                <print-col
                                    :span="16"
                                    class="text-info"
                                >
                                    <div
                                        :overflow="!isAutoChangeLine"
                                        class="overflow-hidden-item"
                                        :class="{'word-break': isAutoChangeLine}"
                                    >
                                        {{ displayMedicalFeeGradeText(row[0], healthCardInfo.medicalFeeGrade, false) }}{{ row[0].name }}
                                    </div>
                                </print-col>
                                <print-col
                                    v-if="showUnitCount"
                                    :span="8"
                                    class="text-right text-info"
                                >
                                    <div>
                                        {{ row[0].unitCount }}{{ row[0].unit }}
                                    </div>
                                </print-col>
                                <print-col
                                    v-if="position && row[0].position"
                                    :span="12"
                                    class="text-info"
                                >
                                    <transition
                                        name="print-fade"
                                        class="overflow-hidden-item"
                                    >
                                        <div
                                            overflow
                                        >
                                            {{ row[0].position }}
                                        </div>
                                    </transition>
                                </print-col>
                                <print-col
                                    v-if="specialRequirement && row[0].specialRequirement"
                                    :span="12"
                                    class="text-info text-right"
                                >
                                    <div>
                                        {{ row[0].specialRequirement && row[0].specialRequirement.substr(0, 2) }}
                                    </div>
                                </print-col>
                            </print-row>
                        </print-col>
                        <print-col
                            v-if="row[1]"
                            :key="`${row[1].id + tIndex }`"
                            :span="12"
                            style="padding-left: 4%;"
                            calss="text-info"
                        >
                            <print-row class="item-row">
                                <print-col
                                    :span="16"
                                    class="text-info"
                                    :class="{'word-break': isAutoChangeLine}"
                                    :overflow="!isAutoChangeLine"
                                >
                                    <div :class="{'word-break': isAutoChangeLine}">
                                        {{ displayMedicalFeeGradeText(row[1], healthCardInfo.medicalFeeGrade, false) }}{{ row[1].name }}
                                    </div>
                                </print-col>
                                <print-col
                                    v-if="showUnitCount"
                                    :span="8"
                                    class="text-right text-info"
                                >
                                    <div>
                                        {{ row[1].unitCount }}{{ row[1].unit }}
                                    </div>
                                </print-col>
                                <print-col
                                    v-if="position && row[1].position"
                                    :span="12"
                                    class="text-info"
                                >
                                    <transition
                                        name="print-fade"
                                        class="overflow-hidden-item"
                                    >
                                        <div

                                            overflow
                                        >
                                            {{ row[1].position }}
                                        </div>
                                    </transition>
                                </print-col>
                                <print-col
                                    v-if="specialRequirement && row[1].specialRequirement"
                                    :span="12"
                                    class="text-right text-info"
                                >
                                    <div>
                                        {{ row[1].specialRequirement && row[1].specialRequirement.substr(0, 2) }}
                                    </div>
                                </print-col>
                            </print-row>
                        </print-col>
                    </print-row>

                    <print-row v-if="showTotalDoseCount">
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            {{ doseCount(form) }}
                            <template v-if="config === 2">
                                ，{{ form.totalPrice | formatMoney }}
                            </template>
                            ，{{ doseTotal(form).kind }}味
                            <template v-if="showTotalCount">
                                （单剂{{
                                    doseTotal(form).count
                                }}g，总重{{ doseTotal(form).totalWeight }}g）
                            </template>
                        </print-col>
                    </print-row>

                    <print-row>
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            {{ usageInfo(form) }}
                        </print-col>
                    </print-row>

                    <print-row v-if="form.processUsageInfo">
                        <print-col
                            :span="24"
                            class="text-info"
                        >
                            加工：{{ form.processUsageInfo }}
                        </print-col>
                    </print-row>
                    <div
                        v-if="index + 1 !== forms.length"
                        class="print-split-line"
                    ></div>
                </div>
            </template>
        </template>
    </div>
</template>

<script>
    import PrintRow from '../layout/print-row.vue';
    import PrintCol from '../layout/print-col.vue';
    import clone from '../../common/clone.js';
    import {
        displayMedicalFeeGradeText,
        filterOwnExpenseRatio,
        formatMoney,
        medicalFeeGrade2PrintStr,
    } from '../../common/utils.js';
    import { MedicalFeeGradeEnum } from "../../common/constants";

    export default {
        name: 'ChineseForm',
        components: {
            PrintRow,
            PrintCol,
        },
        filters: {
            formatMoney,
            medicalFeeGrade2PrintStr,
        },
        props: {
            forms: {
                type: Array,
                required: true,
            },
            config: {
                type: Number,
                required: true,
            },
            totalFee: {
                type: [Number, String],
            },
            totalTitle: {
                type: String,
                default: '中药费',
            },
            position: {
                type: Number,
            },
            shebaoConfig: {
                type: Object,
            },
            specialRequirement: [Number, Boolean],
            showManufacturer: Number,
            showBatchNumber: Number,
            showValidityDate: Number,
            showUnitCount: [Number, Boolean],
            showTotalCount: [Number, Boolean],
            autoChangeLine: {
                type: Number,
                default: 0,
            },
            isFeeCompose: {
                type: Boolean,
                default: false,
            },
            showTotalDoseCount: {
                type: Boolean,
                default: true,
            },
            isOptimization: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                MedicalFeeGradeEnum,
            };
        },
        computed: {

            nameSpan() {
                let span = 9;

                return span;
            },
            isChineseMedicineType() {
                const MedicineTypePill = this.forms.some(item => item.specification === "中药饮片")
                const MedicineTypeGrain = this.forms.some(item => item.specification === "中药颗粒")
                const MedicineTypeBoth = this.forms.some(item => item.specification === "中药颗粒,中药饮片")
                return (MedicineTypeGrain && MedicineTypePill) || MedicineTypeBoth ? "(饮片+颗粒)" : MedicineTypePill ? "(饮片)" : MedicineTypeGrain ? "(颗粒)" : ""
            },
            healthCardInfo() {
                return this.shebaoConfig || {};
            },
            needTowRow() {
                return this.config === 2 || (this.showBatchNumber || this.showManufacturer || this.showValidityDate);
            },
            isAutoChangeLine() {
                return this.autoChangeLine
            },
        },

        methods: {
            displayMedicalFeeGradeText,
            filterOwnExpenseRatio,
            usageInfo(item) {
                if (!item) return '';
                const dailyDosage = item.dailyDosage ? `${item.dailyDosage} ` : '';
                const freq = item.freq ? `${item.freq} ` : '';
                const usageLevel = item.usageLevel ? item.usageLevel : '';
                return dailyDosage + freq + usageLevel;
            },
            doseCount(form) {
                if (!form || !form.doseCount) return '';
                return `共${form.doseCount}剂`;
            },
            splitForm(form) {
                const items = clone(form.chargeFormItems);
                const res = [];
                /*eslint-disable*/
      while (items && items.length) {
        res.push(items.splice(0, 2));
      }
      return res;
    },
    doseTotal(form) {
      let count = 0;
      const kinds = [];
      const NUM = 10000;
      form.chargeFormItems.forEach((item) => {
        if (item.unit === 'g') {
          count += item.unitCount * NUM || 0;
        }
        if (kinds.indexOf(item.name) === -1) {
          kinds.push(item.name);
        }
      });
      return {
        kind: kinds.length,
        count: count / NUM,
        totalWeight: (count / NUM) * form.doseCount,
      };
    },
  },
};
</script>
