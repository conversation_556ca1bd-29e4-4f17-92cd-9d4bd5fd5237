<!--exampleData
{
    patient: {
        id: 'c252b62caccd43ea96a82e8a22b1116b',
        name: '任盈盈',
        mobile: '',
        sex: '女',
        age: {
            year: 10,
            month: 2,
            day: 17,
        },
        isMember: 0,
        birthday: '2018-10-14',
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
    },
    departmentName: '中西医结合科',
    doctorName: '胡青牛',
    orderNo: '10',
    reserveShift: 1,
    reserveDate: '2019-12-11',
    reserveStart: '12:00',
    reserveEnd: '13:00',
    isReserved: 1,
    registrationFee: 100.0,
    consultingRoomName: '05诊室',
    dayOfWeek: '周三',
    created: '2019-12-10',
    signInTime: '2019-12-10',
    createdBy: '令狐冲',
    payStatus: 1,
    patientOrderNo: '00000009',
    chargeTransactions: [{ payMode: 2, payModeName: '微信', amount: 100.0 },{ payMode: 3, payModeName: '支付宝', amount: 100.0 }],
    receivedFee: 100.0,
};
-->

<template>
    <div>
        <registration-template
            :print-data="printData"
            :config="config"
        ></registration-template>
    </div>
</template>

<script>
    import PageSizeMap, {Orientation} from "../share/page-size.js";

    import RegistrationTemplate from './components/registration/index.vue'

    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";

    import PrintCommonDataHandler from "./data-handler/common-handler.js";

    export default {
        DataHandler: PrintCommonDataHandler,
        components: {
            RegistrationTemplate,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        businessKey: PrintBusinessKeyEnum.REGISTRATION,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        computed: {
            printData() {
                return this.renderData.printData;
            },
            config() {
                if(this.renderData.config && this.renderData.config.registration) {
                    return this.renderData.config.registration;
                }
                return {};
            },
        }
    };
</script>
<style lang="scss">
@import './style/registration/index';
.print-registration-content {

    .item-row .print-col {
        font-size: 12pt;
        line-height: 14pt;
    }

    .print-registration-title {
        font-size: 14pt;
    }

    .print-registration-orderno {
        font-size: 14pt;
    }
}
</style>
