<!--exampleData
{
    // blueInvoiceData: {
    //     invoiceCode: '111',
    //     invoiceNumber: '11332342r1',
    // },
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: '511681199909190000',
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
        category: '医院',
    },
    chargeForms: [
        {
            id: '338adf3126c141e0ab38d5de35e9305901',
            chargeFormItems: [
                {
                    id: 'a78701ff2d9c490792c4d04e297ff299',
                    name: '诊费',
                    socialName: '诊费',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 100.11,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 100.11,
                },
            ],
            sourceFormType: 1,
        },
        {
            id: '338adf3126c141e0ab38d5de35e9305902',
            chargeFormItems: [
                {
                    id: 'd0d7fbc0f1514bb48a721d5fae0226d8',
                    name: 'HPV基因全套',
                    socialName: 'HPV基因全套',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 320,
                    ownExpenseRatio: 1,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 320.0,
                },
                {
                    id: '7d546ba7fd4d472db0aedc21d544ad9f',
                    name: '甲胎蛋白（AFP）',
                    socialName: '甲胎蛋白（AFP）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '次',
                    discountedPrice: 40,
                    unit: '次',
                    count: 1.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 40.0,
                },
            ],
            sourceFormType: 2,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '针灸理疗套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '针灸理疗',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '针灸理疗',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '推拿',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '推拿',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '针灸理疗套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
        },

        {
            id: '032a5047e2034430ab535f62bb1da1c5',
            chargeFormItems: [
                {
                    id: '3c25bb08b54740fcafe071026f3ca488',
                    name: '四环素软膏（三益）',
                    socialName: '四环素软膏（三益）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '支',
                    discountedPrice: 36.0,
                    unit: '支',
                    count: 1.0,
                    unitCount: 2.0,
                    doseCount: 1.0,
                    totalPrice: 36.0,
                },
                {
                    id: 'c05ba826b4b748adb7914e0fc27ee395',
                    name: '法莫替丁片（迪诺洛克）',
                    socialName: '法莫替丁片（迪诺洛克）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '片',
                    discountedPrice: 6.0,
                    unit: '片',
                    count: 6.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 6,
                },
                {
                    id: 'f16b033164f341bc88a5b48b649f02f2',
                    name: '胸腺肽肠溶片（奇莫欣）',
                    socialName: '胸腺肽肠溶片（奇莫欣）',
                    socialCode: '00000000',
                    hisCode: '00000000',
                    socialUnit: '盒',
                    discountedPrice: 20.0,
                    unit: '盒',
                    count: 2.0,
                    unitCount: 1.0,
                    doseCount: 1.0,
                    totalPrice: 20.0,
                },
            ],
            sourceFormType: 4,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '',
    doctorName: '胡青牛',
    doctorWorkNo: '123456879', // 医生工号
    departmentName: '中医科', // 科室
    hospitalCode: 'HCFOOO', // 医院编号

    patientOrderNo: '**********',
    subTotals: {
        registrationFee: 100.11,
        westernMedicineFee: 62,
        chineseMedicineFee: 209.61,
        chineseComposeMedicineFee: 0.0,
        treatmentFee: 140.0,
        examinationFee: 360.0,
        materialFee: 28,
        otherFee: 0,
    },
    medicalBill: {
        registrationFee: 100.11, // 挂号费
        westernMedicineFee: 62, // 西药费
        chineseMedicineFee: 209.61,
        chineseMedicineDrinksPieceFee: 200.61, // 中药饮片费用
        chineseComposeMedicineFee: 12.0, // 中成药费用
        treatmentFee: 140.0, // 治疗理疗费
        examinationFee: 360.0,
        examinationInspectionFee: 128, // 检查费
        examinationExaminationFee: 218, // 检验费
        materialFee: 28, // 材料费
        otherFee: 0, // 一般诊疗费( 其他费用 )
    },
    medicalBills: [
        {
            name: '西药费',
            totalFee: 63,
            totalCount: 2,
            unit: '项',
            printType: 1,
        },
        {
            name: '中药饮片',
            totalFee: 200.0,
            totalCount: 1,
            unit: '项',
            printType: 2,
        },

        {
            name: '中成药费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 3,
        },
        {
            name: '检查费',
            totalFee: 19,
            totalCount: 1,
            unit: '项',
            printType: 4,
        },
        {
            name: '化验费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 5,
        },
        {
            name: '治疗费',
            totalFee: 109,
            totalCount: 1,
            unit: '项',
            printType: 6,
        },

        {
            name: '挂号费',
            totalFee: 100.11,
            totalCount: 1,
            unit: '项',
            printType: 7,
        },
        {
            name: '卫生材料费',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 8,
        },
        {
            name: '其他费用',
            totalFee: 22,
            totalCount: 1,
            unit: '项',
            printType: 9,
        },
    ],

    shebaoPayment: {
        cardId: '00000099', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工退休', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: 0, // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 34.46, // 个人帐户支付金额
        personalPaymentFee: 9.9, // 个人现金支付
        fundPaymentFee: 34.46, // 统筹支付金额
        otherPaymentFee: -34.46, // 其它支付金额
        region: 'hangzhou',
        selfHandledPaymentFee: 0.00, //自理金额
        extraInfo: {
            curYearBalance: 8, // 当年账户余额
            allYearBalance: 10, // 历年账户余额
            curYearAccountPaymentFee: 7, // 本年账户支付
            allYearAccountPaymentFee: 9, // 历年账户支付
            fundPayment: 0, // 规定病种
            cashPayment: 0, // 规定病种
            cashPaymentFee: 0, // 医保现金支付
            selfConceitFee: 1, // 自负金额
            allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            personalHandledAmount: 3, // 自理金额
            allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            personalPaymentAmount: 5, // 自费金额
            allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            sbzzPaymentFee: 0, // 商保赔付
            cvlservPay: 0.00, // 公务员补助
            hifobPay: 0.00, // 职工大额医疗费用补助基金支出
            hifesPay: 0.00, // 企业补充医疗保险基金支出
            hifmiPay: 0.00, // 居民大病保险资金支出
            mafPay: 0.00, // 医疗救助基金支出
            psnCashPay: 0.00, // 个人现金支出
            hifpPay: 0.00, // 基本医疗保险统筹基金支出
            actPayDedc: 0.00, // 实际支付起付线
            othPay: 0.00, // 其他支出
            fulamtOwnpayAmt: 0.00, //  全自费金额
            overlmtSelfpay: 0.00, // 超限价自费费用
            preselfpayAmt: 0.00, // 先行自付金额
            inscpScpAmt: 0.00, // 符合政策范围金额
            acctMulaidPay: 0.00, // 个人账户共济支付金额
            hospPartAmt: 0.00, // 医院负担金额
            psnNo: 0.00, //   人员编号
            iptOtpNo: 0.00, //   住院/门诊号
            insutype: 0.00, // 险种类型
            // 青岛数据
            individualAffordabilityLine: '9.9', //个人负担起付线
        },
    },
}
-->
<template>
    <div>
        <template v-for="(page, pageIndex) in currentRenderPage">
            <div
                :key="pageIndex"
                class="huhehaote-medical-bill-content"
            >
                <div
                    class="huhehaote-medical-bill-page"
                >
                    <div
                        v-if="blueInvoiceData"
                        style="position: absolute; top: 1cm; left: 1.8cm;"
                    >
                        销项负数&nbsp;&nbsp;对应正数发票代码：{{ blueInvoiceData.invoiceCode }} 号码：{{ blueInvoiceData.invoiceNumbers[pageIndex] }}
                    </div>
                    <refund-icon
                        v-if="isRefundBill"
                        top="0.5cm"
                        left="0.8cm"
                    ></refund-icon>

                    <div class="organ">
                        {{ huhehaote.institutionName }}
                    </div>
                    <div class="patient">
                        {{ patient.name }}
                    </div>
                    <div class="card-id">
                        {{ shebaoPayment.cardId }}
                    </div>
                    <div class="card-type">
                        {{ shebaoPayment.cardOwnerType }}
                    </div>

                    <div
                        class="medical-bill-wrapper"
                        :style="{ top: medicalBills.length > 14 ? '26mm' : '31mm' }"
                    >
                        <div
                            v-for="(item, index) in medicalBills"
                            :key="index"
                            class="medical-bill-item"
                            :style="{
                                'top': `${Math.floor(index / 2) * 4 }mm`,
                                'left': index % 2 === 0 ? '0' : '24mm'
                            }"
                        >
                            {{ item.name }}{{ item.totalFee | formatMoney }}
                        </div>
                    </div>


                    <div class="total-money">
                        {{ finalFee | formatMoney }}
                    </div>
                    <div class="upper-money">
                        {{ digitUppercase(finalFee) }}
                    </div>


                    <div class="absolute-box account-payment-fee">
                        {{ shebaoPayment.accountPaymentFee | formatMoney }}
                    </div>

                    <div class="absolute-box cvlserv-pay">
                        {{ extraInfo.cvlservPay | formatMoney }}
                    </div>

                    <div class="absolute-box act-pay-dedc">
                        起付线：{{ extraInfo.actPayDedc | formatMoney }}
                    </div>

                    <div class="absolute-box hosp-part-amt">
                        {{ extraInfo.hospPartAmt | formatMoney }}
                    </div>
                    <!--                    <div class="absolute-box receive-fee">-->
                    <!--                        医保支付：{{ shebaoPayment.receivedFee | formatMoney }}-->
                    <!--                    </div>-->
                    <div class="absolute-box personal-payment-fee">
                        {{ printData.personalPaymentFee | formatMoney }}
                    </div>
                    <div class="absolute-box fund-payment-fee">
                        {{ shebaoPayment.fundPaymentFee | formatMoney }}
                    </div>

                    <div class="absolute-box hifob-pay">
                        {{ extraInfo.hifobPay | formatMoney }}
                    </div>

                    <div class="absolute-box self-fee">
                        {{ shebaoPayment.selfHandledPaymentFee | formatMoney }}
                    </div>

                    <div class="card-balance absolute-box">
                        {{ shebaoPayment.cardBalance }}
                    </div>

                    <div class="charger absolute-box">
                        {{ printData.chargedByName }}
                    </div>


                    <div class="charged-time absolute-box">
                        {{ printData.chargedTime | parseTime('y-m-d') }}
                    </div>


                    <div class="form-items-wrapper">
                        <div class="form-item-tr">
                            <span class="item-name">药品</span>
                            <span class="item-count">数量</span>
                            <span class="item-price">单价</span>
                            <span class="total-price">金额</span>
                        </div>
                        <div
                            v-for="(item, index) in page.formItems"
                            :key="index + pageIndex"
                            class="form-item-tr"
                        >
                            <span class="item-name">
                                <template v-if="item.medicalFeeGrade">[{{ item.medicalFeeGrade | medicalFeeGrade2PrintStr }}]</template>
                                {{ item.name }}
                            </span>
                            <span class="item-count">{{ item.count }}{{ item.unit }}</span>
                            <span class="item-price">{{ item.discountedUnitPrice | formatMoney }}</span>
                            <span class="total-price">{{ item.discountedPrice | formatMoney }}</span>
                        </div>
                        <div
                            v-if="hasOverPageTip"
                            class="only-one-page"
                        >
                            *** 因纸张限制，部分项目未打印 ***
                        </div>
                        <template v-else>
                            <div
                                v-if="pageIndex !== renderPage.length - 1"
                                class="next-page"
                            >
                                *** 接下页 ***
                            </div>
                        </template>
                    </div>


                    <div
                        v-for="(item, index) in getUnionItems"
                        :key="index"
                        class="expend-item absolute-box"
                        :style="{
                            top: Math.floor(index / 2) === 1 ? '50mm' : 0,
                            left: index % 2 === 1 ? '190mm' : '150mm'
                        }"
                    >
                        <template v-if="item && item.length">
                            <div class="absolute-box department-name">
                                {{ printData.departmentName }}
                            </div>
                            <div class="absolute-box item-name">
                                {{ patient.name }}
                            </div>
                            <div class="item-code absolute-box">
                                {{ shebaoPayment.cardId }}
                            </div>
                            <div class="pro-items absolute-box">
                                <div
                                    v-for="(it, pIndex) in item"
                                    :key="`${pIndex}it`"
                                >
                                    <span>{{ it.name }}</span>
                                </div>
                            </div>


                            <div class="items-amount absolute-box">
                                {{ getItemsAmount(item) | formatMoney }}
                            </div>

                            <div class="item-charger absolute-box">
                                {{ printData.chargedByName }}
                            </div>
                            <div class="item-time absolute-box">
                                {{ printData.chargedTime | parseTime('y-m-d') }}
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div
                v-if="pageIndex !== currentRenderPage.length - 1"
                data-type="new-page"
            ></div>
        </template>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, { MM240_101_HUHEHAOTE, Orientation } from "../share/page-size.js";
    import BillDataMixins from './mixins/bill-data';
    import RefundIcon from './components/refund-icon/refund-icon.vue';
    import {parseTime} from "./common/utils.js";
    import PrintCol from "./components/layout/print-col.vue";
    import {GoodsFeeType, PrintFormTypeEnum} from "./common/constants";
    export default {
        name: "MedicalBillHuhehaote",
        components: {
            PrintCol,
            RefundIcon
        },
        filters: {
            parseTime,
        },
        mixins: [BillDataMixins],
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_HUHEHAOTE,
        pages: [
            {
                paper: PageSizeMap.MM240_101_HUHEHAOTE,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            hasOverPageTip() {
                return this.isOnlyOnePage;
            },
            currentRenderPage() {
                return this.isOnlyOnePage ? this.renderPage.slice(0, 1) : this.renderPage
            },
            splitType() {
                return this.huhehaote.splitType;
            },
            isOnlyOnePage() {
                return this.splitType === 1 && (this.renderPage.length > 1 || this.extra.isPreview);
            },
            huhehaote() {
                return this.config.huhehaote || {}
            },
            chargeFormItems() {
                // 如果开启了医嘱收费项配置,目前只有医院管家开启
                if (this.isFeeCompose) {
                    let res = [];
                    this.chargeForms.forEach((chargeForm) => {
                        // 如果为医嘱套餐
                        if (chargeForm.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT) {
                            if (chargeForm.chargeFormItems && chargeForm.chargeFormItems.length) {
                                chargeForm.chargeFormItems.forEach((chargeFormItem) => {
                                    res = res.concat(chargeFormItem.composeChildren);
                                });
                            }
                        } else {
                            // 如果为医嘱或者费用项
                            if (chargeForm.chargeFormItems && chargeForm.chargeFormItems.length) {
                                chargeForm.chargeFormItems.forEach((chargeFormItem) => {
                                    // 如果为医嘱
                                    if (chargeFormItem.goodsFeeType === GoodsFeeType.FEE_PARENT) {
                                        if (chargeFormItem.composeChildren && chargeFormItem.composeChildren.length) {
                                            res = res.concat(chargeFormItem.composeChildren);
                                        }
                                    } else {
                                        // 如果为费用项
                                        res.push(chargeFormItem);
                                    }
                                });
                            }
                        }
                    });
                    return res;
                }

                // 未开启医嘱收费项配置,目前只有医院管家开启
                let formItems = [];
                this.chargeForms.forEach((form) => {
                    formItems = formItems.concat(form.chargeFormItems);
                });
                return formItems;
            },
            renderPage() {
                return this.spliceFormItems(this.chargeFormItems, 7);
            },
            getUnionItems() {
                const billItems = [[], [], [], []];
                this.medicalBills.forEach((item, index) => {
                    billItems[index % (billItems.length)].push(item);
                });
                return billItems;
            },
        },
        methods: {
            getItemsAmount(items) {
                let total = 0;
                items.forEach((item) => {
                    total += item.totalFee;
                });
                return total;
            },
        },
    }
</script>

<style lang="scss">
* {
    padding: 0;
    margin: 0;
}

.huhehaote-medical-bill-content {
    @import "./components/refund-icon/refund-icon.scss";

    font-size: 9pt;


    .huhehaote-medical-bill-page {
        position: absolute;
        width: 240mm;
        height: 101mm;
    }
}

.organ,
.card-id,
.card-type,
.patient,
.form-items-wrapper,
.medical-bill-wrapper,
.upper-money,
.total-money,
.absolute-box {
    position: absolute;
    line-height: 11pt;
}

.organ {
    top: 14mm;
    left: 34mm;
}

.patient {
    top: 18mm;
    left: 29mm;
}

.card-id,
.card-type {
    left: 95mm;
}

.card-id {
    top: 14mm;
}

.card-type {
    top: 18mm;
}

.medical-bill-wrapper {
    top: 31mm;
    left: 10mm;
    width: 43mm;
    height: 29mm;

    .medical-bill-item {
        position: absolute;
        width: 30mm;
    }
}

.total-money {
    top: 62mm;
    left: 24mm;
}

.upper-money {
    top: 66mm;
    left: 46mm;
}

.personal-payment-fee {
    top: 76mm;
    left: 92mm;
}

.fund-payment-fee {
    top: 81mm;
    left: 31mm;
}

.cvlserv-pay{
    top: 71mm;
    left: 88mm;
}

.act-pay-dedc{
  top: 71mm;
  left: 110mm;
}

.account-payment-fee {
    top: 71mm;
    left: 31mm;
}

.hosp-part-amt{
    top: 76mm;
    left: 31mm;
}

.hifob-pay{
    top: 81mm;
    left: 80mm;
}

.self-fee {
    top: 86mm;
    left: 41mm;
}

.card-balance {
    top: 86mm;
    left: 113mm;
}

.charger {
    top: 91mm;
    left: 69mm;
}

.charged-time {
    top: 91mm;
    left: 112mm;
}

.form-items-wrapper {
    top: 27mm;
    left: 60mm;
    width: 84mm;
    height: 38mm;

    .form-item-tr {
        width: 100%;
        height: 4mm;
        font-size: 0;
        line-height: 11pt;
    }

    .item-name,
    .item-count,
    .item-price,
    .item-spec,
    .total-price {
        display: inline-block;
        *display: inline;
        overflow: hidden;
        font-size: 9pt;
        word-break: keep-all;
        white-space: nowrap;
        *zoom: 1;
    }

    .item-name {
        width: 43mm;
    }

    .item-price,
    .total-price,
    .item-spec,
    .item-count {
        width: 13mm;
        text-align: right;
    }
}

.only-one-page {
    text-align: center;
}

.next-page {
    text-align: center;
}

.expend-item {
    top: 0;
    left: 150mm;
    width: 39mm;
    height: 50mm;

    .department-name {
        top: 4mm;
        left: 13mm;
    }

    .item-name {
        top: 8.5mm;
        left: 13mm;
    }

    .item-code {
        top: 13mm;
        left: 13mm;
    }

    .pro-items {
        top: 17mm;
        left: 13mm;
    }

    .items-amount {
        top: 33mm;
        left: 13mm;
    }

    .item-time {
        top: 38mm;
        left: 13mm;
    }

    .item-charger {
        top: 43mm;
        left: 13mm;
    }

}

.abc-page_preview {
    color: #2a82e4;
    background: url("/static/assets/print/huhehaote.jpg");
    background-size: 240mm 101mm;
    .huhehaote-medical-bill-page {
        top: -2mm;
    }
}
</style>