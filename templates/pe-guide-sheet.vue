<template>
    <div class="pe-guide-sheet-wrapper">
        <div
            data-type="header"
            class="pe-guide-sheet-header-wrapper"
        >
            <div
                v-if="printData.institutionLogoUrl"
                class="organ-logo"
            >
                <img
                    class="logo-img"
                    :src="printData.institutionLogoUrl"
                    alt=""
                />
            </div>
            <div class="order-barcode">
                <img
                    v-if="barcodeSrc"
                    class="bar-code-img"
                    :src="barcodeSrc"
                    alt=""
                />
                {{ printData.no || '' }}
            </div>

            <div class="guide-sheet-title">
                体检项目引导单
            </div>
            <div class="organ-name">
                {{ printData.institutionName }}
            </div>
        </div>

        <div
            class="order-info-wrapper"
        >
            <div class="order-info">
                <print-row class="patient-info">
                    <print-col
                        overflow
                        :span="9"
                    >
                        体检人：{{ patientInfo.name || '' }}&nbsp;&nbsp;{{ patientInfo.sex || '' }}&nbsp;&nbsp;{{ patientAge }}
                    </print-col>
                    <print-col
                        overflow
                        :span="6"
                    >
                        手机：{{ patientInfo.mobile || '' }}
                    </print-col>
                    <print-col :span="9">
                        {{ getIdCardTypeStr(patientInfo.idCardType) }}：{{ patientInfo.idCard || '' }}
                    </print-col>
                </print-row>

                <table
                    data-type="mix-table"
                    class="pe-order-table-wrapper"
                >
                    <tbody>
                        <tr class="table-tr">
                            <td
                                v-if="isExistAvatar"
                                colspan="4"
                                :rowspan="patientDepartment ? 4 : 3"
                            >
                                <abc-print-image :value="printData.patientImageUrl"></abc-print-image>
                            </td>
                            <td colspan="4">
                                个检/团检
                            </td>
                            <td :colspan="16 + avatarSpan">
                                {{ printData.displayName || '' }}
                            </td>
                        </tr>
                        <tr
                            v-if="patientDepartment"
                            class="table-tr"
                        >
                            <td colspan="4">
                                部门
                            </td>
                            <td :colspan="16 + avatarSpan">
                                {{ patientDepartment }}
                            </td>
                        </tr>
                        <tr class="table-tr">
                            <td colspan="4">
                                体检类型
                            </td>
                            <td colspan="4">
                                {{ businessTypeStr }}
                            </td>
                            <td colspan="4">
                                体检时间
                            </td>
                            <td :colspan="8 + avatarSpan">
                                {{ printData.businessTime | parseTime('y-m-d') }}
                            </td>
                        </tr>
                        <tr class="table-tr">
                            <td colspan="4">
                                取报告方式
                            </td>
                            <td colspan="4">
                                {{ ReportGetWayLabel[printData.reportGetWay] || '' }}
                            </td>
                            <td colspan="4">
                                客户地址
                            </td>
                            <td :colspan="8 + avatarSpan">
                                {{ customerAddress }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <table
            data-type="complex-table"
            class="pe-order-item-table-wrapper"
        >
            <thead>
                <tr class="table-title">
                    <th colspan="4">
                        科室
                    </th>
                    <th colspan="6">
                        地点
                    </th>
                    <th colspan="6">
                        项目
                    </th>
                    <th colspan="4">
                        检查医生
                    </th>
                    <th colspan="4">
                        备注
                    </th>
                </tr>
            </thead>
            <tbody data-type="group">
                <template v-for="(item, index) in departmentGroups">
                    <tr
                        v-for="(it, cIndex) in item.executeItems"
                        :key="it.id + index"
                        class="table-tr"
                    >
                        <!--科室-->
                        <td
                            v-if="cIndex === 0"
                            colspan="4"
                            :rowspan="item.executeItems.length"
                        >
                            {{ item.departmentName }}
                        </td>
                        <!--地点-->
                        <td colspan="6">
                            {{ it.address }}
                        </td>
                        <!--项目-->
                        <td colspan="6">
                            {{ it.goodsName }}
                        </td>
                        <!--检查医生-->
                        <td colspan="4">
                            {{ it.tester ? it.tester.name : '' }}
                        </td>
                        <!--备注-->
                        <td colspan="4">
                            {{ it.samplingReq }}
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>

        <!--        <div class="confirm-content-wrapper">-->
        <!--            <print-row>-->
        <!--                <print-col>-->
        <!--                    请仔细阅读以下内容：-->
        <!--                </print-col>-->
        <!--            </print-row>-->
        <!--            <print-row>-->
        <!--                <print-col :span="18">-->
        <!--                    你的单位或其他第三方可能会要求我们共享您的体检报告。您是否愿意共享-->
        <!--                </print-col>-->
        <!--                <print-col :span="3">-->
        <!--                    同意 [&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;]-->
        <!--                </print-col>-->
        <!--                <print-col :span="3">-->
        <!--                    不同意 [&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;]-->
        <!--                </print-col>-->
        <!--            </print-row>-->
        <!--		    -->
        <!--            <print-row>-->
        <!--                <print-col :span="18">-->
        <!--                    体检报告是否需要纸质版报告-->
        <!--                </print-col>-->
        <!--                <print-col :span="3">-->
        <!--                    需要 [&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;]-->
        <!--                </print-col>-->
        <!--                <print-col :span="3">-->
        <!--                    不需要 [&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;]-->
        <!--                </print-col>-->
        <!--            </print-row>-->
        <!--		    -->
        <!--            <div class="sign-area">-->
        <!--                签字：-->
        <!--            </div>-->
        <!--        </div>-->

        <div
            data-type="footer"
            class="pe-guide-sheet-footer-wrapper"
        >
            <print-row class="customer-info">
                <print-col :span="4">
                    体检人：{{ patientInfo.name || '' }}
                </print-col>
                <print-col :span="16">
                    单位：{{ organName }}
                </print-col>
            </print-row>

            <print-row class="date-info">
                <print-col :span="20">
                    体检日期：{{ printData.businessTime | parseTime('y-m-d') }}
                </print-col>
            </print-row>

            <div class="warn-tips">
                请携带体检者身份证领取报告，请联系所属单位领取，或扫码关注我们公众号，查看电子报告
            </div>
        </div>
    </div>
</template>

<script>

    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import printRow from "./components/layout/print-row.vue";
    import PrintCol from "./components/layout/print-col.vue";
    import PrintCommonDataHandler from "./data-handler/common-handler";
    import {PEBusinessTypeEnum, ReportGetWayLabel} from "./constant/physical-examination";
    import {getIdCardTypeStr, parseTime, textToBase64BarCode} from "./common/utils.js";
    import AbcPrintImage from "./components/layout/abc-print-image.vue";

    export default {
        DataHandler: PrintCommonDataHandler,
        name: "PEGuideSheet",
        businessKey: PrintBusinessKeyEnum.PE_GUIDE_SHEET,
        filters: {
            parseTime
        },
        components: {
            AbcPrintImage,
            printRow,
            PrintCol
        },
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
        },
        data() {
            return {
                ReportGetWayLabel
            }
        },
        computed: {
            printData() {
                console.log(this.renderData.printData, 'this.renderData.printData')
                return this.renderData.printData || {}
            },

            patientInfo() {
                return this.printData.patient || {}
            },

            patientAge() {
                const {
                    age
                } = this.patientInfo
                return age ? age.year + '岁' : '';
            },
            patientMarital() {
                const MaritalStatusLabel = {
                    0: '未知',
                    1: '未婚',
                    2: '已婚',
                    3: '离异',
                    4: '丧偶',
                }

                const {
                    marital
                } = this.patientInfo
                return MaritalStatusLabel[marital] || ''
            },
            businessTypeStr() {
                const {
                    businessType,
                } = this.printData;
                if (businessType === PEBusinessTypeEnum.GENERAL) {
                    return '普通体检';
                }
                if (businessType === PEBusinessTypeEnum.PUBLIC_HEALTH) {
                    return '公卫体检';
                }
                return '';
            },

            customerAddress() {
                const {
                    addressCityName,
                    addressDetail,
                    addressDistrictName,
                    addressProvinceName
                } = this.printData.address || {};
                return [
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                    addressDetail
                ].filter(it => it).join('/')
            },

            departmentGroups() {
                return this.printData.departmentGroups || [];
            },

            organName() {
                const {
                    groupOrder
                } = this.printData;
                if(!groupOrder) return '';
                const {
                    peOrganInfoVO
                } = groupOrder;
                if(!peOrganInfoVO) return '';

                return peOrganInfoVO.name || ''
            },
            barcodeSrc() {
                if(!this.printData.no) return '';
                return textToBase64BarCode(this.printData.no);
            },

            isExistAvatar() {
                return !!this.printData.patientImageUrl;
            },

            avatarSpan() {
                if (this.isExistAvatar) return 0;
                return 4;
            },

            patientDepartment() {
                return this.printData.patientDepartment?.name;
            }
        },
        methods: {getIdCardTypeStr},

    }
</script>

<style lang="scss">
    @import "./components/layout/print-layout.scss";

    .abc-page.abc-page_preview {
        width: 210mm !important;
        height: 297mm !important;
    }

    .pe-guide-sheet-wrapper {
        .pe-guide-sheet-header-wrapper {
            position: relative;
            height: 80pt;
            font-size: 10pt;

            .organ-logo {
                position: absolute;
                top: 0;
                left: 0;
                height: 60pt;

                .logo-img {
                    height: 60pt;
                }
            }

            .order-barcode {
                position: absolute;
                top: 0;
                right: 0;
                width: 112pt;
                text-align: center;

                .bar-code-img {
                    width: 112pt;
                    height: 40pt;
                }
            }

            .guide-sheet-title {
                font-size: 16pt;
                font-weight: normal;
                text-align: center;
            }

            .organ-name {
                margin-top: 4pt;
                text-align: center;
            }
        }

        .order-info-wrapper {
            position: relative;

            //&.with-avatar {
            //	padding-left: 78pt;
            //}

            //.patient-avatar {
            //	position: absolute;
            //	top: 0;
            //	left: 0;
            //	width: 70pt;
            //	height: 93pt;
            //	img {
            //		width: 100%;
            //	}
            //}

            .order-info {
                font-size: 10pt;
            }
        }

        .pe-order-table-wrapper {
            width: 100%;
            margin-top: 8pt;
            table-layout: fixed;
            border-collapse: collapse;
            border: 1px solid #a6a6a6;

            th,
            td {
                padding: 6pt 8pt;
                font-size: 10pt;
                text-align: left;
                word-break: break-all;
                border: 1px solid #000000;
            }
        }

        .pe-order-item-table-wrapper {
            width: 100%;
            margin-top: 8pt;
            table-layout: fixed;
            border-collapse: collapse;
            border: 1px solid #a6a6a6;

            th,
            td {
                padding: 4pt 6pt;
                font-size: 10pt;
                text-align: left;
                word-break: break-all;
                border: 1px solid #000000;
            }
        }

        .confirm-content-wrapper {
            margin-top: 12pt;
            font-size: 10pt;

            .sign-area {
                margin-top: 14pt;
            }

            .print-row + .print-row {
                margin-top: 4pt;
            }
        }

        .pe-guide-sheet-footer-wrapper {
            padding-top: 6pt;
            border-top: 1pt solid #000000;

            .date-info {
                margin-top: 4pt;
            }

            .customer-info,
            .date-info {
                font-size: 10pt;
            }

            .warn-tips {
                margin-top: 6pt;
                font-size: 8pt;
                line-height: 10pt;
                color: #000000;
            }
        }
    }
</style>
