<template>
    <div class="charge-list">
        <div
            data-type="header"
            class="header"
        >
            <div class="header-patient-title">
                <div class="charge-list-title">
                    {{ pageTitle }}
                </div>
                <div class="sub-title">
                    住院费用清单
                </div>
            </div>
            <div class="header-patient-form">
                <div
                    class="header-patient-item width-40"
                    style="word-spacing: initial;"
                    overflow
                >
                    <span>姓名：{{ patient.name }}</span>
                    <span class="margin-left-6">{{ patient.sex }}</span>
                    <span class="margin-left-6">{{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}</span>
                </div>
                <div
                    class="header-patient-item width-35"
                    overflow
                >
                    机构代码：{{ nationalCode }}
                </div>
                <div
                    class="header-patient-item width-25"
                    overflow
                >
                    住院号：{{ patientOrderInfo.no }}
                </div>
            </div>
            <div class="header-patient-form">
                <div
                    class="header-patient-item width-40"
                    overflow
                >
                    科别：{{ patientOrderInfo.departmentName }}
                </div>
                <template v-if="inHospital">
                    <div
                        class="header-patient-item width-35"
                        overflow
                    >
                        <span>床号：{{ patientOrderInfo.wardName }}</span>
                        <span
                            v-if="patientOrderInfo.bedRoomName"
                            class="margin-left-6"
                        >{{ patientOrderInfo.bedRoomName }}</span>
                        <span class="margin-left-6">{{ patientOrderInfo.bedNo }}床</span>
                    </div>
                    <div
                        class="header-patient-item width-25"
                        overflow
                    >
                        入院日期：{{ parseTime(patientOrderInfo.inpatientTime, 'y-m-d') }}
                    </div>
                </template>

                <template v-else>
                    <div
                        class="header-patient-item width-35"
                        overflow
                    >
                        入院日期：{{ parseTime(patientOrderInfo.inpatientTime, 'y-m-d') }}
                    </div>
                    <div
                        class="header-patient-item width-25"
                        overflow
                    >
                        出院日期：{{ parseTime(patientOrderInfo.dischargeTime, 'y-m-d') }}
                    </div>
                </template>
            </div>
            <div
                v-if="inHospital"
                class="header-patient-form"
            >
                <div
                    class="header-patient-item width-40"
                    overflow
                    style="word-spacing: 0;"
                >
                    预缴金：{{ printData.depositReceivedFee | formatMoney }}
                    (当前余额 {{ printData.currentBalance | formatMoney }})
                </div>
                <div
                    class="header-patient-item width-35"
                    overflow
                >
                    费用合计：{{ printData.totalBetweenFee }}（累计合计：{{ printData.totalFee | formatMoney }}）
                </div>
                <div
                    class="header-patient-item width-25"
                    overflow
                >
                    费用日期：{{ printData.beginDate | parseTime('y-m-d') }}~{{ printData.endDate | parseTime('m-d') }}
                </div>
            </div>
            <div
                v-else
                class="header-patient-form"
            >
                <div
                    class="header-patient-item width-40"
                    overflow
                >
                    <span>床号：{{ patientOrderInfo.wardName }}</span>
                    <span
                        v-if="patientOrderInfo.bedRoomName"
                        class="margin-left-6"
                    >{{ patientOrderInfo.bedRoomName }}</span>
                    <span class="margin-left-6">{{ patientOrderInfo.bedNo }}床</span>
                </div>
                <div
                    class="header-patient-item width-35"
                    overflow
                >
                    预缴金：{{ printData.depositReceivedFee | formatMoney }}
                </div>
                <div
                    class="header-patient-item width-25"
                    overflow
                >
                    总计：{{ printData.totalFee | formatMoney }}(医保{{ shebaoExtraInfo.medfeeSumamt | formatMoney }})
                </div>
            </div>
        </div>

        <!-- 项目 -->
        <div
            v-if="config.feeInfo"
            data-type="mix-box"
        >
            <!-- 医嘱项目 -->
            <template v-if="config.feeDetail">
                <div
                    class="charge-list-advice-list"
                    style=" padding: 8px 0 0; border-top: 1px solid #a6a6a6;"
                >
                    <div
                        class="charge-list-advice-list-item"
                    >
                        <!-- 名称 -->
                        <div class="charge-list-advice-list-item-name">
                            医嘱
                        </div>
                        <div
                            v-if="config.medicalGrade"
                            class="charge-list-advice-list-item-medical-grade"
                        >
                            医保等级
                        </div>
                        <!-- 医保码 -->
                        <div
                            v-if="config.code"
                            class="charge-list-advice-list-item-social-code"
                        >
                            国家码
                        </div>
                        <!-- 规格 -->
                        <div
                            v-if="config.spec"
                            class="charge-list-advice-list-item-social-spec"
                        >
                            规格
                        </div>
                        <!-- 单位 -->
                        <div class="charge-list-advice-list-item-social-unit">
                            单位
                        </div>
                        <!-- 单价 -->
                        <div
                            v-if="config.unitPrice"
                            class="charge-list-advice-list-item-social-total-price"
                        >
                            单价
                        </div>
                        <!-- 数量 -->
                        <div class="charge-list-advice-list-item-social-total-price">
                            数量
                        </div>
                        <!-- 金额 -->
                        <div class="charge-list-advice-list-item-social-total-price">
                            金额
                        </div>
                    </div>
                </div>

                <div
                    class="charge-list-advice-list"
                    style="padding: 0 0 4px;"
                    data-type="group"
                >
                    <template
                        v-for="(adviceTypePrint, adviceTypePrintIdx) in itemGroupByAdviceTypePrints"
                    >
                        <!-- 药品、物资、商品、直接开出的费用项 -->
                        <template v-if="[TreatmentTypeEnum.UN_KNOW, TreatmentTypeEnum.MEDICINE, TreatmentTypeEnum.MATERIALS].includes(adviceTypePrint.diagnosisTreatmentType) || isNull(adviceTypePrint.diagnosisTreatmentType)">
                            <template v-for="(advice, adviceIdx) in (adviceTypePrint.hisChargeFormItemGroupByAdvicePrintInfos || [])">
                                <template v-for="(chargeForm, chargeFormIdx) in (advice.hisChargeFormItemPrintInfoList || [])">
                                    <div
                                        v-if="!config.isPrintNonZeroItem || chargeForm.totalPrice"
                                        :key="`charge-list-advice-list-item-${adviceTypePrintIdx}-${adviceIdx}-${chargeFormIdx}`"
                                        class="charge-list-advice-list-item"
                                        data-type="item"
                                    >
                                        <!-- 名称 -->
                                        <div class="charge-list-advice-list-item-name">
                                            {{ chargeForm.name || '' }}
                                        </div>
                                        <!-- 医保等级 -->
                                        <div
                                            v-if="config.medicalGrade"
                                            class="charge-list-advice-list-item-medical-grade"
                                        >
                                            <template v-if="chargeForm.shebaoGoodsItem && isNotNull(chargeForm.shebaoGoodsItem.medicalFeeGrade)">
                                                {{ chargeForm.shebaoGoodsItem.medicalFeeGrade | medicalFeeGrade2PrintStr }}{{ filterOwnExpenseRatio(chargeForm.shebaoGoodsItem.ownExpenseRatio) }}
                                            </template>
                                        </div>
                                        <!-- 医保码 -->
                                        <div
                                            v-if="config.code"
                                            class="charge-list-advice-list-item-social-code"
                                        >
                                            <template
                                                v-if="!inHospital && chargeForm.shebaoGoodsItem && chargeForm.shebaoGoodsItem.socialCode !== 'DISABLED'"
                                            >
                                                {{ chargeForm.shebaoGoodsItem.socialCode || '' }}
                                            </template>
                                            <template
                                                v-if="inHospital && chargeForm.goodsSnapshotVersion && chargeForm.goodsSnapshotVersion.shebao && chargeForm.goodsSnapshotVersion.shebao.nationalCode !== 'DISABLED'"
                                            >
                                                {{ chargeForm.goodsSnapshotVersion.shebao.nationalCode || '' }}
                                            </template>
                                        </div>
                                        <!-- 规格 -->
                                        <div
                                            v-if="config.spec"
                                            class="charge-list-advice-list-item-social-spec"
                                        >
                                            <template v-if="isShowDisplaySpec(chargeForm) && chargeForm.productInfoSnapshot">
                                                {{ chargeForm.productInfoSnapshot.displaySpec || '' }}
                                            </template>
                                        </div>
                                        <!-- 单位 -->
                                        <div class="charge-list-advice-list-item-social-unit">
                                            {{ chargeForm.unit || '' }}
                                        </div>
                                        <!-- 单价 -->
                                        <div
                                            v-if="config.unitPrice"
                                            class="charge-list-advice-list-item-social-total-price"
                                        >
                                            {{ chargeForm.unitPrice | formatMoney }}
                                        </div>
                                        <!-- 数量 -->
                                        <div class="charge-list-advice-list-item-social-total-price">
                                            {{ chargeForm.count }}
                                        </div>
                                        <!-- 金额 -->
                                        <div class="charge-list-advice-list-item-social-total-price">
                                            {{ chargeForm.totalPrice | formatMoney }}
                                        </div>
                                    </div>
                                </template>
                            </template>
                        </template>

                        <!-- 医嘱 -->
                        <template v-else>
                            <template v-for="(advice, adviceIdx) in (adviceTypePrint.hisChargeFormItemGroupByAdvicePrintInfos || [])">
                                <div
                                    v-if="!config.isPrintNonZeroItem || advice.totalPrice"
                                    :key="`charge-list-advice-list-item-${adviceTypePrintIdx}-${adviceIdx}`"
                                    class="charge-list-advice-list-item"
                                    data-type="item"
                                >
                                    <!-- 名称 -->
                                    <div class="charge-list-advice-list-item-name">
                                        {{ advice.adviceName || '' }}
                                    </div>
                                    <!-- 医保等级 -->
                                    <div
                                        v-if="config.medicalGrade"
                                        class="charge-list-advice-list-item-medical-grade"
                                    >
                                    </div>
                                    <!-- 医保码 -->
                                    <div
                                        v-if="config.code"
                                        class="charge-list-advice-list-item-social-code"
                                    >
                                    </div>
                                    <!-- 规格 -->
                                    <div
                                        v-if="config.spec"
                                        class="charge-list-advice-list-item-social-spec"
                                    >
                                    </div>
                                    <!-- 单位 -->
                                    <div class="charge-list-advice-list-item-social-unit">
                                        {{ advice.unit || '' }}
                                    </div>
                                    <!-- 单价 -->
                                    <div
                                        v-if="config.unitPrice"
                                        class="charge-list-advice-list-item-social-total-price"
                                    >
                                        {{ advice.unitPrice | formatMoney }}
                                    </div>
                                    <!-- 数量 -->
                                    <div class="charge-list-advice-list-item-social-total-price">
                                        {{ advice.unitCount }}
                                    </div>
                                    <!-- 金额 -->
                                    <div class="charge-list-advice-list-item-social-total-price">
                                        {{ advice.totalPrice | formatMoney }}
                                    </div>
                                </div>
                            </template>
                        </template>
                    </template>
                </div>
            </template>

            <!-- 费用项目 -->
            <template v-else>
                <template v-for="(printForm, printFormIndex) in itemGroupByGoodsTypePrintInfos">
                    <div
                        v-if="!config.isPrintNonZeroItem || printForm.totalPrice"
                        :key="printFormIndex"
                        data-type="group"
                        class="fee-item-table"
                    >
                        <div
                            class="table-header"
                            data-type="item"
                        >
                            <div
                                class="table-td item-name item-title"
                                :style="{ width: `${itemNameWidth}%` }"
                            >
                                {{ printForm.goodsTypeName }}
                            </div>
                            <div
                                v-if="config.code"
                                class="table-td item-code"
                            >
                                国家代码
                            </div>
                            <div
                                v-if="config.spec"
                                class="table-td item-display-spec"
                            >
                                规格
                            </div>
                            <div class="table-td item-unit">
                                单位
                            </div>
                            <div
                                v-if="config.unitPrice"
                                class="table-td item-unit-price"
                            >
                                单价
                            </div>
                            <div class="table-td item-unit-count">
                                数量
                            </div>
                            <div class="table-td item-total-price">
                                金额
                            </div>
                        </div>
                        <template v-for="(hisChargeFormItem, hisChargeFormItemIndex) in printForm.hisChargeFormItemPrintInfoList">
                            <div
                                v-if="!config.isPrintNonZeroItem || hisChargeFormItem.totalPrice"
                                :key="hisChargeFormItemIndex"
                                data-type="item"
                                class="table-body"
                            >
                                <div
                                    class="table-td item-name"
                                    :style="{ width: `${itemNameWidth}%` }"
                                >
                                    <template v-if="config.medicalGrade && hisChargeFormItem.shebaoGoodsItem && hisChargeFormItem.shebaoGoodsItem.socialCode">
                                        [{{ hisChargeFormItem.shebaoGoodsItem.medicalFeeGrade | medicalFeeGrade2PrintStr }}{{ filterOwnExpenseRatio(hisChargeFormItem.shebaoGoodsItem.ownExpenseRatio) }}]
                                    </template>
                                    {{ hisChargeFormItem.name }}
                                </div>
                                <div
                                    v-if="config.code"
                                    class="table-td item-code"
                                    overflow
                                >
                                    <template
                                        v-if="!inHospital && hisChargeFormItem.shebaoGoodsItem && hisChargeFormItem.shebaoGoodsItem.socialCode !== 'DISABLED'"
                                    >
                                        {{ hisChargeFormItem.shebaoGoodsItem.socialCode }}
                                    </template>
                                    <template
                                        v-if="inHospital && hisChargeFormItem.goodsSnapshotVersion && hisChargeFormItem.goodsSnapshotVersion.shebao && hisChargeFormItem.goodsSnapshotVersion.shebao.nationalCode !== 'DISABLED'"
                                    >
                                        {{ hisChargeFormItem.goodsSnapshotVersion.shebao.nationalCode }}
                                    </template>
                                </div>
                                <div
                                    v-if="config.spec"
                                    class="table-td item-display-spec"
                                    overflow
                                >
                                    <template v-if="isShowDisplaySpec(hisChargeFormItem)">
                                        {{ hisChargeFormItem.productInfoSnapshot && hisChargeFormItem.productInfoSnapshot.displaySpec }}
                                    </template>
                                </div>
                                <div
                                    class="table-td item-unit"
                                    overflow
                                >
                                    {{ hisChargeFormItem.unit }}
                                </div>
                                <div
                                    v-if="config.unitPrice"
                                    class="table-td item-unit-price"
                                    overflow
                                >
                                    {{ hisChargeFormItem.unitPrice | formatMoney }}
                                </div>
                                <div
                                    class="table-td item-unit-count"
                                    overflow
                                >
                                    {{ hisChargeFormItem.count }}
                                </div>
                                <div
                                    class="table-td item-total-price"
                                    overflow
                                >
                                    {{ hisChargeFormItem.totalPrice | formatMoney }}
                                </div>
                            </div>
                        </template>
                        <div
                            data-type="item"
                            class="table-footer"
                        >
                            合计：{{ printForm.totalPrice | formatMoney }}
                        </div>
                    </div>
                </template>
            </template>
        </div>

        <!-- 费用类型 -->
        <div
            v-if="config.feeType"
            data-type="mix-box"
        >
            <div
                data-type="group"
                class="fee-item-table"
            >
                <div
                    class="table-header"
                    data-type="item"
                >
                    <div class="table-td item-title">
                        费用类型
                    </div>
                </div>
                <div
                    v-for="(goodsFeeTypeForm, goodsFeeTypeFormIndex) in goodsFeeTypeList"
                    :key="goodsFeeTypeFormIndex"
                    data-type="item"
                    class="table-body"
                >
                    <div
                        v-for="(goodsFeeTypeFormItem, goodsFeeTypeFormItemIndex) in goodsFeeTypeForm"
                        :key="goodsFeeTypeFormItemIndex"
                        class="table-td item-fee-type"
                    >
                        <div
                            class="fee-type-item"
                            overflow
                        >
                            {{ goodsFeeTypeFormItem.goodsTypeName }}
                        </div>
                        <div
                            class="fee-type-item fee-type-item-right"
                            overflow
                        >
                            {{ goodsFeeTypeFormItem.totalFee | formatMoney }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 结算明细 -->
        <div
            v-if="!inHospital && config.settlementDetail"
            data-type="mix-box"
        >
            <div
                data-type="group"
                class="fee-item-table"
            >
                <div
                    class="table-header"
                    data-type="item"
                >
                    <div class="table-td item-title">
                        结算明细
                    </div>
                </div>
                <div
                    v-for="(list, listIndex) in shebaoListInfo"
                    :key="listIndex"
                    data-type="item"
                    class="table-body"
                >
                    <div
                        v-for="(item, itemIndex) in list"
                        :key="`${listIndex}-${itemIndex}`"
                        class="table-td item-fee-type"
                        :style="item.isPureString ? (isHangzhou ? { width: '71%' } : isChongqing ? { width: '100%' } : {}) : {}"
                    >
                        <div
                            class="fee-type-item"
                            :style="item.isPureString ? (isHangzhou || isChongqing ? { width: 'auto' } : {}) : {}"
                        >
                            {{ item.label }}
                        </div>
                        <div
                            class="fee-type-item fee-type-item-right"
                            :style="item.isPureString ? (isHangzhou || isChongqing ? { width: 'auto', 'margin-left': '27pt' } : { overflow: 'visible', 'white-space': 'normal' }) : {}"
                        >
                            <template v-if="item.isPureString">
                                {{ item.value }}
                            </template>
                            <template v-else>
                                {{ item.value | formatMoney }}
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div
            data-type="footer"
            class="footer"
        >
            <div class="charge-list-footer">
                <div class="charge-list-footer-content">
                    <div v-if="config.sellerName">
                        收费员：{{ printData.sellerName }}
                    </div>
                    <div v-if="config.chargeTime">
                        收费日期：{{ parseTime(printData.chargeTime, 'y-m-d') }}
                    </div>
                    <div
                        v-if="config.chargeClinic"
                        class="charge-list-page-institution"
                    >
                        收费单位：{{ pageInstitution }}
                    </div>
                    <div
                        v-if="config.patientSignature"
                        class="charge-list-patient-signature"
                    >
                        患者签字：
                    </div>
                </div>
                
                <div
                    v-if="isPreview"
                    class="pagination"
                >
                    1/1页
                </div>
                <div
                    v-else
                    class="pagination"
                >
                    <span data-page-no="PageNo"></span>
                    <span>/</span>
                    <span data-page-count="PageCount"></span>
                    <span>页</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import PrintCommonDataHandler from "./data-handler/common-handler";
    import { PrintBusinessKeyEnum } from "./constant/print-constant";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import {
        filterOwnExpenseRatio,
        formatAge,
        formatMoney, getLengthWithFullCharacter, isNotNull, isNull, isShowDisplaySpec,
        parseTime,
    } from "./common/utils";
    import Clone from "./common/clone";
    import { TITLE_MAX_LENGTH, TreatmentTypeEnum } from "./common/constants";

    const initPrintConfig = {
        'title': '', // 抬头名称 字符串必填 String
        'subtitle': '', // 副抬头名称 可选值 String
        'medicalGrade': 1, // 医保等级, 可选值 0 1, 默认 1
        'code': 1, // 国家代码, 可选值 0 1, 默认 1
        'spec': 1, // 规格, 可选值 0 1, 默认 1
        'unit': 1, // 单位, 可选值 0 1, 默认 1
        'unitPrice': 1, // 单价, 可选值 0 1, 默认 1
        'count': 1, // 数量, 可选值 0 1, 默认 1
        'feeInfo': 1, // 收费项目, 可选值 0 1, 默认 1
        'feeType': 1, // 费用类型, 可选值 0 1, 默认 1
        'settlementDetail': 1, // 结算明细, 可选值 0 1, 默认 1
        'sellerName': 1, // 收费员, 可选值 0 1, 默认 1
        'chargeTime': 1, // 收费日期, 可选值 0 1, 默认 1
        'chargeClinic': 1, // 收费单位, 可选值 0 1, 默认 1
        'chargeClinicContent': '', // 收费单位, 默认值同 title, String
        'isPrintNonZeroItem': 0, // 不打印费用为0的项目, 可选值0 1, 默认 0
    };

    export default {
        name: 'ChargeList',
        DataHandler: PrintCommonDataHandler,
        filters: {
            formatMoney,
            medicalFeeGrade2PrintStr(medicalFeeGrade) {
                if(!medicalFeeGrade) return '';
                switch (medicalFeeGrade) {
                    case 1:
                        return '甲 ';
                    case 2:
                        return '乙 ';
                    case 3:
                        return '丙 ';
                    default:
                        return '';
                }
            },
            toFixed(val) {
                return val ? val : '0';
            },
        },
        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
            extra: {
                type: Object,
                default: () => ({}),
            },
        },
        businessKey: PrintBusinessKeyEnum.CHARGE_LIST,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        data() {
            return {
                TreatmentTypeEnum,
            };
        },
        computed: {
            isPreview() {
                return this.extra.isPreview;
            },
            printData() {
                return this.renderData.printData || {};
            },
            config() {
                if (this.renderData.config?.hospitalFeeBills?.chargeFeeList) {
                    return this.renderData.config.hospitalFeeBills.chargeFeeList;
                }
                return initPrintConfig;
            },
            clinicName() {
                return this.printData.clinicName || '';
            },
            inHospital() {
                return this.printData.inHospital || false;
            },
            // 处理最大字数后的标题
            pageTitle() {
                const configTitle = this.config.title || this.clinicName;
                const {
                    fullCharacterLength, splitLength,
                } = getLengthWithFullCharacter(configTitle, TITLE_MAX_LENGTH);
                if (fullCharacterLength > TITLE_MAX_LENGTH) {
                    return configTitle.slice(0, splitLength);
                }
                return configTitle;
            },
            pageInstitution() {
                return this.config.chargeClinicContent || this.clinicName;
            },
            nationalCode() {
                return this.printData.nationalCode || '';
            },
            patientOrderInfo() {
                return this.printData.patientOrderInfo || {};
            },
            patient() {
                return this.patientOrderInfo.patient || {};
            },
            itemGroupByGoodsTypePrintInfos() {
                return this.printData.itemGroupByGoodsTypePrintInfos || [];
            },
            itemGroupByAdviceTypePrints() {
                return this.printData.itemGroupByAdviceTypePrints || [];
            },
            shebaoPayment() {
                return this.printData.shebaoPayment || {};
            },
            region() {
                return (this.shebaoPayment && this.shebaoPayment.region) || '';
            },
            isHangzhou() {
                return this.region.indexOf('zhejiang_') === 0;
            },
            isChongqing() {
                return this.region.includes('chongqing');
            },
            shebaoExtraInfo() {
                return this.shebaoPayment.extraInfo || {};
            },
            goodsFeeTypeList() {
                const result = [];
                let cache = [];
                this.itemGroupByGoodsTypePrintInfos.forEach((printForm, index) => {
                    if (index !== 0 && index % 4 === 0) {
                        result.push(Clone(cache));
                        cache = [];
                    }
                    cache.push({
                        goodsTypeName: printForm.goodsTypeName,
                        totalFee: printForm.totalPrice,
                    });
                });
                if (cache.length) {
                    result.push(Clone(cache));
                }
                return result;
            },
            itemNameWidth() {
                let width = 38;
                if (!this.config.code) {
                    width += 24;
                }
                if (!this.config.spec) {
                    width += 10;
                }
                if (!this.config.unitPrice) {
                    width += 7;
                }
                return width;
            },
            shebaoListInfo() {
                const list = [];

                list.push({
                    label: '费用合计',
                    value: this.printData.totalFee,
                });
                list.push({
                    label: '预缴金额',
                    value: this.printData.depositReceivedFee,
                });
                list.push({
                    label: '补缴金额',
                    value: this.printData.supplyPaymentFee,
                });
                list.push({
                    label: '退费金额',
                    value: this.printData.refundFee,
                });
                list.push({
                    label: '起付线',
                    value: this.shebaoExtraInfo.actPayDedc,
                });
                list.push({
                    label: '统筹支付比例',
                    value: this.shebaoExtraInfo.poolPropSelfpay,
                });
                list.push({
                    label: '统筹基金支付',
                    value: this.shebaoPayment.fundPaymentFee,
                });
                if (this.isHangzhou) {
                    list.push({
                        label: '当年支付',
                        value: this.shebaoExtraInfo.curYearAccountPaymentFee,
                    });
                    list.push({
                        label: '当年余额',
                        value: this.shebaoExtraInfo.curYearBalance,
                    });
                    list.push({
                        label: '历年支付',
                        value: this.shebaoExtraInfo.allYearAccountPaymentFee,
                    });
                    list.push({
                        label: '历年余额',
                        value: this.shebaoExtraInfo.allYearBalance,
                    });
                } else {
                    list.push({
                        label: '个人账户支付',
                        value: this.shebaoPayment.accountPaymentFee,
                    });
                    list.push({
                        label: '支付后余额',
                        value: this.shebaoPayment.cardBalance,
                    });
                }
                list.push({
                    label: '共济账户支付',
                    value: this.shebaoExtraInfo.acctMulaidPay,
                });
                if (this.isChongqing) {
                    list.push({
                        label: '医院负担金额',
                        value: parseFloat(this.shebaoExtraInfo.hospPartAmt),
                    });
                }
                // 现金支付
                const cashFee = parseFloat(this.printData.cashFee);
                // 医院负担金额
                const hospPartAmt = parseFloat(this.shebaoExtraInfo.hospPartAmt);
                // 如果医院负担金额为负数, 则展示的现金支付为原现金支付减去医院负担金额
                const resCashPay = isNaN(cashFee) ? 0 : isNaN(hospPartAmt) ? cashFee : hospPartAmt < 0 ? cashFee + hospPartAmt : cashFee;
                list.push({
                    label: '现金支付',
                    value: resCashPay,
                });
                list.push({
                    label: '医保区划',
                    value: this.shebaoExtraInfo.insuplcAdmdvsName,
                    isPureString: true,
                });

                const res = [];
                let items = [];
                list.forEach((item, index) => {
                    if (index !== 0 && index % 4 === 0) {
                        res.push(items);
                        items = [];
                    }
                    items.push(item);
                });
                if (items.length) {
                    res.push(items);
                }

                return res;
            },
        },
        methods: {
            isNull,
            isNotNull,
            parseTime,
            formatAge,
            filterOwnExpenseRatio,
            isShowDisplaySpec,
        },
    }
</script>

<style lang="scss">
.charge-list {
    .header {
        padding: 16px 0 8px 0;

        .header-patient-title {
            margin-bottom: 16px;

            .charge-list-title {
                font-family: SimSun;
                font-size: 20px;
                font-weight: bolder;
                line-height: 1;
                text-align: center;
                letter-spacing: 2px;
            }

            .sub-title {
                margin-top: 5px;
                font-family: SimSun;
                font-size: 16px;
                line-height: 1;
                text-align: center;
            }
        }

        .header-patient-form {
            line-height: 18px;
            word-spacing: -99px;

            &.margin-top-6 {
                margin-top: 4px;
            }

            .header-patient-item {
                display: inline-block;
                overflow: hidden;
                font-size: 11px;
                line-height: 16px;
                text-overflow: clip;
                white-space: nowrap;
                vertical-align: middle;

                &.width-40 {
                    width: 40%;
                }

                &.width-35 {
                    width: 35%;
                }

                &.width-25 {
                    width: 25%;
                }

                .margin-left-6 {
                    margin-left: 8px;
                }
            }
        }
    }

    .fee-item-table {
        padding: 8px 0 4px;
        border-top: 1px solid #a6a6a6;

        .table-header {
            line-height: 16px;
            vertical-align: top;
            word-spacing: -99px;
        }

        .table-body {
            display: table;
            width: 100%;
            line-height: 16px;
            vertical-align: top;
            word-spacing: -99px;
        }

        .table-footer {
            font-size: 11px;
            font-weight: bold;
            line-height: 16px;
            text-align: right;
        }

        .table-td {
            display: inline-block;
            overflow: hidden;
            font-size: 11px;
            line-height: 16px;
            text-overflow: clip;
            white-space: nowrap;
            vertical-align: top;
            word-spacing: 0;

            &.item-title {
                font-weight: bold;
            }

            &.item-name {
                box-sizing: border-box;
                width: 38%;
                padding-right: 20px;
                white-space: normal;
            }

            &.item-code {
                box-sizing: border-box;
                width: 24%;
                padding-right: 5px;
            }

            &.item-display-spec {
                box-sizing: border-box;
                width: 10%;
                padding-right: 5px;
                text-align: left;
            }

            &.item-unit {
                width: 7%;
                text-align: left;
            }

            &.item-unit-price {
                width: 7%;
                text-align: right;
            }

            &.item-unit-count {
                width: 7%;
                text-align: right;
            }

            &.item-total-price {
                width: 7%;
                text-align: right;
            }

            &.item-fee-type {
                width: 18%;
                line-height: 16px;
                word-spacing: -99px;

                &:not(:first-child) {
                    margin-left: 9.2%;
                }

                .fee-type-item {
                    display: inline-block;
                    width: 58%;
                    overflow: hidden;
                    text-overflow: clip;
                    white-space: nowrap;
                    vertical-align: top;

                    &.fee-type-item-right {
                        width: 42%;
                        text-align: right;
                    }
                }
            }
        }
    }

    .footer {
        padding-top: 8px;
        font-size: 11px;
        line-height: 16px;

        .charge-list-footer {
            position: relative;
            height: 16px;
            padding-top: 8px;
            font-size: 11px;
            line-height: 16px;
            border-top: 1px solid #a6a6a6;

            .margin-left-32 {
                margin-left: 5%;
            }

            .pagination {
                position: absolute;
                top: 8px;
                right: 0;
                word-spacing: -99px;
            }
        }

        .charge-list-footer-content {
            position: relative;
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .charge-list-page-institution {
            overflow: hidden;
            white-space: nowrap;
        }

        .charge-list-patient-signature {
            position: absolute;
            top: 0;
            right: 48px;
            padding-right: 6em;
        }
    }

    .charge-list-advice-list {
        display: flex;
        flex-direction: column;
        width: 100%;
        font-size: 11px;
        line-height: 16px;
    }

    .charge-list-advice-list-item {
        display: flex;
        align-items: flex-start;
        width: 100%;
    }

    .charge-list-advice-list-item-name {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        align-items: flex-start;
        padding-right: 4px;
        word-break: break-all;
        word-wrap: break-word;
    }

    .charge-list-advice-list-item-social-code {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        width: 25%;
        padding-right: 4px;
        word-break: break-all;
        word-wrap: break-word;
    }

    .charge-list-advice-list-item-medical-grade {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
        width: 7%;
        padding-right: 4px;
        overflow: hidden;
    }

    .charge-list-advice-list-item-social-spec {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        width: 11%;
        padding-right: 4px;
        word-break: break-all;
        word-wrap: break-word;
    }

    .charge-list-advice-list-item-social-unit {
        display: flex;
        align-items: flex-start;
        width: 7%;
    }

    .charge-list-advice-list-item-social-total-price {
        display: flex;
        align-items: flex-start;
        justify-content: flex-end;
        width: 7%;
    }
}
</style>
