
<!--exampleData
{
	"id": 1466,
	"orderNo": "PD2021072200001",
	"afterCostAmount": 0,
	"afterCostAmountExcludingTax": 0,
	"afterCount": 0,
	"afterSaleAmount": 0,
	"afterSaleAmountExcludingTax": 0,
	"beforeCostAmount": 49928348.0429,
	"beforeCostAmountExcludingTax": 49927530.4016,
	"beforeCount": 542325.1429,
	"beforeSaleAmount": 68572034.29,
	"beforeSaleAmountExcludingTax": 68567661.8763,
	"totalCostPriceChange": -49928348,
	"totalPriceChange": -68572034.29,
	"createdDate": "2021-07-22T08:05:12Z",
	"createdUser": {
		"id": "6e45706922a74966ab51e4ed1e604641",
		"name": "刘喜"
	},
	"kindCount": 20,
	"stockCheckScope": null,
	"status": 30,
	"statusName": "已完成",
	"comment": null,
	"organ": {
		"clinicId": "fff730ccc5ee45d783d82a85b8a0e52d",
		"id": "fff730ccc5ee45d783d82a85b8a0e52d",
		"chainId": "********************************",
		"parentId": "********************************",
		"name": "成都高新大源店",
		"shortName": "高新大源店",
		"nodeType": 2,
		"shortNameFirst": "高新大源店"
	},
	"list": [{
			"id": "73824",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "fc4f310380f146b8b72ab79ec24a41b0",
			"goods": {
				"id": "fc4f310380f146b8b72ab79ec24a41b0",
				"goodsId": "fc4f310380f146b8b72ab79ec24a41b0",
				"shortId": "4292713732",
				"status": 1,
				"py": "EBSC|EFSC|EBYC|EFYC|ebushicao|efoushicao|ebusicao|efousicao|ebuyicao|efouyicao",
				"organId": "********************************",
				"type": 1,
				"subType": 2,
				"medicineCadn": "鹅不食草",
				"medicineDosageNum": null,
				"materialSpec": "中药饮片",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "g",
				"dismounting": 1,
				"pieceNum": 1,
				"piecePrice": 0.1,
				"packagePrice": 0.1,
				"packageCostPrice": 0,
				"inTaxRat": 16,
				"outTaxRat": 16,
				"createdUserId": "74183cc16afd4920ad955a7a1c793ec9",
				"createdDate": "2018-07-03T14:12:15Z",
				"lastModifiedUserId": "74183cc16afd4920ad955a7a1c793ec9",
				"lastModifiedDate": "2018-07-03T14:18:57Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 14,
				"customTypeId": 0,
				"chainPiecePrice": 0.1,
				"chainPackagePrice": 0.1,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "中药饮片"
			},
			"pieceNum": 1,
			"piecePrice": 0.1,
			"packagePrice": 0.1,
			"packageCostPrice": 0.0187,
			"beforePieceCount": 317000,
			"beforePackageCount": 0,
			"totalCostPriceChange": -5927.9,
			"totalPriceChange": -31700,
			"detail": [{
				"id": null,
				"batchNo": "",
				"batchId": "140400",
				"stockId": 140400,
				"goodsId": "fc4f310380f146b8b72ab79ec24a41b0",
				"pieceNum": 1,
				"beforePackageCount": 0,
				"beforePieceCount": 317000,
				"packageCostPrice": 0.0187,
				"totalCostPriceChange": -5927.9,
				"totalPriceChange": -31700,
				"packageCount": 0,
				"pieceCount": 0,
				"packageCountChange": 0,
				"pieceCountChange": -317000
			}],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": -317000,
			"packageCountChange": 0
		},
		{
			"id": "73825",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a0e00a7b8000",
			"goods": {
				"id": "ffffffff000000001078a0e00a7b8000",
				"goodsId": "ffffffff000000001078a0e00a7b8000",
				"shortId": "4444450118",
				"status": 1,
				"name": "赤尾（天然胶乳橡胶避孕套）黄金超薄小储精囊",
				"py": "CW（TRJRXJBYT）HJCBXCJ|CY（TRJRXJBYT）HJCBXCJ|chiwei（tianranjiaoruxiangjiaobiyuntao）huangjinchaobaoxiaochujing|chiyi（tianranjiaoruxiangjiaobiyuntao）huangjinchaobaoxiaochujing|chiwei（tianranjiaoruxiangjiaobiyuntao）huangjinchaoboxiaochujing|chiyi（tianranjiaoruxiangjiaobiyuntao）huangjinchaoboxiaochujing|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "广州万方健",
				"manufacturerFull": "广州万方健医药有限公司",
				"medicineDosageNum": null,
				"materialSpec": "WK 7只",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "盒",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 1,
				"piecePrice": 0,
				"packagePrice": 29.8,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:10:19Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:10:19Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 29.8,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "WK 7只"
			},
			"pieceNum": 1,
			"piecePrice": null,
			"packagePrice": 29.8,
			"packageCostPrice": 14.9,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -223500,
			"totalPriceChange": -447000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a0e00a7b8000",
					"pieceNum": 1,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 14.9,
					"totalCostPriceChange": -74500,
					"totalPriceChange": -149000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005199",
					"stockId": 100005199,
					"goodsId": "ffffffff000000001078a0e00a7b8000",
					"pieceNum": 1,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 14.9,
					"totalCostPriceChange": -74500,
					"totalPriceChange": -149000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006077",
					"stockId": 100006077,
					"goodsId": "ffffffff000000001078a0e00a7b8000",
					"pieceNum": 1,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 14.9,
					"totalCostPriceChange": -74500,
					"totalPriceChange": -149000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73826",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a2880a7b8004",
			"goods": {
				"id": "ffffffff000000001078a2880a7b8004",
				"goodsId": "ffffffff000000001078a2880a7b8004",
				"shortId": "4444450256",
				"status": 1,
				"name": "稳健医疗（酒精消毒片/全棉型）",
				"py": "WJYL（JJXDP/QMX）|wenjianyiliao（jiujingxiaodupian/quanmianxing）|wenjianyiliao（jiujingxiaodaipian/quanmianxing）|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "稳健医疗（嘉鱼）",
				"manufacturerFull": "稳健医疗（嘉鱼）有限公司",
				"medicineDosageNum": null,
				"materialSpec": "3cm*6cm/袋 50袋/盒",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "片",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 50,
				"piecePrice": 0,
				"packagePrice": 15.8,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:11:12Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:11:12Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 15.8,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "3cm*6cm/袋 50袋/盒"
			},
			"pieceNum": 50,
			"piecePrice": null,
			"packagePrice": 15.8,
			"packageCostPrice": 6.32,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -94800,
			"totalPriceChange": -237000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a2880a7b8004",
					"pieceNum": 50,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 6.32,
					"totalCostPriceChange": -31600,
					"totalPriceChange": -79000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005337",
					"stockId": 100005337,
					"goodsId": "ffffffff000000001078a2880a7b8004",
					"pieceNum": 50,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 6.32,
					"totalCostPriceChange": -31600,
					"totalPriceChange": -79000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006215",
					"stockId": 100006215,
					"goodsId": "ffffffff000000001078a2880a7b8004",
					"pieceNum": 50,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 6.32,
					"totalCostPriceChange": -31600,
					"totalPriceChange": -79000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73827",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a4800a7b8000",
			"goods": {
				"id": "ffffffff000000001078a4800a7b8000",
				"goodsId": "ffffffff000000001078a4800a7b8000",
				"shortId": "4444450417",
				"status": 1,
				"name": "抗HPV生物蛋白敷料",
				"py": "KHPVSWDBFL|kangHPVshengwudanbaifuliao|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "山西锦波生物",
				"manufacturerFull": "山西锦波生物医药股份有限公司",
				"medicineDosageNum": null,
				"materialSpec": "TYC1:3g/支，1支/盒",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "g",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 3,
				"piecePrice": 0,
				"packagePrice": 320,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:12:15Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:12:15Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 320,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "TYC1:3g/支，1支/盒"
			},
			"pieceNum": 3,
			"piecePrice": null,
			"packagePrice": 320,
			"packageCostPrice": 281.6,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -4224000,
			"totalPriceChange": -4800000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a4800a7b8000",
					"pieceNum": 3,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 281.6,
					"totalCostPriceChange": -1408000,
					"totalPriceChange": -1600000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005498",
					"stockId": 100005498,
					"goodsId": "ffffffff000000001078a4800a7b8000",
					"pieceNum": 3,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 281.6,
					"totalCostPriceChange": -1408000,
					"totalPriceChange": -1600000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006376",
					"stockId": 100006376,
					"goodsId": "ffffffff000000001078a4800a7b8000",
					"pieceNum": 3,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 281.6,
					"totalCostPriceChange": -1408000,
					"totalPriceChange": -1600000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73828",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a5600a7b8008",
			"goods": {
				"id": "ffffffff000000001078a5600a7b8008",
				"goodsId": "ffffffff000000001078a5600a7b8008",
				"shortId": "4444450494",
				"status": 1,
				"name": "穴位压力刺激贴（暖贴）",
				"py": "XWYLCJT（NT）|xueweiyalicijitie（nuantie）|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "青岛千江生物工程",
				"manufacturerFull": "青岛千江生物工程有限公司",
				"medicineDosageNum": null,
				"materialSpec": "NT穴位型10*13cm*10贴",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "cm*10贴",
				"packageUnit": "袋",
				"dismounting": 0,
				"pieceNum": 13,
				"piecePrice": 0,
				"packagePrice": 14.8,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:12:43Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:12:43Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 14.8,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "NT穴位型10*13cm*10贴"
			},
			"pieceNum": 13,
			"piecePrice": null,
			"packagePrice": 14.8,
			"packageCostPrice": 8,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -120000,
			"totalPriceChange": -222000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a5600a7b8008",
					"pieceNum": 13,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 8,
					"totalCostPriceChange": -40000,
					"totalPriceChange": -74000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005575",
					"stockId": 100005575,
					"goodsId": "ffffffff000000001078a5600a7b8008",
					"pieceNum": 13,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 8,
					"totalCostPriceChange": -40000,
					"totalPriceChange": -74000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006453",
					"stockId": 100006453,
					"goodsId": "ffffffff000000001078a5600a7b8008",
					"pieceNum": 13,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 8,
					"totalCostPriceChange": -40000,
					"totalPriceChange": -74000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73829",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a2100a7b8000",
			"goods": {
				"id": "ffffffff000000001078a2100a7b8000",
				"goodsId": "ffffffff000000001078a2100a7b8000",
				"shortId": "4444450216",
				"status": 1,
				"name": "明康牌折叠手动轮椅车",
				"py": "MKPZDSDLYC|MKPSDSDLYC|MKPZDSDLYJ|MKPSDSDLYJ|mingkangpaizhedieshoudonglunyiche|mingkangpaishedieshoudonglunyiche|mingkangpaizhedieshoudonglunyiju|mingkangpaishedieshoudonglunyiju|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "天津市助邦医疗器械",
				"manufacturerFull": "天津市助邦医疗器械有限公司",
				"medicineDosageNum": null,
				"materialSpec": "SYIV100-ZB-08",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "-zb-08",
				"packageUnit": "台",
				"dismounting": 0,
				"pieceNum": 100,
				"piecePrice": 0,
				"packagePrice": 480,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:10:57Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:10:57Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 480,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "SYIV100-ZB-08"
			},
			"pieceNum": 100,
			"piecePrice": null,
			"packagePrice": 480,
			"packageCostPrice": 350,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -5250000,
			"totalPriceChange": -7200000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a2100a7b8000",
					"pieceNum": 100,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 350,
					"totalCostPriceChange": -1750000,
					"totalPriceChange": -2400000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005297",
					"stockId": 100005297,
					"goodsId": "ffffffff000000001078a2100a7b8000",
					"pieceNum": 100,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 350,
					"totalCostPriceChange": -1750000,
					"totalPriceChange": -2400000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006175",
					"stockId": 100006175,
					"goodsId": "ffffffff000000001078a2100a7b8000",
					"pieceNum": 100,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 350,
					"totalCostPriceChange": -1750000,
					"totalPriceChange": -2400000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73830",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a5d00a7b8002",
			"goods": {
				"id": "ffffffff000000001078a5d00a7b8002",
				"goodsId": "ffffffff000000001078a5d00a7b8002",
				"shortId": "4444450532",
				"status": 1,
				"name": "康祝(真空拔罐器)",
				"py": "KZ(ZKBGQ)|kangzhu(zhenkongbaguanqi)|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "北京康祝医疗器械",
				"manufacturerFull": "北京康祝医疗器械有限公司",
				"medicineDosageNum": null,
				"materialSpec": "12罐",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "罐",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 12,
				"piecePrice": 0,
				"packagePrice": 38.5,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:12:57Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:12:57Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 38.5,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "12罐"
			},
			"pieceNum": 12,
			"piecePrice": null,
			"packagePrice": 38.5,
			"packageCostPrice": 28.5,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -427500,
			"totalPriceChange": -577500,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a5d00a7b8002",
					"pieceNum": 12,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 28.5,
					"totalCostPriceChange": -142500,
					"totalPriceChange": -192500,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005613",
					"stockId": 100005613,
					"goodsId": "ffffffff000000001078a5d00a7b8002",
					"pieceNum": 12,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 28.5,
					"totalCostPriceChange": -142500,
					"totalPriceChange": -192500,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006491",
					"stockId": 100006491,
					"goodsId": "ffffffff000000001078a5d00a7b8002",
					"pieceNum": 12,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 28.5,
					"totalCostPriceChange": -142500,
					"totalPriceChange": -192500,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73831",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a2c00a7b8000",
			"goods": {
				"id": "ffffffff000000001078a2c00a7b8000",
				"goodsId": "ffffffff000000001078a2c00a7b8000",
				"shortId": "4444450270",
				"status": 1,
				"name": "小型医用制氧机（雅适）",
				"py": "XXYYZYJ（YS）|xiaoxingyiyongzhiyangji（yashi）|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "黄山市雅适医疗器械",
				"manufacturerFull": "黄山市雅适医疗器械有限公司",
				"medicineDosageNum": null,
				"materialSpec": "TYS-100",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "个",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 100,
				"piecePrice": 0,
				"packagePrice": 1980,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:11:19Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:11:19Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 1980,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "TYS-100"
			},
			"pieceNum": 100,
			"piecePrice": null,
			"packagePrice": 1980,
			"packageCostPrice": 1300,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -19500000,
			"totalPriceChange": -29700000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a2c00a7b8000",
					"pieceNum": 100,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 1300,
					"totalCostPriceChange": -6500000,
					"totalPriceChange": -9900000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005351",
					"stockId": 100005351,
					"goodsId": "ffffffff000000001078a2c00a7b8000",
					"pieceNum": 100,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 1300,
					"totalCostPriceChange": -6500000,
					"totalPriceChange": -9900000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006229",
					"stockId": 100006229,
					"goodsId": "ffffffff000000001078a2c00a7b8000",
					"pieceNum": 100,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 1300,
					"totalCostPriceChange": -6500000,
					"totalPriceChange": -9900000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73832",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a6580a7b8004",
			"goods": {
				"id": "ffffffff000000001078a6580a7b8004",
				"goodsId": "ffffffff000000001078a6580a7b8004",
				"shortId": "4444450577",
				"status": 1,
				"name": "甘舒霖笔",
				"py": "GSLB|ganshulinbi|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "通化东宝",
				"manufacturerFull": "通化东宝药业股份有限公司",
				"medicineDosageNum": null,
				"materialSpec": "300IU:3ml",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "ml",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 3,
				"piecePrice": 0,
				"packagePrice": 247.5,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:13:14Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:13:14Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 247.5,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "300IU:3ml"
			},
			"pieceNum": 3,
			"piecePrice": null,
			"packagePrice": 247.5,
			"packageCostPrice": 230,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -3450000,
			"totalPriceChange": -3712500,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a6580a7b8004",
					"pieceNum": 3,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 230,
					"totalCostPriceChange": -1150000,
					"totalPriceChange": -1237500,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005658",
					"stockId": 100005658,
					"goodsId": "ffffffff000000001078a6580a7b8004",
					"pieceNum": 3,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 230,
					"totalCostPriceChange": -1150000,
					"totalPriceChange": -1237500,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006536",
					"stockId": 100006536,
					"goodsId": "ffffffff000000001078a6580a7b8004",
					"pieceNum": 3,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 230,
					"totalCostPriceChange": -1150000,
					"totalPriceChange": -1237500,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73833",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff0000000010789c800a7b8004",
			"goods": {
				"id": "ffffffff0000000010789c800a7b8004",
				"goodsId": "ffffffff0000000010789c800a7b8004",
				"shortId": "4444449771",
				"status": 1,
				"name": "飞鹤奶粉（超级飞帆3段幼儿配方奶粉）",
				"py": "FHNF（CJFF3DYEPFNF）|feihenaifen（chaojifeifan3duanyouerpeifangnaifen）|",
				"organId": "********************************",
				"type": 7,
				"subType": 4,
				"manufacturer": "黑龙江飞鹤乳业",
				"manufacturerFull": "黑龙江飞鹤乳业有限公司",
				"medicineDosageNum": null,
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "g",
				"packageUnit": "听",
				"dismounting": 0,
				"pieceNum": 900,
				"piecePrice": 0,
				"packagePrice": 308,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:07:59Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:07:59Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 28,
				"customTypeId": 0,
				"chainPackagePrice": 308,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0
			},
			"pieceNum": 900,
			"piecePrice": null,
			"packagePrice": 308,
			"packageCostPrice": 246.4,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -3696000,
			"totalPriceChange": -4620000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff0000000010789c800a7b8004",
					"pieceNum": 900,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 246.4,
					"totalCostPriceChange": -1232000,
					"totalPriceChange": -1540000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100004849",
					"stockId": 100004849,
					"goodsId": "ffffffff0000000010789c800a7b8004",
					"pieceNum": 900,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 246.4,
					"totalCostPriceChange": -1232000,
					"totalPriceChange": -1540000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005727",
					"stockId": 100005727,
					"goodsId": "ffffffff0000000010789c800a7b8004",
					"pieceNum": 900,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 246.4,
					"totalCostPriceChange": -1232000,
					"totalPriceChange": -1540000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73834",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff0000000010789c800a7b8000",
			"goods": {
				"id": "ffffffff0000000010789c800a7b8000",
				"goodsId": "ffffffff0000000010789c800a7b8000",
				"shortId": "4444449770",
				"status": 1,
				"name": "飞鹤奶粉（超级飞帆2段较大婴儿配方奶粉）",
				"py": "FHNF（CJFF2DJDYEPFNF）|FHNF（CJFF2DJTYEPFNF）|feihenaifen（chaojifeifan2duanjiaodayingerpeifangnaifen）|feihenaifen（chaojifeifan2duanjiaodaiyingerpeifangnaifen）|feihenaifen（chaojifeifan2duanjiaotaiyingerpeifangnaifen）|",
				"organId": "********************************",
				"type": 7,
				"subType": 4,
				"manufacturer": "黑龙江飞鹤乳业",
				"manufacturerFull": "黑龙江飞鹤乳业有限公司",
				"medicineDosageNum": null,
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "g",
				"packageUnit": "听",
				"dismounting": 0,
				"pieceNum": 900,
				"piecePrice": 0,
				"packagePrice": 328,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:07:59Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:07:59Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 28,
				"customTypeId": 0,
				"chainPackagePrice": 328,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0
			},
			"pieceNum": 900,
			"piecePrice": null,
			"packagePrice": 328,
			"packageCostPrice": 262.4,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -3936000,
			"totalPriceChange": -4920000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff0000000010789c800a7b8000",
					"pieceNum": 900,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 262.4,
					"totalCostPriceChange": -1312000,
					"totalPriceChange": -1640000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100004848",
					"stockId": 100004848,
					"goodsId": "ffffffff0000000010789c800a7b8000",
					"pieceNum": 900,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 262.4,
					"totalCostPriceChange": -1312000,
					"totalPriceChange": -1640000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005726",
					"stockId": 100005726,
					"goodsId": "ffffffff0000000010789c800a7b8000",
					"pieceNum": 900,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 262.4,
					"totalCostPriceChange": -1312000,
					"totalPriceChange": -1640000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73835",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a2480a7b8008",
			"goods": {
				"id": "ffffffff000000001078a2480a7b8008",
				"goodsId": "ffffffff000000001078a2480a7b8008",
				"shortId": "4444450235",
				"status": 1,
				"name": "雷特伯恩（生理性海水鼻腔喷雾器）",
				"py": "LTBE（SLXHSBQPWQ）|leiteboen（shenglixinghaishuibiqiangpenwuqi）|leitebaien（shenglixinghaishuibiqiangpenwuqi）|leitebaen（shenglixinghaishuibiqiangpenwuqi）|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "浙江郎柯生物工程",
				"manufacturerFull": "浙江郎柯生物工程有限公司",
				"medicineDosageNum": null,
				"materialSpec": "T50ml(舒适)",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "瓶",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 1,
				"piecePrice": 0,
				"packagePrice": 69,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:11:04Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:11:04Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 69,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "T50ml(舒适)"
			},
			"pieceNum": 1,
			"piecePrice": null,
			"packagePrice": 69,
			"packageCostPrice": 24.15,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -362250,
			"totalPriceChange": -1035000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a2480a7b8008",
					"pieceNum": 1,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 24.15,
					"totalCostPriceChange": -120750,
					"totalPriceChange": -345000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005316",
					"stockId": 100005316,
					"goodsId": "ffffffff000000001078a2480a7b8008",
					"pieceNum": 1,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 24.15,
					"totalCostPriceChange": -120750,
					"totalPriceChange": -345000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006194",
					"stockId": 100006194,
					"goodsId": "ffffffff000000001078a2480a7b8008",
					"pieceNum": 1,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 24.15,
					"totalCostPriceChange": -120750,
					"totalPriceChange": -345000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73836",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a6800a7b8000",
			"goods": {
				"id": "ffffffff000000001078a6800a7b8000",
				"goodsId": "ffffffff000000001078a6800a7b8000",
				"shortId": "4444450586",
				"status": 1,
				"name": "无菌笔式注射针",
				"py": "WJBSZSZ|WJBSZYZ|wujunbishizhushezhen|wujunbishizhuyezhen|wujunbishizhuyizhen|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "意大利ARTSANA SPA",
				"manufacturerFull": "意大利ARTSANA SPA公司",
				"medicineDosageNum": null,
				"materialSpec": "0.25*6mm*7枚",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "mm*7枚",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 6,
				"piecePrice": 0,
				"packagePrice": 15,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:13:19Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:13:19Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 15,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "0.25*6mm*7枚"
			},
			"pieceNum": 6,
			"piecePrice": null,
			"packagePrice": 15,
			"packageCostPrice": 9,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -135000,
			"totalPriceChange": -225000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a6800a7b8000",
					"pieceNum": 6,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 9,
					"totalCostPriceChange": -45000,
					"totalPriceChange": -75000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005667",
					"stockId": 100005667,
					"goodsId": "ffffffff000000001078a6800a7b8000",
					"pieceNum": 6,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 9,
					"totalCostPriceChange": -45000,
					"totalPriceChange": -75000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006545",
					"stockId": 100006545,
					"goodsId": "ffffffff000000001078a6800a7b8000",
					"pieceNum": 6,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 9,
					"totalCostPriceChange": -45000,
					"totalPriceChange": -75000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73837",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff0000000010789fa80a7b8003",
			"goods": {
				"id": "ffffffff0000000010789fa80a7b8003",
				"goodsId": "ffffffff0000000010789fa80a7b8003",
				"shortId": "4444450026",
				"status": 1,
				"name": "启赋1阶段婴儿配方奶粉",
				"py": "QF1JDYEPFNF|qifu1jieduanyingerpeifangnaifen|",
				"organId": "********************************",
				"type": 7,
				"subType": 4,
				"manufacturer": "爱尔兰",
				"manufacturerFull": "爱尔兰",
				"medicineDosageNum": null,
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "g",
				"packageUnit": "罐",
				"dismounting": 0,
				"pieceNum": 900,
				"piecePrice": 0,
				"packagePrice": 408,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:09:40Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:09:40Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 28,
				"customTypeId": 0,
				"chainPackagePrice": 408,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0
			},
			"pieceNum": 900,
			"piecePrice": null,
			"packagePrice": 408,
			"packageCostPrice": 367.2,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -5508000,
			"totalPriceChange": -6120000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff0000000010789fa80a7b8003",
					"pieceNum": 900,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 367.2,
					"totalCostPriceChange": -1836000,
					"totalPriceChange": -2040000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005107",
					"stockId": 100005107,
					"goodsId": "ffffffff0000000010789fa80a7b8003",
					"pieceNum": 900,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 367.2,
					"totalCostPriceChange": -1836000,
					"totalPriceChange": -2040000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005985",
					"stockId": 100005985,
					"goodsId": "ffffffff0000000010789fa80a7b8003",
					"pieceNum": 900,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 367.2,
					"totalCostPriceChange": -1836000,
					"totalPriceChange": -2040000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73838",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff0000000010789f380a7b8008",
			"goods": {
				"id": "ffffffff0000000010789f380a7b8008",
				"goodsId": "ffffffff0000000010789f380a7b8008",
				"shortId": "4444449990",
				"status": 1,
				"name": "心愿（枣花蜜）",
				"py": "XY（ZHM）|xinyuan（zaohuami）|",
				"organId": "********************************",
				"type": 7,
				"subType": 4,
				"manufacturer": "江西意蜂实业",
				"manufacturerFull": "江西意蜂实业有限公司",
				"medicineDosageNum": null,
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "g",
				"packageUnit": "瓶",
				"dismounting": 0,
				"pieceNum": 1000,
				"piecePrice": 0,
				"packagePrice": 32.8,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:09:26Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:09:26Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 28,
				"customTypeId": 0,
				"chainPackagePrice": 32.8,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0
			},
			"pieceNum": 1000,
			"piecePrice": null,
			"packagePrice": 32.8,
			"packageCostPrice": 19.68,
			"beforePieceCount": 0,
			"beforePackageCount": 15000,
			"totalCostPriceChange": -295200,
			"totalPriceChange": -492000,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff0000000010789f380a7b8008",
					"pieceNum": 1000,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 19.68,
					"totalCostPriceChange": -98400,
					"totalPriceChange": -164000,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005071",
					"stockId": 100005071,
					"goodsId": "ffffffff0000000010789f380a7b8008",
					"pieceNum": 1000,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 19.68,
					"totalCostPriceChange": -98400,
					"totalPriceChange": -164000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005949",
					"stockId": 100005949,
					"goodsId": "ffffffff0000000010789f380a7b8008",
					"pieceNum": 1000,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 19.68,
					"totalCostPriceChange": -98400,
					"totalPriceChange": -164000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -15000
		},
		{
			"id": "73839",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001078a4780a7b8004",
			"goods": {
				"id": "ffffffff000000001078a4780a7b8004",
				"goodsId": "ffffffff000000001078a4780a7b8004",
				"shortId": "4444450415",
				"status": 1,
				"name": "血糖尿酸测试仪",
				"py": "XTNSCSY|XTSSCSY|xietangniaosuanceshiyi|xuetangniaosuanceshiyi|xietangsuisuanceshiyi|xuetangsuisuanceshiyi|",
				"organId": "********************************",
				"type": 2,
				"subType": 1,
				"manufacturer": "三诺生物传感",
				"manufacturerFull": "三诺生物传感股份有限公司",
				"medicineDosageNum": null,
				"materialSpec": "C：EA-18型",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "型",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 18,
				"piecePrice": 0,
				"packagePrice": 280,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:12:14Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:12:14Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 17,
				"customTypeId": 0,
				"chainPackagePrice": 280,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "C：EA-18型"
			},
			"pieceNum": 18,
			"piecePrice": null,
			"packagePrice": 280,
			"packageCostPrice": 180,
			"beforePieceCount": 0,
			"beforePackageCount": 14999,
			"totalCostPriceChange": -2699820,
			"totalPriceChange": -4199720,
			"detail": [{
					"id": null,
					"batchNo": null,
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001078a4780a7b8004",
					"pieceNum": 18,
					"beforePackageCount": 4999,
					"beforePieceCount": 0,
					"packageCostPrice": 180,
					"totalCostPriceChange": -899820,
					"totalPriceChange": -1399720,
					"packageCount": 10000,
					"pieceCount": 0,
					"packageCountChange": -4999,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100005496",
					"stockId": 100005496,
					"goodsId": "ffffffff000000001078a4780a7b8004",
					"pieceNum": 18,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 180,
					"totalCostPriceChange": -900000,
					"totalPriceChange": -1400000,
					"packageCount": 5000,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": null,
					"batchId": "100006374",
					"stockId": 100006374,
					"goodsId": "ffffffff000000001078a4780a7b8004",
					"pieceNum": 18,
					"beforePackageCount": 5000,
					"beforePieceCount": 0,
					"packageCostPrice": 180,
					"totalCostPriceChange": -900000,
					"totalPriceChange": -1400000,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -5000,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": -14999
		},
		{
			"id": "73840",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff000000001089b2100a876000",
			"goods": {
				"id": "ffffffff000000001089b2100a876000",
				"goodsId": "ffffffff000000001089b2100a876000",
				"shortId": "4444452331",
				"status": 1,
				"py": "|robinsCSFY1",
				"organId": "********************************",
				"type": 1,
				"subType": 1,
				"barCode": "1233676767",
				"manufacturer": "李龙彬有限公司",
				"manufacturerFull": "李龙彬有限公司",
				"medicineCadn": "robins测试发药1",
				"medicineNmpn": "1",
				"medicineDosageNum": 1,
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "片",
				"packageUnit": "盒",
				"dismounting": 1,
				"pieceNum": 21,
				"piecePrice": 4.76,
				"packagePrice": 100,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-05T02:00:33Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-04-01T04:18:12Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 12,
				"customTypeId": 14128,
				"customTypeName": "贴剂",
				"chainPiecePrice": 4.76,
				"chainPackagePrice": 100,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0
			},
			"pieceNum": 21,
			"piecePrice": 4.76,
			"packagePrice": 100,
			"packageCostPrice": 1.0736,
			"beforePieceCount": 3,
			"beforePackageCount": 326,
			"totalCostPriceChange": -350.1471,
			"totalPriceChange": -32614.29,
			"detail": [{
					"id": null,
					"batchNo": "1",
					"batchId": "*********",
					"stockId": *********,
					"goodsId": "ffffffff000000001089b2100a876000",
					"pieceNum": 21,
					"beforePackageCount": 322,
					"beforePieceCount": 3,
					"packageCostPrice": 1,
					"totalCostPriceChange": -322.1429,
					"totalPriceChange": -32214.29,
					"packageCount": 4,
					"pieceCount": 0,
					"packageCountChange": -322,
					"pieceCountChange": -3
				},
				{
					"id": null,
					"batchNo": "",
					"batchId": "100013392",
					"stockId": 100013392,
					"goodsId": "ffffffff000000001089b2100a876000",
					"pieceNum": 21,
					"beforePackageCount": 1,
					"beforePieceCount": 0,
					"packageCostPrice": 2,
					"totalCostPriceChange": -2,
					"totalPriceChange": -100,
					"packageCount": 3,
					"pieceCount": 0,
					"packageCountChange": -1,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": "",
					"batchId": "100013393",
					"stockId": 100013393,
					"goodsId": "ffffffff000000001089b2100a876000",
					"pieceNum": 21,
					"beforePackageCount": 1,
					"beforePieceCount": 0,
					"packageCostPrice": 12,
					"totalCostPriceChange": -12,
					"totalPriceChange": -100,
					"packageCount": 2,
					"pieceCount": 0,
					"packageCountChange": -1,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": "",
					"batchId": "100013396",
					"stockId": 100013396,
					"goodsId": "ffffffff000000001089b2100a876000",
					"pieceNum": 21,
					"beforePackageCount": 1,
					"beforePieceCount": 0,
					"packageCostPrice": 2,
					"totalCostPriceChange": -2,
					"totalPriceChange": -100,
					"packageCount": 1,
					"pieceCount": 0,
					"packageCountChange": -1,
					"pieceCountChange": 0
				},
				{
					"id": null,
					"batchNo": "",
					"batchId": "100013397",
					"stockId": 100013397,
					"goodsId": "ffffffff000000001089b2100a876000",
					"pieceNum": 21,
					"beforePackageCount": 1,
					"beforePieceCount": 0,
					"packageCostPrice": 12,
					"totalCostPriceChange": -12,
					"totalPriceChange": -100,
					"packageCount": 0,
					"pieceCount": 0,
					"packageCountChange": -1,
					"pieceCountChange": 0
				}
			],
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": -3,
			"packageCountChange": -326
		},
		{
			"id": "73841",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff0000000010789e480a7b8000",
			"goods": {
				"id": "ffffffff0000000010789e480a7b8000",
				"goodsId": "ffffffff0000000010789e480a7b8000",
				"shortId": "4444449909",
				"status": 1,
				"name": "新盖中盖牌碳酸钙维生素D3咀嚼片",
				"py": "XGZGPTSGWSSD3JJP|XHZGPTSGWSSD3JJP|XGZHPTSGWSSD3JJP|XHZHPTSGWSSD3JJP|XGZGPTSGWSSD3ZJP|XHZGPTSGWSSD3ZJP|XGZHPTSGWSSD3ZJP|XHZHPTSGWSSD3ZJP|xingaizhonggaipaitansuangaiweishengsuD3jujiaopian|xingezhonggaipaitansuangaiweishengsuD3jujiaopian|xinhezhonggaipaitansuangaiweishengsuD3jujiaopian|xingaizhonggepaitansuangaiweishengsuD3jujiaopian|xingezhonggepaitansuangaiweishengsuD3jujiaopian|xinhezhonggepaitansuangaiweishengsuD3jujiaopian|xingaizhonghepaitansuangaiweishengsuD3jujiaopian|xingezhonghepaitansuangaiweishengsuD3jujiaopian|xinhezhonghepaitansuangaiweishengsuD3jujiaopian|xingaizhonggaipaitansuangaiweishengsuD3zuijiaopian|xingezhonggaipaitansuangaiweishengsuD3zuijiaopian|xinhezhonggaipaitansuangaiweishengsuD3zuijiaopian|xingaizhonggepaitansuangaiweishengsuD3zuijiaopian|xingezhonggepaitansuangaiweishengsuD3zuijiaopian|xinhezhonggepaitansuangaiweishengsuD3zuijiaopian|xingaizhonghepaitansuangaiweishengsuD3zuijiaopian|xingezhonghepaitansuangaiweishengsuD3zuijiaopian|xinhezhonghepaitansuangaiweishengsuD3zuijiaopian|xingaizhonggaipaitansuangaiweishengsuD3jujuepian|xingezhonggaipaitansuangaiweishengsuD3jujuepian|xinhezhonggaipaitansuangaiweishengsuD3jujuepian|xingaizhonggepaitansuangaiweishengsuD3jujuepian|xingezhonggepaitansuangaiweishengsuD3jujuepian|xinhezhonggepaitansuangaiweishengsuD3jujuepian|xingaizhonghepaitansuangaiweishengsuD3jujuepian|xingezhonghepaitansuangaiweishengsuD3jujuepian|xinhezhonghepaitansuangaiweishengsuD3jujuepian|xingaizhonggaipaitansuangaiweishengsuD3zuijuepian|xingezhonggaipaitansuangaiweishengsuD3zuijuepian|xinhezhonggaipaitansuangaiweishengsuD3zuijuepian|xingaizhonggepaitansuangaiweishengsuD3zuijuepian|xingezhonggepaitansuangaiweishengsuD3zuijuepian|xinhezhonggepaitansuangaiweishengsuD3zuijuepian|xingaizhonghepaitansuangaiweishengsuD3zuijuepian|xingezhonghepaitansuangaiweishengsuD3zuijuepian|xinhezhonghepaitansuangaiweishengsuD3zuijuepian|",
				"organId": "********************************",
				"type": 7,
				"subType": 3,
				"manufacturer": "Q3哈药六",
				"manufacturerFull": "Q3哈药集团制药六厂",
				"medicineDosageNum": null,
				"materialSpec": "75.6g(2.1g",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "片）",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 36,
				"piecePrice": 0,
				"packagePrice": 48,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:08:56Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:08:56Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 27,
				"customTypeId": 0,
				"chainPackagePrice": 48,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "75.6g(2.1g"
			},
			"pieceNum": 36,
			"piecePrice": null,
			"packagePrice": 48,
			"packageCostPrice": 0,
			"beforePieceCount": 0,
			"beforePackageCount": 0,
			"totalCostPriceChange": 0,
			"totalPriceChange": 0,
			"detail": null,
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": 0
		},
		{
			"id": "73842",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff0000000010789de80a7b8000",
			"goods": {
				"id": "ffffffff0000000010789de80a7b8000",
				"goodsId": "ffffffff0000000010789de80a7b8000",
				"shortId": "4444449878",
				"status": 1,
				"name": "蜂胶软胶囊",
				"py": "FJRJN|fengjiaoruanjiaonang|",
				"organId": "********************************",
				"type": 7,
				"subType": 3,
				"manufacturer": "威海百合生物技术",
				"manufacturerFull": "威海百合生物技术股份有限公司",
				"medicineDosageNum": null,
				"materialSpec": "500mg",
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "粒",
				"packageUnit": "盒",
				"dismounting": 0,
				"pieceNum": 100,
				"piecePrice": 0,
				"packagePrice": 164,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:08:44Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:08:44Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 27,
				"customTypeId": 0,
				"chainPackagePrice": 164,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0,
				"cMSpec": "500mg"
			},
			"pieceNum": 100,
			"piecePrice": null,
			"packagePrice": 164,
			"packageCostPrice": 0,
			"beforePieceCount": 0,
			"beforePackageCount": 0,
			"totalCostPriceChange": 0,
			"totalPriceChange": 0,
			"detail": null,
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": 0
		},
		{
			"id": "73843",
			"orderId": "1466",
			"batchId": null,
			"batchNo": null,
			"goodsId": "ffffffff0000000010789df80a7b8005",
			"goods": {
				"id": "ffffffff0000000010789df80a7b8005",
				"goodsId": "ffffffff0000000010789df80a7b8005",
				"shortId": "4444449885",
				"status": 1,
				"name": "雅客V9牌维生素夹心糖（草莓/蓝莓味）",
				"py": "YKV9PWSSJXT（CM/LMW）|YKV9PWSSGXT（CM/LMW）|yakeV9paiweishengsujiaxintang（caomei/lanmeiwei）|yakeV9paiweishengsugaxintang（caomei/lanmeiwei）|",
				"organId": "********************************",
				"type": 7,
				"subType": 3,
				"manufacturer": "福建雅客食品",
				"manufacturerFull": "福建雅客食品有限公司",
				"medicineDosageNum": null,
				"gradeId": 0,
				"isSell": 1,
				"smartDispense": 0,
				"pieceUnit": "g",
				"packageUnit": "袋",
				"dismounting": 0,
				"pieceNum": 132,
				"piecePrice": 0,
				"packagePrice": 19.8,
				"packageCostPrice": 0,
				"inTaxRat": 0,
				"outTaxRat": 0,
				"createdUserId": "6e45706922a74966ab51e4ed1e604641",
				"createdDate": "2021-02-03T11:08:46Z",
				"lastModifiedUserId": "6e45706922a74966ab51e4ed1e604641",
				"lastModifiedDate": "2021-02-03T11:08:46Z",
				"disable": 0,
				"needExecutive": 0,
				"typeId": 27,
				"customTypeId": 0,
				"chainPackagePrice": 19.8,
				"v2DisableStatus": 0,
				"inorderConfig": 0,
				"sellConfig": 0
			},
			"pieceNum": 132,
			"piecePrice": null,
			"packagePrice": 19.8,
			"packageCostPrice": 0,
			"beforePieceCount": 0,
			"beforePackageCount": 0,
			"totalCostPriceChange": 0,
			"totalPriceChange": 0,
			"detail": null,
			"pieceCount": 0,
			"packageCount": 0,
			"pieceCountChange": 0,
			"packageCountChange": 0
		}
	],
	"logs": [{
		"id": "1688200873762627584",
		"orderId": 1466,
		"action": "新建",
		"detail": null,
		"comment": "",
		"createdUser": {
			"id": "6e45706922a74966ab51e4ed1e604641",
			"name": "刘喜"
		},
		"createdDate": "2021-07-22T08:05:12Z"
	}]
}
-->

<template>
    <div>
        <div data-type="header">
            <div class="order-title">
                {{ tableTitle }}
            </div>
            <print-row class="print-stock-header">
                <print-col :span="8">
                    盘点人：{{ order && order.createdUser.name || '' }}
                </print-col>
                <print-col
                    v-if="multiPharmacyCanUse"
                    :span="8"
                >
                    盘点库房：{{ order && order.pharmacy && order.pharmacy.name || '' }}
                </print-col>
                <print-col :span="8">
                    盘点门店：{{ order && order.organ.shortName || order.organ.name || '' }}
                </print-col>
                <print-col :span="8">
                    盘点日期：{{ order && order.createdDate | parseTime }}
                </print-col>
            </print-row>
        </div>
        <table
            class="print-stock-table-wrapper"
            data-type="big-table"
        >
            <thead>
                <tr class="table-title">
                    <td colspan="2">
                        药品编码
                    </td>
                    <td colspan="2">
                        药品名称
                    </td>
                    <td colspan="2">
                        规格
                    </td>
                    <td colspan="2">
                        类型
                    </td>
                    <td colspan="2">
                        二级分类
                    </td>
                    <td colspan="2">
                        厂家
                    </td>
                    <td colspan="2">
                        批次
                    </td>
                    <td colspan="2">
                        生产批号
                    </td>
                    <td colspan="2">
                        账面数量
                    </td>
                    <td colspan="2">
                        实际数量
                    </td>
                    <td colspan="2">
                        盈亏数量
                    </td>

                    <td
                        colspan="2"
                    >
                        盈亏金额(进价)
                    </td>
                    <td
                        colspan="2"
                        class="no-right-border"
                    >
                        盈亏金额(售价)
                    </td>
                </tr>
            </thead>
            <tbody>
                <tr
                    v-for="(item) in order.list"
                    :key="item.id"
                    class="table-tr"
                >
                    <!--            药品编码-->
                    <td colspan="2">
                        {{ item.goods.shortId }}
                    </td>

                    <!--            药品名称-->
                    <td colspan="2">
                        {{ item.goods | goodsFullName }}
                    </td>
                    <!--            规格-->
                    <td colspan="2">
                        {{ item.goods && item.goods.displaySpec }}
                    </td>

                    <td colspan="2">
                        {{ formatTypeName(item.goods) }}
                    </td>

                    <td colspan="2">
                        {{ item.goods.customTypeName }}
                    </td>

                    <td colspan="2">
                        {{ item.goods && item.goods.manufacturer || '' }}
                    </td>

                    <td colspan="2">
                        {{ item.batchId }}
                    </td>
                    <td colspan="2">
                        {{ item.batchNo }}
                    </td>
                    <td colspan="2">
                        {{ accountCount(item) }}
                    </td>
                    <td colspan="2">
                        {{ item|complexCount }}
                    </td>
                    <td colspan="2">
                        {{ changeCountText(item) }}
                    </td>
                    <td
                        colspan="2"
                    >
                        <data-permission-control :value="item.totalCostPriceChange">
                            {{ item.totalCostPriceChange | formatMoney(false) }}
                        </data-permission-control>
                    </td>
                    <td
                        colspan="2"
                        class="no-right-border"
                    >
                        {{ item.totalPriceChange | formatMoney(false) }}
                    </td>
                </tr>
            </tbody>
        </table>

        <print-row data-last-page="LastPage">
            <print-col :span="24">
                备注：{{ order && order.comment || '' }}
            </print-col>
            <print-col :span="8">
                品种合计：{{ order.kindCount }}
            </print-col>
            <print-col :span="8">
                盈亏进价合计：
                <data-permission-control :value="order.totalCostPriceChange">
                    {{ order.totalCostPriceChange | formatMoney(false) }}
                </data-permission-control>
            </print-col>
            <print-col :span="8">
                盈亏售价合计：{{ order.totalPriceChange | formatMoney(false) }}
            </print-col>
        </print-row>
    </div>
</template>

<script>
    import PDHandler from './data-handler/PD-handler.js'
    import { complexCount, digitUppercase, goodsSpec, parseTime, formatMoney, goodsFullName } from "./common/utils.js";
    import PrintCol from "./components/layout/print-col.vue";
    import PrintRow from "./components/layout/print-row.vue";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import {goodsTypeName} from './common/utils.js'
    import DataPermissionControl from './components/inventory/data-permission-control.js';

    export default {
        name: "PD",
        components: {
            PrintCol,
            PrintRow,
            DataPermissionControl,
        },
        DataHandler: PDHandler,
        businessKey: PrintBusinessKeyEnum.GOODS_CHECK,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.NeedleMultiPaper,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '三等分' // 默认选择的等分纸
            },
        ],
        filters: {
            goodsSpec,
            complexCount,
            parseTime,
            formatMoney,
            goodsFullName
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            },
        },
        computed: {
            order() {
                return this.renderData.printData;
            },
            config() {
                return this.renderData.config;
            },
            tableTitle() {
                if(this.order){
                    return `盘点单-${this.order.orderNo}`;
                }else{
                    return '盘点单';
                }
            },
            multiPharmacyCanUse() {
                return this.order.multiPharmacyCanUse;
            },
            needTransGoodsClassificationName() {
                return this.order.needTransGoodsClassificationName;
            }
        },
        methods: {
            formatMoney,
            goodsTypeName,
            digitUppercase,
            accountCount(that) {
                if(that){
                    return complexCount({
                        goods: that.goods,
                        pieceCount: that.beforePieceCount,
                        packageCount: that.beforePackageCount,
                    });
                }
                return '';
            },
            formatTypeName(goods) {
              let typeName = goodsTypeName(goods);
              if (this.needTransGoodsClassificationName && typeName === '中药饮片') {
                  return '配方饮片'
              }
              return typeName;
            },
            changeCountText(item) {
                let change = this.changeCount(item);
                if(change === '') return '';

                let sign = '';
                if(change > 0) {
                    sign = '+';
                } else if(change < 0) {
                    sign = '-';
                }
                let pieceCount = Math.abs(item.pieceCountChange) || 0;
                let packageCount = Math.abs(item.packageCountChange) || 0;
                return sign + complexCount({ pieceCount, packageCount, goods: item.goods}, true);
            },
            changeCount({ pieceCountChange, packageCountChange}) {
                let pieceCount = +pieceCountChange || 0;
                let packageCount = +packageCountChange || 0;

                if(pieceCount > 0 || packageCount > 0) {
                    return 1;
                } else if(pieceCount < 0 || packageCount < 0) {
                    return -1;
                } else {
                    return 0;
                }
            },
        }
    }
</script>

<style lang="scss">
@import "./style/inventory-common.scss";
</style>
