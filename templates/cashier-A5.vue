<template>
    <div class="cashier-a5">
        <!-- header -->
        <div data-type="header">
            <cashier-header
                :print-data="printData"
                :config="config"
                :organ-title="organTitle"
                :glasses-medicine-forms="glassesMedicineForms"
                :is-support-glasses="isSupportGlasses"
            ></cashier-header>
        </div>

        <template v-if="isSupportGlasses && feeInfo.glassesPrescription && glassesMedicineForms.length">
            <div
                v-for="glassesForm in glassesMedicineForms"
                :key="glassesForm.usage"
                class="glasses-prescription-wrapper"
            >
                <table class="table">
                    <thead>
                        <tr>
                            <th
                                :style="glassesForm.glassesType !== 0 ? null : { width: '9.5%' }"
                            >
                                <template v-if="glassesForm.glassesType === 0">
                                    {{ glassesForm.usage }}
                                </template>
                            </th>
                            <th
                                v-for="formItem in glassesForm.chargeFormItems"
                                :key="formItem.key"
                                :style="glassesForm.glassesType !== 0 ? { width: '16.6%' } : formItem.key !== 'frameCva' ? { width: '9.5%' } : null"
                            >
                                {{ formItem.name }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>右眼</td>
                            <td
                                v-for="formItem in glassesForm.chargeFormItems"
                                :key="formItem.key"
                            >
                                {{ formItem.rightEyeValue }}
                            </td>
                        </tr>
                        <tr>
                            <td>左眼</td>
                            <td
                                v-for="formItem in glassesForm.chargeFormItems"
                                :key="formItem.key"
                            >
                                {{ formItem.leftEyeValue }}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div
                    v-if="glassesForm.requirement"
                    class="table-bottom"
                >
                    <div class="requirement">
                        备注：{{ glassesForm.requirement }}
                    </div>
                </div>
            </div>
        </template>

        <template v-if="feeInfo.chargeItem">
            <!-- 显示费用类型 -->
            <template v-if="isFeeCompose && feeInfo.feeDetail === 2">
                <div data-type="mix-box">
                    <div data-type="group">
                        <print-row
                            v-for="(medicalBill, mIndex) in medicalBills"
                            :key="`${medicalBill.name + mIndex }`"
                            data-type="item"
                        >
                            <print-col
                                :span="18"
                                class="text-info"
                                overflow
                            >
                                {{ medicalBill.name }}
                            </print-col>
                            <print-col
                                :span="6"
                                class="text-right text-info"
                            >
                                {{
                                    medicalBill.totalFee | formatMoney
                                }}
                            </print-col>
                        </print-row>
                        <div class="print-split-line"></div>
                    </div>
                </div>
            </template>

            <!-- 显示医嘱/费用项 -->
            <template v-else>
                <!-- 支持医费分离 -->
                <template v-if="isFeeCompose">
                    <template
                        v-if="(registrationForms.length) || decoctionForms.length || expressForms.length || examinationForms.length || treatmentForms.length || nursingForms.length"
                    >
                        <div data-type="mix-box">
                            <product-form
                                v-for="(form, index) in registrationForms"
                                :key="`${form.id + index }`"
                                :form="form"
                                :total-price-width="3"
                                :unit-width="3"
                            ></product-form>


                            <product-form
                                v-for="(form, index) in expressForms"
                                :key="`${form.id + index }`"
                                :form="form"
                                :total-price-width="3"
                                :unit-width="3"
                            ></product-form>


                            <product-form
                                v-for="(form, index) in decoctionForms"
                                :key="`${form.id + index }`"
                                :form="form"
                                :total-price-width="3"
                                :unit-width="3"
                            ></product-form>

                            <charge-form-new
                                :forms="examinationForms"
                                :config="2"
                                :shebao-config="shebaoConfig"
                                :auto-change-line="!!autoChangeLine"
                                total-title="检查检验费"
                                :total-fee="getTotalPriceByForms(examinationForms)"
                                :is-fee-compose="isFeeCompose"
                            ></charge-form-new>

                            <charge-form-new
                                :forms="treatmentForms"
                                :config="2"
                                :shebao-config="shebaoConfig"
                                :auto-change-line="!!autoChangeLine"
                                total-title="治疗理疗费"
                                :total-fee="getTotalPriceByForms(treatmentForms)"
                                :is-fee-compose="isFeeCompose"
                            ></charge-form-new>

                            <charge-form-new
                                :forms="nursingForms"
                                :config="2"
                                :shebao-config="shebaoConfig"
                                :auto-change-line="!!autoChangeLine"
                                total-title="护理费"
                                :total-fee="getTotalPriceByForms(nursingForms)"
                                :is-fee-compose="isFeeCompose"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="composeForms.length">
                        <div data-type="mix-box">
                            <charge-form-new
                                :forms="composeForms"
                                :config="2"
                                :shebao-config="shebaoConfig"
                                :show-children="feeInfo.composeChildren"
                                :auto-change-line="!!autoChangeLine"
                                total-title="套餐费"
                                :total-fee="getTotalPriceByForms(composeForms)"
                                :is-fee-compose="isFeeCompose"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="westernMedicineForms.length">
                        <div data-type="mix-box">
                            <charge-form-new
                                :shebao-config="shebaoConfig"
                                :forms="westernMedicineForms"
                                :config="2"
                                :auto-change-line="!!autoChangeLine"
                                is-western
                                total-title="中西成药费"
                                :show-med-positon="westernMedicine.position"
                                :show-batch-number="westernMedicine.batchNumber"
                                :show-valid-date="westernMedicine.validityDate"
                                :show-manufacture="westernMedicine.manufacturer"
                                :show-spec="westernMedicine.spec"
                                :total-fee="getTotalPriceByForms(westernMedicineForms)"
                                form-split-line
                                :is-fee-compose="isFeeCompose"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="chineseMedicineForms.length">
                        <div
                            class="content-item"
                            data-type="mix-box"
                        >
                            <chinese-form-new
                                :shebao-config="shebaoConfig"
                                :forms="chineseMedicineForms"
                                :config="2"
                                :position="chinese.position"
                                :special-requirement="chinese.specialRequirement"
                                :show-batch-number="chinese.batchNumber"
                                :auto-change-line="autoChangeLine"
                                :show-validity-date="chinese.validityDate"
                                :show-manufacturer="chinese.manufacturer"
                                :show-unit-count="chinese.unitCount"
                                :show-total-count="chinese.totalCount"
                                total-title="中药费"
                                :total-fee="getTotalPriceByForms(chineseMedicineForms)"
                                :is-fee-compose="isFeeCompose"
                            ></chinese-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="materialGoodsForms.length">
                        <div data-type="mix-box">
                            <charge-form-new
                                :shebao-config="shebaoConfig"
                                :forms="materialGoodsForms"
                                :config="2"
                                :auto-change-line="!!autoChangeLine"
                                :show-med-positon="materialGoods.position"
                                total-title="材料商品费"
                                :total-fee="getTotalPriceByForms(materialGoodsForms)"
                                :is-fee-compose="isFeeCompose"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="otherForms.length">
                        <div data-type="mix-box">
                            <charge-form-new
                                :shebao-config="shebaoConfig"
                                :forms="otherForms"
                                :config="2"
                                :auto-change-line="!!autoChangeLine"
                                total-title="其他费用"
                                :total-fee="getTotalPriceByForms(otherForms)"
                                :is-fee-compose="isFeeCompose"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template
                        v-if="isSupportGlasses && ((feeInfo.eyeglasses === 0 && subTotals.eyeFee > 0) || glassesForms.length)"
                    >
                        <div data-type="mix-box">
                            <charge-form-new
                                :shebao-config="shebaoConfig"
                                :forms="glassesForms"
                                :config="2"
                                :auto-change-line="!!autoChangeLine"
                                total-title="眼镜费"
                                :show-spec="glassesGoods.spec"
                                :total-fee="getTotalPriceByForms(glassesForms)"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>
                </template>

                <template v-else>
                    <template
                        v-if="(feeInfo.registration && registrationForms.length) ||
                            decoctionForms.length ||
                            expressForms.length ||
                            examinationForms.length ||
                            treatmentForms.length"
                    >
                        <div data-type="mix-box">
                            <template v-if="feeInfo.registration">
                                <product-form
                                    v-for="(form, index) in registrationForms"
                                    :key="`${form.id + index}`"
                                    :form="form"
                                    :total-price-width="3"
                                    :unit-width="3"
                                ></product-form>
                            </template>


                            <product-form
                                v-for="(form, index) in expressForms"
                                :key="`${form.id + index}`"
                                :form="form"
                                :total-price-width="3"
                                :unit-width="3"
                            ></product-form>


                            <product-form
                                v-for="(form, index) in decoctionForms"
                                :key="`${form.id + index}`"
                                :form="form"
                                :total-price-width="3"
                                :unit-width="3"
                            ></product-form>

                            <charge-form-new
                                :forms="examinationForms"
                                :config="feeInfo.examination"
                                :shebao-config="shebaoConfig"
                                :auto-change-line="!!autoChangeLine"
                                total-title="检查检验费"
                                :total-fee="getTotalPriceByForms(examinationForms)"
                            ></charge-form-new>


                            <charge-form-new
                                :forms="treatmentForms"
                                :config="feeInfo.treatment"
                                :shebao-config="shebaoConfig"
                                :auto-change-line="!!autoChangeLine"
                                total-title="治疗理疗费"
                                :total-fee="getTotalPriceByForms(treatmentForms)"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="composeForms.length">
                        <div data-type="mix-box">
                            <charge-form-new
                                :forms="composeForms"
                                :config="feeInfo.compose"
                                :shebao-config="shebaoConfig"
                                :show-children="feeInfo.composeChildren"
                                :auto-change-line="!!autoChangeLine"
                                total-title="套餐费"
                                :total-fee="getTotalPriceByForms(composeForms)"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="westernMedicineForms.length">
                        <div data-type="mix-box">
                            <charge-form-new
                                :shebao-config="shebaoConfig"
                                :forms="westernMedicineForms"
                                :config="feeInfo.westernMedicine"
                                :auto-change-line="!!autoChangeLine"
                                is-western
                                total-title="中西成药费"
                                :show-med-positon="westernMedicine.position"
                                :show-batch-number="westernMedicine.batchNumber"
                                :show-valid-date="westernMedicine.validityDate"
                                :show-manufacture="westernMedicine.manufacturer"
                                :show-spec="westernMedicine.spec"
                                :total-fee="getTotalPriceByForms(westernMedicineForms)"
                                form-split-line
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="chineseMedicineForms.length">
                        <div data-type="mix-box">
                            <chinese-form-new
                                :shebao-config="shebaoConfig"
                                :forms="chineseMedicineForms"
                                :config="feeInfo.chineseMedicine"
                                :position="chinese.position"
                                :special-requirement="chinese.specialRequirement"
                                :show-batch-number="chinese.batchNumber"
                                :auto-change-line="autoChangeLine"
                                :show-validity-date="chinese.validityDate"
                                :show-manufacturer="chinese.manufacturer"
                                :show-unit-count="chinese.unitCount"
                                :show-total-count="chinese.totalCount"
                                total-title="中药费"
                                :total-fee="getTotalPriceByForms(chineseMedicineForms)"
                            ></chinese-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="materialGoodsForms.length">
                        <div data-type="mix-box">
                            <charge-form-new
                                :shebao-config="shebaoConfig"
                                :forms="materialGoodsForms"
                                :config="feeInfo.materialGoods"
                                :auto-change-line="!!autoChangeLine"
                                :show-med-positon="materialGoods.position"
                                total-title="材料商品费"
                                :total-fee="getTotalPriceByForms(materialGoodsForms)"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="otherForms.length">
                        <div data-type="mix-box">
                            <charge-form-new
                                :shebao-config="shebaoConfig"
                                :forms="otherForms"
                                :config="feeInfo.other"
                                :auto-change-line="!!autoChangeLine"
                                total-title="其他费用"
                                :total-fee="getTotalPriceByForms(otherForms)"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>

                    <template v-if="isSupportGlasses && glassesForms.length">
                        <div data-type="mix-box">
                            <charge-form-new
                                :shebao-config="shebaoConfig"
                                :forms="glassesForms"
                                :config="feeInfo.eyeglasses"
                                :auto-change-line="!!autoChangeLine"
                                total-title="眼镜费"
                                :show-spec="glassesGoods.spec"
                                :total-fee="getTotalPriceByForms(glassesForms)"
                            ></charge-form-new>
                        </div>
                        <div class="print-split-line"></div>
                    </template>
                </template>
            </template>
        </template>
        
        <!-- 付款信息 -->
        <div
            v-if="cashierInfo.totalFee || (cashierInfo.discountFee && printData.discountFee) || cashierInfo.receivableFee || cashierInfo.netIncomeFee"
            data-type="mix-box"
            class="charge-info-wrapper"
        >
            <div
                data-type="group"
                class="charge-info-group text-font-weight-small"
            >
                <div data-type="item">
                    <div
                        v-if="cashierInfo.totalFee"
                        class="charge-sub-text"
                    >
                        合计：{{ printData.totalFee | formatMoney }}
                    </div>
                    <div
                        v-if="cashierInfo.discountFee && printData.discountFee"
                        class="charge-sub-text"
                        :class="{ 'text-margin-left': cashierInfo.totalFee }"
                    >
                        优惠：{{ printData.discountFee | formatMoney }}
                    </div>
                    <div
                        v-if="cashierInfo.receivableFee"
                        class="charge-sub-text"
                        :class="{ 'text-margin-left': cashierInfo.totalFee || (cashierInfo.discountFee && printData.discountFee) }"
                    >
                        应付：{{ printData.receivableFee | formatMoney }}
                    </div>
                </div>
                <div
                    v-if="cashierInfo.netIncomeFee"
                    data-type="item"
                    class="charge-text"
                >
                    实付：<span
                        v-for="(pay, index) in actuallyReceiveTransactions"
                        :key="index"
                        :class="{ 'text-margin-left': index !== 0 }"
                    >（{{ pay.payModeDisplayName }}）{{ pay.amount | formatMoney }}</span>
                    <span v-if="!actuallyReceiveTransactions.length">0.00</span>
                </div>
            </div>
            <div class="print-split-line"></div>
        </div>

        <!-- 欠费信息 -->
        <div
            v-if="latestOweTransaction || isOweStatus"
            data-type="mix-box"
            class="charge-info-wrapper"
        >
            <div
                data-type="group"
                class="charge-info-group"
            >
                <div data-type="item">
                    <div
                        v-if="latestOweTransaction"
                        class="charge-sub-text"
                    >
                        本次还款：<span>{{ latestOweTransaction.amount | formatMoney }}</span>
                    </div>
                    <div
                        v-if="isOweStatus"
                        class="charge-sub-text text-margin-left"
                    >
                        当前欠费：<span>{{ printData.oweFee | formatMoney }}</span>
                    </div>
                </div>
            </div>
            <div class="print-split-line"></div>
        </div>

        <!-- 会员卡/卡项 -->
        <div
            v-if="hasMemberCardPay || (promotionBalances && promotionBalances.length)"
            data-type="mix-box"
            class="charge-info-wrapper"
        >
            <div
                data-type="group"
                class="charge-info-group"
            >
                <div
                    v-if="hasMemberCardPay"
                    data-type="item"
                >
                    <div class="charge-sub-text">
                        <span>
                            会员卡<template
                                v-if="printData.memberCardMobile"
                            >
                                ({{ getMobile(printData.memberCardMobile) }})
                            </template>
                        </span>
                        <span class="text-margin-left">余额：{{ printData.memberCardBalance | formatMoney }}</span>
                    </div>
                    <div class="charge-sub-text text-margin-left">
                        会员卡原有余额：{{ printData.memberCardBeginningBalance | formatMoney }}
                    </div>
                </div>
                <template v-if="promotionBalances && promotionBalances.length">
                    <div
                        v-for="(item, index) in promotionBalances"
                        :key="`${index}-promotion-balances`"
                        data-type="item"
                    >
                        <div class="charge-sub-text">
                            卡项
                            <template
                                v-if="item.name"
                            >
                                ({{ item.name }})
                            </template>
                            余额：{{ item.remainingBalance | formatMoney }}
                        </div>
                        <div class="charge-sub-text text-margin-left">
                            卡项原有余额：{{ item.beginBalance | formatMoney }}
                        </div>
                    </div>
                </template>
            </div>
            <div class="print-split-line"></div>
        </div>

        <!-- 返券 -->
        <div
            v-if="hasPromotion"
            data-type="mix-box"
            class="charge-info-wrapper"
        >
            <div
                data-type="group"
                class="charge-info-group"
            >
                <div data-type="item">
                    <div class="charge-sub-text">
                        返券：{{ formatGiftCoupons() }}
                    </div>
                </div>
            </div>
            <div class="print-split-line"></div>
        </div>
        
        <!-- 支付信息 -->
        <div
            data-type="mix-box"
            class="pay-info-wrapper text-font-weight-small"
        >
            <hangzhou-new
                v-if="isHangzhou"
                :shebao-config="shebaoConfig"
                :shebao-payment="shebaoPayment"
            ></hangzhou-new>
            <yunnan-new
                v-else-if="isYunnan"
                :shebao-config="shebaoConfig"
                :shebao-payment="shebaoPayment"
            ></yunnan-new>
            <div
                v-else
                data-type="group"
                class="pay-info-group"
            >
                <!-- 医保卡信息 -->
                <template v-if="shebaoConfig.cardInfo">
                    <div
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        医保号：{{ shebaoPayment.cardId }}
                    </div>
                    <div
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        人员编号：{{ extraInfo.personalCode }}
                    </div>
                    <div
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        持卡人：{{ shebaoPayment.cardOwner }}
                    </div>
                    <div
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        关系：{{ shebaoPayment.relationToPatient }}
                    </div>
                </template>
                <!-- 结算信息 -->
                <template v-if="shebaoConfig.settlementInfo">
                    <div
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        医疗类别：{{ shebaoPayment.medType }}
                    </div>
                    <div
                        v-if="isJinan"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        医保支付方式：{{ extraInfo.sfrzfs }}
                    </div>
                    <div
                        v-if="!isEmpty(shebaoPayment.fundPaymentFee) || isShowZeroSettlementInfo"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        基金支付：{{ shebaoPayment.fundPaymentFee | formatMoney }}
                    </div>
                    <div
                        v-if="!isEmpty(extraInfo.hifpPay) || isShowZeroSettlementInfo"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        基金-统筹支出：{{ extraInfo.hifpPay | formatMoney }}
                    </div>
                    <div
                        v-if="!isEmpty(extraInfo.cvlservPay) || isShowZeroSettlementInfo"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        基金-公务员补助：{{ extraInfo.cvlservPay | formatMoney }}
                    </div>
                    <div
                        v-if="!isEmpty(extraInfo.hifmiPay) || isShowZeroSettlementInfo"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        基金-大病保险：{{ extraInfo.hifmiPay | formatMoney }}
                    </div>
                    <div
                        v-if="!isEmpty(extraInfo.hifobPay) || isShowZeroSettlementInfo"
                        data-type="item"
                        class="pay-text-info  no-change-line"
                        overflow
                    >
                        基金-大额补助：{{ extraInfo.hifobPay | formatMoney }}
                    </div>
                    <div
                        v-if="!isEmpty(extraInfo.mafPay) || isShowZeroSettlementInfo"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        基金-医疗救助：{{ extraInfo.mafPay | formatMoney }}
                    </div>
                    <div
                        v-if="!isEmpty(extraInfo.hifesPay) || isShowZeroSettlementInfo"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        基金-企业补充医疗保险：{{ extraInfo.hifesPay | formatMoney }}
                    </div>
                    <div
                        v-if="!isEmpty(extraInfo.othPay) || isShowZeroSettlementInfo"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        基金-其他支出：{{ extraInfo.othPay | formatMoney }}
                    </div>
                    <template v-if="!isEmpty(shebaoPayment.accountPaymentFee) || isShowZeroSettlementInfo">
                        <div
                            data-type="item"
                            class="pay-text-info no-change-line"
                            overflow
                        >
                            个账支付：{{ shebaoPayment.accountPaymentFee | formatMoney }}
                        </div>
                        <div
                            v-if="!isEmpty(extraInfo.acctMulaidPay) || isShowZeroSettlementInfo"
                            data-type="item"
                            class="pay-text-info no-change-line"
                            overflow
                        >
                            共济支付: {{ extraInfo.acctMulaidPay | formatMoney }}
                        </div>
                    </template>
                    <div
                        v-if="isShowDisplayWltpayAmt"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        医保钱包：{{ extraInfo.wltpayAmt | formatMoney }}
                    </div>
                    <div
                        v-if="!isEmpty(shebaoPayment.personalPaymentFee) || isShowZeroSettlementInfo"
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        现金支付：{{ shebaoPayment.personalPaymentFee | formatMoney }}
                    </div>
                    <template>
                        <div
                            v-if="!isEmpty(extraInfo.beforeDedcCumulative)"
                            data-type="item"
                            class="pay-text-info no-change-line"
                            overflow
                        >
                            起付线：本次{{ extraInfo.actPayDedc | formatMoney }}, 累计{{ extraInfo.beforeDedcCumulative | formatMoney }}
                        </div>
                        <div
                            data-type="item"
                            class="pay-text-info no-change-line"
                            overflow
                        >
                            起付线：本次{{ extraInfo.actPayDedc | formatMoney }}
                        </div>
                    </template>
                </template>
                <!-- 余额信息 -->
                <template v-if="shebaoConfig.balanceInfo">
                    <template v-if="isYunnan">
                        <div
                            data-type="item"
                            class="pay-text-info no-change-line"
                            overflow
                        >
                            支付前余额：{{ shebaoPayment.beforeCardBalance | formatMoney }}
                        </div>
                        <div
                            data-type="item"
                            class="pay-text-info no-change-line"
                            overflow
                        >
                            支付后余额：{{ shebaoPayment.cardBalance | formatMoney }}
                        </div>
                    </template>
                    <template v-else>
                        <div
                            data-type="item"
                            class="pay-text-info no-change-line"
                            overflow
                        >
                            医保余额：{{ shebaoPayment.cardBalance | formatMoney }}
                        </div>
                        <div
                            data-type="item"
                            class="pay-text-info no-change-line"
                            overflow
                        >
                            原有余额：{{ shebaoPayment.beforeCardBalance | formatMoney }}
                        </div>
                        <template v-if="$abcSocialSecurity && $abcSocialSecurity.config.isNeedQueryFundQuota">
                            <div
                                data-type="item"
                                class="pay-text-info no-change-line"
                                overflow
                            >
                                统筹余额：{{ extraInfo.generalFundpayBalc | formatMoney }}
                            </div>
                            <div
                                v-if="extraInfo.chronicDiseaseDBalc"
                                data-type="item"
                                class="pay-text-info no-change-line"
                                overflow
                            >
                                大病余额：{{ extraInfo.chronicDiseaseDBalc | formatMoney }}
                            </div>
                            <div
                                v-if="extraInfo.chronicDiseaseMBalc"
                                data-type="item"
                                class="pay-text-info no-change-line"
                                overflow
                            >
                                慢病余额：{{ extraInfo.chronicDiseaseMBalc | formatMoney }}
                            </div>
                        </template>
                    </template>
                </template>
                <!-- 授权人信息 -->
                <template v-if="shebaoConfig.settlementInfo && extraInfo.gongjiAccountPaymentFee">
                    <div
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        授权人：{{ extraInfo.gongjiAuthorName }}
                    </div>
                    <div
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        授权人关系：{{ extraInfo.gongjiRelation }}
                    </div>
                    <div
                        data-type="item"
                        class="pay-text-info no-change-line"
                        overflow
                    >
                        授权人账户余额：{{ extraInfo.gongjiBalc | formatMoney }}
                    </div>
                </template>
            </div>
        </div>
        <div
            v-if="clinicInfo.remark"
            class="print-split-line"
        ></div>
        <div
            v-if="clinicInfo.remark"
            data-type="mix-box"
            class="pay-info-wrapper pay-text-info text-font-weight-small"
        >
            收费备注：{{ printData.latestChargeComment || '无' }}
        </div>
        
        <div
            data-type="footer"
            class="cashier-footer"
        >
            <div class="footer-line"></div>
            <div class="footer-charge-info">
                <div
                    v-if="clinicInfo.chargeOperator"
                    class="footer-text"
                    style="width: 23%;"
                >
                    收费员：{{ printData.chargedByName }}
                </div>
                <div
                    v-if="clinicInfo.chargeDate"
                    class="footer-text"
                    style="width: 40%;"
                >
                    收费时间：{{ printData.chargedTime | parseTime('y-m-d h:i:s') }}
                </div>
                <div
                    v-if="clinicInfo.chargeDate && printData.latestOwePaidTime"
                    class="footer-text text-right"
                    style="width: 37%;"
                >
                    还款时间：{{ printData.latestOwePaidTime | parseTime('y-m-d h:i:s') }}
                </div>
                <div
                    v-if="(!clinicInfo.chargeDate || !printData.latestOwePaidTime) && clinicInfo.printDate"
                    class="footer-text text-right"
                    style="width: 37%;"
                >
                    打印时间：{{ new Date() | parseTime('y-m-d h:i:s') }}
                </div>
            </div>
            <div
                class="footer-charge-info"
                style="margin-top: 6pt;"
            >
                <div
                    class="no-change-line footer-sub-text left-text"
                    overflow
                >
                    <template v-if="clinicInfo.chargeDate && printData.latestOwePaidTime && clinicInfo.printDate">
                        打印时间：{{ new Date() | parseTime('y-m-d h:i:s') }}&nbsp;
                    </template>
                    药品离柜，概不退换
                </div>
                <div
                    v-if="clinicInfo.mobile && clinic.contactPhone"
                    class="no-change-line footer-sub-text right-text"
                    overflow
                >
                    电话：{{ clinic.contactPhone }}
                </div>
            </div>
            <div class="footer-charge-info">
                <div
                    v-if="config.remark"
                    class="no-change-line footer-sub-text left-text"
                    overflow
                >
                    备注：{{ config.remark }}
                </div>
                <div
                    v-if="clinicInfo.address && clinic.addressDetail"
                    class="no-change-line footer-sub-text right-text"
                    overflow
                >
                    地址：{{ clinic.addressDetail }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import PrintCommonDataHandler from "./data-handler/common-handler";
    import { PrintBusinessKeyEnum } from "./constant/print-constant";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import CashierHeader from "./components/medical-document-header/cashier-header.vue";
    import { GLASSES_TYPE, GoodsFeeType, PrintFormTypeEnum, SourceFormTypeEnum } from "./common/constants";
    import PrintRow from "./components/layout/print-row.vue";
    import PrintCol from "./components/layout/print-col.vue";
    import { deepClone, formatMoney, getTotalPriceByForms, parseTime } from "./common/utils";
    import ChargeFormNew from "./components/cashier-form/charge-form-new.vue";
    import ProductForm from "./components/cashier-form/product-form.vue";
    import HangzhouNew from "./components/cashier-form/shebao/hangzhou-new.vue";
    import YunnanNew from "./components/cashier-form/shebao/yunnan-new.vue";
    import ChineseFormNew from "./components/cashier-form/chinese-form-new.vue";

    export default {
        name: 'CashierA5',
        DataHandler: PrintCommonDataHandler,
        businessKey: PrintBusinessKeyEnum.CASHIER_A5,
        pages: [
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分', // 默认选择的等分纸
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        filters: {
            formatMoney,
            parseTime,
        },
        components: {
            ChineseFormNew,
            YunnanNew,
            HangzhouNew,
            ProductForm,
            PrintCol,
            PrintRow,
            CashierHeader,
            ChargeFormNew,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                },
            },
            extra: {
                type: Object,
                default() {
                    return {}
                },
            },
        },
        computed: {
            printData() {
                return this.renderData.printData;
            },
            config() {
                if(this.renderData.config && this.renderData.config.cashier) {
                    return this.renderData.config.cashier;
                }
                return {};
            },
            organ() {
                return this.printData && this.printData.organ;
            },
            organTitle() {
                if (!this.config.title) {
                    return this.organ && this.organ.name || '';
                }
                return this.config.title || '';
            },
            chargeForms() {
                return this.printData.chargeForms || [];
            },
            // 配镜处方
            glassesMedicineForms() {
                const result = [];
                const glassesForms = this.chargeForms.filter((chargeForm) => chargeForm.printFormType === PrintFormTypeEnum.PRESCRIPTION_GLASSES);
                glassesForms.forEach((glassesForm) => {
                    const cacheGlassesForm = deepClone(glassesForm);
                    const chargeFormItems = [];
                    cacheGlassesForm.glassesParams.items.forEach((item) => {
                        if (GLASSES_TYPE[cacheGlassesForm.glassesType].includes(item.key)) {
                            chargeFormItems.push(item);
                        }
                    });
                    delete cacheGlassesForm.glassesParams;
                    cacheGlassesForm.chargeFormItems = chargeFormItems;
                    result.push(cacheGlassesForm);
                });
                return result;
            },
            isSupportGlasses() {
                return this.printData.IS_GLASSES;
            },
            feeInfo() {
                return this.config.feeInfo || {};
            },
            isFeeCompose() {
                return !!this.printData.IS_FEE_COMPOSE;
            },
            medicalBills() {
                return this.printData.medicalBills || [];
            },
            // 挂号费
            registrationForms() {
                return (this.chargeForms || []).filter((form) => form.printFormType === PrintFormTypeEnum.REGISTRATION).map((form) => {
                    // 处理挂号费 label
                    if (!this.feeInfo.registration) return;

                    const cacheForm = deepClone(form);
                    (cacheForm.chargeFormItems || []).forEach((item) => {
                        if (this.feeInfo.registration === 1) {
                            if (item.name === '诊费') {
                                item.name = this.$t('registrationFeeName');
                            }
                        } else if (this.feeInfo.registration === 2) {
                            if (item.name === this.$t('registrationFeeName')) {
                                item.name = '诊费';
                            }
                        }
                    })

                    return cacheForm;
                });
            },
            // 加工费
            decoctionForms() {
                return (
                    this.chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.PROCESS;
                    }) || []
                );
            },
            // 快递费
            expressForms() {
                return (
                    this.chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.EXPRESS_DELIVERY;
                    }) || []
                );
            },
            // 检验检查
            examinationForms() {
                // 医嘱费用项分离-显示费用项
                if (this.isFeeCompose && Number(this.feeInfo.feeDetail) === 1) {
                    const result = [];
                    this.chargeForms.forEach((chargeForm) => {
                        if (chargeForm.printFormType === PrintFormTypeEnum.EXAMINATION) {
                            this.caleFeeItem(chargeForm);
                            result.push(chargeForm);
                        }
                    });
                    return result;
                }

                // 医嘱收费项未分离 ｜ 分离-显示医嘱
                return (
                    this.chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.EXAMINATION;
                    }) || []
                );
            },
            // 治疗理疗
            treatmentForms() {
                const chargeForms = deepClone(this.chargeForms);

                // 医嘱费用项分离-显示费用项
                if (this.isFeeCompose && Number(this.feeInfo.feeDetail) === 1) {
                    const result = [];
                    chargeForms.forEach((chargeForm) => {
                        if (chargeForm.printFormType === PrintFormTypeEnum.TREATMENT) {
                            this.caleFeeItem(chargeForm);
                            result.push(chargeForm);
                        }
                    });
                    return result;
                }

                // 医嘱收费项未分离 ｜ 分离-显示医嘱
                return chargeForms.filter((form) => {
                    return form.printFormType === PrintFormTypeEnum.TREATMENT;
                }) || [];
            },
            // 护理项
            nursingForms() {
                const chargeForms = deepClone(this.chargeForms);
                // 医嘱费用项分离-显示费用项
                if (this.isFeeCompose && Number(this.feeInfo.feeDetail) === 1) {
                    const result = [];
                    chargeForms.forEach((chargeForm) => {
                        if (chargeForm.printFormType === PrintFormTypeEnum.NURSING) {
                            this.caleFeeItem(chargeForm);
                            result.push(chargeForm);
                        }
                    });
                    return result;
                }

                // 医嘱收费项未分离 ｜ 分离-显示医嘱
                return chargeForms.filter((form) => {
                    return form.printFormType === PrintFormTypeEnum.NURSING;
                }) || [];
            },
            // 套餐
            composeForms() {
                let composeForms = [];
                const chargeForms = deepClone(this.chargeForms);

                // 医嘱费用项分离-显示费用项
                if (this.isFeeCompose && Number(this.feeInfo.feeDetail) === 1) {
                    const result = [];
                    chargeForms.forEach((chargeForm) => {
                        if (chargeForm.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT) {
                            this.calcFeeCompose(chargeForm);
                            result.push(chargeForm);
                        }
                    });
                    composeForms = result;
                } else {
                    // 医嘱收费项未分离 ｜ 分离-显示医嘱
                    composeForms = chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.COMPOSE_PRODUCT;
                    }) || [];
                }

                // 如果只打印套餐子项
                if (Number(this.feeInfo.composeChildren) === 1) {
                    composeForms.forEach((composeForm) => {
                        let tempChargeFormItems = [];
                        if (composeForm.chargeFormItems && composeForm.chargeFormItems.length) {
                            tempChargeFormItems = deepClone(composeForm.chargeFormItems);
                            composeForm.chargeFormItems = [];
                        }
                        tempChargeFormItems.forEach((chargeFormItem) => {
                            if (chargeFormItem.composeChildren && chargeFormItem.composeChildren.length) {
                                composeForm.chargeFormItems = composeForm.chargeFormItems.concat(chargeFormItem.composeChildren);
                            }
                        });
                    });
                }
                return composeForms;
            },
            // 西药 + 输液 后台统一将 ADDITIONAL_SALE_PRODUCT_FORM PRESCRIPTION_INFUSION 转换成了 printFormType 4
            westernMedicineForms() {
                // 医嘱费用项分离-显示费用项
                if (this.isFeeCompose && Number(this.feeInfo.feeDetail) === 1) {
                    const result = [];
                    this.chargeForms.forEach((chargeForm) => {
                        if (chargeForm.printFormType === PrintFormTypeEnum.PRESCRIPTION_WESTERN || chargeForm.printFormType === PrintFormTypeEnum.PRESCRIPTION_EXTERNAL) {
                            this.caleFeeItem(chargeForm);
                            result.push(chargeForm);
                        }
                    });
                    return result;
                }

                // 医嘱收费项未分离 ｜ 分离-显示医嘱
                return (
                    this.chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.PRESCRIPTION_WESTERN || form.printFormType === PrintFormTypeEnum.PRESCRIPTION_EXTERNAL;
                    }) || []
                );
            },
            // 中药
            chineseMedicineForms() {
                // 医嘱费用项分离-显示费用项
                if (this.isFeeCompose && Number(this.feeInfo.feeDetail) === 1) {
                    const result = [];
                    this.chargeForms.forEach((chargeForm) => {
                        if (chargeForm.printFormType === PrintFormTypeEnum.PRESCRIPTION_CHINESE || chargeForm.printFormType === PrintFormTypeEnum.AIR_PHARMACY) {
                            this.caleFeeItem(chargeForm);
                            result.push(chargeForm);
                        }
                    });
                    return result;
                }

                // 医嘱收费项未分离 ｜ 分离-显示医嘱
                return (
                    this.chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.PRESCRIPTION_CHINESE || form.printFormType === PrintFormTypeEnum.AIR_PHARMACY;
                    }) || []
                );
            },
            // 材料 商品
            materialGoodsForms() {
                // 医嘱费用项分离-显示费用项
                if (this.isFeeCompose && Number(this.feeInfo.feeDetail) === 1) {
                    const result = [];
                    this.chargeForms.forEach((chargeForm) => {
                        if (chargeForm.printFormType === PrintFormTypeEnum.MATERIAL || chargeForm.printFormType === PrintFormTypeEnum.FAMILY_DOCTOR_SIGN) {
                            this.caleFeeItem(chargeForm);
                            result.push(chargeForm);
                        }
                    });
                    return result;
                }

                // 医嘱收费项未分离 ｜ 分离-显示医嘱
                return (
                    this.chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.MATERIAL || form.printFormType === SourceFormTypeEnum.FAMILY_DOCTOR_SIGN;
                    }) || []
                );
            },
            // 其他费用
            otherForms() {
                // 医嘱费用项分离-显示费用项
                if (this.isFeeCompose && Number(this.feeInfo.feeDetail) === 1) {
                    const result = [];
                    this.chargeForms.forEach((chargeForm) => {
                        if (chargeForm.printFormType === PrintFormTypeEnum.OTHER) {
                            this.caleFeeItem(chargeForm);
                            result.push(chargeForm);
                        }
                    });
                    return result;
                }

                // 医嘱收费项未分离 ｜ 分离-显示医嘱
                return (
                    this.chargeForms.filter((form) => {
                        return form.printFormType === PrintFormTypeEnum.OTHER;
                    }) || []
                );
            },
            // 眼镜
            glassesForms() {
                return this.chargeForms.filter((form) => {
                    return form.printFormType === PrintFormTypeEnum.GLASSES;
                });
            },
            shebaoConfig() {
                return this.config.healthCardInfo || {};
            },
            autoChangeLine() {
                return this.feeInfo.autoChangeLine;
            },
            subTotals() {
                return this.printData && this.printData.subTotals;
            },
            westernMedicine() {
                return this.config.westernMedicine || {};
            },
            chinese() {
                return this.config.chinese || {};
            },
            materialGoods() {
                return this.config.materialGoods|| {};
            },
            glassesGoods() {
                return this.config.glassesGoods || {};
            },
            shebaoPayment() {
                return this.printData.shebaoPayment || {};
            },
            extraInfo() {
                return this.shebaoPayment.extraInfo || {};
            },
            region() {
                return (this.shebaoPayment && this.shebaoPayment.region) || '';
            },
            isJinan() {
                return this.region === 'shandong_jinan';
            },
            isYunnan() {
                return this.region === 'yunnan_kunming' || this.region === 'yunnan_baoshan';
            },
            isHangzhou() {
                return this.region.indexOf('zhejiang_') === 0;
            },
            // 是否打印钱包支付
            isShowDisplayWltpayAmt() {
                return this.$abcSocialSecurity?.config?.isShowWltAcctFlagSwitch && (!this.isEmpty(this.extraInfo.wltpayAmt) || !!this.isShowZeroSettlementInfo)
            },
            // 是否打印金额为0的医保结算信息
            isShowZeroSettlementInfo(){
                return this.shebaoConfig.zeroSettlementInfo;
            },
            cashierInfo() {
                return this.config.cashierInfo || {};
            },
            chargeTransactions() {
                return this.printData && this.printData.chargeTransactions || [];
            },
            actuallyReceiveTransactions() {
                return this.chargeTransactions.filter(item => item.payMode !== 20)
            },
            latestOweTransaction() {
                return this.printData.latestOweTransaction;
            },
            // 欠费状态常量
            owedStatus() {
                return {
                    NO_OWED: 0,
                    OWING: 10,
                }
            },
            // 欠费状态
            isOweStatus() {
                return this.printData.owedStatus === this.owedStatus.OWING;
            },
            // 是否会会员卡支付
            hasMemberCardPay() {
                return (
                    this.printData.memberCardBalance !== null &&
                    this.printData.memberCardBalance !== '' &&
                    this.printData.memberCardBalance !== undefined
                );
            },
            // 卡项信息
            promotionBalances() {
                return this.printData.promotionBalances || [];
            },
            // 返券
            giftCoupons() {
                return this.printData.giftCoupons || [];
            },
            hasPromotion() {
                return this.giftCoupons.length;
            },
            clinicInfo() {
                return this.config.clinicInfo || {};
            },
            clinic() {
                return this.printData.organ || {};
            },
        },
        methods: {
            formatMoney,
            getTotalPriceByForms,
            // 从非套餐中筛选出费用项
            caleFeeItem(chargeForm) {
                let tempChargeFormItems = [];
                if (chargeForm.chargeFormItems && chargeForm.chargeFormItems.length) {
                    tempChargeFormItems = deepClone(chargeForm.chargeFormItems);
                    chargeForm.chargeFormItems = [];
                }
                tempChargeFormItems?.forEach((chargeFormItem) => {
                    if (chargeFormItem.goodsFeeType === GoodsFeeType.FEE_PARENT) {
                        // 如果是医嘱
                        if (chargeFormItem.composeChildren && chargeFormItem.composeChildren.length) {
                            chargeForm.chargeFormItems = chargeForm.chargeFormItems.concat(chargeFormItem.composeChildren);
                        }
                    } else {
                        chargeForm.chargeFormItems.push(chargeFormItem);
                    }
                });
            },
            // 从套餐中筛选出费用项
            calcFeeCompose(chargeForm) {
                if (chargeForm.chargeFormItems && chargeForm.chargeFormItems.length) {
                    chargeForm.chargeFormItems.forEach((chargeFormItem) => {
                        let tempComposeChildren = []
                        if (chargeFormItem.composeChildren && chargeFormItem.composeChildren.length) {
                            tempComposeChildren = deepClone(chargeFormItem.composeChildren);
                            chargeFormItem.composeChildren = [];
                        }
                        tempComposeChildren.forEach((children) => {
                            if (children.goodsFeeType === GoodsFeeType.FEE_PARENT) {
                                // 如果是医嘱
                                if (children.composeChildren && children.composeChildren.length) {
                                    chargeFormItem.composeChildren = chargeFormItem.composeChildren.concat(children.composeChildren);
                                }
                            } else {
                                chargeFormItem.composeChildren.push(children);
                            }
                        });
                    });
                }
            },
            isEmpty(value) {
                return value === undefined || value === null || value === '' || value === '0' || value === 0;
            },
            getMobile(str) {
                if (!str) return '';
                return str.substr(-4);
            },
            formatGiftCoupons() {
                let res = [];
                if (this.giftCoupons.length) {
                    res = this.giftCoupons.map((item) => {
                        return `${item.name}(${item.count}张，` + (item.validType === 0 ? `领券后${item.validDays || "0"}天内有效)` : `${item.validEnd}前有效)`);
                    });
                    return res.join('，');
                }
                return '';
            },
        },
    }
</script>

<style lang="scss">
@import "./style/ticket-common.scss";

.cashier-a5 {
    font-family: 'Microsoft YaHei', 微软雅黑;

    .glasses-prescription-wrapper {
        margin-bottom: 6pt;

        .table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #a6a6a6;
        }

        th,
        td {
            height: 26pt;
            padding: 0;
            font-size: 10pt;
            line-height: 26pt;
            color: #000000;
            text-align: center;
            border: 1px solid #a6a6a6;
        }

        .table-bottom {
            position: relative;
            box-sizing: border-box;
            width: 100%;
            height: auto;
            border-right: 1px solid #a6a6a6;
            border-bottom: 1px solid #a6a6a6;
            border-left: 1px solid #a6a6a6;

            .requirement {
                display: inline-block;
                padding: 5pt 8pt;
                font-size: 10pt;
                line-height: 16pt;
                color: #000000;
            }
        }
    }

    .no-change-line {
        overflow: hidden;
        text-overflow: clip;
        white-space: nowrap;
    }

    .text-info {
        font-size: 10pt;
        line-height: 18pt;
    }

    .text-right {
        text-align: right;
    }

    .print-split-line {
        height: 0;
        margin: 6pt 0;
        font-size: 0;
        border-bottom: 1pt dashed #000000;
    }

    .charge-info-wrapper {
        width: 100%;

        .charge-info-group {
            width: 100%;
            font-size: 0;
        }
    }

    .text-font-weight-small {
        font-weight: 300;
    }

    .charge-sub-text {
        display: inline-block;
        font-size: 10pt;
        line-height: 12pt;
    }

    .text-margin-left {
        margin-left: 8pt;
    }

    .charge-text {
        display: inline-block;
        font-size: 10pt;
        font-weight: bold;
        line-height: 12pt;
    }

    .pay-info-wrapper {
        width: 100%;

        .pay-info-group {
            width: 100%;
            font-size: 0;
        }
    }

    .pay-text-info {
        display: inline-block;
        width: 33%;
        font-size: 8pt;
        line-height: 10pt;
    }

    .cashier-footer {
        overflow: hidden;

        .footer-line {
            width: 100%;
            height: 0;
            padding-top: 6pt;
            margin-bottom: 6pt;
            border-bottom: 1pt solid #000000;
        }

        .footer-charge-info {
            width: 100%;
            font-size: 0;
        }
    }

    .footer-text {
        display: inline-block;
        font-size: 10pt;
        line-height: 12pt;
    }

    .footer-sub-text {
        display: inline-block;
        font-size: 8pt;
        line-height: 12pt;

        &.left-text {
            float: left;
            width: 55%;
        }

        &.right-text {
            float: right;
            width: 45%;
            text-align: right;
        }
    }
}
</style>