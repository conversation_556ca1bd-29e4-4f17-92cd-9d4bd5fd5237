<template>
    <div class="execute-certificate-wrapper">
        <div class="execute-certificate-clinic-name">
            {{ clinicName }}
        </div>

        <div class="execute-certificate-title">
            治疗执行凭证
        </div>
        
        <div class="execute-certificate-patient">
            <span>姓名：{{ patientInfo.name }}</span>
            <span>{{ patientInfo.sex }}</span>
            <span v-if="patientInfo.age && patientInfo.age.year !== undefined && patientInfo.age.year !== null">{{ patientInfo.age.year }}岁</span>
        </div>
        
        <div class="execute-certificate-info">
            <div
                v-for="(item, itemIndex) in medicalInfoList"
                :key="`execute-certificate-info-${itemIndex}`"
                :style="[{ width: (itemIndex + 1) % 2 === 0 ? '40%' : '60%' }, !!item.customStyle ? item.customStyle : {}]"
            >
                {{ item.label }}：{{ item.value }}
            </div>
        </div>

        <spacing-line line-type="solid"></spacing-line>

        <div class="execute-certificate-item-wrapper">
            <div class="execute-certificate-item-name">
                执行项目
            </div>
            <div class="execute-certificate-item-count">
                本次执行次数
            </div>
            <div class="execute-certificate-item-price">
                本次执行金额
            </div>
        </div>

        <spacing-line></spacing-line>

        <div
            v-for="(item, index) in executeItems"
            :key="`execute-certificate-item-wrapper-${index}`"
            class="execute-certificate-item-wrapper"
            :style="index !== executeItems.length - 1 ? { 'padding-bottom': '8px' } : {}"
        >
            <div class="execute-certificate-item-name">
                {{ item.productName }}
            </div>
            <div class="execute-certificate-item-count">
                {{ item.averageCount }}
            </div>
            <div class="execute-certificate-item-price">
                {{ item.receivedFee | formatMoney }}
            </div>
        </div>

        <template v-if="effects.length">
            <template v-for="(item, idx) in effects">
                <spacing-line
                    :key="`execute-certificate-effects-line-${idx}`"
                    line-type="solid"
                ></spacing-line>

                <div :key="`execute-certificate-effects-wrapper-${idx}`">
                    <div
                        v-if="item.treatmentMethod"
                        class="execute-certificate-effects-item-wrapper"
                    >
                        <span class="execute-certificate-effects-title">
                            方法：
                        </span>
                        <span class="execute-certificate-effects-content">
                            {{ item.treatmentMethod }}
                        </span>
                    </div>

                    <div
                        v-if="item.treatmentSite"
                        class="execute-certificate-effects-item-wrapper"
                    >
                        <span class="execute-certificate-effects-title">
                            部位：
                        </span>
                        <span class="execute-certificate-effects-content">
                            {{ item.treatmentSite }}
                        </span>
                    </div>

                    <div
                        v-if="item.treatmentResponse"
                        class="execute-certificate-effects-item-wrapper"
                    >
                        <span class="execute-certificate-effects-title">
                            反应：
                        </span>
                        <span class="execute-certificate-effects-content">
                            {{ item.treatmentResponse }}
                        </span>
                    </div>

                    <div
                        v-if="item.etiologyPathogenesis"
                        class="execute-certificate-effects-item-wrapper"
                    >
                        <span class="execute-certificate-effects-title">
                            病因：
                        </span>
                        <span class="execute-certificate-effects-content">
                            {{ item.etiologyPathogenesis }}
                        </span>
                    </div>

                    <div
                        v-if="item.treatmentResult"
                        class="execute-certificate-effects-item-wrapper"
                    >
                        <span class="execute-certificate-effects-title">
                            结果：
                        </span>
                        <span class="execute-certificate-effects-content">
                            {{ item.treatmentResult }}
                        </span>
                    </div>

                    <div
                        v-if="item.executeDate && item.executeTimeStart && item.executeTimeEnd"
                        class="execute-certificate-effects-item-wrapper"
                        style="padding-left: 54px;"
                    >
                        <span class="execute-certificate-effects-title">
                            执行时间：
                        </span>
                        <span class="execute-certificate-effects-content">
                            {{ item.executeDate }} {{ item.executeTimeStart }}~{{ item.executeTimeEnd }}
                        </span>
                    </div>
                </div>
            </template>
        </template>

        <template v-if="printData.comment">
            <spacing-line></spacing-line>
            
            <div
                class="execute-certificate-effects-item-wrapper"
                style="padding-bottom: 0;"
            >
                <span class="execute-certificate-effects-title">
                    备注：
                </span>
                <span
                    v-if="printData.comment"
                    class="execute-certificate-effects-content"
                    v-html="printData.comment"
                >
                </span>
            </div>
        </template>

        <spacing-line line-type="solid"></spacing-line>
        
        <div
            v-for="(item, index) in executeInfos"
            :key="`execute-certificate-total-info-${index}`"
            class="execute-certificate-total-info"
            :style="!!item.customStyle ? item.customStyle : {}"
        >
            <div class="execute-certificate-total-title">
                {{ item.label }}
            </div>
            <div class="execute-certificate-total-content">
                {{ item.value }}
            </div>
        </div>
    </div>
</template>

<script>
    import PrintCommonDataHandler from "./data-handler/common-handler";
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import SpacingLine from "./components/medical-document-header/spacing-line.vue";
    import {formatMoney, formatPatientOrderNo, parseTime} from "./common/utils";

    export default {
        name: 'ExecuteCertificate',
        components: {SpacingLine},
        DataHandler: PrintCommonDataHandler,
        businessKey: PrintBusinessKeyEnum.EXECUTE_CERTIFICATE,
        pages: [
            {
                paper: PageSizeMap.MM80,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM100,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        filters: {
            formatMoney,
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                }
            },
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            clinicName() {
                return this.printData.clinicName || '';
            },
            patientInfo() {
                return this.printData.patientInfo || {};
            },
            medicalInfoList() {
                const { patientOrderNo, diagnosedDate, doctorName, executors, totalFee } = this.printData;
                const executorNames = executors.map((executor) => executor.name).join('、');
                return [
                    {
                        label: '诊号',
                        value: formatPatientOrderNo(patientOrderNo),
                    },
                    {
                        label: '费用',
                        value: formatMoney(totalFee),
                    },
                    {
                        label: '就诊日期',
                        value: parseTime(diagnosedDate, 'y-m-d'),
                    },
                    {
                        label: '医生',
                        value: doctorName || '',
                    },
                    {
                        label: '执行人',
                        value: executorNames,
                        customStyle: {
                            width: '100%',
                        },
                    },
                ];
            },
            executeItems() {
                const { executeRecordStat } = this.printData;
                const { data } = executeRecordStat || {};
                return data || [];
            },
            executeInfos() {
                const { created, createdByName, executeRecordStat } = this.printData;
                const { summary } = executeRecordStat || {};
                return [
                    {
                        label: '本次执行金额合计',
                        value: formatMoney(summary),
                        customStyle: {
                            'font-size': '13px',
                            'font-weight': 700,
                            'line-height': 'normal',
                        },
                    },
                    {
                        label: '执行登记人',
                        value: createdByName,
                    },
                    {
                        label: '执行登记时间',
                        value: parseTime(created, 'y-m-d h:i'),
                    },
                ];
            },
            effects() {
                return this.printData.effects || [];
            },
        },
    }
</script>

<style lang="scss">
.execute-certificate-wrapper {
    .execute-certificate-clinic-name {
        font-family: SimSun, serif;
        font-size: 19px;
        font-weight: 900;
        line-height: 27px;
        text-align: center;
    }

    .execute-certificate-title {
        padding-bottom: 16px;
        font-family: SimSun, serif;
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        text-align: center;
    }

    .execute-certificate-patient {
        display: flex;
        gap: 8px;
        padding-bottom: 4px;
        font-size: 11px;
        line-height: 15px;
    }

    .execute-certificate-info {
        display: flex;
        flex-wrap: wrap;
        gap: 4px 0;
        font-size: 11px;
        line-height: 15px;
    }

    .execute-certificate-item-wrapper {
        display: flex;
        width: 100%;
        font-size: 11px;
        line-height: 15px;
    }

    .execute-certificate-item-name {
        flex: 1;
    }

    .execute-certificate-item-count {
        width: 29%;
        text-align: right;
    }

    .execute-certificate-item-price {
        width: 33%;
        text-align: right;
    }

    .execute-certificate-total-info {
        display: flex;
        width: 100%;
        font-size: 11px;
        line-height: 15px;

        &:not(:last-child) {
            padding-bottom: 6px;
        }
    }

    .execute-certificate-total-title {
        width: 42%;
    }

    .execute-certificate-total-content {
        flex: 1;
        text-align: right;
    }

    .execute-certificate-effects-item-wrapper {
        position: relative;
        padding-bottom: 8px;
        padding-left: 32px;
        font-size: 11px;
        line-height: 15px;

        &:last-child {
            padding-bottom: 0;
        }
    }

    .execute-certificate-effects-title {
        position: absolute;
        top: 0;
        left: 0;
        font-weight: 700;
    }

    .execute-certificate-effects-content {
        font-weight: 400;
    }
}
</style>
