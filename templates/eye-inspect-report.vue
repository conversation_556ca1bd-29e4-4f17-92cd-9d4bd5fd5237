<template>
    <div class="eye-inspect-report-wrapper">
        <eye-inspect-header
            :print-data="printData"
            :print-config="printConfig"
        ></eye-inspect-header>

        <eye-inspect-merge-item-table
            :items-value="printData.itemsValue"
            :content-config="contentConfig"
            :style-config="styleConfig"
            current-page-is-a4
        ></eye-inspect-merge-item-table>

        <abc-print-space :value="16"></abc-print-space>

        <div
            v-if="contentConfig.diagnosisAdvice"
            class="eye-report-diagnosis-advice"
        >
            <div
                class="label"
                :style="`background: ${styleConfig.themeColor}`"
            >
                诊断意见
            </div>

            <abc-html
                class="content"
                :value="printData.remark"
            ></abc-html>
        </div>

        <eye-inspect-footer
            :print-data="printData"
            :print-config="printConfig"
        ></eye-inspect-footer>
    </div>
</template>

<script>
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import PageSizeMap, {Orientation} from "../share/page-size";
    import EyeInspectHeader from "./components/eye-inspect-report/header.vue";
    import EyeInspectFooter from "./components/eye-inspect-report/footer.vue";
    import AbcHtml from "./components/layout/abc-html.vue";
    import { EyeInspectDataHandler } from './data-handler/eye-inspect'
    // import EyeInspectNoMergeItemTable from "./components/eye-inspect-report/no-merge-item-table.vue";
    import EyeInspectMergeItemTable from "./components/eye-inspect-report/merge-item-table.vue";
    import AbcPrintSpace from "./components/layout/space.vue";

    export default {
        name: 'EyeInspectReport',

        DataHandler: EyeInspectDataHandler,

        components: {
            AbcPrintSpace,
            EyeInspectMergeItemTable,
            // EyeInspectNoMergeItemTable,
            AbcHtml,
            EyeInspectFooter,
            EyeInspectHeader
        },

        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
        },

        businessKey: PrintBusinessKeyEnum.EYE_INSPECT_REPORT,

        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],

        computed: {
            printData() {
                return this.renderData.printData;
            },
            printConfig() {
                return this.renderData.config.medicalDocuments.eyeInspectReport || {};
            },
            contentConfig() {
                return this.printConfig.content || {};
            },
            styleConfig() {
                return this.printConfig.style || {};
            }
        },

        methods: {

        },
    }
</script>

<style lang="scss">
.eye-report-diagnosis-advice {
    display: flex;
    font-size: 10pt;
    border: 1px solid #A6A6A6;

    .label {
        border-right: 1px solid #A6A6A6;
        width: 60pt;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: #8EAD24;
        color: #ffffff;
        font-family: "Microsoft YaHei UI";
        font-size: 10pt;
        font-style: normal;
        font-weight: 600;
        line-height: 12pt; /* 120% */
    }

    .content {
        flex: 1;
        padding: 10pt 8pt;
        background: #EFEFEF;
        color: #2B2D32;
        font-family: "Microsoft YaHei UI";
        font-size: 10pt;
        font-style: normal;
        font-weight: 400;
        line-height: 12pt; /* 120% */
    }
}
</style>