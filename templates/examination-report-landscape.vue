<template>
    <div>
        <template v-for="(report, reportIdx) in examinationReportList">
            <examination-report-header
                :key="reportIdx"
                :print-data="report"
                print-title="检查检验单"
                :header-config="headerConfig"
                data-type="header"
                :organ-title="organTitle"
                :data-pendants-index="`${reportIdx}examinationReport`"
            ></examination-report-header>

            <template v-for="(pageItem, pageIndex) in report.renderPage">
                <template v-if="pageItem.isDoubleColumn">
                    <div
                        :key="pageIndex"
                        class="report-base-landscape-wrapper"
                    >
                        <!-- 左边的表格 -->
                        <div
                            v-if="pageItem.leftItems.length"
                            class="report-content-wrapper"
                            :class="{ 'has-two-cols left-cols': pageItem.hasTwoCol }"
                        >
                            <examination-landscape-table
                                :list="pageItem.leftItems"
                                :content-config="contentConfig"
                                :start-idx="pageIndex * columnNumber * 2"
                                :is-single-column="!pageItem.hasTwoCol"
                                :mode="checkReportIsWillRenderImage(report) ? 'compact' : 'loose'"
                            ></examination-landscape-table>
                        </div>

                        <!-- 右边的表格 -->
                        <div
                            v-if="pageItem.rightItems.length"
                            class="report-content-wrapper"
                            :class="{ 'has-two-cols': pageItem.hasTwoCol }"
                        >
                            <examination-landscape-table
                                :list="pageItem.rightItems"
                                :content-config="contentConfig"
                                :start-idx="
                                    pageIndex * columnNumber * 2 + pageItem.leftItems.length
                                "
                                :is-single-column="false"
                                :mode="checkReportIsWillRenderImage(report) ? 'compact' : 'loose'"
                            ></examination-landscape-table>
                        </div>
                    </div>

                    <!-- 直方图，放到最后一页 -->
                    <template v-if="pageItem.isLastPage && contentConfig.bloodHistogram">
                        <examination-landscape-images
                            :key="pageIndex"
                            :list="report.attachments"
                            data-type="fixed-bottom-box"
                            is-single-column
                        ></examination-landscape-images>
                    </template>

                    <!-- 条数超出最大限制，分页展示 -->
                    <div
                        v-if="!pageItem.isLastPage"
                        :key="pageIndex"
                        data-type="new-page"
                    ></div>
                </template>

                <template v-else-if="pageItem.items.length">
                    <examination-landscape-table
                        :key="pageIndex"
                        :list="pageItem.items"
                        :content-config="contentConfig"
                        is-single-column
                    ></examination-landscape-table>

                    <template v-if="contentConfig.bloodHistogram">
                        <examination-landscape-images
                            :key="pageIndex"
                            :list="report.attachments"
                            data-type="fixed-bottom-box"
                        ></examination-landscape-images>
                    </template>
                </template>
            </template>

            <examination-report-footer
                :key="reportIdx"
                :print-data="report"
                data-type="footer"
                print-type="examination"
                :config="config"
                :data-pendants-index="`${reportIdx}examinationReport`"
            ></examination-report-footer>
        </template>
    </div>
</template>

<script>
    import ExaminationReportHeader from './components/examination-report-landscape/examination-landscape-header.vue'
    import ExaminationReportFooter from './components/examination-report-landscape/examination-landscape-footer.vue'
    import ExaminationLandscapeTable from './components/examination-report-landscape/examination-landscape-table.vue'
    import ExaminationLandscapeImages from './components/examination-report-landscape/examination-landscape-images.vue'

    import PrintCommonHandler from './data-handler/examination-report-handler.js'

    import { calRange, calValue2 } from './common/medical-transformat.js'
    import { PrintBusinessKeyEnum } from './constant/print-constant.js'
    import PageSizeMap, { Orientation } from '../share/page-size.js'
    import clone from './common/clone.js'

    const LEFT_COUNT = 17

    export default {
        DataHandler: PrintCommonHandler,
        name: 'ExaminationReport',
        components: {
            ExaminationReportHeader,
            ExaminationReportFooter,
            ExaminationLandscapeTable,
            ExaminationLandscapeImages
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {}
                }
            }
        },
        businessKey: PrintBusinessKeyEnum.EXAMINATION_REPORT,
        imageTransformSetting: 'x-oss-process=image/resize,p_90',
        pages: [
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.landscape, // 默认方向
                defaultHeightLevel: null
            }
        ],

        computed: {
            printData() {
                return this.renderData.printData || {}
            },

            config() {
                if (
                    this.renderData.config &&
                    this.renderData.config.medicalDocuments &&
                    this.renderData.config.medicalDocuments.examineReport
                ) {
                    return this.renderData.config.medicalDocuments.examineReport
                }
                return {}
            },

            headerConfig() {
                const originConfig = this.config.header || {}
                // 自定义logo的门店列表
                const customLogoClinicIdList = [
                    'ffffffff0000000034a7aff0c70f8002' // 包头稀土高新区稀土路街道办事处社区卫生服务中心
                ]

                return {
                    ...originConfig,
                    isCustomLogo: customLogoClinicIdList.includes(this.printData.organPrintView?.id)
                }
            },

            remark() {
                return this.printData.remark || ''
            },

            contentConfig() {
                return (this.config && this.config.content) || {}
            },

            organ() {
                return this.printData.organPrintView
            },

            organTitle() {
                return this.organ && this.organ.name
            },

            columnNumber() {
                return this.renderData.printOptions.columnNumber || 17
            },

            // 检验报告列表
            examinationReportList() {
                const { isMerge, isDoubleColumn, columnNumber, printProjectIdListOnMergeMode } =
                    this.renderData.printOptions

                const createExaminationPrintReportData = (items, name) => {
                    const data = clone(this.printData)
                    name && (data.name = name)
                    data.attachments = items.filter((item) => item.valueType === 'IMAGE') || []
                    data.items = items.filter((item) => item.valueType !== 'IMAGE') || []
                    if (isDoubleColumn) {
                        // 横向分两栏，条数超出两栏分到下一页
                        data.renderPage = []
                        const _items = clone(data.items)
                        while (_items.length) {
                            data.renderPage.push(_items.splice(0, 2 * columnNumber))
                        }
                        data.renderPage = data.renderPage.map((page, idx) => {
                            return {
                                leftItems: page.slice(0, columnNumber),
                                rightItems: page.slice(columnNumber),
                                items: page,
                                hasTwoCol: page.slice(columnNumber).length > 0,
                                isDoubleColumn:
                                    isDoubleColumn && page.slice(columnNumber).length > 0,
                                isLastPage: idx === data.renderPage.length - 1
                            }
                        })
                    } else {
                        // 不分栏
                        data.renderPage = [
                            {
                                items: data.items,
                                isDoubleColumn: false
                            }
                        ]
                    }

                    return data
                }

                if (isMerge) {
                    const items = (this.printData.itemsValue || []).filter((item) =>
                        printProjectIdListOnMergeMode.includes(item.groupById)
                    )
                    const printData = createExaminationPrintReportData(items)
                    // 合并打印
                    return [printData]
                } else {
                    // 拆分打印

                    // 所有指标
                    const items = this.printData.itemsValue || []

                    // 根据 groupById 拆分
                    const splitExaminationProjectList = items.reduce((res, item) => {
                        const groupItem = res.find((r) => r.groupById === item.groupById)

                        if (groupItem !== undefined) {
                            groupItem.items.push(item)
                        } else {
                            const groupItem = {
                                groupBy: item.groupBy,
                                groupById: item.groupById,
                                items: [item]
                            }

                            res.push(groupItem)
                        }

                        return res
                    }, [])

                    return splitExaminationProjectList.map((groupItem) => {
                        return createExaminationPrintReportData(groupItem.items, groupItem.groupBy)
                    })
                }
            },

            // 是否会渲染图片
            isRenderImage() {
                return this.renderData.printOptions.isRenderImage || false
            }
        },

        mounted() {
            console.log(this.examinationReportList, '检验报告打印列表')
        },

        methods: {
            calValue2,
            calRange,

            checkReportIsWillRenderImage(report) {
                return this.contentConfig.bloodHistogram && report.attachments.length > 0
            }
        }
    }
</script>

<style lang="scss">
    @import './style/reset.scss';
    @import './components/layout/print-layout.scss';

    .abc-page-content {
        box-sizing: border-box;
        font-family: 'Microsoft YaHei', '微软雅黑';
    }

    .report-base-landscape-wrapper {
        display: flex;

        .report-content-wrapper {
            width: 100%;

            &.has-two-cols {
                display: inline-block;
                width: 49.8%;
                vertical-align: top;
            }

            &.left-cols {
                border-right: 1pt solid #000000;
            }
        }
    }
</style>
