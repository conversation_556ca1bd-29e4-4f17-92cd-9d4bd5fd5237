<template>
    <div class="prescription-v2-landscape-wrapper">
        <template v-for="(form, formIndex) in printData.prescriptionWesternForms">
            <prescription-v2-header
                :key="`${formIndex}WP`"
                data-type="header"
                :data-pendants-index="`${formIndex}WP`"
                :print-data="printData"
                :config="config"
                :show-express="!!(form.deliveryInfo && config.westernExpressInfo)"
                :prescription-show-pharmacy-name="calcCurrentPrescriptionShowPharmacyName(form)"
                :show-pharmacy-name="config.pharmacyName"
                :pharmacy-name="form.pharmacyName"
                :print-jinma="form.psychotropicNarcoticType"
                :patient-order-no="printData.patientOrderNo"
                :diagnosis="printData.diagnosis"
                :open-pharmacy-flag="printData.openPharmacyFlag"
                :organ="printData.organ"
                :patient="printData.patient"
                :diagnosed-date="printData.diagnosedDate"
                :shebao-card-info="printData.shebaoCardInfo"
                :department-name="printData.departmentName"
                :health-card-pay-level="printData.healthCardPayLevel"
                :revisit-status="printData.revisitStatus"
                :health-card-no="printData.healthCardNo"
                :medical-record="printData.medicalRecord"
                is-horizontal
            ></prescription-v2-header>

            <template v-if="form.isStandardForm">
                <template v-for="(formItems, formItemsIndex) in form.prescriptionFormItems">
                    <western-item-v2
                        v-for="formItem in formItems"
                        data-type="mix-box"
                        :config="config"
                        :form-item="formItem"
                        is-horizontal
                        :show-group-icon="checkExistedGroupId(form)"
                        :doctor-name="printData.doctorName"
                        :doctor-sign-img-url="printData.doctorSignImgUrl"
                        :style="{ 'padding-top': formItemsIndex ? '12px' : '5px' }"
                    ></western-item-v2>
                    <div
                        v-if="form.prescriptionFormItems.length - 1 !== formItemsIndex"
                        data-type="new-page"
                    ></div>
                </template>
            </template>
            <template v-else>
                <western-item-v2
                    v-for="(formItem, formItemIndex) in form.prescriptionFormItems"
                    data-type="mix-box"
                    :config="config"
                    :form-item="formItem"
                    is-horizontal
                    :show-group-icon="checkExistedGroupId(form)"
                    :doctor-name="printData.doctorName"
                    :doctor-sign-img-url="printData.doctorSignImgUrl"
                    :style="{ 'padding-top': formItemIndex ? '12px' : '5px' }"
                ></western-item-v2>
            </template>

            <div v-if="config.westernExpressInfo && form.deliveryInfo">
                <spacing-line></spacing-line>
                <delivery-info-v2
                    :delivery-info="form.deliveryInfo"
                    is-horizontal
                ></delivery-info-v2>
            </div>

            <div v-if="config.westernDoctorAdvice && doctorAdvice.length">
                <spacing-line></spacing-line>
                <doctor-advice-v2 :doctor-advice="doctorAdvice"></doctor-advice-v2>
            </div>

            <next-is-blank-v2 v-if="!isPreview"></next-is-blank-v2>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}WP`"
            >
                <prescription-v2-footer
                    :config="config"
                    :qr-code="printData.qrCode"
                    :doctor-sign-img-url="printData.doctorSignImgUrl"
                    :doctor-name="printData.doctorName"
                    :audit-hand-sign="form.dispensingInfo ? form.dispensingInfo.auditHandSign : ''"
                    :audit-name="form.dispensingInfo ? form.dispensingInfo.auditName : ''"
                    :compound-by-hand-sign="form.dispensingInfo ? form.dispensingInfo.compoundByHandSign : ''"
                    :compound-name="form.dispensingInfo ? form.dispensingInfo.compoundName : ''"
                    :dispensed-by-hand-sign="form.dispensingInfo ? form.dispensingInfo.dispensedByHandSign : ''"
                    :dispensed-by-name="form.dispensingInfo ? form.dispensingInfo.dispensedByName : ''"
                    :display-total-price="printData.displayTotalPrice"
                    :form="form"
                    :bill-sign="config.westernBillSign"
                    :print-time="printData.printTime"
                ></prescription-v2-footer>
            </div>
        </template>

        <template v-for="(form, formIndex) in printData.prescriptionInfusionForms">
            <prescription-v2-header
                data-type="header"
                :data-pendants-index="`${formIndex}IP`"
                :print-data="printData"
                :config="config"
                :show-express="!!(form.deliveryInfo && config.westernExpressInfo)"
                :prescription-show-pharmacy-name="calcCurrentPrescriptionShowPharmacyName(form)"
                :pharmacy-name="form.pharmacyName"
                :print-jinma="form.psychotropicNarcoticType"
                :patient-order-no="printData.patientOrderNo"
                :diagnosis="printData.diagnosis"
                :open-pharmacy-flag="printData.openPharmacyFlag"
                :organ="printData.organ"
                :patient="printData.patient"
                :diagnosed-date="printData.diagnosedDate"
                :shebao-card-info="printData.shebaoCardInfo"
                :department-name="printData.departmentName"
                :health-card-pay-level="printData.healthCardPayLevel"
                :revisit-status="printData.revisitStatus"
                :health-card-no="printData.healthCardNo"
                :medical-record="printData.medicalRecord"
                is-horizontal
            ></prescription-v2-header>

            <template v-if="form.isStandardForm">
                <template v-for="(formItems, formItemsIndex) in form.prescriptionFormItems">
                    <div
                        v-for="(groupFormItems, groupFormItemIndex) in getInfusionGroupItems(formItems)"
                        data-type="mix-box"
                    >
                        <infusion-item-v2
                            :group-form-items="groupFormItems"
                            :config="config"
                            :doctor-name="printData.doctorName"
                            :doctor-sign-img-url="printData.doctorSignImgUrl"
                            :is-first="groupFormItemIndex === 0"
                            is-horizontal
                        ></infusion-item-v2>
                    </div>
                    <div
                        v-if="form.prescriptionFormItems.length - 1 !== formItemsIndex"
                        data-type="new-page"
                    ></div>
                </template>
            </template>
            <template v-else>
                <div
                    v-for="(groupFormItems, groupFormItemsIndex) in getInfusionGroupItems(form.prescriptionFormItems)"
                    data-type="mix-box"
                >
                    <infusion-item-v2
                        :group-form-items="groupFormItems"
                        :config="config"
                        :doctor-name="printData.doctorName"
                        :doctor-sign-img-url="printData.doctorSignImgUrl"
                        :is-first="groupFormItemsIndex === 0"
                        is-horizontal
                    ></infusion-item-v2>
                </div>
            </template>

            <div v-if="!isPreview && config.westernExpressInfo && form.deliveryInfo">
                <spacing-line></spacing-line>
                <delivery-info-v2
                    :delivery-info="form.deliveryInfo"
                    is-horizontal
                ></delivery-info-v2>
            </div>

            <div v-if="!isPreview && config.westernDoctorAdvice && doctorAdvice.length">
                <spacing-line></spacing-line>
                <doctor-advice-v2 :doctor-advice="doctorAdvice"></doctor-advice-v2>
            </div>

            <next-is-blank-v2 v-if="!isPreview"></next-is-blank-v2>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}IP`"
            >
                <prescription-v2-footer
                    :config="config"
                    :qr-code="printData.qrCode"
                    :doctor-sign-img-url="printData.doctorSignImgUrl"
                    :doctor-name="printData.doctorName"
                    :audit-hand-sign="form.dispensingInfo ? form.dispensingInfo.auditHandSign : ''"
                    :audit-name="form.dispensingInfo ? form.dispensingInfo.auditName : ''"
                    :compound-by-hand-sign="form.dispensingInfo ? form.dispensingInfo.compoundByHandSign : ''"
                    :compound-name="form.dispensingInfo ? form.dispensingInfo.compoundName : ''"
                    :dispensed-by-hand-sign="form.dispensingInfo ? form.dispensingInfo.dispensedByHandSign : ''"
                    :dispensed-by-name="form.dispensingInfo ? form.dispensingInfo.dispensedByName : ''"
                    :display-total-price="printData.displayTotalPrice"
                    :form="form"
                    :bill-sign="config.westernBillSign"
                    :print-time="printData.printTime"
                ></prescription-v2-footer>
            </div>
        </template>

        <template v-for="(chineseForm, formIndex) in printData.prescriptionChineseForms">
            <prescription-v2-header
                data-type="header"
                :data-pendants-index="`${formIndex}CP`"
                :print-data="printData"
                :config="config"
                :show-express="
                    config.chineseExpressInfo &&
                        chineseForm.deliveryInfo &&
                        chineseForm.deliveryInfo.chargeStatus < ChargeItemStatusEnum.REFUND
                "
                :show-process="
                    config.chineseProcessInfo &&
                        chineseForm.processInfo &&
                        chineseForm.processInfo.chargeStatus < ChargeItemStatusEnum.REFUND
                "
                :prescription-show-pharmacy-name="calcCurrentPrescriptionShowPharmacyName(chineseForm)"
                :pharmacy-name="getPharmacyName(chineseForm)"
                :extend-spec="chineseForm.specification"
                :print-jinma="chineseForm.psychotropicNarcoticType"
                :show-virtual-pharmacy="chineseForm.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY"
                :patient-order-no="printData.patientOrderNo"
                :diagnosis="printData.diagnosis"
                :open-pharmacy-flag="printData.openPharmacyFlag"
                :organ="printData.organ"
                :patient="printData.patient"
                :diagnosed-date="printData.diagnosedDate"
                :shebao-card-info="printData.shebaoCardInfo"
                :department-name="printData.departmentName"
                :health-card-pay-level="printData.healthCardPayLevel"
                :revisit-status="printData.revisitStatus"
                :health-card-no="printData.healthCardNo"
                :medical-record="printData.medicalRecord"
                :take-medication-time="getTakeMedicationTime(chineseForm)"
                is-horizontal
            ></prescription-v2-header>

            <div data-type="mix-box">
                <template v-for="groupItems in getChineseGroupItems(chineseForm.prescriptionFormItems, groupCount)">
                    <chinese-item-v2
                        :form-items="groupItems"
                        :config="config"
                        :group-count="groupCount"
                        :chinese-form="chineseForm"
                        :doctor-name="printData.doctorName"
                        :doctor-sign-img-url="printData.doctorSignImgUrl"
                        is-horizontal
                    ></chinese-item-v2>
                </template>
            </div>

            <!-- 用法 -->
            <chinese-usage
                :form="chineseForm"
                :chinese-medicine-total-count="config.chineseMedicineTotalCount"
                is-horizontal
            ></chinese-usage>

            <!-- 加工 -->
            <div
                v-if="config.chineseProcessInfo &&
                    chineseForm.processInfo &&
                    chineseForm.processInfo.chargeStatus < ChargeItemStatusEnum.REFUND &&
                    (getProcessInfoStr(chineseForm) || chineseForm.processRemark)"
            >
                <spacing-line></spacing-line>
                <chinese-process
                    :process-info-str="getProcessInfoStr(chineseForm)"
                    :process-remark="chineseForm.processRemark"
                ></chinese-process>
            </div>

            <div
                v-if="!isPreview &&
                    config.chineseExpressInfo &&
                    chineseForm.deliveryInfo &&
                    chineseForm.deliveryInfo.deliveryCompany &&
                    chineseForm.deliveryInfo.deliveryCompany.id &&
                    chineseForm.deliveryInfo.chargeStatus < ChargeItemStatusEnum.REFUND"
            >
                <spacing-line></spacing-line>
                <delivery-info-v2
                    :delivery-info="chineseForm.deliveryInfo"
                    is-horizontal
                ></delivery-info-v2>
            </div>

            <div v-if="!isPreview && config.chineseDoctorAdvice && doctorAdvice.length">
                <spacing-line></spacing-line>
                <doctor-advice-v2 :doctor-advice="doctorAdvice"></doctor-advice-v2>
            </div>

            <next-is-blank-v2 v-if="!isPreview"></next-is-blank-v2>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}CP`"
            >
                <prescription-v2-footer
                    :config="config"
                    :qr-code="printData.qrCode"
                    :doctor-sign-img-url="printData.doctorSignImgUrl"
                    :doctor-name="printData.doctorName"
                    :audit-hand-sign="chineseForm.dispensingInfo ? chineseForm.dispensingInfo.auditHandSign : ''"
                    :audit-name="chineseForm.dispensingInfo ? chineseForm.dispensingInfo.auditName : ''"
                    :compound-by-hand-sign="chineseForm.dispensingInfo ? chineseForm.dispensingInfo.compoundByHandSign : ''"
                    :compound-name="chineseForm.dispensingInfo ? chineseForm.dispensingInfo.compoundName : ''"
                    :dispensed-by-hand-sign="chineseForm.dispensingInfo ? chineseForm.dispensingInfo.dispensedByHandSign : ''"
                    :dispensed-by-name="chineseForm.dispensingInfo ? chineseForm.dispensingInfo.dispensedByName : ''"
                    :display-total-price="printData.displayTotalPrice"
                    :form="chineseForm"
                    :bill-sign="config.chineseBillSign"
                    :print-time="printData.printTime"
                    is-chinese
                ></prescription-v2-footer>
            </div>
        </template>

        <template v-for="(externalForm, formIndex) in printData.prescriptionExternalForms">
            <prescription-v2-header
                :key="`${formIndex}EP`"
                data-type="header"
                :data-pendants-index="`${formIndex}EP`"
                :print-data="printData"
                :config="config"
                :show-express="!!externalForm.deliveryInfo"
                :pharmacy-name="externalForm.pharmacyName"
                :print-jinma="externalForm && externalForm.psychotropicNarcoticType"
                :patient-order-no="printData.patientOrderNo"
                :diagnosis="printData.diagnosis"
                :open-pharmacy-flag="printData.openPharmacyFlag"
                :organ="printData.organ"
                :patient="printData.patient"
                :diagnosed-date="printData.diagnosedDate"
                :shebao-card-info="printData.shebaoCardInfo"
                :department-name="printData.departmentName"
                :health-card-pay-level="printData.healthCardPayLevel"
                :revisit-status="printData.revisitStatus"
                :health-card-no="printData.healthCardNo"
                :medical-record="printData.medicalRecord"
                :extend-spec="!config.mergeExternal && config.prescriptionType ? getUsageTypeStr(externalForm) : ''"
                is-horizontal
            ></prescription-v2-header>

            <template v-for="(formItem, formItemIndex) in externalForm.prescriptionFormItems">
                <external-form
                    :usage-sub-type="externalForm.usageSubType"
                    :usage-type="externalForm.usageType"
                    :form="externalForm"
                    :form-item="formItem"
                    :serial-number="formItemIndex + 1"
                    :config="config"
                    :external-form="externalForm"
                    :get-usage-type-str="getUsageTypeStr"
                    is-horizontal
                ></external-form>
            </template>

            <div v-if="config.westernDoctorAdvice && doctorAdvice.length">
                <spacing-line></spacing-line>
                <doctor-advice-v2 :doctor-advice="doctorAdvice"></doctor-advice-v2>
            </div>

            <next-is-blank-v2 v-if="!isPreview"></next-is-blank-v2>

            <div
                data-type="footer"
                :data-pendants-index="`${formIndex}EP`"
            >
                <prescription-v2-footer
                    :config="config"
                    :qr-code="printData.qrCode"
                    :doctor-sign-img-url="printData.doctorSignImgUrl"
                    :doctor-name="printData.doctorName"
                    :audit-hand-sign="externalForm.dispensingInfo ? externalForm.dispensingInfo.auditHandSign : ''"
                    :audit-name="externalForm.dispensingInfo ? externalForm.dispensingInfo.auditName : ''"
                    :compound-by-hand-sign="externalForm.dispensingInfo ? externalForm.dispensingInfo.compoundByHandSign : ''"
                    :compound-name="externalForm.dispensingInfo ? externalForm.dispensingInfo.compoundName : ''"
                    :dispensed-by-hand-sign="externalForm.dispensingInfo ? externalForm.dispensingInfo.dispensedByHandSign : ''"
                    :dispensed-by-name="externalForm.dispensingInfo ? externalForm.dispensingInfo.dispensedByName : ''"
                    :display-total-price="printData.displayTotalPrice"
                    :form="externalForm"
                    :bill-sign="config.westernBillSign"
                    :print-time="printData.printTime"
                ></prescription-v2-footer>
            </div>
        </template>

        <template v-for="(glassesForm, glassesIndex) in printData.prescriptionGlassesForms">
            <glasses-prescription-header
                data-type="header"
                :print-data="printData"
                :config="glassesConfig"
                print-title="配镜处方"
                :organ-title="organTitle"
                :data-pendants-index="`${glassesIndex}GP`"
            ></glasses-prescription-header>

            <div class="glasses-prescription-wrapper">
                <table class="table">
                    <thead>
                        <tr>
                            <th
                                :style="glassesForm.glassesType !== 0 ? null : { width: '9.5%' }"
                            >
                                <template v-if="glassesForm.glassesType === 0">
                                    {{ glassesForm.usage }}
                                </template>
                            </th>
                            <th
                                v-for="formItem in glassesForm.chargeFormItems"
                                :key="formItem.key"
                                :style="glassesForm.glassesType !== 0 ? { width: '16.6%' } : formItem.key !== 'frameCva' ? { width: '9.5%' } : null"
                            >
                                {{ formItem.name }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>右眼</td>
                            <td
                                v-for="formItem in glassesForm.chargeFormItems"
                                :key="formItem.key"
                            >
                                {{ formItem.rightEyeValue }}
                            </td>
                        </tr>
                        <tr>
                            <td>左眼</td>
                            <td
                                v-for="formItem in glassesForm.chargeFormItems"
                                :key="formItem.key"
                            >
                                {{ formItem.leftEyeValue }}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="table-bottom">
                    <div class="requirement">
                        备注：{{ glassesForm.requirement }}
                    </div>
                    <div
                        v-if="glassesFooterConfig.optometristSignature"
                        class="optometristName"
                    >
                        验光师：{{ glassesForm.optometristName }}
                    </div>
                    <div class="clear-float"></div>
                </div>
                <div class="remark">
                    注：{{ glassesFooterConfig.remark }}
                </div>
            </div>
        </template>

        <div
            v-if="!isPreview"
            class="prescription-v2-next-page"
            data-type="next-page"
        >
            (接下页)
        </div>
        <div
            v-if="!isPreview"
            class="prescription-v2-prev-page"
            data-type="prev-page"
        >
            (接上页)
        </div>
    </div>
</template>

<script>
    import PrescriptionHandler from "./data-handler/prescription-handler";
    import { ExternalPRUsageTypeEnumRevert, PrintBusinessKeyEnum } from "./constant/print-constant";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import PrescriptionV2Header from "./components/medical-document-header/header-v2/prescription-v2-header.vue";
    import prescriptionMixin from "./components/prescription/prescription-v2/prescription-mixin";
    import { ChargeItemStatusEnum, GLASSES_TYPE, PharmacyTypeEnum } from "./common/constants";
    import PrescriptionV2Footer from "./components/medical-document-footer/footer-v2/prescription-v2-footer.vue";
    import WesternItemV2 from "./components/prescription/prescription-v2/western-item-v2.vue";
    import SpacingLine from "./components/medical-document-header/spacing-line.vue";
    import NextIsBlankV2 from "./components/prescription/prescription-v2/next-is-blank-v2.vue";
    import DeliveryInfoV2 from "./components/prescription/prescription-v2/delivery-info-v2.vue";
    import DoctorAdviceV2 from "./components/prescription/prescription-v2/doctor-advice-v2.vue";
    import InfusionItemV2 from "./components/prescription/prescription-v2/infusion-item-v2.vue";
    import ChineseItemV2 from "./components/prescription/prescription-v2/chinese-item-v2.vue";
    import ChineseProcess from "./components/prescription/prescription-v2/chinese-process.vue";
    import ChineseUsage from "./components/prescription/prescription-v2/chinese-usage.vue";
    import ExternalForm from "./components/prescription/prescription-v2/external-form.vue";
    import {
        ExternalPRUsageTypeEnum,
        AiJiuSubOptions,
        BaGuanSubOptions,
        TieFuSubOptions,
        TuiNaSubOptions,
        UsageTypeOptions,
        ZhenCiSubOptions,
    } from "./constant/external-constants";
    import GlassesPrescriptionHeader from './components/medical-document-header/glasses-prescription-header.vue';

    export default {
        name: 'PrescriptionV2Landscape',
        components: {
            GlassesPrescriptionHeader,
            ExternalForm,
            ChineseUsage,
            ChineseProcess,
            ChineseItemV2,
            InfusionItemV2,
            DoctorAdviceV2,
            DeliveryInfoV2,
            NextIsBlankV2,
            SpacingLine,
            WesternItemV2,
            PrescriptionV2Footer,
            PrescriptionV2Header,
        },
        DataHandler: PrescriptionHandler,
        businessKey: PrintBusinessKeyEnum.PRESCRIPTION_V2,
        pages: [
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
        ],
        mixins: [prescriptionMixin],
        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
            extra: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                PharmacyTypeEnum,
                ChargeItemStatusEnum,
                ExternalPRUsageTypeEnumRevert,
            }
        },
        computed: {
            printData() {
                let res;
                this.renderData.printData.prescriptionWesternForms && this.renderData.printData.prescriptionWesternForms.forEach( form => {
                    form.prescriptionFormItems = this.groupMedicine(form.prescriptionFormItems);
                })
                if (this.config.westernStandardKindCount) {
                    const { prescriptionWesternForms, prescriptionInfusionForms } = this.transStandardForm();
                    const data =  {
                        ...this.renderData.printData,
                        prescriptionWesternForms: prescriptionWesternForms,
                        prescriptionInfusionForms: prescriptionInfusionForms,
                    }

                    console.log('自动分页过后的printData', data);
                    res = data;
                } else {
                    res = this.renderData.printData || null;
                }

                // 处理配镜处方
                if (res && res.prescriptionGlassesForms && res.prescriptionGlassesForms.length) {
                    res.prescriptionGlassesForms.forEach((glassesForm) => {
                        const glassesFormItems = [];
                        glassesForm.glassesParams.items.forEach((item) => {
                            if (GLASSES_TYPE[glassesForm.glassesType].includes(item.key)) {
                                glassesFormItems.push(item);
                            }
                        });
                        delete glassesForm.glassesParams;
                        glassesForm.chargeFormItems = glassesFormItems;
                    });
                }

                return res;
            },
            config() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.prescription && this.renderData.config.medicalDocuments.prescription.horizontal) {
                    return Object.assign(this.renderData.config.medicalDocuments.prescription.horizontal, {
                        infusionUsageSingleLine: this.renderData.config.medicalDocuments.prescription.infusionUsageSingleLine,
                    })
                }
                return {};
            },
            glassesConfig() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.glassesPrescription) {
                    return this.renderData.config.medicalDocuments.glassesPrescription;
                }
                return {};
            },
            isPreview() {
                return !!this.extra.isPreview;
            },
            groupCount() {
                return 4;
            },

            isTakeMedicationTime() {
                return this.printData._dispensingConfig?.isTakeMedicationTime;
            },
            organ() {
                return this.printData && this.printData.organ;
            },
            organTitle() {
                if (!this.config.title) {
                    return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.prescription || '';
                }
                return this.config.title;
            },
            glassesFooterConfig() {
                return this.glassesConfig.footer || {};
            },
        },
        methods: {
            getTakeMedicationTime(form) {
                if(!this.isTakeMedicationTime) return '';
                return form.processInfo?.takeMedicationTime || '';
            },
            getUsageTypeStr(externalForm) {
                const { usageType, usageSubType } = externalForm;
                const { label: typeStr } = UsageTypeOptions.find(it => it.value === usageType) || {};
                let usageSubTypeOptions = [];
                switch (usageType) {
                    case ExternalPRUsageTypeEnum.tieFu:
                        usageSubTypeOptions = TieFuSubOptions;
                        break;
                    case ExternalPRUsageTypeEnum.zhenCi:
                        usageSubTypeOptions = ZhenCiSubOptions;
                        break;
                    case ExternalPRUsageTypeEnum.aiJiu:
                        usageSubTypeOptions = AiJiuSubOptions;
                        break;
                    case ExternalPRUsageTypeEnum.baGuan:
                        usageSubTypeOptions = BaGuanSubOptions;
                        break;
                    case ExternalPRUsageTypeEnum.tuiNa:
                        usageSubTypeOptions = TuiNaSubOptions;
                        break;
                }
                const { label: subTypeStr } = usageSubTypeOptions.find(it => it.value === usageSubType) || {};
                if (subTypeStr) {
                    return subTypeStr;
                }
                return typeStr;
            },
        },
    }
</script>

<style lang="scss">
.prescription-v2-landscape-wrapper {
    box-sizing: border-box;

    * {
        box-sizing: border-box;
    }

    .prescription-v2-next-page {
        position: relative;
        font-size: 8pt;
        font-weight: lighter;
        text-align: center;
    }

    .prescription-v2-prev-page {
        position: absolute;
        bottom: 2px;
        left: 40px;
        font-size: 8pt;
        font-weight: lighter;
    }

    .glasses-prescription-wrapper {
        padding-top: 6pt;

        .table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #a6a6a6;
        }

        th,
        td {
            height: 31px;
            font-size: 13px;
            line-height: 31px;
            color: #000000;
            text-align: center;
            border: 1px solid #a6a6a6;
        }

        .table-bottom {
            position: relative;
            box-sizing: border-box;
            width: 100%;
            height: auto;
            border-right: 1px solid #a6a6a6;
            border-bottom: 1px solid #a6a6a6;
            border-left: 1px solid #a6a6a6;

            .requirement {
                display: inline-block;
                float: left;
                padding: 8px;
                font-size: 13px;
                line-height: 15px;
                color: #000000;
            }

            .optometristName {
                display: inline-block;
                float: right;
                padding: 8px 8px 8px 0;
                font-size: 13px;
                line-height: 15px;
                color: #000000;
            }

            .clear-float {
                clear: both;
            }
        }

        .remark {
            margin-top: 15px;
            font-size: 11px;
            line-height: 13px;
            color: #000000;
        }
    }
}
</style>
