<!--exampleData
{
  "patient": {
    "name": "李思思",
    "sex": "女",
    "age": {
      "year": 23
    }
  },
  "patientOrderNo": "00000098",
  "departmentName": "消化内科",
  "admissionDate": "2024-09-09",
  "nursingLevel": "三级护理",
  "doctorName": "张珊珊",
  "nurseName": "汪鸣鸣",
  "diagnosis": "劲椎椎间盘炎、肾病综合症",
  "allergicHistory": "头孢类",
  "notes": ""
}
-->

<template>
    <div class="hospital-bedside-card">
        <div
            class="card-header"
        >
            <div>床</div>
            <div>头</div>
            <div>卡（{{ bedNo }}床）</div>
        </div>

        <table
            class="bedside-card-table"
        >
            <tbody>
                <tr>
                    <td class="label-cell">
                        <div>
                            <span>
                                科
                            </span>
                            <span>
                                室
                            </span>
                        </div>
                    </td>
                    <td class="value-cell">
                        {{ printData.departmentName }}
                    </td>
                    <td class="label-cell">
                        <div>
                            <span>
                                入
                            </span>
                            <span>
                                院
                            </span>
                            <span>
                                时
                            </span>
                            <span>
                                间
                            </span>
                        </div>
                    </td>
                    <td class="value-cell">
                        {{ parseTime(printData.inpatientTime, 'y-m-d', true) }}
                    </td>
                </tr>
                <tr>
                    <td class="label-cell">
                        <div>
                            <span>
                                姓
                            </span>
                            <span>
                                名
                            </span>
                        </div>
                    </td>
                    <td
                        class="value-cell"
                        colspan="3"
                    >
                        {{ patient.name }}&nbsp;&nbsp;&nbsp;{{ patient.sex }}&nbsp;&nbsp;&nbsp;{{ formatAge(patient.age, { monthYear: 12, dayYear: 1 }) }}
                    </td>
                </tr>
                <tr>
                    <td class="label-cell">
                        <div>
                            <span>
                                住
                            </span>
                            <span>
                                院
                            </span>
                            <span>
                                号
                            </span>
                        </div>
                    </td>
                    <td class="value-cell">
                        {{ printData.no }}
                    </td>
                    <td class="label-cell">
                        <div>
                            <span>
                                护
                            </span>
                            <span>
                                理
                            </span>
                            <span>
                                等
                            </span>
                            <span>
                                级
                            </span>
                        </div>
                    </td>
                    <td class="value-cell">
                        {{ printData.nurseLevel }}
                    </td>
                </tr>
                <tr>
                    <td class="label-cell">
                        <div>
                            <span>
                                责
                            </span>
                            <span>
                                任
                            </span>
                            <span>
                                医
                            </span>
                            <span>
                                生
                            </span>
                        </div>
                    </td>
                    <td class="value-cell">
                        {{ printData.doctorName }}
                    </td>
                    <td class="label-cell">
                        <div>
                            <span>
                                主
                            </span>
                            <span>
                                管
                            </span>
                            <span>
                                护
                            </span>
                            <span>
                                士
                            </span>
                        </div>
                    </td>
                    <td class="value-cell">
                        {{ printData.nurseName }}
                    </td>
                </tr>
                <tr
                    class="diagnosis-tr"
                    style="vertical-align: top"
                >
                    <td class="label-cell">
                        <div>
                            <span>
                                入
                            </span>
                            <span>
                                院
                            </span>
                            <span>
                                诊
                            </span>
                            <span>
                                断
                            </span>
                        </div>
                    </td>
                    <td
                        class="value-cell"
                        colspan="3"
                    >
                        {{ preDiagnosisInfo }}
                    </td>
                </tr>
                <tr>
                    <td class="label-cell">
                        <div>
                            <span>
                                过
                            </span>
                            <span>
                                敏
                            </span>
                            <span>
                                史
                            </span>
                        </div>
                    </td>
                    <td
                        class="value-cell"
                        colspan="3"
                    >
                        {{ printData.allergicHistory }}
                    </td>
                </tr>
                <tr>
                    <td class="label-cell">
                        <div>
                            <span>
                                注
                            </span>
                            <span>
                                意
                            </span>
                            <span>
                                事
                            </span>
                            <span>
                                项
                            </span>
                        </div>
                    </td>
                    <td
                        class="value-cell"
                        colspan="3"
                    >
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import {  parseTime, formatAge } from "./common/utils";

    export default {
        name: "HospitalBedsideCard",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.HOSPITAL_BEDSIDE_CARD,
        pages: [
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                onlyOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
        ],
        props: {
            renderData: Object,
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            patient() {
                return this.printData.patient || {};
            },
            bedNo() {
                if(Number(this.printData.bedNo) < 10) {
                    return `0${this.printData.bedNo}`
                }
                return this.printData.bedNo;
            },
            preDiagnosisInfo() {
                // 遍历所有的诊断信息
                if(this.printData.preDiagnosisInfos && this.printData.preDiagnosisInfos.length) {
                    let diagnosisArr = [];
                    this.printData.preDiagnosisInfos.forEach((item) => {
                        item.value.forEach((it) => {
                            if(it.name) {
                                diagnosisArr.push(it.name)
                            }
                        });
                    });
                    return diagnosisArr.join('，');
                }
                return '';
            },
        },
        methods: {
            parseTime,
            formatAge,
            formatPatientOrderNo(orderNo) {
                if (!orderNo) return '';
                return String(orderNo).padStart(8, '0');
            },
        },
    }
</script>

<style lang="scss">
.hospital-bedside-card {
  font-family: "Microsoft YaHei", "微软雅黑";
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .card-header {
    font-size: 32px;
    display: flex;
    gap: 36px;
    justify-content: center;
    margin-bottom: 10px;
    font-weight: bold;
    min-height: 43px;
  }

  .bedside-card-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    flex: 1;

    td {
      border: 1px solid #000;
      font-size: 24px;
    }

    .label-cell {
      width: 17%;
      min-width: 17%;
      max-width: 17%;
      padding: 10px 16px;
      > div {
        width: 100%;
        display: flex;
        justify-content: space-between;
      }
    }


    .value-cell {
      width: 33%;
      padding: 10px 16px;
      text-align: left;
      word-wrap: break-word;
      word-break: break-all;
    }
    .diagnosis-tr {
      td {
        min-height: 96px;
        height: 96px;
      }
    }
  }
}
</style>
