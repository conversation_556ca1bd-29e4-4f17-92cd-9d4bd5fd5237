<template>
    <div class="pe-individual-report-wrapper">
        <!-- 封面在pc端的预览 -->
        <template v-if="isPreviewCover">
            <individual-report-cover
                :business-time="individualReport.businessTime"
                :order-no="individualReport.no"
                :patient="patient"
                :report-released="individualReport.reportReleased"
                :config="coverConfig"
                :address="individualReport.address"
                :print-company="individualReport.printCompany"
            ></individual-report-cover>

            <div
                v-if="coverConfig.qrCode && qrCodeUrl"
                class="individual-report-cover-footer-qr"
                data-pendants-index="1"
                data-type="footer"
                style="position: absolute"
            >
                <div class="qr-code-wrapper">
                    <img :src="qrCodeUrl" />
                </div>

                <div class="extra-info">
                    <div>
                        关注官方公众号
                    </div>
                    <div>
                        体检预约、查报告
                    </div>
                    <div>
                        健康咨询电话：
                        <span class="contact-phone-number">
                            {{ coverConfig.institutionContact }}
                        </span>
                    </div>
                    <div>
                        地址：{{ coverConfig.institutionAddress }}
                    </div>
                </div>
            </div>

            <div 
                v-else
                class="individual-report-cover-footer"
                data-pendants-index="1"
                data-type="footer"
            >
                <div>
                    地址：{{ coverConfig.institutionAddress }}
                </div>

                <div>
                    健康咨询电话：
                    <span class="contact-phone-number">
                        {{ coverConfig.institutionContact }}
                    </span>
                </div>
            </div>
        </template>

        <!-- 介绍页在pc端的预览 -->
        <template v-else-if="isPreviewIntroDuction">
            <div 
                data-type="header"
            >
                <div class="pe-individual-report-header">
                    <div class="header-left">
                        <span class="organ-name">
                            {{ coverConfig.institutionName }}
                        </span>
                        <span class="header-title">
                            健康体检报告
                        </span>
                    </div>

                    <div class="header-right">
                        <span>
                            {{ patient.name }}  {{ patient.sex }}  {{ formatAge(patient.age) }}
                        </span>

                        <span>
                            <span class="header-label">体检单号</span>
                            {{ individualReport.no }}
                        </span>

                        <span>
                            <span class="header-label">申领时间</span>
                            {{
                                individualReport.reportReleased ?
                                    parseTime(individualReport.reportReleased, 'y-m-d', true) :
                                    ''
                            }}
                        </span>
                    </div>
                </div>

                <div class="space-16"></div>
            </div>

            <pe-introduction
                :patient="patient"
                :config="introductionConfig"
                :results-detail="individualReport.resultsDetail"
            ></pe-introduction>
        </template>

        <!-- 实际打印预览 -->
        <template v-else>
            <div
                data-type="header"
                data-pendants-index="1"
            ></div>
            <!-- 封面 -->
            <individual-report-cover
                :business-time="individualReport.businessTime"
                :order-no="individualReport.no"
                :patient="patient"
                :report-released="individualReport.reportReleased"
                :config="coverConfig"
                :address="individualReport.address"
                :print-company="individualReport.printCompany"
            ></individual-report-cover>

            <!-- 封面页尾 -->
            <div
                v-if="coverConfig.qrCode && qrCodeUrl"
                class="individual-report-cover-footer-qr"
                data-pendants-index="1"
                data-type="footer"
            >
                <div class="qr-code-wrapper">
                    <img :src="qrCodeUrl" />
                </div>

                <div class="extra-info">
                    <div>
                        关注官方公众号
                    </div>
                    <div>
                        体检预约、查报告
                    </div>
                    <div>
                        健康咨询电话：
                        <span class="contact-phone-number">
                            {{ coverConfig.institutionContact }}
                        </span>
                    </div>
                    <div>
                        地址：{{ coverConfig.institutionAddress }}
                    </div>
                </div>
            </div>

            <div 
                v-else
                class="individual-report-cover-footer"
                data-pendants-index="1"
                data-type="footer"
            >
                <div>
                    地址：{{ coverConfig.institutionAddress }}
                </div>

                <div>
                    健康咨询电话：
                    <span class="contact-phone-number">
                        {{ coverConfig.institutionContact }}
                    </span>
                </div>
            </div>

            <!-- 报告页眉 -->
            <div 
                data-type="header"
                data-pendants-index="3"
            >
                <div class="pe-individual-report-header">
                    <div class="header-left">
                        <span class="organ-name">
                            {{ coverConfig.institutionName }}
                        </span>
                        <span class="header-title">
                            健康体检报告
                        </span>
                    </div>

                    <div class="header-right">
                        <span>
                            {{ patient.name }}  {{ patient.sex }}  {{ formatAge(patient.age) }}
                        </span>

                        <span>
                            <span class="header-label">体检单号</span>
                            {{ individualReport.no }}
                        </span>
                    </div>
                </div>
                <div class="space-16"></div>
            </div>

            <!-- 介绍页 -->
            <pe-introduction
                :config="introductionConfig"
                :patient="patient"
                :results-detail="individualReport.resultsDetail"
            ></pe-introduction>

            <div data-type="new-page"></div>

            <!-- 阳性结果页 -->
            <positive-data-advice 
                :results-detail="individualReport.resultsDetail"
                :report-released-by="individualReport.reportReleasedBy"
                :report-approved="individualReport.reportApproved"
                :report-approved-by="individualReport.reportApprovedBy"
            ></positive-data-advice>

            <div
                class="report-doctor-info"
                data-type="fixed-bottom-box"
            >
                <div class="doctor-info">
                    <span>报告医师：</span>
                    <span>
                        {{ individualReport.reportReleasedBy && individualReport.reportReleasedBy.name }}
                    </span>
                </div>
                <div class="doctor-info">
                    <span>审核医师：</span>
                    <span>{{ individualReport.reportApprovedBy && individualReport.reportApprovedBy.name }}</span>
                </div>
                <div class="doctor-info">
                    <span>总检时间：</span>
                    <span>
                        {{ parseTime(individualReport.reportApproved, 'y-m-d', true) }}
                    </span>
                </div>
            </div>

            <div data-type="new-page"></div>

            <!-- 异常解读与建议-->
            <template v-if="individualReport.advices && individualReport.advices.length">
                <div
                    class="abnormal-title"
                >
                    异常解读与建议
                </div>

                <template
                    v-for="(item, index) in renderAdvices"
                >
                    <div
                        v-if="index !== 0"
                        :key="index"
                        class="space-16"
                    ></div>

                    <div
                        :key="index"
                        class="interpretation-item-title"
                    >
                        {{ index + 1 }}. {{ item.title }}
                    </div>
                    
                    <div
                        :key="index"
                        class="space-6"
                    ></div>

                    <div
                        v-if="item.commonReason"
                        :key="index"
                        class="interpretation-item-content"
                    >
                        <p class="title">
                            常见原因：
                        </p>
                        <p>{{ item.commonReason }}</p>
                    </div>

                    <div
                        :key="index"
                        class="space-6"
                    ></div>

                    <div
                        v-if="item.medicalExplain"
                        :key="index"
                        class="interpretation-item-content"
                    >
                        <p class="title">
                            医学解释：
                        </p>
                        <p>{{ item.medicalExplain }}</p>
                    </div>

                    <div
                        :key="index"
                        class="space-6"
                    ></div>

                    <div
                        v-if="item.doctorAdvice"
                        :key="index"
                        class="interpretation-item-content advice"
                    >
                        <p class="title">
                            医生建议：
                        </p>
                        <p>{{ item.doctorAdvice }}</p>
                    </div>
                </template>
            </template>

            <abc-print-space :value="16"></abc-print-space>
            <div>
                <div
                    v-if="printConfig.content.mainComments || printData.mainComments"
                    class="title-2"
                >
                    总检总评
                </div>
                <div class="general-content">
                    <abc-html :value="printData.mainComments"></abc-html>
                </div>
                <abc-print-space :value="8"></abc-print-space>
                <div
                    class="general-footer"
                >
                    <div>
                        总检医师：
                        <hand-sign
                            :value="printData.signContent"
                            :sign-block="true"
                        ></hand-sign>
                    </div>
                    <abc-print-space :value="24"></abc-print-space>
                </div>
            </div>

          
            <div 
                data-type="new-page"
            ></div>

            <!-- 检查检验报告 -->
            <template v-if="isExistExamItem(individualReport)">
                <div 
                    class="title-super"
                >
                    体检科室详细检查结果
                </div>

                <template v-for="(report, j) in getReportList(individualReport)">
                    <!-- 临床检查 -->
                    <template 
                        v-if="
                            isInspect(report.type) && (
                                isNormalInspect(report.deviceType) || 
                                isClinicalInspect(report.deviceType)
                            )
                        "
                    >
                        <div
                            :key="j"
                            class="title-2"
                        >
                            {{ report.name }}
                            <div class="space-12"></div>
                        </div>

                        <result-table
                            :key="j"
                            :columns="report.columns"
                            :list="report.itemsValue"
                            border
                            style="font-size: 10pt;"
                        ></result-table>

                        <div
                            :key="j"
                            class="diagnosis-advice-wrapper"
                            :class="{ 'is-abnormal': !!report.diagnosisAdvice }"
                        >
                            <div class="diagnosis-advice-label">
                                诊断意见
                            </div>

                            <div class="diagnosis-advice-value">
                                {{ report.diagnosisAdvice || '未见明显异常' }}
                            </div>
                        </div>
                    </template>

                    <!-- 检验报告 -->
                    <template v-if="isExamination(report.type)">
                        <!-- header -->
                        <report-common-header
                            :key="j"
                            :header-item-list="report.header"
                            :print-data="report"
                            :cover-config="coverConfig"
                            :is-single-page="isSinglePageReport(report)"
                        ></report-common-header>

                        <!-- 表格 -->
                        <result-table
                            :key="j"
                            :columns="report.columns"
                            :list="report.itemsValue"
                            border
                            style="font-size: 10pt;"
                            class="section-item"
                        ></result-table>

                        <result-section
                            v-for="(r, k) in report.resultList"
                            :key="k"
                            :result-item="r"
                        ></result-section>

                        <image-section
                            :key="j"
                            :image-files="report.imageFiles"
                        ></image-section>

                        <common-footer
                            :key="j"
                            :print-data="report"
                            :print-config="report.printConfig.footer"
                            is-examination
                        ></common-footer>
                    </template>
                    
                    <!-- 放射科/彩超/检查/胃镜报告 -->
                    <template 
                        v-if="
                            isInspect(report.type) && (
                                isRadiologyInspect(report.deviceType) || 
                                isCDUInspect(report.deviceType) ||
                                isGastroscopyInspect(report.deviceType) ||
                                isOtherInspect(report.deviceType)
                            )
                        "
                    >
                        <!-- 头部 -->
                        <report-common-header
                            :key="j"
                            :header-item-list="report.header"
                            :print-data="report"
                            :cover-config="coverConfig"
                            :is-single-page="isSinglePageReport(report)"
                        ></report-common-header>

                        <!-- 内容 -->
                        <image-section
                            :key="j"
                            :image-files="report.imageFiles"
                            :small="isGastroscopyInspect(report.deviceType)"
                        ></image-section>

                        <result-section
                            v-for="(r, k) in report.resultList"
                            :key="k"
                            :result-item="r"
                        >
                        </result-section>

                        <!-- 把结尾放到最后一个section中，防止只有结尾被分到第二页的情况 -->
                        <common-footer
                            :key="j"
                            :print-data="report"
                            :data-type="isGastroscopyInspect(report.deviceType) ? 'block' : 'fixed-bottom-box'"
                            :style="{
                                paddingBottom: isGastroscopyInspect(report.deviceType) ? '0' : '8pt',
                                paddingTop: isGastroscopyInspect(report.deviceType) ? '24pt' : '0',
                            }"
                            :print-config="report.printConfig.footer"
                            :tester-prefix="isGastroscopyInspect(report.deviceType) ? '检查' : '报告'"
                        ></common-footer>
                    </template>

                    <!-- 心电图 -->
                    <template
                        v-if="isInspect(report.type) && (isECGInspect(report.deviceType))"
                    >
                        <!-- 头部 -->
                        <report-common-header
                            :key="j"
                            :header-item-list="report.header"
                            :print-data="report"
                            :cover-config="coverConfig"
                            :is-single-page="isSinglePageReport(report)"
                        ></report-common-header>

                        <!-- 心电图 & 动脉硬化图片 -->
                        <div
                            v-if="report.singleImg"
                            :key="j" 
                        >
                            <div
                                class="report-img is-full"
                            >
                                <img
                                    :src="report.singleImg"
                                    alt=""
                                />
                            </div>
                        </div>

                        <result-section
                            v-for="(r, k) in report.resultList"
                            :key="k"
                            :result-item="r"
                        >
                        </result-section>

                        <common-footer
                            :key="j"
                            data-type="fixed-bottom-box"
                            :print-data="report"
                            :style="{
                                paddingBottom: '8pt',
                                paddingTop: '0',
                            }"
                        ></common-footer>
                    </template>

                    <!--动脉硬化报告 & 骨密度 & 人体成分-->
                    <template
                        v-if="
                            isInspect(report.type) && (
                                isASOInspect(report.deviceType) ||
                                isMDBInspect(report.deviceType) ||
                                isBodyCompositionInspect(report.deviceType) || isC1314Inspect(report.deviceType)
                            )
                        "
                    >
                        <!-- 心电图 & 动脉硬化图片 -->
                        <div
                            v-if="report.singleImg"
                            :key="j"
                        >
                            <div
                                class="report-img super-full"
                            >
                                <img
                                    :src="report.singleImg"
                                    alt=""
                                />
                            </div>
                        </div>

                        <result-section
                            v-for="(r, k) in report.resultList"
                            :key="k"
                            :result-item="r"
                        >
                        </result-section>

                        <common-footer
                            :key="j"
                            data-type="fixed-bottom-box"
                            :print-data="report"
                            :style="{
                                paddingBottom: '8pt',
                                paddingTop: '0',
                            }"
                        ></common-footer>
                    </template>

                    <div
                        v-if="report.needSpace"
                        :key="j"
                        class="space-32"
                    ></div>

                    <div
                        v-if="report.needAddNewPage"
                        :key="j"
                        data-type="new-page"
                    ></div>
                </template>
            </template>
         
            <!-- 页尾 -->
            <div 
                data-type="footer"
                data-pendants-index="3"
                class="pe-individual-report-footer"
            >
                <div class="organ-info">
                    <span>
                        地址：{{ coverConfig.institutionAddress }}
                    </span>

                    <span>
                        健康咨询电话：
                        <span style="color: #000000">
                            {{ coverConfig.institutionContact }}
                        </span>
                    </span>
                </div>
                <div class="pagination">
                    <span
                        data-page-no="PageNo"
                        style="color: #000000"
                    ></span>
                    /
                    <span data-page-count="PageCount"></span>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import {
        IndividualReportCover,
        PeIntroduction,
        PositiveDataAdvice,
        IndividualReportInspectMixin,
        ReportCommonHeader,
        ImageSection,
        ResultSection,
        CommonFooter,
    } from './components/pe-individual-report/index.js';

    import ResultTable from './components/pe-individual-report/inspect-report/result-table.vue';

    import CommonHandler from "./data-handler/common-handler.js";
    import { formatAge } from "./common/utils.js";
    import { parseTime } from "@tool/date";
    import HandSign from "./components/hand-sign/index.vue";
    import AbcPrintSpace from "./components/layout/space.vue";
    import AbcHtml from "./components/layout/abc-html.vue";

    export default {
        name: "PEIndividualReport",
        businessKey: PrintBusinessKeyEnum.PE_INDIVIDUAL_REPORT,
        DataHandler: CommonHandler,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        getInjectGlobalStyle(data, config) {
            const isPublicHealthReport = data.businessType === 10;
            const _config = (isPublicHealthReport ? config.publicHealthReport : config.peReport) || {
                cover: {
                    personInfo: {},
                },
                introduction: {},
            };
            const background = _config.cover.background;
            return `
                .abc-page:has(.individual-report-cover) {
                    background-image: url('${background}');
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: cover;
                }

                .abc-page-content {
                    overflow: hidden;
                }
            `
        },
        components: {
            AbcHtml,
            AbcPrintSpace,
            HandSign,
            IndividualReportCover,
            PeIntroduction,
            PositiveDataAdvice,
            ResultTable,
            ReportCommonHeader,
            ImageSection,
            ResultSection,
            CommonFooter,
        },

        mixins: [
            IndividualReportInspectMixin,
        ],

        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },

            extra: {
                type: Object,
                default() {
                    return {}
                },
            },
        },

        computed: {
            printData() {
                return this.renderData.printData;
            },

            individualReport() {
                return this.printData || {};
            },

            organ() {
                return this.individualReport.organPrintView || {};
            },

            qrCodeUrl() {
                return this.printData.qrCodeUrl;
            },

            // 是否预览封面
            isPreviewCover() {
                return this.extra.isPreviewCover;
            },
            // 是否预览介绍页
            isPreviewIntroDuction() {
                return this.extra.isPreviewIntroDuction;
            },
            // 是否为公卫报告
            isPublicHealthReport() {
                return this.printData.businessType === 10;
            },

            printConfig() {
                const defaultConfig = {
                    cover: {
                        personInfo: {},
                    },
                    introduction: {},
                };
                const config = this.isPublicHealthReport ? this.renderData.config?.publicHealthReport : this.renderData.config?.peReport;
                return config || defaultConfig;
            },

            coverConfig() {
                const config = this.printConfig.cover || {};
                return {
                    ...config,
                    institutionName: config.institutionName || this.organ.name,
                };
            },

            introductionConfig() {
                return this.printConfig.introduction;
            },

            patient() {
                return this.individualReport.patient;
            },

            renderAdvices() {
                const advices = this.individualReport.advices;
                if(advices && advices.length) {
                    return advices.filter(item => {
                        return item.commonReason || item.doctorAdvice || item.medicalExplain
                    });
                }
                return [];
            },
        },
        methods: {
            parseTime,
            formatAge,
            isExistExamItem(individualReport) {
                return individualReport.resultsDetail?.length !== 0;
            },
        },
    }
</script>

<style lang="scss">

@import "./style/reset.scss";
// .abc-page_preview {
//   width: 70mm !important;
//   height: 43mm !important;
//   padding: 0 !important; 
// }
.pe-individual-report-wrapper {
    font-family: "Microsoft YaHei", "微软雅黑";
    font-size: 14pt;

    @for $i from 1 through 48 {
        .space-#{$i} {
            height: $i * 1pt;
            width: 100%;
        }
    }

    @for $i from 1 through 30 {
        .font-size-#{$i} {
            font-size: $i * 1pt;
        }
    }
    
    .title-super {
        color: #000000;
        text-align: center;
        font-size: 17pt;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        letter-spacing: 0.51pt;
        margin-top: 16pt;
        margin-bottom: 16pt;
    }

    .title-1 {
        font-size: 12pt;
        font-weight: 500;
    }

    .title-2 {
        font-size: 12pt;
        font-weight: 600;
    }

    .text-level-1 {
        font-size: 12pt;
        font-weight: 400;
    }

    .text-level-2 {
        font-size: 10pt;
        font-weight: 300;
    }

    .individual-report-cover-footer {
        box-sizing: border-box;
        width: 100%;
        display: flex;
        justify-content: center;
        gap: 8pt;
        border-top: 0.6px solid #D7D7D7;
        padding: 4pt 0 0;
        font-size: 9pt;
        font-weight: 400;
        line-height: 12pt;
        color: #8B8E98;

        .contact-phone-number {
            color: #000000;
            font-weight: 600;
        }
    }

    .individual-report-cover-footer-qr {
        display: flex;
        justify-content: center;
        gap: 16pt;
        align-items: center;
        width: 279pt;
        left: 50%;
        position: relative;
        transform: translateX(-50%);

        .qr-code-wrapper {
            width: 84pt;
            height: 84pt;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }

        .extra-info {
            color: #8B8E98;
            font-size: 9pt;
            flex: 1;

            >div:nth-child(1) {
                margin-bottom: 3pt;
            }

            >div:nth-child(2) {
                font-size: 12pt;
                color: #000000;
                padding-bottom: 3pt;
                border-bottom: 0.6pt solid #d7d7d7;
                margin-bottom: 6pt;
            }

            >div:nth-child(3) {
                .contact-phone-number {
                    color: #000000;
                    font-weight: 600;
                }
            }
        }
    }
    
    .pe-individual-report-header {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        padding-bottom: 4pt;
        border-bottom: 0.5pt solid #A6A6A6;
        font-size: 10pt;
        font-weight: 400;

        .header-left {
            display: inline-flex;
            align-items: flex-end;

            .organ-name {
                font-size: 9pt;
                font-weight: 600;
                margin-right: 4pt;
                color: #005ED9;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                max-width: 180pt;
            }

            .header-title {
                font-size: 8pt;
                font-weight: 400;
                color: #000000;
            }
        }

        .header-right {
            flex: 1;
            justify-content: flex-end;
            font-weight: 400;
            display: flex;
            gap: 12pt;
            font-size: 8pt;

            .header-label {
                color: #8B8E98;
                margin-right: 4pt;
            }
        }
    }

    .report-doctor-info {
        padding-bottom: 32pt;
        color: #8B8E98;
        font-size: 10pt;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .doctor-info {
            display: inline-flex;

            >span:last-child {
                height: 18pt;
                color: #000000;
                width: 80pt;
                text-align: center;
                border-bottom: 0.83pt solid #D7D7D7;
            }
        }

    }

    .abnormal-title {
        color: #000000;
        text-align: center;
        font-size: 17pt;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        letter-spacing: 0.51pt;
        padding-bottom: 16pt;
    }

    .interpretation-item-title{
        display: flex;
        padding: 4pt 8pt;
        color: #FFFFFF;
        font-size: 12pt;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        background: #8A8E99;
    }

    .interpretation-item-content {
        background: #F0F0F0;
        padding: 6pt 20pt;

        &.advice {
            background: #D3E8FF;
        }

        > p {
            &:first-child {
                color: #000000;
                font-size: 10pt;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
            }

            & + p {
                margin-top: 4pt;
            }

            &:last-child {
                color:  #37393F;
                font-size: 10pt;
                font-style: normal;
                font-weight: 300;
                line-height: 16pt; /* 160% */
            }
        }
    }

    .diagnosis-advice-wrapper {
        display: flex;
        margin-top: 12pt;
        box-sizing: border-box;

        .diagnosis-advice-label {
            width: 60pt;
            background: #49B27D;
            color: #FFFFFF;
            font-size: 10pt;
            font-style: normal;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            justify-content: center;

            &.is-abnormal {
                background: #FF4234;
            }
        }

        .diagnosis-advice-value {
            flex: 1;
            background: #E2F9EE;
            color: #2B2D32;
            font-size: 10pt;
            font-style: normal;
            font-weight: 350;
            padding: 4pt 8pt;
            line-height: 12pt;
        }
    }

    .inspect-report-item {
        &:not(:first-child){
            margin-top: 24pt;
        }
    }

    .inspect-report-item-content + .inspect-report-item-content {
        margin-top: 16pt;
    }

    .inspect-report-item-content {
        .report-image-list {
            display: flex;
            justify-content: space-between;

            .report-image-item {
                width: 23%;

                img {
                    width: 100%;
                }
            }
        }
    }

    .report-img {
      display: inline-block;
      font-size: 0;
      overflow: hidden;

      &.is-full {
        width: 100%;
        height: 165mm;
      }

        &.super-full {
            width: 100%;
            height: 210mm;
        }

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .general-content{
      padding: 8px 0;
      font-size: 10pt;
      width: 100%;
      height: 376px;
    }
    .general-footer{
      color: #8B8E98;
      font-size: 10pt;
      text-align: right;
    }

    .pe-individual-report-footer {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        border-top: 0.6px solid #D7D7D7;
        padding-top: 4pt;
        line-height: 11pt;
        font-weight: 400;

        .organ-info {
            color: #8B8E98;
            font-size: 8pt;

            span:first-child{
                margin-right: 8pt;
            }
        }

        .pagination {
            width: 56pt;
            font-size: 8pt;
            color: #8B8E98;
            text-align: right;
        }
    }
}
</style>