<template>
    <shandong-medical-bill
        class="shandong-medical-bill-wrapper"
        :detail-top="36.5"
        :detail-height="69"
        :split-count="28"
        v-bind="$options.propsData"
    >
        <block-box
            :top="18"
            :left="23"
        >
            医疗类别：
        </block-box>
        <block-box
            :top="18"
            :left="40"
        >
            {{ shebaoPayment.medType }}
        </block-box>

        <!-- 业务流水号 -->
        <block-box
            :top="23"
            :left="40"
        >
            {{ printData.serialNo }}
        </block-box>

        <block-box
            :top="18"
            :left="70"
        >
            个人编号：
        </block-box>
        <block-box
            :top="18"
            :left="89"
        >
            {{ printData.healthCardId }}
        </block-box>

        <!-- 医疗机构类型 -->
        <block-box
            :top="23"
            :left="89"
        >
            {{ organ.category }}
        </block-box>

        <!-- 姓名 -->
        <block-box
            :top="29"
            :left="31"
        >
            {{ patient.name }}
        </block-box>

        <!-- 性别 -->
        <block-box
            :top="29"
            :left="66"
        >
            {{ patient.sex }}
        </block-box>

        <!-- 医保类型 -->
        <block-box
            :top="29"
            :left="83"
        >
            {{ shebaoPayment.cardOwnerType }}
        </block-box>

        <!-- 社保号 -->
        <block-box
            :top="29"
            :left="147"
        >
            {{ printData.healthCardNo }}
        </block-box>

        <!-- 合计(大写) -->
        <block-box
            :top="107"
            :left="39"
        >
            {{ digitUppercase(finalFee) }}
        </block-box>

        <!-- 合计 -->
        <block-box
            :top="107"
            :left="132"
        >
            {{ finalFee | formatMoney }}
        </block-box>

        <!-- 医保统筹支付 -->
        <block-box
            :top="113"
            :left="42"
        >
            {{ shebaoPayment.fundPaymentFee | formatMoney }}
        </block-box>

        <!-- 个人账户支付 -->
        <block-box
            :top="113"
            :left="72"
        >
            {{ shebaoPayment.accountPaymentFee | formatMoney }}
        </block-box>

        <!-- 其他医保支付 -->
        <block-box
            :top="113"
            :left="100"
        >
            {{ shebaoPayment.otherPaymentFee | formatMoney }}
        </block-box>

        <!-- 个人支付金额 -->
        <block-box
            :top="113"
            :left="137"
        >
            {{ printData.personalPaymentFee | formatMoney }}
        </block-box>

        <!-- 收款单位 -->
        <block-box
            :top="118"
            :left="45"
            class="institution-name"
        >
            {{ currentConfig.institutionName }}
        </block-box>

        <!-- 收款人 -->
        <block-box
            :top="118"
            :left="104"
        >
            {{ printData.chargedByName }}
        </block-box>

        <!-- 收款年份 -->
        <block-box
            :top="118"
            :left="132"
        >
            {{ year }}
        </block-box>

        <!-- 收款月份 -->
        <block-box
            :top="118"
            :left="147"
        >
            {{ month }}
        </block-box>

        <!-- 收款日期 -->
        <block-box
            :top="118"
            :left="156"
        >
            {{ day }}
        </block-box>
    </shandong-medical-bill>
</template>

<script>
    import CommonHandler from './data-handler/common-handler.js'
    import {PrintBusinessKeyEnum} from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import ShandongMedicalBill from './components/medical-bill/national-medical-bill/shandong.vue';
    import BlockBox from './components/medical-bill/national-medical-bill/block-box.vue';
    import BillDataMixins from "./mixins/bill-data.js";
    import NationalBillData from "./mixins/national-bill-data.js";

    export default {
        name: "MedicalBillShandong",
        DataHandler: CommonHandler,
        components: {
            ShandongMedicalBill,
            BlockBox
        },
        mixins: [BillDataMixins, NationalBillData],
        businessKey: PrintBusinessKeyEnum.MEDICAL_BILL_SHANDONG,
        pages: [
            {
                paper: PageSizeMap.MM210_127_SHANDONG,
                isRecommend: true,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
        ],
        computed: {
            // 是否医保支付
            isShebaoPayment() {
                return Object.keys(this.shebaoPayment).length
            },
        },
    }
</script>
<style lang="scss">
* {
  padding: 0;
  margin: 0;
}

.abc-page_preview {
  background: url("/static/assets/print/shandong.jpg");
  background-size: 100%;
  color: #2a82e4;
}

.shandong-medical-bill-wrapper {
    .institution-name {
        width: 36mm;
        word-wrap: break-word;
        word-break: break-all;
    }
}
</style>
