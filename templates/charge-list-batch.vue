<template>
    <div>
        <template v-for="item in feeDetailPrintInfoList">
            <div
                :key="`${item.patientOrderInfo.id}-header`"
                class="header"
            >
                <div
                    v-if="showTraceCodeQrCode && printData.traceCodeQrCodeUrl"
                    style="position: absolute; top: 0; right: 0; text-align: center;"
                >
                    <img
                        :src="printData.traceCodeQrCodeUrl"
                        alt=""
                        style="width: 38pt;"
                    />
                    <div style="font-size: 9pt;">
                        扫码查看追溯码
                    </div>
                </div>
                <div class="header-patient-title">
                    <div class="title">
                        {{ pageTitle }}
                    </div>
                    <div class="sub-title">
                        住院费用清单
                    </div>
                </div>
                <div class="header-patient-form">
                    <div
                        class="header-patient-item width-40"
                        style="word-spacing: initial;"
                        overflow
                    >
                        <span>姓名：{{ item.patientOrderInfo.patient.name }}</span>
                        <span class="margin-left-6">{{ item.patientOrderInfo.patient.sex }}</span>
                        <span class="margin-left-6">{{ formatAge(item.patientOrderInfo.patient.age, { monthYear: 12, dayYear: 1 }) }}</span>
                    </div>
                    <div
                        class="header-patient-item width-35"
                        overflow
                    >
                        机构代码：{{ nationalCode }}
                    </div>
                    <div
                        class="header-patient-item width-25"
                        overflow
                    >
                        住院号：{{ item.patientOrderInfo.no }}
                    </div>
                </div>
                <div class="header-patient-form">
                    <div
                        class="header-patient-item width-40"
                        overflow
                    >
                        科别：{{ item.patientOrderInfo.departmentName }}
                    </div>
                    <div
                        class="header-patient-item width-35"
                        overflow
                    >
                        <span>床号：{{ item.patientOrderInfo.wardName }}</span>
                        <span
                            v-if="item.patientOrderInfo.bedRoomName"
                            class="margin-left-6"
                        >{{ item.patientOrderInfo.bedRoomName }}</span>
                        <span class="margin-left-6">{{ item.patientOrderInfo.bedNo }}床</span>
                    </div>
                    <div
                        class="header-patient-item width-25"
                        overflow
                    >
                        入院日期：{{ parseTime(item.patientOrderInfo.inpatientTime, 'y-m-d') }}
                    </div>
                </div>
                <div class="header-patient-form">
                    <div
                        class="header-patient-item width-40"
                        overflow
                        style="word-spacing: 0;"
                    >
                        预缴金：{{ item.depositReceivedFee | formatMoney }}
                        (当前余额 {{ item.currentBalance | formatMoney }})
                    </div>
                    <div
                        class="header-patient-item width-35"
                        overflow
                    >
                        费用合计：{{ item.totalBetweenFee }}（累计合计：{{ item.totalFee | formatMoney }}）
                    </div>
                    <div
                        class="header-patient-item width-25"
                        overflow
                    >
                        费用日期：{{ item.beginDate | parseTime('y-m-d') }}~{{ item.endDate | parseTime('m-d') }}
                    </div>
                </div>
            </div>

            <!-- 项目 -->
            <div
                :key="item.patientOrderInfo.id"
                class="charge-list"
                data-type="mix-box"
            >
                <!-- 医嘱项目 -->
                <template v-if="config.feeDetail">
                    <div
                        class="charge-list-advice-list"
                        style=" padding: 8px 0 0; border-top: 1px solid #a6a6a6;"
                    >
                        <div
                            class="charge-list-advice-list-item"
                        >
                            <!-- 名称 -->
                            <div class="charge-list-advice-list-item-name">
                                医嘱
                            </div>
                            <!-- 医保等级 -->
                            <div
                                v-if="config.medicalGrade"
                                class="charge-list-advice-list-item-medical-grade"
                            >
                                医保等级
                            </div>
                            <!-- 医保码 -->
                            <div
                                v-if="config.code || showChargeItemTraceCode"
                                class="charge-list-advice-list-item-social-code"
                            >
                                <template v-if="config.code">
                                    国家码
                                </template>
                                <template v-if="config.code && showChargeItemTraceCode">
                                    /
                                </template>
                                <template v-if="showChargeItemTraceCode">
                                    追溯码
                                </template>
                            </div>
                            <!-- 规格 -->
                            <div
                                v-if="config.spec"
                                class="charge-list-advice-list-item-social-spec"
                            >
                                规格
                            </div>
                            <!-- 单位 -->
                            <div class="charge-list-advice-list-item-social-unit">
                                单位
                            </div>
                            <!-- 单价 -->
                            <div
                                v-if="config.unitPrice"
                                class="charge-list-advice-list-item-social-total-price"
                            >
                                单价
                            </div>
                            <!-- 数量 -->
                            <div class="charge-list-advice-list-item-social-total-price">
                                数量
                            </div>
                            <!-- 金额 -->
                            <div class="charge-list-advice-list-item-social-total-price">
                                金额
                            </div>
                        </div>
                    </div>

                    <div
                        class="charge-list-advice-list"
                        style="padding: 0 0 4px;"
                        data-type="group"
                    >
                        <template
                            v-for="(adviceTypePrint, adviceTypePrintIdx) in item.itemGroupByAdviceTypePrints"
                        >
                            <!-- 药品、物资、商品、直接开出的费用项 -->
                            <template v-if="[TreatmentTypeEnum.UN_KNOW, TreatmentTypeEnum.MEDICINE, TreatmentTypeEnum.MATERIALS].includes(adviceTypePrint.diagnosisTreatmentType) || isNull(adviceTypePrint.diagnosisTreatmentType)">
                                <template v-for="(advice, adviceIdx) in (adviceTypePrint.hisChargeFormItemGroupByAdvicePrintInfos || [])">
                                    <template v-for="(chargeForm, chargeFormIdx) in (advice.hisChargeFormItemPrintInfoList || [])">
                                        <div
                                            v-if="!config.isPrintNonZeroItem || chargeForm.totalPrice"
                                            :key="`charge-list-advice-list-item-${adviceTypePrintIdx}-${adviceIdx}-${chargeFormIdx}`"
                                            class="charge-list-advice-list-item"
                                            data-type="item"
                                        >
                                            <!-- 名称 -->
                                            <div class="charge-list-advice-list-item-name">
                                                {{ chargeForm.name || '' }}
                                            </div>
                                            <!-- 医保等级 -->
                                            <div
                                                v-if="config.medicalGrade"
                                                class="charge-list-advice-list-item-medical-grade"
                                            >
                                                <template v-if="chargeForm.shebaoGoodsItem && isNotNull(chargeForm.shebaoGoodsItem.medicalFeeGrade)">
                                                    {{ chargeForm.shebaoGoodsItem.medicalFeeGrade | medicalFeeGrade2PrintStr }}{{ filterOwnExpenseRatio(chargeForm.shebaoGoodsItem.ownExpenseRatio) }}
                                                </template>
                                            </div>
                                            <!-- 医保码 -->
                                            <div
                                                v-if="config.code"
                                                class="charge-list-advice-list-item-social-code"
                                            >
                                                <template
                                                    v-if="chargeForm.goodsSnapshotVersion && chargeForm.goodsSnapshotVersion.shebao && chargeForm.goodsSnapshotVersion.shebao.nationalCode !== 'DISABLED'"
                                                >
                                                    {{ chargeForm.goodsSnapshotVersion.shebao.nationalCode || '' }}
                                                </template>
                                            </div>
                                            <!-- 规格 -->
                                            <div
                                                v-if="config.spec"
                                                class="charge-list-advice-list-item-social-spec"
                                            >
                                                <template v-if="isShowDisplaySpec(chargeForm) && chargeForm.productInfoSnapshot">
                                                    {{ chargeForm.productInfoSnapshot.displaySpec || '' }}
                                                </template>
                                            </div>
                                            <!-- 单位 -->
                                            <div class="charge-list-advice-list-item-social-unit">
                                                {{ chargeForm.unit || '' }}
                                            </div>
                                            <!-- 单价 -->
                                            <div
                                                v-if="config.unitPrice"
                                                class="charge-list-advice-list-item-social-total-price"
                                            >
                                                {{ chargeForm.unitPrice | formatMoney }}
                                            </div>
                                            <!-- 数量 -->
                                            <div class="charge-list-advice-list-item-social-total-price">
                                                {{ chargeForm.count }}
                                            </div>
                                            <!-- 金额 -->
                                            <div class="charge-list-advice-list-item-social-total-price">
                                                {{ chargeForm.totalPrice | formatMoney }}
                                            </div>
                                        </div>
                                        <div
                                            v-if="(!config.isPrintNonZeroItem || chargeForm.totalPrice) && showChargeItemTraceCode && Array.isArray(chargeForm.traceableCodeList) && chargeForm.traceableCodeList.length"
                                            :key="`charge-list-advice-list-item-traceable-code-tr-${adviceTypePrintIdx}-${adviceIdx}-${chargeFormIdx}`"
                                            class="charge-list-advice-list-item"
                                            data-type="item"
                                        >
                                            <!-- 名称 -->
                                            <div class="charge-list-advice-list-item-name"></div>
                                            <!-- 医保等级 -->
                                            <div
                                                v-if="config.medicalGrade"
                                                class="charge-list-advice-list-item-medical-grade"
                                            ></div>
                                            <div :style="{ width: `calc(${adviceChargeItemTraceableCodeWidth}% + 8px)` }">
                                                {{ chargeForm.traceableCodeList.map((traceableCode) => traceableCode.no).join('、') }}
                                            </div>
                                        </div>
                                    </template>
                                </template>
                            </template>

                            <!-- 医嘱 -->
                            <template v-else>
                                <template v-for="(advice, adviceIdx) in (adviceTypePrint.hisChargeFormItemGroupByAdvicePrintInfos || [])">
                                    <div
                                        v-if="!config.isPrintNonZeroItem || advice.totalPrice"
                                        :key="`charge-list-advice-list-item-${adviceTypePrintIdx}-${adviceIdx}`"
                                        class="charge-list-advice-list-item"
                                        data-type="item"
                                    >
                                        <!-- 名称 -->
                                        <div class="charge-list-advice-list-item-name">
                                            {{ advice.adviceName || '' }}
                                        </div>
                                        <!-- 医保等级 -->
                                        <div
                                            v-if="config.medicalGrade"
                                            class="charge-list-advice-list-item-medical-grade"
                                        >
                                        </div>
                                        <!-- 医保码 -->
                                        <div
                                            v-if="config.code"
                                            class="charge-list-advice-list-item-social-code"
                                        >
                                        </div>
                                        <!-- 规格 -->
                                        <div
                                            v-if="config.spec"
                                            class="charge-list-advice-list-item-social-spec"
                                        >
                                        </div>
                                        <!-- 单位 -->
                                        <div class="charge-list-advice-list-item-social-unit">
                                            {{ advice.unit || '' }}
                                        </div>
                                        <!-- 单价 -->
                                        <div
                                            v-if="config.unitPrice"
                                            class="charge-list-advice-list-item-social-total-price"
                                        >
                                            {{ advice.unitPrice | formatMoney }}
                                        </div>
                                        <!-- 数量 -->
                                        <div class="charge-list-advice-list-item-social-total-price">
                                            {{ advice.unitCount }}
                                        </div>
                                        <!-- 金额 -->
                                        <div class="charge-list-advice-list-item-social-total-price">
                                            {{ advice.totalPrice | formatMoney }}
                                        </div>
                                    </div>
                                </template>
                            </template>
                        </template>
                    </div>
                </template>

                <!-- 费用项目 -->
                <template v-else>
                    <template v-for="(printForm, printFormIndex) in item.itemGroupByGoodsTypePrintInfos">
                        <div
                            v-if="!config.isPrintNonZeroItem || printForm.totalPrice"
                            :key="printFormIndex"
                            class="fee-item-table"
                            data-type="group"
                        >
                            <div
                                class="table-header"
                                data-type="item"
                            >
                                <div
                                    class="table-td item-name item-title"
                                    :style="{ width: `${itemNameWidth}%` }"
                                >
                                    {{ printForm.goodsTypeName }}
                                </div>
                                <div
                                    v-if="config.code || showChargeItemTraceCode"
                                    class="table-td item-code"
                                >
                                    <template v-if="config.code">
                                        国家代码
                                    </template>
                                    <template v-if="config.code && showChargeItemTraceCode">
                                        /
                                    </template>
                                    <template v-if="showChargeItemTraceCode">
                                        追溯码
                                    </template>
                                </div>
                                <div
                                    v-if="config.spec"
                                    class="table-td item-display-spec"
                                >
                                    规格
                                </div>
                                <div class="table-td item-unit">
                                    单位
                                </div>
                                <div
                                    v-if="config.unitPrice"
                                    class="table-td item-unit-price"
                                >
                                    单价
                                </div>
                                <div class="table-td item-unit-count">
                                    数量
                                </div>
                                <div class="table-td item-total-price">
                                    金额
                                </div>
                            </div>
                            <template v-for="(hisChargeFormItem, hisChargeFormItemIndex) in printForm.hisChargeFormItemPrintInfoList">
                                <div
                                    v-if="!config.isPrintNonZeroItem || hisChargeFormItem.totalPrice"
                                    :key="hisChargeFormItemIndex"
                                    class="table-body"
                                    data-type="item"
                                >
                                    <div
                                        class="table-td item-name"
                                        :style="{ width: `${itemNameWidth}%` }"
                                    >
                                        <template v-if="config.medicalGrade && hisChargeFormItem.shebaoGoodsItem && hisChargeFormItem.shebaoGoodsItem.socialCode">
                                            [{{ hisChargeFormItem.shebaoGoodsItem.medicalFeeGrade | medicalFeeGrade2PrintStr }}{{ filterOwnExpenseRatio(hisChargeFormItem.shebaoGoodsItem.ownExpenseRatio) }}]
                                        </template>
                                        {{ hisChargeFormItem.name }}
                                    </div>
                                    <div
                                        v-if="config.code"
                                        class="table-td item-code"
                                        overflow
                                    >
                                        <template v-if="hisChargeFormItem.goodsSnapshotVersion && hisChargeFormItem.goodsSnapshotVersion.shebao">
                                            {{ hisChargeFormItem.goodsSnapshotVersion.shebao.nationalCode }}
                                        </template>
                                    </div>
                                    <div
                                        v-if="config.spec"
                                        class="table-td item-display-spec"
                                        overflow
                                    >
                                        <template v-if="isShowDisplaySpec(hisChargeFormItem)">
                                            {{ hisChargeFormItem.productInfoSnapshot && hisChargeFormItem.productInfoSnapshot.displaySpec }}
                                        </template>
                                    </div>
                                    <div
                                        class="table-td item-unit"
                                        overflow
                                    >
                                        {{ hisChargeFormItem.unit }}
                                    </div>
                                    <div
                                        v-if="config.unitPrice"
                                        class="table-td item-unit-price"
                                        overflow
                                    >
                                        {{ hisChargeFormItem.unitPrice | formatMoney }}
                                    </div>
                                    <div
                                        class="table-td item-unit-count"
                                        overflow
                                    >
                                        {{ hisChargeFormItem.count }}
                                    </div>
                                    <div
                                        class="table-td item-total-price"
                                        overflow
                                    >
                                        {{ hisChargeFormItem.totalPrice | formatMoney }}
                                    </div>
                                </div>
                                <div
                                    v-if="(!config.isPrintNonZeroItem || hisChargeFormItem.totalPrice) && showChargeItemTraceCode && Array.isArray(hisChargeFormItem.traceableCodeList) && hisChargeFormItem.traceableCodeList.length"
                                    :key="`traceable-code-list-tr-${hisChargeFormItemIndex}`"
                                    class="table-body"
                                    data-type="item"
                                >
                                    <div
                                        class="table-td item-name"
                                        :style="{ width: `${itemNameWidth}%` }"
                                    ></div>
                                    <div
                                        class="table-td item-code"
                                        :style="{ width: `${100 - itemNameWidth}%` }"
                                    >
                                        {{ hisChargeFormItem.traceableCodeList.map((traceableCode) => traceableCode.no).join('、') }}
                                    </div>
                                </div>
                            </template>
                            <div
                                class="table-footer"
                                data-type="item"
                            >
                                合计：{{ printForm.totalPrice | formatMoney }}
                            </div>
                        </div>
                    </template>
                </template>
            </div>
        </template>
    </div>
</template>

<script>
    import PrintCommonDataHandler from "./data-handler/common-handler";
    import { PrintBusinessKeyEnum } from "./constant/print-constant";
    import PageSizeMap, { Orientation } from "../share/page-size.js";
    import {
        filterOwnExpenseRatio,
        formatAge,
        formatMoney, getLengthWithFullCharacter, isNotNull, isNull, isShowDisplaySpec,
        parseTime,
    } from "./common/utils";
    import { TITLE_MAX_LENGTH, TreatmentTypeEnum } from "./common/constants";

    const initPrintConfig = {
        'title': '', // 抬头名称 字符串必填 String
        'subtitle': '', // 副抬头名称 可选值 String
        'medicalGrade': 1, // 医保等级, 可选值 0 1, 默认 1
        'code': 1, // 国家代码, 可选值 0 1, 默认 1
        'spec': 1, // 规格, 可选值 0 1, 默认 1
        'unit': 1, // 单位, 可选值 0 1, 默认 1
        'unitPrice': 1, // 单价, 可选值 0 1, 默认 1
        'count': 1, // 数量, 可选值 0 1, 默认 1
        'feeInfo': 1, // 收费项目, 可选值 0 1, 默认 1
        'feeType': 1, // 费用类型, 可选值 0 1, 默认 1
        'settlementDetail': 1, // 结算明细, 可选值 0 1, 默认 1
        'sellerName': 1, // 收费员, 可选值 0 1, 默认 1
        'chargeTime': 1, // 收费日期, 可选值 0 1, 默认 1
        'chargeClinic': 1, // 收费单位, 可选值 0 1, 默认 1
        'chargeClinicContent': '', // 收费单位, 默认值同 title, String
        'isPrintNonZeroItem': 0, // 不打印费用为0的项目, 可选值0 1, 默认 0
        'traceCode': 0, // 追溯码二维码, 可选值0 1, 默认 0
    };

    export default {
        name: 'ChargeListBatch',
        DataHandler: PrintCommonDataHandler,
        filters: {
            formatMoney,
            medicalFeeGrade2PrintStr(medicalFeeGrade) {
                if(!medicalFeeGrade) return '';
                switch (medicalFeeGrade) {
                    case 1:
                        return '甲 ';
                    case 2:
                        return '乙 ';
                    case 3:
                        return '丙 ';
                    default:
                        return '';
                }
            },
        },
        props: {
            renderData: {
                type: Object,
                default: () => ({}),
            },
        },
        businessKey: PrintBusinessKeyEnum.CHARGE_LIST_BATCH,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
        ],
        data() {
            return {
                TreatmentTypeEnum,
            };
        },
        computed: {
            printData() {
                return this.renderData.printData || {};
            },
            config() {
                if (this.renderData.config?.hospitalFeeBills?.chargeFeeList) {
                    return this.renderData.config.hospitalFeeBills.chargeFeeList;
                }
                return initPrintConfig;
            },
            // 处理最大字数后的标题
            pageTitle() {
                const configTitle = this.config.title || this.clinicName;
                const {
                    fullCharacterLength, splitLength,
                } = getLengthWithFullCharacter(configTitle, TITLE_MAX_LENGTH);
                if (fullCharacterLength > TITLE_MAX_LENGTH) {
                    return configTitle.slice(0, splitLength);
                }
                return configTitle;
            },
            itemNameWidth() {
                let width = 38;
                if (!this.config.code) {
                    width += 24;
                }
                if (!this.config.spec) {
                    width += 10;
                }
                if (!this.config.unitPrice) {
                    width += 7;
                }
                return width;
            },
            feeDetailPrintInfoList() {
                return this.printData.feeDetailPrintInfoList || [];
            },
            clinicName() {
                return this.printData.clinicName || '';
            },
            nationalCode() {
                return this.printData.nationalCode || '';
            },
            traceCodeConfig() {
                return this.config.traceCode ?? 0;
            },
            showTraceCodeQrCode() {
                return this.traceCodeConfig === 2;
            },
            showChargeItemTraceCode() {
                return this.traceCodeConfig === 1;
            },
            adviceChargeItemTraceableCodeWidth() {
                let width = 25 + 7 + 7 + 7;
                if (this.config.spec) {
                    width += 11;
                }
                if (this.config.unitPrice) {
                    width += 7;
                }
                return width;
            },
        },
        methods: {
            isNotNull,
            isNull,
            parseTime,
            formatAge,
            filterOwnExpenseRatio,
            isShowDisplaySpec,
        },
    }
</script>

<style lang="scss">
    .header {
        padding: 16px 0 8px 0;
        position: relative;

        .header-patient-title {
            margin-bottom: 16px;

            .title {
                font-family: SimSun;
                font-size: 20px;
                font-weight: bolder;
                line-height: 1;
                text-align: center;
                letter-spacing: 2px;
            }

            .sub-title {
                margin-top: 5px;
                font-family: SimSun;
                font-size: 16px;
                line-height: 1;
                text-align: center;
            }
        }

        .header-patient-form {
            line-height: 18px;
            word-spacing: -99px;

            &.margin-top-6 {
                margin-top: 4px;
            }

            .header-patient-item {
                display: inline-block;
                overflow: hidden;
                font-size: 11px;
                line-height: 16px;
                text-overflow: clip;
                white-space: nowrap;
                vertical-align: middle;

                &.width-40 {
                    width: 40%;
                }

                &.width-35 {
                    width: 35%;
                }

                &.width-25 {
                    width: 25%;
                }

                .margin-left-6 {
                    margin-left: 8px;
                }
            }
        }
    }

    .charge-list {
        border-bottom: 1px dashed #a6a6a6;

        .fee-item-table {
            padding: 8px 0;
            border-top: 1px solid #a6a6a6;

            .table-header {
                line-height: 16px;
                vertical-align: top;
                word-spacing: -99px;
            }

            .table-body {
                display: table;
                width: 100%;
                line-height: 16px;
                vertical-align: top;
                word-spacing: -99px;
            }

            .table-footer {
                font-size: 11px;
                font-weight: bold;
                line-height: 16px;
                text-align: right;
            }

            .table-td {
                display: inline-block;
                overflow: hidden;
                font-size: 11px;
                line-height: 16px;
                text-overflow: clip;
                white-space: nowrap;
                vertical-align: top;
                word-spacing: 0;

                &.item-title {
                    font-weight: bold;
                }

                &.item-name {
                    box-sizing: border-box;
                    width: 38%;
                    padding-right: 20px;
                    white-space: normal;
                }

                &.item-code {
                    box-sizing: border-box;
                    width: 24%;
                    padding-right: 5px;
                }

                &.item-display-spec {
                    box-sizing: border-box;
                    width: 10%;
                    padding-right: 5px;
                    text-align: left;
                }

                &.item-unit {
                    width: 7%;
                    text-align: left;
                }

                &.item-unit-price {
                    width: 7%;
                    text-align: right;
                }

                &.item-unit-count {
                    width: 7%;
                    text-align: right;
                }

                &.item-total-price {
                    width: 7%;
                    text-align: right;
                }

                &.item-fee-type {
                    width: 18%;
                    line-height: 16px;
                    word-spacing: -99px;

                    &:not(:first-child) {
                        margin-left: 9.2%;
                    }

                    .fee-type-item {
                        display: inline-block;
                        width: 58%;
                        overflow: hidden;
                        text-overflow: clip;
                        white-space: nowrap;
                        vertical-align: top;

                        &.fee-type-item-right {
                            width: 42%;
                            text-align: right;
                        }
                    }
                }
            }
        }

        .charge-list-advice-list {
            display: flex;
            flex-direction: column;
            width: 100%;
            font-size: 11px;
            line-height: 16px;
        }

        .charge-list-advice-list-item {
            display: flex;
            align-items: flex-start;
            width: 100%;
        }

        .charge-list-advice-list-item-name {
            display: flex;
            flex: 1;
            flex-wrap: wrap;
            align-items: flex-start;
            padding-right: 4px;
            word-break: break-all;
            word-wrap: break-word;
        }

        .charge-list-advice-list-item-social-code {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            width: 25%;
            padding-right: 4px;
            word-break: break-all;
            word-wrap: break-word;
        }

        .charge-list-advice-list-item-medical-grade {
            display: flex;
            flex-wrap: nowrap;
            align-items: flex-start;
            width: 7%;
            padding-right: 4px;
            overflow: hidden;
        }

        .charge-list-advice-list-item-social-spec {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            width: 11%;
            padding-right: 4px;
            word-break: break-all;
            word-wrap: break-word;
        }

        .charge-list-advice-list-item-social-unit {
            display: flex;
            align-items: flex-start;
            width: 7%;
        }

        .charge-list-advice-list-item-social-total-price {
            display: flex;
            align-items: flex-start;
            justify-content: flex-end;
            width: 7%;
        }
    }
</style>
