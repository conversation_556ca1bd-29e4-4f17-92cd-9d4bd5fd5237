<template>
    <div></div>
</template>

<script>
    import {PrintBusinessKeyEnum} from "./constant/print-constant";
    import CommonHandler from "./data-handler/common-handler";
    import PageSizeMap, {Orientation} from "../share/page-size.js";

    export default {
        name: "CashierReportGroupTemplate",
        DataHandler: CommonHandler,
        businessKey: PrintBusinessKeyEnum.CASHIER_REPORT_GROUP_TEMPLATE,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: true,
                defaultOrientation: Orientation.landscape,
                defaultHeightLevel: null,
            },
        ],
    }
</script>

<style lang="scss">
.abc-flex-wrapper{
  display: flex;
}
.abc-flex-justify-normal {
  justify-content: normal;
}
.abc-flex-justify-space-between {
  justify-content: space-between;
}
.abc-flex-align-center {
  align-items: center;
}
.abc-flex-wrap-wrap {
  flex-wrap: wrap;
}
.abc-flex-wrap-nowrap {
  flex-wrap: nowrap;
}
.abc-flex-vertical {
  flex-direction: column;
}
.abc-text-large {
  font-size: 16px;
  line-height: 22px;
}
.abc-text-mini {
  font-size: 12px;
  line-height: 16px;
}
.cashier-report-header{
  margin-bottom: 24px;
}
table {
  width: 100%;
  font-size: 12px;
  border: 1px solid #aaa;
  table-layout: fixed;
  border-collapse: collapse;
  border-spacing: 0;
  margin-bottom: 24px;

  thead {
    background: #f5f7fb;

    th {
      height: 28px !important;
      font-weight: bold;
      color: #000000;
      border: 1px solid #aaa;
      border-left: none;
      border-top: none;
      padding-left: 8px;
      padding-right: 8px;
      white-space: nowrap;
      word-break: keep-all;
    }
  }

  tbody {
    .tr-summaries {
      font-weight: bold;
    }
    tr {
      border-bottom: 1px solid #aaa;
      border-left: none;
      border-right: none;

      td {
        height: 28px !important;
        box-sizing: border-box;

        &:not(:last-child) {
          border-right: 1px solid #aaa;
        }

        div.cell {
          padding: 2px;
        }

        background: #fff;
      }
    }
  }
}

.custom-report-table {
  table-layout: fixed;

  &.no-border-table {
    td {
      border: none;

      &:nth-child(2) {
        padding-left: 22px;
      }

      &:nth-child(3) {
        padding-left: 78px;
      }
    }
  }

  td {
    padding: 7px 6px;
    overflow: hidden;
    font-size: 12px;
    line-height: 13px;
    text-overflow: ellipsis;
    white-space: nowrap;
    border: 1px solid #aaa;
  }

  .title-td {
    font-size: 12px;
    font-weight: bold;
    line-height: 13px;
    background-color: #f5f7fb;
  }

  .report-table-container-type {
    padding-top: 4px;
  }
}
</style>
