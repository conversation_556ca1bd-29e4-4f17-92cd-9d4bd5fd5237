<template>
    <div>
        <template v-for="(form, idx) in renderFormList">
            <treatment-execute-nanning-header
                :key="`treatment-header-${idx}`"
                :print-data="form"
                :config="config"
                data-type="header"
                :print-title="getCurrentSheetTitle(form)"
                :data-pendants-index="`${idx}-treatment-form`"
            >
            </treatment-execute-nanning-header>

            <!-- 签名栏在右侧 -->
            <template v-if="headerConfig.signPosition === signPosition.right">
                <table
                    :key="`treatment-sign-right-table-${idx}`"
                    data-type="mix-box"
                    class="print-treatment-table"
                >
                    <thead>
                        <tr class="treatment-execute-form-tr">
                            <th
                                :colspan="executeFormNameColSpan"
                                class="no-right-border"
                                style="padding-right: 0;"
                            >
                                项目
                            </th>
                            <th
                                v-if="contentConfig.productUnitPrice"
                                class="text-right no-right-border no-left-border"
                                :colspan="2"
                                style="padding-right: 0; padding-left: 0;"
                            >
                                单价
                            </th>
                            <th
                                class="text-right no-right-border no-left-border"
                                :colspan="2"
                                style="padding-right: 0; overflow: hidden; white-space: nowrap;"
                                overflow
                            >
                                执行/总次数
                            </th>
                            <th
                                v-if="contentConfig.productExecutedPrice"
                                class="text-right no-right-border no-left-border"
                                :colspan="2"
                                style="padding-right: 0;"
                            >
                                执行金额
                            </th>
                            <th
                                v-if="contentConfig.productTotalPrice"
                                class="text-right no-right-border no-left-border"
                                :colspan="2"
                                style="padding-right: 0;"
                            >
                                总金额
                            </th>
                            <th
                                colspan="4"
                                class="text-center"
                            >
                                时间/签名
                            </th>
                        </tr>
                    </thead>
                    <tbody data-type="group">
                        <template v-for="(formItem, formItemIndex) in form.formItems">
                            <template v-if="formItem.composeType === 1">
                                <template v-if="contentConfig.compose !== 1">
                                    <tr
                                        v-if="formItem.groupItems.length"
                                        :key="`treatment-right-compose-name-${formItemIndex}`"
                                        class="treatment-execute-form-tr"
                                        :class="{'has-remark-tr': formItem.remark}"
                                        data-type="item"
                                    >
                                        <td
                                            :colspan="executeFormNameColSpan"
                                            class="no-right-border"
                                            style="padding-right: 0; font-weight: bold;"
                                            overflow
                                        >
                                            <span
                                                v-if="formItem.toothNos && formItem.toothNos.length"
                                                v-html="formatToothNos2Html(formItem.toothNos)"
                                            ></span>
                                            【套】{{ formItem.composeName }}
                                        </td>
                                        <td
                                            v-if="contentConfig.productUnitPrice"
                                            colspan="2"
                                            style="padding-right: 0; padding-left: 0;"
                                            class="no-right-border no-left-border text-right"
                                        >
                                            {{ formItem.composeUnitPrice | formatMoney }}
                                        </td>
                                        <td
                                            colspan="2"
                                            class="no-right-border no-left-border"
                                            style="padding-right: 0;"
                                        >
                                        </td>
                                        <!-- 执行金额 -->
                                        <td
                                            v-if="contentConfig.productExecutedPrice"
                                            colspan="2"
                                            style="padding-right: 0;"
                                            class="no-right-border no-left-border text-right"
                                        >
                                            <template v-if="formItem.needExecutive">
                                                {{ formItem.executedTotalPrice | formatMoney }}
                                            </template>
                                        </td>
                                        <!-- 总金额 -->
                                        <td
                                            v-if="contentConfig.productTotalPrice"
                                            colspan="2"
                                            style="padding-right: 0;"
                                            class="no-right-border no-left-border text-right"
                                        >
                                            {{ formItem.totalPrice | formatMoney }}
                                        </td>

                                        <td
                                            colspan="4"
                                            class="border-td"
                                            :rowspan="formItem.remark ? 2 : 1"
                                        ></td>
                                    </tr>
                                    <td
                                        v-if="formItem.remark && formItem.groupItems.length"
                                        :key="`treatment-right-compose-remark-${formItemIndex}`"
                                        overflow
                                        colspan="12"
                                        style="word-break: normal; white-space: normal;"
                                        class="remark-text no-right-border remark-td"
                                    >
                                        {{ formItem.remark }}
                                    </td>
                                </template>
                                <template v-if="contentConfig.compose !== 0">
                                    <template v-for="(groupItem, gIndex) in formItem.groupItems">
                                        <tr
                                            class="treatment-execute-form-tr"
                                            :class="{'has-remark-tr': contentConfig.compose === 1 && formItem.remark}"
                                            data-type="item"
                                        >
                                            <td
                                                :colspan="executeFormNameColSpan"
                                                class="no-right-border"
                                                style="padding-right: 0; font-weight: bold;"
                                                overflow
                                            >
                                                <div
                                                    v-if="contentConfig.compose === 2"
                                                    class="no-right-border"
                                                    style="display: inline-block; margin-left: 16px; text-align: right;"
                                                >
                                                    {{ gIndex + 1 }}.
                                                </div>
                                                {{ groupItem.name }}
                                            </td>
                                            <td
                                                v-if="contentConfig.productUnitPrice"
                                                colspan="2"
                                                style="padding-right: 0; padding-left: 0;"
                                                class="no-right-border no-left-border text-right"
                                            >
                                                {{ groupItem.unitPrice | formatMoney }}
                                            </td>
                                            <td
                                                overflow
                                                colspan="2"
                                                class="no-right-border no-left-border text-right"
                                                style="padding-right: 0;"
                                            >
                                                <span v-if="!groupItem.needExecutive">{{ groupItem.unitCount
                                                }}{{ formatTreatmentUnit(groupItem.unit, ' ') }}</span>
                                                <span v-else> {{ groupItem.executedCount || 0 }}/{{ groupItem.unitCount
                                                }}{{ formatTreatmentUnit(groupItem.unit, ' ') }}</span>
                                            </td>
                                            <!-- 执行金额 -->
                                            <td
                                                v-if="contentConfig.productExecutedPrice"
                                                colspan="2"
                                                style="padding-right: 0;"
                                                class="no-right-border no-left-border text-right"
                                            >
                                                <template v-if="groupItem.needExecutive">
                                                    {{ groupItem.executedTotalPrice | formatMoney }}
                                                </template>
                                            </td>
                                            <!-- 总金额 -->
                                            <td
                                                v-if="contentConfig.productTotalPrice"
                                                colspan="2"
                                                style="padding-right: 0;"
                                                class="no-right-border no-left-border text-right"
                                            >
                                                {{ groupItem.totalPrice | formatMoney }}
                                            </td>
                                            <td
                                                colspan="4"
                                                class="border-td"
                                                :rowspan="contentConfig.compose === 1 && formItem.remark ? 2 : 1"
                                            ></td>
                                        </tr>
                                        <tr
                                            v-if="contentConfig.compose === 1 && formItem.remark"
                                            data-type="item"
                                        >
                                            <td
                                                overflow
                                                class="remark-text no-right-border remark-td"
                                                colspan="12"
                                            >
                                                {{ formItem.remark }}
                                            </td>
                                        </tr>
                                    </template>
                                </template>
                            </template>
                            <template v-else>
                                <template v-for="(groupItem, gIndex) in formItem.groupItems">
                                    <tr
                                        class="treatment-execute-form-tr"
                                        :class="{'has-remark-tr': groupItem.remark}"
                                        data-type="item"
                                    >
                                        <td
                                            :colspan="executeFormNameColSpan"
                                            class="no-right-border"
                                            style="padding-right: 0; font-weight: bold;"
                                            overflow
                                        >
                                            <span
                                                v-if="groupItem.toothNos && groupItem.toothNos.length"
                                                v-html="formatToothNos2Html(groupItem.toothNos)"
                                            ></span>
                                            {{ groupItem.name }}
                                        </td>
                                        <td
                                            v-if="contentConfig.productUnitPrice"
                                            colspan="2"
                                            style="padding-right: 0; padding-left: 0;"
                                            class="no-right-border no-left-border text-right"
                                        >
                                            {{ groupItem.unitPrice | formatMoney }}
                                        </td>
                                        <td
                                            overflow
                                            colspan="2"
                                            class="no-right-border no-left-border text-right"
                                            style="padding-right: 0;"
                                        >
                                            <span v-if="!groupItem.needExecutive">{{ groupItem.unitCount
                                            }}{{ formatTreatmentUnit(groupItem.unit, ' ') }}</span>
                                            <span v-else> {{ groupItem.executedCount || 0 }}/{{ groupItem.unitCount
                                            }}{{ formatTreatmentUnit(groupItem.unit, ' ') }}</span>
                                        </td>
                                        <!-- 执行金额 -->
                                        <td
                                            v-if="contentConfig.productExecutedPrice"
                                            colspan="2"
                                            style="padding-right: 0;"
                                            class="no-right-border no-left-border text-right"
                                        >
                                            <template v-if="groupItem.needExecutive">
                                                {{ groupItem.executedTotalPrice | formatMoney }}
                                            </template>
                                        </td>
                                        <!-- 总金额 -->
                                        <td
                                            v-if="contentConfig.productTotalPrice"
                                            colspan="2"
                                            style="padding-right: 0;"
                                            class="no-right-border no-left-border text-right"
                                        >
                                            {{ groupItem.totalPrice | formatMoney }}
                                        </td>

                                        <td
                                            class="border-td"
                                            colspan="4"
                                            :rowspan="groupItem.remark ? 2 : 1"
                                        ></td>
                                    </tr>
                                    <tr
                                        v-if="groupItem.remark"
                                        data-type="item"
                                    >
                                        <td
                                            class="remark-text no-right-border remark-td"
                                            style="word-break: normal; white-space: normal;"
                                            colspan="12"
                                        >
                                            {{ formItem.remark }}
                                        </td>
                                    </tr>
                                </template>
                            </template>
                        </template>

                        <!-- 外治处方 -->
                        <template v-for="(externalForm, formIndex) in form.externalForms">
                            <template v-for="(formItem, formItemIndex) in externalForm.prescriptionFormItems">
                                <tr
                                    :key="`external-table-tr-${formItemIndex}`"
                                    data-type="item"
                                    :data-item-group-id="`${formIndex}-${formItemIndex}`"
                                    class="treatment-execute-form-tr"
                                >
                                    <td
                                        overflow
                                        :colspan="executeFormNameColSpan"
                                        class="no-right-border no-bottom-border"
                                        style="padding-right: 0; padding-bottom: 4.5pt; font-weight: bold;"
                                    >
                                        <div
                                            class="no-right-border"
                                            style="display: inline-block; text-align: right;"
                                        >
                                            {{ formatName(externalForm, formItem) }}
                                        </div>
                                    </td>
                                    <td
                                        v-if="contentConfig.productUnitPrice"
                                        colspan="2"
                                        style="padding-right: 0; padding-bottom: 4.5pt; padding-left: 0;"
                                        class="no-right-border no-left-border text-right no-bottom-border"
                                    >
                                        {{ formItem.unitPrice | formatMoney }}
                                    </td>
                                    <td
                                        colspan="2"
                                        class="no-right-border no-left-border text-right no-bottom-border"
                                        style="padding-right: 0; padding-bottom: 4.5pt;"
                                    >
                                        <span v-if="!formItem.needExecutive">
                                            {{ formItem.unitCount }}{{ formatTreatmentUnit(formItem.unit, ' ') }}</span>
                                        <span v-else>
                                            {{ formItem.executedCount || 0 }}/{{ formItem.unitCount }}{{ formatTreatmentUnit(formItem.unit, ' ') }}
                                        </span>
                                    </td>
                                    <!-- 执行金额 -->
                                    <td
                                        v-if="contentConfig.productExecutedPrice"
                                        colspan="2"
                                        style="padding-right: 0; padding-bottom: 4.5pt;"
                                        class="no-right-border no-left-border text-right no-bottom-border"
                                    >
                                        <template v-if="formItem.needExecutive">
                                            {{ formItem.executedTotalPrice | formatMoney }}
                                        </template>
                                    </td>
                                    <!-- 总金额 -->
                                    <td
                                        v-if="contentConfig.productTotalPrice"
                                        colspan="2"
                                        style="padding-right: 0; padding-bottom: 4.5pt;"
                                        class="no-right-border no-left-border text-right no-bottom-border"
                                    >
                                        {{ formItem.totalPrice | formatMoney }}
                                    </td>
                                    <td
                                        class="border-td"
                                        colspan="4"
                                        :rowspan="externalFormRowSpan(formItem)"
                                    ></td>
                                </tr>

                                <tr
                                    v-if="formItem.externalGoodsItems && formItem.externalGoodsItems.length > 0"
                                    :key="`external-right-goods-name-${formItemIndex}`"
                                    data-type="item"
                                    :data-item-group-id="`${formIndex}-${formItemIndex}`"
                                >
                                    <td
                                        class="remark-text no-right-border remark-td no-bottom-border"
                                        style="word-break: normal;"
                                        colspan="12"
                                    >
                                        <!--药品-->
                                        <div style="display: flex;">
                                            <span class="label">药品：</span>
                                            <div style="display: inline-block; word-break: normal; white-space: normal;">
                                                {{ goodsStr(formItem.externalGoodsItems) }}
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr
                                    v-if="contentConfig.includeExternalAcupuncture && formatAcupoints(formItem.acupoints)"
                                    :key="`external-right-goods-acu-points-${formItemIndex}`"
                                    data-type="item"
                                    :data-item-group-id="`${formIndex}-${formItemIndex}`"
                                >
                                    <td
                                        class="remark-text no-right-border remark-td no-bottom-border"
                                        colspan="12"
                                    >
                                        <div style="display: flex;">
                                            <span class="label">
                                                {{ isBasedOnAcupoint(externalForm) ? '穴位：' : '部位：' }}
                                            </span>
                                            <div style="display: inline-block; word-break: normal; white-space: normal;">
                                                {{ formatAcupoints(formItem.acupoints) }}
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr
                                    :key="`external-right-goods-usage-${formItemIndex}`"
                                    data-type="item"
                                    :data-item-group-id="`${formIndex}-${formItemIndex}`"
                                >
                                    <td
                                        overflow
                                        class="remark-text no-right-border remark-td"
                                        colspan="12"
                                    >
                                        <div style="display: flex;">
                                            <span class="label">用法：</span>
                                            <div style="display: inline-block; word-break: normal; white-space: normal;">
                                                <template v-if="formItem.unitCount">
                                                    共 {{ formItem.dosage }} 次
                                                </template>
                                                <template v-if="formItem.freq">
                                                    ，{{ formItem.freq }}
                                                </template>
                                                <template
                                                    v-if="formItem.unitCount &&
                                                        formItem.dosage &&
                                                        isBasedOnAcupoint(externalForm) &&
                                                        formItem.acupoints &&
                                                        formItem.acupoints.length"
                                                >
                                                    ，每次{{ calcDosageCount(formItem) }}穴
                                                    ，共{{ formItem.dosage * calcDosageCount(formItem) }}穴
                                                </template>
                                                <tempalte v-if="formItem.specialRequirement">
                                                    ，{{ formItem.specialRequirement }}
                                                </tempalte>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </template>
                    </tbody>
                </table>

                <!-- 以下空白 -->
                <next-is-blank
                    :key="`external-next-blank-${idx}`"
                    style="margin: 0;"
                ></next-is-blank>
            </template>

            <!-- 签名栏在下方 -->
            <template v-else>
                <table
                    :key="`treatment-bottom-${idx}`"
                    data-type="mix-box"
                    class="print-treatment-table "
                >
                    <thead>
                        <tr>
                            <th
                                :colspan="executeFormBottomNameColSpan"
                                class="no-border unit-td"
                            >
                                项目
                            </th>
                            <th
                                v-if="contentConfig.productUnitPrice"
                                colspan="3"
                                class="no-border text-right unit-td"
                            >
                                单价
                            </th>
                            <th
                                colspan="2"
                                class="no-border text-right unit-td"
                            >
                                数量
                            </th>
                            <th
                                colspan="2"
                                class="no-border text-right unit-td"
                            >
                                单位
                            </th>
                            <th
                                v-if="contentConfig.productExecutedPrice"
                                colspan="3"
                                class="no-border text-right unit-td"
                            >
                                执行金额
                            </th>
                            <th
                                v-if="contentConfig.productTotalPrice"
                                colspan="3"
                                class="no-border text-right unit-td"
                            >
                                总金额
                            </th>
                            <th
                                colspan="2"
                                class="no-border text-right unit-td"
                            >
                                天数
                            </th>
                        </tr>
                    </thead>
                    <tbody data-type="group">
                        <template v-for="(formItem, formItemIndex) in form.formItems">
                            <template v-if="formItem.composeType === 1">
                                <!--需要打印套餐名-->
                                <template v-if="contentConfig.compose !== 1">
                                    <tr
                                        v-if="formItem.groupItems.length"
                                        :key="`treatment-bottom-compose-name-${formItemIndex}`"
                                        :class="{'has-remark-tr': formItem.remark}"
                                        data-type="item"
                                    >
                                        <td
                                            overflow
                                            class="no-border unit-td"
                                            :colspan="executeFormBottomNameColSpan"
                                        >
                                            <span
                                                v-if="formItem.toothNos && formItem.toothNos.length"
                                                v-html="formatToothNos2Html(formItem.toothNos)"
                                            ></span>
                                            【套】{{ formItem.composeName }}
                                            <div
                                                class="line-remark"
                                                style="word-break: normal; white-space: normal;"
                                            >
                                                {{ formItem.remark }}
                                            </div>
                                        </td>
                                        <td
                                            v-if="contentConfig.productUnitPrice"
                                            class="no-border text-right unit-td"
                                            colspan="3"
                                        >
                                            {{ formItem.composeUnitPrice | formatMoney }}
                                        </td>
                                        <td
                                            class="no-border text-right unit-td"
                                            colspan="2"
                                        >
                                            {{ formItem.composeUnitCount }}
                                        </td>
                                        <td
                                            overflow
                                            class="no-border text-right unit-td unit-td"
                                            colspan="2"
                                        >
                                            {{ formItem.composeUnit }}
                                        </td>
                                        <!-- 执行金额 -->
                                        <td
                                            v-if="contentConfig.productExecutedPrice"
                                            class="no-border text-right unit-td"
                                            colspan="3"
                                        >
                                            <template v-if="formItem.needExecutive">
                                                {{ formItem.executedTotalPrice | formatMoney }}
                                            </template>
                                        </td>
                                        <!-- 总金额 -->
                                        <td
                                            v-if="contentConfig.productTotalPrice"
                                            class="no-border text-right unit-td"
                                            colspan="3"
                                        >
                                            {{ formItem.totalPrice | formatMoney }}
                                        </td>
                                        <td
                                            colspan="2"
                                            class="no-border text-right unit-td"
                                        >
                                            {{ formItem.days ? `${formItem.days }天` : '' }}
                                        </td>
                                    </tr>
                                </template>
                                <!--需要打印套餐子项-->
                                <template v-if="contentConfig.compose !== 0">
                                    <template v-for="(groupItem,gIndex) in formItem.groupItems">
                                        <!-- 未知原因，加 gIndex 作为 key 会渲染失败 -->
                                        <tr
                                            :class="{'has-remark-tr': contentConfig.compose === 1 && formItem.remark}"
                                            data-type="item"
                                        >
                                            <td
                                                overflow
                                                :colspan="executeFormBottomNameColSpan"
                                                class="no-border unit-td"
                                            >
                                                <div
                                                    v-if="contentConfig.compose === 2"
                                                    class="no-right-border"
                                                    style="display: inline-block; margin-left: 16px; text-align: right;"
                                                >
                                                    {{ gIndex + 1 }}.
                                                </div>
                                                {{ groupItem.name }}
                                                <span
                                                    v-if="contentConfig.compose === 1 && formItem.remark"
                                                    calss="line-remark"
                                                >
                                                    {{ formItem.remark }}
                                                </span>
                                            </td>
                                            <td
                                                v-if="contentConfig.productUnitPrice"
                                                colspan="3"
                                                class="no-border text-right unit-td"
                                            >
                                                {{ groupItem.unitPrice | formatMoney }}
                                            </td>
                                            <td
                                                colspan="2"
                                                class="no-border text-right unit-td"
                                            >
                                                {{ groupItem.unitCount }}
                                            </td>
                                            <td
                                                colspan="2"
                                                overflow
                                                class="no-border text-right unit-td"
                                            >
                                                {{ groupItem.unit }}
                                            </td>
                                            <!-- 执行金额 -->
                                            <td
                                                v-if="contentConfig.productExecutedPrice"
                                                class="no-border text-right unit-td"
                                                colspan="3"
                                            >
                                                <template v-if="groupItem.needExecutive">
                                                    {{ groupItem.executedTotalPrice | formatMoney }}
                                                </template>
                                            </td>
                                            <!-- 总金额 -->
                                            <td
                                                v-if="contentConfig.productTotalPrice"
                                                class="no-border text-right unit-td"
                                                colspan="3"
                                            >
                                                {{ groupItem.totalPrice | formatMoney }}
                                            </td>
                                            <td
                                                colspan="2"
                                                class="no-border text-right unit-td"
                                            >
                                                {{ groupItem.days ? `${groupItem.days }天` : '' }}
                                            </td>
                                        </tr>
                                    </template>
                                </template>
                            </template>
                            <template v-else>
                                <template v-for="(groupItem, gIndex) in formItem.groupItems">
                                    <!-- 未知原因，加 gIndex 作为 key 会渲染失败 -->
                                    <tr
                                        :class="{'has-remark-tr': groupItem.remark}"
                                        data-type="item"
                                    >
                                        <td
                                            :colspan="executeFormBottomNameColSpan"
                                            class="no-border unit-td"
                                        >
                                            <span
                                                v-if="groupItem.toothNos && groupItem.toothNos.length"
                                                v-html="formatToothNos2Html(groupItem.toothNos)"
                                            ></span>
                                            {{ groupItem.name }}
                                            <div
                                                class="line-remark"
                                                style="margin-left: 0; word-break: normal; white-space: normal;"
                                            >
                                                {{ groupItem.remark }}
                                            </div>
                                        </td>
                                        <td
                                            v-if="contentConfig.productUnitPrice"
                                            colspan="3"
                                            class="no-border text-right unit-td"
                                        >
                                            {{ groupItem.unitPrice | formatMoney }}
                                        </td>
                                        <td
                                            colspan="2"
                                            class="no-border text-right unit-td"
                                        >
                                            {{ groupItem.unitCount }}
                                        </td>
                                        <td
                                            colspan="2"
                                            overflow
                                            class="no-border text-right unit-td"
                                        >
                                            {{ groupItem.unit }}
                                        </td>
                                        <!-- 执行金额 -->
                                        <td
                                            v-if="contentConfig.productExecutedPrice"
                                            class="no-border text-right unit-td"
                                            colspan="3"
                                        >
                                            <template v-if="groupItem.needExecutive">
                                                {{ groupItem.executedTotalPrice | formatMoney }}
                                            </template>
                                        </td>
                                        <!-- 总金额 -->
                                        <td
                                            v-if="contentConfig.productTotalPrice"
                                            class="no-border text-right unit-td"
                                            colspan="3"
                                        >
                                            {{ groupItem.totalPrice | formatMoney }}
                                        </td>
                                        <td
                                            v-show="needDays(groupItem.productInfo)"
                                            colspan="2"
                                            class="no-border text-right unit-td"
                                        >
                                            {{ groupItem.days ? `${groupItem.days }天` : '' }}
                                        </td>
                                    </tr>
                                </template>
                            </template>
                        </template>

                        <template v-for="(externalForm) in form.externalForms">
                            <template
                                v-for="(formItem, formItemIndex) in externalForm.prescriptionFormItems"
                            >
                                <tr
                                    :key="`treatment-bottom-compose-item-${formItemIndex}`"
                                    data-type="item"
                                >
                                    <td class="no-border"></td>
                                </tr>
                                <tr
                                    :key="`treatment-bottom-compose-name-${formItemIndex}`"
                                    data-type="item"
                                >
                                    <td
                                        overflow
                                        :colspan="executeFormBottomNameColSpan"
                                        class="no-border unit-td"
                                    >
                                        <div
                                            class="no-right-border"
                                            style="display: inline-block; text-align: right;"
                                        >
                                            {{ formatName(externalForm, formItem) }}
                                        </div>
                                    </td>
                                    <td
                                        v-if="contentConfig.productUnitPrice"
                                        colspan="3"
                                        class="no-border text-right unit-td"
                                    >
                                        {{ formItem.unitPrice | formatMoney }}
                                    </td>
                                    <td
                                        colspan="2"
                                        class="no-border text-right unit-td"
                                    >
                                        {{ formItem.unitCount }}
                                    </td>
                                    <td
                                        colspan="2"
                                        overflow
                                        class="no-border text-right unit-td"
                                    >
                                        {{ formItem.unit }}
                                    </td>
                                    <!-- 执行金额 -->
                                    <td
                                        v-if="contentConfig.productExecutedPrice"
                                        class="no-border text-right unit-td"
                                        colspan="3"
                                    >
                                        <template v-if="formItem.needExecutive">
                                            {{ formItem.executedTotalPrice | formatMoney }}
                                        </template>
                                    </td>
                                    <!-- 总金额 -->
                                    <td
                                        v-if="contentConfig.productTotalPrice"
                                        class="no-border text-right unit-td"
                                        colspan="3"
                                    >
                                        {{ formItem.totalPrice | formatMoney }}
                                    </td>
                                    <td
                                        colspan="2"
                                        class="no-border text-right unit-td"
                                    >
                                        {{ formItem.days ? `${formItem.days }天` : '' }}
                                    </td>
                                </tr>

                                <tr
                                    v-if=" formItem.externalGoodsItems && formItem.externalGoodsItems.length > 0"
                                    :key="`external-bottom-goods-name-${formItemIndex}`"
                                    data-type="item"
                                >
                                    <td
                                        :colspan="24"
                                        class="no-border"
                                    >
                                        <!--药品-->
                                        <div style="display: flex;">
                                            <span class="label">药品：</span>
                                            <div style="display: inline-block; word-break: normal; white-space: normal;">
                                                {{ goodsStr(formItem.externalGoodsItems) }}
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr
                                    v-if="contentConfig.includeExternalAcupuncture && formatAcupoints(formItem.acupoints)"
                                    :key="`external-bottom-goods-acu-point-${formItemIndex}`"
                                    data-type="item"
                                >
                                    <td
                                        :colspan="24"
                                        class="no-border"
                                    >
                                        <div style="display: flex;">
                                            <span class="label">
                                                {{ isBasedOnAcupoint(externalForm) ? '穴位：' : '部位：' }}
                                            </span>
                                            <div style="display: inline-block; word-break: normal; white-space: normal;">
                                                {{ formatAcupoints(formItem.acupoints) }}
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr
                                    :key="`external-bottom-goods-usage-${formItemIndex}`"
                                    data-type="item"
                                >
                                    <td
                                        :colspan="24"
                                        class="no-border"
                                    >
                                        <div style="display: flex;">
                                            <span class="label">用法：</span>
                                            <div style="display: inline-block; word-break: normal; white-space: normal;">
                                                <template v-if="formItem.unitCount">
                                                    共 {{ formItem.dosage }} 次
                                                </template>
                                                <template v-if="formItem.freq">
                                                    ，{{ formItem.freq }}
                                                </template>
                                                <template
                                                    v-if="
                                                        formItem.unitCount &&
                                                            formItem.dosage &&
                                                            isBasedOnAcupoint(externalForm) &&
                                                            formItem.acupoints &&
                                                            formItem.acupoints.length
                                                    "
                                                >
                                                    ，每次{{ calcDosageCount(formItem) }}穴
                                                    ，共{{ formItem.dosage * calcDosageCount(formItem) }}穴
                                                </template>
                                                <template v-if="formItem.specialRequirement">
                                                    ，{{ formItem.specialRequirement }}
                                                </template>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </template>
                    </tbody>
                </table>

                <!-- 以下空白 -->
                <next-is-blank
                    :key="`external-next-blank-${idx}`"
                    style="margin: 0;"
                ></next-is-blank>
            </template>

            <!-- footer -->
            <div
                :key="`treatment-footer-${idx}`"
                data-type="footer"
                :data-pendants-index="`${idx}-treatment-form`"
            >
                <div
                    v-if="!extra.isPreview && headerConfig.signPosition === signPosition.bottom && tableRowNumber"
                    style="padding: 8pt;"
                >
                    <fill-box
                        class="treatment-sign-table"
                        :table-header="tableHeader"
                        :table-row-number="tableRowNumber"
                    ></fill-box>
                </div>

                <treatment-execute-nanning-footer
                    :print-data="form"
                    :config="config"
                >
                </treatment-execute-nanning-footer>

                <table
                    v-if="extra.isPreview && headerConfig.signPosition === signPosition.bottom"
                    class="treatment-sign-table treatment-sign-table__preview"
                >
                    <thead>
                        <tr>
                            <td
                                v-for="(item, index) in tableHeader"
                                :key="`treatment-footer-bottom-table-${index}`"
                                colspan="2"
                            >
                                {{ item }}
                            </td>
                        </tr>
                    </thead>
                    <tbody data-type="group">
                        <tr
                            v-for="item in (enableSignNumber ? 5 : 7)"
                            :key="`treatment-footer-bottom-table-tr-${item}`"
                            data-type="item"
                        >
                            <td
                                v-for="(item, index) in tableHeader"
                                :key="`treatment-footer-bottom-td-${index}`"
                                colspan="2"
                            ></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </template>

        <div
            class="next-page"
            data-type="next-page"
        >
            (接下页)
        </div>
        <div
            class="prev-page"
            data-type="prev-page"
        >
            (接上页)
        </div>
    </div>
</template>

<script>
    import FillBox from './components/infusion-execute/fill-box.vue';
    import { PrintBusinessKeyEnum } from "./constant/print-constant.js";
    import PageSizeMap, {Orientation} from "../share/page-size.js";
    import {calcTableRowNumber, formatTreatmentUnit} from "./common/utils.js";
    import {formatMoney} from "./common/utils.js";
    import {
        CLINIC_TYPE,
        GoodsTypeEnum,
        GoodsSubTypeEnum,
    } from "./common/constants.js";
    import {
        formatAcupoints,
        formatToothNos2Html,
        getChineseGroupItems,
        isNumber
    } from "./common/medical-transformat.js";
    import TreatmentHandler from "./data-handler/treatment-handler";
    import {
        ExternalPRUsageTypeEnum,
        UsageTypeOptions,
        TieFuSubOptions,
        ZhenCiSubOptions,
        AiJiuSubOptions,
        TieFuUsageSubTypeEnum,
        BaGuanSubOptions,
        TuiNaSubOptions,
    } from "./constant/external-constants.js";
    import NextIsBlank from "./components/next-is-blank/index.vue";

    import clone from "./common/clone.js";
    import TreatmentExecuteNanningHeader from './components/treatment/treatment-execute-nanning-header.vue';
    import TreatmentExecuteNanningFooter from './components/treatment/treatment-execute-nanning-footer.vue';

    export default {
        DataHandler: TreatmentHandler,
        name: "TreatmentExecuteNanning",
        components: {
            TreatmentExecuteNanningFooter,
            TreatmentExecuteNanningHeader,
            NextIsBlank,
            FillBox,
        },
        filters: {
            formatMoney
        },
        props: {
            renderData: {
                type: Object,
                default() {
                    return {};
                },
            },
            extra: {
                type: Object,
                default() {
                    return {}
                }
            },
            splitSize: {
                type: Object,
                default: () => ({}),
            },
            viewDistributePrintConfig: {
                type: Object,
                default() {
                    return {};
                }
            }
        },
        businessKey: PrintBusinessKeyEnum.TREATMENT_EXECUTE_NANNING,
        pages: [
            {
                paper: PageSizeMap.A4,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.A5,
                isRecommend: true,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.B6,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MM95_190,
                isRecommend: false,
                defaultOrientation: Orientation.portrait, // 默认方向
                onlyOrientation: Orientation.portrait,
                defaultHeightLevel: null,
            },
            {
                paper: PageSizeMap.MedicalNeedleMultiPaper,
                isRecommend: false,
                defaultOrientation: Orientation.portrait,
                defaultHeightLevel: '一等分' // 默认选择的等分纸
            },
        ],
        data() {
            return {
                ExternalPRUsageTypeEnum,
                TieFuUsageSubTypeEnum,
            }
        },
        computed: {
            splitHeight() {
                const splitHeight = parseFloat(this.splitSize.height);
                return isNaN(splitHeight) ? 0 : splitHeight;
            },
            /**
             * 小纸张打印时由于高度很小,表格行数过多导致分页失败
             * 所以小纸张打印时减少表格行数
             */
            tableRowNumber() {
                if (this.enableSignNumber) return 5;
                return calcTableRowNumber(this.splitHeight);
            },
            signPosition() {
                return {
                    bottom: 2,
                    right: 1,
                    none: 0,
                }
            },
            printData() {
                return this.renderData.printData || {};
            },
            filterPrintData() {
                const { productMaterialsPrice } = this.contentConfig;
                // 如果包含材料商品, 则直接返回
                if (productMaterialsPrice) return this.printData;

                // 如果不包含, 则过滤
                const cachePrintData = clone(this.printData);
                const { executeForms } = cachePrintData;
                (executeForms || []).forEach((form) => {
                    const { executeFormItems } = form;
                    (executeFormItems || []).forEach((item) => {
                        const cacheGroupItems = clone(item.groupItems || []);
                        item.groupItems = cacheGroupItems.filter((groupItem) => {
                            const { productInfo } = groupItem;
                            const { type } = productInfo || {};
                            return type !== GoodsTypeEnum.MATERIAL && type !== GoodsTypeEnum.GOODS;
                        });
                    });
                });
                return cachePrintData;
            },
            config() {
                if(this.renderData.config && this.renderData.config.medicalDocuments && this.renderData.config.medicalDocuments.treatment) {
                    return this.renderData.config.medicalDocuments.treatment;
                }
                return {};
            },
            organ() {
                return this.printData.organ || {};
            },
            organTitle() {
                if (!this.headerConfig.title) {
                    return this.organ && this.organ.medicalDocumentsTitle && this.organ.medicalDocumentsTitle.treatment || '';
                }
                return this.headerConfig.title;
            },
            headerConfig() {
                return this.config.header || {};
            },
            contentConfig() {
                return this.config.content || {};
            },
            // 是否指定签名行数为5行
            enableSignNumber() {
                return this.headerConfig.enableSignNumber;
            },
            // 是否拆分治疗理疗单
            isSplitTreatmentPhysiotherapy() {
                return !!this.contentConfig.treatmentPhysicalSeparate;
            },
            isPreview() {
                return this.extra.isPreview;
            },
            tableHeader() {
                return this.contentConfig.tableHeader || ['执行时间', '执行签名', '患者签名','执行时间', '执行签名', '患者签名' ];
            },
            nameColspan() {
                let colspan= 8;
                if(this.contentConfig.productUnitPrice) {
                    colspan--;
                }
                if(this.isGuangdongShenzhen) {
                    colspan--;
                }
                return colspan;
            },
            isGuangdongShenzhen() {
                return this.printData.isGuangdongShenzhen;
            },
            renderFormList() {
                const hasTreatmentGoods = this.checkHasSubTypeGoods(
                    this.filterPrintData,
                    GoodsTypeEnum.TREATMENT,
                    GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Treatment,
                );
                const hasPhysiotherapyGoods = this.checkHasSubTypeGoods(
                    this.filterPrintData,
                    GoodsTypeEnum.TREATMENT,
                    GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy,
                );

                // 配置不拆分或者只有治疗理疗的某一种，不用拆分
                // 在打印设置里预览的时候也不用拆分
                if(
                    this.isPreview ||
                    !this.isSplitTreatmentPhysiotherapy ||
                    !hasTreatmentGoods ||
                    !hasPhysiotherapyGoods
                ) {
                    return [
                        this.reComputeTotalPrice({
                            ...this.filterPrintData,
                            formItems: this.filterPrintData.executeForms.reduce((res, executeForm) => {
                                return res.concat(executeForm.executeFormItems);
                            }, []),
                            externalForms: this.filterPrintData.prescriptionExternalForms || [],
                        }),
                    ];
                }

                return [
                    this.splitFormByGoodsSubType(
                        this.filterPrintData,
                        GoodsTypeEnum.TREATMENT,
                        GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy,
                        0
                    ), // 治疗单
                    this.splitFormByGoodsSubType(
                        this.filterPrintData,
                        GoodsTypeEnum.TREATMENT,
                        GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy,
                        1
                    ), // 理疗单
                ]
            },
            isSupportPhysiotherapy() {
                return !!this.viewDistributePrintConfig.treatment?.isSupportPhysiotherapy;
            },
            executeFormNameColSpan() {
                if (this.contentConfig.productUnitPrice && this.contentConfig.productExecutedPrice && this.contentConfig.productTotalPrice) {
                    return 4;
                }
                if (
                    (!this.contentConfig.productUnitPrice && this.contentConfig.productExecutedPrice && this.contentConfig.productTotalPrice) ||
                    (this.contentConfig.productUnitPrice && !this.contentConfig.productExecutedPrice && this.contentConfig.productTotalPrice) ||
                    (this.contentConfig.productUnitPrice && this.contentConfig.productExecutedPrice && !this.contentConfig.productTotalPrice)
                ) {
                    return 6;
                }
                if (
                    (!this.contentConfig.productUnitPrice && !this.contentConfig.productExecutedPrice && this.contentConfig.productTotalPrice) ||
                    (!this.contentConfig.productUnitPrice && this.contentConfig.productExecutedPrice && !this.contentConfig.productTotalPrice) ||
                    (this.contentConfig.productUnitPrice && !this.contentConfig.productExecutedPrice && !this.contentConfig.productTotalPrice)
                ) {
                    return 8;
                }
                return 10;
            },
            executeFormBottomNameColSpan() {
                if (this.contentConfig.productUnitPrice && this.contentConfig.productExecutedPrice && this.contentConfig.productTotalPrice) {
                    return 9;
                }
                if (
                    (!this.contentConfig.productUnitPrice && this.contentConfig.productExecutedPrice && this.contentConfig.productTotalPrice) ||
                    (this.contentConfig.productUnitPrice && !this.contentConfig.productExecutedPrice && this.contentConfig.productTotalPrice) ||
                    (this.contentConfig.productUnitPrice && this.contentConfig.productExecutedPrice && !this.contentConfig.productTotalPrice)
                ) {
                    return 12;
                }
                if (
                    (!this.contentConfig.productUnitPrice && !this.contentConfig.productExecutedPrice && this.contentConfig.productTotalPrice) ||
                    (!this.contentConfig.productUnitPrice && this.contentConfig.productExecutedPrice && !this.contentConfig.productTotalPrice) ||
                    (this.contentConfig.productUnitPrice && !this.contentConfig.productExecutedPrice && !this.contentConfig.productTotalPrice)
                ) {
                    return 15;
                }
                return 18;
            },
        },
        methods: {
            formatTreatmentUnit,
            formatToothNos2Html,
            getChineseGroupItems,
            formatAcupoints,
            calcDosageCount(item) {
                if (item.acupointUnitCount && item.acupointUnit) {
                    return item.acupointUnitCount;
                }
                let count = 0;
                (item.acupoints || []).forEach((it) => {
                    if (it.name) {
                        if (isNumber(it.position)) {
                            count += parseFloat(it.position);
                        } else if (it.position === '双') {
                            count += 2;
                        } else if (it.position !== '-') {
                            count++;
                        }
                    }
                });
                return count || 1;
            },
            formatName(form, item) {
                // [贴敷-成品贴]
                const {usageType, usageSubType} = form;
                const {name} = item;

                if (!this.contentConfig.printExternalUsageType) {
                    return name;
                }

                const {label: typeStr} = UsageTypeOptions.find(it => it.value === usageType) || {};
                if (!typeStr) return name;
                let usageSubTypeOptions = [];
                switch (usageType) {
                    case ExternalPRUsageTypeEnum.tieFu:
                        usageSubTypeOptions = TieFuSubOptions;
                        break;
                    case ExternalPRUsageTypeEnum.zhenCi:
                        usageSubTypeOptions = ZhenCiSubOptions;
                        break;
                    case ExternalPRUsageTypeEnum.aiJiu:
                        usageSubTypeOptions = AiJiuSubOptions;
                        break;
                    case ExternalPRUsageTypeEnum.baGuan:
                        usageSubTypeOptions = BaGuanSubOptions;
                        break;
                    case ExternalPRUsageTypeEnum.tuiNa:
                        usageSubTypeOptions = TuiNaSubOptions;
                        break;
                }
                const {label: subTypeStr} = usageSubTypeOptions.find(it => it.value === usageSubType) || {};
                if (subTypeStr) {
                    return `${name} 【${subTypeStr}】`;
                }
                return `${name} 【${typeStr}】`;
            },

            externalFormRowSpan(item) {
                let rowspan = 2;
                const {externalGoodsItems, acupoints} = item || {};
                if (externalGoodsItems?.length> 0) {
                    rowspan += 1;
                }
                if (this.contentConfig.includeExternalAcupuncture && acupoints?.length > 0) {
                    rowspan += 1;
                }
                return rowspan;
            },
            goodsStr(externalGoodsItems) {
                let str = '';
                (externalGoodsItems || []).forEach((it, index) => {
                    str += `${it.name || it.medicineCadn}${it.unitCount}${it.unit}`;
                    if (index !== externalGoodsItems.length - 1) {
                        str += '、';
                    }
                });
                return str
            },
            isTreatment(type) {
                return type === GoodsTypeEnum.TREATMENT;
            },
            isCompose(type) {
                return type === GoodsTypeEnum.COMPOSE;
            },
            needDays(productInfo) {
                return this.isTreatment(productInfo?.type) || this.isCompose(productInfo?.type);
            },
            /**
             * 拆分治疗理疗的form
             * @param filterType 0 过滤下当前类型的商品 1 只保留当前类型商品
             */
            splitFormByGoodsSubType(
                form,
                type,
                subType,
                filterType
            ) {
                const curForm = clone(form);

                // 诊疗项目中的治疗理疗项目拆分
                for (const executeForm of curForm.executeForms) {
                    for (const executeFormItem of executeForm.executeFormItems) {
                        if(filterType === 1) {
                            executeFormItem.groupItems = executeFormItem.groupItems.filter(groupItem => {
                                return groupItem.productInfo?.type === type && groupItem.productInfo?.subType === subType;
                            })
                        } else {
                            executeFormItem.groupItems = executeFormItem.groupItems.filter(groupItem => {
                                return groupItem.productInfo?.type !== type || groupItem.productInfo?.subType !== subType;
                            })
                        }

                        if (this.contentConfig.compose !== 1 && executeFormItem.composeType === 1) { // 非打印套餐子项，为套餐，取套餐金额*次数
                            executeFormItem.totalPrice = executeFormItem.composeUnitPrice * executeFormItem.composeUnitCount;
                        } else {
                            executeFormItem.totalPrice = executeFormItem.groupItems.reduce((total, groupItem) => {
                                total += groupItem.totalPrice;
                                return total;
                            },0);
                        }
                    }

                    executeForm.executeFormItems = executeForm.executeFormItems.filter((executeFormItem) => {
                        return executeFormItem.groupItems.length !== 0;
                    })

                    executeForm.totalPrice = executeForm.executeFormItems.reduce((total, executeFormItem) => {
                        if (this.contentConfig.compose !== 1 && executeFormItem.composeType === 1) { // 非打印套餐子项，为套餐，金额为套餐金额相加
                            total += executeFormItem.composeUnitPrice * executeFormItem.composeUnitCount;
                        } else {
                            total += executeFormItem.totalPrice;
                        }
                        return total;
                    },0);
                }

                // 外治处方拆分
                for (const prescriptionExternalForm of curForm.prescriptionExternalForms) {
                    if (filterType === 1) {
                        prescriptionExternalForm.prescriptionFormItems =
                            prescriptionExternalForm.prescriptionFormItems.filter((formItem) => {
                                return formItem.type === type && formItem.subType === subType;
                            })
                    } else {
                        prescriptionExternalForm.prescriptionFormItems =
                            prescriptionExternalForm.prescriptionFormItems.filter((formItem) => {
                                return formItem.type !== type || formItem.subType !== subType;
                            })
                    }

                    prescriptionExternalForm.totalPrice = prescriptionExternalForm.prescriptionFormItems.reduce((total, formItem) => {
                        total += formItem.totalPrice;
                        return total;
                    },0);
                }

                curForm.executeForms = curForm.executeForms.filter((executeForm) => {
                    return executeForm.executeFormItems.length !== 0;
                })

                // 整个单子的总价 = 诊疗项目的总价 + 外治处方的总价
                curForm.totalPrice = curForm.executeForms.reduce((total, executeForm) => {
                    total += executeForm.totalPrice;
                    return total;
                },0) + curForm.prescriptionExternalForms.reduce((total, executeForm) => {
                    total += executeForm.totalPrice;
                    return total;
                },0);

                // 扁平化诊疗项目
                curForm.formItems = curForm.executeForms.reduce((res, executeForm) => {
                    return res.concat(executeForm.executeFormItems);
                }, []);
                curForm.externalForms = curForm.prescriptionExternalForms || [];
                console.log(curForm, '拆分的form');

                return curForm;
            },

            /**
             * 检查单子中是否存在某种类型的商品
             */
            checkHasSubTypeGoods(form, type, subType) {
                let flag = false;

                for (const executeForm of form.executeForms) {
                    for (const executeFormItem of executeForm.executeFormItems) {
                        flag = executeFormItem.groupItems.some((groupItem) => {
                            return groupItem.productInfo?.type === type && groupItem.productInfo?.subType === subType;
                        })
                        if(flag) {
                            return true;
                        }
                    }
                }

                return flag;
            },

            // 获取当前单子的标题
            getCurrentSheetTitle(form) {
                if (!this.isSupportPhysiotherapy) {
                    return '治疗单';
                }

                if(this.organ.hisType === CLINIC_TYPE.DENTISTRY) {
                    return '治疗单';
                }

                if(!this.isSplitTreatmentPhysiotherapy || this.isPreview) {
                    return '治疗理疗单';
                }

                const hasTreatmentGoods = this.checkHasSubTypeGoods(
                    form,
                    GoodsTypeEnum.TREATMENT,
                    GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Treatment,
                );

                const hasPhysiotherapyGoods = this.checkHasSubTypeGoods(
                    form,
                    GoodsTypeEnum.TREATMENT,
                    GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy,
                );

                if(hasTreatmentGoods) {
                    return '治疗单';
                }

                if(hasPhysiotherapyGoods) {
                    return '理疗单';
                }

                return '治疗单';
            },

            // 重新计算总价：修复打印选项更改后，项目发生变化，总价未修改的bug
            reComputeTotalPrice(curForm) {
                // 诊疗项目
                for (const executeForm of curForm.executeForms) {
                    for (const executeFormItem of executeForm.executeFormItems) {
                        if (this.contentConfig.compose !== 1 && executeFormItem.composeType === 1) { // 非打印套餐子项，为套餐，取套餐金额*次数
                            executeFormItem.totalPrice = executeFormItem.composeUnitPrice * executeFormItem.composeUnitCount;
                        } else {
                            executeFormItem.totalPrice = executeFormItem.groupItems.reduce((total, groupItem) => {
                                total += groupItem.totalPrice;
                                return total;
                            },0);
                        }
                    }

                    executeForm.executeFormItems = executeForm.executeFormItems.filter((executeFormItem) => {
                        return executeFormItem.groupItems.length !== 0;
                    })

                    executeForm.totalPrice = executeForm.executeFormItems.reduce((total, executeFormItem) => {
                        if (this.contentConfig.compose !== 1 && executeFormItem.composeType === 1) { // 非打印套餐子项，为套餐，金额为套餐金额相加
                            total += executeFormItem.composeUnitPrice * executeFormItem.composeUnitCount;
                        } else {
                            total += executeFormItem.totalPrice;
                        }
                        return total;
                    },0);
                }

                // 外治处方
                for (const prescriptionExternalForm of curForm.prescriptionExternalForms) {
                    prescriptionExternalForm.totalPrice = prescriptionExternalForm.prescriptionFormItems.reduce((total, formItem) => {
                        total += formItem.totalPrice;
                        return total;
                    },0);
                }

                curForm.executeForms = curForm.executeForms.filter((executeForm) => {
                    return executeForm.executeFormItems.length !== 0;
                })

                // 整个单子的总价 = 诊疗项目的总价 + 外治处方的总价
                curForm.totalPrice = curForm.executeForms.reduce((total, executeForm) => {
                    total += executeForm.totalPrice;
                    return total;
                },0) + curForm.prescriptionExternalForms.reduce((total, executeForm) => {
                    total += executeForm.totalPrice;
                    return total;
                },0);

                return curForm;
            },
            isBasedOnAcupoint(form) {
                return [
                    ExternalPRUsageTypeEnum.tieFu,
                    ExternalPRUsageTypeEnum.zhenCi,
                    ExternalPRUsageTypeEnum.aiJiu,
                ].includes(form.usageType);
            },
        }
    }
</script>

<style lang="scss">
@import "./style/reset.scss";
@import "./components/layout/print-layout.scss";

.abc-page-content {
    box-sizing: border-box;
    padding: 8pt;
    font-family: "Microsoft YaHei", "微软雅黑";
}

.print-treatment-table {
    width: 100%;
    margin-top: 6pt;
    font-size: 10pt;
    line-height: 12pt;
    table-layout: fixed;

    .has-remark-tr {
        td {
            padding-bottom: 2pt;
            border-bottom: none;

            &.border-td {
                border: 1px solid #000000;
            }
        }
    }

    td,
    th {
        padding: 3pt 6pt;
        overflow: hidden;
        word-break: keep-all;
        white-space: nowrap;
        vertical-align: top;
        border: 1px solid #000000;

        &.remark-td {
            padding-top: 0;
            word-break: break-all;
            border-top: none;
        }

        &.unit-td {
            padding-right: 2pt;
            padding-left: 0;
        }

        .line-remark {
            margin-left: 4pt;
            font-size: 9pt;
        }
    }

    th {
        font-family: "Microsoft YaHei", "微软雅黑";
    }

    .no-border {
        border: none;
    }

    .no-left-border {
        border-left: none;
    }

    .no-right-border {
        border-right: none;
    }

    .no-bottom-border {
        border-bottom: none;
    }

    .text-center {
        text-align: center;
    }

    .text-right {
        text-align: right;
    }

    .remark-text {
        font-size: 9pt;
    }

    td.border-td {
        border: 1px solid #000000;
    }

    .treatment-execute-form-tr {
        td:nth-last-of-type(2) {
            padding-right: 6px !important;
        }

        th:nth-last-of-type(2) {
            padding-right: 6px !important;
        }
    }
}

.text-right {
    text-align: right;
}

.remark-text {
    font-size: 9pt;
}

.treatment-sign-table {
    width: 100%;
    font-size: 10pt;
    text-align: center;
    table-layout: fixed;

    td {
        height: 20pt;
        border: 1px solid #000000;
    }
}

.treatment-sign-table__preview {
    position: absolute;
    top: -6pt;
    left: 0;
    transform: translateY(-100%);
}

.global-tooth-selected-quadrant {
    position: relative;
    display: inline-flex;
    flex-direction: column;
    width: auto;
    min-width: 32pt;
    height: 17pt;
    vertical-align: middle;

    .top-tooth,
    .bottom-tooth {
        display: flex;
        align-items: center;
        width: 100%;
        min-width: 32pt;
    }

    .left-tooth,
    .right-tooth {
        display: flex;
        align-items: center;
        width: 50%;
        height: 7pt;
        padding: 0 1pt;
        font-family: 'MyKarlaRegular';
        font-size: 9pt;
        letter-spacing: 1px;
        user-select: none;
    }

    .left-tooth {
        justify-content: flex-end;
        border-right: 1pt solid #7a8794;
    }

    .top-tooth {
        min-width: 32pt;
        border-bottom: 1pt solid #7a8794;

        > div {
            padding-bottom: 1px;
        }
    }

    .bottom-tooth {
        > div {
            padding-top: 1px;
        }
    }

    &.all-tooth {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }

    .all-tooth {
        min-width: 18pt;
        height: 11pt;
        font-size: 9pt;
        line-height: 11pt;
    }

    &.no-data {
        .left-tooth {
            border-right: 1px dashed #000000;
        }

        .top-tooth {
            border-bottom: 1px dashed #000000;
        }
    }
}

.next-page {
    position: relative;
    font-size: 8pt;
    font-weight: lighter;
    text-align: center;
}

.prev-page {
    font-size: 8pt;
    font-weight: lighter;
}
</style>
