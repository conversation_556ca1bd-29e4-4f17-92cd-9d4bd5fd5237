import { getTimeStamp } from "./utils";

export default class Logger {
    static debug(message: string) {
        console.log(`%c AbcPrint %c ${getTimeStamp()} %c${message}`, 'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff', "color: #aaa", "color: #999999")
    }

    static log(message: string) {
        console.log(`%c AbcPrint %c ${getTimeStamp()} %c${message}`, 'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff', "color: #aaa", "color: #41b883")
    }

    static warn(message: string) {
        console.log(`%c AbcPrint %c ${getTimeStamp()} %c${message}`, 'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff', "color: #aaa", "color: orange")
    }

    static error(message: string) {
        console.log(`%c AbcPrint %c ${getTimeStamp()} %c${message}`, 'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff', "color: #aaa", "color: red")
    }
}
