import { ElementType } from "./spliter";
import { insertAfter } from "./utils";

const ZeroWidthSpace = '\u200b';

export function splitLine(p: HTMLElement, pure = false) {
    try {
        // 图片不是文本不会参与后面的位置计算
        // 但是如果在图片后面和前面插入一个零宽字符那么也就相当于标识了图片的位置
        // 因为零宽字符的位置和图片必在一行 因为这个文档是底部对其的也就标识了图片的位置
        const images = p.querySelectorAll('img');
        if(images) {
            images.forEach(img => {
                img.parentNode?.insertBefore(document.createTextNode(ZeroWidthSpace), img);
                insertAfter(document.createTextNode(ZeroWidthSpace), img);
            });
        }

        // 遍历文本节点
        const tree = document.createTreeWalker(p, NodeFilter.SHOW_TEXT);
        let node = tree.nextNode();
        let textNodes = [];
        while (node) {
            textNodes.push(node);
            node = tree.nextNode();
        }

        // 将文本拆分
        // 假设现在有一段很长的文本<span>这是一段很长的文本</span>
        // span的最大宽度为2个字
        // 浏览器会按照此规则做以下的排列
        // line1 这是
        // line2 一段
        // line3 很长
        // line4 的文
        // line5 本
        // 以上为浏览器的布局规则
        // 由于浏览器的限制 我们很难通过span上的任何方法获取到line1 - lineN的位置
        // 但是如果我们将<span>这是一段很长的文本</span>拆成如下结构
        // 这 是 一 段 很 长 的 文 本
        // 也就是9个字的话就可以通过 textNode('这').getBoundingClientRect()获取到他的位置
        // 且拆成单个textNode也不会影响原有的布局
        // 想象一下在一个富文本段落里面 我们可以拿到每一个字的位置的话就可以做很多事情
        // 以下函数将textNode拆成单个textNode
        textNodes.forEach((textNode) => {
            if(textNode.textContent) {
                for (let i = textNode.textContent.length - 1; i >= 0; i--) {
                    // @ts-ignore
                    textNode.splitText(i);
                }
            }
        });

        const positionBottoms = [];
        const textTree = document.createTreeWalker(p, NodeFilter.SHOW_TEXT);
        const pTop = p.getBoundingClientRect().top;
        let textNode = textTree.nextNode();

        while (textNode) {
            const range = document.createRange();
            range.selectNode(textNode);
            const position = range.getClientRects();
            // 过滤没有节点信息的元素
            if (position && position.length) {
                // 这里底部位置加1是为了给文本的border留位置
                // 对整体的切割不会有影响
                positionBottoms.push(position[0].bottom - pTop + 1);
            }
            textNode = textTree.nextNode();
        }

        // 将位置去重并排序
        const uniqSplitBottoms = [...new Set(positionBottoms)].sort((a, b) => a - b);

        // 从上向下收敛
        const splitBottom:number[] = [];
        // 收敛的边界
        // 即两根线差距小于12px则需要合并
        const mergeTriggerOffset = 12;

        for(let i = uniqSplitBottoms.length - 1; i >=0; i --) {
            const currentBottom = uniqSplitBottoms[i];
            const preBottom = uniqSplitBottoms[i + 4];

            // 最底部直接push
            if(!preBottom) {
                splitBottom.unshift(currentBottom);
                continue
            }
            if(preBottom - currentBottom > mergeTriggerOffset) {
                splitBottom.unshift(currentBottom);
            }
        }

        let slices = [];
        let prev = 0;

        for (let i = 0; i < splitBottom.length; i++) {
            const current = splitBottom[i];
            // 此处不要对html进行换行
            // 富文本编辑器设置了white-space: break-spaces;
            // 会导致换行出现问题
            const sliceHTML = `<div class="---viewport" style="height: ${current - prev}px; overflow: hidden;display: block;"><div class="---offset-content" style="transform: translateY(-${prev}px)">${(p.cloneNode(true) as HTMLElement).outerHTML}</div></div>`;
            if(pure) {
                slices.push(`<div class="line">${ sliceHTML }</div>`);
            } else {
                slices.push(`<div class="line" data-type="${ElementType.ITEM}">${ sliceHTML }</div>`);
            }
            prev = current;
        }

        if(pure) {
            return slices.join('');
        }

        return `<div data-type="${ElementType.GROUP}">${slices.join('')}</div>`;
    } catch (e) {
        console.error(e);
        return '';
    }
}
