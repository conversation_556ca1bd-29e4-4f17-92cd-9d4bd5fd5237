import { VueConstructor } from "vue";
import PageSize, { IPageSize, Orientation } from '../share/page-size';
import Logger from './logger';
import {
    CustomStyles,
    PAGE_CLASSNAME,
    PAGE_CLASSNAME_PREVIEW,
    PAGE_CLASSNAME_PRINT,
    PAGE_CONTENT_CLASSNAME,
    PAGE_WRAPPER_CLASSNAME,
    Spliter,
} from './spliter';
import {
    clone,
    generateUUID,
    getImageTransformParams,
    image2Base64,
    isArray,
    isEqual,
    isObject,
    isString,
    loadScript,
    loadStyle,
    strToMM,
} from "./utils";

// @ts-ignore
import extractStack from "extract-stack";
// @ts-ignore
import Abc<PERSON>rowth<PERSON>hart from 'abc-growth-chart';
// @ts-ignore
import PrintForm from 'abc-form-engine/lib/print';
import VueI18n from 'vue-i18n';
import recommendOffsetMatcher from "../share/printer-recommend-offset";
import i18nLangConfig, { DEFAULT_I18N_LOCALE } from './i18n';
import PrintLogger, { ErrorLogMetaData } from "./print-logger";
import JsBarcode from "jsbarcode";

const HTTP_REGEXP = /(http|https):\/\/.+/g

const MEDICINE_USAGE = Object.freeze({
    // 口服
    oral: ['口服', '含服', '嚼服', '晨服', '餐前服', '餐中服', '餐后服', '睡前服'],
    // 注射
    injections: ['静脉注射', '肌内注射', '腔内注射', '皮下注射', '皮内注射', '穴位注射', '局部注射', '局部麻醉', '超声透药', '静脉泵入', '椎管内注射'],
    // 外用
    external: ['溶媒用', '外用', '滴眼', '滴鼻', '滴耳', '口腔喷入', '吸入', '鼻腔喷入', '含漱', '涂抹', '塞肛', '直肠给药', '阴道给药', '膀胱冲洗', '灌肠'],
    // 输液
    infusion: ['静脉滴注', '直肠滴注', '入壶静滴', '输液冲管', '鼻饲', '膀胱给药', '封管'],
    // 雾化
    atomization: ['雾化吸入'],
})

const bodyStartSpliter = '<body>'
const bodyEndSpliter = '</body>'
const docType = `<!DOCTYPE html>`

type SplitOptions = {
    keepPageWidth?: boolean,
}


interface ITemplateItem {
    css: string,
    hackCss: string,
    file: string,
    url: string,
    windowRegisteredName: string,
    pages: ITemplatePage[],
    exampleData?: OriginData,
    imageTransformSetting?: string
}

declare global {
    interface Window {
        AbcPackages?: {
            AbcPrint?: {
                $mount?: Record<string, string>
            }
        },
        AbcPrintDisableErrorLog?: boolean
    }
}

type matchTemplateCallback = (options: IOptions) => ITemplateItem

interface ITemplate {
    group: string,
    businessKey: String,
    pages: ITemplatePage[],
    templates: ITemplateItem[],
}

interface IReportLogData {
    keyId: string,
    scene: string,
    data?: any,
}

interface SplitSize {
    height: string,
    width: string
}

interface IPrintPage {
    size: string,
    orientation: String,
    pageHeightLevel: String,
    customStyles?: CustomStyles,
    pageSizeReduce?: IPageSizeReduce,
    height: string,
    width: string,
}

interface IOptions {
    template: ITemplate,
    page: IPrintPage,
    runtime: VueConstructor,
    originData?: OriginData,
    extra?: Record<string, any>,
    matchTemplateCallback?: matchTemplateCallback

    reportLogData?: IReportLogData,

    needReportLog?: Boolean,

    printLogger: any,

    isRunInH5?: boolean, // 是否运行在h5页面中，需要对 ios 兼容 iframe srcdoc 属性无效的问题
}

type GlobalConfig = Record<string, any>
type OriginData = Record<string, any>
type ViewDistributePrintConfig = Record<string, any>
type Component = VueConstructor & {
    DataHandler?: DataHandler;
    businessKey?: string;
    getInjectGlobalStyle?: Function;
}

interface DataHandlerOptions {
    originData: OriginData;
    globalConfig: GlobalConfig;
}

interface IPageSizeReduce {
    // mm
    top: number,
    bottom: number,
    right: number,
    left: number,
}

interface Ipage extends IPageSize {
    pageSizeReduce?: IPageSizeReduce,
}


interface ITemplatePage {
    paper: Ipage,
    isRecommend: boolean,
    onlyOrientation?: Orientation,
    defaultOrientation?: Orientation,
    defaultHeightLevel?: string | null,
}

interface DataHandler {
    originData: OriginData;
    globalConfig: GlobalConfig;
    preProcessData: any;
    preProcessConfig: any;
    renderConfig: any;
    renderData: any;

    new(options: DataHandlerOptions): DataHandler;

    preProcessHandlers(): void;

    preProcessConfigHandlers(): void;

    renderDataHandlers(): void;

    getStaticRenderConfig(): void;

    setRenderConfig(renderConfig: any): void;
}

interface globalConfigOptions {
    locale: string,
    messages: object,
}

interface PrintLogData {
    options: IOptions;
    _globalConfig: GlobalConfig
}

interface CustomInitOption {
    printConfig: GlobalConfig;
}

interface CreateAbcPageSizeParams {
        key: string,
        name: string,
        width: string,
        height: string,
        heightLevels: any[] | null,
        pageSizeReduce: IPageSizeReduce | undefined,
        orientations: Orientation[],
}

export default class AbcPrint {

    // @deprecated
    public static errorHandler = function (e: Error, extractStack: string, file: File) {
        console.error(e)
        console.error(extractStack)
        console.log(file)
    }

    public static printLogData = {} as PrintLogData

    // AbcPrint Url
    private static url = __ABCPRINT_URL__

    // 默认的vue运行时
    private static vue = __VUE_URL__

    // 全局打印配置
    private static globalConfig = {} as GlobalConfig
    // 基于产品线分发的打印配置，由PC直接提供
    private static viewDistributePrintConfig = {} as ViewDistributePrintConfig

    // v2
    private static isEnablePrintV2 = false

    private _globalConfig = {} as GlobalConfig

    // @deprecated
    // 分页器
    public static Spliter = Spliter

    // 打印机匹配器
    public static recommendOffsetMatcher = recommendOffsetMatcher

    public static AbcPageSizeMap = PageSize

    public static createAbcPageSize = (params: CreateAbcPageSizeParams) => {
        Logger.debug(JSON.stringify(params));
        return {
            key: params.key,
            name: params.name,
            width: params.width,
            height: params.height,
            heightLevels: params.heightLevels,
            pageSizeReduce: params.pageSizeReduce,
            orientations: params.orientations,
        }
    }

    public static createPageDocument(self: AbcPrint, html:string) {
        const customStyles = self.options.page.customStyles || {};
        const customStyleStr = Object.keys(customStyles).reduce((prev, key) => {
            const styleKey = key.replace(/([A-Z])/g, "-$1").toLowerCase();
            if(!customStyles[key]) {
                return prev;
            }
            return prev + `${styleKey}: ${customStyles[key]};`
        }, '');
        return `
            <div class="${PAGE_CLASSNAME}" style="height: ${self.splitSize.height};width: ${self.splitSize.width};position: relative;${customStyleStr}" data-pagesize="${self.options.page.size}">
                <div class="${PAGE_CONTENT_CLASSNAME}" data-size="${self.options.page.size}" style="position: relative;">
                    ${html}
                </div>
            </div>
        `
    }

    public static createPrintDocument(self: AbcPrint) {
        let template = `
               <html lang="en">
                <head>
                <meta charset="UTF-8">
                <meta http-equiv="X-UA-Compatible" content="IE=edge">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${self.matchedTemplateItem.windowRegisteredName}</title>
                <style type="text/css">
                    * {
                        margin: 0;
                        padding: 0;
                    }
                    .${PAGE_CLASSNAME} {
                        overflow: hidden;
                        box-sizing: border-box;
                        background: #fff;
                        ${(self.options.page.pageSizeReduce?.left ?? '') === (self.options.page.pageSizeReduce?.right ?? '') ? 'margin: 0 auto;' : ''}
                    }
                    @page {
                        margin: 0;
                    }
                    .abc-page-content {
                        position: relative;
                    }
                    div[data-pagesize="A4"] {
                        background: #fff;
                    }
                    ${self.injectGlobalStyle}
                </style>
                <style type="text/css">
                    ${self.matchedTemplateItem.css}
                </style>
                <style type="text/css">
                    ${self.matchedTemplateItem.hackCss}
                </style>
                </head>
                <body>
                    
                </body>
            </html>
        `

        return {
            getHTML: () => {
                return template
            },
            append: (html: string) => {
                const bodyEndIndex = template.indexOf(bodyEndSpliter);
                template = template.slice(0, bodyEndIndex) + html + template.slice(bodyEndIndex);
            },
            appendStyle: (styleStr: string) => {
                const styleEndIndex = template.indexOf('</head>');
                template = template.slice(0, styleEndIndex) + styleStr + template.slice(styleEndIndex);
            },
        }
    }

    public static addCustomPageSize = (pageSize: IPageSize) => {
        Logger.debug(JSON.stringify(pageSize));
        // @ts-ignore
        PageSize[pageSize.key] = pageSize;
    }

    // runtime
    private static runtime: VueConstructor

    // 页面方向
    public static Orientation = Orientation

    private static errorLogMetaData: ErrorLogMetaData

    public static splitAbcPageHTML = (html?: string): Array<string> => {
        if (!html) {
            return []
        }
        const isSplitPreview = html.includes(`<div class="${PAGE_CLASSNAME} ${PAGE_CLASSNAME_PREVIEW}"`)
        const pageSpliter = `<div class="${PAGE_CLASSNAME} ${isSplitPreview ? PAGE_CLASSNAME_PREVIEW : PAGE_CLASSNAME_PRINT}"`
        const bodyStartIndex = html.indexOf(bodyStartSpliter)
        const bodyEndIndex = html.indexOf(bodyEndSpliter)
        const bodyPrevHTML = html.slice(0, bodyStartIndex)
        const bodyNextHTML = html.slice(bodyEndIndex + bodyEndSpliter.length, html.length)
        const abcPageHTML = html.slice(bodyStartIndex + bodyStartSpliter.length, bodyEndIndex)
        const htmls = abcPageHTML.split(pageSpliter).filter(it => it.includes('div')).map(it => docType + bodyPrevHTML + bodyStartSpliter + pageSpliter + it + bodyEndSpliter + bodyNextHTML)
        // @ts-ignore
        window.AbcPackages?.AbcPrint?._HTMLs = htmls;
        return htmls;
    }

    public static mergeAbcPageHTML = (htmls: Array<string>): string => {
        if(!htmls.length) {
            return ''
        }
        const bodyStartIndex = htmls[0].indexOf(bodyStartSpliter)
        const bodyEndIndex = htmls[0].indexOf(bodyEndSpliter)
        const bodyPrevHTML = htmls[0].slice(0, bodyStartIndex)
        const bodyNextHTML = htmls[0].slice(bodyEndIndex + bodyEndSpliter.length, htmls[0].length)
        const abcPageHTML = htmls.map(it => {
            const bodyStartIndex = it.indexOf(bodyStartSpliter)
            const bodyEndIndex = it.indexOf(bodyEndSpliter)
            return it.slice(bodyStartIndex + bodyStartSpliter.length, bodyEndIndex)
        }).join('')
        return bodyPrevHTML + bodyStartSpliter + abcPageHTML + bodyEndSpliter + bodyNextHTML
    }

    public static addWrapperToAbcPageHTML = (html: string): string => {
        const bodyEndSpliter = '</body>'
        const PAGE_REGEXP = new RegExp(`<div class="${PAGE_CLASSNAME} [^>]+[\\s\\S]*?>`, 'g')
        let isAddWrapperPrevHTML = false

        const pageHTML = html.replace(PAGE_REGEXP, (match) => {
            try {
                return `<div class="${PAGE_WRAPPER_CLASSNAME}">${match}`
            } catch (e) {
                console.error(e)
                return match
            }
        })
        if (!isAddWrapperPrevHTML) {
            return pageHTML
        }
        // body结束位置前面增加wrapper结尾
        const bodyEndIndex = pageHTML.indexOf(bodyEndSpliter)
        return pageHTML.slice(0, bodyEndIndex) + '</div>' + pageHTML.slice(bodyEndIndex)
    }

    private options: IOptions

    // 模板组件
    private component!: Component

    // 模板实例
    private instance: Vue | undefined

    // 原始打印数据
    private originData = {} as OriginData

    // 数据处理器
    private dataHandler: DataHandler | undefined

    // 匹配到的模板
    private matchedTemplateItem!: ITemplateItem

    // 实际分页时的宽高
    private splitSize = {} as SplitSize

    // 注入的全局的样式
    private injectGlobalStyle: String = ''

    private firstLoad: boolean

    private printLogger: PrintLogger

    private static i18n = {
        locale: DEFAULT_I18N_LOCALE,
        messages: {},
    }

    private static injectConfig = {} as GlobalConfig

    private i18nInstance: VueI18n | undefined;

    constructor(options: IOptions) {
        if (!isObject(options)) {
            throw Error('options must be an object')
        }
        console.debug(options, 'options')
        this.options = options
        AbcPrint.printLogData.options = options
        this.firstLoad = true;
        this.printLogger = new PrintLogger();
        // 通过npm引用barcode, 将其挂载在window上, 避免每个template都被打包一份barcode代码
        if (!window.JsBarcode) {
            window.JsBarcode = JsBarcode;
        }
    }

    static setErrorLogMetaData(errorLogMetaData: ErrorLogMetaData) {
        if (errorLogMetaData) {
            AbcPrint.errorLogMetaData = errorLogMetaData;
        } else {
            Logger.warn('未提供错误上报元数据')
        }
        Logger.debug('errorLogMetaData已设置')
    }

    static setGlobalConfig(params: {
        globalConfig: GlobalConfig;
        options: globalConfigOptions;
        viewDistributePrintConfig: ViewDistributePrintConfig;
        injectConfig: GlobalConfig;
    }) {
        const {
            globalConfig,
            options,
            viewDistributePrintConfig,
            injectConfig,
        } = params
        if (globalConfig) {
            AbcPrint.globalConfig = globalConfig
        } else {
            Logger.warn('未提供自定义全局配置、使用默认配置')
        }
        if (options) {
            AbcPrint.i18n.locale = options.locale ?? DEFAULT_I18N_LOCALE;
            AbcPrint.i18n.messages = options.messages ?? {
                'zh-CN': i18nLangConfig.zhCN,
                'zh-MO': i18nLangConfig.zhMO,
            };
            Logger.debug('i18n已设置')
        }
        if (viewDistributePrintConfig) {
            AbcPrint.viewDistributePrintConfig = viewDistributePrintConfig;
            Logger.debug('viewDistributePrintConfig已设置')
        }
        if (injectConfig) {
            AbcPrint.injectConfig = injectConfig;
            Logger.debug('injectConfig已设置')
        }
        Logger.debug(`globalConfig已设置`)
    }

    static setEnablePrintV2(isEnablePrintV2: boolean) {
        AbcPrint.isEnablePrintV2 = isEnablePrintV2
    }

    async init(loadInstance: boolean = true, customOptions: CustomInitOption) {
        try {
            await this.loadRuntime()
            this.initPrintLogger();
            await this.matchTemplate()
            await this.updateSplitSize()
            await this.loadTemplate()
            this.loadI18nInstance();
            await this.loadOriginData()
            await this.transferNetworkImage()
            await this.updateInstanceGlobalConfig()
            await this.preHandleProps()
            await this.loadDataHandler(customOptions)
            this.loadGlobalStyle()
            // TODO 需要优化
            loadInstance && await this.loadInstance()
        } catch (e) {
            this.printLogger._send('abcPrint.init', e);
        }
    }

    update(options: IOptions) {
        // 日志上报
        if(this.options.needReportLog && this.options.reportLogData) {
            this.printLogger._send(this.options.reportLogData.scene, {
                keyId: this.options.reportLogData?.keyId,
                info: 'pc调用update触发配置更新',
                pcOptionsPage: JSON.stringify(options.page),
                originData: JSON.stringify(this.options.originData),
                page: JSON.stringify(this.options.page),
            })
        }

        this.options = options
        AbcPrint.printLogData.options = options
        return this
    }

    async transferNetworkImage() {
        const { isEnableTransferNetworkImage = true } = this.options.extra ?? {};
        if(!isEnableTransferNetworkImage) {
            return Logger.debug(`isEnableTransferNetworkImage = ${isEnableTransferNetworkImage}，不转换网络图片`)
        }
        if(AbcPrint.isEnablePrintV2) {
            return Logger.debug(`isEnablePrintV2 = ${AbcPrint.isEnablePrintV2}，不转换网络图片`)
        }
        let promiseAll: Promise<void>[] = []
        Logger.debug('转换图片为base64')
        const loop = (obj: any, key: any, parent: any) => {
            if (isObject(obj)) {
                for (const key in obj) {
                    const value = obj[key]
                    loop(value, key, obj)
                }
            }
            if (isArray(obj)) {
                for (let i = 0; i < obj.length; i++) {
                    loop(obj[i], i, obj)
                }
            }
            // 挂号标签的图片不需要装换
            const isRegistrationTag = this.options?.template?.businessKey === 'registration-tag'
            if (isString(obj) && HTTP_REGEXP.test(obj) && !isRegistrationTag) {
                if (parent && key && obj.includes('aliyun')) {
                    function generatorImagePromise(src: string, key: string): Promise<void> {
                        return new Promise((resolve) => {
                            const img = new Image();
                            img.src = src
                            img.setAttribute("crossOrigin", 'Anonymous');
                            img.onload = function () {
                                try {
                                    parent[key] = image2Base64(img)
                                    resolve();
                                } catch (e) {
                                    Logger.error(`图片转换失败: ${key}: ${src}`)
                                    // 继续执行
                                    resolve();
                                }
                            }
                            img.onerror = function () {
                                Logger.error(`图片转换失败: ${key}: ${src}`)
                                // 继续执行
                                resolve();
                            }
                        })
                    }
                    const sourceSrc = obj;
                    // 普通转换
                    const templateKey = this.options?.template?.businessKey as string;
                    // 自定义图片转换格式
                    const imgTransformParams = getImageTransformParams(templateKey);
                    // 是否已经有处理参数， 有的话不做处理
                    const isExistenceParams = sourceSrc.indexOf('x-oss-process=image')
                    const imgSrc = isExistenceParams === -1 ? `${sourceSrc}${sourceSrc.endsWith('?') ? '' : '?'}${imgTransformParams || 'x-oss-process=image/resize,w_168,h_64/sharpen,399'}` : sourceSrc
                    promiseAll.push(generatorImagePromise(imgSrc, key));

                    // 32高度
                    if(!imgTransformParams) {
                        const imgSrc32 = isExistenceParams === -1 ? `${sourceSrc}${sourceSrc.endsWith('?') ? '' : '?'}x-oss-process=image/resize,h_32/sharpen,399` : sourceSrc
                        const key32 = `${key}32`;
                        promiseAll.push(generatorImagePromise(imgSrc32, key32));
                    }
                }
            }
        }
        loop(this.originData, null, this.originData)
        return Promise.all(promiseAll)
    }

    async matchTemplate() {
        // @ts-ignore
        this.matchedTemplateItem = null;
        if (this.options.matchTemplateCallback) {
            Logger.warn('使用自定义模板匹配')
            const matchedTemplate = this.options.matchTemplateCallback(this.options)
            if (matchedTemplate) {
                this.matchedTemplateItem = clone(matchedTemplate)
                return;
            }
        }
        const pageSize = this.options.page.size; // 传入的纸张大小

        // 日志上报
        if(this.options.needReportLog && this.options.reportLogData) {
            this.printLogger._send(this.options.reportLogData.scene, {
                keyId: this.options.reportLogData?.keyId,
                info: 'pc传入的page信息',
                originData: JSON.stringify(this.options.originData),
                page: JSON.stringify(this.options.page),
            })
        }

        const pageOrientation = Number(this.options.page.orientation); // 传入的纸张方向
        const heightLevel = this.options.page.pageHeightLevel; // 传入的纸张方向
        const templates = this.options.template.templates; // 模板列表
        let matchedTemplateItems: ITemplateItem[] = []

        if (!templates.length) {
            throw Error('模板不能为空')
        }
        matchedTemplateItems = clone(templates);

        // 过滤传入的纸张
        matchedTemplateItems = matchedTemplateItems.filter(it => {
            let pages: ITemplatePage[] = [];
            pages = it.pages.filter(page => {
                return page.paper.key === pageSize || page.paper.name === pageSize;
            })
            console.log('纸张问题', pages);
            it.pages = pages;

            // 过滤纸张方向
            pages = it.pages.filter(page => {
                if (page.onlyOrientation) {
                    return page.onlyOrientation === pageOrientation;
                }
                return page.defaultOrientation === pageOrientation;
            })
            it.pages = pages;
            console.log('纸张方向', pageOrientation, pages);
            // 过滤等分纸
            pages = it.pages.filter(page => {
                // 存在几等分
                if (page.paper.heightLevels) {
                    page.paper.heightLevels = page.paper.heightLevels.filter(item => {
                        return item.name === heightLevel;
                    });
                    return page.paper.heightLevels.length;
                }
                return page;
            })
            it.pages = pages;
            console.log('等分纸张', pages)
            return it.pages.length;
        });

        if (matchedTemplateItems.length) {
            this.matchedTemplateItem = matchedTemplateItems[0];
            console.log('匹配到的模板', this.matchedTemplateItem)
            return;
        }

        if (!this.matchedTemplateItem) {
            console.warn('未匹配到模板，开始根据纸张方向匹配模板');

            // 多个模板再去根据方向匹配，如果只有一个模板则直接使用这个模板即可
            if (templates.length > 1) {
                const cacheTemplates = clone(templates);
                for (let i = 0; i < cacheTemplates.length; i++) {
                    const pages = cacheTemplates[i].pages;
                    for (let j = 0; j < pages.length; j++) {
                        const page = pages[j];
                        if (page.onlyOrientation) {
                            if (page.onlyOrientation === pageOrientation) {
                                this.matchedTemplateItem = cacheTemplates[i];
                                break;
                            }
                        } else if (page.defaultOrientation) {
                            if (page.defaultOrientation === pageOrientation) {
                                this.matchedTemplateItem = cacheTemplates[i];
                                break;
                            }
                        }
                    }
                    if (!!this.matchedTemplateItem) break;
                }
            }

            if (!this.matchedTemplateItem) {
                this.matchedTemplateItem = clone(templates)[0];
                console.warn('根据纸张方向未匹配到模板，默认使用第一个模版', this.matchedTemplateItem)
            } else {
                console.warn('根据纸张方向匹配到模板', this.matchedTemplateItem)
            }
        }

        // 日志上报
        if(this.options.needReportLog && this.options.reportLogData) {
            this.printLogger._send(this.options.reportLogData.scene, {
                keyId: this.options.reportLogData?.keyId,
                info: 'abc-print 匹配到的模板信息',
                originData: JSON.stringify(this.options.originData),
                matchedTemplateItem: JSON.stringify(this.matchedTemplateItem),
            })
        }

        Logger.debug('模板匹配完成')
    }

    async updateSplitSize() {
        const optionsSize = this.options.page.size;
        const optionsPages = this.options.template.pages;
        const isOwnPage = optionsPages.some(it => it.paper.name === optionsSize);
        if (AbcPrint.isEnablePrintV2 && !isOwnPage && optionsSize) {
            const templates = this.options.template.templates;
            this.matchedTemplateItem = clone(templates)[0];
            const pageOrientation = +this.options.page.orientation;
            const splitHeight = this.options.page.height
            const splitWidth = this.options.page.width
            const isLandScape = pageOrientation === Orientation.landscape
            const { top, left, right, bottom } = this.options.page.pageSizeReduce || {}
            this.splitSize.height = isLandScape ? splitWidth : splitHeight
            this.splitSize.width = isLandScape ? splitHeight : splitWidth
            if (top) {
                this.splitSize.height = `${strToMM(this.splitSize.height) - top}mm`
            }
            if (bottom) {
                this.splitSize.height = `${strToMM(this.splitSize.height) - bottom}mm`
            }
            if (left) {
                this.splitSize.width = `${strToMM(this.splitSize.width) - left}mm`
            }
            if (right) {
                this.splitSize.width = `${strToMM(this.splitSize.width) - right}mm`
            }
        } else {
            let pageInfo = this.matchedTemplateItem.pages[0].paper;
            if (this.matchedTemplateItem.pages.length > 1) {
                const pageSize = this.options.page.size;
                const _pageInfo = this.matchedTemplateItem.pages.find(page => {
                    return page.paper.key === pageSize || page.paper.name === pageSize;
                })
                if (_pageInfo) {
                    pageInfo = _pageInfo.paper;
                }
            }

            const pageOrientation = +this.options.page.orientation;

            // @ts-ignore
            const pageSizeKey = pageInfo.key;
            // @ts-ignore
            const matchedSize = Object.keys(PageSize).map(key => PageSize[key]).find(it => it.key === pageSizeKey)

            if (!matchedSize) {
                // 日志上报
                if(this.options.needReportLog && this.options.reportLogData) {
                    this.printLogger._send(this.options.reportLogData.scene, {
                        keyId: this.options.reportLogData?.keyId,
                        info: 'abc-print 系统页面中未找到该size',
                        originData: JSON.stringify(this.options.originData),
                        page: JSON.stringify(this.options.page),
                        templatePages: JSON.stringify(this.matchedTemplateItem.pages),
                    })
                }
                throw Error('系统页面中未找到该size')
            }

            let splitHeight = (matchedSize as IPageSize).height
            let splitWidth = (matchedSize as IPageSize).width

            if (this.options.page.size !== matchedSize.name && this.options.page.height && this.options.page.width) {
                splitHeight = this.options.page.height
                splitWidth = this.options.page.width
            }

            if (pageInfo.heightLevels && pageInfo.heightLevels.length) {
                splitHeight = pageInfo.heightLevels[0].height;
                splitWidth = pageInfo.heightLevels[0].width;
            }
            const isLandScape = pageOrientation === Orientation.landscape

            this.splitSize.height = isLandScape ? splitWidth : splitHeight
            this.splitSize.width = isLandScape ? splitHeight : splitWidth

            if (!this.splitSize.height || !this.splitSize.width) {
                // 日志上报
                if(this.options.needReportLog && this.options.reportLogData) {
                    this.printLogger._send(this.options.reportLogData.scene, {
                        keyId: this.options.reportLogData?.keyId,
                        info: '分页宽高设置失败',
                        originData: JSON.stringify(this.options.originData),
                        page: JSON.stringify(this.options.page),
                        pageInfo: JSON.stringify(pageInfo),
                    })
                }
                throw Error('分页宽高设置失败')
            }

            const { top, left, right, bottom } = this.options.page.pageSizeReduce || {}

            if (this.splitSize.height !== 'auto') {
                if (top) {
                    this.splitSize.height = `${strToMM(this.splitSize.height) - top}mm`
                }

                if (bottom) {
                    this.splitSize.height = `${strToMM(this.splitSize.height) - bottom}mm`
                }
            }

            if (this.splitSize.width !== 'auto') {
                if (left) {
                    this.splitSize.width = `${strToMM(this.splitSize.width) - left}mm`
                }

                if (right) {
                    this.splitSize.width = `${strToMM(this.splitSize.width) - right}mm`
                }
            }
            // 日志上报
            if(this.options.needReportLog && this.options.reportLogData) {
                this.printLogger._send(this.options.reportLogData.scene, {
                    keyId: this.options.reportLogData?.keyId,
                    info: '计算得到的宽高',
                    originData: JSON.stringify(this.options.originData),
                    page: JSON.stringify(this.options.page),
                    splitSize: JSON.stringify(this.splitSize),
                })
            }
        }
    }

    async loadTemplate() {
        await loadScript(this.matchedTemplateItem.url)
        if (Object.prototype.hasOwnProperty.call(window?.AbcPackages?.AbcPrint?.$mount, this.matchedTemplateItem.windowRegisteredName)) {
            const templateName = this.matchedTemplateItem.windowRegisteredName
            this.component = (window?.AbcPackages?.AbcPrint?.$mount?.[templateName]) as Component

            // 日志上报
            if(this.options.needReportLog && this.options.reportLogData) {
                this.printLogger._send(this.options.reportLogData.scene, {
                    keyId: this.options.reportLogData?.keyId,
                    info: '模板加载成功',
                    templateName,
                    component: JSON.stringify(this.component),
                    originData: JSON.stringify(this.options.originData),
                    page: JSON.stringify(this.options.page),
                })
            }
        } else {
            // 日志上报
            if(this.options.needReportLog && this.options.reportLogData) {
                this.printLogger._send(this.options.reportLogData.scene, {
                    keyId: this.options.reportLogData?.keyId,
                    info: '模板加载失败',
                    originData: JSON.stringify(this.options.originData),
                    page: JSON.stringify(this.options.page),
                })
            }
            throw Error('模板加载失败')
        }
        Logger.debug('template加载完成')
    }

    async loadRuntime() {
        if (AbcPrint.runtime) {
            return Logger.log('runtime使用缓存')
        }
        if (this.options.runtime) {
            AbcPrint.runtime = this.options.runtime
            return Logger.log('runtime使用options.runtime')
        }
        if (window.Vue) {
            AbcPrint.runtime = window.Vue
            return Logger.log('runtime使用window.Vue')
        }
        if (!this.options.runtime) {
            await loadScript(AbcPrint.vue)
            AbcPrint.runtime = (window as any).Vue
        }
    }

    initPrintLogger() {
        // 初始化错误上报
        if (AbcPrint.runtime) {
            this.printLogger.init(AbcPrint.runtime, AbcPrint.errorLogMetaData, this.options, this._globalConfig);
        }
    }

    loadI18nInstance() {
        const Vue = AbcPrint.runtime;
        Vue.use(VueI18n);
        this.i18nInstance = new VueI18n({
            locale: AbcPrint.i18n.locale,
            messages: AbcPrint.i18n.messages,
        });
    }

    async loadInstance() {
        const Vue = AbcPrint.runtime;
        if (Vue) {
            const ComponentConstructor = Vue.extend(this.component)
            const AbcGrowthChartComponent = Vue.extend(AbcGrowthChart)
            const AbcPrintForm = Vue.extend(PrintForm)
            Vue.component('AbcGrowthChart', AbcGrowthChartComponent)
            Vue.component('AbcPrintForm', AbcPrintForm)

            this.instance && this.instance.$destroy()

            this.instance = new ComponentConstructor({
                propsData: {
                    renderData: this?.dataHandler?.renderData,
                    extra: this.options.extra ?? {},
                    options: this.options,
                    splitSize: this.splitSize,
                    printLogger: this.printLogger,
                    loggerKeyId: this.options.reportLogData?.keyId,
                    loggerScene: this.options.reportLogData?.scene,
                    viewDistributePrintConfig: AbcPrint.viewDistributePrintConfig,
                    injectConfig: AbcPrint.injectConfig,
                },
                i18n: this.i18nInstance,
            }).$mount();
            // 日志上报
            if(this.options.needReportLog && this.options.reportLogData) {
                this.printLogger._send(this.options.reportLogData.scene, {
                    keyId: this.options.reportLogData?.keyId,
                    info: 'loadInstance 获取的page信息',
                    originData: JSON.stringify(this.options.originData),
                    instanceEl: this.instance && this.instance.$el.outerHTML,
                    page: JSON.stringify(this.options.page),
                })
            }
            if (this.instance && this.options.page.size) {
                this.instance.$el.setAttribute('data-size', `page_${this.options.page.size}`)
            }
        }
        Logger.debug('instance加载完成')
    }

    async loadOriginData() {
        if (this.options.originData) {
            this.originData = this.options.originData || {};
            // 日志上报
            if(this.options.needReportLog && this.options.reportLogData) {
                this.printLogger._send(this.options.reportLogData.scene, {
                    keyId: this.options.reportLogData?.keyId,
                    info: '打印数据',
                    originData: JSON.stringify(this.originData),
                    page: JSON.stringify(this.options.page),
                })
            }
        } else {
            if (this.matchedTemplateItem.exampleData) {
                Logger.warn('已设置exampleData => originData')
                this.originData = this.matchedTemplateItem.exampleData
            } else {
                Logger.warn('未提供originData、可能导致渲染异常')
            }
        }
        Logger.debug('originData加载完成')
    }


    async updateInstanceGlobalConfig(instanceGlobalConfig?: GlobalConfig) {
        if (instanceGlobalConfig) {
            this._globalConfig = instanceGlobalConfig
            AbcPrint.printLogData._globalConfig = instanceGlobalConfig
            return;
        }
        if (AbcPrint.globalConfig) {
            this._globalConfig = AbcPrint.globalConfig
            AbcPrint.printLogData._globalConfig = AbcPrint.globalConfig
        }
    }

    processingData() {
        // 处理输注内容,根据设置中的是否拆分决定
        const isInfusionSplit = this._globalConfig.medicalDocuments?.infusion?.content?.infusionSplitByUsage === 1;
        // TODO: 最终需要去掉感叹号
        if (isInfusionSplit) {
            const { executeForms } = this.originData;
            const indexList: any[] = [];
            const pendingExecuteForms: any[] = [];
            // 筛选出西药处方和输注处方
            executeForms.forEach((item: any, index: number) => {
                if (item.type === 1 || item.type === 2) {
                    indexList.push(index);
                    pendingExecuteForms.push(item);
                }
            });
            // 将原来的输液注射单删除
            for (let i = indexList.length - 1; i > -1; i--) {
                executeForms.splice(indexList[i], 1);
            }
            // 将拆分后的输液注射单进行排序
            pendingExecuteForms.sort((a, b) => {
                return a.type - b.type;
            });
            // 将拆分后的输液注射单分别插入到原来的位置
            let index = 0;
            pendingExecuteForms.forEach((item) => {
                const { executeFormItems } = item;

                const lastExecuteForms = executeForms[executeForms.length - 1];
                if (lastExecuteForms) {
                    if (lastExecuteForms.type !== item.type) {
                        index = 0;
                    }
                }

                const dealExecuteForms: any[] = [];
                executeFormItems.forEach((ele: any) => {
                    // 注射
                    if (MEDICINE_USAGE.injections.includes(ele.usage)) {
                        const externalExecuteForms = dealExecuteForms.find((it) => it.typeInfo === 'injections');
                        if (!externalExecuteForms) {
                            dealExecuteForms.push({
                                ...item,
                                totalPrice: ele.totalPrice,
                                executeFormItems: [ele],
                                typeInfo: 'injections',
                                typeInfoZh: '注射单',
                                subIndex: index,
                            });
                        } else {
                            externalExecuteForms.executeFormItems.push(ele);
                            externalExecuteForms.totalPrice += ele.totalPrice;
                        }
                    } else if (MEDICINE_USAGE.infusion.includes(ele.usage)) {
                        // 输液
                        const externalExecuteForms = dealExecuteForms.find((it) => it.typeInfo === 'infusion');
                        if (!externalExecuteForms) {
                            dealExecuteForms.push({
                                ...item,
                                totalPrice: ele.totalPrice,
                                executeFormItems: [ele],
                                typeInfo: 'infusion',
                                typeInfoZh: '输液单',
                                subIndex: index,
                            });
                        } else {
                            externalExecuteForms.executeFormItems.push(ele);
                            externalExecuteForms.totalPrice += ele.totalPrice;
                        }
                    } else if (MEDICINE_USAGE.atomization.includes(ele.usage)) {
                        // 雾化
                        const externalExecuteForms = dealExecuteForms.find((it) => it.typeInfo === 'atomization');
                        if (!externalExecuteForms) {
                            dealExecuteForms.push({
                                ...item,
                                totalPrice: ele.totalPrice,
                                executeFormItems: [ele],
                                typeInfo: 'atomization',
                                typeInfoZh: '雾化单',
                                subIndex: index,
                            });
                        } else {
                            externalExecuteForms.executeFormItems.push(ele);
                            externalExecuteForms.totalPrice += ele.totalPrice;
                        }
                    } else if (MEDICINE_USAGE.external.includes(ele.usage)) {
                        // 外用
                        const externalExecuteForms = dealExecuteForms.find((it) => it.typeInfo === 'external');
                        if (!externalExecuteForms) {
                            dealExecuteForms.push({
                                ...item,
                                totalPrice: ele.totalPrice,
                                executeFormItems: [ele],
                                typeInfo: 'external',
                                typeInfoZh: '外治单',
                                subIndex: index,
                            });
                        } else {
                            externalExecuteForms.executeFormItems.push(ele);
                            externalExecuteForms.totalPrice += ele.totalPrice;
                        }
                    } else {
                        // 自定义用法归类在输液中
                        const externalExecuteForms = dealExecuteForms.find((it) => it.typeInfo === 'infusion');
                        if (!externalExecuteForms) {
                            dealExecuteForms.push({
                                ...item,
                                totalPrice: ele.totalPrice,
                                executeFormItems: [ele],
                                typeInfo: 'infusion',
                                typeInfoZh: '输液单',
                                subIndex: index,
                            });
                        } else {
                            externalExecuteForms.executeFormItems.push(ele);
                            externalExecuteForms.totalPrice += ele.totalPrice;
                        }
                    }
                });
                executeForms.push(...dealExecuteForms);
                index++;
            });
        }
    }

    /**
     * 预处理数据
     */
    async preHandleProps() {
        if (this.firstLoad) {
            const componentBusinessKey = this.component?.businessKey;
            /**
             * 对输注单做额外处理
             */
            if (componentBusinessKey === 'infusion-execute') {
                this.processingData();
            }
            this.firstLoad = false;
        }
    }

    async loadDataHandler(customOptions?: CustomInitOption) {
        if (this.component.DataHandler) {
            const DataHandler = this.component.DataHandler;
            this.dataHandler = new DataHandler({
                originData: this.originData,
                globalConfig: customOptions?.printConfig || this._globalConfig,
            })
        } else {
            Logger.error('模板未提供DataHandler')
            // 提供默认dataHandler
            // @ts-ignore
            this.dataHandler = {
                originData: this.originData,
                renderData: this.originData,
            }
        }
        Logger.debug('dataHandler加载完成')
    }

    loadGlobalStyle() {
        try {
            if (this.component.getInjectGlobalStyle instanceof Function) {
                this.injectGlobalStyle = this.component.getInjectGlobalStyle(this.originData, AbcPrint.globalConfig)
            } else {
                this.injectGlobalStyle = ''
            }
        } catch (e) {
            console.error(e);
            Logger.error('loadGlobalStyle error');
        }
    }

    getStaticRenderConfig() {
        return this.dataHandler?.getStaticRenderConfig?.()
    }

    _errorHandler(e = new Error()) {
        if(!e.message) {
            return;
        }
        if(e.message === 'AbcPackages is not defined') {
            return;
        }
        try {
            console.error(e)
            const uploadMessage = {
                namespace: 'abc-print-error',
                detail: JSON.stringify({
                    error: {
                        message: e.message,
                        stack: e.stack,
                    },
                    extractStack: extractStack(e),
                    options: this.options,
                    _globalConfig: this._globalConfig,
                }),
                message: e.message,
            };
            const url = __DEV__ ? '//172.19.119.250:8998/api/v2/print-log/' : '/api/v2/print-log'
            if(window.AbcPrintDisableErrorLog) {
                return
            }
            fetch(url, {
                method: 'POST',
                body: JSON.stringify(uploadMessage),
                headers: {
                    'Content-Type': 'application/json',
                },
            }).catch(e => {
                console.error(e)
            })
        } catch (e) {
            console.error(e)
        }
    }

    async setRenderConfig(renderConfig: any) {
        this.dataHandler?.setRenderConfig?.(renderConfig)
        if (this.instance) {
            // @ts-ignore
            if(isEqual(this.instance.renderData, this.dataHandler?.renderData)) {
                Logger.log('renderData相同无需更新')
                return;
            }
            this.instance.renderData = clone(this.dataHandler?.renderData)
            Logger.log('renderData已更新')
            await this.instance.$nextTick()
        } else {
            Logger.error('renderData更新失败')
        }
    }

    async updateDatahandlerAndInstance() {
        await this.loadDataHandler()
        if (this.instance) {
            // @ts-ignore
            this.instance.renderData = clone(this.dataHandler?.renderData)
            Logger.log('renderData已更新')
            await this.instance.$nextTick()
        } else {
            Logger.error('renderData更新失败')
        }
    }

    loadInstanceStyle() {
        const cssStyleEl = loadStyle(this.matchedTemplateItem.css)
        const hackCssStyleEl = loadStyle(this.matchedTemplateItem.hackCss)
        return () => {
            try {
                cssStyleEl?.parentElement?.removeChild(cssStyleEl);
                hackCssStyleEl?.parentElement?.removeChild(hackCssStyleEl);
            } catch (e) {
                console.error(e)
            }
        }
    }

    async splitPreview(isSplit = false, options?: SplitOptions) {
        let template = await this.splitTemplate()
        if(this.options.needReportLog && this.options.reportLogData) {
            this.printLogger._send(this.options.reportLogData.scene, {
                keyId: this.options.reportLogData?.keyId,
                info: '分页后的template',
                splitTemplate: JSON.stringify(template),
                page: JSON.stringify(this.options.page),
            })
        }
        if (typeof template === 'string') {
            /**
             * 对用药标签做特殊处理
             * 在实际打印时将大于1份的标签按需要打印的份数做copy处理
             */
            const businessKey = this.component?.businessKey || '';
            if ((businessKey === 'medicine-tag' || businessKey === 'hospital-medicine-tag') && isSplit) {
                const parser = new DOMParser();
                const container = parser.parseFromString(template, 'text/html');
                const medicineTagContainers = container.getElementsByClassName('medicine-tag-header-patient-container');
                const body = container.body;
                const resBody = document.createElement('body');
                let groupList: Array<Array<HTMLElement | Node>> = [];
                let lastPrescriptionGroup: string | null = '0';
                for (let i = 0; i < medicineTagContainers.length; i++) {
                    const medicineTagContainer = medicineTagContainers[i];
                    const cacheCount = medicineTagContainer.getAttribute('data-count');
                    const prescriptionGroup = medicineTagContainer.getAttribute('data-prescription-group');
                    const count = cacheCount ? +cacheCount : 1;

                    if (prescriptionGroup !== lastPrescriptionGroup) {
                        groupList.forEach((itemArr) => {
                            itemArr.forEach((item) => {
                                resBody.appendChild(item);
                            });
                        });
                        groupList = [];
                        lastPrescriptionGroup = prescriptionGroup;
                    }

                    const parentContainer = medicineTagContainer.parentElement?.parentElement;
                    if (parentContainer && parentContainer.classList.contains(PAGE_CLASSNAME)) {
                        for (let j = 0; j < count; j++) {
                            const cloneContainer = parentContainer.cloneNode(true);
                            if (!Array.isArray(groupList[j])) {
                                groupList.push([]);
                            }
                            groupList[j].push(cloneContainer);
                        }
                    }
                }
                groupList.forEach((itemArr) => {
                    itemArr.forEach((item) => {
                        resBody.appendChild(item);
                    });
                });
                body.innerHTML = resBody.innerHTML;
                const htmlDOM = container.getElementsByTagName('html')[0];
                template = htmlDOM.outerHTML;
            }

            const PAGE_REGEXP = new RegExp(`<div class="${PAGE_CLASSNAME}" [^>]+[\\s\\S]*?>`, 'g')
            const PAGE_CONTENT_REGEXP = new RegExp(`<div class="${PAGE_CONTENT_CLASSNAME}[^_]*?" [^>]+[\\s\\S]*?>`, 'g')
            const STYLE_REGEXP = new RegExp(`style="([\\s\\S]*?)"`)
            const result = template.replace(PAGE_REGEXP, (str) => {
                return str.replace(PAGE_CLASSNAME, `${PAGE_CLASSNAME} ${isSplit ? PAGE_CLASSNAME_PRINT : PAGE_CLASSNAME_PREVIEW}`)
                    .replace(STYLE_REGEXP, (styleStr, $0) => {
                        if (isSplit) {
                            if (!options?.keepPageWidth) {
                                $0 = $0.replace(/(width):.*?;/g, '')
                            }
                            $0 = $0.replace(/height:(.*?);/g, function (str: any, $1: string | string[]) {
                                if ($1.includes('mm')) {
                                    // electron 打印需要减去0.5mm
                                    return `height: ${parseFloat(<string>$1) - 0.5}mm;`
                                }
                                return str;
                            })
                        }
                        const { top, bottom, left, right } = this.options.page.pageSizeReduce ?? {};
                        if (left) {
                            $0 += `padding-left: ${left}mm;`
                        }

                        if (right) {
                            $0 += `padding-right: ${right}mm;`
                        }

                        if (top) {
                            $0 += `padding-top: ${top}mm;`
                        }

                        if (bottom) {
                            $0 += `padding-bottom: ${bottom}mm;`
                        }
                        $0 += `box-sizing: content-box;`

                        return `style="${$0}"`
                    })
            }).replace(PAGE_CONTENT_REGEXP, str => {
                return str.replace(STYLE_REGEXP, (styleStr, $0) => {
                    $0 += `height: 100%;`
                    return `style="${$0}"`
                })
            })
            return result
        } else {
            throw Error('template must be string')
        }
    }

    async split(options: SplitOptions) {
        return await this.splitPreview(true, options)
    }

    async splitPreview1() {
        // TODO 分页里面直接算好高度
        let html = await this.splitPreview()
        html = html.replace(/<head>([\s\S]*?)<\/head>/, function (str, $1) {
            const customStyle = `
            <style>
                .${PAGE_CLASSNAME} {
                    margin-bottom: 10px;
                    border: 1px solid #ced0da;
                    box-sizing: border-box!important;
                    overflow: hidden;
                }
            </style>
            `
            return `<head>${$1 + customStyle}</head>`
        });

        const iframe = document.createElement('iframe')
        iframe.setAttribute('style', 'height:0;width:0;overflow:hidden;font-size:0;postion:fixed;right:9999px;z-index: -999;')
        document.body.append(iframe)
        let height: number = 0
        if(this.options.isRunInH5) {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
            iframeDoc?.open()
            iframeDoc?.write(html)
            iframeDoc?.close()
        } else {
            iframe.srcdoc = html

            height = await new Promise(resolve => {
                iframe.onload = function () {
                    resolve(iframe?.contentDocument?.body?.clientHeight)
                }
            })
        }
        document.body.removeChild(iframe)
        return {
            html,
            height,
        }
    }

    splitTemplate() {
        let extraHTML = (typeof this.options.extra?.getHTML) === 'function' ? this.options?.extra?.getHTML() : '';
        const pageExtraTemplate = this.options?.extra?.pageExtraTemplate;
        const isTicketEscPosPrint = this.options?.extra?.isTicketEscPosPrint;
        let extraRealHTML = '';
        let extraStyle = '';
        if(extraHTML) {
            // 拆分style元素
            extraRealHTML = extraHTML.replace(/<style>[\s\S]*?<\/style>/, '')
            extraStyle = (extraHTML.match(/<style>[\s\S]*?<\/style>/) ?? [])[0] ?? ''
        }
        const templateHTML = extraRealHTML || this.instance?.$el.outerHTML;
        if(this.options.needReportLog && this.options.reportLogData) {
            this.printLogger._send(this.options.reportLogData.scene, {
                keyId: this.options.reportLogData?.keyId,
                info: '分页前instance outerHTML',
                splitTemplate: JSON.stringify(templateHTML),
                page: JSON.stringify(this.options.page),
                splitSize: JSON.stringify(this.splitSize),
            })
        }
        if(this.options.extra?.customSplitTemplate) {
            return this.options.extra?.customSplitTemplate(this);
        }
        return new Promise((resolve, reject) => {
            const startTime = Date.now()
            const uuid = generateUUID()
            const iframe = document.createElement('iframe')
            const scriptStr = __DEV__ ? `<script src="${AbcPrint.url}?t=${startTime}"></script>` : ''
            iframe.setAttribute('style', 'height:0;width:0;overflow:hidden;font-size:0;postion:fixed;right:9999px;z-index: -999;')
            const template = `
            <html lang="en">
                <head>
                <meta charset="UTF-8">
                <meta http-equiv="X-UA-Compatible" content="IE=edge">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${this.matchedTemplateItem.windowRegisteredName}</title>
                <style type="text/css">
                    * {
                        margin: 0;
                        padding: 0;
                    }
                    .${PAGE_CLASSNAME} {
                        overflow: hidden;
                        box-sizing: border-box;
                        background: #fff;
                        ${(this.options.page.pageSizeReduce?.left ?? '') === (this.options.page.pageSizeReduce?.right ?? '') ? 'margin: 0 auto;' : ''}
                    }
                    @page {
                        margin: 0;
                    }
                    .abc-page-content {
                        position: relative;
                    }
                    div[data-pagesize="A4"] {
                        background: #fff;
                    }
                    ${this.injectGlobalStyle}
                </style>
                ${ extraStyle }
                <style type="text/css">
                    ${this.matchedTemplateItem.css}
                </style>
                <style type="text/css">
                    ${this.matchedTemplateItem.hackCss}
                </style>
                </head>
                <body>
                <!--AbcSpliterInject-->
                ${scriptStr}
                <script>
                    'use strict';
                    window.onload = function (){
                      try {
                            var Spliter = (AbcPackages.AbcPrint && AbcPackages.AbcPrint.Spliter) || AbcPackages.AbcSpliter.Spliter
                            var html = '${btoa(encodeURIComponent(templateHTML as string))}'
                            var div = document.createElement('div')
                            div.innerHTML = decodeURIComponent(atob(html))
                            new Spliter({
                                ${pageExtraTemplate ? `pageExtraTemplate: '${pageExtraTemplate}',` : ''}
                                dom: div.children[0], 
                                page: ${JSON.stringify({
                                    size: {
                                        height: this.splitSize.height,
                                        width: this.splitSize.width,
                                        name: this.options.page.size,
                                    },
                                    customStyles: this.options.page.customStyles,
                                })}
                            })
							if (${isTicketEscPosPrint}) {
								const abcPageDom = document.documentElement.querySelector('${'.' + PAGE_CLASSNAME}');
								abcPageDom.style.width = 'min-content';
							}
                            top.postMessage({
                                html: document.documentElement.innerHTML.replace(/<!---->/g, ''),
                                uuid: "${uuid}"
                            }, "${location.href}")
                        } catch (e) {
                            top.postMessage({
                                uuid: "${uuid}",
                                error: {
                                  message: e.message,
                                  stack: e.stack
                                }
                            }, "${location.href}")
                        }
                    }
                </script>
                </body>
            </html>
            `
            document.body.append(iframe)

            if(this.options.isRunInH5) {
                const iframeDoc = iframe.contentDocument
                iframeDoc?.open()
                iframeDoc?.write(template)
                iframeDoc?.close()
            } else {
                iframe.srcdoc = template
            }

            const messageListener = (e: MessageEvent) => {
                if(e.data.uuid !== uuid) {
                    if(this.options.needReportLog && this.options.reportLogData) {
                        this.printLogger._send(this.options.reportLogData.scene, {
                            keyId: this.options.reportLogData?.keyId,
                            info: 'uuid不匹配',
                        })
                    }
                    return console.error('uuid不匹配');
                }
                if (e.data.error) {
                    Logger.error(`分页渲染失败: ${e.data.error.message}`)
                    const error = new Error(e.data.error.message)
                    error.stack = e.data.error.stack
                    this.printLogger._send('abcPrint.splitPage', error);
                    reject(error)
                    if (!__DEV__) {
                        document.body.removeChild(iframe)
                    }
                    window.removeEventListener("message", messageListener)
                }
                if (e.data.html) {
                    const html = '<html lang="en">\n' + e.data.html.replace(/<script[^>]*?>[\s\S]*?<\/script>/g, '') + '\n</html>'
                    if (/https?:\/\//g.test(html)) {
                        const errorMessage = '模板中包含http或者https外部链接，可能会在云南特殊网络环境中导致资源加载失败';
                        if (__DEV__) {
                            console.error(errorMessage)
                        } else {
                            // this._errorHandler(Error(errorMessage));
                        }
                    }
                    // 判断html中是否包含http链接
                    resolve(html)
                    if (!__DEV__) {
                        document.body.removeChild(iframe)
                    }
                    window.removeEventListener("message", messageListener)
                }
            }
            window.addEventListener("message", messageListener)
        })
    }
}
