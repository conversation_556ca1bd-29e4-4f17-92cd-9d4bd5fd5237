export function isString(str: any) {
    return Object.prototype.toString.call(str) === '[object String]'
}

export function isObject(obj: any) {
    return Object.prototype.toString.call(obj) === '[object Object]'
}

export function isArray(obj: any) {
    return Object.prototype.toString.call(obj) === '[object Array]'
}

export function isFunction(fn: any) {
    return typeof fn === 'function'
}

export function any2List<T>(any: any): T[] {
    return Array.prototype.slice.call(any)
}

export function getElementHeight(element: Element) {
    return parseFloat(getComputedStyle(element).height)
}

export function getElementWidth(element: Element) {
    return parseFloat(getComputedStyle(element).width)
}

export function getElementHeightWithoutPadding(element: Element) {
    const outerHeight = getElementHeight(element)
    return outerHeight - parseFloat(getComputedStyle(element).paddingTop) - parseFloat(getComputedStyle(element).paddingBottom)
}

export function isElementNode(node?: Node): node is Element {
    return node?.ELEMENT_NODE === node?.nodeType
}

export function isTextNode(node?: Node) {
    return node?.TEXT_NODE === node?.nodeType
}

export function insertAfter(newElement: Node, targetElement: Node) {
    const parentElement = targetElement.parentNode;
    if (parentElement?.lastChild == targetElement) {
        parentElement.appendChild(newElement);
    } else {
        parentElement?.insertBefore(newElement, targetElement.nextSibling);
    }
}

export function loadScript(src: string) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = src
        script.onload = () => {
            document.body.removeChild(script)
            resolve(void 0)
        }
        script.onerror = () => {
            reject()
            document.body.removeChild(script)
        }
        document.body.appendChild(script)
    })
}

export function getTimeStamp() {
    const now = new Date();
    const hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();
    return `${hours > 9 ? hours : '0' + hours}:${minutes > 9 ? minutes : '0' + minutes}:${seconds > 9 ? seconds : '0' + seconds}`
}

export function toHTML(dom: Element | undefined) {
    const div = document.createElement("div")
    if (!dom) return div
    div.appendChild(dom);
    return div.innerHTML;
}

// @ts-ignore
export function clone(obj: any) {
    if (null == obj || "object" != typeof obj) return obj
    if (obj instanceof Array) {
        let copy = [];
        for (let i = 0, len = obj.length; i < len; ++i) {
            copy[i] = clone(obj[i])
        }
        return copy
    }
    if (obj instanceof Object) {
        let copy = {}
        for (let attr in obj) {
            // @ts-ignore
            if (obj.hasOwnProperty(attr)) copy[attr] = clone(obj[attr])
        }
        return copy
    }
}

export function encodeHtml(string: string) {
    if (!string) return string
    return string.replace(/[<>&"]/g, (char) => {
        return {
            '<': '&lt;',
            '>': '&gt;',
            '&': '&amp;',
            '"': '&quot;',
        }[char] ?? ''
    })
}

export function strToMM(string: string): number {
    if (!string) return 0;
    if (string.endsWith('mm') || string.endsWith('MM')) {
        return parseFloat(string)
    }
    if (string.endsWith('cm') || string.endsWith('CM')) {
        return parseFloat(string) * 10
    }
    if (string.endsWith('m') || string.endsWith('M')) {
        return parseFloat(string) * 1000
    }

    return 0
}

export function loadStyle(styleContent: string) {
    const style = document.createElement('style');
    style.innerHTML = styleContent;
    style.type = "text/css";
    style.id = `abc-print-style-${Date.now()}`;
    document.head.append(style);
    return style;
}

export function image2Base64(img: HTMLImageElement) {
    const canvas = document.createElement("canvas");
    canvas.width = img.width;
    canvas.height = img.height;
    const ctx = canvas.getContext("2d");
    ctx!.drawImage(img, 0, 0, img.width, img.height);
    return canvas.toDataURL("image/png");
}

export function sleep(delay: number = 0) {
    return new Promise(resolve => setTimeout(resolve, delay))
}


export function getImageTransformParams(type: string): string {
    const TEMPLATE_TO_IMAGE_PARAMS: Record<string, string> = {
        'hospital-inspect': 'x-oss-process=image/resize,p_90',
        'examination-report': 'x-oss-process=image/resize,p_90',
        'pe-individual-report': 'x-oss-process=image/resize,p_90',
        'prescription-yibao': 'x-oss-process=image/resize,p_90',
        'prescription-circulation': 'x-oss-process=image/resize,p_90',
    };

    return TEMPLATE_TO_IMAGE_PARAMS[type] ?? '';
}

export const resolveErrorMessage = (err: Error | string) => {
    if (!err) return '未知错误';

    if (typeof err === 'string') return err;

    try {
        return JSON.stringify(err, Object.getOwnPropertyNames(err));
    } catch (e) {
        console.error('resolve error message', e);
        return err;
    }
}

export type CellPosition = {
    rowIndex?: number,
    cell?: HTMLTableCellElement,
    colSpan?: number,
    rowSpan?: number,
    content?: string | null,
    rowHeight?: number,
    from?: CellPosition
}

export type RowPosition = {
    tr: HTMLTableRowElement,
    rowHeight: number,
    cells: CellPosition[],
}

export function findFirstEmptyIdxInSparseArray(arr: any[]) {
    for(let i = 0; i < arr.length; i++) {
        if(!arr[i]) {
            return i
        }
    }
    return -1
}

export function getComplexTablePosition(table: HTMLTableElement): RowPosition[] {
    const tbodyRows = table.tBodies[0].rows;
    const positions: RowPosition[] = [];
    let borderSpace: string | Array<string> = window.getComputedStyle(table).borderSpacing;
    let borderSpaceY: number | string = 0;
    if(borderSpace) {
        borderSpace = borderSpace.split(' ');
        borderSpaceY = borderSpace.length === 1 ? borderSpace[0] : borderSpace[1];
        borderSpaceY = borderSpaceY.replace('px', '');
        borderSpaceY = Number(borderSpaceY);
    }
    // 构建初始位置表
    for (let i = 0; i < tbodyRows.length; i++) {
        const row = tbodyRows[i];
        const currentRowPositions = positions[i] ?? {
            rowHeight: row.offsetHeight + borderSpaceY,
            cells: [],
            tr: row,
        }
        const curEmptyIndex = findFirstEmptyIdxInSparseArray(currentRowPositions.cells)
        let colIndex = curEmptyIndex > -1 ? curEmptyIndex : 0;
        positions[i] = currentRowPositions;
        for (let j = 0; j < row.cells.length; j++) {
            let emptyIndex = -1;
            const col = row.cells[j];
            const colSpan = col.colSpan;
            const rowSpan = col.rowSpan;
            let from: CellPosition;
            for (let k = 0; k < colSpan; k++) {
                emptyIndex = findFirstEmptyIdxInSparseArray(currentRowPositions.cells)

                function addContent(from: CellPosition) {
                    if (emptyIndex === -1) {
                        currentRowPositions.cells.push(from)
                    } else {
                        currentRowPositions.cells[emptyIndex] = from
                    }
                }

                if (k == 0) {
                    from = {
                        rowIndex: i,
                        cell: col,
                        colSpan,
                        rowSpan,
                        content: col.textContent,
                        rowHeight: col.parentElement!.offsetHeight + borderSpaceY,
                    }
                    addContent(from)
                } else {
                    addContent({
                        from: from!,
                    })
                }

                for (let l = 1; l < rowSpan; l++) {
                    const rowIndex = l + i;
                    if (!positions[rowIndex]) {
                        positions[rowIndex] = {
                            rowHeight: tbodyRows[rowIndex].offsetHeight + borderSpaceY,
                            cells: [],
                            tr: tbodyRows[rowIndex],
                        }
                    }
                    positions[rowIndex].cells[colIndex] = {
                        from: from!,
                    }
                }
                colIndex++;
            }
        }
    }

    return positions;
}

export function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
    })
}

export function isEqual() {
    //标识是否相似
    let i, l, leftChain: any, rightChain:any;

    function compare2Objects(x: any, y:any) {
        let p;

        // remember that NaN === NaN returns false
        // and isNaN(undefined) returns true
        if (isNaN(x) && isNaN(y) && typeof x === 'number' && typeof y === 'number') {
            return true;
        }

        // Compare primitives and functions.
        // Check if both arguments link to the same object.
        // Especially useful on the step where we compare prototypes
        if (x === y) {
            return true;
        }

        // Works in case when functions are created in constructor.
        // Comparing dates is a common scenario. Another built-ins?
        // We can even handle functions passed across iframes
        if (
            (typeof x === 'function' && typeof y === 'function') ||
            (x instanceof Date && y instanceof Date) ||
            (x instanceof RegExp && y instanceof RegExp) ||
            (x instanceof String && y instanceof String) ||
            (x instanceof Number && y instanceof Number)
        ) {
            return x.toString() === y.toString();
        }

        // At last checking prototypes as good as we can
        if (!(x instanceof Object && y instanceof Object)) {
            return false;
        }

        if (x.isPrototypeOf(y) || y.isPrototypeOf(x)) {
            return false;
        }

        if (x.constructor !== y.constructor) {
            return false;
        }

        if (x.prototype !== y.prototype) {
            return false;
        }

        // Check for infinitive linking loops
        if (leftChain.indexOf(x) > -1 || rightChain.indexOf(y) > -1) {
            return false;
        }

        // Quick checking of one object being a subset of another.
        // todo: cache the structure of arguments[0] for performance
        for (p in y) {
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false;
            } if (typeof y[p] !== typeof x[p]) {
                return false;
            }
        }

        for (p in x) {
            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {
                return false;
            } if (typeof y[p] !== typeof x[p]) {
                return false;
            }

            switch (typeof x[p]) {
                case 'object':
                case 'function':
                    leftChain.push(x);
                    rightChain.push(y);

                    if (!compare2Objects(x[p], y[p])) {
                        return false;
                    }

                    leftChain.pop();
                    rightChain.pop();
                    break;

                default:
                    if (x[p] !== y[p]) {
                        return false;
                    }
                    break;
            }
        }

        return true;
    }

    if (arguments.length < 1) {
        return true; //Die silently? Don't know how to handle such case, please help...
        // throw "Need two or more arguments to compare";
    }

    for (i = 1, l = arguments.length; i < l; i++) {
        leftChain = []; //Todo: this can be cached
        rightChain = [];

        if (!compare2Objects(arguments[0], arguments[i])) {
            return false;
        }
    }

    return true;
}
