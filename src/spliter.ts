import { A4, IPageSize } from "../share/page-size";
import Logger from "./logger";
import {
    RowPosition,
    any2List,
    getComplexTablePosition,
    getElementHeight,
    getElementHeightWithoutPadding,
    insertAfter,
    isElementNode,
    isObject,
    isTextNode,
} from "./utils";

import { splitLine } from "./split-dom";
export type CustomStyles = Record<string, any>

export enum ElementType {
    BLOCK = 'block', // 不分页，排不下依旧放在当前页
    MIX_BOX = 'mix-box', // 自动分页，提供group item, group以外的数据被带到下一页. item需要包含在group中
    FILL_BOX = 'fill-box', // 填充当前页面剩余的空间
    BIG_TABLE = 'big-table', // 行高固定的大表格
    MIX_TABLE = 'mix-table',
    FIXED_BOTTOM_BOX = 'fixed-bottom-box',
    COMPLEX_TABLE = 'complex-table',

    GROUP = 'group',
    ITEM = 'item',
    TEXT = 'text',

    HEADER = 'header',
    FOOTER = 'footer',

    NEW_PAGE = 'new-page',

    PENDANTS_HASH = 'pendants-hash',

    NEXT_PAGE = 'next-page',
    PREV_PAGE = 'prev-page',
    NEXT_IS_BLANK = 'next-is-blank',
    DAEA_SIGN = 'dae-sign',
    BreakPageAtFirstPage = 'break-page-at-first-page',
    LAST_PAGE_FOOTER = 'last-page-footer',
    MULTIPLY_SAMPLE_TABLE = 'multiply-sample-table',
}

export enum SpliterElementClassName {
    IS_SPLIT_AT_END = '__is-split_end__',
    IS_SPLIT_AT_START = '__is-split_start__'
}

const ITEM_GROUP_ID_ATTR = 'data-item-group-id'

type ContinueSplit = boolean

export const PAGE_CLASSNAME = 'abc-page'
export const PAGE_WRAPPER_CLASSNAME = 'abc-page-wrapper'
export const PAGE_CLASSNAME_PREVIEW = 'abc-page_preview'
export const PAGE_CLASSNAME_PRINT = 'abc-page_print'
export const PAGE_CONTENT_CLASSNAME = 'abc-page-content'
export const PAGE_FOOTER_CLASSNAME = 'abc-page-content__footer'
export const NEXT_PAGE_ALIGN_FOOTER = 'next-page_align_footer'

const MAX_STACK = 500
let stack = 0
const GLOBAL_PENDANTS_KEY = '__global_pendants__'
const BOTTOM_OFFSET = 8
const lodopCommandAttr = 'data-lodop-command'
const isSplitLineTag = 'is_split_line';
const TrMergeIdAttr = 'data-merge-tr-id'
const AvoidFirstPageBreakId = 'data-avoid-first-page-break-id'
const AvoidFirstPageBreakRefId = 'data-avoid-first-page-break-ref-id'

interface ISpliterOptions {
    dom: Element;
    currentPendants: {
        nodes: Element[];
        height: number
    }
    pendantsMap: Record<string, Element[]>
    page: {
        size: IPageSize,
        customStyles?: CustomStyles,
    },
    pageExtraTemplate?: string
}

interface IPageDOM {
    page: HTMLElement,
    content: HTMLElement
}

interface IAnchorNodeMap {
    origin: {
        type: string,
        node: Node,
        parent?: Node | ChildNode | null
        prev?: Node | ChildNode | null
        next?: Node | ChildNode | null
    },
    render: {
        type: string,
        node: Node,
        parent?: Node | ChildNode | null
        prev?: Node | ChildNode | null
        next?: Node | ChildNode | null
    }
}

const defaultRenderOptions = {
    dom: document.body,
    currentPendants: {
        nodes: [],
        height: 0,
    },
    pendantsMap: {},
    page: {
        size: A4,
        padding: '18mm',
    },
}

export class Spliter {
    private $options = defaultRenderOptions as ISpliterOptions

    constructor(options?: ISpliterOptions) {
        if (options) {
            if (!isObject(options)) {
                throw Error('options must be an object')
            }
            Object.assign(this.$options, options)
            if (this.$options.dom) {
                // 收集页面挂件
                this.getPendants(this.$options.dom)
                // 开始布局
                Logger.debug('开始分页')
                this.pureTemplateElement(this.$options.dom)
                this.newPageLayout(this.$options.dom)
            }

            try {
                // 设置自定义样式
                if (this.$options.page.customStyles) {
                    const abcPages = document.getElementsByClassName(PAGE_CLASSNAME) as HTMLCollectionOf<HTMLElement>;
                    const pageCount = abcPages.length;
                    for (let i = 0; i < pageCount; i++) {
                        Object.assign(abcPages[i].style, this.$options.page.customStyles)
                    }
                }
                // 增加页头页尾
                const abcPages = document.getElementsByClassName(PAGE_CONTENT_CLASSNAME);

                // 处理额外数据
                if(this.$options.pageExtraTemplate) {
                    const pageWrappers = document.getElementsByClassName(PAGE_CLASSNAME);
                    if(pageWrappers) {
                        const total = pageWrappers.length;
                        any2List<HTMLElement>(pageWrappers).forEach((pageWrapper, index) => {
                            const supportReplacer = [{
                                match: /{{pageNo}}/g,
                                value: index + 1,
                            }, {
                                match: /{{pageCount}}/g,
                                value: total,
                            }]
                            const handledTemplate = supportReplacer.reduce((template, replacer) => {
                                return template.replace(replacer.match, replacer.value.toString())
                            }, this.$options.pageExtraTemplate ?? '');

                            pageWrapper.innerHTML += handledTemplate;
                        })
                    }
                }

                let pageCount = abcPages.length;
                for (let i = 0; i < pageCount; i++) {
                    const pageIndexNode = (abcPages[i] as HTMLElement).querySelector('[data-page-no=PageNo]');
                    if (pageIndexNode) {
                        pageIndexNode.innerHTML = (i + 1).toString();
                    }
                    const pageCountNode = (abcPages[i] as HTMLElement).querySelector('[data-page-count=PageCount]');
                    if (pageCountNode) {
                        pageCountNode.innerHTML = (pageCount).toString();
                    }
                    const lastPageVisible = (abcPages[i] as HTMLElement).querySelector('[data-last-page=LastPage]');
                    if (lastPageVisible && i !== pageCount - 1) {
                        lastPageVisible.innerHTML = '';
                    }
                }
                // 增加接下一页
                for (let i = 0; i < pageCount; i++) {
                    const currentPage = abcPages[i];
                    const nextPage = abcPages[i + 1];

                    const currentPageHeader = currentPage?.querySelector('[data-type=header]')
                    const currentPageFooter = currentPage?.querySelector('[data-type=footer]')
                    const currentPendantsIndex = currentPageHeader?.getAttribute('data-pendants-index')
                    const nextPageHeader = nextPage?.querySelector('[data-type=header]')
                    const nextPendantsIndex = nextPageHeader?.getAttribute('data-pendants-index')

                    if (currentPendantsIndex === nextPendantsIndex) {
                        if (this.$options.pendantsMap[GLOBAL_PENDANTS_KEY] && this.$options.pendantsMap[GLOBAL_PENDANTS_KEY].length) {
                            const nextPagePendants = this.$options.pendantsMap[GLOBAL_PENDANTS_KEY].find(it => it.getAttribute('data-type') === ElementType.NEXT_PAGE)
                            const prevPagePendants = this.$options.pendantsMap[GLOBAL_PENDANTS_KEY].find(it => it.getAttribute('data-type') === ElementType.PREV_PAGE)

                            if (nextPagePendants && currentPageFooter) {
                                const newNextPagePendants = nextPagePendants.cloneNode(true)
                                currentPage.appendChild(newNextPagePendants)

                                const nextPageOffsetTop = (currentPage.querySelector(`.${ElementType.NEXT_PAGE}`) as HTMLElement)?.offsetTop
                                const footerEle = currentPage.querySelector(`.${PAGE_FOOTER_CLASSNAME}`) as HTMLElement
                                const footerHeight = parseFloat(getComputedStyle(footerEle).height)
                                const pageHeight = (currentPage?.parentElement && parseFloat(getComputedStyle(currentPage.parentElement).height)) ?? 0

                                if (nextPageOffsetTop >= pageHeight - footerHeight) {
                                    Logger.warn('接下一页超出，放置到脚部')
                                    currentPageFooter.appendChild(newNextPagePendants);
                                    (newNextPagePendants as HTMLElement).classList.add(NEXT_PAGE_ALIGN_FOOTER)
                                }
                            }

                            if (prevPagePendants && nextPageHeader) {
                                nextPageHeader.appendChild(prevPagePendants.cloneNode(true))
                            }
                        }
                    }
                }

                // 移除多余的display为none的元素
                document.querySelectorAll('[style="display: none"]').forEach(node => {
                    node?.parentNode?.removeChild(node)
                })

                // 移除最后一页是以下为空白
                for (let i = 0; i < pageCount; i++) {
                    const currentPage = abcPages[i];
                    const prevPage = abcPages[i - 1];
                    const nextIsBlank = currentPage.querySelector(`[data-type=${ElementType.NEXT_IS_BLANK}]`);
                    if (prevPage && nextIsBlank && currentPage.children.length <= 3) {
                        Logger.warn('存在仅有以下为空白的页面')
                        const abcPage = currentPage.parentElement
                        if (abcPage) {
                            const nextPage = prevPage.querySelector(`[data-type=${ElementType.NEXT_PAGE}]`)
                            if (nextPage) {
                                nextPage.parentElement?.removeChild(nextPage)
                            }
                            --pageCount;
                            --i;
                            document.body.removeChild(abcPage);
                        }
                    }
                }

                // 移除每个处方除最后一页以外的签名
                for (let i = 1; i < pageCount; i++) {
                    // const currentPage = abcPages[i];
                    const prevPage = abcPages[i - 1];
                    let nextPage = null
                    // const notLastPage = currentPage.querySelector(`[not-last-footer]`);
                    if (prevPage) {
                        nextPage = prevPage.querySelector(`[data-type=${ElementType.NEXT_PAGE}]`)
                    }
                    const daeSign = prevPage.querySelector(`[data-type=${ElementType.DAEA_SIGN}]`);
                    if (i <= pageCount - 1 && daeSign && nextPage) {
                        daeSign.parentElement?.removeChild(daeSign)
                    }
                }
                // 查找溢出问题
                document.querySelectorAll(`[overflow]`)?.forEach(el => {
                    if (isElementNode(el)) {
                        // 获取textNode节点
                        function findTextNode(root: HTMLElement, textNodeList: Node[] = []) {
                            root.childNodes.forEach(node => {
                                if (isTextNode(node)) {
                                    textNodeList.push(node)
                                    return
                                }
                                if (isElementNode(node)) {
                                    findTextNode(node as HTMLElement, textNodeList)
                                }
                            })
                        }

                        // 多行溢出
                        if (el.hasAttribute('multiline')) {
                            const scrollHeight = el.scrollHeight
                            const offsetHeight = (el as HTMLElement).offsetHeight
                            // 12像素字体作为高度比较的误差
                            const NORMAL_TEXT_HEIGHT = 12
                            if (scrollHeight > offsetHeight + NORMAL_TEXT_HEIGHT) {
                                Logger.warn('元素高度溢出')
                                const textNodeList: Node[] = []
                                const originTextContent = el.textContent
                                let currentScrollHeight = el.scrollHeight
                                let MAX_OVERFLOW_STACK = 800
                                let currentStack = 0
                                findTextNode(el as HTMLElement, textNodeList)
                                const reverseTextNodeList = textNodeList.reverse();
                                for (let i = 0; i < reverseTextNodeList.length; i++) {
                                    const textNode = reverseTextNodeList[i]
                                    // 清理textCotent中的换行和多余的空格
                                    textNode.textContent = textNode?.textContent?.replace(/\n/g, '')?.replace(/\s+/g, ' ') ?? ''
                                    while (currentScrollHeight > offsetHeight + NORMAL_TEXT_HEIGHT && textNode.textContent?.length) {
                                        currentStack++
                                        if (currentStack > MAX_OVERFLOW_STACK) {
                                            Logger.error(`overflow多行元素移除堆栈溢出[${MAX_OVERFLOW_STACK}]`)
                                            break
                                        }
                                        textNode.textContent = textNode.textContent.slice(0, -1)
                                        currentScrollHeight = el.scrollHeight
                                    }
                                    if (currentScrollHeight > offsetHeight + NORMAL_TEXT_HEIGHT) {
                                        break
                                    }
                                }
                                Logger.debug(`[${originTextContent}] => [${el.textContent}] 循环次数: [${currentStack}]`)
                            }
                        }

                        // 宽度溢出
                        const scrollWidth = el.scrollWidth
                        const offsetWidth = (el as HTMLElement).offsetWidth
                        if (scrollWidth > offsetWidth) {
                            Logger.warn('元素宽度溢出')
                            const textNodeList: Node[] = []
                            const originTextContent = el.textContent
                            let currentScrollWidth = el.scrollWidth
                            let MAX_OVERFLOW_STACK = 800
                            let currentStack = 0
                            findTextNode(el as HTMLElement, textNodeList)
                            const reverseTextNodeList = textNodeList.reverse();
                            for (let i = 0; i < reverseTextNodeList.length; i++) {
                                const textNode = reverseTextNodeList[i]
                                // 清理textCotent中的换行和多余的空格
                                textNode.textContent = textNode?.textContent?.replace(/\n/g, '')?.replace(/\s+/g, ' ') ?? ''
                                while (currentScrollWidth > offsetWidth && textNode.textContent?.length) {
                                    currentStack++
                                    if (currentStack > MAX_OVERFLOW_STACK) {
                                        Logger.error(`overflow元素移除堆栈溢出[${MAX_OVERFLOW_STACK}]`)
                                        break
                                    }
                                    textNode.textContent = textNode.textContent.slice(0, -1)
                                    currentScrollWidth = el.scrollWidth
                                }
                                if (currentScrollWidth === offsetWidth) {
                                    break
                                }
                            }
                            Logger.debug(`[${originTextContent}] => [${el.textContent}] 循环次数: [${currentStack}]`)
                        }
                    }
                })
            } catch (e) {
                throw Error(e)
            }
        }
    }

    pureTemplateElement(root: Element) {
        for (let i = root.childNodes.length - 1; i >= 0; i--) {
            const originNode = root.childNodes[i]
            if (!(isElementNode(originNode))) {
                root.removeChild(originNode)
            }
        }
    }

    createNewPage(fragment): IPageDOM {
        const pageDOM = this.createNewPageDOM(fragment)
        this.renderPendants(pageDOM)
        return pageDOM
    }

    getRenderPageCount() {
        return document.querySelectorAll(`.${PAGE_CLASSNAME}`).length
    }

    createNewPageDOM(fragment) {
        const page = document.createElement('div')
        const LODOPCommand = this.$options.dom.getAttribute(lodopCommandAttr) ?? ''
        page.className = PAGE_CLASSNAME
        page.style.height = this.$options.page.size.height
        page.style.width = this.$options.page.size.width
        page.style.position = 'relative'
        page.setAttribute('data-pagesize', this.$options.page.size.name)
        if (LODOPCommand) {
            page.setAttribute(lodopCommandAttr, LODOPCommand)
        }
        (fragment || document.body).append(page)
        const content = document.createElement('div')
        content.className = PAGE_CONTENT_CLASSNAME
        if (this.$options.dom.className) {
            const classList = this.$options.dom.className.replace(/\s+/, ' ').split(' ')
            classList.forEach(className => {
                content.classList.add(className)
            })
        }
        if (this.$options.dom.hasAttribute('data-size')) {
            content.setAttribute('data-size', <string>this.$options.dom.getAttribute('data-size'))
        }
        content.style.position = 'relative'
        page.appendChild(content)
        return {
            page,
            content,
        }
    }

    renderPendants(pageDOM: IPageDOM) {
        if (!this.$options.currentPendants.nodes.length) return
        this.$options.currentPendants.nodes.forEach(pendant => {
            const { type } = (pendant as HTMLElement).dataset
            if (type === ElementType.HEADER) {
                pageDOM.content.appendChild(pendant.cloneNode(true))
            }

            if (type === ElementType.FOOTER) {
                const clonedFooter = <HTMLElement>pendant.cloneNode(true)
                const footerWrapper = <HTMLElement>document.createElement('div')
                footerWrapper.appendChild(clonedFooter)
                pageDOM.content.appendChild(footerWrapper)
                this.$options.currentPendants.height = getElementHeight(footerWrapper as Element)
                footerWrapper.style.position = 'absolute'
                footerWrapper.style.bottom = '0'
                footerWrapper.style.left = '0'
                footerWrapper.style.width = '100%'
                footerWrapper.style.paddingTop = '0'
                footerWrapper.style.boxSizing = 'border-box'
                footerWrapper.classList.add(PAGE_FOOTER_CLASSNAME)
            }
        })
    }

    getPendants(root: Element) {
        if (!root.childNodes.length) return
        Logger.debug('收集页面挂件')
        const pendantsMap = this.$options.pendantsMap
        for (let i = 0; i < root.childNodes.length; i++) {
            const originNode = root.childNodes[i]
            let currentGroupName = GLOBAL_PENDANTS_KEY
            if (isElementNode(originNode)) {
                const {
                    type,
                    pendantsIndex,
                } = (originNode as HTMLElement).dataset
                // 没有设置挂件index时 默认为全局挂件
                if (pendantsIndex) {
                    currentGroupName = pendantsIndex
                }

                if (!pendantsMap[currentGroupName]) {
                    pendantsMap[currentGroupName] = []
                }

                if (
                    [
                        ElementType.HEADER,
                        ElementType.FOOTER,
                        ElementType.NEXT_PAGE,
                        ElementType.PREV_PAGE,
                    ].some(eleType => eleType === type)
                ) {
                    // 存储挂件节点
                    pendantsMap[currentGroupName].push(originNode.cloneNode(true) as Element)

                    // 创建锚点
                    const div = document.createElement('div')
                    div.dataset.type = ElementType.PENDANTS_HASH
                    div.dataset.pendantsIndex = currentGroupName
                    if (ElementType.HEADER === type) {
                        root.replaceChild(div, originNode)
                    } else {
                        root.removeChild(originNode)
                        i--
                    }
                }
            }
        }
        Logger.debug('挂件收集完成')
    }

    splitNormalElement(pageDOM: IPageDOM, originNode: Element, renderNode: Node, root: Element): ContinueSplit {
        pageDOM.content.appendChild(renderNode)
        const unUsageHeight = this.getUnUsageHeight(pageDOM)
        if (unUsageHeight < 0) {
            // 是否仅有该元素存在于该页面
            // 若该页面仅存在该元素依然放不下
            // 则不必再往下分页
            // 避免引起无限循环

            // 可以再分时需要在当前页把已渲染元素移除
            if (pageDOM.content.childNodes.length > 1 + this.$options.currentPendants.nodes.length) {
                pageDOM.content.removeChild(renderNode)
            } else {
                // 否则从原始数据中移除
                root.removeChild(originNode)
            }
            return true
        } else {
            // 当block元素出现在页面最开始位置时
            // 需要将其关联的锚点移动到当前页面的最开始位置
            // 例如河池发票中药处方中不允许出现用法在当前页的最开始
            // 需要把上一页的最后一行中药移动到当前页的最开始
            if(isElementNode(renderNode)) {
                const avoidFirstPageBreakId = renderNode.getAttribute(AvoidFirstPageBreakId)
                // 当前插入位置是否为当前页面的第一个元素
                if (avoidFirstPageBreakId && pageDOM.content.childNodes.length === 1 + this.$options.currentPendants.nodes.length) {
                    const avoidFirstPageBreakRefEl = document.body.querySelector(`[${AvoidFirstPageBreakRefId}="${avoidFirstPageBreakId}"]`);
                    if(avoidFirstPageBreakRefEl) {
                        avoidFirstPageBreakRefEl.removeAttribute(AvoidFirstPageBreakRefId);
                        renderNode.removeAttribute(AvoidFirstPageBreakId);
                        pageDOM.content.insertBefore(avoidFirstPageBreakRefEl, renderNode);
                    }
                }
            }
            root.childNodes.length && root.removeChild(originNode)
            return false
        }
    }

    splitMixTableElement(pageDOM: IPageDOM, originNode: Element, renderNode: Node, root: Element) {
        pageDOM.content.appendChild(renderNode)
        const unUsageHeight = this.getUnUsageHeight(pageDOM)
        if (unUsageHeight < 0) {
            // 移除tr 找到需要拆分的位置
            const renderTrs = (renderNode as HTMLElement).querySelectorAll('tbody tr');
            const originTrs = (originNode as HTMLElement).querySelectorAll('tbody tr');
            const renderTbody = (renderNode as HTMLElement).querySelector('tbody');

            for (let i = renderTrs.length - 1; i >= 0; i--) {
                const tr = renderTrs[i];
                tr.remove();
                if (this.getUnUsageHeight(pageDOM) >= 0) {
                    const originTrNode = originTrs[i];
                    const newTrNode = originTrNode.cloneNode(true);
                    // 重新插入元素
                    renderTbody?.appendChild(newTrNode);
                    // 将内容拆行
                    const newParagraph = (newTrNode as HTMLElement).querySelectorAll('p');
                    const originParagraph = (originTrNode as HTMLElement).querySelectorAll('p');

                    // 避免多次拆行
                    if (!originTrNode.classList.contains(isSplitLineTag)) {
                        newParagraph.forEach((paragraph, paragraphIndex) => {
                            const splitHTML = splitLine(paragraph, true);
                            paragraph.innerHTML = splitHTML;
                            originParagraph[paragraphIndex].innerHTML = splitHTML;
                        });
                        (originTrNode as HTMLElement).classList.add(isSplitLineTag);
                        (newTrNode as HTMLElement).classList.add(isSplitLineTag);
                    }

                    // 找出所有td
                    const newTds: HTMLElement[] = any2List((newTrNode as HTMLElement).querySelectorAll('td'));
                    const originTds: HTMLElement[] = any2List((originTrNode as HTMLElement).querySelectorAll('td'));
                    // 计算最长的行数
                    const maxLen = newTds.reduce((max, td) => {
                        return Math.max(td.querySelectorAll('.line')?.length, max)
                    }, 0);

                    // 从尾部开始移除
                    let breakLine = 0;
                    for (let i = maxLen - 1; i >= 0; i--) {
                        newTds.forEach((td) => {
                            const lines = td.querySelectorAll('.line');
                            if (lines[i]) {
                                lines[i].remove();
                            }
                        });
                        if (this.getUnUsageHeight(pageDOM) >= 0) {
                            breakLine = i;
                            break;
                        }
                    }

                    Logger.debug(`分隔行: [${breakLine}]`);

                    // 移除原始数据
                    for (let i = breakLine - 1; i >= 0; i--) {
                        originTds.forEach((td) => {
                            const lines = td.querySelectorAll('.line');
                            if (lines[i]) {
                                lines[i].remove();
                            }
                        });
                    }

                    // 移除没有任何内容的td元素
                    if (newTds.every(td => !td.textContent)) {
                        (newTrNode as HTMLElement).remove();
                    }

                    // 从原始节点中移除
                    for (let j = 0; j < i; j++) {
                        originTrs[j].remove();
                    }

                    this.newPageLayout(root);
                    break;
                }
            }
        } else {
            root.childNodes.length && root.removeChild(originNode);
            this.newPageLayout(root, pageDOM);
        }
    }

    splitComplexTableElement(pageDOM: IPageDOM, originNode: HTMLTableElement, renderNode: HTMLTableElement, root: Element) {
        let currentUnUsageHeight = this.getUnUsageHeightWithoutBottomOffset(pageDOM)
        pageDOM.content.appendChild(renderNode)
        const unUsageHeight = this.getUnUsageHeightWithoutBottomOffset(pageDOM)
        if (unUsageHeight < 0) {
            const complexTablePositions = getComplexTablePosition(renderNode)
            // 表头高度
            const theadHeight = renderNode.tHead?.offsetHeight ?? 0;
            // 可用空间减去表头高度
            currentUnUsageHeight -= theadHeight;
            // 拆分当前页面可以放入的表格
            for (let i = 0; i < complexTablePositions.length; i++) {
                const currentComplexTablePosition = complexTablePositions[i];
                currentUnUsageHeight -= currentComplexTablePosition.rowHeight;
                if (currentUnUsageHeight <= 0) {
                    const preTablePosition = complexTablePositions.slice(0, i);
                    const nextTablePosition = complexTablePositions.slice(i);

                    const tbodyClassName = renderNode.querySelector('tbody')?.classList.value || '';
                    const theadClassName = renderNode.querySelector('thead')?.classList.value || '';

                    let preTable: undefined | HTMLTableElement;
                    let nextTable: undefined | HTMLTableElement;

                    // 前置表格
                    // 构造前置表格
                    if(preTablePosition.length !== 0) {
                        preTable = renderNode.cloneNode() as HTMLTableElement;
                        preTable.innerHTML = `<thead class="${theadClassName}"></thead><tbody class="${tbodyClassName}"></tbody>`
                    }

                    // 后置表格
                    if(nextTablePosition.length !== 0) {
                        nextTable = renderNode.cloneNode() as HTMLTableElement;
                        nextTable.innerHTML = `<thead class="${theadClassName}"></thead><tbody class="${tbodyClassName}"></tbody>`;
                    }
                    // 加表头
                    if (renderNode.tHead) {
                        if(preTable) {
                            preTable.tHead = <HTMLTableSectionElement>renderNode.tHead.cloneNode(true)
                        }
                        if(nextTable) {
                            nextTable.tHead = <HTMLTableSectionElement>renderNode.tHead.cloneNode(true)
                        }
                    }
                    // 加 colGroup
                    const colGroup = renderNode.querySelector('colgroup');
                    if(colGroup) {
                        if(preTable) {
                            preTable.insertBefore(colGroup.cloneNode(true), preTable.tHead);
                        }
                        if(nextTable) {
                            nextTable.insertBefore(colGroup.cloneNode(true), nextTable.tHead);
                        }
                    }

                    const buildTable = (tableElement: HTMLTableElement, tablePosition: RowPosition[], breakIndex = 0) => {
                        // 截断位置不在首位时
                        // 要考虑横向的colspan
                        // 已经处理过的就不再处理了
                        const visited = new Set();
                        // 构造当前页面的表格
                        for (let j = 0; j < tablePosition.length; j++) {
                            const tr = tablePosition[j].tr.cloneNode();
                            const currentCells = tablePosition[j].cells;
                            const isLastTr = j === tablePosition.length - 1;
                            const isFirstTr = j === 0;
                            for (let k = 0; k < currentCells.length; k++) {
                                const currentCell = currentCells[k];
                                if (currentCell.cell) {
                                    const newNode = currentCell.cell.cloneNode(true) as HTMLTableCellElement;
                                    tr.appendChild(newNode)
                                    currentCell.cell = newNode;
                                }

                                // 修正rowSpan以及内容
                                if (currentCell.from && currentCell.from.cell && currentCell.from.rowIndex !== j + breakIndex) {
                                    if (isLastTr) {
                                        if ((currentCell.from.rowIndex ?? 0) + (currentCell.from.rowSpan ?? 1) > j + breakIndex) {
                                            // 包含row本身 所以要加1
                                            // 需要移除分割位置之后的rowSpan
                                            currentCell.from.cell.rowSpan = j - (currentCell.from?.rowIndex ?? 0) + breakIndex + 1
                                        }
                                    }
                                    if (isFirstTr) {
                                        if (!visited.has(currentCell.from.cell)) {
                                            const newNode = currentCell.from.cell.cloneNode(true) as HTMLTableCellElement;
                                            tr.appendChild(newNode);
                                            // 修正rowSpan
                                            // 从分割位置开始到当前位置剩余的rowSpan
                                            newNode.rowSpan = (currentCell.from.rowIndex ?? 0) + (currentCell.from.rowSpan ?? 0) - breakIndex;
                                            visited.add(currentCell.from.cell);
                                        }
                                    }
                                }
                            }
                            tableElement.tBodies[0].appendChild(tr)
                        }

                        return tableElement;
                    }

                    // 移除已渲染的表格
                    pageDOM.content.removeChild(renderNode);

                    if(preTable) {
                        // 加入新的表格
                        pageDOM.content.appendChild(buildTable(preTable, preTablePosition));
                    }

                    if(nextTable) {
                        // 插入剩余的表格
                        root.insertBefore(buildTable(nextTable, nextTablePosition, i), originNode);
                        // 移除原始表格
                        root.removeChild(originNode);
                        // 开始下一次分页
                        this.newPageLayout(root);
                    } else {
                        // 如果后面已经没有表格tr直接开始后面的分页
                        root.removeChild(originNode);
                        this.newPageLayout(root);
                    }

                    return
                }
            }

            root.removeChild(originNode);
            this.newPageLayout(root);
        } else {
            root.childNodes.length && root.removeChild(originNode);
            this.newPageLayout(root, pageDOM);
        }
    }

    splitFillBox(pageDOM: IPageDOM, originNode: Element, renderNode: Node, root: Element): ContinueSplit {
        pageDOM.content.appendChild(renderNode)
        const unUsageHeight = this.getUnUsageHeight(pageDOM)
        // 框架元素在本页放不下
        if (unUsageHeight < 0) {
            // 是否仅有该元素存在于该页面
            // 若该页面仅存在该元素依然放不下
            // 则不必再往下分页
            // 避免引起无限循环

            // 可以再分时需要在当前页把已渲染元素移除
            if (pageDOM.content.childNodes.length > 1 + this.$options.currentPendants.nodes.length) {
                pageDOM.content.removeChild(renderNode)
            } else {
                // 否则从原始数据中移除
                root.removeChild(originNode)
            }
            return true
        } else {
            const group = (renderNode as HTMLElement).querySelector(`[data-type=${ElementType.GROUP}]`)
            if (!group) {
                throw Error('fill-box必须提供group元素')
            }
            const item = group.querySelector(`[data-type=${ElementType.ITEM}]`)
            if (!item) {
                throw Error('fill-box必须提供item元素用于repeat')
            }
            let { max } = (renderNode as HTMLElement).dataset
            if (!max) {
                throw Error('fill-box必须提供max标识最大可重复次数')
            }

            for (let i = 0; i < +max - 1 /*忽略本身的repeat元素*/; i++) {
                const repeatElement = item.cloneNode(true)
                item.parentElement?.appendChild(repeatElement)
                const unUsageHeight = this.getUnUsageHeight(pageDOM)
                if (unUsageHeight < 0) {
                    item.parentElement?.removeChild(repeatElement)
                    break
                }
            }
            const unUsageHeight = this.getUnUsageHeight(pageDOM)
            const renderElement = (renderNode as HTMLElement)
            // 占用页面底部
            renderElement.style.marginTop = `${unUsageHeight}px`
            root.removeChild(originNode)
            return false
        }
    }

    splitFixedBottomBox(pageDOM: IPageDOM, originNode: Element, renderNode: Node, root: Element): ContinueSplit {
        pageDOM.content.appendChild(renderNode)
        const unUsageHeight = this.getUnUsageHeight(pageDOM)
        const fixedToBottom = (renderElement: HTMLElement) => {
            if (pageDOM.content.childNodes.length > 1 + this.$options.currentPendants.nodes.length) {
                const unUsageHeight = this.getUnUsageHeight(pageDOM)
                renderElement.style.marginTop = `${unUsageHeight}px`
            }
        }
        if (unUsageHeight < 0) {
            // 是否仅有该元素存在于该页面
            // 若该页面仅存在该元素依然放不下
            // 则不必再往下分页
            // 避免引起无限循环

            // 可以再分时需要在当前页把已渲染元素移除
            if (pageDOM.content.childNodes.length > 1 + this.$options.currentPendants.nodes.length) {
                pageDOM.content.removeChild(renderNode)
            } else {
                // 否则从原始数据中移除
                root.removeChild(originNode)
            }
            return true
        } else {
            root.childNodes.length && root.removeChild(originNode)
            fixedToBottom(renderNode as HTMLElement);
            return false
        }
    }

    splitLastPageFooter(pageDOM: IPageDOM, originNode: Element, renderNode: Node, root: Element) {
        pageDOM.content.appendChild(renderNode)
        const unUsageHeight = this.getUnUsageHeight(pageDOM)

        if (unUsageHeight < 0) {
            // 是否仅有该元素存在于该页面
            // 若该页面仅存在该元素依然放不下
            // 则不必再往下分页
            // 避免引起无限循环

            // 可以再分时需要在当前页把已渲染元素移除
            if (pageDOM.content.childNodes.length > 1 + this.$options.currentPendants.nodes.length) {
                pageDOM.content.removeChild(renderNode)
            } else {
                // 否则从原始数据中移除
                root.removeChild(originNode)
            }
            return true
        } else {
            root.childNodes.length && root.removeChild(originNode)
            if (pageDOM.content.childNodes.length === 1 + this.$options.currentPendants.nodes.length) {
                // 当前页只有页眉，需要把上一页的最后一个元素带到当前页
                const pages = document.body.getElementsByClassName('abc-page')
                const lastPage = pages[pages.length - 2]
                const lastPageContentDOMList = lastPage.childNodes[0].childNodes
                const lastDOM = lastPageContentDOMList[lastPageContentDOMList.length - 1]
                pageDOM.content.insertBefore(lastDOM,(pageDOM.content.firstChild as HTMLElement).nextSibling)
            }

            const unUsageHeight = this.getUnUsageHeight(pageDOM)
            renderNode.style.marginTop = `${unUsageHeight - 11}px`

            return false
        }
    }

    splitMultiplySampleTable(pageDOM: IPageDOM, originNode: Element, renderNode: Node, root: Element) {
        root.removeChild(originNode);
        let curPageDOM = pageDOM;
        let canUsageHeight = this.getUnUsageHeight(curPageDOM) - BOTTOM_OFFSET;
        let pageHeight = canUsageHeight;
        let fragment = document.createDocumentFragment();
        curPageDOM.content.appendChild(renderNode)
        const children = renderNode.children;
        const map = new Map();

        const domList = Array.from(children).map(child => {
            if(child.tagName === 'TABLE') {
                if(!map.has('thead')) {
                    map.set('thead', getElementHeight(child.tHead))
                }
                // if(!map.has('tr')) {
                //     map.set('tr', getElementHeight(child.tBodies[0].children[0]))
                // }
                let borderSpace: string | Array<string> = window.getComputedStyle(child).borderSpacing;

                if(!map.has('tableBorderSpaceY')) {
                    borderSpace = borderSpace.split(' ');
                    let borderSpaceY = borderSpace.length === 1 ? borderSpace[0] : borderSpace[1];
                    borderSpaceY = borderSpaceY.replace('px', '');
                    borderSpaceY = Number(borderSpaceY);
                    map.set('tableBorderSpaceY', borderSpaceY)
                }
                return {
                    tagName: child.tagName,
                    height: getElementHeight(child),
                    node: child,
                    tHead: {
                        tagName: 'THEAD',
                        height: map.get('thead'),
                        node: child.tHead,
                    },
                    trList: Array.from(child.tBodies[0].children).map(tr => {
                        return {
                            tagName: 'TR',
                            height: getElementHeight(tr) + map.get('tableBorderSpaceY'),
                            node: tr,
                        }
                    }),
                }
            }
            const key = child.dataset.type || child.className;
            if(!map.has(key)) {
                map.set(key, getElementHeight(child));
            }

            return {
                type: child.dataset.type,
                tagName: child.tagName,
                height: map.get(key),
                node: child,
            }
        })

        curPageDOM.content.removeChild(renderNode)
        // 生成新的一页
        const generateNewPage = () => {
            curPageDOM = this.createNewPage(fragment);
            canUsageHeight = pageHeight;
        }

        const handleSplitTable = (dom, canUsageHeight: number) => {
            if(dom.tagName !== 'TABLE') {
                return {
                    preTable: null,
                    canUsageHeight: canUsageHeight,
                };
            }
            // 表格判断能否分行
            const tableHeaderHeight = dom.tHead.height;
            // 可以分行，查询可以分几行
            let canStayCurrentPageCount = 0;
            let _canUsageHeight = canUsageHeight - tableHeaderHeight;
            for(let j = 0; j < dom.trList.length; j++) {
                const tr = dom.trList[j];
                if(tr.height > _canUsageHeight) {
                    break;
                }
                canStayCurrentPageCount++;
                _canUsageHeight -= tr.height;
            }

            // 至少需要分一行在当前页
            if(canStayCurrentPageCount !== 0) {
                const tableNode = dom.node;
                const tbodyClassName = tableNode.querySelector('tbody')?.classList.value || '';
                const theadClassName = tableNode.querySelector('thead')?.classList.value || '';

                let preTable: undefined | HTMLTableElement;
                preTable = tableNode.cloneNode() as HTMLTableElement;
                preTable.innerHTML = `<thead class="${theadClassName}"></thead><tbody class="${tbodyClassName}"></tbody>`
                preTable.prepend(tableNode.firstChild.cloneNode(true));
                preTable.tHead = dom.tHead.node.cloneNode(true);
                const addTrList = dom.trList.splice(0, canStayCurrentPageCount);
                let addTrHeightCount = 0;
                addTrList.forEach(tr => {
                    preTable.tBodies[0].appendChild(tr.node)
                    addTrHeightCount += tr.height;
                })
                // 更新 table 的高度
                dom.height = dom.height - addTrHeightCount;

                return {
                    preTable,
                    remainingHeight: _canUsageHeight,
                };
            }

            return {
                preTable: null,
                canUsageHeight: canUsageHeight,
            };
        }

        for(let i= 0; i < domList.length; i++) {
           const dom = domList[i];
            if(dom.type && dom.type.startsWith('title')) {
                // 往后面找到 table，判断能否放下标题加表格
                let titleMainHeight = 0,
                    titleSubHeight = 0,
                    tableHeight = 0,
                    tableOneRowHeight = 0;


                if(dom.type === 'title-main') {
                    const table = domList[i + 2];
                    if(!table.trList.length) {
                        i += 2;
                        continue;
                    }
                    titleMainHeight = dom.height;
                    titleSubHeight = domList[i + 1].height;
                    tableHeight = domList[i + 2].height;
                    tableOneRowHeight = domList[i + 2].tHead.height + domList[i + 2].trList?.[0]?.height || 0;
                } else {
                    const table = domList[i + 1];
                    if(!table.trList.length) {
                        i += 1;
                        continue;
                    }
                    titleSubHeight = domList[i].height;
                    tableHeight = domList[i + 1].height;
                    tableOneRowHeight = domList[i + 1].tHead.height + domList[i + 1].trList?.[0]?.height || 0;
                }

                const titleWithTableHeight = titleMainHeight + titleSubHeight + tableHeight;
                const titleWithTableOneRowHeight = titleMainHeight + titleSubHeight + tableOneRowHeight;

                if(canUsageHeight >= titleWithTableHeight) {
                    if(dom.type === 'title-main') {
                        curPageDOM.content.appendChild(domList[i].node)
                        curPageDOM.content.appendChild(domList[i + 1].node)
                        curPageDOM.content.appendChild(domList[i + 2].node)
                        i += 2;
                    } else {
                        curPageDOM.content.appendChild(domList[i].node)
                        curPageDOM.content.appendChild(domList[i + 1].node)
                        i += 1;
                    }
                    canUsageHeight -= titleWithTableHeight;
                } else if(canUsageHeight >= titleWithTableOneRowHeight) {
                    if(dom.type === 'title-main') {
                        curPageDOM.content.appendChild(domList[i].node.cloneNode(true))
                        curPageDOM.content.appendChild(domList[i + 1].node.cloneNode(true))
                    } else {
                        curPageDOM.content.appendChild(domList[i].node.cloneNode(true))
                    }
                    canUsageHeight = canUsageHeight - titleMainHeight - titleSubHeight;

                    // 可以放下部分表格，拆分表格
                    const idx = dom.type === 'title-main' ? i + 2 : i + 1;
                    const {
                        preTable,
                        remainingHeight,
                    } = handleSplitTable(domList[idx], canUsageHeight);

                    if(preTable) {
                        curPageDOM.content.appendChild(preTable);
                        canUsageHeight = remainingHeight;
                    }
                    if(dom.type === 'title-sub') {
                        i--;
                    }
                } else {
                    i--;
                    generateNewPage();
                }
            } else {
               // 如果高度够用，直接添加
               if(canUsageHeight > dom.height) {
                   canUsageHeight -= dom.height;
                   curPageDOM.content.appendChild(dom.node)
               } else {
                   // 回退
                   i--;
                   generateNewPage();
               }
            }
       }

        // 将fragment中的元素添加到当前页
        document.body.appendChild(fragment)

        this.newPageLayout(root, pageDOM);
    }

    newPageLayout(root: Element, prePageDOM?: IPageDOM) {
        // 发生溢出时开启调试
        if (__DEV__ && stack === MAX_STACK - 1) {
            debugger;
        }
        // 清理无关的元素
        this.pureTemplateElement(this.$options.dom)
        if (!root.childNodes.length) return
        const pageDOM = prePageDOM || this.createNewPage()
        const childNodes = any2List<Element>(root.childNodes)
        if (stack >= MAX_STACK) {
            throw Error(`Max stack ${MAX_STACK}`)
        }
        stack++
        let isMainLoopBreak = false;
        while (childNodes.length) {
            if (isMainLoopBreak) {
                break;
            }
            const originNode = childNodes.shift()
            const renderNode = originNode?.cloneNode(true)

            if (renderNode && originNode) {
                // 文本节点
                if (isTextNode(renderNode)) {
                    if (this.splitNormalElement(pageDOM, originNode, renderNode, root)) {
                        this.newPageLayout(root)
                        break
                    }
                }

                // 元素节点
                if (isElementNode(renderNode)) {
                    let { type } = (renderNode as HTMLElement).dataset

                    if (!type) {
                        Logger.debug('元素不设置type时默认为block')
                        type = ElementType.BLOCK
                    }

                    const isParagraph = renderNode.nodeName === 'P';

                    // pendants-hash
                    if (type === ElementType.PENDANTS_HASH) {
                        const { pendantsIndex } = (originNode as HTMLElement).dataset
                        if (pendantsIndex) {
                            if (!!(this.$options.pendantsMap[pendantsIndex] ?? []).length) {
                                this.$options.currentPendants.nodes = this.$options.pendantsMap[pendantsIndex]
                            }
                        }
                        root.childNodes.length && root.removeChild(originNode)
                        if (!pageDOM.content.childNodes.length) {
                            document.body.removeChild(pageDOM.page)
                        }
                        this.newPageLayout(root)
                        break
                    }

                    // new-page
                    if (type === ElementType.NEW_PAGE) {
                        root.childNodes.length && root.removeChild(originNode)
                        this.newPageLayout(root)
                        break
                    }

                    if(type === ElementType.BreakPageAtFirstPage) {
                        if (this.getRenderPageCount() === 1) {
                            root.removeChild(originNode);
                            this.newPageLayout(root);
                            break;
                        }
                    }

                    // fill-box
                    if (type === ElementType.FILL_BOX) {
                        if (this.splitFillBox(pageDOM, originNode, renderNode, root)) {
                            this.newPageLayout(root)
                            break
                        }
                    }

                    // big-table
                    if (type === ElementType.BIG_TABLE) {
                        Logger.warn('big-table分页')
                        pageDOM.content.appendChild(renderNode)
                        const trs = (renderNode as HTMLElement).querySelectorAll('tbody tr')
                        if (trs) {
                            const trHeightAndHTML = any2List<HTMLElement>(trs).map(it => {
                                return {
                                    height: getElementHeight(it),
                                    html: it.outerHTML,
                                }
                            });

                            const tbody = (renderNode as HTMLElement).querySelector('tbody')
                            if (tbody) {
                                tbody.innerHTML = ''
                                let nextElementSibling = originNode.nextElementSibling
                                // 插入后面的元素
                                while (nextElementSibling) {
                                    pageDOM.content.appendChild(nextElementSibling)
                                    nextElementSibling = nextElementSibling?.nextElementSibling
                                }
                                const pageHTML = pageDOM.page.outerHTML
                                const unUsageHeight = this.getUnUsageHeight(pageDOM)
                                let pageHTMLGroup = [{
                                    usedHeight: 0,
                                    html: '',
                                }];

                                let splitTimes = 0;
                                for (let i = 0; i < trHeightAndHTML.length; i++) {
                                    if (splitTimes > trHeightAndHTML.length) {
                                        throw Error(`表格分页异常,分页次数超过表格条数[${trHeightAndHTML.length}]`);
                                    }
                                    const currentGroup = pageHTMLGroup[pageHTMLGroup.length - 1]
                                    const {
                                        height,
                                        html,
                                    } = trHeightAndHTML[i];
                                    if (height && html) {
                                        currentGroup.usedHeight += height
                                        if (currentGroup.usedHeight > unUsageHeight) {
                                            // 发生分页时
                                            splitTimes++
                                            pageHTMLGroup.push({
                                                usedHeight: 0,
                                                html: '',
                                            })
                                            i--;
                                        } else {
                                            currentGroup.html += html
                                        }
                                    }
                                }

                                document.body.innerHTML = pageHTMLGroup.map(it => {
                                    return pageHTML.replace('<tbody></tbody>', `<tbody>${it.html}</tbody>`)
                                }).join('')
                                root.removeChild(originNode);
                            }
                        }
                        break;
                    }

                    // mix-box
                    // 段落
                    if (type === ElementType.MIX_BOX || isParagraph) {
                        pageDOM.content.appendChild(renderNode)
                        const unUsageHeight = this.getUnUsageHeight(pageDOM)
                        if (unUsageHeight < 0) {
                            if (isParagraph && !(renderNode as HTMLElement).classList.contains(isSplitLineTag)) {
                                const splitLineHTML = splitLine(renderNode as HTMLElement);
                                (renderNode as HTMLElement).innerHTML = splitLineHTML;
                                (originNode as HTMLElement).innerHTML = splitLineHTML;
                                (renderNode as HTMLElement).classList.add(isSplitLineTag);
                                (originNode as HTMLElement).classList.add(isSplitLineTag);
                            }
                            // 收集锚点元素
                            // 并从渲染结构中移除
                            let anchorNodeMapList = [] as IAnchorNodeMap[]
                            this.collectAnchorNodes(originNode, renderNode, anchorNodeMapList)
                            // 插入前检查框架元素高度是否溢出
                            // 若框架元素在该页面剩余位置放不下
                            // 则开始新的分页
                            if (this.getUnUsageHeight(pageDOM) < 0) {
                                if (this.splitNormalElement(pageDOM, originNode, renderNode, root)) {
                                    this.newPageLayout(root)
                                    break
                                }
                            }
                            anchorNodeMapList = anchorNodeMapList.reverse()
                            // 插入锚点元素
                            for (let anchorIndex = 0; anchorIndex < anchorNodeMapList.length; anchorIndex++) {
                                const {
                                    render: renderAnchor,
                                    origin: originAnchor,
                                } = anchorNodeMapList[anchorIndex]
                                if (!renderAnchor.parent) {
                                    throw Error('未找到父元素')
                                } else {
                                    if (renderAnchor.prev) {
                                        insertAfter(renderAnchor.node, renderAnchor.prev)
                                    } else if (renderAnchor.next) {
                                        renderAnchor.parent?.insertBefore(renderAnchor.node, renderAnchor.next)
                                    } else {
                                        renderAnchor.parent.appendChild(renderAnchor.node)
                                    }
                                    // 分组节点
                                    if (renderAnchor.type === ElementType.GROUP) {
                                        if (this.getUnUsageHeight(pageDOM) < 0) {
                                            let isSplit = false
                                            let isFirstInsertOutHeight = false
                                            const renderItemNodes = (renderAnchor.node as HTMLElement).querySelectorAll(`[data-type=${ElementType.ITEM}]`)
                                            const originItemNodes = (originAnchor.node as HTMLElement).querySelectorAll(`[data-type=${ElementType.ITEM}]`)

                                            // 设置为display none
                                            for (let i = 0; i < renderItemNodes.length; i++) {
                                                const renderItemNode = renderItemNodes[i]
                                                // TODO
                                                // 避免影响模板原有样式
                                                renderItemNode.setAttribute('style', 'display: none')
                                            }

                                            // 按照groupId分组
                                            const renderGroupItemList = [];
                                            for (let i = 0; i < renderItemNodes.length; i++) {
                                                // 遇到groupId不同的节点新增一组
                                                if (i === 0) {
                                                    renderGroupItemList.push([renderItemNodes[i]])
                                                } else {
                                                    const prevGroupId = renderItemNodes[i - 1].getAttribute(ITEM_GROUP_ID_ATTR)
                                                    const currentGroupId = renderItemNodes[i].getAttribute(ITEM_GROUP_ID_ATTR)
                                                    if (prevGroupId !== currentGroupId || !currentGroupId) {
                                                        renderGroupItemList.push([renderItemNodes[i]])
                                                    } else {
                                                        renderGroupItemList[renderGroupItemList.length - 1].push(renderItemNodes[i])
                                                    }
                                                }
                                            }

                                            // 按照groupId分组
                                            const originGroupItemList = [];
                                            for (let i = 0; i < originItemNodes.length; i++) {
                                                // 遇到groupId不同的节点新增一组
                                                if (i === 0) {
                                                    originGroupItemList.push([originItemNodes[i]])
                                                } else {
                                                    const prevGroupId = originItemNodes[i - 1].getAttribute(ITEM_GROUP_ID_ATTR)
                                                    const currentGroupId = originItemNodes[i].getAttribute(ITEM_GROUP_ID_ATTR)
                                                    if (prevGroupId !== currentGroupId || !currentGroupId) {
                                                        originGroupItemList.push([originItemNodes[i]])
                                                    } else {
                                                        originGroupItemList[originGroupItemList.length - 1].push(originItemNodes[i])
                                                    }
                                                }
                                            }

                                            let isBreak = false;

                                            for (let i = 0; i < renderGroupItemList.length; i++) {
                                                const renderGroupItem = renderGroupItemList[i]
                                                const originGroupItem = originGroupItemList[i]

                                                // 插入元素
                                                if (!isBreak) {
                                                    renderGroupItem.forEach(it => {
                                                        it.removeAttribute('style')
                                                    })
                                                }

                                                if (this.getUnUsageHeight(pageDOM) > 0 && !isBreak) {
                                                    originGroupItem.forEach(it => {
                                                        it.parentNode?.removeChild(it)
                                                    });
                                                } else {
                                                    // 插入一条就分页
                                                    if (i === 0) {
                                                        isFirstInsertOutHeight = true
                                                    }
                                                    isSplit = true
                                                    renderGroupItem.forEach(it => {
                                                        it.parentNode?.removeChild(it)
                                                    });
                                                    isBreak = true;
                                                }
                                            }

                                            if (isSplit) {
                                                if (isFirstInsertOutHeight && anchorIndex === 0) {
                                                    renderNode?.parentNode?.removeChild(renderNode);
                                                } else {
                                                    (renderNode as HTMLElement).classList.add(SpliterElementClassName.IS_SPLIT_AT_END);
                                                    (originNode as HTMLElement).classList.add(SpliterElementClassName.IS_SPLIT_AT_START);
                                                    (originNode as HTMLElement).style.textIndent = '';


                                                    // 修正renderNode的rowSpan
                                                    const trRenderList = (renderNode as HTMLElement).querySelectorAll(`[${TrMergeIdAttr}]`);
                                                    const renderTrMap = new Map<string, HTMLElement[]>();
                                                    const originTrMap = new Map<string, HTMLElement[]>();
                                                    if (trRenderList.length) {
                                                        trRenderList.forEach(tr => {
                                                            const groupId = tr.getAttribute(TrMergeIdAttr);
                                                            if (groupId) {
                                                                const groupList = renderTrMap.get(groupId) ?? [];
                                                                groupList.push(tr as HTMLElement);
                                                                renderTrMap.set(groupId, groupList);
                                                            }
                                                        });

                                                        // 修正rowSpan
                                                        renderTrMap.forEach((trList) => {
                                                            const firstTr = trList[0];
                                                            if (firstTr) {
                                                                const rowSpanTds = firstTr.querySelectorAll('[rowspan]');
                                                                if (rowSpanTds.length) {
                                                                    rowSpanTds.forEach(td => {
                                                                        td.setAttribute('rowspan', trList.length.toString());
                                                                    });
                                                                }
                                                            }
                                                        });
                                                    }

                                                    // 修正originNode的rowSpan
                                                    const trOriginList = (originNode as HTMLElement).querySelectorAll(`[${TrMergeIdAttr}]`);
                                                    if (trOriginList.length) {
                                                        trOriginList.forEach(tr => {
                                                            const groupId = tr.getAttribute(TrMergeIdAttr);
                                                            if (groupId) {
                                                                const groupList = originTrMap.get(groupId) ?? [];
                                                                groupList.push(tr as HTMLElement);
                                                                originTrMap.set(groupId, groupList);
                                                            }
                                                        });

                                                        // 修正rowSpan
                                                        originTrMap.forEach((trList, trListId) => {
                                                            const firstTr = trList[0];
                                                            if (firstTr) {
                                                                const rowSpanTds = firstTr.querySelectorAll('[rowspan]');
                                                                if (!rowSpanTds.length) {
                                                                    // 先简单处理，如果没有rowSpan的td，就在第一个td前面插入一个
                                                                    // 复制第一个td中的内容
                                                                    const td = renderTrMap.get(trListId)?.[0]?.querySelector('td')?.cloneNode(true) ?? document.createElement('td');
                                                                    (td as Element).setAttribute('rowspan', trList.length.toString());
                                                                    firstTr.prepend(td);
                                                                }
                                                            }
                                                        });
                                                    }
                                                }
                                                isMainLoopBreak = true;
                                                this.newPageLayout(root)
                                                break
                                            } else {
                                                originAnchor.parent?.removeChild(originAnchor.node)
                                            }
                                        } else {
                                            originAnchor.parent?.removeChild(originAnchor.node)
                                        }
                                    }
                                    // 文本节点
                                    if (renderAnchor.type === ElementType.TEXT) {
                                        const textContent = renderAnchor.node.textContent
                                        const textLength = textContent?.length ?? 0
                                        let high = textLength - 1
                                        let base = 0
                                        while (base < high) {
                                            const mid = Math.floor((base + high) / 2)
                                            const insertText = textContent?.slice(0, mid)
                                            renderAnchor.node.textContent = insertText ?? ''
                                            if (this.getUnUsageHeight(pageDOM) > 0) {
                                                if (base + 1 === high) {
                                                    break
                                                }
                                                base = mid
                                            } else {
                                                high = mid
                                            }
                                        }
                                        // 截断原始元素
                                        originAnchor.node.textContent = textContent?.slice(base) ?? ''
                                        // 截断时位置为0时说明即使不填充也放不下
                                        // 需要删除
                                        if (base === 0) {
                                            pageDOM.content.removeChild(renderNode)
                                        }
                                        isMainLoopBreak = true;
                                        this.newPageLayout(root)
                                        break
                                    }
                                }
                            }
                        } else {
                            root.removeChild(originNode)
                        }
                    }

                    // mix-table
                    if (type === ElementType.MIX_TABLE) {
                        this.splitMixTableElement(pageDOM, originNode, renderNode as HTMLElement, root);
                        break;
                    }

                    // complex-table
                    if (type === ElementType.COMPLEX_TABLE) {
                        this.splitComplexTableElement(pageDOM, originNode as HTMLTableElement, renderNode as HTMLTableElement, root);
                        break;
                    }

                    // block
                    if (!isParagraph) {
                        if ((type === ElementType.BLOCK || type === ElementType.NEXT_IS_BLANK)) {
                            if (this.splitNormalElement(pageDOM, originNode, renderNode as HTMLElement, root)) {
                                this.newPageLayout(root)
                                break;
                            }
                        }
                        if (type === ElementType.FIXED_BOTTOM_BOX) {
                            if (this.splitFixedBottomBox(pageDOM, originNode, renderNode as HTMLElement, root)) {
                                this.newPageLayout(root)
                                break;
                            }
                        }
                        if(type === ElementType.LAST_PAGE_FOOTER) {
                            if(this.splitLastPageFooter(pageDOM, originNode, renderNode as HTMLElement, root)) {
                                this.newPageLayout(root)
                                break;
                            }
                        }
                    }

                    // multiply-sample-table
                    if(type === ElementType.MULTIPLY_SAMPLE_TABLE) {
                        this.splitMultiplySampleTable(pageDOM, originNode as HTMLElement, renderNode as HTMLElement, root);
                        break;
                    }
                }
            }
        }
    }

    // 已使用高度
    getUsageHeight(pageDOM: IPageDOM) {
        return getElementHeight(pageDOM.content)
    }

    // 未使用高度
    getUnUsageHeight(pageDOM: IPageDOM) {
        return this.getCanUsageHeight(pageDOM) - this.getUsageHeight(pageDOM) + BOTTOM_OFFSET
    }

    // 未使用高度
    // 不加入底部偏移量
    getUnUsageHeightWithoutBottomOffset(pageDOM: IPageDOM) {
        const canUsageHeight = this.getCanUsageHeight(pageDOM)
        const  usageHeight = this.getUsageHeight(pageDOM);
        return canUsageHeight - usageHeight
    }

    // 可以使用高度
    getCanUsageHeight(pageDOM: IPageDOM) {
        // 当页面高度为auto时
        // 可使用高度为最大
        const isPageAuto = pageDOM.page.style.height === 'auto'
        if (isPageAuto) {
            return Number.MAX_VALUE
        }
        const elementHeightWithoutPadding = getElementHeightWithoutPadding(pageDOM.page)
        return elementHeightWithoutPadding - this.$options.currentPendants.height
    }

    collectAnchorNodes(originNode: Node, renderNode: Node, anchorNodes: IAnchorNodeMap[]) {
        const originChildNodes = any2List<Element>(originNode.childNodes)
        const renderChildNodes = any2List<Element>(renderNode.childNodes)
        for (let i = originChildNodes.length - 1; i >= 0; i--) {
            const originChildNode = originChildNodes[i]
            const renderChildNode = renderChildNodes[i]
            this.collectAnchorNodes(originChildNode, renderChildNode, anchorNodes)
            if (isElementNode(originChildNode)) {
                let { type } = (originChildNode as HTMLElement).dataset
                // item元素不需要收集
                if (type === ElementType.ITEM) {
                    continue
                }
                if (type === ElementType.GROUP) {
                    anchorNodes.push({
                        origin: {
                            type,
                            node: originChildNode,
                            parent: originChildNode?.parentNode,
                            prev: originChildNode.previousSibling,
                            next: originChildNode.nextSibling,
                        },
                        render: {
                            type,
                            node: renderChildNode,
                            parent: renderChildNode?.parentNode,
                            prev: renderChildNode.previousSibling,
                            next: renderChildNode.nextSibling,
                        },
                    })
                    // 收集后需要从渲染中清除
                    renderChildNode?.parentNode?.removeChild(renderChildNode)
                }
            }
        }
    }
}
