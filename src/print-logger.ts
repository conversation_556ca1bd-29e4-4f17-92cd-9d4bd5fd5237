// @ts-ignore
import { createAbcLogger } from 'abc-rum/lib/es';
import { VueConstructor } from "vue";
import { resolveErrorMessage } from "./utils";

export interface ErrorLogMetaData {
    userId: string;
    clinicId: string;
    chainId: string;
}

const AbcLogger = createAbcLogger();

export default class PrintLogger {
    private send: (data: Record<string, any>) => void;

    constructor() {
        this.send = () => {};
    }

    init(Vue: VueConstructor, errorLogMetaData: ErrorLogMetaData, options: any, globalConfig: any) {
        let cacheOption = {};
        if (options) {
            cacheOption = {
                template: options.template ?? {},
                page: options.page ?? {},
                originData: options.originData ?? {},
                reportLogData: options.reportLogData ?? {},
            }
        }

        // @ts-ignore
        const env = __BUILD_ENV__ || 'dev';
        // @ts-ignore
        const tag = __BUILD_TAG__ || 'latest'
        const envFix = ['pre', 'gray', 'prod'].includes(env) ? 'prod' : env;

        AbcLogger.init({
            host: 'cn-shanghai.log.aliyuncs.com',
            project: 'abc-fed-log',
            logstore: `${envFix}-metrics-raw`,
            workspace: `${envFix}-metrics`,
            env,
            namespace: 'PRINT',
            version: tag,
            enableDomClick: false,
        });

        this.send = (data: Record<string, any>) => {
            try {
                data = {
                    ...data,
                    meta: {
                        ...errorLogMetaData,
                        options: cacheOption,
                        _globalConfig: globalConfig,
                    },
                };
                AbcLogger.addLog(data);
            } catch (e) {
                console.error('printLogger.send err', e);
            }
        };
    }

    _send(scene: string, data: Record<string, any> | Error) {
        if (data instanceof Error) {
            this.send({
                scene,
                data: { error: resolveErrorMessage(data) },
            });
            return;
        }
        this.send({
            scene,
            data,
        });
    }
}
