{"name": "abc-print", "version": "1.0.0", "main": "lib/index.js", "module": "lib/index.esm.js", "license": "MIT", "devDependencies": {"@babel/core": "^7.14.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-runtime": "^7.14.5", "@rollup/plugin-alias": "^3.1.2", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-eslint": "^8.0.1", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/chalk": "^2.2.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "abc-fed-build-tool": "0.8.4", "ali-oss": "^6.16.0", "archiver": "^5.3.1", "autoprefixer": "^10.2.6", "babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "chalk": "^4.1.1", "cos-nodejs-sdk-v5": "^2.11.4", "cross-env": "^7.0.3", "dayjs": "^1.10.7", "default-gateway": "^6.0.3", "esbuild": "^0.19.10", "eslint": "^7.29.0", "eslint-plugin-vue": "^7.9.0", "ipaddr.js": "^2.0.1", "node-fetch": "2.6.6", "open": "7.0.1", "postcss": "^8.3.5", "postcss-html": "^1.8.0", "rollup": "^2.52.3", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-esbuild": "^6.1.0", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-uglify": "^6.0.4", "rollup-plugin-vue": "5.1.9", "stylelint": "^13.13.1", "stylelint-config-recess-order": "^2.5.0", "stylelint-config-standard": "^22.0.0", "stylelint-order": "^4.1.0", "stylelint-scss": "^3.20.1", "tsc": "^2.0.3", "tslib": "^2.3.0", "typescript": "^4.3.4", "vue": "^2.6.14", "vue-template-compiler": "^2.6.14"}, "scripts": {"dev": "cross-env NODE_ENV=development babel-node --presets env ./build/dev.js", "dev:scope": "cross-env NODE_ENV=development SCOPE=1 babel-node --presets env ./build/dev.js", "dev1": "export NODE_OPTIONS=\"--max-old-space-size=6144\" && cross-env NODE_ENV=development tsc --module umd ./share/page-size.ts && yarn dev", "dev1:scope": "cross-env NODE_ENV=development tsc --module umd ./share/page-size.ts && yarn dev:scope", "dev:server": "node ./build/dev-server", "dev:core": "rollup -c ./build/rollup.config.js -w", "dev:templates": "rollup -c ./build/rollup.templates.config.js -w", "dev:template-library": "cd ./template-library && yarn dev", "build:all": "cross-env NODE_ENV=production yarn build:core && yarn build:templates && yarn build:loader", "build:core": "rollup -c ./build/rollup.config.js", "build:templates": "cross-env NODE_ENV=production rollup -c ./build/rollup.templates.config.js", "build:loader": "node build/loader.js", "eslint": "eslint src --ext .ts", "upload:dev": "cross-env NODE_ENV=development node build/upload.js", "upload:prod": "cross-env NODE_ENV=production node build/upload.js", "build": "tsc --module umd ./share/page-size.ts && rm -rf ./lib/* && cross-env __BASE_URL__=__BASE_URL__HOLDER__ NODE_ENV=production yarn build:all && yarn upload:prod", "format": "prettier --write src/"}, "types": "src/types/index.d.ts", "stylelint": {"rules": {"indentation": 4}}, "dependencies": {"@tool/date": "^0.1.0", "abc-form-engine": "^1.0.4", "abc-growth-chart": "^1.0.5", "abc-rum": "0.0.26", "big.js": "^6.2.1", "extract-stack": "2.0.0", "jsbarcode": "3.8.0", "vue-i18n": "8.28.2"}}